<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dialog高度控制演示</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .demo-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        .demo-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .demo-btn.smooth { background-color: #28a745; color: white; }
        .demo-btn.fixed { background-color: #17a2b8; color: white; }
        .demo-btn:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
        
        /* 模拟Prompt组件的样式 */
        .mock-prompt {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: white;
            margin-top: 20px;
            max-width: 400px;
        }
        .mock-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #4a90e2;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
            outline: none;
            transition: all 0.25s ease;
        }
        .mock-input.error {
            border-color: #dc3545;
            background-color: #fff5f5;
        }
        .mock-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        /* 方案1: 平滑过渡 */
        .error-message-container {
            min-height: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-top: 8px;
        }
        .error-message-container.has-error {
            min-height: 20px;
            max-height: 60px;
        }
        .error-message-content {
            color: #dc3545;
            font-size: 12px;
            line-height: 1.4;
            padding: 2px 0;
            opacity: 0;
            transform: translateY(-8px);
            transition: all 0.25s ease;
        }
        .has-error .error-message-content {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 方案2: 固定高度 */
        .error-message-container.fixed-height {
            min-height: 24px;
            max-height: 24px;
            transition: none;
        }
        .error-message-container.fixed-height:not(.has-error)::after {
            content: '\00A0';
            color: transparent;
            font-size: 12px;
            line-height: 1.4;
            padding: 2px 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <h1>🎛️ Dialog高度控制演示</h1>
            <p>解决input输入时错误信息出现/消失导致的高度跳动问题</p>
            
            <div class="demo-section">
                <h3>方案1: 平滑过渡动画</h3>
                <p>错误信息区域使用CSS过渡动画，让高度变化更柔和</p>
                <div class="demo-buttons">
                    <button class="demo-btn smooth" @click="showSmoothDemo">
                        🌊 体验平滑过渡
                    </button>
                </div>
                <div v-if="showSmooth" class="mock-prompt">
                    <input 
                        v-model="smoothInput" 
                        class="mock-input"
                        :class="{ error: smoothError }"
                        placeholder="试试输入超过5个字符..."
                        @input="validateSmooth"
                    />
                    <div 
                        class="error-message-container"
                        :class="{ 'has-error': smoothError }"
                    >
                        <div v-if="smoothError" class="error-message-content">
                            {{ smoothErrorMessage }}
                        </div>
                    </div>
                    <p style="margin-top: 12px; color: #666; font-size: 14px;">
                        💡 注意观察错误信息出现/消失时的动画效果
                    </p>
                </div>
            </div>
            
            <div class="demo-section">
                <h3>方案2: 固定高度预留</h3>
                <p>错误信息区域始终占据固定高度，完全避免高度变化</p>
                <div class="demo-buttons">
                    <button class="demo-btn fixed" @click="showFixedDemo">
                        📐 体验固定高度
                    </button>
                </div>
                <div v-if="showFixed" class="mock-prompt">
                    <input 
                        v-model="fixedInput" 
                        class="mock-input"
                        :class="{ error: fixedError }"
                        placeholder="试试输入超过3个字符..."
                        @input="validateFixed"
                    />
                    <div 
                        class="error-message-container fixed-height"
                        :class="{ 'has-error': fixedError }"
                    >
                        <div v-if="fixedError" class="error-message-content">
                            {{ fixedErrorMessage }}
                        </div>
                    </div>
                    <p style="margin-top: 12px; color: #666; font-size: 14px;">
                        🔧 注意：高度始终保持不变，没有任何跳动
                    </p>
                </div>
            </div>
            
            <div class="demo-section">
                <h3>📊 对比总结</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                    <div style="padding: 15px; background: #e8f5e8; border-radius: 6px;">
                        <h4 style="margin-top: 0; color: #28a745;">🌊 平滑过渡方案</h4>
                        <ul style="margin: 0; padding-left: 20px; font-size: 14px;">
                            <li>高度会变化，但有柔和的动画</li>
                            <li>视觉效果自然，用户体验好</li>
                            <li>适合大多数场景</li>
                            <li>实现简单，只需CSS</li>
                        </ul>
                    </div>
                    <div style="padding: 15px; background: #e6f3ff; border-radius: 6px;">
                        <h4 style="margin-top: 0; color: #17a2b8;">📐 固定高度方案</h4>
                        <ul style="margin: 0; padding-left: 20px; font-size: 14px;">
                            <li>高度完全不变，零跳动</li>
                            <li>始终预留错误信息空间</li>
                            <li>适合对稳定性要求极高的场景</li>
                            <li>可能会有轻微的空间浪费</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        
        createApp({
            setup() {
                const showSmooth = ref(false);
                const showFixed = ref(false);
                const smoothInput = ref('');
                const fixedInput = ref('');
                const smoothError = ref(false);
                const fixedError = ref(false);
                const smoothErrorMessage = ref('');
                const fixedErrorMessage = ref('');
                
                const showSmoothDemo = () => {
                    showSmooth.value = !showSmooth.value;
                    if (showSmooth.value) {
                        showFixed.value = false;
                    }
                };
                
                const showFixedDemo = () => {
                    showFixed.value = !showFixed.value;
                    if (showFixed.value) {
                        showSmooth.value = false;
                    }
                };
                
                const validateSmooth = () => {
                    const value = smoothInput.value;
                    if (value.length > 5) {
                        smoothError.value = true;
                        smoothErrorMessage.value = `❌ 内容过长！最多5个字符，当前${value.length}个`;
                    } else if (value.includes(' ')) {
                        smoothError.value = true;
                        smoothErrorMessage.value = '❌ 不能包含空格';
                    } else {
                        smoothError.value = false;
                        smoothErrorMessage.value = '';
                    }
                };
                
                const validateFixed = () => {
                    const value = fixedInput.value;
                    if (value.length > 3) {
                        fixedError.value = true;
                        fixedErrorMessage.value = `❌ 超出限制！最多3个字符，当前${value.length}个`;
                    } else if (value.includes(' ')) {
                        fixedError.value = true;
                        fixedErrorMessage.value = '❌ 不能包含空格';
                    } else {
                        fixedError.value = false;
                        fixedErrorMessage.value = '';
                    }
                };
                
                return {
                    showSmooth,
                    showFixed,
                    smoothInput,
                    fixedInput,
                    smoothError,
                    fixedError,
                    smoothErrorMessage,
                    fixedErrorMessage,
                    showSmoothDemo,
                    showFixedDemo,
                    validateSmooth,
                    validateFixed
                };
            }
        }).mount('#app');
    </script>
</body>
</html> 