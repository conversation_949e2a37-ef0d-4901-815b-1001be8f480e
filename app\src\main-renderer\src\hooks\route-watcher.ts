import { watch, type WatchSource, computed } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'

export interface RouteChangeInfo {
  from: RouteLocationNormalized | undefined
  to: RouteLocationNormalized
  fromName?: string
  toName: string
  fromPath: string
  toPath: string
}

export interface RouteWatcherOptions {
  onRouteChange?: (info: RouteChangeInfo) => void
  onRouteEnter?: (routeName: string) => void
  onRouteLeave?: (routeName: string) => void
  onParamsChange?: (newParams: any, oldParams: any) => void
  onQueryChange?: (newQuery: any, oldQuery: any) => void
  immediate?: boolean
}

/**
 * 路由监听工具函数
 * @param route 当前路由对象
 * @param options 监听选项
 * @returns 停止监听的函数
 */
export function useRouteWatcher(
  route: RouteLocationNormalized,
  options: RouteWatcherOptions = {}
) {
  const {
    onRouteChange,
    onRouteEnter,
    onRouteLeave,
    onParamsChange,
    onQueryChange,
    immediate = true
  } = options

  const stopWatchers: (() => void)[] = []

  // 监听路由名称变化
  if (onRouteChange || onRouteEnter || onRouteLeave) {
    const stopNameWatcher = watch(
      () => route.name,
      (newRouteName, oldRouteName) => {
        const info: RouteChangeInfo = {
          from: route,
          to: route,
          fromName: oldRouteName as string,
          toName: newRouteName as string,
          fromPath: route.path,
          toPath: route.path
        }

        if (onRouteChange) {
          onRouteChange(info)
        }

        if (onRouteEnter && newRouteName) {
          onRouteEnter(newRouteName as string)
        }

        if (onRouteLeave && oldRouteName) {
          onRouteLeave(oldRouteName as string)
        }
      },
      { immediate }
    )
    stopWatchers.push(stopNameWatcher)
  }

  // 监听路由参数变化
  if (onParamsChange) {
    const stopParamsWatcher = watch(
      () => route.params,
      (newParams, oldParams) => {
        onParamsChange(newParams, oldParams)
      },
      { deep: true, immediate }
    )
    stopWatchers.push(stopParamsWatcher)
  }

  // 监听查询参数变化
  if (onQueryChange) {
    const stopQueryWatcher = watch(
      () => route.query,
      (newQuery, oldQuery) => {
        onQueryChange(newQuery, oldQuery)
      },
      { deep: true, immediate }
    )
    stopWatchers.push(stopQueryWatcher)
  }

  // 返回停止监听的函数
  return () => {
    stopWatchers.forEach(stop => stop())
  }
}

/**
 * 创建路由变化日志记录器
 */
export function createRouteLogger(route: RouteLocationNormalized, prefix = '路由变化') {
  return useRouteWatcher(
    route,
    {
      onRouteChange: (info) => {
        console.log(`${prefix}:`, {
          from: info.fromName,
          to: info.toName,
          fromPath: info.fromPath,
          toPath: info.toPath
        })
      },
      onParamsChange: (newParams, oldParams) => {
        console.log(`${prefix} - 参数变化:`, {
          from: oldParams,
          to: newParams
        })
      },
      onQueryChange: (newQuery, oldQuery) => {
        console.log(`${prefix} - 查询参数变化:`, {
          from: oldQuery,
          to: newQuery
        })
      }
    }
  )
} 