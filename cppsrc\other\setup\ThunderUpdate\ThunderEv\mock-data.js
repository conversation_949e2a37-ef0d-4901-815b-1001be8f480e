const http = require('http')

let url = 'http://**************/node.zip';
let md5 = 'C2C5305201E13B85BE2DCED9B8D94F4A';
let version = '11.4.5.9999';

let server = http.createServer((request, response) => {
  let body = '';
  request.on('readable', () => {
    const text = request.read();
    if (text) {
      body += text;
    }
  });

  request.on('end', () => {
    let urlObj = null;
    try {
      urlObj = new URL(request.url);
    } catch (error) {
      console.error('new Url error', error, request.url);
    }
    const pathname = (urlObj && urlObj.pathname) || request.url;
    let param = null;
    if (request.method === 'POST') {
      param = JSON.parse(body);
    }

    console.log('request on end', pathname, param, body);
    if (pathname === '/app/upgrade/v2/check') {
      response.writeHead(200, { 'Content-Type': 'application/json' });
      const responseBody = {};
      responseBody.code = 0;
      responseBody.data = {
        need_upgrade: true,
        app_id: '20001',
        md5: md5,
        download_url: url,
        title: '发现新版本',
        version_name: version,
        version_code: 0,
      };
      console.log('request on end response', responseBody);
      response.write(JSON.stringify(responseBody));
    } else {
      response.writeHead(404);
      console.log('request on end 404');
    }
    response.end();
  });
});

server.listen(6666);