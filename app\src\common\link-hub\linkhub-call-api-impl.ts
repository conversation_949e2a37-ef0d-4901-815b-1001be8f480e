import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { mainRendererContext } from '@xunlei/node-net-ipc/dist/ipc-base';
import { EventEmitter } from 'events'

export class LinkHubCallApiProxyImplWithIpcServer {
  private eventContainor: EventEmitter = new EventEmitter();
  private ipc_event_ids: { [event: string]: number } = {};

  private on(event: string): number {
      return client.attachServerEvent(event, (context: unknown, ...args: any[]) => {
        this.eventContainor.emit(event, ...args);
      });
    }
  
    private off(event: string, id: number): void {
      client.detachServerEvent(event, id);
    }

  public Init() {
    // client.registerFunctions({
    //   LinkHubCallApiProxyImplWithIpcServerFireEvent: (context: any, eventName: string, ...args: any[]) => {
    //     this.eventContainor.emit(eventName, ...args);
    //   },
    // });
  }
  public async CallApi(name: string, ...args: any[]): Promise<{ bSucc: boolean, result?: any, error?: any }> {
    try {
      const res = await client.callRemoteClientFunction(mainRendererContext, name, ...args);
      if (!res?.[1]) {
        return {
          bSucc: true,
          result: res[0],
        }
      }
      return {
        bSucc: false,
        error: res[1],
      }
    } catch (err) {
      return {
        bSucc: false,
        error: err,
      }
    }
  }

  public AttachServerEvent(name: string, callback: (...args: any[]) => void) {
    const count = this.eventContainor.listenerCount(name);
    if (count === 0) {
      this.ipc_event_ids[name] = this.on(name);
    }
    this.eventContainor.on(name, callback);
  }

  public DetachServerEvent(name: string, callback: (...args: any[]) => void): void {
    this.eventContainor.off(name, callback);
    const count = this.eventContainor.listenerCount(name);
    if (count === 0) {
      const id = this.ipc_event_ids[name];
      if (id) {
        this.off(name, id);
      }
    }
  }
}
let linkHubCallApiProxyInstance: LinkHubCallApiProxyImplWithIpcServer | null = null;
export function getLinkHubCallApiProxy(): LinkHubCallApiProxyImplWithIpcServer {
    if (!linkHubCallApiProxyInstance) {
        linkHubCallApiProxyInstance = new LinkHubCallApiProxyImplWithIpcServer();
        linkHubCallApiProxyInstance.Init();
    }
    return linkHubCallApiProxyInstance;
}