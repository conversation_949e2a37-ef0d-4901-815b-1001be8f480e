import { createMemoryHistory, createRouter, RouteRecordRaw } from 'vue-router'
import Download from '../views/download/index.vue'
import LinkDetail from '../views/link/Detail.vue'
import Link from '../views/link/index.vue'
import RecentPlay from '../views/recentplay/index.vue'
import Trash from '../views/trash/index.vue'
import Loading from '../views/loading/index.vue'
import { getRouteHistoryManager, setRoutesConfig } from '@/common/route-history-manager'
import { MainRenderUIHelper } from '@root/common/main-renderer-ui-helper'
import downloadHistory from '../views/downloadHistory/index.vue'

// 空组件工厂函数，用于有 resident 配置的路由
const createEmptyComponent = (name: string) => ({
  name,
  template: '<div></div>'
})

// 导航项配置
export interface NavItem {
  name: string
  title: string
  path: string
  redirect?: string
  icon?: {
    active: string    // 选中状态的图标
    default: string   // 未选中状态的图标
  },
  resident?: string // 常驻挂载id
  hideInSidebar?: boolean  // 是否在侧边栏中隐藏
}

// 统一的路由配置 这个路由是由 Vue Router  控制
export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/link',
    meta: { keepAlive: true }
  },
  {
    path: '/link',
    name: 'link',
    component: Link,
    meta: {
      title: '全部',
      icon: {
        default: 'xl-icon-nav-home',
        active: 'xl-icon-nav-home-select'
      },
      keepAlive: true
    },
    children: [
      {
        path: '/link/:id',
        name: 'link-detail',
        component: LinkDetail,
        meta: { keepAlive: true }
      },
    ]
  },
  {
    path: '/recent_play',
    name: 'recent_play',
    component: RecentPlay,
    meta: {
      title: '最近播放',
      icon: {
        default: 'xl-icon-nav-video',
        active: 'xl-icon-nav-video-select'
      },
      keepAlive: true
    }
  },
  {
    path: '/download',
    name: 'download',
    component: Download,
    meta: {
      title: '下载',
      icon: {
        default: 'xl-icon-nav-download',
        active: 'xl-icon-nav-download-select'
      },
      keepAlive: true,
      preload: true
    }
  },
  {
    path: '/trash',
    name: 'trash',
    component: Trash,
    meta: {
      title: '回收站',
      keepAlive: false,
      hideInSidebar: true  // 不显示在侧边栏
    }
  },
  {
    path: '/cloud',
    name: 'cloud',
    component: createEmptyComponent('cloud'),
    meta: {
      title: '云盘',
      icon: {
        default: 'xl-icon-nav-cloud',
        active: 'xl-icon-nav-cloud-select'
      },
      keepAlive: true,
      resident: 'thunder-pan'
    }
  },
  {
    path: '/loading',
    name: 'loading',
    component: Loading,
    meta: {
      title: 'loading',
      keepAlive: false,
      hideInSidebar: true  // 不显示在侧边栏
    }
  },
  {
    path: '/download-history',
    name: 'download-history',
    component: downloadHistory,
    meta: {
      title: '下载记录',
      keepAlive: false,
      hideInSidebar: true  // 不显示在侧边栏
    }
  },
  // {
  //   path: '/demo',
  //   name: 'demo',
  //   component: Demo,
  //   meta: {
  //     title: 'demo',
  //     icon: {
  //       default: 'xl-icon-nav-download',
  //       active: 'xl-icon-nav-download-select'
  //     }
  //   },
  //   children: [
  //     {
  //       path: '/demo/button',
  //       name: 'button',
  //       component: ButtonDemo,
  //       meta: {
  //         title: 'Button Demo'
  //       }
  //     },
  //     {
  //       path: '/demo/input',
  //       name: 'input',
  //       component: InputDemo,
  //       meta: {
  //         title: 'Input Demo'
  //       }
  //     },
  //     {
  //       path: '/demo/dropdown-menu',
  //       name: 'dropdown-menu',
  //       component: DropdownMenuDemo,
  //       meta: {
  //         title: 'DropdownMenu Demo'
  //       }
  //     },
  //     {
  //       path: '/demo/context-menu',
  //       name: 'context-menu',
  //       component: ContextMenuDemo,
  //       meta: {
  //         title: 'ContextMenu Demo'
  //       }
  //     },
  //     {
  //       path: '/demo/tooltip',
  //       name: 'tooltip',
  //       component: TooltipDemo,
  //       meta: {
  //         title: 'Tooltip Demo'
  //       }
  //     },
  //     {
  //       path: '/demo/file-icon',
  //       name: 'file-icon',
  //       component: FileIconDemo,
  //       meta: {
  //         title: 'FileIcon Demo'
  //       }
  //     },
  //     {
  //       path: '/demo/progress',
  //       name: 'progress',
  //       component: Progress,
  //       meta: {
  //         title: 'Progress Demo',
  //       }
  //     },
  //     {
  //       path: '/demo/dialog',
  //       name: 'dialog',
  //       component: DialogExample,
  //       meta: {
  //         title: 'Dialog Demo',
  //       }
  //     },
  //     {
  //       path: '/demo/tree',
  //       name: 'tree',
  //       component: TreeDemo,
  //       meta: {
  //         title: 'Tree Demo',
  //       }
  //     },
  //     {
  //       path: '/demo/checkbox',
  //       name: 'checkbox',
  //       component: Checkbox,
  //       meta: {
  //         title: 'Checkbox Demo',
  //       }
  //     }
  //   ]
  // }
]

// 创建路由实例
export const router = createRouter({
  history: createMemoryHistory(),
  routes: routes,
})

// 设置路由配置到历史管理器
setRoutesConfig(routes)

// 路由守卫 - 处理路由历史管理
router.beforeEach((to, from) => {
  const routeHistoryManager = getRouteHistoryManager()

  // 处理重定向情况
  if (from.path && routeHistoryManager.isRedirect(from.path, to.path)) {
    console.log('检测到重定向，不添加到历史记录:', { from: from.path, to: to.path })
    return true
  }

  // 检查是否是前进后退操作
  const isNavigation = routeHistoryManager.isNavigatingHistory()

  // 避免初始化时重复添加（from.path 为空时是初始化）
  // 前进后退操作不添加到历史记录 排除 loading 页面
  if (to.path !== from.path && from.path && from.path !== '/loading' && to.path !== '/loading' && !isNavigation) {
    routeHistoryManager.addToHistory(to.path)
    console.log('路由守卫 - 添加历史记录:', { from: from.path, to: to.path })
  }

  // 重置导航标志（确保所有路由变化后都重置）
  routeHistoryManager.resetNavigationFlag()

  // 更新头部按钮状态
  const headerManager = MainRenderUIHelper.getInstance()
  const canGoBack = routeHistoryManager.canGoBack.value
  const canGoForward = routeHistoryManager.canGoForward.value

  headerManager.updateQuickFunctionIconStatus({
    backward: { enable: canGoBack },
    forward: { enable: canGoForward },
    refresh: { enable: true }
  })

  return true
})

// 动态收集所有需要 keepAlive 的路由 name
function getKeepAliveNames(routes: RouteRecordRaw[]): string[] {
  const names: string[] = []
  routes.forEach(route => {
    // 默认所有组件都缓存，除非 meta.keepAlive 设置为 false
    if (route.name && route.meta?.keepAlive) {
      names.push(route.name as string)
    }
    if (route.children) {
      names.push(...getKeepAliveNames(route.children))
    }
  })
  return names
}

export const routesKeepAliveNames = getKeepAliveNames(routes)

// 导出导航项配置
export const navItems: NavItem[] = routes
  .filter(route => route.meta?.title)
  .map(route => ({
    name: route.name as string,
    title: route.meta?.title as string,
    redirect: route.redirect as string,
    icon: route.meta?.icon as { active: string; default: string } | undefined,
    path: route.path,
    resident: route.meta?.resident as string,
    hideInSidebar: route.meta?.hideInSidebar as boolean
  }))

// 导出所有 resident id
export const residentIds = routes
  .map(route => route.meta?.resident)
  .filter(Boolean) as string[]

export const preloadRoutes = routes.reduce((acc, route) => {
  if (route.meta?.preload) {
    acc.push(route)
  }
  if (route.children) {
    acc.push(...route.children?.filter(r => r.meta?.preload))
  }
  return acc
}, [] as RouteRecordRaw[])