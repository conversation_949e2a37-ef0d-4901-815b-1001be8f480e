#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 静默运行，只在出错时输出
const silent = process.argv.includes('--silent');

function log(message) {
  if (!silent) {
    console.log(message);
  }
}

// 1. 检查并移除 sass-embedded（如果存在且有问题）
const sassEmbeddedPath = path.join(process.cwd(), 'node_modules', 'sass-embedded');
if (fs.existsSync(sassEmbeddedPath)) {
  try {
    // 简单测试 sass-embedded 是否可用
    require('sass-embedded');
    log('✓ sass-embedded 可用');
  } catch (e) {
    // sass-embedded 有问题，移除它
    try {
      if (process.platform === 'win32') {
        const { execSync } = require('child_process');
        execSync(`rmdir /s /q "${sassEmbeddedPath}"`, { stdio: 'pipe' });
      } else {
        fs.rmSync(sassEmbeddedPath, { recursive: true, force: true });
      }
      log('✓ 已移除损坏的 sass-embedded');
    } catch (removeError) {
      log('⚠️ 无法移除 sass-embedded，但会强制使用 sass');
    }
  }
}

// 2. 确保 sass 可用
try {
  require('sass');
  log('✓ sass 可用');
} catch (e) {
  console.error('✗ sass 不可用，请运行 npm install');
  process.exit(1);
}

// 3. 设置环境变量
process.env.SASS_IMPLEMENTATION = 'sass';
process.env.NODE_OPTIONS = process.env.NODE_OPTIONS || '--max-old-space-size=4096';

log('✓ SASS 构建环境准备完成'); 