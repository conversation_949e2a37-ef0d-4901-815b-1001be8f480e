<script setup lang="ts">
import { TabsList, TabsRoot, TabsTrigger } from 'reka-ui'
import { HTMLAttributes } from 'vue'
import './style.css'

export interface ITabItem {
  name: string
  value: string
}

const props = defineProps<{
  tabs: ITabItem[]
  defalutValue: string
  tabsClass?: HTMLAttributes['class']
  tabClass?: HTMLAttributes['class']
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const handleChange = (value: string) => {
  emit('update:modelValue', value)
}

</script>

<template>
  <TabsRoot :class="['TabsRoot', tabsClass]" :default-value="defalutValue" @update:model-value="handleChange">
    <TabsList class="TabsList">
      <TabsTrigger v-for="(tab) in tabs" :class="['TabsTrigger', tabClass]" :value="tab.value">
        {{ tab.name }}
      </TabsTrigger>
    </TabsList>
  </TabsRoot>
</template>