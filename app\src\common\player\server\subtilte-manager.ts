import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import * as BaseType from '../base'
import { AplayerStack } from '../impl/aplayer-stack';
import { AplayerMedia } from '../impl/aplayer-media';
import {LifeObjectContainor} from '@root/common/life-object-containor'

export class AplayerServerSubtitleManager {
    static init() {
        client.registerFunctions({
            AplayerSubtitleManagerSetPosition: (context: any, id: string, pos: number) => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return ;
                }

                media.getSubtitleManager().setPosition(pos);
            },
            AplayerSubtitleManagerGetPosition: (context: any, id: string): number => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return 0;
                }

                return media.getSubtitleManager().getPosition();
            },
            AplayerSubtitleManagerSetTimming: (context: any, id: string, n: number) => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return ;
                }

                media.getSubtitleManager().setTimming(n);
            },
            AplayerSubtitleManagerGetTimming: (context: any, id: string): number => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return 0;
                }

                return media.getSubtitleManager().getTimming();
            },
            AplayerSubtitleManagerSetVisible: (context: any, id: string, b: boolean) => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return ;
                }

                media.getSubtitleManager().setVisible(b);
            },
            AplayerSubtitleManagerGetVisible: (context: any, id: string): boolean => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return true;
                }

                return media.getSubtitleManager().getVisible();
            },
            AplayerSubtitleManagerSetFontSize: (context: any, id: string, n: number) => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return ;
                }

                media.getSubtitleManager().setFontSize(n);
            },
            AplayerSubtitleManagerSetFontColor: (context: any, id: string, s: string) => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return ;
                }

                media.getSubtitleManager().setFontColor(s);
            },
            AplayerSubtitleManagerSetFontFamily: (context: any, id: string, s: string) => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return ;
                }

                media.getSubtitleManager().setFontFamily(s);
            },
            AplayerSubtitleManagerSetFontStyle: (context: any, id: string, fontStyle: BaseType.SubtitleFontStyle) => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return ;
                }

                media.getSubtitleManager().setFontStyle(fontStyle);
            },
            AplayerSubtitleManagerGetFontStyle: (context: any, id: string): BaseType.SubtitleFontStyle => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return {fontSize: 12, fontColor: '', fontName: ''};
                }

                return media.getSubtitleManager().getFontStyle();
            },
            AplayerSubtitleManagerGetList: (context: any, id: string, category: BaseType.SubtitleCategory): BaseType.SubtitleItemDisplayInfo[] => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return [];
                }

                return media.getSubtitleManager().getList(category);
            },
            AplayerSubtitleManagerGetSubtitleCategoryItemCount: (context: any, id: string): number => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return 0;
                }

                return media.getSubtitleManager().getSubtitleCategoryItemCount();
            },
            AplayerSubtitleManagerGetSubtitleById: (context: any, id: string, subtitleId: string): BaseType.SubtitleItemDisplayInfo => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return {id: '', displayName: '', filePath: '', gcid: '', category: BaseType.SubtitleCategory.Min};
                }

                return media.getSubtitleManager().getSubtitleById(subtitleId);
            },
            AplayerSubtitleManagerSelect: (context: any, id: string, subtitleId: string): void => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return ;
                }

                media.getSubtitleManager().select(subtitleId);
            },
            AplayerSubtitleManagerGetSelect: (context: any, id: string): string => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return '';
                }

                return media.getSubtitleManager().getSelect();
            },
            AplayerSubtitleManagerGetSelectSubtitleCategory: (context: any, id: string): BaseType.SubtitleCategory => {
                let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
                if (!media) {
                    return BaseType.SubtitleCategory.Min;
                }

                return media.getSubtitleManager().getSelectSubtitleCategory();
            },
        });
    }
}