<script setup lang="ts">
import { onMounted, ref, computed, onBeforeUnmount, nextTick } from 'vue'
import { platform } from '@root/common/env'
import AppMenu from '@root/common/components/ui/app-menu/index.vue'
import AppHeader from '@root/main-renderer/src/components/app-header/index.vue'
import BackToTop from '@root/common/components/ui/back-to-top/index.vue'
import MessageBox from '@root/common/components/ui/message-box'
import { TaskManager } from '@root/common/task/impl/task-manager';
import { Channels } from '@root/common/constant'
import { useRouter, useRoute } from 'vue-router'
import { navItems, NavItem, routes, routesKeepAliveNames, residentIds, preloadRoutes } from '../router'
import { MainRenderUIHelper } from '@root/common/main-renderer-ui-helper'
import { PopUpNS } from '@root/common/pop-up';
import { AccountHelper } from '@root/common/account/client/accountHelper'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'
import { AccountHelperEventKey, UserInfo } from '@root/common/account/account-type'
import { useUserStore } from '@/stores/user'
import { useAlertDialog } from '@root/common/components/ui/Dialog/useAlertDialog'
import { getRouteHistoryManager } from '@/common/route-history-manager'
import { registerPageLifecycleResultHandler } from '@/hooks/useKeepAliveHooks'
import { message } from '@root/common/components/ui/message/index'
import * as PopUpTypes from '@root/common/pop-up/types';
import { quitApp } from '@root/common/quit-promises'
import { useThrottleFn } from '@vueuse/core'
import { useIpcRendererOn } from '@vueuse/electron'
import { MainToRenderer_Window_Channel } from '@root/common/constant'
import { useSidebarInfoStore } from '@/stores/sidebarInfo'
import { env } from '@root/common/env'
import { requestHelper } from '@/utils/request'
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client'

const alertDialog = useAlertDialog()
const uerStore = useUserStore();

MainRenderUIHelper.getInstance().init()

const sidebarInfoStore = useSidebarInfoStore()

const menuCollapse = ref(false)
const isWindowMax = ref(false)
const isPreloading = ref(true)

const selectedRoute = ref<NavItem>(navItems[0])
const router = useRouter()
const route = useRoute()

// 根据当前路由自动更新选中的导航项
const currentRoute = computed(() => {
  // 根据当前路由路径找到对应的导航项
  const currentNavItem = navItems.find(item => {
    // 精确匹配路径
    if (item.path === route.path) {
      return true
    }
    // 处理子路由匹配（如 /link/123 匹配 /link）
    if (route.path.startsWith(item.path + '/')) {
      return true
    }
    // 处理根路径重定向
    if (route.path === item.redirect) {
      return true
    }
    return false
  })

  const result = currentNavItem

  return result
})


// 路由历史管理器
const routeHistoryManager = getRouteHistoryManager()

// 当有常驻挂载元素时，使用头部管理器的事件
const canGoBack = computed(() => routeHistoryManager.canGoBack.value || MainRenderUIHelper.getInstance().backward.enable)
const canGoForward = computed(() => routeHistoryManager.canGoForward.value || MainRenderUIHelper.getInstance().forward.enable)

// 获取当前路由的 resident 配置
const currentResident = computed(() => {
  const currentRouteConfig = routes.find(r => r.name === route.name)
  return currentRouteConfig?.meta?.resident as string
})

// 判断是否显示常驻挂载元素
const showResidentElement = computed(() => {
  return !!currentResident.value
})

const onNavClick = useThrottleFn((item: NavItem) => {
  console.log('点击导航项:', item.name, item.path)
  selectedRoute.value = item
  router.push(item.path)
}, 200)

// 同步搜索数据
async function syncSearchData(userId: string): Promise<void> {
  try {
    const baseUrl =  env === 'prod' ? 'https://api-gateway-pan.xunlei.com'  :  'https://test-api-gateway-pan.xunlei.com' 
    const data = {
      user_ids: [userId],
    }
    
    await requestHelper.request({
      body: data,
      url: `${baseUrl}/drive/v1/trigger/search`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      withCredentials: true,
    })
  } catch (error) {
    console.error('同步搜索数据失败:', error)
  }
}

let willQuitApp = false;
onMounted(async () => {
  const initialPath = route.fullPath;

  // 预加载路由
  if (preloadRoutes.length > 0) {
    routeHistoryManager.setIsNavigating(true)
    for (const r of preloadRoutes) {
      await router.push(r.path);
    }
    await router.push(initialPath);
    routeHistoryManager.setIsNavigating(false)
    routeHistoryManager.initRouteHistory(initialPath);
  } else {
    // 设置路由配置到历史管理器
    routeHistoryManager.initRouteHistory(route.path)
  }
  isPreloading.value = false;

  const curUserInfo = await AccountHelper.getInstance().getUserInfo()
  if (curUserInfo) {
    uerStore.setUserInfo(curUserInfo)
  }
  AccountHelper.getInstance().attachEvent(AccountHelperEventKey.USER_INFO_CHANGE, (value: UserInfo) => {
    uerStore.setUserInfo(value)
    // if (value.sub) {
    //   syncSearchData(value.sub)
    // }
  })
  AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_FAILURE, () => { })
  window.addEventListener('beforeunload', (event) => {
    if (!willQuitApp) {
      // 阻止默认行为
      event.preventDefault();
      event.returnValue = '';
      onClose();
    }
    return '';
  });
  clientModule.registerFunctions({
    QuitThunder: () => {
      willQuitApp = true;
      quitApp();
    }
  })
})

onBeforeUnmount(() => {
  routeHistoryManager.reset()
})

useIpcRendererOn(MainToRenderer_Window_Channel.onMaximize, (_, isMaximize: boolean) => {
  console.log('onMaximize ---------------->', isMaximize)
  isWindowMax.value = isMaximize
})

const onMinimize = () => window.__ELECTRON__.ipcRenderer.send(Channels.Minimize)
const onMaximize = () => {
  return window.__ELECTRON__.ipcRenderer.send(Channels.Maximize)
}
const onClose = () => {
  if (TaskManager.GetInstance().getDownloadQueueCount() > 0) {
    MessageBox.confirm('当前有正在下载的任务，确定退出吗？', '退出迅雷', {
      showCancelButton: true,
      showConfirmButton: true,
      closeOnClickModal: true,
      closeOnPressEscape: true,
    }).then(() => {
      willQuitApp = true;
      quitApp();
    })
  } else {
    willQuitApp = true;
    quitApp();
  }
}
const onNewWin = async () => {
  ThunderNewTaskHelperNS.showNewTaskWindow();
}

const onAddNav = () => { }

const keepAliveNames = ref(routesKeepAliveNames)

// 前进后退刷新处理函数
const onGoBack = useThrottleFn(() => {
  // 如果有常驻挂载元素，使用头部管理器的事件
  if (showResidentElement.value) {
    if (MainRenderUIHelper.getInstance().backward.enable) {
      MainRenderUIHelper.getInstance().handleBackward()
    } else {
      // 在挂载页面中，当后退按钮不可用时，代表已经在历史挂载路由页面的最前列，调用 goBack
      goBack()
    }
  } else {
    goBack()
  }

}, 200)

const onGoForward = useThrottleFn(() => {

  if (showResidentElement.value) {
    // 如果有常驻挂载元素，使用头部管理器的事件
    if (MainRenderUIHelper.getInstance().forward.enable) {
      MainRenderUIHelper.getInstance().handleForward()
    } else {
      // 在挂载页面中，前进退按钮不可用时，代表已经在历史挂载路由页面的最后列，调用 goForward
      goForward()
    }
  } else {
    goForward()
  }
}, 200)

const onRefresh = useThrottleFn(() => {
  const name = route.name as string

  // 如果有常驻挂载元素，使用头部管理器的事件
  if (showResidentElement.value) {
    MainRenderUIHelper.getInstance().handleRefresh()
    message({ message: '页面刷新成功', type: 'success' })
    return
  }

  // 普通路由的刷新逻辑
  // 1. 移除当前页面缓存
  keepAliveNames.value = keepAliveNames.value.filter(n => n !== name)
  // 注册刷新结果回调
  registerPageLifecycleResultHandler(name,
    () => {
      nextTick(() => {
        message({ message: '页面刷新成功', type: 'success' })
        keepAliveNames.value = routesKeepAliveNames
      })
    },
    () => {
      message({ message: '页面刷新失败，请检查网络！', type: 'error' })
    }
  )
  router.replace({
    path: '/loading',
    query: { redirect: route.fullPath, refreshName: name }
  })
}, 1000)

const handleLogin = () => {
  PopUpNS.showLoginDlg();
}

const handleLogout = async () => {
  const result = await alertDialog.confirm({
    title: '确定要退出吗?',
    content: '退出当前账号，已完成同步链接、下载记录、播放记录将不再在当前设备展示',
    confirmText: '退出登录',
    cancelText: '取消',
    showTitleIcon: false,
  })
  if (result) {
    await AccountHelper.getInstance().signOut()
    router.replace({
      path: '/',
    })
  }
}

// 前进后退刷新处理函数
function goBack() {
  const targetPath = routeHistoryManager.goBack()
  if (targetPath) {
    // 使用 router.push 跳转到目标路径
    router.push(targetPath)
  }
}

function goForward() {
  // 检查是否可以后退
  const targetPath = routeHistoryManager.goForward()
  if (targetPath) {
    // 使用 router.push 跳转到目标路径
    router.push(targetPath)
  }
}

const handleSetting = () => {
  message({ message: '暂未实现，敬请期待！', type: 'warning' })
}

const handleFeedback = () => {
  message({ message: '暂未实现，敬请期待！', type: 'warning' })
}

</script>

<template>
  <div class="app-container" :class="{ 'is-collapse': menuCollapse, 'is-mac': platform.isMacOS }">
    <!-- 左侧栏 -->
    <div class="app-menu">
      <AppMenu :items="navItems" :active-item="currentRoute" @login="handleLogin" @nav-click="onNavClick"
        @add-nav="onAddNav" :user-info="uerStore.userInfo" @logout="handleLogout" @setting="handleSetting"
        @feedback="handleFeedback" :extra-info="{
          download: {
            downloadCount: sidebarInfoStore.getDownloadCount
          }
        }" />
    </div>
    <div class="app-body">
      <!-- 顶部栏 -->
      <div class="app-header draggable">
        <AppHeader :is-window-max="isWindowMax.valueOf()" :can-go-back="canGoBack" :can-go-forward="canGoForward"
          :can-refresh="true" @close="onClose" @max="onMaximize" @minize="onMinimize" @go-back="onGoBack"
          @go-forward="onGoForward" @add="onNewWin" @refresh="onRefresh" />
      </div>
      <div class="app-content" v-show="!isPreloading">
        <!-- 渲染所有 resident 容器，始终在 DOM 中 -->
        <div v-for="id in residentIds" :key="id" :id="id" class="resident-element" v-show="currentResident === id">
        </div>
        <!-- 普通路由视图 -->
        <router-view v-slot="{ Component, route }">
          <keep-alive :include="keepAliveNames" :max="10">
            <component :is="Component" class="router-view-component" v-show="!currentResident" />
          </keep-alive>
        </router-view>
      </div>
    </div>

    <!-- 回到顶部按钮 -->
    <BackToTop />
  </div>
</template>

<style lang="scss" scoped>
.app-container {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: #fff;

  &.is-mac {
    .app-menu {
      padding-top: 40px;
    }
  }

  .app-menu {
    width: 200px;
    padding: 8px 18px 0 18px;
    flex-shrink: 0;
    position: relative;
    border-right: 1px solid #F2F3F5;
  }

  .app-body {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    width: calc(100% - 204px - 12px);

    .app-header {
      height: 64px;
      flex-shrink: 0;
      padding: 14px 12px;
    }

    .app-content {
      flex-grow: 1;
      position: relative;
      display: flex;
      flex-direction: column;
      min-height: 0;

      .router-view-component {
        width: 100%;
        height: 100%;
        flex: 1;
      }

      .resident-element {
        width: 100%;
        height: 100%;
        flex: 1;
      }
    }
  }

  &.is-collapse {
    .app-menu {
      width: 90px;
    }

    .app-body {
      width: calc(100% - 90px);
    }
  }
}
</style>
