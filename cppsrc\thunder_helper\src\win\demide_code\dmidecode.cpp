/*
 * DMI Decode
 *
 *   Copyright (C) 2000-2002 <PERSON> <<EMAIL>>
 *   Copyright (C) 2002-2008 <PERSON> <<EMAIL>>
 *
 *   This program is free software; you can redistribute it and/or modify
 *   it under the terms of the GNU General Public License as published by
 *   the Free Software Foundation; either version 2 of the License, or
 *   (at your option) any later version.
 *
 *   This program is distributed in the hope that it will be useful,
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *   GNU General Public License for more details.
 *
 *   You should have received a copy of the GNU General Public License
 *   along with this program; if not, write to the Free Software
 *   Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307 USA
 *
 *   For the avoidance of doubt the "preferred form" of this code is one which
 *   is in an open unpatent encumbered format. Where cryptographic key signing
 *   forms part of the process of creating an executable the information
 *   including keys needed to generate an equivalently functional executable
 *   are deemed to be part of the source code.
 *
 * Unless specified otherwise, all references are aimed at the "System
 * Management BIOS Reference Specification, Version 2.6" document,
 * available from http://www.dmtf.org/standards/smbios/.
 *
 * Note to contributors:
 * Please reference every value you add or modify, especially if the
 * information does not come from the above mentioned specification.
 *
 * Additional references:
 *  - Intel AP-485 revision 32
 *    "Intel Processor Identification and the CPUID Instruction"
 *    http://developer.intel.com/design/xeon/applnots/241618.htm
 *  - DMTF Common Information Model
 *    CIM Schema version 2.19.1
 *    http://www.dmtf.org/standards/cim/
 *  - IPMI 2.0 revision 1.0
 *    "Intelligent Platform Management Interface Specification"
 *    http://developer.intel.com/design/servers/ipmi/spec.htm
 *  - AMD publication #25481 revision 2.28
 *    "CPUID Specification"
 *    http://www.amd.com/us-en/assets/content_type/white_papers_and_tech_docs/25481.pdf
 *  - BIOS Integrity Services Application Programming Interface version 1.0
 *    http://www.intel.com/design/archives/wfm/downloads/bisspec.htm
 */
#include "stdafx.h"
#include <stdio.h>
#include <string.h>
//#include <strings.h>
#include <stdlib.h>
//#include <unistd.h>

#include "version.h"
#include "config.h"
#include "types.h"
#include "util.h"
#include "dmidecode.h"
#include "dmiopt.h"
#include "dmioem.h"


#ifdef _WIN32
#include "winsmbios.h"
#endif /* _WIN32 */

#define out_of_spec "<OUT OF SPEC>"
static const char *bad_index = "<BAD INDEX>";

/*
 * Type-independant Stuff
 */

const char *dmi_string(const struct dmi_header *dm, u8 s)
{
	char *bp = (char *)dm->data;
	size_t i, len;

	if (s == 0)
		return "Not Specified";

	bp += dm->length;
	while (s > 1 && *bp)
	{
		bp += strlen(bp);
		bp++;
		s--;
	}

	if (!*bp)
		return bad_index;

	if (!(opt.flags & FLAG_DUMP))
	{
		/* ASCII filtering */
		len = strlen(bp);
		for (i = 0; i < len; i++)
			if (bp[i] < 32 || bp[i] == 127)
				bp[i] = '.';
	}

	return bp;
}



static int dmi_bcd_range(u8 value, u8 low, u8 high)
{
	if (value > 0x99 || (value & 0x0F) > 0x09)
		return 0;
	if (value < low || value > high)
		return 0;
	return 1;
}

static void dmi_dump(const struct dmi_header *h, const char *prefix)
{
	int row, i;
	const char *s;

	//printf("%sHeader and Data:\n", prefix);
	for (row = 0; row < ((h->length - 1) >> 4) + 1; row++)
	{
		//printf("%s\t", prefix);
		for (i = 0; i < 16 && i < h->length - (row << 4); i++);
			/*printf("%s%02X", i ? " " : "",
			       (h->data)[(row << 4) + i]);
		printf("\n");*/
	}

	if ((h->data)[h->length] || (h->data)[h->length + 1])
	{
		//printf("%sStrings:\n", prefix);
		i = 1;
		while ((s = dmi_string(h, i++)) != bad_index)
		{
			if (opt.flags & FLAG_DUMP)
			{
				int j, l = strlen(s) + 1;
				for (row = 0; row < ((l - 1) >> 4) + 1; row++)
				{
					//printf("%s\t", prefix);
					for (j = 0; j < 16 && j < l - (row << 4); j++)
						printf("%s%02X", j ? " " : "",
						       s[(row << 4) + j]);
					printf("\n");
				}
				/* String isn't filtered yet so do it now */
				printf("%s\t\"", prefix);
				while (*s)
				{
					if (*s < 32 || *s == 127)
						fputc('.', stdout);
					else
						fputc(*s, stdout);
					s++;
				}
				printf("\"\n");
			}
			else
				printf("%s\t%s\n", prefix, s);
		}
	}
}


/*
 * 3.3.2 System Information (Type 1)
 */

static void dmi_system_uuid(const u8 *p, u16 ver, char** ppUUID)
{
	int only0xFF = 1, only0x00 = 1;
	int i;

	for (i = 0; i < 16 && (only0x00 || only0xFF); i++)
	{
		if (p[i] != 0x00) only0x00 = 0;
		if (p[i] != 0xFF) only0xFF = 0;
	}

	if (only0xFF)
	{
		printf("Not Present");
		return;
	}
	if (only0x00)
	{
		printf("Not Settable");
		return;
	}

	/*
	 * As off version 2.6 of the SMBIOS specification, the first 3
	 * fields of the UUID are supposed to be encoded on little-endian.
	 * The specification says that this is the defacto standard,
	 * however I've seen systems following RFC 4122 instead and use
	 * network byte order, so I am reluctant to apply the byte-swapping
	 * for older versions.
	 */

	char* pUUID = new char[50];
	//char buffUUID[50];
	//u8 *buffUUID = NULL;
	//buffUUID = new u8[100];
	memset(pUUID, '\0', 50) ;
	if (ver >= 0x0206)
	{
		/*printf("%02X%02X%02X%02X-%02X%02X-%02X%02X-%02X%02X-%02X%02X%02X%02X%02X%02X",
			p[3], p[2], p[1], p[0], p[5], p[4], p[7], p[6],
			p[8], p[9], p[10], p[11], p[12], p[13], p[14], p[15]);*/
		sprintf(pUUID, "%02X%02X%02X%02X-%02X%02X-%02X%02X-%02X%02X-%02X%02X%02X%02X%02X%02X",
		p[3], p[2], p[1], p[0], p[5], p[4], p[7], p[6],
		p[8], p[9], p[10], p[11], p[12], p[13], p[14], p[15]);
	}
	else
	{
		/*printf("%02X%02X%02X%02X-%02X%02X-%02X%02X-%02X%02X-%02X%02X%02X%02X%02X%02X",
			p[0], p[1], p[2], p[3], p[4], p[5], p[6], p[7],
			p[8], p[9], p[10], p[11], p[12], p[13], p[14], p[15]);*/
		sprintf(pUUID, "%02X%02X%02X%02X-%02X%02X-%02X%02X-%02X%02X-%02X%02X%02X%02X%02X%02X",
		p[0], p[1], p[2], p[3], p[4], p[5], p[6], p[7],
		p[8], p[9], p[10], p[11], p[12], p[13], p[14], p[15]);
	}

	if (ppUUID != NULL)
	{
		*ppUUID = pUUID;
	}
	else
	{
		delete[] pUUID;
		pUUID = NULL;
	}
	
	//g_GainDemideCodeobj.FireOnGainDemideCode(1,buffUUID);
	//WriterTslog(buffUUID);
}

static const char *dmi_system_wake_up_type(u8 code)
{
	/* 3.3.2.1 */
	static const char *type[] = {
		"Reserved", /* 0x00 */
		"Other",
		"Unknown",
		"APM Timer",
		"Modem Ring",
		"LAN Remote",
		"Power Switch",
		"PCI PME#",
		"AC Power Restored" /* 0x08 */
	};

	if (code <= 0x08)
		return type[code];
	return out_of_spec;
}

static void dmi_decode(const struct dmi_header *h, u16 ver, char** ppUUID)
{
	const u8 *data = h->data;

	/*
	 * Note: DMI types 37, 39 and 40 are untested
	 */
	if(h->type == 1)
	{
		/*printf("System Information\n");
		if (h->length < 0x08) return;
		printf("\tManufacturer: %s\n",
			dmi_string(h, data[0x04]));
		printf("\tProduct Name: %s\n",
			dmi_string(h, data[0x05]));
		printf("\tVersion: %s\n",
			dmi_string(h, data[0x06]));
		printf("\tSerial Number: %s\n",
			dmi_string(h, data[0x07]));
		if (h->length < 0x19) return;
		printf("\tUUID: ");*/
		dmi_system_uuid(data + 0x08, ver, ppUUID);
		/*printf("\n");
		printf("\tWake-up Type: %s\n",
			dmi_system_wake_up_type(data[0x18]));
		if (h->length < 0x1B) return;
		printf("\tSKU Number: %s\n",
			dmi_string(h, data[0x19]));
		printf("\tFamily: %s\n",
			dmi_string(h, data[0x1A]));*/
	}
}

static void to_dmi_header(struct dmi_header *h, u8 *data)
{
	h->type = data[0];
	h->length = data[1];
	h->handle = WORD(data + 2);
	h->data = data;
}

static void dmi_table_string(const struct dmi_header *h, const u8 *data, u16 ver, char** ppUUID)
{
	int key;
	u8 offset = opt.string->offset;

	if (offset >= h->length)
		return;

	key = (opt.string->type << 8) | offset;
	switch (key)
	{
		case 0x108:
			dmi_system_uuid(data + offset, ver, ppUUID);
			printf("\n");
			break;
		case 0x305:
			//printf("%s\n", dmi_chassis_type(data[offset]));
			break;
		case 0x406:
			//printf("%s\n", dmi_processor_family(h));
			break;
		case 0x416:
			//dmi_processor_frequency(data + offset);
			//printf("\n");
			break;
		default:
			//printf("%s\n", dmi_string(h, data[offset]));
			;
	}
}

static void dmi_table_dump(u32 base, u16 len, const char *devmem)
{
	u8 *buf;

	if ((buf = (u8 *)mem_chunk(base, len, devmem)) == NULL)
	{
		//fprintf(stderr, "Failed to read table, sorry.\n");
		return;
	}

	if (!(opt.flags & FLAG_QUIET))
		//printf("# Writing %d bytes to %s.\n", len, opt.dumpfile);
	write_dump(32, len, buf, opt.dumpfile, 0);
	free(buf);
}

static void dmi_table(u32 base, u16 len, u16 num, u16 ver, const char *devmem, char** ppUUID)
{
	
	u8 *buf;
	u8 *data;
	int i = 0;

	if (opt.flags & FLAG_DUMP_BIN)
	{
		dmi_table_dump(base, len, devmem);
		return;
	}

	if (!(opt.flags & FLAG_QUIET))
	{
		if (opt.type == NULL)
		{
			printf("%u structures occupying %u bytes.\n",
				num, len);
			if (!(opt.flags & FLAG_FROM_DUMP))
				printf("Table at 0x%08X.\n", base);
		}
		printf("\n");
	}
	/*
	 * if devmem is NULL then base has the SMBIOS Table address
	 * already allocated and not the physical memory address that 
	 * needs to be mapped.
	 * This change was made to support Windows 2003 that blocks
	 * access to physical memory but returns the SMBIOS Table
	 * througth GetSystemFirmwareTable API.
	 *
	 * see more on winsmbios.h and winsmbios.c
	 */	
	if (devmem == NULL) 
	{
          buf = (u8 *)base;
     } else {
	if ((buf = (u8 *)(mem_chunk(base, len, devmem))) == NULL)
	{
		fprintf(stderr, "Table is unreachable, sorry."
#ifndef USE_MMAP
			" Try compiling dmidecode with -DUSE_MMAP."
#endif
			"\n");
		return;
		}
	}
	data = buf;
	while (i < num && data+4 <= buf + len) /* 4 is the length of an SMBIOS structure header */
	{
		u8 *next;
		struct dmi_header h;
		int display;

		to_dmi_header(&h, data);
		display = ((opt.type == NULL || opt.type[h.type])
			&& !((opt.flags & FLAG_QUIET) && (h.type > 39 && h.type <= 127))
			&& !opt.string);

		/*
		 * If a short entry is found (less than 4 bytes), not only it
		 * is invalid, but we cannot reliably locate the next entry.
		 * Better stop at this point, and let the user know his/her
		 * table is broken.
		 */
		if (h.length < 4)
		{
			printf("Invalid entry length (%u). DMI table is "
			       "broken! Stop.\n\n", (unsigned int)h.length);
			opt.flags |= FLAG_QUIET;
			break;
		}

		/* In quiet mode, stop decoding at end of table marker */
		if ((opt.flags & FLAG_QUIET) && h.type == 127)
			break;

		if (display
		 && (!(opt.flags & FLAG_QUIET) || (opt.flags & FLAG_DUMP)))
			//printf("Handle 0x%04X, DMI type %d, %d bytes\n",
			//	h.handle, h.type, h.length);

		/* assign vendor for vendor-specific decodes later */
		if (h.type == 0 && h.length >= 5)
			dmi_set_vendor(dmi_string(&h, data[0x04]));

		/* look for the next handle */
		next = data + h.length;
		while (next - buf + 1 < len && (next[0] != 0 || next[1] != 0))
			next++;
		next += 2;
		if (display)
		{
			if (next - buf <= len)
			{
				if (opt.flags & FLAG_DUMP)
				{
					dmi_dump(&h, "\t");
					printf("\n");
				}
				else
					dmi_decode(&h, ver, ppUUID);
			}
			else if (!(opt.flags & FLAG_QUIET))
				printf("\t<TRUNCATED>\n\n");
		}
		else if (opt.string != NULL
		      && opt.string->type == h.type)
			dmi_table_string(&h, data, ver, ppUUID);

		data = next;
		i++;
	}

	if (!(opt.flags & FLAG_QUIET))
	{
		if (i != num)
			printf("Wrong DMI structures count: %d announced, "
				"only %d decoded.\n", num, i);
		if (data - buf != len)
			printf("Wrong DMI structures length: %d bytes "
				"announced, structures occupy %d bytes.\n",
				len, (unsigned int)(data - buf));
	}

	//free(buf);
}


static void dmi_table_1(u8* base, u16 len, u16 num, u16 ver, const char* devmem, char** ppUUID)
{

	u8* buf;
	u8* data;
	int i = 0;

	if (opt.flags & FLAG_DUMP_BIN)
	{
		return;
	}

	/*
	 * if devmem is NULL then base has the SMBIOS Table address
	 * already allocated and not the physical memory address that
	 * needs to be mapped.
	 * This change was made to support Windows 2003 that blocks
	 * access to physical memory but returns the SMBIOS Table
	 * througth GetSystemFirmwareTable API.
	 *
	 * see more on winsmbios.h and winsmbios.c
	 */
	if (devmem == NULL)
	{
		buf = base;
	}
	data = buf;
	while (i < num && data + 4 <= buf + len) /* 4 is the length of an SMBIOS structure header */
	{
		u8* next;
		struct dmi_header h;
		int display;

		to_dmi_header(&h, data);
		display = ((opt.type == NULL || opt.type[h.type])
			&& !((opt.flags & FLAG_QUIET) && (h.type > 39 && h.type <= 127))
			&& !opt.string);

		/*
		 * If a short entry is found (less than 4 bytes), not only it
		 * is invalid, but we cannot reliably locate the next entry.
		 * Better stop at this point, and let the user know his/her
		 * table is broken.
		 */
		if (h.length < 4)
		{
			printf("Invalid entry length (%u). DMI table is "
				"broken! Stop.\n\n", (unsigned int)h.length);
			opt.flags |= FLAG_QUIET;
			break;
		}

		/* In quiet mode, stop decoding at end of table marker */
		if ((opt.flags & FLAG_QUIET) && h.type == 127)
			break;

		if (display
			&& (!(opt.flags & FLAG_QUIET) || (opt.flags & FLAG_DUMP)))
			//printf("Handle 0x%04X, DMI type %d, %d bytes\n",
			//	h.handle, h.type, h.length);

		/* assign vendor for vendor-specific decodes later */
			if (h.type == 0 && h.length >= 5)
				dmi_set_vendor(dmi_string(&h, data[0x04]));

		/* look for the next handle */
		next = data + h.length;
		while (next - buf + 1 < len && (next[0] != 0 || next[1] != 0))
			next++;
		next += 2;
		if (display)
		{
			if (next - buf <= len)
			{
				if (opt.flags & FLAG_DUMP)
				{
					dmi_dump(&h, "\t");
					printf("\n");
				}
				else
					dmi_decode(&h, ver, ppUUID);
			}
			else if (!(opt.flags & FLAG_QUIET))
				printf("\t<TRUNCATED>\n\n");
		}
		else if (opt.string != NULL
			&& opt.string->type == h.type)
			dmi_table_string(&h, data, ver, ppUUID);

		data = next;
		i++;
	}

	if (!(opt.flags & FLAG_QUIET))
	{
		if (i != num)
			printf("Wrong DMI structures count: %d announced, "
				"only %d decoded.\n", num, i);
		if (data - buf != len)
			printf("Wrong DMI structures length: %d bytes "
				"announced, structures occupy %d bytes.\n",
				len, (unsigned int)(data - buf));
	}

	//free(buf);
}


/*
 * Build a crafted entry point with table address hard-coded to 32,
 * as this is where we will put it in the output file. We adjust the
 * DMI checksum appropriately. The SMBIOS checksum needs no adjustment.
 */
static void overwrite_dmi_address(u8 *buf)
{
	buf[0x05] += buf[0x08] + buf[0x09] + buf[0x0A] + buf[0x0B] - 32;
	buf[0x08] = 32;
	buf[0x09] = 0;
	buf[0x0A] = 0;
	buf[0x0B] = 0;
}

static int smbios_decode(u8 *buf, const char *devmem, char** ppUUID)
{
	u16 ver;

	if (!checksum(buf, buf[0x05])
	 || memcmp(buf + 0x10, "_DMI_", 5) != 0
	 || !checksum(buf + 0x10, 0x0F))
		return 0;

	ver = (buf[0x06] << 8) + buf[0x07];
	/* Some BIOS report weird SMBIOS version, fix that up */
	switch (ver)
	{
		case 0x021F:
			if (!(opt.flags & FLAG_QUIET))
				printf("SMBIOS version fixup (2.%d -> 2.%d).\n",
				       31, 3);
			ver = 0x0203;
			break;
		case 0x0233:
			if (!(opt.flags & FLAG_QUIET))
				printf("SMBIOS version fixup (2.%d -> 2.%d).\n",
				       51, 6);
			ver = 0x0206;
			break;
	}
	if (!(opt.flags & FLAG_QUIET))
		printf("SMBIOS %u.%u present.\n",
			ver >> 8, ver & 0xFF);

	dmi_table(DWORD(buf + 0x18), WORD(buf + 0x16), WORD(buf + 0x1C),
		ver, devmem, ppUUID);

	if (opt.flags & FLAG_DUMP_BIN)
	{
		u8 crafted[32];

		memcpy(crafted, buf, 32);
		overwrite_dmi_address(crafted + 0x10);

		if (!(opt.flags & FLAG_QUIET))
			printf("# Writing %d bytes to %s.\n", crafted[0x05],
				opt.dumpfile);
		write_dump(0, crafted[0x05], crafted, opt.dumpfile, 1);
	}

	return 1;
}

static int legacy_decode(u8 *buf, const char *devmem, char** ppUUID)
{
	if (!checksum(buf, 0x0F))
		return 0;

	if (!(opt.flags & FLAG_QUIET))
		printf("Legacy DMI %u.%u present.\n",
			buf[0x0E] >> 4, buf[0x0E] & 0x0F);

	dmi_table(DWORD(buf + 0x08), WORD(buf + 0x06), WORD(buf + 0x0C),
		((buf[0x0E] & 0xF0) << 4) + (buf[0x0E] & 0x0F), devmem, ppUUID);

	if (opt.flags & FLAG_DUMP_BIN)
	{
		u8 crafted[16];

		memcpy(crafted, buf, 16);
		overwrite_dmi_address(crafted);

		printf("# Writing %d bytes to %s.\n", 0x0F, opt.dumpfile);
		write_dump(0, 0x0F, crafted, opt.dumpfile, 1);
	}

	return 1;
}

/*
 * Probe for EFI interface
 */
#define EFI_NOT_FOUND   (-1)
#define EFI_NO_SMBIOS   (-2)
static int address_from_efi(size_t *address)
{
	FILE *efi_systab;
	const char *filename;
	char linebuf[64];
	int ret;

	*address = 0; /* Prevent compiler warning */

	/*
	 * Linux up to 2.6.6: /proc/efi/systab
	 * Linux 2.6.7 and up: /sys/firmware/efi/systab
	 */
	if ((efi_systab = fopen(filename = "/sys/firmware/efi/systab", "r")) == NULL
	 && (efi_systab = fopen(filename = "/proc/efi/systab", "r")) == NULL)
	{
		/* No EFI interface, fallback to memory scan */
		return EFI_NOT_FOUND;
	}
	ret = EFI_NO_SMBIOS;
	while ((fgets(linebuf, sizeof(linebuf) - 1, efi_systab)) != NULL)
	{
		char *addrp = strchr(linebuf, '=');
		*(addrp++) = '\0';
		if (strcmp(linebuf, "SMBIOS") == 0)
		{
			*address = strtoul(addrp, NULL, 0);
			if (!(opt.flags & FLAG_QUIET))
				printf("# SMBIOS entry point at 0x%08lx\n",
				       (unsigned long)*address);
			ret = 0;
			break;
		}
	}
	if (fclose(efi_systab) != 0)
		perror(filename);

	if (ret == EFI_NO_SMBIOS)
		fprintf(stderr, "%s: SMBIOS entry point missing\n", filename);
	return ret;
}

int get_dmide_code(char** ppUUID)
{
	int ret = 0;                /* Returned value */
	int found = 0;
	size_t fp;
	int efi;
	u8 *buf;

#ifdef _WIN32
    /*
     * these varibles are used only when run on windows 2003 or above.
     * Since these versions block access to physical memory.
     * Windows NT, 2000 and XP still accessing physical memory througth
     * mem_chunck
     */
    int num_structures = 0;
    PRawSMBIOSData smb = NULL;
#endif /* _WIN32 */
	
	if (sizeof(u8) != 1 || sizeof(u16) != 2 || sizeof(u32) != 4 || '\0' != 0)
	{
		exit(255);
	}

	/* Set default option values */
	opt.devmem = DEFAULT_MEM_DEV;
	opt.flags = 0;

	if (opt.flags & FLAG_HELP)
	{
		print_help();
		goto exit_free;
	}

	if (opt.flags & FLAG_VERSION)
	{
		goto exit_free;
	}

	/* Read from dump if so instructed */
	if (opt.flags & FLAG_FROM_DUMP)
	{
			
		if ((buf = (u8 *)(mem_chunk(0, 0x20, opt.dumpfile))) == NULL)
		{
			ret = 1;
			goto exit_free;
		}

		if (memcmp(buf, "_SM_", 4) == 0)
		{
			if (smbios_decode(buf, opt.dumpfile, ppUUID))
				found++;
		}
		else if (memcmp(buf, "_DMI_", 5) == 0)
		{
			if (legacy_decode(buf, opt.dumpfile, ppUUID))
				found++;
		}
		goto done;
	}

	/* First try EFI (ia64, Intel-based Mac) */
	efi = address_from_efi(&fp);
	switch (efi)
	{
		case EFI_NOT_FOUND:
			goto memory_scan;
		case EFI_NO_SMBIOS:
			ret = 1;
			goto exit_free;
	}

	if ((buf = (u8*)(mem_chunk(fp, 0x20, opt.devmem))) == NULL)
	{
		ret = 1;
		goto exit_free;
	}

	if (smbios_decode(buf, opt.devmem, ppUUID))
		found++;
	goto done;

memory_scan:
	/* Fallback to memory scan (x86, x86_64) */
		
    /*
     * If running on windows, checks if its Windows 2003 or vista and
     * get the SMBIOS data without access to physical memory.
     * If its Windows NT, 2000 or XP, access the physical memory and
     * scans for SMBIOS table entry point, just like all other OS.
     * If its Windows 9x or Me, print error and exits.
     */
#ifdef __WIN32__
    switch(get_windows_platform()){
                                   
        case WIN_2003_VISTA://gets the SMBIOS table, prints values and exits

            //loads the GetSystemFirmwareTable function
            if(!LocateNtdllEntryPoints()){
       	        return 0;
                goto exit_free;             	
            }

            //gets the raw smbios table
            smb = get_raw_smbios_table();
			if (smb != NULL)
			{
				num_structures = count_smbios_structures((char *)(&smb->SMBIOSTableData[0]), smb->Length);

				//shows the smbios information
				dmi_table_1(&smb->SMBIOSTableData[0], smb->Length, num_structures,
					(smb->SMBIOSMajorVersion<<8)+smb->SMBIOSMinorVersion, NULL, ppUUID);

				free(smb);
			}
            
            goto exit_free;
        break;
        
        case WIN_UNSUPORTED://prints error and exits
          //  printf("\nDMIDECODE runs on Windows NT/2000/XP. Windows 9x/Me are not supported.\n");
            goto exit_free;
        break;
        
        default:
            /*
             * do nothing. Follow the code below, scans for the 
             * SMBIOS table entry point, etc...
             */
        break;
    }
#endif  /*__WIN32__*/

	if ((buf = (u8 *)(mem_chunk(0xF0000, 0x10000, opt.devmem))) == NULL)
	{
		ret = 1;
		goto exit_free;
	}

	for (fp = 0; fp <= 0xFFF0; fp += 16)
	{
		if (memcmp(buf + fp, "_SM_", 4) == 0 && fp <= 0xFFE0)
		{
			if (smbios_decode(buf+fp, opt.devmem, ppUUID))
			{
				found++;
				fp += 16;
			}
		}
		else if (memcmp(buf + fp, "_DMI_", 5) == 0)
		{
			if (legacy_decode(buf + fp, opt.devmem, ppUUID))
				found++;
		}
	}

done:
	if (!found && !(opt.flags & FLAG_QUIET))
		printf("# No SMBIOS nor DMI entry point found, sorry.\n");

	free(buf);
exit_free:
	free(opt.type);
	//system("pause");
	return ret;
}
