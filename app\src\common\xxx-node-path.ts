import path from 'path'
import {app} from 'electron'
import * as fs from 'fs'
import { isDev } from './env';

let macStorePath: string = '';
let basePath = path.dirname(process.execPath);
if (process.platform === 'darwin') {
    macStorePath = path.join(process.env.HOME as string, 'Library', 'Application Support', 'XLPlayer');
    try {
        if (!fs.existsSync(macStorePath)) {
            fs.mkdirSync(macStorePath, {recursive: true});
        }
    } catch(e) {
        console.log(e)
    }
    basePath = process.env.APP_BASE_DIR as string;
    if (!basePath) {
        try {
            // renderer进程里面无法获取这个信息，所以从环境变量获取
            basePath = app.getAppPath();
        } catch(e) {

        }
    }
    if (basePath && basePath.length > 0) {
        basePath = path.join(basePath, '../../'); //contents目录
    }
    if (isDev) {
        basePath = path.join(basePath, '/bin')
    }
} else {
    if (isDev) {
        basePath = path.join(basePath, '../../../../bin')
    }
}

let profilesPath: string = '';
let sdkPath: string = '';

export function GetExePath(): string {
    return basePath;
}

export function GetIcoPath(): string {
    if (process.platform !== 'darwin') {
        return path.join(basePath, 'thunder11.ico');
    }

    return path.join(basePath, 'resources/xxx');
}

export function GetLogsPath(): string {
    if (process.platform === 'darwin') {
        if (!fs.existsSync(path.join(macStorePath, 'logs'))) {
            fs.mkdirSync(path.join(macStorePath, 'logs'), {recursive: true})
        }
        return path.join(macStorePath, 'logs');
    } else {
        return path.join(basePath, 'logs');
    }
}

export function GetResourceAppPath(): string {
    return path.join(basePath, './resources/app');
}

export function GetPluginPath(): string {
    return path.join(GetResourceAppPath(), 'plugins');
}

export function GetDkAddonNodeName(): string {
    if (process.platform !== 'darwin') {
        return 'dk_addon.node';
    }
    return 'dk.node';
}

export function GetPlayerControlAddonNodeName(): string {
    if (process.platform !== 'darwin') {
        return 'pc_addon.node';
    }
    return 'playercontrol.node';
}

export function GetXxxNodePath(): string {
    if (process.platform !== 'darwin') {
        return basePath
    }
    return GetResourceAppPath();
}

export function GetProfilesPath(): string {
    if (profilesPath.length > 0) {
        return profilesPath;
    }
    if (process.platform === 'darwin') {
        profilesPath = path.join(macStorePath, 'profiles');
        if (!fs.existsSync(profilesPath)) {
            fs.mkdirSync(profilesPath, {recursive: true});
        }
    } else {
        profilesPath = path.join(basePath, '../profiles');
    }
    return profilesPath;
}

export function GetSdkPath(): string {
    if (sdkPath.length > 0) {
        return sdkPath;
    }
    sdkPath = path.join(basePath, 'SDK');
    return sdkPath;
}