<svg width="140" height="140" viewBox="0 0 140 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.9">
<path d="M83.7617 25.8962C85.6947 22.5481 89.9758 21.401 93.3239 23.334L112.521 34.4173L115.218 46.0784L117.767 57.1031L105.519 78.3167C103.586 81.6647 99.3053 82.8118 95.9573 80.8788L69.0646 65.3523C65.7165 63.4193 64.5694 59.1382 66.5024 55.7902L83.7617 25.8962Z" fill="#BAC2D3"/>
<path d="M83.7617 25.8962C85.6947 22.5481 89.9758 21.401 93.3239 23.334L112.521 34.4173L115.218 46.0784L117.767 57.1031L105.519 78.3167C103.586 81.6647 99.3053 82.8118 95.9573 80.8788L69.0646 65.3523C65.7165 63.4193 64.5694 59.1382 66.5024 55.7902L83.7617 25.8962Z" fill="white" fill-opacity="0.6"/>
<path d="M93.0324 23.8389L112.009 34.7949L114.65 46.2104L117.148 57.0095L105.014 78.025C103.242 81.094 99.3176 82.1457 96.2486 80.3738L69.3561 64.8474C66.2871 63.0755 65.2354 59.1507 67.0073 56.0816L84.2666 26.1877C86.0385 23.1186 89.9634 22.067 93.0324 23.8389Z" stroke="black" stroke-opacity="0.03" stroke-width="1.16667"/>
</g>
<g opacity="0.9" filter="url(#filter0_d_606_72393)">
<path d="M112.52 34.416L107.51 43.0924C105.577 46.4404 106.724 50.7216 110.072 52.6546L117.777 57.1027L112.52 34.416Z" fill="#F8F8F8"/>
</g>
<foreignObject x="41.2435" y="10.9056" width="59.431" height="64.7161"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.33px);clip-path:url(#bgblur_0_606_72393_clip_path);height:100%;width:100%"></div></foreignObject><g opacity="0.9" data-figma-bg-blur-radius="4.66667">
<path d="M55.0828 20.7615C56.0834 17.0272 59.9217 14.8111 63.656 15.8117L85.0674 21.5489L90.6911 32.1144L96.0064 42.1038L89.6666 65.7645C88.666 69.4987 84.8276 71.7148 81.0934 70.7142L51.0985 62.6771C47.3642 61.6765 45.1481 57.8382 46.1487 54.1039L55.0828 20.7615Z" fill="#BAC2D3"/>
<path d="M63.5051 16.3749L84.6706 22.0461L90.1764 32.389L95.3839 42.1737L89.1032 65.6135C88.186 69.0365 84.6671 71.0682 81.244 70.151L51.2494 62.1139C47.8263 61.1967 45.7947 57.6778 46.7119 54.2547L55.6459 20.9124C56.5632 17.4893 60.082 15.4577 63.5051 16.3749Z" stroke="black" stroke-opacity="0.03" stroke-width="1.16667"/>
</g>
<g opacity="0.9" filter="url(#filter2_d_606_72393)">
<path d="M85.0664 21.5508L82.4734 31.228C81.4728 34.9623 83.6889 38.8006 87.4231 39.8012L96.0162 42.1037L85.0664 21.5508Z" fill="#F8F8F8"/>
</g>
<path d="M26.832 25.668C26.832 21.802 29.966 18.668 33.832 18.668H55.9987L64.1654 27.418L71.885 35.6913V60.1866C71.885 64.0526 68.751 67.1866 64.885 67.1866H33.832C29.966 67.1866 26.832 64.0526 26.832 60.1866L26.832 25.668Z" fill="#BAC2D3"/>
<path d="M26.832 25.668C26.832 21.802 29.966 18.668 33.832 18.668H55.9987L64.1654 27.418L71.885 35.6913V60.1866C71.885 64.0526 68.751 67.1866 64.885 67.1866H33.832C29.966 67.1866 26.832 64.0526 26.832 60.1866L26.832 25.668Z" fill="white" fill-opacity="0.78"/>
<path d="M33.832 19.251H55.7441L63.7393 27.8164L71.3018 35.9199V60.1865C71.3018 63.7304 68.4286 66.6035 64.8848 66.6035H33.832C30.2882 66.6035 27.415 63.7304 27.415 60.1865L27.415 25.668C27.415 22.1241 30.2882 19.251 33.832 19.251Z" stroke="black" stroke-opacity="0.03" stroke-width="1.16667"/>
<g opacity="0.9" filter="url(#filter3_d_606_72393)">
<path d="M56 18.668V28.6866C56 32.5526 59.134 35.6866 63 35.6866H71.8962L56 18.668Z" fill="#F8F8F8"/>
</g>
<g filter="url(#filter4_i_606_72393)">
<path d="M20.5045 57.2912C19.1871 50.1233 24.691 43.5156 31.979 43.5156H64.4477C67.5571 43.5156 70.5379 44.7569 72.7283 46.9639L78.1059 52.3822C80.2963 54.5892 83.2771 55.8304 86.3865 55.8304H106.048C113.527 55.8304 119.074 62.7685 117.429 70.0642L110.47 100.916C109.27 106.237 104.543 110.016 99.0889 110.016H39.9127C34.2828 110.016 29.4559 105.995 28.4382 100.458L20.5045 57.2912Z" fill="url(#paint0_linear_606_72393)"/>
</g>
<path d="M31.9785 43.9883H64.4473C67.2443 43.9883 69.9334 45.0353 71.9902 46.9111L72.3926 47.2969L77.7705 52.7158C80.0497 55.012 83.1514 56.3037 86.3867 56.3037H106.048C113.111 56.3037 118.379 62.7535 117.036 69.6318L116.968 69.96L110.008 100.812C108.856 105.916 104.322 109.543 99.0889 109.543H39.9131C34.6802 109.543 30.1704 105.922 29.0059 100.866L28.9033 100.372L20.9697 57.2061C19.7058 50.3289 24.9863 43.9886 31.9785 43.9883Z" stroke="black" stroke-opacity="0.02" stroke-width="0.945946"/>
<rect x="85.6719" y="81.2266" width="32.6667" height="32.6667" rx="16.3333" fill="url(#paint1_linear_606_72393)"/>
<path d="M108.499 91.0671C106.829 89.3969 104.121 89.3969 102.451 91.0671L100.244 93.2743C99.9667 93.5514 99.9667 94.0006 100.244 94.2776C100.521 94.5547 100.97 94.5547 101.247 94.2776L103.454 92.0704C104.57 90.9544 106.38 90.9544 107.496 92.0704C108.612 93.1865 108.612 94.996 107.496 96.1121L105.604 98.004C104.488 99.1201 102.679 99.1201 101.562 98.004C101.285 97.7269 100.836 97.7269 100.559 98.004C100.282 98.281 100.282 98.7303 100.559 99.0073C102.229 100.678 104.937 100.678 106.607 99.0073L108.499 97.1154C110.17 95.4452 110.17 92.7373 108.499 91.0671Z" fill="white"/>
<path d="M95.5144 104.052C97.1846 105.722 99.8925 105.722 101.563 104.052L103.596 102.019C103.873 101.742 103.873 101.293 103.596 101.016C103.319 100.739 102.869 100.739 102.592 101.016L100.559 103.049C99.4433 104.165 97.6338 104.165 96.5177 103.049C95.4016 101.933 95.4016 100.123 96.5177 99.0072L98.1541 97.3708C99.2702 96.2548 101.08 96.2548 102.196 97.3708C102.473 97.6479 102.922 97.6479 103.199 97.3708C103.476 97.0938 103.476 96.6446 103.199 96.3675C101.529 94.6973 98.821 94.6973 97.1508 96.3675L95.5144 98.0039C93.8442 99.6741 93.8442 102.382 95.5144 104.052Z" fill="white"/>
<path d="M108.499 91.0671C106.829 89.3969 104.121 89.3969 102.451 91.0671L100.244 93.2743C99.9667 93.5514 99.9667 94.0006 100.244 94.2776C100.521 94.5547 100.97 94.5547 101.247 94.2776L103.454 92.0704C104.57 90.9544 106.38 90.9544 107.496 92.0704C108.612 93.1865 108.612 94.996 107.496 96.1121L105.604 98.004C104.488 99.1201 102.679 99.1201 101.562 98.004C101.285 97.7269 100.836 97.7269 100.559 98.004C100.282 98.281 100.282 98.7303 100.559 99.0073C102.229 100.678 104.937 100.678 106.607 99.0073L108.499 97.1154C110.17 95.4452 110.17 92.7373 108.499 91.0671Z" stroke="white" stroke-opacity="0.01" stroke-width="0.756757"/>
<path d="M95.5144 104.052C97.1846 105.722 99.8925 105.722 101.563 104.052L103.596 102.019C103.873 101.742 103.873 101.293 103.596 101.016C103.319 100.739 102.869 100.739 102.592 101.016L100.559 103.049C99.4433 104.165 97.6338 104.165 96.5177 103.049C95.4016 101.933 95.4016 100.123 96.5177 99.0072L98.1541 97.3708C99.2702 96.2548 101.08 96.2548 102.196 97.3708C102.473 97.6479 102.922 97.6479 103.199 97.3708C103.476 97.0938 103.476 96.6446 103.199 96.3675C101.529 94.6973 98.821 94.6973 97.1508 96.3675L95.5144 98.0039C93.8442 99.6741 93.8442 102.382 95.5144 104.052Z" stroke="white" stroke-opacity="0.01" stroke-width="0.756757"/>
<g filter="url(#filter5_f_606_72393)">
<ellipse cx="73.0276" cy="128.307" rx="39.1916" ry="2.30539" fill="#ADBDDE" fill-opacity="0.38"/>
</g>
<defs>
<filter id="filter0_d_606_72393" x="101.904" y="34.416" width="20.5404" height="32.0208" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.66667"/>
<feGaussianBlur stdDeviation="2.33333"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_606_72393"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_606_72393" result="shape"/>
</filter>
<clipPath id="bgblur_0_606_72393_clip_path" transform="translate(-41.2435 -10.9056)"><path d="M55.0828 20.7615C56.0834 17.0272 59.9217 14.8111 63.656 15.8117L85.0674 21.5489L90.6911 32.1144L96.0064 42.1038L89.6666 65.7645C88.666 69.4987 84.8276 71.7148 81.0934 70.7142L51.0985 62.6771C47.3642 61.6765 45.1481 57.8382 46.1487 54.1039L55.0828 20.7615Z"/>
</clipPath><filter id="filter2_d_606_72393" x="77.5677" y="21.5508" width="23.1146" height="29.8861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.66667"/>
<feGaussianBlur stdDeviation="2.33333"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_606_72393"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_606_72393" result="shape"/>
</filter>
<filter id="filter3_d_606_72393" x="51.3333" y="18.668" width="25.2279" height="26.3529" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.66667"/>
<feGaussianBlur stdDeviation="2.33333"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_606_72393"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_606_72393" result="shape"/>
</filter>
<filter id="filter4_i_606_72393" x="20.3086" y="43.5156" width="97.4102" height="67.2685" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.768463"/>
<feGaussianBlur stdDeviation="3.45808"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.898039 0 0 0 0 0.917647 0 0 0 0 0.945098 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_606_72393"/>
</filter>
<filter id="filter5_f_606_72393" x="26.8359" y="119.002" width="92.3828" height="18.6113" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.5" result="effect1_foregroundBlur_606_72393"/>
</filter>
<linearGradient id="paint0_linear_606_72393" x1="89.8393" y1="55.6742" x2="63.1984" y2="126.926" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E8EDF4"/>
</linearGradient>
<linearGradient id="paint1_linear_606_72393" x1="86.0567" y1="80.7431" x2="118.005" y2="114.023" gradientUnits="userSpaceOnUse">
<stop stop-color="#92BEFF"/>
<stop offset="1" stop-color="#4C93FF"/>
</linearGradient>
</defs>
</svg>
