import { server } from '@xunlei/node-net-ipc/dist/ipc-server';
import { EventEmitter } from 'events';
import { DownLoad_IPC_KEY, IDownloadModelPositionParams } from '../download-model-type'

export class DownloadModelManagerImpl {
  private static instance: DownloadModelManagerImpl;

  private eventContainer: EventEmitter = new EventEmitter();
  // private categoryManager = new CategoryManager();

  public async init(): Promise<void> {
    server.registerFunctions({
      DownloadModelManagerPositionTask: (params: IDownloadModelPositionParams) => {
        console.log('>>>>>>>>>>>>>> DownloadModelManagerPositionTask', params )
        this.eventContainer.emit(DownLoad_IPC_KEY.OnDownloadModelManagerPositionTask, params)
      }
    })
    // this.categoryManager.init(this.apiProxy);
  }

  public static GetInstance(): DownloadModelManagerImpl {
    if (!DownloadModelManagerImpl.instance) {
      if (global.DownloadModelManagerImplInstance) {
        DownloadModelManagerImpl.instance = global.DownloadModelManagerImplInstance;
      } else {
        DownloadModelManagerImpl.instance = new DownloadModelManagerImpl();
        global.DownloadModelManagerImplInstance = DownloadModelManagerImpl.instance;
      }
    }
    return DownloadModelManagerImpl.instance;
  }

  public attachDownloadModelPositionTaskEvent(cb: (params: IDownloadModelPositionParams) => void) {
    return this.eventContainer.on(DownLoad_IPC_KEY.OnDownloadModelManagerPositionTask, cb);
  }
  
  public detachDownloadModelPositionTaskEvent(cb: (params: IDownloadModelPositionParams) => void): void {
    console.log('detachDownloadModelPositionTaskEvent', cb)
    this.eventContainer.off(DownLoad_IPC_KEY.OnDownloadModelManagerPositionTask, cb);
  }

  public async positionDownloadTask(params: IDownloadModelPositionParams) {
    this.eventContainer.emit(DownLoad_IPC_KEY.OnDownloadModelManagerPositionTask, params)
  }

}

export function GetDownloadModelManager(): DownloadModelManagerImpl {
  return DownloadModelManagerImpl.GetInstance();
}
