import { CallApiProxyImplWithIpcClient } from '../call-api-impl';
import * as BaseType from '../base'
export class BtTask {
    private apiProxy: CallApiProxyImplWithIpcClient | null = null;
    taskId: number = 0;
    constructor(apiProxy: CallApiProxyImplWithIpcClient, id: number) {
        this.apiProxy = apiProxy;
        this.taskId = id;
    }

    public async getInfoHash(): Promise<string> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskBtTaskGetInfoHash', this.taskId);
        if (info.bSucc) {
            return info.result as string;
        }

        return '';
    }

    public updateDownloadIndex(downloadIndexs: number[]): void {
        this.apiProxy!.CallApi('TaskManagerTaskBtTaskUpdateDownloadIndex', this.taskId, downloadIndexs);
    }

    public cancelSubTask(downloadIndexs: number[]): void {
        this.apiProxy!.CallApi('TaskManagerTaskBtTaskCancelSubTask', this.taskId, downloadIndexs);
    }

    public downloadSubTask(downloadIndexs: number[]): void {
        this.apiProxy!.CallApi('TaskManagerTaskBtTaskDownloadSubTask', this.taskId, downloadIndexs);
    }

    public deleteSubTask(downloadIndexs: number[]): void {
        this.apiProxy!.CallApi('TaskManagerTaskBtTaskDeleteSubTask', this.taskId, downloadIndexs);
    }

    public async isOnlyOneFile(): Promise<boolean> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskBtTaskIsOnlyOneFile', this.taskId);
        if (info.bSucc) {
            return info.result as boolean;
        }

        return false;
    }

    public async getDownloadCount(): Promise<number> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskBtTaskGetDownloadCount', this.taskId);
        if (info.bSucc) {
            return info.result as number;
        }

        return 0;
    }

    public async getCompleteCount(): Promise<number> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskBtTaskGetCompleteCount', this.taskId);
        if (info.bSucc) {
            return info.result as number;
        }

        return 0;
    }

    public async getTotalCount(): Promise<number> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskBtTaskGetTotalCount', this.taskId);
        if (info.bSucc) {
            return info.result as number;
        }

        return 0;
    }

    public async getBtFileInfos(): Promise<BaseType.BtFileDownloadInfo[]> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskBtTaskGetBtFileInfos', this.taskId);
        if (info.bSucc) {
            return info.result as BaseType.BtFileDownloadInfo[];
        }

        return [];
    }

    public async updateBtSubFileScheduler(s: BaseType.XLBTTaskSubFileSchedulerType) {
        await this.apiProxy!.CallApi('TaskManagerTaskBtTaskUpdateBtSubFileScheduler', this.taskId, s);
    }

    public async getBtSubFileScheduler(): Promise<BaseType.XLBTTaskSubFileSchedulerType> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskBtTaskGetBtSubFileScheduler', this.taskId);
        if (info.bSucc) {
            return info.result as BaseType.XLBTTaskSubFileSchedulerType;
        }

        return BaseType.XLBTTaskSubFileSchedulerType.XL_BTTaskSubFileInvalidValue;
    }
}