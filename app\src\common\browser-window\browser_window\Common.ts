import { buildXbaseRemoteNamespaceChannelString } from "../process_remote/Common"

const PRE_NAMESPACE_STR = 'BrowserWindow'

export function buildBrowserWindowNamespaceChannelString(
  channel: string,
): string {
  return `${PRE_NAMESPACE_STR}.${channel}`
}

export function buildFullBrowserWindowNamespaceChannelString(
  channel: string,
): string {
  return buildXbaseRemoteNamespaceChannelString(
    buildBrowserWindowNamespaceChannelString(channel),
  )
}
