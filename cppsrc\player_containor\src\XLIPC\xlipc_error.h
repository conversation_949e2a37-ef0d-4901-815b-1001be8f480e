#ifndef __XLIPC_ERROR_H__
#define __XLIPC_ERROR_H__
#include <winerror.h>
#include "xlipc_define.h"

/* Expand this list if necessary. */
#define XLIPC_RESULT_ERRNO_MAP(XX)                                                      \
  XX(E2BIG, "argument list too long")                                         \
  XX(EACCES, "permission denied")                                             \
  XX(EADDRINUSE, "address already in use")                                    \
  XX(EADDRNOTAVAIL, "address not available")                                  \
  XX(EAFNOSUPPORT, "address family not supported")                            \
  XX(EAGAIN, "resource temporarily unavailable")                              \
  XX(EAI_ADDRFAMILY, "address family not supported")                          \
  XX(EAI_AGAIN, "temporary failure")                                          \
  XX(EAI_BADFLAGS, "bad ai_flags value")                                      \
  XX(EAI_BADHINTS, "invalid value for hints")                                 \
  XX(EAI_CANCELED, "request canceled")                                        \
  XX(EAI_FAIL, "permanent failure")                                           \
  XX(EAI_FAMILY, "ai_family not supported")                                   \
  XX(EAI_MEMORY, "out of memory")                                             \
  XX(EAI_NODATA, "no address")                                                \
  XX(EAI_NONAME, "unknown node or service")                                   \
  XX(EAI_OVERFLOW, "argument buffer overflow")                                \
  XX(EAI_PROTOCOL, "resolved protocol is unknown")                            \
  XX(EAI_SERVICE, "service not available for socket type")                    \
  XX(EAI_SOCKTYPE, "socket type not supported")                               \
  XX(EALREADY, "connection already in progress")                              \
  XX(EBADF, "bad file descriptor")                                            \
  XX(EBUSY, "resource busy or locked")                                        \
  XX(ECANCELED, "operation canceled")                                         \
  XX(ECHARSET, "invalid Unicode character")                                   \
  XX(ECONNABORTED, "software caused connection abort")                        \
  XX(ECONNREFUSED, "connection refused")                                      \
  XX(ECONNRESET, "connection reset by peer")                                  \
  XX(EDESTADDRREQ, "destination address required")                            \
  XX(EEXIST, "file already exists")                                           \
  XX(EFAULT, "bad address in system call argument")                           \
  XX(EFBIG, "file too large")                                                 \
  XX(EHOSTUNREACH, "host is unreachable")                                     \
  XX(EINTR, "interrupted system call")                                        \
  XX(EINVAL, "invalid argument")                                              \
  XX(EIO, "i/o error")                                                        \
  XX(EISCONN, "socket is already connected")                                  \
  XX(EISDIR, "illegal operation on a directory")                              \
  XX(ELOOP, "too many symbolic links encountered")                            \
  XX(EMFILE, "too many open files")                                           \
  XX(EMSGSIZE, "message too long")                                            \
  XX(ENAMETOOLONG, "name too long")                                           \
  XX(ENETDOWN, "network is down")                                             \
  XX(ENETUNREACH, "network is unreachable")                                   \
  XX(ENFILE, "file table overflow")                                           \
  XX(ENOBUFS, "no buffer space available")                                    \
  XX(ENODEV, "no such device")                                                \
  XX(ENOENT, "no such file or directory")                                     \
  XX(ENOMEM, "not enough memory")                                             \
  XX(ENONET, "machine is not on the network")                                 \
  XX(ENOPROTOOPT, "protocol not available")                                   \
  XX(ENOSPC, "no space left on device")                                       \
  XX(ENOSYS, "function not implemented")                                      \
  XX(ENOTCONN, "socket is not connected")                                     \
  XX(ENOTDIR, "not a directory")                                              \
  XX(ENOTEMPTY, "directory not empty")                                        \
  XX(ENOTSOCK, "socket operation on non-socket")                              \
  XX(ENOTSUP, "operation not supported on socket")                            \
  XX(EPERM, "operation not permitted")                                        \
  XX(EPIPE, "broken pipe")                                                    \
  XX(EPROTO, "protocol error")                                                \
  XX(EPROTONOSUPPORT, "protocol not supported")                               \
  XX(EPROTOTYPE, "protocol wrong type for socket")                            \
  XX(ERANGE, "result too large")                                              \
  XX(EROFS, "read-only file system")                                          \
  XX(ESHUTDOWN, "cannot send after transport endpoint shutdown")              \
  XX(ESPIPE, "invalid seek")                                                  \
  XX(ESRCH, "no such process")                                                \
  XX(ETIMEDOUT, "connection timed out")                                       \
  XX(ETXTBSY, "text file is busy")                                            \
  XX(EXDEV, "cross-device link not permitted")                                \
  XX(UNKNOWN, "unknown error")                                                \
  XX(EOF, "end of file")                                                      \
  XX(ENXIO, "no such device or address")                                      \
  XX(EMLINK, "too many links")                                                \
  XX(EHOSTDOWN, "host is down")                                               \

int XLIPCTranslateSysError(int sys_errno)
{
	if (sys_errno <= 0)
	{
		return sys_errno;  /* If < 0 then it's already a libuv error. */
	}

	switch (sys_errno) {
	case ERROR_NOACCESS:                    return XLIPC_RESULT_EACCES;
	case WSAEACCES:                         return XLIPC_RESULT_EACCES;
	case ERROR_ELEVATION_REQUIRED:          return XLIPC_RESULT_EACCES;
	case ERROR_ADDRESS_ALREADY_ASSOCIATED:  return XLIPC_RESULT_EADDRINUSE;
	case WSAEADDRINUSE:                     return XLIPC_RESULT_EADDRINUSE;
	case WSAEADDRNOTAVAIL:                  return XLIPC_RESULT_EADDRNOTAVAIL;
	case WSAEAFNOSUPPORT:                   return XLIPC_RESULT_EAFNOSUPPORT;
	case WSAEWOULDBLOCK:                    return XLIPC_RESULT_EAGAIN;
	case WSAEALREADY:                       return XLIPC_RESULT_EALREADY;
	case ERROR_INVALID_FLAGS:               return XLIPC_RESULT_EBADF;
	case ERROR_INVALID_HANDLE:              return XLIPC_RESULT_EBADF;
	case ERROR_LOCK_VIOLATION:              return XLIPC_RESULT_EBUSY;
	case ERROR_PIPE_BUSY:                   return XLIPC_RESULT_EBUSY;
	case ERROR_SHARING_VIOLATION:           return XLIPC_RESULT_EBUSY;
	case ERROR_OPERATION_ABORTED:           return XLIPC_RESULT_ECANCELED;
	case WSAEINTR:                          return XLIPC_RESULT_ECANCELED;
	case ERROR_NO_UNICODE_TRANSLATION:      return XLIPC_RESULT_ECHARSET;
	case ERROR_CONNECTION_ABORTED:          return XLIPC_RESULT_ECONNABORTED;
	case WSAECONNABORTED:                   return XLIPC_RESULT_ECONNABORTED;
	case ERROR_CONNECTION_REFUSED:          return XLIPC_RESULT_ECONNREFUSED;
	case WSAECONNREFUSED:                   return XLIPC_RESULT_ECONNREFUSED;
	case ERROR_NETNAME_DELETED:             return XLIPC_RESULT_ECONNRESET;
	case WSAECONNRESET:                     return XLIPC_RESULT_ECONNRESET;
	case ERROR_ALREADY_EXISTS:              return XLIPC_RESULT_EEXIST;
	case ERROR_FILE_EXISTS:                 return XLIPC_RESULT_EEXIST;
	case ERROR_BUFFER_OVERFLOW:             return XLIPC_RESULT_EFAULT;
	case WSAEFAULT:                         return XLIPC_RESULT_EFAULT;
	case ERROR_HOST_UNREACHABLE:            return XLIPC_RESULT_EHOSTUNREACH;
	case WSAEHOSTUNREACH:                   return XLIPC_RESULT_EHOSTUNREACH;
	case ERROR_INSUFFICIENT_BUFFER:         return XLIPC_RESULT_EINVAL;
	case ERROR_INVALID_DATA:                return XLIPC_RESULT_EINVAL;
	case ERROR_INVALID_PARAMETER:           return XLIPC_RESULT_EINVAL;
	case ERROR_SYMLINK_NOT_SUPPORTED:       return XLIPC_RESULT_EINVAL;
	case WSAEINVAL:                         return XLIPC_RESULT_EINVAL;
	case WSAEPFNOSUPPORT:                   return XLIPC_RESULT_EINVAL;
	case WSAESOCKTNOSUPPORT:                return XLIPC_RESULT_EINVAL;
	case ERROR_BEGINNING_OF_MEDIA:          return XLIPC_RESULT_EIO;
	case ERROR_BUS_RESET:                   return XLIPC_RESULT_EIO;
	case ERROR_CRC:                         return XLIPC_RESULT_EIO;
	case ERROR_DEVICE_DOOR_OPEN:            return XLIPC_RESULT_EIO;
	case ERROR_DEVICE_REQUIRES_CLEANING:    return XLIPC_RESULT_EIO;
	case ERROR_DISK_CORRUPT:                return XLIPC_RESULT_EIO;
	case ERROR_EOM_OVERFLOW:                return XLIPC_RESULT_EIO;
	case ERROR_FILEMARK_DETECTED:           return XLIPC_RESULT_EIO;
	case ERROR_GEN_FAILURE:                 return XLIPC_RESULT_EIO;
	case ERROR_INVALID_BLOCK_LENGTH:        return XLIPC_RESULT_EIO;
	case ERROR_IO_DEVICE:                   return XLIPC_RESULT_EIO;
	case ERROR_NO_DATA_DETECTED:            return XLIPC_RESULT_EIO;
	case ERROR_NO_SIGNAL_SENT:              return XLIPC_RESULT_EIO;
	case ERROR_OPEN_FAILED:                 return XLIPC_RESULT_EIO;
	case ERROR_SETMARK_DETECTED:            return XLIPC_RESULT_EIO;
	case ERROR_SIGNAL_REFUSED:              return XLIPC_RESULT_EIO;
	case WSAEISCONN:                        return XLIPC_RESULT_EISCONN;
	case ERROR_CANT_RESOLVE_FILENAME:       return XLIPC_RESULT_ELOOP;
	case ERROR_TOO_MANY_OPEN_FILES:         return XLIPC_RESULT_EMFILE;
	case WSAEMFILE:                         return XLIPC_RESULT_EMFILE;
	case WSAEMSGSIZE:                       return XLIPC_RESULT_EMSGSIZE;
	case ERROR_FILENAME_EXCED_RANGE:        return XLIPC_RESULT_ENAMETOOLONG;
	case ERROR_NETWORK_UNREACHABLE:         return XLIPC_RESULT_ENETUNREACH;
	case WSAENETUNREACH:                    return XLIPC_RESULT_ENETUNREACH;
	case WSAENOBUFS:                        return XLIPC_RESULT_ENOBUFS;
	case ERROR_BAD_PATHNAME:                return XLIPC_RESULT_ENOENT;
	case ERROR_DIRECTORY:                   return XLIPC_RESULT_ENOENT;
	case ERROR_FILE_NOT_FOUND:              return XLIPC_RESULT_ENOENT;
	case ERROR_INVALID_NAME:                return XLIPC_RESULT_ENOENT;
	case ERROR_INVALID_DRIVE:               return XLIPC_RESULT_ENOENT;
	case ERROR_INVALID_REPARSE_DATA:        return XLIPC_RESULT_ENOENT;
	case ERROR_MOD_NOT_FOUND:               return XLIPC_RESULT_ENOENT;
	case ERROR_PATH_NOT_FOUND:              return XLIPC_RESULT_ENOENT;
	case WSAHOST_NOT_FOUND:                 return XLIPC_RESULT_ENOENT;
	case WSANO_DATA:                        return XLIPC_RESULT_ENOENT;
	case ERROR_NOT_ENOUGH_MEMORY:           return XLIPC_RESULT_ENOMEM;
	case ERROR_OUTOFMEMORY:                 return XLIPC_RESULT_ENOMEM;
	case ERROR_CANNOT_MAKE:                 return XLIPC_RESULT_ENOSPC;
	case ERROR_DISK_FULL:                   return XLIPC_RESULT_ENOSPC;
	case ERROR_EA_TABLE_FULL:               return XLIPC_RESULT_ENOSPC;
	case ERROR_END_OF_MEDIA:                return XLIPC_RESULT_ENOSPC;
	case ERROR_HANDLE_DISK_FULL:            return XLIPC_RESULT_ENOSPC;
	case ERROR_NOT_CONNECTED:               return XLIPC_RESULT_ENOTCONN;
	case WSAENOTCONN:                       return XLIPC_RESULT_ENOTCONN;
	case ERROR_DIR_NOT_EMPTY:               return XLIPC_RESULT_ENOTEMPTY;
	case WSAENOTSOCK:                       return XLIPC_RESULT_ENOTSOCK;
	case ERROR_NOT_SUPPORTED:               return XLIPC_RESULT_ENOTSUP;
	case ERROR_BROKEN_PIPE:                 return XLIPC_RESULT_EOF;
	case ERROR_ACCESS_DENIED:               return XLIPC_RESULT_EPERM;
	case ERROR_PRIVILEGE_NOT_HELD:          return XLIPC_RESULT_EPERM;
	case ERROR_BAD_PIPE:                    return XLIPC_RESULT_EPIPE;
	case ERROR_NO_DATA:                     return XLIPC_RESULT_EPIPE;
	case ERROR_PIPE_NOT_CONNECTED:          return XLIPC_RESULT_EPIPE;
	case WSAESHUTDOWN:                      return XLIPC_RESULT_EPIPE;
	case WSAEPROTONOSUPPORT:                return XLIPC_RESULT_EPROTONOSUPPORT;
	case ERROR_WRITE_PROTECT:               return XLIPC_RESULT_EROFS;
	case ERROR_SEM_TIMEOUT:                 return XLIPC_RESULT_ETIMEDOUT;
	case WSAETIMEDOUT:                      return XLIPC_RESULT_ETIMEDOUT;
	case ERROR_NOT_SAME_DEVICE:             return XLIPC_RESULT_EXDEV;
	case ERROR_INVALID_FUNCTION:            return XLIPC_RESULT_EISDIR;
	case ERROR_META_EXPANSION_TOO_LONG:     return XLIPC_RESULT_E2BIG;
	default:                                return XLIPC_RESULT_UNKNOWN;
	}
}

#endif  //__XLIPC_ERROR_H__