/**
 * @description: A/B测试接入
 */

import EventEmitter from 'events';
import { config } from '@root/common/config/config';
import { ThunderHelper } from '@root/common/thunder-helper';
import { ConfigInitState } from '@root/common/config/types';
import { AccountHelper } from '@root/common/account/impl/accountHelper';
import { AccountHelperEventKey } from '@root/common/account/account-type';
import { ApplicationManager } from '@root/common/thunder-pan-manager/manager/application';

enum VariantType {
  JSON = 0,
  STRING,
  NUMBER,
  BOOL = 4,
  ARRAY,
}

interface IAbTestFeatureOptions {
  time_stamp: number;
  app_id: string;
  /** feature id */
  flag_id: string;
  /** feature KEY */
  flag_key: string;
  /** feature 名称 */
  flag_name: string;
  /** 是否命中分流规则 */
  match: boolean;
  /** 分流结果的ID */
  variant_id: string;
  /** 分流结果的名称 */
  variant_name: string;
  /** 分流结果的类型，0为json, 1为string , 2为number, 4为bool ,5 为array */
  variant_type: VariantType;
  /** 分流结果的配置 */
  variant_value: string | number | boolean | Object | [];
}

class AbTestServer extends EventEmitter {
  protected initState_: ConfigInitState = ConfigInitState.None;
  protected configData_: { [key: string]: any } = {};

  private getVersionCode(vers: string[]): string {
    const majorNum = vers[0];
    const minorNum = vers[1];
    const revisionNum = vers[2];
    const buildNum = vers[3];

    let versionCode = buildNum;
    if (majorNum && minorNum && revisionNum && buildNum) {
      versionCode = majorNum.padStart(2, '0') + minorNum.padStart(2, '0') + revisionNum.padStart(2, '0') + buildNum.padStart(4, '0');
    }
    return versionCode;
  }

  protected async request(): Promise<void> {
    const testServ: boolean = await config.getValue('ConfigRemoteGlobal', 'TestServer', false) as boolean;
    let origin: string = 'https://feature-x-xluser.xunlei.com';
    if (testServ) {
      origin = 'https://xfeature.office.k8s.xunlei.cn';
    }
    const url = origin + '/v1/flags';

    let user_id: string = '';
    const accountHelper = AccountHelper.getInstance();
    let is_login: 0 | 1 = 0;
    if (accountHelper.isSignIn) {
      is_login = 1;
      let userInfo: any = await accountHelper.getUserInfo();
      user_id = userInfo.id!;
    }

    const app_id: string = ApplicationManager.getCurrentDeviceClientId();
    const device_id: string = await accountHelper.getDeviceID();
    const peer_id: string = ThunderHelper.getPeerId();
    const channel: string = ThunderHelper.getInstallChannel();
    const version_name: string = ThunderHelper.getFileVersion(process.execPath);
    const version_code: string = this.getVersionCode(version_name.split('.'));

    const body = {
      app_id, // string, 应用ID
      filters: [] as string[],  // string[], feature的key列表  可空，为空是查询 app_id下的所有feature
      user_id,
      device_id,
      entity_context: {
        peer_id,
        channel,
        version_code,
        version_name,
        is_login
      }
    }

    const input: RequestInit = {
      method: 'POST',
      body: JSON.stringify(body),
      mode: 'cors',
      headers: {
        'content-type': 'application/json',
      }
    };

    let res: Response | null = null;
    try {
      res = await fetch(url, input);
    } catch (error) {
      //
    }

    if (res?.ok && res?.status == 200) {
      const res_data: {
        code: number;
        data: IAbTestFeatureOptions[]
      } = await res.json();
      if (res_data?.code === 200 && res_data.data?.length) {
        for (let item of res_data.data) {
          // 提前做好映射，方便后续查找
          this.configData_[item.flag_key] = item;
        }
      }
    }
  }

  private async update(): Promise<void> {
    this.initState_ = ConfigInitState.Ing;
    await this.request();
    this.initState_ = ConfigInitState.Done;
    this.emit('config-load-finish');
  }

  protected async init(): Promise<void> {
    do {
      if (this.initState_ === ConfigInitState.Done) {
        break;
      }

      if (this.initState_ === ConfigInitState.Ing) {
        return new Promise<void>(
          (resolve: () => void): void => {
            this.once('config-load-finish', () => {
              resolve();
            });
          }
        );
      }

      this.initState_ = ConfigInitState.Ing;
      const accountHelper = AccountHelper.getInstance();
      await accountHelper.whenReady();
      accountHelper.attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, async () => {
        await this.update();
      });
      accountHelper.attachEvent(AccountHelperEventKey.SIGN_OUT, async () => {
        await this.update();
      });
      await this.request();
      this.initState_ = ConfigInitState.Done;
      this.emit('init-finish');
    } while (0);
  }

  public async getAbTestValue(groupName: string, key: string): Promise<any> {
    await this.init();
    let value: any = null;
    do {
      if (!groupName) {
        value = this.configData_;
        break;
      }

      if (!key) {
        value = this.configData_?.[groupName];
        break;
      }

      value = this.configData_?.[groupName]?.[key];
    } while (0);
    return value;
  }
}

const abTestServer: AbTestServer = new AbTestServer();
export default abTestServer;