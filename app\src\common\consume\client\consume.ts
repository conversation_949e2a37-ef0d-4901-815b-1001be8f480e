import {
    Dialog,
    OpenDialogReturnValue,
} from 'electron';
import { AsyncRemoteCall } from '@root/common/renderer-async-remote-call';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'

export namespace ConsumeManagerNs {
    export async function playTask(taskId: number, fileIndex: number): Promise<boolean> {
        try {
            const result = await client.callServerFunction('ConsumeManagerPlayTask', taskId, fileIndex);
            return result as boolean;
        } catch(e) {
            return false;
        }
    }

    export async function consumeFile(filePath: string): Promise<boolean> {
        try {
            const result = await client.callServerFunction('ConsumeManagerConsumeFile', filePath);
            return result as boolean;
        } catch(e) {
            return false;
        }
    }

    export async function openTaskFolder(taskId: number): Promise<boolean> {
        try {
            const result = await client.callServerFunction('ConsumeManagerOpenTaskFolder', taskId);
            return result as boolean;
        } catch(e) {
            return false;
        }
    }

    export async function showItemInFolder(filePath: string) {
        try {
            const result = await client.callServerFunction('ConsumeManagerShowItemInFolder', filePath);
            return result as boolean;
        } catch(e) {
            return false;
        }
    }


    export async function showOpenDialog(): Promise<string> {
        const parent: any = await await AsyncRemoteCall.GetInstance().getCurrentWindow();
        const dialog: any = await await AsyncRemoteCall.GetInstance().getDialog();
        if (parent && !(await parent.isDestroyed())) {
            let res: OpenDialogReturnValue = await (dialog as Dialog).showOpenDialog(parent, { properties: ['openDirectory'] });
            if (!res.canceled) {
                return res.filePaths[0];
            }
        }

        return '';
    }

    export async function consumeTask(taskId: number, fileIndex: number): Promise<boolean> {
        try {
            const result = await client.callServerFunction('ConsumeManagerConsumeTask', taskId, fileIndex);
            return result as boolean;
        } catch(e) {
            return false;
        }
    }


    export async function consumeLink(params: ThunderClientAPI.dataStruct.dataModals.LinkRecord): Promise<boolean> {
        try {
            const result = await client.callServerFunction('ConsumeManagerConsumeLink', params);
            return result as boolean;
        } catch(e) {
            return false;
        }
    }

    export async function consumePlaybackInfo(info: ThunderClientAPI.dataStruct.dataModals.PlaybackRecord) {
        try {
            const result = await client.callServerFunction('ConsumeManagerConsumePlaybackInfo', info);
            return result as boolean;
        } catch(e) {
            return false;
        }
    }
}