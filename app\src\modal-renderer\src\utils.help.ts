// 工具函数集合
// 从 create-task/index.vue 中提取的工具函数
import dayjs from 'dayjs'

// 导入TaskUtilHelper用于文件类型判断
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'

import {
  EmuleTaskStatus,
  type EmuleTaskStatusType,
  HttpFileType,
  type HttpFileTypeEnum,
} from '../types/new-task.type'

// 获取FileExtType类型别名
type FileExtType = TaskUtilHelper.FileExtType

/**
 * 检测文件类型的工具函数
 * @param url 文件URL
 * @returns HttpFileTypeEnum 文件类型枚举
 */
export function detectFileType(url: string): HttpFileTypeEnum {
  const extension = url.split('.').pop()?.toLowerCase() || ''

  const videoExts = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v', '3gp', 'ts', 'm3u8']
  const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma']
  const documentExts = ['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx', 'xls', 'xlsx']
  const archiveExts = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz']
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']

  if (videoExts.includes(extension)) return HttpFileType.VIDEO
  if (audioExts.includes(extension)) return HttpFileType.AUDIO
  if (documentExts.includes(extension)) return HttpFileType.DOCUMENT
  if (archiveExts.includes(extension)) return HttpFileType.ARCHIVE
  if (imageExts.includes(extension)) return HttpFileType.IMAGE

  return HttpFileType.UNKNOWN
}

/**
 * 格式化大小显示
 * @param bytes 字节数
 * @returns 格式化后的大小字符串
 */
export function formatSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))

  return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + units[i]
}

/**
 * 格式化速度显示
 * @param bytesPerSecond 每秒字节数
 * @returns 格式化后的速度字符串
 */
export function formatSpeed(bytesPerSecond: number): string {
  return formatSize(bytesPerSecond) + '/s'
}

/**
 * 将HttpFileType映射到文件分类字符串
 * @param fileType HTTP文件类型
 * @returns 文件分类字符串
 */
export function mapFileTypeToCategory(fileType: HttpFileTypeEnum): string {
  switch (fileType) {
    case HttpFileType.VIDEO:
      return 'video'
    case HttpFileType.IMAGE:
      return 'image'
    case HttpFileType.AUDIO:
      return 'audio'
    case HttpFileType.DOCUMENT:
      return 'document'
    case HttpFileType.ARCHIVE:
      return 'compressed'
    default:
      return 'other'
  }
}

/**
 * 根据文件名获取文件分类
 * @param fileName 文件名
 * @returns 文件分类字符串
 */
export function getFileCategoryFromFileName(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''

  const videoExts = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v', '3gp', 'ts', 'm3u8']
  const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma']
  const documentExts = ['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx', 'xls', 'xlsx']
  const archiveExts = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz']
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']

  if (videoExts.includes(extension)) return 'video'
  if (audioExts.includes(extension)) return 'audio'
  if (documentExts.includes(extension)) return 'document'
  if (archiveExts.includes(extension)) return 'compressed'
  if (imageExts.includes(extension)) return 'image'

  return 'other'
}

/**
 * 将Emule任务状态映射到显示状态
 * @param status Emule任务状态
 * @returns 显示状态字符串
 */
export function mapEmuleStatusToDisplayStatus(status: EmuleTaskStatusType): string {
  switch (status) {
    case EmuleTaskStatus.PARSING:
      return 'loading'
    case EmuleTaskStatus.PARSED:
      return 'success'
    case EmuleTaskStatus.CREATING:
      return 'loading'
    case EmuleTaskStatus.DONE:
      return 'success'
    case EmuleTaskStatus.ERROR:
      return 'error'
    default:
      return 'error'
  }
}

/**
 * 根据HttpFileType映射到MediaType（用于LinkHub）
 * @param fileType HTTP文件类型
 * @returns MediaType字符串
 */
export function getMediaType(
  fileType: HttpFileTypeEnum
): ThunderClientAPI.dataStruct.dataModals.MediaType {
  switch (fileType) {
    case HttpFileType.VIDEO:
      return 'MEDIA_VIDEO'
    case HttpFileType.AUDIO:
      return 'MEDIA_AUDIO'
    case HttpFileType.IMAGE:
      return 'MEDIA_IMAGE'
    case HttpFileType.ARCHIVE:
      return 'MEDIA_ARCHIVE'
    case HttpFileType.DOCUMENT:
      return 'MEDIA_TEXT'
    default:
      return 'MEDIA_UNKNOWN'
  }
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID字符串
 */
export function generateUniqueId(prefix: string = ''): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(7)
  return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`
}

/**
 * 从URL中提取文件名
 * @param url URL字符串
 * @param defaultName 默认文件名
 * @returns 文件名
 */
export function extractFileNameFromUrl(url: string, defaultName: string = 'unknown-file'): string {
  try {
    // 移除查询参数和锚点
    const cleanUrl = url.split('?')[0].split('#')[0]
    // 提取路径的最后一部分作为文件名
    const fileName = cleanUrl.split('/').pop()
    return fileName && fileName.trim() ? fileName : defaultName
  } catch (error) {
    console.warn('提取文件名失败:', error)
    return defaultName
  }
}

/**
 * 验证URL是否为有效的磁力链接
 * @param url URL字符串
 * @returns 是否为磁力链接
 */
export function isMagnetUrl(url: string): boolean {
  return url.trim().startsWith('magnet:?')
}

/**
 * 验证URL是否为有效的HTTP/HTTPS链接
 * @param url URL字符串
 * @returns 是否为HTTP链接
 */
export function isHttpUrl(url: string): boolean {
  const trimmedUrl = url.trim()
  return trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')
}

/**
 * 验证URL是否为有效的FTP链接
 * @param url URL字符串
 * @returns 是否为FTP链接
 */
export function isFtpUrl(url: string): boolean {
  return url.trim().startsWith('ftp://')
}

/**
 * 验证URL是否为有效的电驴链接
 * @param url URL字符串
 * @returns 是否为电驴链接
 */
export function isEmuleUrl(url: string): boolean {
  return url.trim().startsWith('ed2k://')
}

/**
 * 验证文件路径是否为种子文件
 * @param filePath 文件路径
 * @returns 是否为种子文件
 */
export function isTorrentFile(filePath: string): boolean {
  return filePath.trim().toLowerCase().endsWith('.torrent')
}

/**
 * 检测链接类型
 * @param url URL字符串
 * @returns 链接类型
 */
export function detectUrlType(
  url: string
): 'magnet' | 'http' | 'ftp' | 'emule' | 'torrent' | 'unknown' {
  if (isMagnetUrl(url)) return 'magnet'
  if (isHttpUrl(url)) return 'http'
  if (isFtpUrl(url)) return 'ftp'
  if (isEmuleUrl(url)) return 'emule'
  if (isTorrentFile(url)) return 'torrent'
  return 'unknown'
}

/**
 * 解析多行URL输入
 * @param input 输入字符串
 * @returns URL数组
 */
export function parseMultilineUrls(input: string): string[] {
  if (!input || !input.trim()) {
    return []
  }

  return input
    .split('\n')
    .map(url => url.trim())
    .filter(url => url !== '')
}

/**
 * 验证URL数组中是否包含重复的URL
 * @param urls URL数组
 * @param newUrl 新URL
 * @returns 是否存在重复
 */
export function isDuplicateUrl(urls: Array<{ url: string }>, newUrl: string): boolean {
  return urls.some(item => item.url === newUrl)
}

/**
 * 媒体列表结果类型
 */
export interface MediaListsResult {
  video: any[]
  audio: any[]
}

/**
 * 从dataMap中筛选出视频和音频文件（纯函数）
 * @param dataMap 数据映射对象
 * @returns 包含video和audio数组的对象
 */
export function updateMediaLists(dataMap: Record<string, any>): MediaListsResult {
  const videos: any[] = []
  const audios: any[] = []

  Object.values(dataMap).forEach((item: any) => {
    if (!item) return

    // 获取文件名，优先使用fileName，其次使用url
    const fileName = item.fileName || item.url || ''

    if (!fileName) return

    try {
      // 使用TaskUtilHelper判断文件类型
      const fileType = TaskUtilHelper.getTaskFileType(fileName)

      if (fileType === TaskUtilHelper.FileExtType.Video) {
        videos.push(item)
      } else if (fileType === TaskUtilHelper.FileExtType.Music) {
        audios.push(item)
      }
    } catch (error) {
      console.error('判断文件类型时出错:', error, 'fileName:', fileName)
    }
  })

  return {
    video: videos,
    audio: audios,
  }
}

/**
 * 生成默认的磁力任务名称
 * 格式：磁力URL值_年月日时分秒_随机数
 * @param magnetUrl 磁力链接URL
 * @returns 默认的磁力任务名称
 */
export function generateDefaultMagnetTaskName(magnetUrl: string): string {
  try {
    // 提取磁力链接的hash值（去掉magnet:?xt=urn:btih:前缀）
    let urlPart = magnetUrl
    if (magnetUrl.includes('xt=urn:btih:')) {
      // 提取hash值
      const hashMatch = magnetUrl.match(/xt=urn:btih:([a-fA-F0-9]{40}|[a-fA-F0-9]{32})/)
      if (hashMatch && hashMatch[1]) {
        urlPart = hashMatch[1].substring(0, 8) // 取前8位作为标识
      }
    } else {
      // 如果无法提取hash，使用URL的最后8个字符
      urlPart = magnetUrl.substring(Math.max(0, magnetUrl.length - 8))
    }

    // 生成时间戳：年月日时分秒格式 (YYYYMMDDHHmmss)
    const timeStamp = dayjs().format('YYYYMMDDHHmmss')

    // 生成4位随机数
    const randomNum = Math.floor(Math.random() * 9000) + 1000 // 1000-9999

    // 组合生成默认名称
    const defaultName = `磁力_${urlPart}_${timeStamp}_${randomNum}`

    console.log(`[Utils] 生成默认磁力任务名称: ${defaultName}`)
    return defaultName
  } catch (error) {
    console.error('[Utils] 生成默认磁力任务名称失败:', error)
    // 如果出错，使用简单的时间戳和随机数
    const timeStamp = dayjs().format('YYYYMMDDHHmmss')
    const randomNum = Math.floor(Math.random() * 9000) + 1000
    return `磁力任务_${timeStamp}_${randomNum}`
  }
}
