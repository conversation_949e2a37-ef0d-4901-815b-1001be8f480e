#ifndef __XLIPC_PIPE_H__
#define __XLIPC_PIPE_H__

#include <Windows.h>
#include <string>

#include <atlbase.h>
#include <atlwin.h>

#define WM_PIPE_OPEN		(WM_USER+200)
#define WM_PIPE_CONNECT		(WM_USER+201)
#define WM_PIPE_DISCONNECT	(WM_USER+202)
#define WM_PIPE_READ		(WM_USER+203)
#define WM_PIPE_WRITE		(WM_USER+204)

class IXLIPCPipeCallback
{
public:
	virtual void OnPipeConnect(int status) = 0;
	virtual void OnPipeDisconnect(int status) = 0;
	virtual void OnPipeRead(const char* buf, int size) = 0;
	virtual void OnPipeWrite(int status) = 0;
};

class XLIPCPipe
	: public CWindowImpl<XLIPCPipe>
{
public:
	XLIPCPipe(const std::wstring& pipe_name);
	~XLIPCPipe();

	BEGIN_MSG_MAP(XLIPCPipe)
		MESSAGE_HANDLER(WM_PIPE_OPEN, OnPipeOpen)
		MESSAGE_HANDLER(WM_PIPE_CONNECT, OnPipeConnect)
		MESSAGE_HANDLER(WM_PIPE_DISCONNECT, OnPipeDisconnect)
		MESSAGE_HANDLER(WM_PIPE_READ, OnPipeRead)
		MESSAGE_HANDLER(WM_PIPE_WRITE, OnPipeWrite)
	END_MSG_MAP()

private:
	typedef enum tagXLIPCOverlappedType
	{
		UNKNOWN = 0,
		READ,
		WRITE,
		DISCONNECT
	}XLIPCOverlappedType;
	/* Used by I/O operations */
	typedef struct tagXLIPCOverlapped
	{
		OVERLAPPED overlapped;
		XLIPCOverlappedType type;
		tagXLIPCOverlapped()
		{
			memset(&overlapped, 0, sizeof(OVERLAPPED));
		}
	}XLIPCOverlapped;

	HANDLE OpenNamedPipe(long* duplex_flags);
	bool SetPipeHandle(long duplex_flags);
	int PipeQueueRead();
	static DWORD WINAPI PipeConnectThreadProc(void* parameter);
	static DWORD WINAPI PipeIoCompletionProc(void* parameter);

	void ProcessPipeReadReq(XLIPCOverlapped* xlipc_overlapped);
	void PipeReadError(int err);
	void PipeReadEOF();
	void PipeReadErrorOrEOF(XLIPCOverlapped* xlipc_overlapped, int err);
	void PipeReadStop();
	void ProcessPipeWriteReq(XLIPCOverlapped* xlipc_overlapped);

private:
	// MESSAGE_HANDLER
	LRESULT OnPipeOpen(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL bHandled);
	LRESULT OnPipeConnect(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL bHandled);
	LRESULT OnPipeDisconnect(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL bHandled);
	LRESULT OnPipeRead(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL bHandled);
	LRESULT OnPipeWrite(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL bHandled);

	virtual void OnFinalMessage(HWND);

public:
	void SetCallback(IXLIPCPipeCallback* callback);

	bool Connect();
	bool Disconnect();

	void ReadStart();
	int Write(void* data, uint32_t len);

	void SafeDelete();

private:
	std::wstring pipe_name_;
	IXLIPCPipeCallback* callback_;
	HANDLE pipe_handle_;
	HANDLE iocp_handle_;
	long volatile flags_;
	long volatile disconnect_;

	HANDLE event_connect_;
	HANDLE event_iocp_;

	bool delete_;
};

#endif