import type {
  SimpleBrowserWindowEvent,
  EventListener,
  ISimpleBrowserWindow,
} from '@xbase/electron_common_kit'

import { buildBrowserWindowNamespaceChannelString } from './Common'
import { rendererProcessRemote } from '../process_remote/RendererProcessRemote'
import type { BrowserWindowConstructorOptions } from 'electron'
import type { TBrowserWindowExtraConfig } from './type'

export class RendererProcessBrowserWindow implements ISimpleBrowserWindow {
  private _obejctId: string
  private _isDestroy: boolean = false

  constructor(
    options?: BrowserWindowConstructorOptions,
    extraConfig?: TBrowserWindowExtraConfig,
  ) {
    const obejctId = rendererProcessRemote.sendSync(
      buildBrowserWindowNamespaceChannelString(
        'new-RendererProcessBrowserWindow',
      ),
      options,
      extraConfig,
    )
    this._obejctId = obejctId
  }

  on(event: SimpleBrowserWindowEvent, listener: EventListener<any[]>): void {
    rendererProcessRemote.on(
      buildBrowserWindowNamespaceChannelString(`${this._obejctId}-${event}`),
      listener,
    )
  }

  once(event: SimpleBrowserWindowEvent, listener: EventListener<any[]>): void {
    rendererProcessRemote.once(
      buildBrowserWindowNamespaceChannelString(`${this._obejctId}-${event}`),
      listener,
    )
  }

  off(event: SimpleBrowserWindowEvent, listener: EventListener<any[]>): void {
    rendererProcessRemote.removeListener(
      buildBrowserWindowNamespaceChannelString(`${this._obejctId}-${event}`),
      listener,
    )
  }

  async loadURL(url: string): Promise<void> {
    return rendererProcessRemote.invoke(
      buildBrowserWindowNamespaceChannelString(`${this._obejctId}-loadURL`),
      url,
    )
  }

  async getURL(): Promise<string | undefined> {
    return rendererProcessRemote.invoke(
      buildBrowserWindowNamespaceChannelString(`${this._obejctId}-getURL`),
    )
  }

  async destroy(): Promise<void> {
    const res = await rendererProcessRemote.invoke(
      buildBrowserWindowNamespaceChannelString(`${this._obejctId}-destroy`),
    )
    this._isDestroy = true
    return res
  }

  async isDestroyed(): Promise<boolean> {
    return this._isDestroy
  }
}
