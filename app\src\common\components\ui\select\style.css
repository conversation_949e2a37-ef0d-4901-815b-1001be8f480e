.ComboboxRoot {
  position: relative;
}

.ComboboxAnchor {
  display: flex;
  align-items: center;
  padding: 11px;
  height: 44px;
  width: 336px;
  border-radius: var(--border-radius-M, 8px);
  border: 1px solid var(--border-border-2, #E5E6EB);
  gap: 18px;
  flex-shrink: 0;
}

.ComboboxAnchor:focus-within {
  border: 1px solid var(--border-border-primary, #226DF5);
}

.ComboboxRoot[data-disabled] .ComboboxAnchor {
  cursor: not-allowed;
}

.ComboboxAnchor i {
  height: 16px;
  width: 16px;
  cursor: pointer;
}

.ComboboxRoot[data-disabled] .ComboboxAnchor i {
  cursor: not-allowed;
  color: var(--font-font-4, #C2C6CB);
}

.ComboboxInput {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  text-align: justify;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: var(--font-font-1, #272E3B);
}

.ComboboxInput[data-disabled] {
  color: var(--font-font-4, #C2C6CB);
}

.ComboboxInput::placeholder {
  color: var(--font-font-4, #C9CDD4);
}

.ComboboxContent {
  border-radius: var(--border-radius-M, 8px);
  border: 1px solid var(--border-border-2, #E5E6EB);
  background: var(--background-background-elevated, r);
  box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
  width: 336px;
  margin-top: 2px;
  z-index: 1000;
}

.ComboboxViewport {
  padding: 6px;
}

.ComboboxItem {
  display: flex;
  height: 36px;
  padding: 0px 12px;
  align-items: center;
  gap: 4px;
  align-self: stretch;
  color: var(--font-font-1, #272E3B);
  font-size: 13px;
  line-height: 22px;
  border-radius: var(--border-radius-S, 6px);
  border: none;
  cursor: pointer;
  justify-content: space-between;
}

.ComboboxItem .ComboboxClose {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--font-font-3, #86909C);
  visibility: hidden;
}

.ComboboxItem:hover {
  background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
  outline: none;
}

.ComboboxClear {
  display: flex;
  height: 36px;
  padding: 0px 12px;
  align-items: center;
  align-self: stretch;
  color: var(--font-font-1, #272E3B);
  font-size: 13px;
  line-height: 22px;
  cursor: pointer;
}

.ComboboxItem:hover .ComboboxClose {
  visibility: visible;
}

.ComboboxClear:hover {
  color: var(--primary-primary-hover, #488BF7);
}

.ComboboxDivider {
  width: 1px;
  height: 16px;
  background: var(--fill-fill-2, rgba(12, 24, 49, 0.06));
}