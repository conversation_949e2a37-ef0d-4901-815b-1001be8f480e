.DropdownMenuContent,
.DropdownMenuSubContent {
  min-width: 160px;
  padding: 6px;
  animation-duration: 400ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;
  border-radius: var(--border-radius-M, 8px);
  border: 1px solid var(--border-border-2, #E5E6EB);
  background: var(--background-background-elevated, #FFF);
  box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
  z-index: 999;
}

.DropdownMenuContent[data-side='top'],
.DropdownMenuSubContent[data-side='top'] {
  animation-name: slideDownAndFade;
}

.DropdownMenuContent[data-side='right'],
.DropdownMenuSubContent[data-side='right'] {
  animation-name: slideLeftAndFade;
}

.DropdownMenuContent[data-side='bottom'],
.DropdownMenuSubContent[data-side='bottom'] {
  animation-name: slideUpAndFade;
}

.DropdownMenuContent[data-side='left'],
.DropdownMenuSubContent[data-side='left'] {
  animation-name: slideRightAndFade;
}

/* .DropdownMenuSubContent[data-side='right'] {
  margin-left: 10px;
} */

.DropdownMenuItem,
.DropdownMenuCheckboxItem,
.DropdownMenuSubTrigger {
  font-size: 13px;
  line-height: 22px;
  color: var(--font-font-1, #272E3B);
  border-radius: var(--border-radius-S, 6px);
  display: flex;
  align-items: center;
  height: 36px;
  padding: 4px;
  position: relative;
  padding-left: 32px;
  user-select: none;
  outline: none;
  cursor: pointer;
}

.DropdownMenuSubTrigger[data-state='open'] {
  background-color: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
  justify-content: space-between;
}


.DropdownMenuItem[data-disabled],
.DropdownMenuCheckboxItem[data-disabled],
.DropdownMenuSubTrigger[data-disabled] {
  color: var(--font-font-4, #C9CDD4);
  pointer-events: none;
}

.DropdownMenuItem[data-highlighted],
.DropdownMenuCheckboxItem[data-highlighted],
.DropdownMenuSubTrigger[data-highlighted] {
  background-color: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
}


.DropdownMenuSeparator {
  height: 1px;
  background-color: var(--border-border-3, #F2F3F5);
  margin: 2px 0;
}

.DropdownMenuArrow {
  color: var(--font-font-3, #86909C);
  margin-left: auto;
  font-size: 12px !important;
}

.RightSlot {
  margin-left: auto;
  color: var(--font-font-3, #86909C);
  font-size: 12px;
}

[data-disabled] .RightSlot {
  color: var(--font-font-4, #C9CDD4);
}

.LeftSlot {
  position: absolute;
  left: 8px;
  width: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

@keyframes slideUpAndFade {
  from {
    opacity: 0;
    transform: translateY(2px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideRightAndFade {
  from {
    opacity: 0;
    transform: translateX(-2px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeftAndFade {
  from {
    opacity: 0;
    transform: translateX(2px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}