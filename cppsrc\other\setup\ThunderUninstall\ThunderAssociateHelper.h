#pragma once

class CThunderAssociateHelper
{
public:
	CThunderAssociateHelper();
	~CThunderAssociateHelper();
	static CThunderAssociateHelper* GetInstance();

	void RegisterAllProtocol(wstring& wstrInstallPath);
	void RegisterAllFileType(wstring& wstrInstallPath);

private:
	void RegisterFileType(const wstring& strExeFile, wstring& wstrInstallPath);
	void RegisterFileTypeEx(const wstring& strExeFile,wstring& wstrInstallPath);
	void UnRegisterFileType(const wstring& strExeFile);
	void RegisterProtocol(const wstring& strProtocol, wstring& wstrInstallPath);
	void UnRegisterProtocol(const wstring& strProtocol);
};

#define theAssociateHelper  CThunderAssociateHelper::GetInstance()