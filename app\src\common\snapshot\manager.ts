import path, { join } from 'path'
import { AplayerStack } from '@root/common/player/client/aplayer-stack';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { GetProfilesPath } from '@root/common/xxx-node-path'
import { ensureString } from '@root/common/ensure';

export class SnapshotManager {
    static instance: SnapshotManager;
    private outDir: string = '';
    private md5: ((str: string) => string) | undefined;
    constructor() {
        this.outDir = path.join(GetProfilesPath(), 'snapshot');
    }

    public static getInstance(): SnapshotManager {
        if (!SnapshotManager.instance) {
            if (global.SnapshotManagerInstance) {
                SnapshotManager.instance = global.SnapshotManagerInstance;
            } else {
                SnapshotManager.instance = new SnapshotManager();
                global.SnapshotManagerInstance = SnapshotManager.instance;
            }
        }
        return SnapshotManager.instance
    }

    public init(md5: (str: string) => string) {
        this.md5 = md5;
    }

    public async getLocalFileSnapshot(filePath: string): Promise<{ succ: boolean, out?: string }> {

        if (!this.md5) {
            return { succ: false };
        }

        let md5 = this.md5(filePath);
        let out = path.join(this.outDir, md5 + '.png');
        if (await FileSystemAWNS.existsAW(out)) {
            // TODO 缓存过期时间
            return { succ: true, out };
        }
        let b = await AplayerStack.GetInstance().createSnapshot(filePath, out, 762, 426);
        console.log('getLocalFileSnapshot', filePath, md5, b);
        return { succ: b, out };
    }

    public async getPanFileSnapshot(fileId: string, space?: string): Promise<{ succ: boolean, out?: string }> {
        let headers: any = {};
        let params: any = {};
        params.space = space ? space : '';
        if (params && (params.space === 'SPACE_SAFE')) {
            // TODO
            // headers['space-authorization'] = await ThunderPanClientSDK.getInstance().getSafeBoxToken();
        }
        let info = await ThunderPanClientSDK.getInstance().getFileInfo(fileId, {
            params,
            headers
        });
        try {
            if (info.success && info.data) {
                return { succ: true, out: info.data.thumbnail_link };
            }
            return { succ: false };
        } catch (e) {
            return { succ: false };
        }
    }

    public checkIsDefaultCover(url: string) {
        return ensureString(url).includes('backstage-img-ssl.a.88cdn.com')
    }
}