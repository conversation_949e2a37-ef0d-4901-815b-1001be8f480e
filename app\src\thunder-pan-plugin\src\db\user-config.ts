import Knex from "knex";
import { ThunderPanDataBase } from ".";
import { useUserStore } from "@/store/user-store";
import crypto from 'node:crypto'

export interface IUserTable {
  // 用户 id 唯一主键
  user_id: string
  // 保险箱密码
  hash: string
  // 其他扩展字段
  ext?: Record<string, any>
}

const originalUserFileColumnList = [
  'user_id',
  'hash',
  'ext',
]
const sKey = '5TgbYHnMJU8IKm9OLp0qAzWsXeDcRvF3'
const sIv = 'RtFvGyHuJiKoPlMn'

export class UserConfigTableManager {
  private static _instance: UserConfigTableManager;

  static getInstance() {
    if (UserConfigTableManager._instance) return UserConfigTableManager._instance;
    UserConfigTableManager._instance = new UserConfigTableManager();
    return UserConfigTableManager._instance;
  }

  private readonly tableName = 'user_config'
  private readonly ormInstance = Knex<IUserTable>({
    client: 'sqlite3'
  })

  private db () {
    return this.ormInstance(this.tableName)
  }

  async exist () {
    const sql = `SELECT name FROM sqlite_master WHERE name='${this.tableName}'`
    try {
      const res = await ThunderPanDataBase.getInstance().query(sql)
      return res.success && !!res.items.length
    } catch (err) {
      return false
    }
  }

  async create () {
    const sqlString: string = this.ormInstance.schema
      .createTable(this.tableName, table => {
        table.string('user_id', 128).primary()
        table.text('hash', 'string')
        table.json('ext').defaultTo('{}')
      })
      .toString()

    try {
      await ThunderPanDataBase.getInstance().execute(sqlString)
    } catch (err) {
      console.warn('[UserConfigTableManager] create table error', err)
    }
  }

  async getHash (): Promise<string> {
    const { userStoreState } = useUserStore()
    const uid = userStoreState.curUser.userId!
    const sql = this.db().where(...['user_id', '=', uid]).toString()

    try {
      const res = await ThunderPanDataBase.getInstance().query(sql)

      if (res.success) {
        return this._decrypt(res.items[0].hash)
      }
      return ''
    } catch (err) {
      return ''
    }
  }

  setHash (newHash: string) {
    const { userStoreState } = useUserStore()
    const uid = userStoreState.curUser.userId!

    this._insertOrUpdate({
      user_id: uid,
      hash: this._encrypt(newHash),
    })
  }

  private _encrypt (text) {
    const cipher = crypto.createCipheriv('aes-256-cbc', sKey, sIv)
    let encrypted = cipher.update(text, 'utf8', 'base64')
    encrypted += cipher.final('base64')

    return encrypted
  }

  private _decrypt (encrypted) {
    const decipher = crypto.createDecipheriv('aes-256-cbc', sKey, sIv)
    let decrypted = decipher.update(encrypted, 'base64', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  }

  private async _insertOrUpdate (data: IUserTable) {
    const sql = this.db().insert(this._serialize(data)).toString()
    const insertOrUpdaterSql = 'insert or replace' + sql.slice(6)
    try {
      await ThunderPanDataBase.getInstance().execute(insertOrUpdaterSql)
    } catch (err) {
      console.error('[UserConfigTableManager] insert or update error', err)
    }
  }

  private _serialize (data: any) {
    // 如果无法获取则使用最老的表含有的字段
    let columns: string[] = []
    if (!columns || !columns.length) {
      columns = originalUserFileColumnList
    }
    return Object.keys(data).filter(key => columns.includes(key)).reduce((acc, key) => {
      if (typeof data[key] === 'object') {
        acc[key] = JSON.stringify(data[key])
      } else if (typeof data[key] === 'boolean') {
        acc[key] = Number(data[key])
      } else {
        acc[key] = data[key]
      }
      return acc
    }, Object.create(null))
  }
}
