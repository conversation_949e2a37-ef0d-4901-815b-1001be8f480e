<script setup lang="ts">
import TDCheckbox from '@root/common/components/ui/checkbox/index.vue'
import Button from '@root/common/components/ui/button/index.vue'
import { computed } from 'vue';
import { API_FILE } from '@root/common/thunder-pan-manager/pan-sdk/types';
import { formatSize, isFolder, isSensitiveFile } from '@root/common/thunder-pan-manager/pan-sdk/utils/drive'
import { formatDate, SYSTEM_FOLDER_TYPE_LIST } from '@root/common/thunder-pan-manager/pan-sdk/utils/file'
import { FileOperationHelper } from '@/utils/file-operation';

const props = withDefaults(defineProps<{
  file: API_FILE.DriveFile
  index: number
  pickedIds: string[]
  highlightIds?: string[]
  isInSafeBoxFolder?: boolean
  isInFavoriteFolder?: boolean
}>(), {

})

const emit = defineEmits<{
  (e: 'consume', file: API_FILE.DriveFile): void
  (e: 'right-click', event: MouseEvent, file: API_FILE.DriveFile): void
  (e: 'item-click', event: MouseEvent, index: number, file: API_FILE.DriveFile): void
  (e: 'item-dblclick', file: API_FILE.DriveFile): void
  (e: 'checkbox-click', isCheck: boolean, file: API_FILE.DriveFile): void
  (e: 'operation-click', type: string, file: API_FILE.DriveFile): void
}>()

const isPicked = computed(() => props.pickedIds.includes(props.file.id!))
const isHighlight = computed(() => props.highlightIds?.includes(props.file.id!))
const isSystemFolder = computed(() => SYSTEM_FOLDER_TYPE_LIST.includes(props.file.folder_type!))
const isSensitive = computed(() => isSensitiveFile(props.file))
const fileQuickOperation = computed(() => FileOperationHelper.getInstance().getFileOperations([props.file], { isInSafeBoxFolder: props.isInSafeBoxFolder }))
const disableDownload = computed(() => props.isInFavoriteFolder || props.file.space === 'SPACE_FAVORITE')
const disableShare = computed(() =>  props.isInFavoriteFolder || props.file.space === 'SPACE_FAVORITE')

function handleItemClick (event: MouseEvent) {
  emit('item-click', event, props.index, props.file)
}

function handleItemDblclick () {
  if (isSensitive.value) return

  emit('item-dblclick', props.file)
}

function handleCheckChange (isCheck: boolean) {
  emit('checkbox-click', isCheck, props.file)
}

function handleConsume () {
  if (isSensitive.value) return

  emit('consume', props.file)
}

function handleContextMenu (event: MouseEvent) {
  emit('right-click', event, props.file)
}

function handleOperationClick (type: string) {
  emit('operation-click', type, props.file)
}

function handleFileNameClick (event: MouseEvent) {
  if (isSensitive.value) {
    handleItemClick(event)
  } else {
    handleConsume()
  }
}
</script>

<template>
  <div
    class="file-item"
    :class="{
      'is-selected': isPicked,
      'is-disabled': isSensitive,
      'is-highlight': isHighlight,
    }"
    @click.stop="handleItemClick"
    @click.right.stop="handleContextMenu($event)"
    @dblclick.stop="handleItemDblclick"
  >
    <!-- 文件名复合区 -->
    <div class="file-name-container">
      <!-- 复选框 -->
      <div
        class="checkbox"
        :class="{
          'is-hidden': isSystemFolder
        }"
        @click.stop
      >
        <TDCheckbox label="" :model-value="isPicked" @update:model-value="handleCheckChange" />
      </div>

      <!-- 文件图标 -->
      <div class="icon">
        <img :src="file.icon_link" alt="">
      </div>

      <!-- 文件名 -->
      <div class="text">
        <span v-tooltip="file.name" class="file-name disable-drag-select"  @click.stop="handleFileNameClick">{{ file.name }}</span>
      </div>

      <!-- 文件状态 -->
      <div class="file-status">
        <div v-if="isSensitive" class="icon-tips">
          <i class="xl-icon-tips-warning" />
          <span>{{ file.audit?.status === 'STATUS_UNKNOW' ? '疑似不良资源，正在审核中' : file.audit?.title }}</span>
        </div>
      </div>

      <!-- 快捷操作区 -->
      <div class="operation-area">
        <Button
          v-show="fileQuickOperation.download"
          v-tooltip="'下载'"
          class="disable-drag-select"
          variant="ghost"
          size="sm"
          :is-icon="true"
          :disabled="disableDownload"
          @click.stop="handleOperationClick('download')"
        >
          <i class="xl-icon-download"></i>
        </Button>
        <Button
          v-show="fileQuickOperation.share"
          v-tooltip="'分享'"
          class="disable-drag-select"
          variant="ghost"
          size="sm"
          :is-icon="true"
          :disabled="disableShare"
          @click.stop="handleOperationClick('share')"
        >
          <i class="xl-icon-general-share-m"></i>
        </Button>
        <Button
          v-show="fileQuickOperation.rename"
          v-tooltip="'重命名'"
          class="disable-drag-select"
          variant="ghost"
          size="sm"
          :is-icon="true"
          @click.stop="handleOperationClick('rename')"
        >
          <i class="xl-icon-general-edit-l"></i>
        </Button>
        <Button
          v-show="fileQuickOperation.delete || fileQuickOperation.trash"
          v-tooltip="'删除'"
          class="disable-drag-select"
          variant="ghost"
          size="sm"
          :is-icon="true"
          @click.stop="handleOperationClick('delete')"
        >
          <i class="xl-icon-delete"></i>
        </Button>
      </div>
    </div>

    <!-- 文件大小 -->
    <div class="file-size">
      {{ !isFolder(file) ? formatSize(file.size!, 0) : '-' }}
    </div>

    <!-- 修改时间 -->
    <div class="file-time">
      {{ formatDate(file.modified_time!, 'YYYY-MM-DD HH:mm') }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.file-item {
  display: flex;
  align-items: center;
  gap: 24px;
  height: 56px;
  margin: 0 28px;
  padding: 0 12px;
  font-size: 12px;
  line-height: 22px;
  color: var(--font-font-1, #272E3B);
  border-radius: var(--border-radius-M2, 10px);
  overflow: hidden;

  .file-name-container {
    display: flex;
    align-items: center;
    flex-grow: 1;

    .checkbox {
      flex-shrink: 0;

      &.is-hidden {
        visibility: hidden;
      }
    }

    .icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      margin: 0 12px;

      img {
        width: 100%;
      }
    }

    .text {
      flex-grow: 1;
      display: flex;
      align-items: center;
      padding: 4px;

      .file-name {
        -webkit-line-clamp: 1;
        display: -webkit-box;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        white-space: pre-wrap;
        transition: color .2s;

        &:hover {
          color: var(--primary-primary-default);
          cursor: pointer;
        }
      }
    }

    .file-status {
      flex-shrink: 0;
      margin-left: 24px;

      .icon-tips {
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .xl-icon-tips-warning {
        color: var(--functional-warning-default);
      }
    }

    .operation-area {
      display: none;
      flex-shrink: 0;
      margin-left: 24px;
    }
  }

  .file-size {
    flex-shrink: 0;
    width: 58px;
  }

  .file-time {
    flex-shrink: 0;
    width: 110px;
  }

  &.is-selected,
  &:hover {
    background-color: var(--fill-fill-3, #0C18310A);

    .file-status {
      display: none;
    }
  }

  &.is-disabled {
    .icon {
      opacity: 0.4;
    }

    .file-name,
    .file-name:hover,
    .file-size,
    .file-time {
      color: var(--font-font-4) !important;
      cursor: default !important;
    }
  }

  &.is-highlight {
    background-color: var(--fill-fill-3, #0C18310A);
  }

  &:hover:not(.is-selected) {
    .file-name-container {
      .operation-area {
        display: block;
      }
    }
  }
}
</style>
