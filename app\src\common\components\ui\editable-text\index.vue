<template>
  <div
    class="editable-text-container"
    :style="{ width: containerWidth }"
  >
    <!-- 显示模式 -->
    <span
      class="editable-text-display"
      v-if="!isEditing"
      ref="displaySpan"
      :class="{ editable: !disabled }"
      @click="handleStartEdit"
      :title="disabled ? '' : '点击编辑'"
    >
      <slot :value="displayValue">{{ displayValue }}</slot>
    </span>

    <!-- 编辑模式 -->
    <textarea
      class="editable-text-input textarea-reset hide-scrollbar"
      v-else
      v-model="editValue"
      ref="editInput"
      :placeholder="placeholder"
      :rows="rows"
      :style="{ height: inputHeight }"
      @blur="handleConfirm"
      @keyup.enter="handleEnterKey"
      @keyup.esc="handleCancel"
      @click.stop
      :resize="resize"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch } from 'vue'

interface Props {
  /** 绑定的值 */
  modelValue: string
  /** 是否禁用编辑 */
  disabled?: boolean
  /** 输入框占位符 */
  placeholder?: string
  /** 容器宽度，默认auto */
  width?: string | number
  /** textarea行数 */
  rows?: number
  /** 是否允许调整大小 */
  resize?: 'none' | 'both' | 'horizontal' | 'vertical'
  /** 是否在按Enter时确认（默认false，多行模式下Enter应该换行） */
  confirmOnEnter?: boolean
  /** 验证函数，返回false或错误信息则不允许确认 */
  validator?: (value: string) => boolean | string
  /** 是否在失去焦点时确认更改 */
  confirmOnBlur?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  placeholder: '',
  width: 'auto',
  rows: 2,
  resize: 'vertical',
  confirmOnEnter: false,
  confirmOnBlur: true,
})

interface Emits {
  /** 值更新事件 */
  'update:modelValue': [value: string]
  /** 开始编辑事件 */
  'edit-start': [value: string]
  /** 确认编辑事件 */
  'edit-confirm': [oldValue: string, newValue: string]
  /** 取消编辑事件 */
  'edit-cancel': [value: string]
}

const emit = defineEmits<Emits>()

// 响应式数据
const isEditing = ref(false)
const editValue = ref('')
const editInput = ref<HTMLTextAreaElement>()
const displaySpan = ref<HTMLSpanElement>()
const inputHeight = ref('auto')

// 计算属性
const displayValue = computed(() => props.modelValue || '')

const containerWidth = computed(() => {
  if (typeof props.width === 'number') {
    return `${props.width}px`
  }
  return props.width
})

// 监听外部值变化
watch(
  () => props.modelValue,
  newValue => {
    if (!isEditing.value) {
      editValue.value = newValue || ''
    }
  }
)

// 方法
const handleStartEdit = () => {
  if (props.disabled) return

  // 获取显示元素的高度
  if (displaySpan.value) {
    const spanHeight = displaySpan.value.offsetHeight
    // 对于多行文本，确保至少有足够的高度
    const minHeight = Math.min(spanHeight, props.rows * 30) // 假设每行大约20px
    inputHeight.value = `${minHeight}px`
  }

  isEditing.value = true
  editValue.value = props.modelValue || ''
  emit('edit-start', props.modelValue)

  nextTick(() => {
    if (editInput.value) {
      editInput.value.focus()
      editInput.value.select()
    }
  })
}

const handleEnterKey = (event: KeyboardEvent) => {
  // 如果设置了confirmOnEnter，则Enter确认；否则Enter换行
  if (props.confirmOnEnter) {
    event.preventDefault()
    handleConfirm()
  }
  // 如果是Ctrl+Enter或Cmd+Enter，也确认
  else if (event.ctrlKey || event.metaKey) {
    event.preventDefault()
    handleConfirm()
  }
}

const handleConfirm = () => {
  if (!props.confirmOnBlur && event?.type === 'blur') {
    return
  }

  const newValue = editValue.value.trim()
  const oldValue = props.modelValue

  // 验证新值
  if (props.validator) {
    const validateResult = props.validator(newValue)
    if (validateResult === false) {
      return // 验证失败，不确认更改
    }
    if (typeof validateResult === 'string') {
      console.warn('验证失败:', validateResult)
      return
    }
  }

  isEditing.value = false

  if (newValue !== oldValue) {
    emit('update:modelValue', newValue)
    emit('edit-confirm', oldValue, newValue)
  }
}

const handleCancel = () => {
  isEditing.value = false
  editValue.value = props.modelValue || ''
  emit('edit-cancel', props.modelValue)
}

// 暴露方法给父组件
defineExpose({
  startEdit: handleStartEdit,
  confirm: handleConfirm,
  cancel: handleCancel,
  isEditing: () => isEditing.value,
})
</script>

<style lang="scss" scoped>
.editable-text-container {
  display: inline-block;
  position: relative;
}

.editable-text-display {
  display: inline-block;
  width: 100%;
  white-space: pre-wrap; // 保持换行符显示
  word-break: break-all;
  &.editable {
    align-items: center;
    padding: 4px 8px;
    border: 1px solid transparent;

    border-radius: 8px;
    background-color: transparent;
    text-align: left;
    cursor: pointer;

    &:hover {
      border: 1px solid var(--border-border-2, #e5e6eb);
    }
  }
}

.editable-text-input {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid var(--primary-primary-default, #226df5);
  border-radius: 8px;
  background-color: transparent;
  line-height: 22px;

  &::placeholder {
    color: var(--font-font-4, #c9cdd4);
  }
}

/* 隐藏滚动条但保持滚动功能 */
.hide-scrollbar {
  /* Firefox */
  scrollbar-width: none;

  /* Safari and Chrome */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}
</style>
