#pragma once
#include <vector>
#include "liveupdate.h"
#include "UpdateInfoChecker.h"
#include "UpdateDownloader.h"
#include "./SockIPC/SockIPC_Interface.h"

class LiveUpdater
{
public:
    LiveUpdater(void);
    ~LiveUpdater(void);

    static LiveUpdater* __stdcall LiveUpdater::GetInstance(void* p = NULL)
    {
        static LiveUpdater *updater = new LiveUpdater();
        return updater;
    }

    bool Init(const wchar_t* xmlParam, const wchar_t* wsPid, const wchar_t* wsChannelId);
    bool InitXLStat(const char* peerid, const wchar_t* LUver);
    void UninitXLStat();
    bool KeepInstance();
    void StartUpdate(UPDATE_TYPE type);
    void ExecuteStartupPatch();
    void Clean();
    void UnInit();
    
    //bool LoadXAR();
    bool CheckUpdateInfo(UPDATE_TYPE type, PACK_TYPE& PackType, bool& needRestart);
	bool CheckLocalUpdateInfo(PACK_TYPE& PackType, bool& needRestart);
	void DeleteLocalUpdateInfoFile();
    bool DownloadPackage();
    DWORD InstallPack(bool bNeedRestart);
    bool GetDownloadProgress(unsigned long& downloadedSize, unsigned long& fileSize);
    void ExecuteProduct();

    bool GetXarParam(std::wstring& param);

    bool CheckProductProcessExist();
    void TerminateProductProcess();
    void GetCurrentExePath(std::wstring& path);
    void GetProductPath(std::wstring& path);

    void ForceSetUAC(bool bNeedUAC);
	const string& GetChannelID();
	const string& GetCurProductVersion();
	bool IsUpdateFileExist();

private:
    bool DownloadPatch(bool isUpdatePackPatch = false);
    bool DownloadUpdate();
    bool DownloadFile(const char* id, const char* url, const char* httpurl, const char* md5, std::wstring& path);
    void ReadStartupXml();
    void WriteStartupXml();
    void InsertRecordToStartupXml(const char* id, const char* path, const char* param);
    void InsertRecordToInstallList(const char* id, const char* path, const char* param);
    void ExecutePacksByInstaller(const char* productKey, const char* productKeyName, const wchar_t* productName, const wchar_t* productVer, const wchar_t* productPathMD5, bool bNeedUAC, bool needRestartProduct);

    DWORD ExecutePacksByLiveUpdater(const wchar_t* productName, const wchar_t* productVer, bool needRestartProduct);

    void ExecutePacksByInstaller(const char* productKey, const char* productKeyName, const wchar_t* productName, const wchar_t* productVer, const wchar_t* productPathMD5, std::vector<std::string>& ids, std::vector<std::string>& files, std::vector<std::string>& params, const wchar_t* ProductPath, const wchar_t* ProductParam, bool bNeedAdmin = true, bool bNeedRestart = true);
    DWORD ExecutePacksByLiveUpdater(const wchar_t* productName, const wchar_t* productVer, std::vector<std::string>& ids, std::vector<std::string>& files, std::vector<std::string>& params, const wchar_t* ProductPath, const wchar_t* ProductParam, bool bNeedAdmin = true, bool bNeedRestart = true);

    bool CopyNeedFile(const wchar_t* DestDir, const wchar_t* file_name);
    bool CopyNeedFiles(const wchar_t* DestDir);

    static unsigned int __stdcall InstallThreadFunc(void* pParam);

    //static DWORD OnRecv(XAF_IPC_CONNECTION_HANDLE sockIPC, void* data);
    //static DWORD OnSend(XAF_IPC_CONNECTION_HANDLE sockIPC, void* data);
    //static unsigned int __stdcall IPCThread(void* param);
    //unsigned int m_nIPCThreadId;
    //XAF_IPC_CONNECTION_HANDLE m_IPCSocket;
    bool m_bReadyRecvMsgBody;
    unsigned int m_nBodySize;
    std::wstring m_wsRestartParam;
    HANDLE m_hInstanceMutex;
    //HANDLE m_hIPCConnectEvent;

    bool m_bCanSendMessage;
    std::wstring m_ProductName;
    std::wstring m_ProductVer;
    std::string m_szProductName;
    std::string m_szProductVer;
    std::string m_szProductKey;
    std::string m_szProductKeyName;
    DWORD m_ProcessID;
    std::wstring m_ProcessPath;
    std::wstring m_ProcessPathMD5;
    std::wstring m_dlPath;
    UPDATE_TYPE m_UpdateType;
    PACK_TYPE m_PackType;
    int m_nServerVersion;
    bool m_bCheckUpdatePakcage;

    UpdateInfoChecker checker;
    UpdateDownloader downloader;

    xml_element* m_pStartupXmlRoot;

    std::vector<std::string> m_InstallPaths;
    std::vector<std::string> m_InstallParams;
    std::vector<std::string> m_InstallIds;

    std::wstring m_XarPath;
    std::string m_XarName;
    std::wstring m_XarParam;

    std::string m_LocalParam;

    bool m_bDownloading;
    bool m_bNeedUAC;

    bool m_isInService;
    //HANDLE m_packReadyReplyEvent;
    bool m_canInstallPack;

    unsigned int m_XLStatCookie;

	std::string m_strChannelID;
	int m_productID;

    HANDLE m_hInstallThread;
    unsigned int m_mainThreadId;
    DWORD m_dwTempRet;
    std::wstring m_wsTempExePath;
    std::wstring m_wsTempParam;
    std::wstring m_wsTempProductDir;
    bool m_bTempNeedAdmin;
};
