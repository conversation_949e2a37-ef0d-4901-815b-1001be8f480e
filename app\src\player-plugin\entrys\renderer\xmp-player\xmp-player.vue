<template>
  <div v-show="playerControl_rct.show" ref="$xmpPlayerContainer" class="player-container" :style="{
    '--Player_Ani_Fade_Second': `opacity ${Player_UI_Const.Player_Ani_Fade_Second}s ease`,
    '--Player_Ani_Fade_Menu_Second': `opacity ${Player_UI_Const.Player_Ani_Fade_Menu_Second}s ease`,
    '--Player_Ani_Fade_Popover_Second': `opacity ${Player_UI_Const.Popover_Hide_Delay_Second}s ease`,
  }">
    <div class="xmp-player "
      :class="{ 'video-player': !isAPlayer, 'a-player': isAPlayer, 'is-audio': isMediaAudioWithoutCover_computed, 'is-media-loading': playerControl_rct.mediaChangeLoading, 'is-mac': platform.isMacOS, 'is-fullscreen': windowControlGlobal.isFullScreen, 'is-dwm': DwmHelper.getInstance().getIsDwm() }">
      <!-- * video mock -->
      <video v-if="playerType === 'video'" class="video-dom" ref="$videoRef"></video>

      <!-- 本地用来看数据的面板 -->
      <!-- <div 
          style="display:none;position: absolute; background: white; color: black; top:0; left: 0; right: 0; min-height: 20px;">
          {{ playerControl_rct }}
          <div>isPlayerHeaderHovered_ref: {{ isPlayerHeaderHovered_ref }}</div>
          <div>isPlayerMenuHovered_ref: {{ isPlayerMenuHovered_ref }}</div>
          {{ playerControl_rct.playList }}
          {{ playerControl_rct.playMediaInfoMap }}
        </div> -->

      <!-- * 手动 开始，暂停 中间icon动效 -->
      <div ref="$playStatusAniContainer" class="player-status-container play-status-ani-container">
        <Transition name="scaleIn-player-play-icon" @beforeEnter="async (el) => {
          (el as HTMLElement).style.display = 'block'
          // await nextTick()
          // DwmHelper.getInstance().addShow([{ name: 'play_status_ani_container', obj: { value: $playStatusAniContainer } }])
        }" @afterEnter="(el) => {
          (el as HTMLElement).style.display = 'none'
          // DwmHelper.getInstance().delShow(['play_status_ani_container'])
        }" @beforeLeave="(el) => {
          (el as HTMLElement).style.display = 'none'
          // DwmHelper.getInstance().delShow(['play_status_ani_container'])
        }">
          <img v-if="playerControl_rct.playStatusAni === MediaState.MsPlay"
            src="@root/common/assets/img/player/ic_player_play_circle.svg" />
        </Transition>
        <Transition name="scaleIn-player-play-icon" @beforeEnter="async (el) => {
          (el as HTMLElement).style.display = 'block'
          // await nextTick()
          // DwmHelper.getInstance().addShow([{ name: 'play_status_ani_container', obj: { value: $playStatusAniContainer } }])
        }" @afterEnter="(el) => {
          (el as HTMLElement).style.display = 'none'
          // DwmHelper.getInstance().delShow(['play_status_ani_container'])
        }" @beforeLeave="(el) => {
          (el as HTMLElement).style.display = 'none'
          // DwmHelper.getInstance().delShow(['play_status_ani_container'])
        }">
          <img v-if="playerControl_rct.playStatusAni === MediaState.MsPause"
            src="@root/common/assets/img/player/ic_player_pause_circle.svg" />
        </Transition>
      </div>
      <!-- * 中间播放状态 -->
      <div class="player-status-container">
        <div ref="$errorContainer" class="player-error-container" v-if="isPlayErrorUiShow_computed">
          <img class="ic-status-error" src="@root/common/assets/img/player/im_default_playfailed.svg" />
          <div class="player-status-title">播放失败</div>
          <div class="player-status-subtitle">{{ playerControl_rct.playStatusFailReason }}</div>
        </div>
        <!-- ? v-else-if 当展示 error，就不展示 loading -->
        <div class="player-loading-container"
          v-else-if="playerControl_rct.mediaChangeLoading || playerControl_rct.buffer.loading">
          <LottieAnimation class="ic-player-loading"
            :animation-data="require('@root/common/assets/lottie/ic-loading.json')" :auto-play="true" :loop="true" />
          <div class="player-loading-speed" v-if="playerControl_rct.buffer.loading && playerControl_rct.buffer.speed">
            加载中({{ formatFileSize(playerControl_rct.buffer.speed) }}/s)
          </div>
        </div>

        <!-- 无封面 音频icon 图片+旋转 -->
        <div ref="$audioContainer" class="player-audio-container" v-if="isAudioNoCoverUiShow_computed">
          <img class="img-player-audio" :class="{
            'is-play': playerControl_rct.playStatus === MediaState.MsPlay,
            'is-pause': playerControl_rct.playStatus === MediaState.MsPause
          }" src="@root/common/assets/img/player/img-player-audio.png" />
        </div>
      </div>

      <!-- * 顶部栏 -->
      <Transition :name="platform.isWindows ? 'fade-player-menu' : ''">
        <div ref="$playerHeader" class="player-header-container draggable" v-show="playerControl_rct.showMenu">
          <!-- 左侧 回退按钮 -->
          <tooltip class="controls-btn-container player-header-logo-container">
            
          </tooltip>
          <!-- 中间 标题 -->
          <div class="player-header-text" :title="aplayerInfo_rct.mediaInfo?.name">{{ aplayerInfo_rct.mediaInfo?.name
          }}
          </div>
          <!-- 右侧 窗口按钮 -->
          <div v-if="!windowControlGlobal.isFullScreen" class="player-header-option none-draggable">
            <!-- 置顶 -->
            <tooltip class="controls-btn-container" v-if="windowControlGlobal.isPin"
              v-bind="genTooltipOptionsForControlBtn('取消置顶', 'bottom')">
              <img class="option-btn ic-pinoff" src="@root/common/assets/img/player/ic_ceilingoff_24.svg" role="button"
                @click.stop="() => {
                  statPlayerAction({
                    action: 'cancel_topping',
                    control: 'mouse',
                  })
                  windowControlGlobalAction.togglePin()
                }" />
            </tooltip>
            <tooltip class="controls-btn-container" v-else="windowControlGlobal.isPin"
              v-bind="genTooltipOptionsForControlBtn('置顶', 'bottom')">
              <img class="option-btn ic-pin" src="@root/common/assets/img/player/ic_ceiling_24.svg" role="button"
                @click.stop="() => {
                  statPlayerAction({
                    action: 'topping',
                    control: 'mouse',
                  })
                  windowControlGlobalAction.togglePin()
                }" />
            </tooltip>
            <template v-if="platform.isWindows">
              <!-- 最小化 -->
              <tooltip class="controls-btn-container" v-bind="genTooltipOptionsForControlBtn('最小化', 'bottom')">
                <img class="option-btn ic-minimize" src="@root/common/assets/img/player/ic_minimize_24.svg"
                  role="button" @click.stop="() => {
                    playerAction.minimizeWindow()
                    statPlayerAction({
                      action: 'min',
                      control: 'mouse',
                    })
                  }" />
              </tooltip>
              <!-- 最大化 -->
              <tooltip v-if="!windowControlGlobal.isWindowMax" class="controls-btn-container"
                v-bind="genTooltipOptionsForControlBtn('最大化', 'bottom')">
                <img class="option-btn ic-full" src="@root/common/assets/img/player/ic_full_24.svg" role="button"
                  @click.stop="() => {
                    playerAction.toggleMaxWindow()
                    statPlayerAction({
                      action: 'max',
                      control: 'mouse',
                    })
                  }" />
              </tooltip>
              <tooltip v-else class="controls-btn-container" v-bind="genTooltipOptionsForControlBtn('还原', 'bottom')">
                <img class="option-btn ic-revert-full" src="@root/common/assets/img/player/ic_revert_24.svg"
                  role="button" @click.stop="() => {
                    playerAction.toggleMaxWindow()
                    statPlayerAction({
                      action: 'max',
                      control: 'mouse',
                    })
                  }" />
              </tooltip>
              <!-- 关闭 -->
              <tooltip class="controls-btn-container" v-bind="genTooltipOptionsForControlBtn('关闭', 'bottom')">
                <img class=" option-btn ic-close" src="@root/common/assets/img/player/ic_close_24.svg" role="button"
                  @click.stop="handlePlayerClose" />
              </tooltip>
            </template>
          </div>
          <div v-if="windowControlGlobal.isFullScreen" class="player-header-fullscreen-option none-draggable">
            <div>{{ useDateFormat(timestamp, 'HH:mm') }}</div>
            <div class="player-header-fullscreen-container" @click.stop="() => {
              playerControlAction.toggleFullScreen()
              statPlayerAction({
                action: 'full_screen',
                control: 'mouse',
              })
            }">
              <inline-svg class="ic-header-fullscreen-off"
                :src="require('@root/common/assets/img/player/ic_player_minimize_30.svg')" />
              <div>退出全屏</div>
            </div>
          </div>
        </div>
      </Transition>

      <!-- ? loading 关闭菜单栏，不带动画 -->
      <!-- * 底部栏 菜单栏 -->
      <!-- <Transition :name="!playerControl_rct.mediaChangeLoading ? 'fade-player-menu' : ''"> -->
      <!-- ? mac 需要额外控制红绿灯显示隐藏且无动画, 所以mac播放菜单显示隐藏无动画 -->
      <Transition :name="platform.isWindows ? 'fade-player-menu' : ''">
        <div ref="$playerMenu" class="player-menu-container none-draggable" v-show="playerControl_rct.showMenu">
          <!-- v-show="playerControl_rct.showMenu && !playerControl_rct.mediaChangeLoading" -->
          <!-- * 进度条 -->
          <div ref="$processBar" class="player-progress-bar-container">
            <!-- 总进度条 -->
            <div class="player-progress-bar">
              <!-- 预加载进度条 -->
              <div class="player-progress-preload" :style="{
                width: progressBarCssVar_computed['--progress-preload-percent']
              }"></div>
              <!-- 当前进度条 -->
              <div class="player-progress-inner" :style="{
                width: progressBarCssVar_computed['--progress-percent']
              }"></div>
              <!-- 鼠标悬停进度条白块 -->
              <div v-show="!playerControl_rct.isProcessBarDraging" class="player-progress-hover-anchor" :style="{
                left: progressBarCssVar_computed['--progress-hover-percent']
              }"></div>
            </div>
            <!-- 当前进度条圆点 -->
            <div class="player-progress-anchor" :style="{
              left: progressBarCssVar_computed['--progress-percent']
            }"></div>
            <!-- 鼠标悬停进度条tooltip -->
            <div class="player-progress-hover-tooltip" :style="{
              left: progressBarCssVar_computed['--progress-hover-percent'],
              bottom: DwmHelper.getInstance().getIsDwm() ? undefined : '22px',
              top: DwmHelper.getInstance().getIsDwm() ? '30px' : undefined,
            }">
              {{ formatTime(playerControl_rct.duration * (playerControl_rct.hoverPercent / 100)) }}
            </div>
          </div>
          <!-- * 底部控制栏 -->
          <div class="player-controls">
            <!-- * 左侧 播控栏 -->
            <div class="player-controls-play">
              <!-- 停止 icon -->
              <!-- <tooltip class="controls-btn-container"
                v-bind="genTooltipOptionsForControlBtn(`停止 (${platform.isMacOS ? 'Command' : 'Ctrl'}+S)`)">
                <img class="controls-btn ic-stop" src="@root/common/assets/img/player/ic_player_stop_30.svg"
                  role="button" 
                  @click.stop="async () => {
                    statData_rct.endway = 'stop'
                    await statPlayerAction({
                      action: 'stop',
                      control: 'mouse',
                    })
                    playerAction.hidePlayer()
                  }"
                   />
              </tooltip> -->
              <!-- 切换上一集 icon -->
              <tooltip class="controls-btn-container" v-bind="genTooltipOptionsForControlBtn('上一集 (Page Up)')">
                <img class="controls-btn ic-prev-process" src="@root/common/assets/img/player/ic_player_prve_30.svg"
                  role="button"
                  @click.stop="() => {
                   playerControlAction.switchPrevPlayListItem()
                   statPlayerAction({
                     action: 'previous_episode',
                     control: 'mouse',
                   })
                 }" 
                  />
              </tooltip>

              <!-- 暂停 icon -->
              <tooltip class="controls-btn-container" v-if="playerControl_rct.playStatus === MediaState.MsPlay"
                v-bind="genTooltipOptionsForControlBtn('暂停 (Space)')">
                <inline-svg class="controls-btn ic-pause"
                  :src="require('@root/common/assets/img/player/ic_player_pause_30.svg')" role="button" @click.stop="async () => {
                    const isSuccess = await playerControlAction.pause(true)
                    if (isSuccess) {
                      statPlayerAction({
                        action: 'pause',
                        control: 'mouse',
                      })
                    }
                  }" />
              </tooltip>
              <!-- 播放 icon -->
              <tooltip class="controls-btn-container" v-else v-bind="genTooltipOptionsForControlBtn('播放 (Space)')">
                <img class="controls-btn ic-play" src="@root/common/assets/img/player/ic_player_play_30.svg"
                  role="button" @click.stop="async () => {
                    const isSuccess = await playerControlAction.play(true)
                    if (isSuccess) {
                      statPlayerAction({
                        action: 'play',
                        control: 'mouse',
                      })
                    }
                  }" />
              </tooltip>

              <!-- 切换下一集 icon -->
              <tooltip class="controls-btn-container" v-bind="genTooltipOptionsForControlBtn('下一集 (Page Down)')">
                <inline-svg class="controls-btn ic-next-process"
                  :src="require('@root/common/assets/img/player/ic_player_next_30.svg')" role="button" @click.stop="() => {
                    playerControlAction.switchNextPlayListItem()
                    statPlayerAction({
                      action: 'next_episode',
                      control: 'mouse',
                    })
                  }" />
              </tooltip>

              <!-- 音量 icon -->
              <tooltip class="controls-btn-container" ref="$voicePopover" v-bind="genPopoverOptionsForControlBtn({
                offset: [0, DwmHelper.getInstance().getIsDwm() ? 54 : 50],
                onShow() {

                  sleep(20).then(() => {
                    DwmHelper.getInstance().addShow([{ name: 'voice_popover', obj: { value: voice_popover } }]);
                  })
                },
                onHide() {
                  sleep(Player_UI_Const.Popover_Hide_Delay_Second).then(() => {
                    DwmHelper.getInstance().delShow(['voice_popover'])
                  })
                }
              })">
                <img class="controls-btn ic-audio"
                  :src="(getVolumeConfig(playerControl_rct.volume[0]) || Player_Volume_Config_List[0]).icon"
                  role="button" @click.stop="async () => {
                    const action = await playerControlAction.toggleVolumeMute()
                    if (action) {
                      statPlayerAction({
                        action,
                        control: 'mouse',
                      })
                    }
                  }" />
                <template #content="{ hide }">
                  <div class="voice-popover none-draggable" ref="voice_popover">
                    <div class="voice-popover-text">{{ playerControl_rct.volume[0] }}%</div>
                    <SliderRoot v-model="playerControl_rct.volume" class="voice-slider" :max="100" :step="1"
                      orientation="vertical" @valueCommit="async () => {
                        const res = await playerControlAction.setVolume(playerControl_rct.volume[0])
                        if (res) {
                          // 音量有变化
                          statPlayerAction({
                            action: res[0] > res[1] ? 'volume_down' : 'volume_up',
                            control: 'mouse',
                          })
                        }
                      }">
                      <SliderTrack class="voice-slider-track">
                        <SliderRange class="voice-slider-range" />
                      </SliderTrack>
                      <!-- 去掉 tabindex, 使不可tab切换选中 -->
                      <SliderThumb class="voice-slider-thumb" aria-label="Volume" :tabindex="undefined">
                        <div class="voice-slider-thumb-inner"></div>
                      </SliderThumb>
                    </SliderRoot>
                  </div>
                </template>
              </tooltip>
            </div>
            <!-- * 中间 -->
            <!-- 播放进度时间 -->
            <div class="player-controls-text">
              {{ formatTime(Math.floor(playerControl_rct.currentTime)) }} /
              {{ playerControl_rct.duration ? formatTime(Math.floor(playerControl_rct.duration)) : '' }}
            </div>
            <!-- * 右侧 视频功能栏 -->
            <div class="player-controls-tool">
              <!-- * 清晰度 icon -->
              <tooltip ref="$qualityPopover" class="controls-btn-container" v-if="playerControl_rct.ratioList.length > 1" v-bind="genPopoverOptionsForControlBtn({
                offset: [0, DwmHelper.getInstance().getIsDwm() ? 54 : 33], onShow() {


                  sleep(50).then(() => {
                    closeSubtitlesPopover()
                  })

                  sleep(20).then(() => {
                    DwmHelper.getInstance().addShow([{ name: 'quality_popover', obj: { value: quality_popover } }]);
                  })
                }, onHide() {
                  sleep(Player_UI_Const.Popover_Hide_Delay_Second).then(() => {
                    DwmHelper.getInstance().delShow(['quality_popover']);
                  })
                },

              })">
                <!-- 原画 icon -->
                <img v-if="playerControl_rct.currentRatio?.origin === 1" class="controls-btn ic-quality"
                  src="@root/common/assets/img/player/ic_player_yuanhua_30.svg" role="button" />
                <img v-else class="controls-btn ic-quality"
                  :src="dictGet(Player_Ratio_Config_List, 'resolutionName', playerControl_rct.currentRatio?.resolutionName!, 'icon') || require('@root/common/assets/img/player/ic_player_hd_30.svg')"
                  role="button" />
                <template #content="{ hide }">
                  <div class="quality-popover none-draggable" ref="quality_popover">
                    <div class="quality-item"
                      :class="{ 'popover-item-active': playerControl_rct.currentRatio?.mediaId === ratioItem.mediaId }"
                      v-for="ratioItem in playerControl_rct.ratioList" :key="ratioItem.mediaId" @click.stop="async () => {
                        hide()
                        await playerControlAction.changeRatio(ratioItem)
                      }">
                      {{ ratioItem.mediaName }}
                      <!-- <img class="popover-item-tag"
                          :src="dictGet(Player_Ratio_Config_List, 'resolutionName', ratioItem.resolutionName, 'tag')" /> -->
                      <!-- tag icon 服务器下发 -->
                      <img v-if="ratioItem.iconLink" class="popover-item-tag popover-item-tag_img"
                        :src="ratioItem.iconLink" />
                    </div>
                  </div>
                </template>
              </tooltip>
              <!-- * 倍速 播放 icon -->
              <tooltip ref="$speedPopover" class="controls-btn-container" v-if="playerControl_rct.speedList.length > 1" v-bind="genPopoverOptionsForControlBtn({
                offset: [0, DwmHelper.getInstance().getIsDwm() ? 54 : 33],
                onShow() {

                  sleep(50).then(() => {
                    closeSubtitlesPopover()
                  })
                  sleep(20).then(() => {
                    DwmHelper.getInstance().addShow([{ name: 'speed_popover', obj: { value: speed_popover } }]);
                  })
                },
                onHide() {
                  sleep(Player_UI_Const.Popover_Hide_Delay_Second).then(() => {
                    DwmHelper.getInstance().delShow(['speed_popover'])
                  })
                }
              })">
                <img class="controls-btn ic-speed" v-if="playerControl_rct.speedList.length"
                  :src="dictGet(Player_Speed_Config_List, 'id', playerControl_rct.currentSpeed?.id!, 'icon', 'play_speed_1')"
                  role="button" />
                <template #content="{ hide }">
                  <div class="speed-popover none-draggable" ref="speed_popover">
                    <div class="speed-item"
                      :class="{ 'popover-item-active': playerControl_rct.currentSpeed?.id === speedItem.id }"
                      v-for="speedItem in playerControl_rct.speedList" :key="speedItem.id" @click.stop="async () => {
                        hide()
                        await playerControlAction.changeSpeed(speedItem)
                      }">
                      {{ speedItem.name }}

                      <img class="popover-item-tag"
                        :src="dictGet(Player_Speed_Config_List, 'id', speedItem.id, 'tag')" />
                    </div>
                  </div>
                </template>
              </tooltip>

              <!-- * 字幕 icon -->
              <div ref="$subtitlesContainer" class="subtitles-container">
                <div class="controls-btn-container" @click.stop="() => {
                  playerControlAction.toggleSubtitles()
                  statPlayerAction({
                    action: 'subtitle',
                    control: 'mouse',
                  })
                }">
                  <img v-if="playerControl_rct.currentSubtitles" class="controls-btn btn-subtitles-on"
                    src="@root/common/assets/img/player/ic_player_subs_30.svg" role="button" />
                  <div v-show="!playerControl_rct.currentSubtitles" ref="$btnSubtitlesOff"
                    class="btn-subtitles-off-lottie controls-btn">
                  </div>
                </div>
                <Transition name="fade-popover" @afterEnter="() => $subtitlesScrollbarRef?.ps?.update()">
                  <div class="popover-container none-draggable" v-show="playerControl_rct.subtitlesPopoverState.visible"
                    ref="subtitle_popover">
                    <div class="popover-content">
                      <!-- 字幕列表 -->
                      <div class="subtitles-list-container"
                        v-show="playerControl_rct.subtitlesPopoverState.module === 'list'">
                        <div class="subtitles-header">
                          <div class="subtitles-title">字幕列表</div>

                          <!-- 字幕设置 icon -->
                          <inline-svg class="ic-subtitles-config" :class="{
                            'ic-subtitles-config-disabled': playerControl_rct.subtitlesList.length === 0
                          }" :src="require('@root/common/assets/img/player/ic_setting_24.svg')" role="button"
                            @click.stop="async () => {
                              if (playerControl_rct.subtitlesList.length > 0) {
                                playerControl_rct.subtitlesPopoverState.isHoverFalseAutoClose = false
                                playerControl_rct.subtitlesPopoverState.module = 'config'
                              }
                            }" />
                        </div>
                        <PerfectScrollbar class="subtitles-item-container" :options="{ swipeEasing: false }"
                          ref="$subtitlesScrollbarRef">
                          <div class="subtitles-item"
                            :class="{ 'popover-item-active': !playerControl_rct.currentSubtitles }" @click.stop="async () => {
                              await playerControlAction.changeSubtitles(null)

                            }">
                            {{ playerControl_rct.subtitlesList.length > 0 ? '隐藏字幕' : '无字幕' }}
                          </div>
                          <SubtitlesItem ref="$subtitlesItemList"
                            v-for="(subtitlesItem, idx) in playerControl_rct.subtitlesList" :key="subtitlesItem.id"
                            :idx="idx" :item="subtitlesItem" :activeItem="playerControl_rct.currentSubtitles"
                            @click.stop="async () => {
                              await playerControlAction.changeSubtitles(subtitlesItem)

                            }" />
                        </PerfectScrollbar>
                      </div>

                      <!-- 字幕配置 -->
                      <div class="subtitles-config-container"
                        v-show="playerControl_rct.subtitlesPopoverState.module === 'config'">
                        <div class="subtitles-header">
                          <div class="subtitles-title popover-title-clickable" @click.stop="async () => {

                            playerControl_rct.subtitlesPopoverState.isHoverFalseAutoClose = false
                            playerControl_rct.subtitlesPopoverState.module = 'list'
                          }">
                            <inline-svg class="ic-subtitles-back"
                              :src="require('@root/common/assets/img/player/ic_return_small_24.svg')" role="button" />
                            字幕设置
                          </div>
                        </div>
                        <div class="subtitles-form">
                          <div class="form-item subtitles-form-timing">
                            <div class="form-label">
                              字幕延迟
                              <div class="form-label-option">
                                <div class="form-label-btn" @click.stop="async () => {

                                  playerControlAction.setSubtitlesTiming(genSubtitlesDefaultConfig().timing)
                                }
                                ">
                                  重置
                                </div>
                              </div>
                            </div>
                            <div class="form-content">
                              <!-- 字幕延迟 减 -->
                              <div class="subtitle-timing-dec" @click.stop="async () => {

                                playerControlAction.setSubtitlesTiming(accAdd(playerControl_rct.subtitlesConfig.timing, -0.1))
                              }">
                                <img class="ic-subtitle-timing-dec"
                                  src="@root/common/assets/img/player/ic_decrease_24.svg" role="button" />
                              </div>

                              <!-- 字幕延迟 input -->
                              <div class="subtitle-timing-input-container">
                                <div v-if="!playerControl_rct.subtitlesPopoverState.isTimingInputVisible"
                                  class="subtitle-timing-input-btn" @click="async () => {
                                    playerControl_rct.subtitlesPopoverState.isTimingInputVisible = true

                                  }">
                                  {{ playerControl_rct.subtitlesConfig.timing }} 秒
                                </div>
                                <input v-if="playerControl_rct.subtitlesPopoverState.isTimingInputVisible" type="number"
                                  v-focus min="-10" max="10" class="subtitle-timing-input"
                                  v-model="playerControl_rct.subtitlesConfig.timing" @input="(e) => {
                                    const val = ensureNum((e.target as HTMLInputElement).value)
                                    if (val > 10) {
                                      // 最大值 10
                                      playerControl_rct.subtitlesConfig.timing = 10
                                    } else if (val < -10) {
                                      // 最小值 -10
                                      playerControl_rct.subtitlesConfig.timing = -10
                                    } else {
                                      // 保留一位小数
                                      const fixedVal = Math.floor(val * 10) / 10
                                      if (fixedVal !== val) {
                                        playerControl_rct.subtitlesConfig.timing = fixedVal
                                      }
                                    }
                                  }" @blur="(e) => {
                                    playerControlAction.setSubtitlesTiming(playerControl_rct.subtitlesConfig.timing)
                                    playerControl_rct.subtitlesPopoverState.isTimingInputVisible = false
                                  }" @keyup.enter="async (e) => {
                                    playerControlAction.setSubtitlesTiming(playerControl_rct.subtitlesConfig.timing)
                                    await sleep(20) // 防止enter快捷键进入全屏
                                    playerControl_rct.subtitlesPopoverState.isTimingInputVisible = false
                                  }" />
                              </div>

                              <!-- 字幕延迟 加 -->
                              <div class="subtitle-timing-plus" @click.stop="async () => {

                                playerControlAction.setSubtitlesTiming(accAdd(playerControl_rct.subtitlesConfig.timing, 0.1))
                              }">
                                <img class="ic-subtitle-timing-plus" src="@root/common/assets/img/player/ic_add_24.svg"
                                  role="button" />
                              </div>
                            </div>
                          </div>

                          <div class="form-item subtitles-form-font-size">
                            <div class="form-label">
                              字幕大小
                              <div class="form-label-option">
                                <div class="form-label-btn" @click.stop="async () => {

                                  playerControlAction.setSubtitlesFontSize(genSubtitlesDefaultConfig().fontSize)
                                }">
                                  重置
                                </div>
                              </div>
                            </div>
                            <div class="form-content">
                              <!-- 字幕大小 列表 -->
                              <div class="subtitle-font-size-radio"
                                :class="{ 'radio-active': subtitlesFontSizeConfig.value === playerControl_rct.subtitlesConfig.fontSize }"
                                v-for="subtitlesFontSizeConfig in Player_Subtitles_FontSize_Config_List"
                                :key="subtitlesFontSizeConfig.value" @click.stop="async () => {

                                  playerControlAction.setSubtitlesFontSize(subtitlesFontSizeConfig.value)
                                }">
                                {{ subtitlesFontSizeConfig.label }}
                              </div>
                            </div>
                          </div>

                          <div class="form-item subtitles-form-position">
                            <div class="form-label">
                              字幕位置
                              <div class="form-label-option"></div>
                            </div>
                            <div class="form-content mt-0">
                              <!-- 字幕位置 列表 -->
                              <div class="subtitle-position-radio"
                                :class="{ 'radio-active': subtitlePositionModeConfig.value === playerControl_rct.subtitlesConfig.positionMode }"
                                v-for="subtitlePositionModeConfig in Player_Subtitles_PositionMode_Config_List"
                                :key="subtitlePositionModeConfig.value" @click.stop="async () => {

                                  playerControlAction.setSubtitlesPositionMode(subtitlePositionModeConfig.value)
                                }">
                                {{ subtitlePositionModeConfig.label }}
                              </div>
                            </div>
                            <!-- 字幕位置 拖动设置 -->
                            <div class="subtitle-position-custom"
                              v-if="playerControl_rct.subtitlesConfig.positionMode === Player_Subtitles_PositionMode.custom">
                              <div>顶部</div>

                              <SliderRoot v-model="playerControl_rct.subtitlesConfig.position"
                                class="subtitle-position-slider" :max="100" :step="1" @valueCommit="async () => {

                                  playerControlAction.setSubtitlesPosition(playerControl_rct.subtitlesConfig.position[0])
                                }">
                                <SliderTrack class="subtitle-position-slider-track">
                                  <SliderRange class="subtitle-position-slider-range" />
                                </SliderTrack>
                                <!-- 去掉 tabindex, 使不可tab切换选中 -->
                                <SliderThumb class="subtitle-position-slider-thumb" aria-label="Volume"
                                  :tabindex="undefined">
                                  <div class="subtitle-position-slider-thumb-inner"></div>
                                </SliderThumb>
                              </SliderRoot>

                              <div>底部</div>
                            </div>
                          </div>

                          <div class="form-content mt-0">
                            <div class="subtitle-reset" @click.stop="async () => {
                              playerControlAction.resetSubtitlesConfig()

                            }">
                              <inline-svg class="ic-subtitle-reset"
                                :src="require('@root/common/assets/img/player/ic_restore_16.svg')" />
                              恢复默认设置
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="popover-interactive"></div>
                  </div>
                </Transition>
              </div>

              <!-- * 播放列表 icon -->
              <div ref="$playlistContainer" class="playlist-container controls-btn-container">
                <inline-svg class="controls-btn ic-play-list"
                  :src="require('@root/common/assets/img/player/ic_player_epsel_30.svg')" role="button" />

                <Transition name="fade-popover" @afterEnter="() => $playlistScrollbarRef?.ps?.update()">
                  <div class="popover-container none-draggable" v-show="playerControl_rct.playlistPopoverState.visible"
                    ref="playlist_popover">
                    <div class="popover-content">
                      <div class="playlist-title">
                        选集
                      </div>
                      <PerfectScrollbar class="playlist-item-container" :options="{ swipeEasing: false }"
                        ref="$playlistScrollbarRef">
                        <PlaylistItem ref="$playlistItemList" v-for="(playItem, idx) in playerControl_rct.playList"
                          :key="playerControl_rct.mockMediaId + '_' + playItem.id" :item="playItem" :idx="idx"
                          :activeId="playerControl_rct.currentPlayId"
                          :playMediaInfoMap="playerControl_rct.playMediaInfoMap" @click.stop="async () => {

                            await playerControlAction.switchPlayListItem(playItem)
                          }" :getMediaInfo="playerControlAction.getCurrentPlayListItemMediaInfo" />
                      </PerfectScrollbar>
                    </div>
                    <div class="popover-interactive"></div>
                  </div>
                </Transition>
              </div>

              <!-- * v1.1 画面调节 icon -->
              <tooltip ref="$playviewPopover" v-if="!isMediaAudioWithoutCover_computed" class="controls-btn-container" v-bind="genPopoverOptionsForControlBtn({
                offset: [0, DwmHelper.getInstance().getIsDwm() ? 54 : 33],
                onShow() {

                  sleep(50).then(() => {
                    closeSubtitlesPopover()
                  })
                  sleep(20).then(() => {
                    DwmHelper.getInstance().addShow([{ name: 'playview_popover', obj: { value: playview_popover } }]);
                  })
                },
                onHide() {

                  sleep(Player_UI_Const.Popover_Hide_Delay_Second).then(() => {
                    DwmHelper.getInstance().delShow(['playview_popover'])
                  })
                }
              }, 10)">
                <img class="controls-btn ic-playview" src="@root/common/assets/img/player/ic_player_adjust_30.svg"
                  role="button" />
                <template #content="{ hide }">
                  <div class="playview-popover none-draggable" ref="playview_popover">
                    <div class="playview-header">
                      <div class="playview-title">画面调节</div>
                    </div>
                    <div class="playview-form">
                      <div class="form-item playview-form-aspect-ratio">
                        <div class="form-label">
                          画面比例
                        </div>
                        <div class="form-content">
                          <div class="playview-aspect-ratio-radio"
                            :class="{ 'radio-active': imageRatioItem.id === playerControl_rct.playviewConfig.imageRatio }"
                            :style="{ width: dictGet(Player_PlayView_Aspect_Config_List, 'value', imageRatioItem.id, 'width') + 'px' }"
                            v-for="imageRatioItem in playerControl_rct.imageRatioItemList" :key="imageRatioItem.id"
                            @click.stop="async () => {
                              playerControlAction.setImageRatio(imageRatioItem.id)
                            }">
                            {{ imageRatioItem.name }}
                          </div>
                        </div>
                      </div>
                      <div class="form-item playview-form-rotate">
                        <div class="form-label">
                          画面旋转
                        </div>
                        <div class="form-content">
                          <div class="playview-rotate-btn"
                            v-for="playviewRotateConfig in Player_PlayView_Rotate_Config_List"
                            :key="playviewRotateConfig.value" @click.stop="async () => {
                              playerControlAction.setImageRotate(playviewRotateConfig.value)
                            }">
                            <inline-svg :src="playviewRotateConfig.icon" />
                            {{ playviewRotateConfig.label }}
                          </div>
                        </div>
                      </div>
                      <div class="form-content mt-0">
                        <div class="playview-reset" @click.stop="async () => {
                          playerControlAction.resetPlayViewConfig()
                        }">
                          <inline-svg class="ic-playview-reset"
                            :src="require('@root/common/assets/img/player/ic_restore_16.svg')" />
                          恢复默认设置
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </tooltip>

              <!-- 全屏 icon -->
              <tooltip v-if="!windowControlGlobal.isFullScreen" class="controls-btn-container"
                v-bind="genTooltipOptionsForControlBtn('全屏 (Enter)')">
                <img class="controls-btn ic-fullscreen" src="@root/common/assets/img/player/ic_player_subs_30-1.svg"
                  role="button" @click.stop="() => {
                    playerControlAction.toggleFullScreen()
                    statPlayerAction({
                      action: 'full_screen',
                      control: 'mouse',
                    })
                  }" />
              </tooltip>
              <tooltip v-else class="controls-btn-container" v-bind="genTooltipOptionsForControlBtn('退出全屏 (Esc)')">
                <img class="controls-btn ic-fullscreen-off"
                  src="@root/common/assets/img/player/ic_player_minimize_30.svg" role="button" @click.stop="() => {
                    playerControlAction.toggleFullScreen()
                    statPlayerAction({
                      action: 'full_screen',
                      control: 'mouse',
                    })
                  }" />
              </tooltip>
            </div>
          </div>
        </div>
      </Transition>

      <!-- * 底部左侧提示栏, 必须放在 底部栏 下面, 否则会被底部栏挡住 -->
      <div class="player-notice-container none-draggable" :class="{
        'menu-hide': !playerControl_rct.showMenu,
      }">
        <!-- 上次播放 -->
        <div class="player-notice player-notice-lastplay" v-if="playerControl_rct.showLastPlayNotice"
          ref="lastPlayNotice_container">
          <div>已为您定位到上次播放的位置，点击&nbsp;</div>
          <div class="player-notice-lastplay-replay" @click.stop="() => {
            playerControlAction.replay()
            playerControl_rct.showLastPlayNotice = false
          }">从头播放</div>
          <div class="player-notice-lastplay-nomore" @click.stop="() => {
            playerControl_rct.showLastPlayNotice = false
            config.setValue('playerControl', 'showLastPlayNotice', false)
          }">
            不再提示
          </div>
          <inline-svg class="player-notice-lastplay-cancel"
            :src="require('@root/common/assets/img/player/ic_close_16.svg')"
            @click.stop="playerControl_rct.showLastPlayNotice = false" />
        </div>
        <!-- commonNotice -->
        <Transition name="fade">
          <div class="player-notice player-notice-common" v-show="playerControl_rct.commonNoticeVisible"
            ref="commonNotice_container">
            <div v-show="playerControl_rct.commonNoticeIcon === 'forward'" ref="$commonNoticeForwardIcon"
              class="ic-notice-forward"></div>
            <div v-show="playerControl_rct.commonNoticeIcon === 'backward'" ref="$commonNoticeBackwardIcon"
              class="ic-notice-backward"></div>
            {{ playerControl_rct.commonNotice }}
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  useTemplateRef,
  watch,
  nextTick,
  readonly,
} from 'vue'

import { useWindowControlGlobal } from '../store/windowGlobal'
import { WaitSomeThing } from '@root/common/wait-some-thing'
import {
  useThrottleFn,
  useEventListener,
  useTimestamp,
  useDateFormat,
  onClickOutside,
  cloneFnJSON,
} from '@vueuse/core'
import { TippyComponent, type TippyOptions } from 'vue-tippy'
import { SliderRange, SliderRoot, SliderThumb, SliderTrack } from 'reka-ui'

import {
  aPlayerLog,
  vidoeLog,
  xmpPlayerLog,
} from '../utils/clientLogger'
import {
  ListPlaySequence,
  type MediaAttribute,
  MediaState,
  MediaType,
  type PlayListItem,
  type PlayListItemMediaInfo,
  type PlaySpeedDisplayInfo,
  type RatioItem,
  SubtitleCategory,
  type SubtitleItemDisplayInfo,
  ImageRatioItem,
  type TOpenMediaFrom,
} from '@root/common/player/base'
import type { AplayerMedia } from '@root/common/player/impl/aplayer-media'
import { dictGet, sleep } from '../utils/tools'
import { genFunctionHotkeyBool } from '../utils/hotkeys'
import {
  Player_Speed_Config_List,
  getVolumeConfig,
  Player_Volume_Config_List,
  Player_Ratio_Config_List,
  Player_Subtitles_FontSize_Config_List,
  Player_Subtitles_PositionMode_Config_List,
  Player_Subtitles_PositionMode,
  Player_UI_Const,
  genSubtitlesDefaultConfig,
  TSubtitlesConfig,
  Player_PlayView_Aspect_Config_List,
  Player_PlayView_Rotate_Config_List,
  genPlayViewDefaultConfig,
} from '@root/common/player/consts'
import { ensureArray, ensureNum, ensureString } from '@root/common/ensure'
import { isUndefined } from '@root/common/checkTypes'
import { platform } from '@root/common/env'
import Lottie, { type AnimationItem } from 'lottie-web'

import { LottieAnimation } from 'lottie-web-vue'

import { config } from '@root/common/config/config'
import { AccountHelper } from '@root/common/account/impl/accountHelper'
import SubtitlesItem from './components/subtitles-item.vue'
import {
  mockPlayList,
  mockPlayMediaInfoMap,
  mockRatioList,
  mockSpeedList,
  mockSubtitlesList,
} from './mock'
import PlaylistItem from './components/playlist-item.vue'
import { getFileExtension } from '@root/common/thunder-pan-manager/pan-sdk/utils/drive'
import { getComposedPath } from '../utils/dom'
import { genOnClickOutsideIgnore } from '../utils/vueuseUtils'
import pTimeout from 'p-timeout'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { FileExtNS } from '@root/common/file-ext'
import { MainToRenderer_Player_Channel, MainToRenderer_Stat_Channel } from '@root/common/constant'
import { usePlayerControlGlobal } from '../store/playerGlobal'
import { formatFileSize } from '../utils/calc'
// import { xmpContextMenu } from '@root/components/xmpDropdownMenu'
// import { MenuConfig } from '@root/components/xmpDropdownMenu/type'
// import { xmpSettingDialog } from '@player-plugin/components/xmp-setting-dialog';
import XMPMessage from '@root/common/components/ui/message'
import path from 'node:path'
// import { getSettingStoreData } from '@root/common/components/xmp-setting-dialog/settingStore'
import { playerControlStore } from '../store/playerStore'
import { debuggerLogger, win7Logger } from '@root/common/logger'
import { AplayerStack } from '@root/common/player/impl/aplayer-stack'
import { DwmHelper } from '@root/common/player/dwm-helper'
import { VideoUtils } from '../utils/videoUtils'
import { accAdd } from '../utils/math'
import { useMouseDragMoveAndClickViaEventAndMain } from '../composables/useMouseDragMoveAndClickViaEventAndMain'

const props = withDefaults(
  defineProps<{
    playerType: 'video' | 'aplayer'
  }>(),
  {
    playerType: 'video',
  },
)
const isAPlayer = props.playerType === 'aplayer'

// * templateRef
const $xmpPlayerContainer = useTemplateRef<HTMLDivElement>(
  '$xmpPlayerContainer',
)
const $playerHeader = useTemplateRef<HTMLDivElement>('$playerHeader')
const $playerMenu = useTemplateRef<HTMLDivElement>('$playerMenu')
const $processBar = useTemplateRef<HTMLDivElement>('$processBar')
const $subtitlesContainer = useTemplateRef<HTMLDivElement>(
  '$subtitlesContainer',
)
const $subtitlesItemList = ref<InstanceType<typeof SubtitlesItem>[]>()
const $subtitlesScrollbarRef = ref<InstanceType<typeof PerfectScrollbar>>()
const $playlistContainer = useTemplateRef<HTMLDivElement>('$playlistContainer')
const $playlistItemList = ref<InstanceType<typeof PlaylistItem>[]>()
const $playlistScrollbarRef = ref<InstanceType<typeof PerfectScrollbar>>()

const $audioContainer = useTemplateRef<HTMLDivElement>('$audioContainer')
const $errorContainer = useTemplateRef<HTMLDivElement>('$errorContainer')

const $voicePopover = useTemplateRef<TippyComponent>('$voicePopover')
const $qualityPopover = useTemplateRef<TippyComponent>('$qualityPopover')
const $speedPopover = useTemplateRef<TippyComponent>('$speedPopover')
const $playviewPopover = useTemplateRef<TippyComponent>('$playviewPopover')


const { windowControlGlobal, windowControlGlobalAction } = useWindowControlGlobal()
const { playerControlGlobalAction, playerControlGlobal } = usePlayerControlGlobal()

// * ref
// const contextMenuIns_ref = ref<ReturnType<typeof xmpContextMenu>>()
// const settingDialogIns_ref = ref<ReturnType<typeof xmpSettingDialog>>()
// const linkDialogIns_ref = ref<ReturnType<typeof xmpLinkDialog>>()

// * win7 ref
const quality_popover = useTemplateRef<HTMLDivElement>('quality_popover')
const speed_popover = useTemplateRef<HTMLDivElement>('speed_popover')
const subtitle_popover = useTemplateRef<HTMLDivElement>('subtitle_popover')
const playlist_popover = useTemplateRef<HTMLDivElement>('playlist_popover')
const playview_popover = useTemplateRef<HTMLDivElement>('playview_popover')

const voice_popover = useTemplateRef<HTMLDivElement>('voice_popover')

const commonNotice_container = useTemplateRef<HTMLDivElement>('commonNotice_container')
const lastPlayNotice_container = useTemplateRef<HTMLDivElement>('lastPlayNotice_container')

const $playStatusAniContainer = useTemplateRef<HTMLDivElement>('$playStatusAniContainer')


// * 监听滚轮事件,调整音量
useEventListener($xmpPlayerContainer, 'wheel', async (e: WheelEvent) => {
  if (!playerControl_rct.show) return

  // debuggerLogger.log('wheel', e.deltaX, e.deltaY)

  // 阻止默认滚动行为
  e.preventDefault()

  if (
    (isPlayerHeaderHovered_ref.value || isPlayerMenuHovered_ref.value) && // hover 在menu和header时，不执行滚动
    !$voicePopover.value?.state.isVisible // hover 在音量时，执行滚动
  ) {
    return
  }

  // y轴偏移量大于x, 才触发音量修改, 防止触控屏左右滑触发
  if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
    if (e.deltaY !== 0) {
      // 向上滚动增加音量,向下滚动减少音量
      let isPlus: boolean
      if (platform.isMacOS) {
        isPlus = e.deltaY > 0
      } else {
        isPlus = e.deltaY < 0
      }

      if (isPlus) {
        // 向上滚动,音量+3
        const res = await playerControlAction.plusVolume(3)
        if (res) {
          statPlayerAction({
            action: 'volume_up',
            control: 'mouse',
          })
        }
      } else {
        // 向下滚动,音量-3
        const res = await playerControlAction.plusVolume(-3)
        if (res) {
          statPlayerAction({
            action: 'volume_down',
            control: 'mouse',
          })
        }
      }
    }

  }

})

function genTooltipOptionsForControlBtn(content: string, placement: 'top' | 'bottom' = 'top'): TippyOptions {
  let offset: [number, number] = [0, 20]
  let delay: [number, number] | undefined
  if (placement === 'bottom') {
    offset = [0, 3]
    delay = [800, 0]
  }
  const duration = Player_UI_Const.Popover_Hide_Delay_Second

  return {
    content,
    placement,
    arrow: false,
    offset,
    theme: 'tooltip',
    duration,
    maxWidth: 'none',
    delay,
    onShow(ins) {
      sleep(20).then(() => {
        DwmHelper.getInstance().addShow([{ name: 'popover_' + ins.id, obj: { value: ins.popper } }]);
      })
    },
    onHide(ins) {
      sleep(duration).then(() => {
        DwmHelper.getInstance().delShow(['popover_' + ins.id]);
      })
    }
  }
}
function genPopoverOptionsForControlBtn(options: TippyOptions, rightSave?: number): TippyOptions {
  return {
    placement: 'top',
    arrow: false,
    interactive: true,
    duration: Player_UI_Const.Popover_Hide_Delay_Second,
    maxWidth: 'none',
    hideOnClick: false,
    theme: 'playerwnd',
    popperOptions: {
      modifiers: [
        {
          name: 'preventOverflow',
          options: {
            padding: {
              right: isUndefined(rightSave) ? 20 : rightSave, // 右侧安全区 20px
            },
          },
        },
      ],
    },
    ...options,
  }
}

const formatTime = VideoUtils.formatTime

type TMediaInfo = {
  name: string
  type: MediaType
  width: number
  height: number
  attribute: MediaAttribute
}

const aplayerInfo_rct = reactive({
  mediaInfo: null as TMediaInfo | null,
})

let aplayerMedia = null as AplayerMedia | null
let mediaEventCookies = {
  progressChangeCookie: 0 as number,
  stateChangeCookie: 0 as number,
  subtitlePreparedCookie: 0 as number,
  subtitleSelectChangeCookie: 0 as number,
  ratioPrepareEvent: 0 as number,
  setToLatestCookie: 0 as number,
  playBufferCookie: 0 as number,
  audioTrackPrepareCookie: 0 as number,
  audioTrackSelectCookie: 0 as number,
}
let menuManagerObject = {
  latestMouseMoveTime: 0 as number,
  puaseMonitorMouseLeave: false as boolean,
  hideMenuTimer: undefined as (NodeJS.Timeout|undefined),
  mouseOnWnd: true as boolean,
  pauseMonitorHideMenu: () => {
    menuManagerObject.puaseMonitorMouseLeave = true;
    menuManagerObject.stopMonitorTimer();
  },
  continueMonitorHIdeMenu: () => {
    menuManagerObject.latestMouseMoveTime = (new Date()).getTime();
    menuManagerObject.puaseMonitorMouseLeave = false;
    menuManagerObject.beginMonitorTimer();
  },
  beginMonitorTimer: () => {
    if (playerControl_rct.showMenu && !menuManagerObject.hideMenuTimer) {
      menuManagerObject.hideMenuTimer = setInterval(() => {
        if ((menuManagerObject.latestMouseMoveTime > 0) && ((new Date()).getTime() - menuManagerObject.latestMouseMoveTime > 3000)) {
          playerControl_rct.showMenu = false;
          if (menuManagerObject.mouseOnWnd) {
            windowControlGlobalAction.showCursor(false);
            //windowControlGlobalAction.hideOrShowPlayerControlWnd(false);
          }
          clearInterval(menuManagerObject.hideMenuTimer);
          menuManagerObject.hideMenuTimer = undefined;
        }
      }, 1000);
    }
  },
  stopMonitorTimer: () => {
    menuManagerObject.latestMouseMoveTime = 0;
    if (menuManagerObject.hideMenuTimer) {
      clearInterval(menuManagerObject.hideMenuTimer);
      menuManagerObject.hideMenuTimer = undefined;
    }
  },
  onMouseLeave: () => {
    menuManagerObject.mouseOnWnd = false;
  },
  onMouseMove: () => {
    if (playerControl_rct.showMenu && !menuManagerObject.puaseMonitorMouseLeave) {
      menuManagerObject.latestMouseMoveTime = (new Date()).getTime();
    }
  },
  showMenu: (show: boolean, showfrom: TShowFrom) => {
    menuManagerObject.mouseOnWnd = true;
    if (show && !playerControl_rct.showMenu) {
      setShowFrom(showfrom)
      windowControlGlobalAction.showCursor(true);
      //windowControlGlobalAction.hideOrShowPlayerControlWnd(true);
      playerControl_rct.showMenu = show;
      menuManagerObject.continueMonitorHIdeMenu();
      menuManagerObject.onMouseMove();
    } else if (!show && playerControl_rct.showMenu){
      playerControl_rct.showMenu = false;
      menuManagerObject.pauseMonitorHideMenu();
      windowControlGlobalAction.showCursor(true);
      //windowControlGlobalAction.hideOrShowPlayerControlWnd(false);
    }
    // if (options.delay) {
    //   timerSetMenuShow = setTimeout(async () => {
    //     setShowFrom(showfrom)
    //     windowControlGlobalAction.showCursor(show);
    //     playerControl_rct.showMenu = show
    //     if (platform.isMacOS) {
    //       // ? 全屏时不处理红绿灯按钮
    //       if (!(windowControlGlobal.value.isFullScreen)) {
    //         if (!playerControl_rct.showMenu) {
    //           // ? 隐藏红绿灯按钮会比隐藏菜单快一点, 加延时
    //           await sleep(50)
    //         }
    //         windowControlGlobalAction.setWindowButtonVisibility(playerControl_rct.showMenu)
    //       }
    //     }
    //   }, options.delay)
    // } else {
    //   setShowFrom(showfrom)
    //   playerControl_rct.showMenu = show
    //   windowControlGlobalAction.showCursor(show);
    //   if (platform.isMacOS) {
    //     // ? 全屏时不处理红绿灯按钮
    //     if (!(windowControlGlobal.value.isFullScreen)) {
    //       if (!playerControl_rct.showMenu) {
    //         // ? 隐藏红绿灯按钮会比隐藏菜单快一点, 加延时
    //         await sleep(50)
    //       }
    //       windowControlGlobalAction.setWindowButtonVisibility(playerControl_rct.showMenu)
    //     }
    //   }
    // }
  }
}

type TMediaType = 'video' | 'audio'

const isMediaAudioWithoutCover_computed = computed(() => {
  const mediaInfo = aplayerInfo_rct.mediaInfo
  if (mediaInfo) {
    if (mediaInfo.width === 0 && mediaInfo.height === 0) {
      return true
    }
  }
  return false
})

type TCommonNoticeIcon = '' | 'forward' | 'backward'
type TCommonNoticeModule = 'volume' | 'progress' | 'speed' | 'subtitles' | 'ratio'

function genDefaultPlayerControl() {
  return {
    showMenu: true,
    show: false,
    isFirstMedia: true,
    mockMediaId: 0, // date
    mediaChangeLoading: false,
    buffer: {
      loading: false,
      speed: 0, // B/S
    },
    playStatus: MediaState.MsUnKnown, // 播放状态
    playStatusAni: MediaState.MsUnKnown, // 手动触发播放状态
    playStatusFailReason: '', // 播放失败原因
    setCurrentTimeCount: 0, // setCurrentTimeThrottle 方法加了节流，执行前后setCurrentTimeCount值比较来确定是否有执行
    isIgnoreProgressChanged: false, // 是否忽略进度条变化, 在主动设置进度+进度条ui，忽略第一次 pos event
    currentTime: 0, // 单位秒, 当前播放时间
    waitSetCurrentTimeDone: new WaitSomeThing(), // 等待setCurrentTime执行完成
    duration: 0, // 单位秒, 视频总时长
    percent: 0, // 单位 0-100, 当前播放进度百分比
    isFirstMediaChangeAttachMap: {} as Record<AplayerMedia['id'], boolean>, // 切换media后，是否 mediaChange 事件在主动获取前就已经执行
    preloadPercent: 0, // 单位 0-100, 视频预加载进度百分比
    hoverPercent: 0, // 单位 0-100, 鼠标悬停进度条百分比
    isProcessBarMoving: false, // 是否鼠标移动进度条
    isProcessBarDraging: false, // 是否鼠标拖动进度条
    processBarDragPreCurrentTime: 0, // 进度条拖动前当前播放时间
    showLastPlayNotice: false, // 是否显示定位提示
    speedList: isAPlayer ? [] : (mockSpeedList as PlaySpeedDisplayInfo[]), // 播放速度
    currentSpeed: null as PlaySpeedDisplayInfo | null, // 当前播放速度
    commonNotice: '' as string, // 通用 notice
    commonNoticeModule: undefined as TCommonNoticeModule | undefined, // 通用 notice 模块
    commonNoticeIcon: '' as TCommonNoticeIcon, // 通用 notice icon
    commonNoticeVisible: false,
    commonNoticeHideTimer: null as any,

    volume: [0], // 0~100

    ratioList: (isAPlayer ? [] : mockRatioList) as RatioItem[], // 清晰度
    currentRatio: null as RatioItem | null, // 当前清晰度
    switchingRatioId: '',

    subtitlesList: (isAPlayer
      ? []
      : mockSubtitlesList) as SubtitleItemDisplayInfo[],
    prevSubtitles: null as SubtitleItemDisplayInfo | null,
    currentSubtitles: null as SubtitleItemDisplayInfo | null,
    subtitlesPopoverState: {
      visible: false,
      module: 'list' as 'list' | 'config',
      isTimingInputVisible: false,
      isHoverFalseAutoClose: true,
    },
    subtitlesConfig: genSubtitlesDefaultConfig(),

    currentPlayId: '',
    playList: (isAPlayer ? [] : mockPlayList) as PlayListItem[],
    playMediaInfoMap: isAPlayer
      ? new Map<PlayListItem['id'], PlayListItemMediaInfo>()
      : mockPlayMediaInfoMap,
    playlistPopoverState: {
      visible: false,
    },
    playlistChanging: false,

    audioTrackList: [] as string[],
    currentAudioTrackIdx: null as number | null,

    currentPlaySequence: null as ListPlaySequence | null, // 当前播放模式

    playviewConfig: genPlayViewDefaultConfig(),
    imageRatioItemList: [] as ImageRatioItem[],
  }
}

function clone<T extends object>(val: T): T {
  let obj = {} as T
  Object.keys(val).forEach((key) => {
    obj[key] = val[key]
  })
  return obj
}

const playerControl_rct = reactive(genDefaultPlayerControl())
function resetPlayerControl() {
  const defaultVal = genDefaultPlayerControl()
  Object.keys(playerControl_rct).forEach((key) => {
    playerControl_rct[key] = defaultVal[key]
  })
}

const isAudioNoCoverUiShow_computed = computed(() => [MediaState.MsPlay, MediaState.MsPause].includes(playerControl_rct.playStatus) && isMediaAudioWithoutCover_computed.value)
const isPlayErrorUiShow_computed = computed(() => playerControl_rct.playStatus === MediaState.MsFailed)


let showLastPlayNoticeTimer: NodeJS.Timeout
watch(
  () => playerControl_rct.showLastPlayNotice,
  () => {
    showLastPlayNoticeTimer && clearTimeout(showLastPlayNoticeTimer)
    if (playerControl_rct.showLastPlayNotice) {
      showLastPlayNoticeTimer = setTimeout(() => {
        playerControl_rct.showLastPlayNotice = false
      }, 3000)
    }
  },
)

async function clearXmpPlayerData() {

  prevCloseCache = {
    playerControl: clone(playerControl_rct),
    statData: clone(statData_rct),
    aplayerInfo: clone(aplayerInfo_rct),
  }
  prevCloseCache.statData.waitClosePlayEndEvent?.waitDone().then(() => {
    prevCloseCache = null
  })
  aplayerMedia = null
  aplayerInfo_rct.mediaInfo = null
  resetPlayerControl()
  resetStatData()
}

const playerAction = {
  showPlayer(
    mediaAttr?: MediaAttribute,
    openMediaFrom?: TOpenMediaFrom,
    autoPlay?: boolean,
  ) {
    if (mediaAttr && openMediaFrom) {
      playerControlAction.setVideo(mediaAttr, openMediaFrom, autoPlay)
    }
    playerControl_rct.show = true
  },
  /**
   *
   * @param isCloseMedia true, 会清空数据，埋点需要再前面执行
   */
  async hidePlayer(isCloseMedia: boolean = true) {
    playerControl_rct.show = false
    if (isCloseMedia) {
      await playerControlAction.close()
      await clearXmpPlayerData()
    }
    if (platform.isWindows) {
      if (windowControlGlobal.value.isFullScreen) {
        windowControlGlobalAction.toggleFullscreen()
      }
    }
  },
  minimizeWindow() {
    windowControlGlobalAction.minizeWindow()
  },
  toggleMaxWindow() {
    windowControlGlobalAction.toggleMaxWindow()
  },
}

// * ===== 处理 鼠标hover 菜单展示逻辑 ===== start
// let timerSetMenuShow
// const setMenuShow = async (
//   show: boolean,
//   showfrom: TShowFrom,
//   options: {
//     delay?: number
//     animate?: boolean
//   } = {
//       animate: true,
//     },
// ) => {
//   timerSetMenuShow && clearTimeout(timerSetMenuShow)
//   if (show === playerControl_rct.showMenu) return

//   if (options.delay) {
//     timerSetMenuShow = setTimeout(async () => {
//       setShowFrom(showfrom)
//       playerControl_rct.showMenu = show
//       if (platform.isMacOS) {
//         // ? 全屏时不处理红绿灯按钮
//         if (!(windowControlGlobal.value.isFullScreen)) {
//           if (!playerControl_rct.showMenu) {
//             // ? 隐藏红绿灯按钮会比隐藏菜单快一点, 加延时
//             await sleep(50)
//           }
//           windowControlGlobalAction.setWindowButtonVisibility(playerControl_rct.showMenu)
//         }
//       }
//     }, options.delay)
//   } else {
//     setShowFrom(showfrom)
//     playerControl_rct.showMenu = show
//     if (platform.isMacOS) {
//       // ? 全屏时不处理红绿灯按钮
//       if (!(windowControlGlobal.value.isFullScreen)) {
//         if (!playerControl_rct.showMenu) {
//           // ? 隐藏红绿灯按钮会比隐藏菜单快一点, 加延时
//           await sleep(50)
//         }
//         windowControlGlobalAction.setWindowButtonVisibility(playerControl_rct.showMenu)
//       }
//     }
//   }
// }

const isPlayerHeaderHovered_ref = ref(false)
const isPlayerMenuHovered_ref = ref(false) // 是否鼠标悬停在底部菜单dom, 包括菜单dom下的浮窗
const playerContainerIsOutside_ref = ref(false)
const isSubtitlesHover_ref = ref(false)
const isPlaylistHover_ref = ref(false)

// ? browserWindow not focus, 鼠标移入和移除时, 会先触发一次mousemove, 然后立即触发 mouseleave
const macWindowUnFocus_mousemove_count_ref = ref(0)

watch(() => windowControlGlobal.value.focus, () => {
  // if (windowControlGlobal.value.focus) {
  // focus 时清零
  // debuggerLogger.log('window focus change, clear')
  macWindowUnFocus_mousemove_count_ref.value = 0
  // }
})

const x = ref(0)
const y = ref(0)

useEventListener($xmpPlayerContainer, 'mousemove', (e) => {
  // debuggerLogger.log('playermousemove-- screenX', e.screenX, "|x1|", e.x, "|screenY|", e.screenY, "|y1|", e.y)
  // win7Logger.log('playermousemove-- screenX', e.screenX, "|x1|", e.x, "|screenY|", e.screenY, "|y1|", e.y)

  win7Logger.log('playermousemove--')
  x.value = e.x
  y.value = e.y

  const composedPathArr = e.composedPath()
  isPlayerHeaderHovered_ref.value = composedPathArr.includes(
    $playerHeader.value!,
  )
  isPlayerMenuHovered_ref.value = composedPathArr.includes($playerMenu.value!)
  isSubtitlesHover_ref.value = composedPathArr.includes(
    $subtitlesContainer.value!,
  )
  isPlaylistHover_ref.value = composedPathArr.includes(
    $playlistContainer.value!,
  )
})

useEventListener($xmpPlayerContainer, 'mouseleave', (e) => {
  // 全屏时会触发 mouseleave
  // debuggerLogger.log('playermouseleave-- screenX', e.screenX, "|x1|", e.x, "|screenY|", e.screenY, "|y1|", e.y)
  // if (!windowControlGlobal.value.focus) return

  if (platform.isMacOS) {
    if (windowControlGlobal.value.focus) {
      playerContainerIsOutside_ref.value = true
    } else {
      // if (macWindowUnFocus_mousemove_count_ref.value % 2 === 0) {
      //   // ? 偶数次触发, 算 mouseleave
      //   debuggerLogger.log('mac unfocus mouseleave')
      //   // playerContainerIsOutside_ref.value = true
      //   setMenuShow(false, '')
      // }
    }
  } else {
    playerContainerIsOutside_ref.value = true
    // debuggerLogger.log('playermouseleave-- true')
  }

  const composedPathArr = e.composedPath()
  isPlayerHeaderHovered_ref.value = composedPathArr.includes(
    $playerHeader.value!,
  )
  isPlayerMenuHovered_ref.value = composedPathArr.includes($playerMenu.value!)
  isSubtitlesHover_ref.value = composedPathArr.includes(
    $subtitlesContainer.value!,
  )
  isPlaylistHover_ref.value = composedPathArr.includes(
    $playlistContainer.value!,
  )
  isPlayerHeaderHovered_ref.value = false
  isPlayerMenuHovered_ref.value = false
  isSubtitlesHover_ref.value = false
  isPlaylistHover_ref.value = false

  $voicePopover.value?.hide()
  $qualityPopover.value?.hide()
  $speedPopover.value?.hide()
  $playviewPopover.value?.hide()


  win7Logger.log('playermouseleave--', isPlaylistHover_ref.value)
})



// const { x, y } = useMouse()

const useMouseDragAndClickFn = useMouseDragMoveAndClickViaEventAndMain
useMouseDragAndClickFn($xmpPlayerContainer, {
  isSetWindowDraggable: true,
  async onClick(e) {
    if (e) {
      // 是否触发
      const target = e.target as HTMLElement
      let isMatch = false
      const composedPath = getComposedPath(e)
      if (composedPath.includes($audioContainer.value!)) {
        isMatch = true
      }
      if (isAPlayer) {
        if (target.matches('.xmp-player')) {
          isMatch = true
        }
      } else {
        if (target.matches('.video-dom')) {
          isMatch = true
        }
      }
      if (!isMatch) return
    }

    if (!e) {
      // if (contextMenuIns_ref.value) {
      //   // ? 如果右键菜单出现，则只关闭右键菜单, 后直接return
      //   contextMenuIns_ref.value.handler.close()
      //   return
      // }
      if (playerControl_rct.subtitlesPopoverState.visible) {
        // ? 如果 字幕popover 出现，则只关闭 字幕popover , 后直接return
        closeSubtitlesPopover()
        return
      }
    }

    // ? 鼠标单击 播放暂停
    if (playerControl_rct.playStatus === MediaState.MsPlay) {
      const isSuccess = await playerControlAction.pause(true)
      if (isSuccess) {
        statPlayerAction({
          action: 'pause',
          control: 'mouse',
        })
      }
    } else {
      const isSuccess = await playerControlAction.play(true)
      if (isSuccess) {
        statPlayerAction({
          action: 'play',
          control: 'mouse',
        })
      }
    }
  },
  onDbClick(e) {
    if (e) {
      // 是否触发
      const target = e.target as HTMLElement
      let isMatch = false
      const composedPath = getComposedPath(e)
      if (composedPath.includes($audioContainer.value!)) {
        isMatch = true
      }
      // 双击header进入全屏
      if (composedPath.includes($playerHeader.value!)) {
        isMatch = true
      }
      if (isAPlayer) {
        if (target.matches('.xmp-player')) {
          isMatch = true
        }
      } else {
        if (target.matches('.video-dom')) {
          isMatch = true
        }
      }
      if (!isMatch) return
    }

    // ? 鼠标双击全屏
    playerControlAction.toggleFullScreen()
    statPlayerAction({
      action: 'full_screen',
      control: 'mouse',
    })
  },
  onMouseMove(e) {
    // debuggerLogger.log('playermousemove-- screenX', e.screenX, "|x1|", e.x, "|screenY|", e.screenY, "|y1|", e.y)
    // win7Logger.log('playermousemove-- screenX', e.screenX, "|x1|", e.x, "|screenY|", e.screenY, "|y1|", e.y)

    // win7Logger.log('playermousemove--')

    menuManagerObject.showMenu(true, 'mouse_hover');
    menuManagerObject.onMouseMove();
  },
  onMouseLeave(e) {
    menuManagerObject.onMouseLeave();
  },
  onMouseWheel(w) {
    let n = w>>16;
    if (n < 0) {
      playerControlAction.plusVolume(-5)
    } else {
      playerControlAction.plusVolume(5)
    }
  },
  async onKeyDown(code: number) {
    if (!playerControl_rct.show) return

  if (playerControl_rct.subtitlesPopoverState.isTimingInputVisible) return
  // if (settingDialogIns_ref.value) return
  // if (linkDialogIns_ref.value) return

  // console.log(
  //   'keydown 111111 222222',
  //   event.keyCode,
  //   event.code,
  //   event.repeat,
  //   event.altKey,
  //   event.ctrlKey,
  //   event.metaKey,
  //   event.shiftKey,
  //   event,
  // )
  switch (code) {
    // 空格：播放/暂停
    case 32: {
      if (playerControl_rct.playStatus === MediaState.MsPlay) {
         await playerControlAction.pause(true)
      } else {
        await playerControlAction.play(true)
      }
      break
    }

    // Ctrl+s：停止
    case 83: {
      AplayerStack.GetInstance().closePlayWindow();
      break
    }

    // Page Up：上一集
    case 33: {
      playerControlAction.switchPrevPlayListItem()
      break
    }

    // Page Down：下一集
    case 34: {
      playerControlAction.switchNextPlayListItem()
      break
    }

    case 77: {
      // M: 开启/解除静音
      const action = await playerControlAction.toggleVolumeMute()
      break
    }

    // ESC：退出全屏
    case 27: {
      if (windowControlGlobal.value.isFullScreen) {
          playerControlAction.toggleFullScreen()
          statPlayerAction({
            action: 'full_screen',
            control: 'keyboard',
          })
        }
      break
    }

    // Enter：进入全屏
    case 13: {
      if ($xmpPlayerContainer.value) {
          // 有input框且是focus状态下, 不触发全屏 (和businessNeedMenuShow_computed不完全相等)
          const inputElList =
            $xmpPlayerContainer.value.querySelectorAll<HTMLInputElement>(
              'input',
            )
          if (
            Array.from(inputElList).some(
              (inputEl) => document.activeElement === inputEl,
            )
          ) {
            return
          }
        }
        if (!windowControlGlobal.value.isFullScreen) {
          playerControlAction.toggleFullScreen()
          statPlayerAction({
            action: 'full_screen',
            control: 'keyboard',
          })
        }
      break
    }

    // 左键：快退5s
    case 37: {
      const res = await playerControlAction.plusCurrentTime(-5)
      break
    }

    // 右键：快进5s
    case 39: {
      const res = await playerControlAction.plusCurrentTime(5)
      break
    }

    // 上键：音量+5
    case 38: {
      const res = await playerControlAction.plusVolume(5)
      break
    }

    // 下键：音量-5
    case 40: {
      const res = await playerControlAction.plusVolume(-5)
      break
    }
  }
  }
})

const businessNeedMenuShow_computed = computed(() => {
  // popover input 框展示时, 不隐藏
  return playerControl_rct.subtitlesPopoverState.isTimingInputVisible
})

const clacMenuShow = () => {
  if (!playerControl_rct.show) return

  const isShowViaBusiness = isPlayerHeaderHovered_ref.value ||
    isPlayerMenuHovered_ref.value ||
    businessNeedMenuShow_computed.value

  // if (platform.isMacOS && !windowControlGlobal.value.focus) {
  //   // mac 窗口非focus时 不处理
  //   return
  // }
  if(isShowViaBusiness) {
    menuManagerObject.pauseMonitorHideMenu();
  } else {
    menuManagerObject.continueMonitorHIdeMenu();
  }

  // if ([MediaState.MsPlay, MediaState.MsSucc].includes(playerControl_rct.playStatus)) {
  //   if (!playerContainerIsOutside_ref.value) {
  //     // 鼠标在 player 中

  //     if (playerControl_rct.showMenu) {
  //       // 当前菜单展示, 计算是否隐藏

  //       if (
  //         // ? 把需要显示的条件都放在这里
  //         isShowViaBusiness
  //       ) {
  //         // 鼠标悬浮在 顶部栏 和 底部菜单栏 中(或其他自定义条件), 不隐藏
  //         win7Logger.log('setMenuShow 1', true)
  //         setMenuShow(true, 'mouse_hover')
  //       } else {
  //         // 鼠标悬浮在 非菜单 区, 隐藏, delay
  //         win7Logger.log('setMenuShow 2 delay', false)
  //         setMenuShow(false, '', {
  //           delay: 2000,
  //         })
  //       }
  //     } else {
  //       // 鼠标移动，当前菜单隐藏, 展示, 立即
  //       win7Logger.log('setMenuShow 3', true)
  //       setMenuShow(true, 'mouse_hover')
  //     }
  //   } else {
  //     // 鼠标在 player 外, 隐藏, 立即
  //     win7Logger.log('setMenuShow 4', false)
  //     setMenuShow(false, '', {
  //       delay: 20,
  //     })
  //   }
  // } else {
  //   // 未播放, 展示, 立即
  //   if (playerControl_rct.playlistChanging) {
  //     // 切换选集展示菜单
  //     win7Logger.log('setMenuShow 5', true)
  //     setMenuShow(true, 'select_episode')
  //   } else {
  //     // 默认 暂停
  //     win7Logger.log('setMenuShow 6', true)
  //     setMenuShow(true, 'pause')
  //     win7Logger.log('setMenuShow xxx', playerContainerIsOutside_ref.value, isShowViaBusiness)
  //     if (
  //       // 鼠标在 player 外
  //       playerContainerIsOutside_ref.value ||
  //       // ? 把需要显示的条件都放在这里
  //       !isShowViaBusiness
  //     ) {
  //       // 暂停, 鼠标悬浮在 非菜单 区, 隐藏, delay
  //       win7Logger.log('setMenuShow 7 delay', false)
  //       setMenuShow(false, '', {
  //         delay: 3000,
  //       })
  //     } else {
  //       // 暂停, 鼠标悬浮在 顶部栏 和 底部菜单栏 中(或其他自定义条件), 不隐藏
  //     }
  //   }
  // }
}

watch(
  () => [
    playerContainerIsOutside_ref.value,
    isPlayerHeaderHovered_ref.value,
    isPlayerMenuHovered_ref.value,
    // 播放状态
    playerControl_rct.playStatus,
    // 监听鼠标移动, 触发菜单展示计算
    x.value,
    y.value,
    // 业务需要不隐藏菜单
    businessNeedMenuShow_computed.value,
  ],
  clacMenuShow,
  {
    immediate: true,
  },
)
watch(
  () => [windowControlGlobal.value.isFullScreen],
  async () => {
    menuManagerObject.showMenu(true, 'mouse_hover');
  },
)

// * ===== 处理 鼠标hover 菜单展示逻辑 ===== end

// * ===== 字幕 popover 展示逻辑 ===== start
let closeSubtitlesPopoverTimer
async function closeSubtitlesPopover() {
  closeSubtitlesPopoverTimer && clearTimeout(closeSubtitlesPopoverTimer)

  playerControl_rct.subtitlesPopoverState.visible = false
  closeSubtitlesPopoverTimer = setTimeout(() => {
    playerControl_rct.subtitlesPopoverState = {
      visible: false,
      module: 'list',
      isTimingInputVisible: false,
      isHoverFalseAutoClose: true,
    }
  }, Player_UI_Const.Popover_Hide_Delay_Second * 1000)
}
async function openSubtitlesPopover(isActiveScrollIntoView: boolean) {
  closeSubtitlesPopoverTimer && clearTimeout(closeSubtitlesPopoverTimer)

  playerControl_rct.subtitlesPopoverState.visible = true
  if (isActiveScrollIntoView) {
    $subtitlesItemList.value?.forEach((ref) => ref.scrollIntoView())
  }

}
onClickOutside(
  $subtitlesContainer,
  (event) => {
    if (!playerControl_rct.show) return
    if (!event.composedPath().includes($subtitlesContainer.value!)) {
      closeSubtitlesPopover()
    }
  },
  {
    ignore: genOnClickOutsideIgnore(),
  },
)
watch(
  [
    isSubtitlesHover_ref,
    () => playerControl_rct.subtitlesPopoverState.module,
    () => playerControl_rct.subtitlesPopoverState.isHoverFalseAutoClose,
  ],
  () => {
    if (isSubtitlesHover_ref.value) {
      openSubtitlesPopover(true)
      return
    }
    if (!playerControl_rct.subtitlesPopoverState.isHoverFalseAutoClose) {
      openSubtitlesPopover(false)
      return
    }
    closeSubtitlesPopover()
  },
)
// * ===== 字幕 popover 展示逻辑 ===== end

// * ===== 播放列表 popover 展示逻辑 ===== start
async function closePlaylistPopover() {
  playerControl_rct.playlistPopoverState.visible = false
}
async function openPlaylistPopover(isActiveScrollIntoView: boolean) {
  playerControl_rct.playlistPopoverState.visible = true
  if (isActiveScrollIntoView) {
    $playlistItemList.value?.forEach((ref) => ref.scrollIntoView())
  }
}
onClickOutside(
  $playlistContainer,
  (event) => {
    if (!playerControl_rct.show) return
    if (!event.composedPath().includes($playlistContainer.value!)) {
      closePlaylistPopover()
    }
  },
  {
    ignore: genOnClickOutsideIgnore(),
  },
)
watch([isPlaylistHover_ref], () => {
  if (isPlaylistHover_ref.value) {
    openPlaylistPopover(true)
    return
  }
  closePlaylistPopover()
})
// * ===== 播放列表 popover 展示逻辑 ===== end

const $videoRef = useTemplateRef<HTMLVideoElement>('$videoRef')
let videoWaitCanPlay = new WaitSomeThing()
let videoWaitPlay: WaitSomeThing | undefined
const videoInfo_rct = reactive({
  src: '',
})

// * init h5 video
const initVideo = () => {
  if (!$videoRef.value) return

  const video = $videoRef.value

  playerControl_rct.volume = [Math.floor(video.volume * 100)]

  playerControl_rct.currentSpeed =
    playerControl_rct.speedList.find(
      (speedItem) => speedItem.showValue === '1.0',
    ) || playerControl_rct.speedList[0]

  // * 播放进度更新事件
  const onTimeUpdate = () => {
    // ? 正在拖动进度条时, 视频正常播, 但不更新进度条
    if (playerControl_rct.isProcessBarDraging) return

    // 视频时间更新处理
    const currentTime = video.currentTime
    playerControl_rct.currentTime = currentTime
    const percent = (currentTime / playerControl_rct.duration) * 100
    playerControl_rct.percent = percent
  }
  const onLoadedMetadata = async () => {
    // 视频元数据加载完成处理
    vidoeLog.log('onLoadedMetadata', video.duration)
    playerControl_rct.showLastPlayNotice = false

    playerControl_rct.playStatus = MediaState.MsSucc
    if (!Number.isNaN(video.duration)) {
      playerControl_rct.duration = video.duration
    }

    setTimeout(() => {
      playerControl_rct.showLastPlayNotice = true
    }, 200)
  }
  const onProgress = () => {
    // 视频预加载进度处理
    if (!video.buffered.length) return
    const buffered = video.buffered
    const currentTime = video.currentTime
    const duration = video.duration

    // 查找当前时间所在的缓冲区间
    let currentBufferEnd = 0
    for (let i = 0; i < buffered.length; i++) {
      if (currentTime >= buffered.start(i) && currentTime <= buffered.end(i)) {
        currentBufferEnd = buffered.end(i)
        break
      }
    }

    // 计算预加载百分比
    const preloadPercent = (currentBufferEnd / duration) * 100
    playerControl_rct.preloadPercent = preloadPercent
  }
  const onLoadStart = () => {
    // 视频开始加载处理
    vidoeLog.log('onLoadStart', video.duration)
    playerControl_rct.playStatus = MediaState.MsOpenning
  }
  const onCanPlay = async () => {
    await sleep(2000)

    vidoeLog.log('onCanPlay', video.duration)
    // 视频可以播放处理
    videoWaitCanPlay.done()
  }
  const onWaiting = () => {
    // 视频缓冲处理
  }
  const onPlay = () => {
    vidoeLog.log('onPlay', video.duration)
    // 播放状态处理
    playerControl_rct.playStatus = MediaState.MsPlay

    videoWaitPlay?.done()
  }
  const onPause = () => {
    // 暂停状态处理
    playerControl_rct.playStatus = MediaState.MsPause
  }
  const onEnded = () => {
    // 播放结束处理
    playerControl_rct.playStatus = MediaState.MsStop
  }
  const onError = (...e) => {
    // 错误处理
    vidoeLog.log('onError', video.src, aplayerInfo_rct.mediaInfo)
    playerControl_rct.playStatus = MediaState.MsFailed
    switch (aplayerInfo_rct.mediaInfo?.type) {
      case MediaType.MtNewXmpLocal:
        playerControl_rct.playStatusFailReason =
          '网络链接出错/文件格式不支持/文件已损坏'
        break
      default:
        playerControl_rct.playStatusFailReason = '请检查链接是否正确'
        break
    }
  }

  // 绑定事件监听
  video.addEventListener('timeupdate', onTimeUpdate)
  video.addEventListener('play', onPlay)
  video.addEventListener('pause', onPause)
  video.addEventListener('ended', onEnded)
  video.addEventListener('error', onError)
  video.addEventListener('loadedmetadata', onLoadedMetadata)
  video.addEventListener('progress', onProgress)
  video.addEventListener('loadstart', onLoadStart)
  video.addEventListener('canplay', onCanPlay)
  video.addEventListener('waiting', onWaiting)

  // 监听视频源变化
  const watchHandle = watch(
    () => videoInfo_rct.src,
    async () => {
      if (videoInfo_rct.src) {
        vidoeLog.log('watch $videoRef.value.src', videoInfo_rct.src)
        // 重置等待播放状态
        videoWaitCanPlay = new WaitSomeThing()
        // 重置播放状态
        playerControl_rct.playStatus = MediaState.MsUnKnown
        playerControl_rct.currentTime = 0
        playerControl_rct.duration = 0
        playerControl_rct.percent = 0
        playerControl_rct.preloadPercent = 0
      }
      if ($videoRef.value) {
        $videoRef.value.src = videoInfo_rct.src
      }
    },
  )

  // 返回解绑函数
  return () => {
    video.removeEventListener('timeupdate', onTimeUpdate)
    video.removeEventListener('play', onPlay)
    video.removeEventListener('pause', onPause)
    video.removeEventListener('ended', onEnded)
    video.removeEventListener('error', onError)
    video.removeEventListener('loadedmetadata', onLoadedMetadata)
    video.removeEventListener('progress', onProgress)
    video.removeEventListener('loadstart', onLoadStart)
    video.removeEventListener('canplay', onCanPlay)
    video.removeEventListener('waiting', onWaiting)
    watchHandle.stop()
  }
}
let unbindVideo: (() => void) | undefined
onMounted(() => {
  unbindVideo = initVideo()
  AplayerStack.GetInstance().addCloseWndHook(() => {
    clearXmpPlayerData();
  })
})
onBeforeUnmount(() => {
  unbindVideo?.()
})

// * Aplayer
let AplayerStackModule:
  | typeof import('@root/common/player/impl/aplayer-stack')['AplayerStack']
  | null = null
const waitAplayerStackLoad = new WaitSomeThing()
if (isAPlayer) {
  // @ts-ignore
  import('@root/common/player/impl/aplayer-stack').then((module) => {
    AplayerStackModule = module.AplayerStack
    waitAplayerStackLoad.done()
  })
}

const aplayerAction = {
  async onMediaChange(m: AplayerMedia) {
    aPlayerLog.log('onMediaChange', m)

    playerControl_rct.mediaChangeLoading = true

    aplayerMedia = m
    playerControl_rct.mockMediaId = Date.now()

    // 清空播放信息
    aplayerInfo_rct.mediaInfo = null
    playerControl_rct.showLastPlayNotice = false
    playerControl_rct.currentTime = 0
    playerControl_rct.duration = 0
    playerControl_rct.percent = 0

    // 展示播放页
    playerControl_rct.show = true

    // 清空 画面调节
    playerControl_rct.playviewConfig.imageRatio = genDefaultPlayerControl().playviewConfig.imageRatio
    const AplayerStack = await playerControlAction.getAplayerStack()
    if (AplayerStack) {
      playerControl_rct.imageRatioItemList =
        AplayerStack.GetInstance().getImageRatioItems()
    }
  },
}

const initAplayer = async () => {
  if (!isAPlayer) return

  const AplayerStack = await playerControlAction.getAplayerStack()
  if (!AplayerStack) return

  // ? 初始化player静音
  AplayerStack?.GetInstance().setVolume(0)

  const onPlayListPreparedEvent = async () => {
    aPlayerLog.log('onPlayListPreparedEvent fire')
    const playListIns = AplayerStack.GetInstance().getPlayList()
    playerControl_rct.playList = playListIns.getPlayList()
    playerControl_rct.currentPlayId = playListIns.getPlayingItemId()
    aPlayerLog.log('client playlist info=', playerControl_rct.playList)

    // 异步去查询 mediaInfo
    // ? playlistItem scrollIntoView 再请求 item mediaInfo
    // playerControlAction.getCurrentPlayListMediaInfo()
  }
  const onPlayListSelectChangeEvent = (id: string) => {
    aPlayerLog.log('onPlayListSelectChangeEvent id =', id)
    playerControl_rct.currentPlayId = id
    statData_rct.endway = 'select_episode'

  }

  const onMediaChangeEvent = async (m: AplayerMedia) => {
    const onProgressChangedEvent = async (progress: number) => {
      aPlayerLog.log('play pos =', progress)

      if (playerControl_rct.mediaChangeLoading) {
        playerControl_rct.mediaChangeLoading = false
      }
      if (playerControl_rct.buffer.loading) {
        playerControl_rct.buffer.loading = false
      }
      // if (playerControl_rct.mediaChangeLoading) return
      // ? 正在拖动进度条时, 视频正常播, 但不更新进度条
      if (playerControl_rct.isProcessBarDraging) return

      // ? 手动设置进度后，会有1次pos是设置前的位置，在这里忽略
      if (playerControl_rct.isIgnoreProgressChanged) {
        playerControl_rct.isIgnoreProgressChanged = false
        return
      }

      const duration = Math.floor(
        m.getDuration() / 1000,
      )
      // ? 前面 await 后会出现时序问题，有可能当前已经正在切换选集了，在这里忽略
      // if (playerControl_rct.mediaChangeLoading) return

      playerControl_rct.duration = duration
      // 视频时间更新处理
      const currentTime = Math.round(progress / 1000)
      // ? 过滤异常的时间
      if (currentTime <= playerControl_rct.duration) {
        playerControl_rct.currentTime = currentTime
        const percent = (currentTime / playerControl_rct.duration) * 100
        playerControl_rct.percent = percent

        if (!playerControl_rct.waitSetCurrentTimeDone.isDone()) {
          playerControl_rct.waitSetCurrentTimeDone.done()
        }
      }
    }
    const onPlayStateChangeEvent = async (state: MediaState) => {
      // TODO
      // playerControl_rct.isFirstMediaChangeAttachMap[m.id] = true

      aPlayerLog.log('media state =', state)
      playerControl_rct.playStatus = state

      switch (playerControl_rct.playStatus) {
        case MediaState.MsFailed: {
          playerControl_rct.mediaChangeLoading = false
          playerControl_rct.playlistChanging = false
          const errorInfo = aplayerMedia?.getMediaErrorInfo()
          playerControl_rct.playStatusFailReason = `出错了${errorInfo?.errCode ? `：${errorInfo.errCode}` : ''}，请检查播放的链接或文件`

          // if (errorInfo?.errMsg) {
          //   playerControl_rct.playStatusFailReason = errorInfo.errMsg
          // } else if () {
          // } else {
          //   switch (aplayerInfo_rct.mediaInfo?.type) {
          //     case MediaType.MtNewXmpLocal:
          //       playerControl_rct.playStatusFailReason =
          //         '文件格式不支持/文件已损坏'
          //       break
          //     default:
          //       playerControl_rct.playStatusFailReason = '请检查链接是否正确'
          //       break
          //   }
          // }

          // 更新 mediaInfo 信息
          if (aplayerMedia) {
            aplayerInfo_rct.mediaInfo = {
              name: aplayerMedia.getName(),
              type: aplayerMedia.getType(),
              width: aplayerMedia.getMediaWidth(),
              height: aplayerMedia.getMediaHeight(),
              attribute: aplayerMedia.getAttribute(),
            }
          }
          break
        }

        // 开始播放新media
        case MediaState.MsSucc: {
          debugger
          playerControl_rct.mediaChangeLoading = false
          playerControl_rct.playlistChanging = false
          if (aplayerMedia) {
            // * 初始化 设置 media 和 aplayer 各参数
            (aplayerInfo_rct.mediaInfo as any) = {
              name: aplayerMedia.getName(),
              type: aplayerMedia.getType(),
              width: aplayerMedia.getMediaWidth(),
              height: aplayerMedia.getMediaHeight(),
              attribute: aplayerMedia.getAttribute(),
            }
            aPlayerLog.log('mediaInfo', aplayerInfo_rct.mediaInfo)
            if (!waitPlayerShowGetMediaInfo.isDone()) {
              waitPlayerShowGetMediaInfo.done()
            }
            
            if (playerControl_rct.isFirstMedia) {
              // ? 首次进入，恢复 1.0x 倍速
              playerControl_rct.speedList = AplayerStack.GetInstance()
                .getSupportedPlaySpeedList()
              const defaultSpeedItem = playerControl_rct.speedList.find(
                (item) => item.id === 'play_speed_1',
              )
              if (defaultSpeedItem) {
                AplayerStack?.GetInstance().switchSpeed(
                  defaultSpeedItem.id,
                )
                playerControl_rct.currentSpeed = defaultSpeedItem
              } else {
                const currentSpeedId =
                  AplayerStack.GetInstance().getCurrentPlaySpeedId()
                playerControl_rct.currentSpeed =
                  playerControl_rct.speedList.find(
                    (speedItem) => speedItem.id === currentSpeedId,
                  ) || null
              }
            } else {
              const currentSpeedId =
                AplayerStack.GetInstance().getCurrentPlaySpeedId()
              playerControl_rct.speedList = AplayerStack.GetInstance()
                .getSupportedPlaySpeedList()
              playerControl_rct.currentSpeed =
                playerControl_rct.speedList.find(
                  (speedItem) => speedItem.id === currentSpeedId,
                ) || null
            }

            let currentVolume = await AplayerStack.GetInstance().getVolume()
            // todo mac 1.2
            if (playerControlGlobal.value.isAppLaunchFirstMediaPlay) {
              // ? App 首次进入, 默认上次记忆的音量，时机可以提前设置
              currentVolume = await playerControlStore.get('currVolume') ?? 80
              AplayerStack?.GetInstance().setVolume(currentVolume)
            }
            playerControl_rct.volume = [currentVolume]

            // todoNext 播放模式 目前默认写死
            if (
              (await AplayerStack.GetInstance().getCurrentPlaySequence()) !==
              ListPlaySequence.OneByOne
            ) {
              AplayerStack.GetInstance().setPlaySequence(
                ListPlaySequence.OneByOne,
              )
            }
            playerControl_rct.currentPlaySequence =
              await AplayerStack.GetInstance().getCurrentPlaySequence()



            if (playerControl_rct.isFirstMedia) {
              playerControl_rct.isFirstMedia = false
            }
            if (playerControlGlobal.value.isAppLaunchFirstMediaPlay) {
              playerControlGlobalAction.set('isAppLaunchFirstMediaPlay', false)
            }

          }
          break
        }

        case MediaState.MsPause:
        case MediaState.MsStop: {
          playerControl_rct.mediaChangeLoading = false
          break
        }
      }

      // 处理时长统计
      switch (playerControl_rct.playStatus) {
        case MediaState.MsPlay: {
          startPlayDurationTime()
          break
        }
        case MediaState.MsPause:
        case MediaState.MsStop: {
          pausePlayDurationTime()
          break
        }
      }
    }
    const onSubtitleSelectChangeEvent = async (
      id: string,
      type: SubtitleCategory,
      isAuto: boolean,
    ) => { }
    const onSubtitleManagerPreparedEvent = async () => {
      aPlayerLog.log('media subtitlemanager prepared')
      const [
        subtitleList_Embed,
        subtitleList_Online,
        subtitleList_Local,
        subtitleList_Pan,
      ] = [
        m.getSubtitleManager().getList(SubtitleCategory.Embed),
        m.getSubtitleManager().getList(SubtitleCategory.Online),
        m.getSubtitleManager().getList(SubtitleCategory.Local),
        m.getSubtitleManager().getList(SubtitleCategory.Pan),
      ]
      aPlayerLog.log(
        'media all category subtitleList，',
        '内挂：',
        subtitleList_Embed,
        '在线:',
        subtitleList_Online,
        '本地',
        subtitleList_Local,
        '云盘',
        subtitleList_Pan,
      )

      playerControl_rct.subtitlesList = ([] as SubtitleItemDisplayInfo[])
        .concat(subtitleList_Embed)
        .concat(subtitleList_Online)
      const subtitleId = m.getSubtitleManager().getSelect()
      if (subtitleId) {
        playerControl_rct.currentSubtitles =
          playerControl_rct.subtitlesList.find(
            (subtitlesItem) => subtitlesItem.id === subtitleId,
          ) || null
      } else {
        playerControl_rct.currentSubtitles = null
      }
      playerControl_rct.prevSubtitles = playerControl_rct.currentSubtitles
      aPlayerLog.log(
        'media subtitleList',
        playerControl_rct.subtitlesList,
        playerControl_rct.currentSubtitles,
      )

      // ? 底层默认选中字幕
      // if (
      //   !playerControl_rct.currentSubtitles &&
      //   playerControl_rct.subtitlesList.length
      // ) {
      //   // 自动选择第一个字幕
      //   await playerControlAction.changeSubtitles(
      //     playerControl_rct.subtitlesList[0],
      //     false,
      //   )
      // }

      const [timing, fontStyle, position] = [
        m.getSubtitleManager().getTimming(),
        m.getSubtitleManager().getFontStyle(),
        m.getSubtitleManager().getPosition(),
      ]
      aPlayerLog.log('media subtitles config', timing, fontStyle, position)
      playerControl_rct.subtitlesConfig.timing = Math.floor(timing / 1000)
      playerControl_rct.subtitlesConfig.fontSize = fontStyle.fontSize

      // 字幕位置默认给的是0，实际上是最底部，手动设置为默认值
      if (position === 0) {
        playerControlAction.setSubtitlesPosition(
          genSubtitlesDefaultConfig().position[0],
          true,
        )
        playerControl_rct.subtitlesConfig.positionMode =
          Player_Subtitles_PositionMode.default
      } else {
        playerControl_rct.subtitlesConfig.position = [position]
        playerControl_rct.subtitlesConfig.positionMode =
          Player_Subtitles_PositionMode.custom
      }

      // 字体大小非可选列表的值，恢复默认值
      if (
        !dictGet(
          Player_Subtitles_FontSize_Config_List,
          'value',
          fontStyle.fontSize,
          'value',
        )
      ) {
        playerControlAction.setSubtitlesFontSize(
          genSubtitlesDefaultConfig().fontSize,
        )
      }
    }
    const onRatioPrepareEvent = async () => {
      const ratioList = m.getRatioList();
      aPlayerLog.log('ratioList', ratioList, m.getRatioSelectedId())
      playerControl_rct.ratioList = ratioList
      const currentRatioId = m.getRatioSelectedId()
      playerControl_rct.currentRatio =
        playerControl_rct.ratioList.find(
          (ratioItem) => ratioItem.mediaId === currentRatioId,
        ) || null

      if (playerControl_rct.switchingRatioId) {
        if (playerControl_rct.switchingRatioId === currentRatioId) {
          playerControlAction.showCommonNotice(`切换成功`, undefined)
        } else {
          playerControlAction.showCommonNotice(`切换失败`, undefined)
        }
        playerControl_rct.switchingRatioId = ''
      }
    }
    const onProgressSetToLatestEvent = async (nPos: number) => {
      const showLastPlayNotice = await config.getValue(
        'playerControl',
        'showLastPlayNotice',
        true as boolean,
      )
      playerControl_rct.showLastPlayNotice = showLastPlayNotice as boolean
    }

    const onPlayBufferEvent = (
      isBuffer: boolean,
      local: boolean,
      type: MediaType,
      speed: number, // B/S
    ) => {
      console.log('onPlayBufferEvent', isBuffer, local, type, speed)
      playerControl_rct.buffer = {
        loading: isBuffer,
        speed: speed
      }
    }

    const onAudioTrackPrepareEvent = async () => {
      aPlayerLog.log('media audio track prepare')
      const audioTrackList = await m.getAudioTrackList()
      playerControl_rct.audioTrackList = audioTrackList
      playerControl_rct.currentAudioTrackIdx = await m.getAudioTrackSelectedIndex()
      aPlayerLog.log('media audio track list', playerControl_rct.audioTrackList, playerControl_rct.currentAudioTrackIdx)
    }

    const onAudioTrackSelectEvent = (index: number) => {
      aPlayerLog.log('onAudioTrackSelectEvent', index)
      playerControl_rct.currentAudioTrackIdx = index
    }

    aPlayerLog.log('onMediaChangeEvent fire')

    // 解绑旧的 media 事件
    waitPlayerShowGetMediaInfo.reset()
    const oldMedia = aplayerMedia
    if (oldMedia) {
      oldMedia.detachProgressChangedEvent(mediaEventCookies.progressChangeCookie)
      oldMedia.detachPlayStateChangeEvent(mediaEventCookies.stateChangeCookie)
      oldMedia
        .getSubtitleManager()
        .detachPreparedEvent(mediaEventCookies.subtitlePreparedCookie)
      oldMedia
        .getSubtitleManager()
        .detachSelectChangeEvent(mediaEventCookies.subtitleSelectChangeCookie)
      oldMedia.detachRatioPreparedEvent(mediaEventCookies.ratioPrepareEvent)
      oldMedia.detachProgressSetToLatestEvent(mediaEventCookies.setToLatestCookie)
      oldMedia.detachPlayBufferEvent(mediaEventCookies.playBufferCookie)
      oldMedia.detachAudioTrackPreparedEvent(mediaEventCookies.audioTrackPrepareCookie)
      oldMedia.detachAudioTrackSelectChangeEvent(mediaEventCookies.audioTrackSelectCookie)
    }

    // 切换新 media, 相关数据同步
    aplayerAction.onMediaChange(m)

    // * 播放进度更新事件
    mediaEventCookies.progressChangeCookie = m.attachProgressChangedEvent(onProgressChangedEvent)
    // * 播放状态更新事件
    mediaEventCookies.stateChangeCookie = m.attachPlayStateChangeEvent(onPlayStateChangeEvent)
    // ? 播放状态更新事件 挂载慢了，则主动获取一次
    let state = m.getMediaState()
    // TODO
    // if (!playerControl_rct.isFirstMediaChangeAttachMap[m.id]) {
    //   console.log('my getMediaState', state)
    //   onPlayStateChangeEvent(state)
    // }
    // * 字幕准备完成事件
    mediaEventCookies.subtitlePreparedCookie = m.getSubtitleManager().attachPreparedEvent(onSubtitleManagerPreparedEvent)
    // * 字幕切换事件
    mediaEventCookies.subtitleSelectChangeCookie = m.getSubtitleManager().attachSelectChangeEvent(onSubtitleSelectChangeEvent)
    // * 清晰度初始化
    mediaEventCookies.ratioPrepareEvent = m.attachRatioPreparedEvent(onRatioPrepareEvent)
    // * 上次播放进度事件
    mediaEventCookies.setToLatestCookie = m.attachProgressSetToLatestEvent(onProgressSetToLatestEvent)
    // * buffer 事件
    mediaEventCookies.playBufferCookie = m.attachPlayBufferEvent(onPlayBufferEvent)
    // * 音轨初始化
    mediaEventCookies.audioTrackPrepareCookie = m.attachAudioTrackPreparedEvent(onAudioTrackPrepareEvent)
    // * 音轨切换
    mediaEventCookies.audioTrackSelectCookie = m.attachAudioTrackSelectChangeEvent(onAudioTrackSelectEvent)
  }
  const onMediaClosedEvent = (m: AplayerMedia) => {
    aPlayerLog.log('media closed')
    aplayerMedia = null
  }

  const onQuitPlayEvent = async () => {
    aPlayerLog.log('onQuitPlayEvent')
    AplayerStack.GetInstance().closePlayWindow();
  }

  // * media event
  AplayerStack.GetInstance().attachMediaChangeEvent(onMediaChangeEvent)
  AplayerStack.GetInstance().attachMediaClosedEvent(onMediaClosedEvent)
  AplayerStack.GetInstance().attachQuitPlayEvent(onQuitPlayEvent)

  // * playlist event
  AplayerStack.GetInstance()
    .getPlayList()
    .attachPlayListPreparedEvent(onPlayListPreparedEvent)
  AplayerStack.GetInstance()
    .getPlayList()
    .attachPlayListSelectChangeEvent(onPlayListSelectChangeEvent)


  const initialMedia = AplayerStack.GetInstance().getCurrPlayMedia()
  if (initialMedia) {
    onMediaChangeEvent(initialMedia)
  }

  //emit('inited', !!initialMedia?.id)

  return () => {
    // * media event
    AplayerStack.GetInstance().detachMediaChangeEvent(onMediaChangeEvent)
    AplayerStack.GetInstance().detachMediaCloseEvent(onMediaClosedEvent)
    AplayerStack.GetInstance().detachQuitPlayEvent(onQuitPlayEvent)
    // * playlist event
    // TODO
    // AplayerStack.GetInstance()
    //   .getPlayList()
    //   .detachPlayListPreparedEvent(onPlayListPreparedEvent)
    // AplayerStack.GetInstance()
    //   .getPlayList()
    //   .detachPlayListSelectChangeEvent(onPlayListSelectChangeEvent)
  }
}
let unbindAplayer: (() => void) | undefined
onMounted(async () => {
  unbindAplayer = await initAplayer()
})
onBeforeUnmount(() => {
  unbindAplayer?.()
})

const playerControlAction = {
  async getAplayerStack() {
    await waitAplayerStackLoad.waitDone()
    return AplayerStackModule
  },
  async setVideo(
    mediaAttr: MediaAttribute,
    openMediaFrom: TOpenMediaFrom,
    autoPlay?: boolean,
  ) {
    if (isAPlayer) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      AplayerStack?.GetInstance().openMedia(mediaAttr)
      if (autoPlay) {
        playerControlAction.play(false)
      }
    } else if ($videoRef.value) {
      videoInfo_rct.src = mediaAttr.playUrl
      playerControl_rct.currentPlayId = mediaAttr.gcid
      if (autoPlay) {
        await nextTick()
        playerControlAction.play(false)
      }
    }
  },
  async play(showAni: boolean): Promise<boolean> {
    if (playerControl_rct.mediaChangeLoading) return false
    if (showAni) {
      playerControl_rct.playStatusAni = MediaState.MsPlay
    }
    if (isAPlayer) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      AplayerStack?.GetInstance().playMedia()
    } else if ($videoRef.value) {
      if ($videoRef.value.paused) {
        await videoWaitCanPlay.waitDone()
        videoWaitPlay = new WaitSomeThing()
        $videoRef.value.play()
      }
    }
    return true
  },
  async pause(showAni: boolean): Promise<boolean> {
    if (playerControl_rct.mediaChangeLoading) return false
    if (showAni) {
      playerControl_rct.playStatusAni = MediaState.MsPause
    }
    if (isAPlayer) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      AplayerStack?.GetInstance().pauseMedia()
    } else if ($videoRef.value) {
      $videoRef.value.pause()
    }
    return true
  },
  async close() {
    if (isAPlayer) {
      if (statData_rct.waitClosePlayEndEvent) {
        statData_rct.waitClosePlayEndEvent.done()
        statData_rct.waitClosePlayEndEvent.reset()
      } else {
        statData_rct.waitClosePlayEndEvent = new WaitSomeThing()
      }
      const AplayerStack = await playerControlAction.getAplayerStack()
      AplayerStack?.GetInstance().closeMedia()
    } else if ($videoRef.value) {
      videoInfo_rct.src = ''
    }
  },
  replay() {
    playerControlAction.setCurrentTimeThrottle(0, true)
    playerControlAction.play(false)
  },
  setCurrentTimeThrottle: useThrottleFn(
    async (time: number, isUpdateProcessBar?: boolean) => {
      plusSetCurrentTimeCount()
      // 频繁执行时，未等 process 更新，就再次设置，则直接done上次的wait
      if (!playerControl_rct.waitSetCurrentTimeDone.isDone()) {
        playerControl_rct.waitSetCurrentTimeDone.done()
      }
      // reset wait
      playerControl_rct.waitSetCurrentTimeDone.reset()
      if (isAPlayer) {
        aPlayerLog.log('progressMoveTo', Math.floor(time * 1000))
        await aplayerMedia?.progressMoveTo(Math.floor(time * 1000))
        if (isUpdateProcessBar) {
          playerControl_rct.isIgnoreProgressChanged = true
          playerControl_rct.currentTime = time
          const percent = (time / playerControl_rct.duration) * 100
          playerControl_rct.percent = percent
        }
      } else if ($videoRef.value) {
        $videoRef.value.currentTime = Math.floor(time)
        if (isUpdateProcessBar) {
          playerControl_rct.currentTime = time
          const percent = (time / playerControl_rct.duration) * 100
          playerControl_rct.percent = percent
        }
      }
    },
    200, // todo 节流 时长
    true,
  ),
  async plusCurrentTime(plusSecond: number) {
    if (plusSecond === 0) return
    if (playerControl_rct.mediaChangeLoading) return

    const newTime = Math.max(
      0,
      Math.min(
        playerControl_rct.duration,
        playerControl_rct.currentTime + plusSecond,
      ),
    )
    const preSetCount = playerControl_rct.setCurrentTimeCount
    const preCurrentTime = playerControl_rct.currentTime
    await playerControlAction.setCurrentTimeThrottle(newTime, true)
    if (preSetCount !== playerControl_rct.setCurrentTimeCount) {
      // 真正有执行 setCurrentTime

      // ! 在mac上 pos 触发很慢, 所以注释掉, 前置提示
      // await playerControl_rct.waitSetCurrentTimeDone.waitDone()

      const timeStr = formatTime(playerControl_rct.currentTime)
      const percent = Math.floor(playerControl_rct.percent)
      if (plusSecond > 0) {
        playerControlAction.showCommonNotice(
          `快进: ${timeStr}`,
          'progress',
          'forward',
        )
      } else {
        playerControlAction.showCommonNotice(
          `快退: ${timeStr}`,
          'progress',
          'backward',
        )
      }
      return [preCurrentTime, playerControl_rct.currentTime]
    } else {
      return null
    }
  },
  // 切换 清晰度
  async changeRatio(ratioItem: RatioItem) {
    if (playerControl_rct.currentRatio?.mediaId === ratioItem.mediaId) return

    if (ratioItem.needMoreQuota) {
      // todo vip 不可播放业务处理，暂时只弹toast, 目前服务端配置限免
      if (ratioItem.vipType === 'vip.super') {
        if (!AccountHelper.getInstance().isSuperVip) {
          playerControlAction.showCommonNotice(`需要SuperVip`, undefined)
          return
        }
      } else {
        if (!AccountHelper.getInstance().isVip()) {
          playerControlAction.showCommonNotice(`需要vip`, undefined)
          return
        }
      }
    }
    statData_rct.endway = 'qualitychange'
    playerControlAction.showCommonNotice(
      `正在为您切换到 ${ratioItem.mediaName}，请稍等...`,
      'ratio',
      undefined,
      false,
    )

    playerControl_rct.switchingRatioId = ratioItem.mediaId

    let isSuccess = false
    if (isAPlayer) {
      if (aplayerMedia) {
        await aplayerMedia.switchRatio(ratioItem.mediaId)
        isSuccess = true
      }
    } else {
      if ($videoRef.value) {
        // const currentTime = $videoRef.value.currentTime
        // const isPlaying = !$videoRef.value.paused
        // const url =
        // if (url) {
        //  videoInfo_rct.src = url
        // }
        // $videoRef.value.currentTime = currentTime
        // if (isPlaying) {
        //   playerControlAction.play(false)
        // }
        // isSuccess = true

        await sleep(2000)
      }
    }
  },
  /**
   * 切换 倍速 播放
   * @param speed 播放速度, 1.0 为正常速度, 1.5 为 1.5 倍速度
   */
  async changeSpeed(speedItem: PlaySpeedDisplayInfo) {
    if (playerControl_rct.currentSpeed?.id === speedItem.id) return

    if (isAPlayer) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      await AplayerStack?.GetInstance().switchSpeed(speedItem.id)
      playerControlAction.showCommonNotice(
        `${speedItem.showValue}X倍速播放中`,
        'speed',
        'forward',
      )

      playerControl_rct.currentSpeed = speedItem
    } else if ($videoRef.value) {
      let rate = Number(speedItem.name.split('x')[0])
      if (Number.isNaN(rate)) {
        rate = 1
      }
      $videoRef.value.playbackRate = rate
      playerControlAction.showCommonNotice(
        `${speedItem.showValue}X倍速播放中`,
        'speed',
        'forward',
      )

      playerControl_rct.currentSpeed = speedItem
    }
  },
  /**
   *
   * @param volume
   * @param isUpdateVolumeInput
   * @returns [修改前音量， 修改后音量]
   */
  async setVolume(
    volume: number,
    isUpdateVolumeInput?: boolean,
  ): Promise<[number, number] | null> {
    const newVolume = ensureNum(volume)

    let currVolume: number | undefined = undefined
    let isSuccess = false
    if (isAPlayer) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      // 当前真实音量
      currVolume = ensureNum(await AplayerStack?.GetInstance().getVolume())
      await AplayerStack?.GetInstance().setVolume(newVolume)
      isSuccess = true
    } else if ($videoRef.value) {
      currVolume = Math.floor($videoRef.value.volume * 100)
      $videoRef.value.volume = newVolume / 100
      isSuccess = true
    }

    xmpPlayerLog.log('changeVolume', newVolume, currVolume)

    if (isSuccess) {
      if (newVolume > 0) {
        playerControlAction.showCommonNotice(`音量: ${newVolume}%`, 'volume')
      } else {
        playerControlAction.showCommonNotice(`已静音`, 'volume')
      }
      if (!isUndefined(currVolume)) {
        await playerControlStore.set('prevVolume', ensureNum(currVolume))
      }
      await playerControlStore.set('currVolume', ensureNum(newVolume))
      if (isUpdateVolumeInput) {
        playerControl_rct.volume = [newVolume]
      }
      return [currVolume!, newVolume]
    } else {
      // 切换音量失败, slider切回去
      playerControl_rct.volume = [(await playerControlStore.get('prevVolume') || 0)]
      return null
    }
  },
  /**
   * 切换静音
   */
  async toggleVolumeMute() {
    const currentVolume = ensureNum(playerControl_rct.volume[0])
    if (currentVolume) {
      // 当前有声音, 切到静音
      const res = await playerControlAction.setVolume(0, true)
      if (res) {
        return 'mute_on'
      }
    } else {
      // 当前静音, 恢复 prevVolume
      const prevVolume = await playerControlStore.get('prevVolume') || 80
      const res = await playerControlAction.setVolume(prevVolume, true)
      if (res) {
        return 'mute_off'
      }
    }
    return ''
  },
  async plusVolume(plusVolume: number) {
    const currentVolume = ensureNum(playerControl_rct.volume[0])
    const newVolume = Math.max(0, Math.min(100, currentVolume + plusVolume))
    return playerControlAction.setVolume(newVolume, true)
  },
  async toggleSubtitles() {
    if (playerControl_rct.currentSubtitles) {
      await playerControlAction.changeSubtitles(null)
    } else {
      if (playerControl_rct.prevSubtitles) {
        await playerControlAction.changeSubtitles(playerControl_rct.prevSubtitles)
      } else if (playerControl_rct.subtitlesList[0]) {
        await playerControlAction.changeSubtitles(playerControl_rct.subtitlesList[0])
      }
    }
  },
  async changeSubtitles(subtitleItem: SubtitleItemDisplayInfo | null, showToast = true) {
    playerControl_rct.prevSubtitles = playerControl_rct.currentSubtitles
    playerControl_rct.currentSubtitles = subtitleItem
    if (subtitleItem) {
      aplayerMedia?.getSubtitleManager().select(subtitleItem.id)
      aplayerMedia?.getSubtitleManager().setVisible(true)
      if (showToast) {
        playerControlAction.showCommonNotice('字幕切换成功', 'subtitles')
      }
    } else {
      aplayerMedia?.getSubtitleManager().setVisible(false)
      if (showToast) {
        playerControlAction.showCommonNotice('字幕已关闭', 'subtitles')
      }
    }
  },
  switchPrevPlayListItem: useThrottleFn(async () => {
    aPlayerLog.log('switchPrevPlayListItem')
    if (isAPlayer) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      if (AplayerStack) {
        playerControl_rct.playlistChanging = true
        statData_rct.endway = 'select_episode'
        const playListIns = AplayerStack.GetInstance().getPlayList()
        await playListIns.playPrev()
      }
    } else if ($videoRef.value) {
      const idx = playerControl_rct.playList.findIndex(
        (playItem) => playItem.id === playerControl_rct.currentPlayId,
      )
      if (idx > -1) {
        if (idx > 0) {
          playerControlAction.switchPlayListItem(
            playerControl_rct.playList[idx - 1],
          )
        } else {
          playerControlAction.switchPlayListItem(
            playerControl_rct.playList[playerControl_rct.playList.length - 1],
          )
        }
      }
    }
  },
    500,
    true,
  ),
  switchNextPlayListItem: useThrottleFn(async () => {
    aPlayerLog.log('switchNextPlayListItem')
    if (isAPlayer) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      if (AplayerStack) {
        playerControl_rct.playlistChanging = true
        statData_rct.endway = 'select_episode'
        const playListIns = AplayerStack.GetInstance().getPlayList()
        playListIns.playNext()
      }
    } else if ($videoRef.value) {
      const idx = playerControl_rct.playList.findIndex(
        (playItem) => playItem.id === playerControl_rct.currentPlayId,
      )
      if (idx > -1) {
        if (idx < playerControl_rct.playList.length - 1) {
          playerControlAction.switchPlayListItem(
            playerControl_rct.playList[idx + 1],
          )
        } else {
          playerControlAction.switchPlayListItem(playerControl_rct.playList[0])
        }
      }
    }
  },
    500,
    true,
  ),
  switchPlayListItem: useThrottleFn(async (playItem: PlayListItem) => {
    aPlayerLog.log('switchPlayListItem', playItem.name)
    if (isAPlayer) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      if (AplayerStack) {
        playerControl_rct.playlistChanging = true
        statData_rct.endway = 'select_episode'
        const playListIns = AplayerStack.GetInstance().getPlayList()
        playListIns.playItem(playItem.id)
        playerControl_rct.currentPlayId = playItem.id
      }
    } else if ($videoRef.value) {
      playerControl_rct.currentPlayId = playItem.id
      videoInfo_rct.src = playItem.url
      $videoRef.value.play()
    }
  },
    500,
    true,
  ),
  toggleFullScreen() {
    windowControlGlobalAction.toggleFullscreen()
  },
  async hideAllNotice() {
    playerControl_rct.commonNoticeHideTimer &&
      clearTimeout(playerControl_rct.commonNoticeHideTimer)

    const prevVisible = playerControl_rct.commonNoticeVisible
    playerControl_rct.commonNoticeVisible = false

    if (prevVisible) {
      await sleep(Player_UI_Const.Player_Ani_Fade_Second * 1000)
    }
  },
  /**
   * 展示左下角提示
   * ? 同一种module, 不隐藏当前notice, 直接改文案
   * ? 切换module, 先关闭当前notice, 再弹出新notice
   * @param msg
   * @param module
   * @param icon
   * @param isAutoHide
   */
  showCommonNotice: async (
    msg: string,
    module: TCommonNoticeModule | undefined,
    icon?: TCommonNoticeIcon,
    isAutoHide = true,
  ) => {
    debuggerLogger.log('showCommonNotice', msg, module, icon, module !== playerControl_rct.commonNoticeModule)
    if (module !== playerControl_rct.commonNoticeModule) {
      await playerControlAction.hideAllNotice()
    } else {
      playerControl_rct.commonNoticeHideTimer &&
        clearTimeout(playerControl_rct.commonNoticeHideTimer)
    }
    debuggerLogger.log('showCommonNotice 2')
    playerControl_rct.commonNoticeModule = module

    playerControl_rct.commonNotice = msg

    if (icon !== playerControl_rct.commonNoticeIcon) {
      if (icon) {
        Object.entries(commonNoticeIconAnimeObj).forEach(
          ([key, commonNoticeIconAnime]) => {
            if (key === icon) {
              commonNoticeIconAnime?.show()
              commonNoticeIconAnime?.play()
            } else {
              commonNoticeIconAnime?.hide()
              commonNoticeIconAnime?.stop()
            }
          },
        )
      } else {
        Object.values(commonNoticeIconAnimeObj).forEach(
          (commonNoticeIconAnime) => {
            commonNoticeIconAnime?.hide()
            commonNoticeIconAnime?.stop()
          },
        )
      }
      playerControl_rct.commonNoticeIcon = icon || ''
    }

    playerControl_rct.commonNoticeVisible = true

    if (isAutoHide) {
      playerControl_rct.commonNoticeHideTimer = setTimeout(() => {
        playerControl_rct.commonNoticeVisible = false
      }, Player_UI_Const.Player_CommonNotice_Hide_Ms)
    }
  },
  hideCommonNotice() {
    playerControl_rct.commonNoticeVisible = false
  },
  setSubtitlesTiming(second: number, showToast = true) {
    playerControl_rct.subtitlesConfig.timing = Math.max(
      -2000,
      Math.min(2000, second),
    )
    aplayerMedia
      ?.getSubtitleManager()
      .setTimming(playerControl_rct.subtitlesConfig.timing * 1000)
    if (showToast) {
      if (playerControl_rct.subtitlesConfig.timing) {
        playerControlAction.showCommonNotice(
          `当前字幕延迟${playerControl_rct.subtitlesConfig.timing}秒`,
          'subtitles',
        )
      } else {
        playerControlAction.showCommonNotice(
          `当前字幕提前${Math.abs(playerControl_rct.subtitlesConfig.timing)}秒`,
          'subtitles',
        )
      }
    }
  },
  setSubtitlesFontSize(fontSize: number) {
    console.log('setSubtitlesFontSize', fontSize)
    playerControl_rct.subtitlesConfig.fontSize = fontSize
    aplayerMedia
      ?.getSubtitleManager()
      .setFontSize(playerControl_rct.subtitlesConfig.fontSize)
  },
  setSubtitlesPositionMode(positionMode: Player_Subtitles_PositionMode) {
    playerControl_rct.subtitlesConfig.positionMode = positionMode
    switch (positionMode) {
      case Player_Subtitles_PositionMode.default: {
        playerControlAction.setSubtitlesPosition(
          genSubtitlesDefaultConfig().position[0],
          true,
        )
        break
      }
      case Player_Subtitles_PositionMode.custom: {
        break
      }
    }
  },
  setSubtitlesPosition(position: number, isUpdateInput?: boolean) {
    if (isUpdateInput) {
      playerControl_rct.subtitlesConfig.position = [position]
    }
    aplayerMedia?.getSubtitleManager().setPosition(position)
  },
  resetSubtitlesConfig() {
    const subtitlesConfig = genSubtitlesDefaultConfig()
    playerControlAction.setSubtitlesTiming(subtitlesConfig.timing, false)
    playerControlAction.setSubtitlesFontSize(subtitlesConfig.fontSize)
    playerControlAction.setSubtitlesPositionMode(subtitlesConfig.positionMode)
    playerControlAction.showCommonNotice(`已恢复字幕默认设置`, 'subtitles')
  },

  async getCurrentPlayListMediaInfo() {
    if (playerControl_rct.playList.length) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      if (AplayerStack) {
        const playListIns = AplayerStack.GetInstance().getPlayList()
        for (const playItem of playerControl_rct.playList) {
          if (!playerControl_rct.show) break
          // TODO 
          // const mediaInfo = playListIns.getItemMediaInfo(playItem.id)
          // if (mediaInfo.local && !mediaInfo.snapshot) {
          //   const { SnapshotManager } = require('@root/common/snapshot/manager')
          //   const snapshotRes =
          //     await SnapshotManager.getInstance().getLocalFileSnapshot(
          //       playItem.url,
          //     )
          //   if (snapshotRes.succ && snapshotRes.out) {
          //     mediaInfo.snapshot = snapshotRes.out
          //   }
          // }
          // playerControl_rct.playMediaInfoMap.set(playItem.id, mediaInfo)
        }
        aPlayerLog.log('playMediaInfoMap', playerControl_rct.playMediaInfoMap)
      }
    }
  },
  async getCurrentPlayListItemMediaInfo(playItem: PlayListItem) {
    if (!playerControl_rct.show) return
    const AplayerStack = await playerControlAction.getAplayerStack()
    if (AplayerStack) {
      const playListIns = AplayerStack.GetInstance().getPlayList()
      // TODO
      // const mediaInfo = await playListIns.getItemMediaInfo(playItem.id)
      // // debuggerLogger.log('getMediaInfo', playItem, mediaInfo)
      // if (mediaInfo.local && !mediaInfo.snapshot) {
      //   const { SnapshotManager } = require('@root/common/snapshot/manager')
      //   const snapshotRes =
      //     await SnapshotManager.getInstance().getLocalFileSnapshot(
      //       playItem.url,
      //     )
      //   if (snapshotRes.succ && snapshotRes.out) {
      //     mediaInfo.snapshot = snapshotRes.out
      //   }
      // }
      // playerControl_rct.playMediaInfoMap.set(playItem.id, mediaInfo)
    }
  },

  async resetPlayViewConfig() {
    const defaultConfig = genPlayViewDefaultConfig()

    playerControlAction.setImageRatio(defaultConfig.imageRatio)

    const AplayerStack = await playerControlAction.getAplayerStack()
    AplayerStack?.GetInstance().imageRotation(defaultConfig.rorate)
    playerControl_rct.playviewConfig.rorate = defaultConfig.rorate

    if (playerControl_rct.playviewConfig.flip_horizontal) {
      playerControlAction.setImageRotate('flip_horizontal')
    }
    if (playerControl_rct.playviewConfig.flip_vertical) {
      playerControlAction.setImageRotate('flip_vertical')
    }
  },

  setAudioTrackIdx(index: number) {
    aplayerMedia?.switchAudioTrack(index)
  },
  async setImageRatio(id: ImageRatioItem['id']) {
    if (isAPlayer) {
      playerControl_rct.playviewConfig.imageRatio = id
      const AplayerStack = await playerControlAction.getAplayerStack()
      AplayerStack?.GetInstance().imageRatio(id)
    }
  },
  async setImageRotate(playviewRotateAction: string) {
    if (isAPlayer) {
      const AplayerStack = await playerControlAction.getAplayerStack()
      switch (playviewRotateAction) {
        case 'turn_left': {
          playerControl_rct.playviewConfig.rorate = (playerControl_rct.playviewConfig.rorate - 90) % 360
          if (playerControl_rct.playviewConfig.rorate < 0) {
            playerControl_rct.playviewConfig.rorate += 360
          }
          AplayerStack?.GetInstance().imageRotation(playerControl_rct.playviewConfig.rorate)
          break
        }
        case 'turn_right': {
          playerControl_rct.playviewConfig.rorate = (playerControl_rct.playviewConfig.rorate + 90) % 360
          AplayerStack?.GetInstance().imageRotation(playerControl_rct.playviewConfig.rorate)
          break
        }
        case 'flip_horizontal': {
          AplayerStack?.GetInstance().imageFlip(true, playerControl_rct.playviewConfig.flip_horizontal)
          playerControl_rct.playviewConfig.flip_horizontal = !playerControl_rct.playviewConfig.flip_horizontal
          break
        }
        case 'flip_vertical': {
          AplayerStack?.GetInstance().imageFlip(false, playerControl_rct.playviewConfig.flip_vertical)
          playerControl_rct.playviewConfig.flip_vertical = !playerControl_rct.playviewConfig.flip_vertical
          break
        }
      }
    }
  }
}

// * ===== 进度条 ===== start
const progressBarCssVar_computed = computed(() => {
  return {
    '--progress-percent': playerControl_rct.percent + '%',
    '--progress-preload-percent': playerControl_rct.preloadPercent + '%',
    '--progress-hover-percent': playerControl_rct.hoverPercent + '%',
  }
})
onMounted(async () => {
  await videoWaitPlay?.waitDone()
  $processBar.value?.addEventListener(
    'pointerdown',
    handleProgressBarPointerDown,
  )
  $processBar.value?.addEventListener(
    'pointermove',
    handleProgressBarPointerMove,
  )
  $processBar.value?.addEventListener('pointerup', handleProgressBarPointerUp)
  $processBar.value?.addEventListener('mouseenter', handleProgressBarMouseEnter)
  $processBar.value?.addEventListener('mouseleave', handleProgressBarMouseLeave)
  $processBar.value?.addEventListener('mousemove', handleProgressBarMouseMove)
})
onBeforeUnmount(() => {
  $processBar.value?.removeEventListener(
    'pointerdown',
    handleProgressBarPointerDown,
  )
  $processBar.value?.removeEventListener(
    'pointermove',
    handleProgressBarPointerMove,
  )
  $processBar.value?.removeEventListener(
    'pointerup',
    handleProgressBarPointerUp,
  )
  $processBar.value?.removeEventListener(
    'mouseenter',
    handleProgressBarMouseEnter,
  )
  $processBar.value?.removeEventListener(
    'mouseleave',
    handleProgressBarMouseLeave,
  )
  $processBar.value?.removeEventListener(
    'mousemove',
    handleProgressBarMouseMove,
  )
})
async function updateProgressBarByDocumentEvent(
  e: MouseEvent,
  eventType: 'mousemove' | 'mousedownAndMove',
  isSetPlayerCurrentTime: boolean,
) {
  if (!$processBar.value) return

  const rect = $processBar.value.getBoundingClientRect()

  const offsetX = Math.max(0, Math.min(rect.width, e.clientX - rect.left))
  const percent = offsetX / rect.width

  // 计算视频时间
  const duration = playerControl_rct.duration
  const currentTime = duration * percent

  // 点击进度条时, 主动更新进度条 dom
  if (eventType === 'mousemove') {
    playerControl_rct.hoverPercent = percent * 100
  } else if (eventType === 'mousedownAndMove') {
    playerControl_rct.currentTime = currentTime
    playerControl_rct.percent = percent * 100
    if (isSetPlayerCurrentTime) {
      // 设置视频时间
      await playerControlAction.setCurrentTimeThrottle(currentTime)
    }
  }
}
function handleProgressBarMouseEnter() {
  xmpPlayerLog.debug('handleProgressBarMouseEnter')
  playerControl_rct.isProcessBarMoving = true
}
function handleProgressBarMouseLeave() {
  xmpPlayerLog.debug('handleProgressBarMouseLeave')
  playerControl_rct.isProcessBarMoving = false
  // ? 长按在外部松开, PointerUp 先执行，后执行 MouseLeave
  if (!playerControl_rct.isProcessBarDraging) {
    playerControl_rct.hoverPercent = 0
    playerControl_rct.processBarDragPreCurrentTime = 0
  }
}
const handleProgressBarMouseMove = (e: MouseEvent) => {
  if (!playerControl_rct.isProcessBarMoving) return

  updateProgressBarByDocumentEvent(e, 'mousemove', false)
}

function handleProgressBarPointerDown(e: PointerEvent) {
  xmpPlayerLog.debug('handleProgressBarPointerDown', e.buttons, e.button)
  if (e.buttons !== 1) return
    ; (e.target as HTMLElement).setPointerCapture(e.pointerId)
  playerControl_rct.isProcessBarDraging = true
  playerControl_rct.processBarDragPreCurrentTime = playerControl_rct.currentTime

  updateProgressBarByDocumentEvent(e, 'mousedownAndMove', true)
}
const handleProgressBarPointerMove = (e: PointerEvent) => {
  if ((e.target as HTMLElement).hasPointerCapture(e.pointerId)) {
    updateProgressBarByDocumentEvent(e, 'mousedownAndMove', true)
  }
}
async function handleProgressBarPointerUp(e: PointerEvent) {
  const target = e.target as HTMLElement
  if (target.hasPointerCapture(e.pointerId)) {
    xmpPlayerLog.debug('handleProgressBarPointerUp')

    target.releasePointerCapture(e.pointerId)
    playerControl_rct.isProcessBarDraging = false
    const preCurrentTime = playerControl_rct.processBarDragPreCurrentTime

    // ? 松开鼠标，注释掉，按下时和move已经设置了，这里无需设置，设置了连续2次相同的底层播放库会有buffer的bug
    // await updateProgressBarByDocumentEvent(e, 'mousedownAndMove', true)

    playerControl_rct.waitSetCurrentTimeDone.waitDone().then(() => {
      const nextCurrentTime = playerControl_rct.currentTime
      statPlayerAction({
        action:
          nextCurrentTime > preCurrentTime ? 'fast_forward' : 'fast_rewind',
        control: 'mouse',
        before_adjust: preCurrentTime,
        after_adjust: nextCurrentTime,
      })
    })

    // ? 切换进度条后, 自动开始播放
    // playerControlAction.play(false)
  }
}

// * ===== 进度条 ===== end

// * ===== 快捷键 hotkeys ===== start

// const onKeydown = async (event: KeyboardEvent) => {

// }
// useEventListener('keydown', onKeydown)

// * ===== 快捷键 hotkeys ===== end

// * 监听 xmpPlayer 展示和隐藏, emit 事件
const emit = defineEmits<{
  (e: 'show'): void
  (e: 'hide'): void
  (e: 'inited', boolean): void // isInitialMedia
}>()
watch(
  () => playerControl_rct.show,
  () => {
    if (playerControl_rct.show) {
      emit('show')

      // player 去掉圆角
      if (DwmHelper.getInstance().getIsDwm()) {
        ;[4, 6, 8, 12, 16, 20].forEach((item) => {
          document.documentElement.style.setProperty('--radius-' + item, '0px');
        })
      }

      statPlayerShow()
    } else {
      emit('hide')
      // 退出player 恢复圆角
      if (DwmHelper.getInstance().getIsDwm()) {
        ;[4, 6, 8, 12, 16, 20].forEach((item) => {
          document.documentElement.style.setProperty('--radius-' + item, item + 'px');
        })
      }
    }
  },
)

// ===== 动画 =====
let subtitlesIconAnime: AnimationItem | undefined
const $btnSubtitlesOff = useTemplateRef<HTMLDivElement>('$btnSubtitlesOff')

onMounted(() => {
  subtitlesIconAnime = Lottie.loadAnimation({
    container: $btnSubtitlesOff.value!,
    loop: false,
    autoplay: true,
    animationData: require('@root/common/assets/lottie/subsoff.json'),
  })
})
onBeforeUnmount(() => {
  if (subtitlesIconAnime) {
    subtitlesIconAnime.destroy()
    subtitlesIconAnime = undefined
  }
})
watch(
  () => playerControl_rct.currentSubtitles,
  () => {
    if (playerControl_rct.currentSubtitles) {
      subtitlesIconAnime?.hide()
      subtitlesIconAnime?.stop()
    } else {
      subtitlesIconAnime?.play()
      subtitlesIconAnime?.show()
    }
  },
)

const commonNoticeIconAnimeObj = {
  forward: undefined as AnimationItem | undefined,
  backward: undefined as AnimationItem | undefined,
}
const $commonNoticeForwardIcon = useTemplateRef<HTMLDivElement>(
  '$commonNoticeForwardIcon',
)
const $commonNoticeBackwardIcon = useTemplateRef<HTMLDivElement>(
  '$commonNoticeBackwardIcon',
)
onMounted(() => {
  commonNoticeIconAnimeObj.forward = Lottie.loadAnimation({
    container: $commonNoticeForwardIcon.value!,
    loop: true,
    autoplay: true,
    animationData: require('@root/common/assets/lottie/ic-forward.json'),
  })
  commonNoticeIconAnimeObj.backward = Lottie.loadAnimation({
    container: $commonNoticeBackwardIcon.value!,
    loop: true,
    autoplay: true,
    animationData: require('@root/common/assets/lottie/ic-backward.json'),
  })
})
onBeforeUnmount(() => {
  if (commonNoticeIconAnimeObj.forward) {
    commonNoticeIconAnimeObj.forward.destroy()
    commonNoticeIconAnimeObj.forward = undefined
  }
  if (commonNoticeIconAnimeObj.backward) {
    commonNoticeIconAnimeObj.backward.destroy()
    commonNoticeIconAnimeObj.backward = undefined
  }
})

// * ===== 当前时间 ===== start
const { timestamp, pause, resume } = useTimestamp({
  interval: 1000,
  controls: true,
})
watch(
  () => playerControl_rct.showMenu,
  () => {
    if (playerControl_rct.showMenu) {
      resume()
    } else {
      pause()
    }
  },
)
// * ===== 当前时间 ===== end

// * ===== 隐藏鼠标 ===== start
// ? 隐藏菜单栏时，隐藏鼠标
watch(
  [
    // () => playerControl_rct.showMenu, // 菜单栏
    // () => contextMenuIns_ref.value, // 右键菜单
    // () => settingDialogIns_ref.value, // 设置Dialog
    // () => linkDialogIns_ref.value, // 打开链接Dialog
  ],
  async () => {

    // if (
    //   playerControl_rct.showMenu ||
    //   contextMenuIns_ref.value ||
    //   settingDialogIns_ref.value
    //   // linkDialogIns_ref.value
    // ) {
    //   document.body.style.cursor = 'auto'
    // } else {
    //   if (document.body.style.cursor === 'none') {
    //     document.body.style.cursor = 'auto'
    //     await sleep(20)
    //     if (playerControl_rct.show) {
    //       document.body.style.cursor = 'none'
    //     } else {
    //       document.body.style.cursor = 'auto'
    //     }
    //   } else {
    //     if (playerControl_rct.show) {
    //       document.body.style.cursor = 'none'
    //     } else {
    //       document.body.style.cursor = 'auto'
    //     }
    //   }
    // }
  },
)
onBeforeUnmount(() => {
  document.body.style.cursor = 'auto' // 恢复
})
watch(() => playerControl_rct.show, () => {
  if (!playerControl_rct.show) {
    document.body.style.cursor = 'auto' // 恢复
  }
})
// * ===== 隐藏鼠标 ===== end


// * ===== 埋点 stat ===== start
type TShowFrom = 'first_open' | 'mouse_hover' | 'pause' | 'select_episode' | ''
type TEndway =
  // 'pause' | 
  'stop' | 'return' | 'close' | 'select_episode' | 'qualitychange' | ''
function genDefaultStatData() {
  return {
    waitSetShowFrom: new WaitSomeThing(),
    // 时长, [[playTime1, pauseTime1], [playTime2, pauseTime2]]
    playDurationList: [] as Array<number[]>,
    // statPlayerAction 操作后 菜单曝光来源（首开/鼠标hover/暂停）, 菜单不展示时传空
    showfrom: 'first_open' as TShowFrom,
    // 字幕设置曝光时数据(修改前)
    prevSubtitlesConfig: null as TSubtitlesConfig | null,
    waitClosePlayEndEvent: null as WaitSomeThing | null,
    endway: '' as TEndway,
  }
}
const statData_rct = reactive(genDefaultStatData())
function resetStatData() {
  const defaultVal = genDefaultStatData()
  Object.keys(statData_rct).forEach((key) => {
    statData_rct[key] = defaultVal[key]
  })
}

let prevCloseCache: {
  playerControl: typeof playerControl_rct
  statData: typeof statData_rct
  aplayerInfo: typeof aplayerInfo_rct
} | null = null

function setShowFrom(showfrom: TShowFrom) {
  if (showfrom) {
    statData_rct.showfrom = showfrom
    statData_rct.waitSetShowFrom.done()
  } else {
    statData_rct.waitSetShowFrom.reset()
  }
}

const waitPlayerShowGetMediaInfo = new WaitSomeThing()
async function statPlayerShow(isAwaitStat = false) {

  waitPlayerShowGetMediaInfo.reset()

}
type TPlayerAction =
  | 'fast_forward' // 快进
  | 'fast_rewind' // 快退
  | 'previous_episode' // 上一集
  | 'next_episode' // 下一集
  | 'play' // 播放
  | 'pause' // 暂停
  | 'volume_up' // 音量增加
  | 'volume_down' // 音量减少
  | 'stop' // 停止
  | 'mute_on' // 开启静音
  | 'mute_off' // 关闭静音
  | 'speed' // 倍速
  | 'quality' // 清晰度
  | 'subtitle' // 字幕
  | 'select_episode' // 选集
  | 'full_screen' // 全屏
  | 'min' // 最小化
  | 'max' // 最大化
  | 'close' // 关闭
  | 'right_click' // 右键
  | 'topping' // 置顶
  | 'cancel_topping' // 取消置顶

// 需要等待 showfrom 等字段更新的，statPlayerAction 放在 (await?) myAction() 之后
/**
 *
 * @param param0
 * @param isWaitShowfrom 如果通过操作会触发 showfrom 更新（键盘暂停 showfrom 变成 pause），需要等待 showfrom 更新完再执行，传 true
 */
async function statPlayerAction(
  {
    action,
    control,
    ...args
  }: {
    action: TPlayerAction
    control: 'mouse' | 'keyboard'
    before_adjust?: number
    after_adjust?: number
  },
  isWaitShowfrom: boolean = false,
  isAwaitStat = false
) {
  if (isWaitShowfrom) {
    await statData_rct.waitSetShowFrom.waitDone() // 等待 watch setShowFrom 触发完成
  }
}

async function genStatMediaInfo(aplayerInfo = aplayerInfo_rct) {
  // todo 1.1 check 如果视频loading时间长，mediaInfo返回时间
  await waitPlayerShowGetMediaInfo.waitDone()

  const name = aplayerInfo.mediaInfo?.name || ''
  const type = aplayerInfo.mediaInfo?.type

  let resource_type = ''
  if (type === MediaType.MtNewXmpLocal) {
    resource_type = 'local_file'
  } else if (type === MediaType.MtPan) {
    resource_type = 'cloud_file'
  } else {
    aPlayerLog.log('aplayerInfo.mediaInfo', aplayerInfo.mediaInfo)
    if (aplayerInfo.mediaInfo?.attribute.task?.url) {
      const sourceUrl = aplayerInfo.mediaInfo.attribute.task.url
      if (
        sourceUrl.startsWith('magnet:') ||
        sourceUrl.startsWith('ed2k:') ||
        sourceUrl.startsWith('thunder')
      ) {
        resource_type = 'magnet'
      } else if (sourceUrl.startsWith('http:') || sourceUrl.startsWith('https:')) {
        resource_type = 'http'
      } else {
        resource_type = 'other'
      }
    }
  }

  return {
    resource_type,
    file_extension: getFileExtension(name),
    resource_name: name,
    url: (aplayerInfo.mediaInfo?.attribute.mediaType === MediaType.MtDownload ? aplayerInfo.mediaInfo?.attribute?.task?.url : aplayerInfo.mediaInfo?.attribute.playUrl) || '',
    gcid: aplayerInfo.mediaInfo?.attribute?.gcid || '',
  }
}


function startPlayDurationTime(statData = statData_rct) {
  pausePlayDurationTime(statData)
  statData.playDurationList.push([Date.now()])
}
function pausePlayDurationTime(statData = statData_rct) {
  // 给上一段做结束
  if (statData.playDurationList.length) {
    if (
      statData.playDurationList[statData.playDurationList.length - 1]
        .length === 1
    ) {
      statData.playDurationList[
        statData.playDurationList.length - 1
      ][1] = Date.now()
    }
  }
}

watch(
  () => playerControl_rct.playlistPopoverState.visible,
  () => {
    if (playerControl_rct.playlistPopoverState.visible) {
      closeSubtitlesPopover()
    }
  },
)

watch(
  () => playerControl_rct.subtitlesPopoverState.module,
  async () => {
    if (
      playerControl_rct.subtitlesPopoverState.visible &&
      playerControl_rct.subtitlesPopoverState.module === 'config'
    ) {
      statData_rct.prevSubtitlesConfig = cloneFnJSON(
        playerControl_rct.subtitlesConfig,
      )
    } else {
      statData_rct.prevSubtitlesConfig = null
    }
  },
)


client.registerFunctions({
  [MainToRenderer_Stat_Channel.playerStat]: async (ctx, eventKey: string, value: string) => {

    switch (eventKey) {
      case 'player_start': {
        break
      }
      case 'player_end': {
        // ! 注意这里面要 区分当前数据和上次数据
        let dataMap: typeof prevCloseCache
        console.log('xlstat dataMap 0', statData_rct.endway, prevCloseCache)
        if (statData_rct.endway && (new Array<TEndway>('select_episode', 'qualitychange')).includes(statData_rct.endway)) {
          // 取当前数据
          dataMap = {
            playerControl: playerControl_rct,
            statData: statData_rct,
            aplayerInfo: aplayerInfo_rct
          }
          console.log('xlstat dataMap 1', dataMap)
        } else {
          // 取上次数据 prevCloseCache
          dataMap = prevCloseCache
          console.log('xlstat dataMap 2', dataMap)
        }

        dataMap?.statData.waitClosePlayEndEvent?.done()
        break
      }
    }
  }
})
onBeforeUnmount(() => {
  client.unRegisterFunctions([MainToRenderer_Stat_Channel.playerStat])
})


// * ===== 埋点 ===== end

const handlePlayerClose = async () => {
  // statData_rct.endway = 'close'
  // // const settingData = await getSettingStoreData()
  // // if (settingData.general_closeWindowType === 'close') {
  // //   await playerControlAction.close()
  // //   windowControlGlobalAction.closeWindow()
  // // } else {
  // //   windowControlGlobalAction.hideWindow()
  // //   playerAction.hidePlayer()
  // // }
  // windowControlGlobalAction.hideWindow()
  // playerAction.hidePlayer()
  // // todo 埋点 window-all-closed 里执行
  // await pTimeout(
  //   Promise.all([
  //     statPlayerAction({
  //       action: 'close',
  //       control: 'mouse',
  //     }, undefined, true),
  //     statData_rct.waitClosePlayEndEvent?.waitDone()
  //   ]),
  //   {
  //     milliseconds: 200,
  //   },
  // ).catch(() => { })
  AplayerStack.GetInstance().closePlayWindow();
}

type TRightMenuClickId =
  // 主菜单
  | 'open_file' // 打开文件
  | 'open_folder' // 打开文件夹
  | 'open_link' // 打开链接
  | 'full_screen' // 全屏
  | 'cancel_full_screen' // 取消全屏
  | 'sound_track' // 音轨
  | 'setting' // 设置
  | 'screen_adjust' // 画面调节
  // 画面调节菜单
  | 'original' // 原始比例
  | 'cover_screen' // 铺满全屏
  | '16:9' // 16:9
  | '4:3' // 4:3
  | '21:9' // 21:9

const onContextMenu = async (x, y) => {

  statPlayerAction({
    action: 'right_click',
    control: 'mouse',
  })

  const dwmNameSet = new Set<string>()

  // 音轨菜单
  // const audioTrackMenuList: MenuConfig[] = playerControl_rct.audioTrackList.length ? [{
  //   type: 'separator'
  // },
  // {
  //   type: 'sub',
  //   label: '音轨',
  //   async onUpdateSubOpen(isOpen, id) {
  //     console.log('onUpdateSubOpen 音轨', isOpen, id)
  //     if (isOpen) {

  //       // ? 解决没有 data-highlighted 问题
  //       const el = document.getElementById(id)
  //       if (el) {
  //         DwmHelper.getInstance().addShow([{ name: 'player_contextmenu_' + id, obj: { value: el } }])
  //         dwmNameSet.add('player_contextmenu_' + id)

  //         sleep(50).then(() => {
  //           el.focus()
  //           useEventListener(el, 'mouseenter', (e) => {
  //             if (el) {
  //               windowControlGlobalAction.focusWindow()
  //               sleep(50).then(() => {
  //                 el.focus()
  //               })
  //             }
  //           })
  //         })
  //       }
  //     } else {
  //       DwmHelper.getInstance().delShow(['player_contextmenu_' + id])
  //       dwmNameSet.delete('player_contextmenu_' + id)
  //     }
  //   },
  //   children: playerControl_rct.audioTrackList.map((audioTrackName, idx) => {
  //     return {
  //       type: 'checkbox',
  //       checked: playerControl_rct.currentAudioTrackIdx === idx,
  //       label: audioTrackName,
  //       // label: `音轨 ${idx + 1}`,
  //       value: ensureString(idx),
  //       onClick(item) {
  //         playerControlAction.setAudioTrackIdx(idx)
  //       },
  //     }
  //   }),
  // }] : []

  // // 画面调节菜单
  // const imageRatioMenuList: MenuConfig[] = playerControl_rct.imageRatioItemList.length ? [{
  //   type: 'sub',
  //   label: '画面调节',
  //   async onUpdateSubOpen(isOpen, id) {
  //     console.log('onUpdateSubOpen 画面调节', isOpen, id)
  //     if (isOpen) {
  //       // ? 解决没有 data-highlighted 问题
  //       const el = document.getElementById(id)
  //       if (el) {
  //         DwmHelper.getInstance().addShow([{ name: 'player_contextmenu_' + id, obj: { value: el } }])
  //         dwmNameSet.add('player_contextmenu_' + id)

  //         sleep(50).then(() => {
  //           el.focus()
  //           useEventListener(el, 'mouseenter', (e) => {
  //             if (el) {
  //               windowControlGlobalAction.focusWindow()
  //               sleep(50).then(() => {
  //                 el.focus()
  //               })
  //             }
  //           })
  //         })
  //       }
  //     } else {
  //       DwmHelper.getInstance().delShow(['player_contextmenu_' + id])
  //       dwmNameSet.delete('player_contextmenu_' + id)
  //     }
  //   },
  //   children: playerControl_rct.imageRatioItemList.map((imageRatioItem, idx) => {
  //     return {
  //       type: 'checkbox',
  //       checked: playerControl_rct.playviewConfig.imageRatio === imageRatioItem.id,
  //       label: imageRatioItem.name,
  //       value: imageRatioItem.id + '',
  //       onClick(item) {
  //         playerControlAction.setImageRatio(imageRatioItem.id)
  //       },
  //     }
  //   }),
  // }] : []


  // const menuConfig: MenuConfig[] = [
  //   {
  //     type: 'sub',
  //     label: '打开',
  //     async onUpdateSubOpen(isOpen, id) {
  //       console.log('onUpdateSubOpen 打开', isOpen, id)
  //       if (isOpen) {

  //         // ? 解决没有 data-highlighted 问题
  //         const el = document.getElementById(id)
  //         console.log('onUpdateSubOpen 打开 el', el)
  //         if (el) {
  //           DwmHelper.getInstance().addShow([{ name: 'player_contextmenu_' + id, obj: { value: el } }])
  //           dwmNameSet.add('player_contextmenu_' + id)

  //           sleep(50).then(() => {
  //             el.focus()
  //             useEventListener(el, 'mouseenter', (e) => {
  //               if (el) {
  //                 windowControlGlobalAction.focusWindow()
  //                 sleep(50).then(() => {
  //                   el.focus()
  //                 })
  //               }
  //             })
  //           })
  //         }
  //       } else {
  //         DwmHelper.getInstance().delShow(['player_contextmenu_' + id])
  //         dwmNameSet.delete('player_contextmenu_' + id)
  //       }
  //     },
  //     children: [
  //       {
  //         type: 'item',
  //         label: '打开文件',
  //         onClick: async () => {
  //           await nextTick()

  //           const res = await client.callServerFunction('openElectronSelectFileDialog', {
  //             title: '选择文件',
  //             filters: [
  //               {
  //                 name: '媒体文件',
  //                 extensions: FileExtNS.getAllFileExt().map(ext => ext.replace('.', ''))
  //               }
  //             ],
  //             properties: ['openFile', 'multiSelections']
  //           })

  //           if (windowControlGlobal.value.isPin) {
  //             windowControlGlobalAction.togglePin()
  //             windowControlGlobalAction.togglePin()
  //           }

  //           console.log('打开本地文件', res)
  //           if (res && res.length) {
  //             const filePath = res[0]
  //             const fileName = path.basename(filePath);

  //             const AplayerStack = await playerControlAction.getAplayerStack()
  //             AplayerStack?.GetInstance().openMedia({
  //               name: fileName,
  //               gcid: '',
  //               playUrl: filePath,
  //               playFrom: '',
  //               zipPlay: 0,
  //               dlnaPlay: 0,
  //               mediaType: MediaType.MtNewXmpLocal
  //             }, 'rk_open_file')
  //           }


  //         }
  //       },
  //       {
  //         type: 'item',
  //         label: '打开文件夹',
  //         onClick: async () => {

  //           await nextTick()

  //           const res = await client.callServerFunction('openElectronSelectFileDialog', {
  //             title: '选择文件夹',
  //             properties: ['openDirectory']
  //           })

  //           console.log('打开文件夹', res)

  //           if (windowControlGlobal.value.isPin) {
  //             windowControlGlobalAction.togglePin()
  //             windowControlGlobalAction.togglePin()
  //           }

  //           if (res && res.length) {
  //             let filePath = ''
  //             let fileName = ''

  //             const dirPath = res[0]
  //             if (dirPath) {
  //               const dirItems = await new DirEntry(dirPath).getCurrentDirFiles()
  //               console.log('打开文件夹 dirItems', dirItems)
  //               if (dirItems.length) {
  //                 const fileItem = dirItems.find(dirItem => dirItem.fileType === DirItemType.File)
  //                 console.log('打开文件夹 fileItem', fileItem)
  //                 if (fileItem) {
  //                   filePath = fileItem.filePath
  //                   fileName = fileItem.fileName
  //                 } else {
  //                   // 文件夹内无音视频
  //                   XMPMessage({
  //                     message: '暂无可播内容',
  //                   })
  //                 }
  //               } else {
  //                 // 空目录
  //                 XMPMessage({
  //                   message: '暂无可播内容',
  //                 })
  //               }
  //             }

  //             console.log('打开文件夹 filePath', filePath)
  //             if (filePath) {
  //               const AplayerStack = await playerControlAction.getAplayerStack()
  //               AplayerStack?.GetInstance().openMedia({
  //                 name: fileName,
  //                 gcid: '',
  //                 playUrl: filePath,
  //                 playFrom: '',
  //                 zipPlay: 0,
  //                 dlnaPlay: 0,
  //                 mediaType: MediaType.MtNewXmpLocal
  //               }, 'rk_open_folder')
  //             }
  //           }

  //         }
  //       },
  //       {
  //         type: 'item',
  //         label: '打开链接',
  //         onClick: () => {
  //           // linkDialogIns_ref.value = xmpLinkDialog('', 'rk_open_link', 'player', {
  //           //   async onShow(el) {
  //           //     if (el) {
  //           //       DwmHelper.getInstance().addShow([{ name: 'xmpLinkDialog', obj: { value: el } }])
  //           //     }
  //           //   },
  //           //   async onClose(closeAniMs) {
  //           //     linkDialogIns_ref.value = undefined
  //           //     if (closeAniMs) {
  //           //       await sleep(closeAniMs)
  //           //     }
  //           //     DwmHelper.getInstance().delShow(['xmpLinkDialog'])
  //           //   },
  //           // })
  //         }
  //       },
  //     ],
  //   },
  //   {
  //     type: 'separator'
  //   },
  //   {
  //     type: 'item',
  //     label: windowControlGlobal.value.isFullScreen ? '退出全屏' : '全屏',
  //     onClick: () => {
  //       playerControlAction.toggleFullScreen()
  //     }
  //   },
  //   ...imageRatioMenuList,
  //   ...audioTrackMenuList,
  //   {
  //     type: 'separator'
  //   },
  //   {
  //     type: 'item',
  //     label: '设置',
  //     onClick: () => {
  //       settingDialogIns_ref.value = xmpSettingDialog({
  //         async onClose(closeAniMs) {
  //           settingDialogIns_ref.value = undefined
  //           if (closeAniMs) {
  //             await sleep(closeAniMs)
  //           }
  //           DwmHelper.getInstance().delShow(['xmpsetting']);
  //         },
  //         onVisible(el) {
  //           if (el) {
  //             DwmHelper.getInstance().addShow([{ name: 'xmpsetting', obj: { value: el } }])
  //           }
  //         },
  //       })
  //     },
  //   },
  // ]

  // contextMenuIns_ref.value = xmpContextMenu({
  //   config: menuConfig,
  //   position: {
  //     x,
  //     y,
  //   },
  //   contentStyle: {
  //     minWidth: '140px',
  //     maxWidth: '300px',
  //     background: 'var(--background-neutral-6)',
  //   },
  //   onClose() {
  //     DwmHelper.getInstance().delShow(['player_contextmenu'])
  //     DwmHelper.getInstance().delShow(Array.from(dwmNameSet))
  //     contextMenuIns_ref.value = undefined
  //   },
  //   onVisible(el) {
  //     if (el) {
  //       DwmHelper.getInstance().addShow([{ name: 'player_contextmenu', obj: { value: el } }])

  //       windowControlGlobalAction.focusWindow()
  //       sleep(50).then(() => {
  //         el.focus()
  //         useEventListener(el, 'mouseenter', (e) => {
  //           debuggerLogger.log('menu mouseenter')
  //           if (el) {
  //             windowControlGlobalAction.focusWindow()
  //             sleep(50).then(() => {
  //               el.focus()
  //             })
  //           }
  //         })
  //       })
  //     }
  //   },
  // })
}

// ===== v1.1 右键菜单 =====
useEventListener($xmpPlayerContainer, "contextmenu", async (e) => {
  e.preventDefault();

  // 是否触发
  const target = e.target as HTMLElement
  let isMatch = false
  const composedPath = getComposedPath(e)
  if (composedPath.includes($audioContainer.value!)) {
    isMatch = true
  }
  if (isAPlayer) {
    if (target.matches('.xmp-player')) {
      isMatch = true
    }
  } else {
    if (target.matches('.video-dom')) {
      isMatch = true
    }
  }
  if (!isMatch) return

  onContextMenu(e.clientX, e.clientY)
});

// ===== v1.1 右键菜单 ===== end

// ===== v1.1 画面调节 =====

// ===== v1.1 画面调节 ===== end

// * ===== win7 旧系统窗口兼容 ===== start

// win7 c++ 层事件透传
// window.api!.onMessageFromMain((data) => {
//   // debuggerLogger.log('收到主进程消息:', data.event, "|x|", data.mousex, "|y|", data.mousey, "|true|", playerContainerIsOutside_ref.value, playerControl_rct.showMenu)
//   // win7Logger.log('收到主进程消息:', data.event, data)
//   // 输出: "收到主进程消息: Hello from Main Process!"

//   switch (data.event) {
//     case 675: {
//       // win mouseleave
//       playerContainerIsOutside_ref.value = true
//       break
//     }
//     case 512: {
//       // win mousemove
//       x.value = data.mousex
//       y.value = data.mousey
//       playerContainerIsOutside_ref.value = false
//       break
//     }
//     case 513: {
//       // win mousedown
//       break
//     }
//     case 514: {
//       // win mouseup
//       break
//     }
//     case 1536: {
//       // win drag
//       break
//     }
//     case 516: {
//       // win 右键按下
//       break
//     }
//     case 517: {
//       // win 右键up
//       onContextMenu(data.mousex, data.mousey)
//       break
//     }
//     case 256: {
//       // win 键盘
//       const sysKeyDownList: number[] = ensureArray(data.sysKeyDownList)
//       // sysKeyDownList
//       onKeydown({
//         keyCode: data.keyDownId,
//         altKey: sysKeyDownList.includes(18),
//         ctrlKey: sysKeyDownList.includes(17),
//         metaKey: sysKeyDownList.includes(91),
//         shiftKey: sysKeyDownList.includes(16),
//         preventDefault: () => { },
//       } as any as KeyboardEvent)

//       break
//     }
//   }
// })

// win7 窗口显隐
watch([
  () => playerControl_rct.subtitlesPopoverState.visible,
  // 会触发高度变化
  () => playerControl_rct.subtitlesPopoverState.module,
  () => playerControl_rct.subtitlesConfig.positionMode,
], async () => {
  if (playerControl_rct.subtitlesPopoverState.visible) {
    await sleep(20);
    DwmHelper.getInstance().addShow([{ name: 'subtitle_popover', obj: subtitle_popover }]);
  } else {
    DwmHelper.getInstance().delShow(['subtitle_popover']);
  }
})
watch(() => playerControl_rct.playlistPopoverState.visible, async () => {
  if (playerControl_rct.playlistPopoverState.visible) {
    await sleep(20);
    DwmHelper.getInstance().addShow([{ name: 'playlist_popover', obj: playlist_popover }]);
  } else {
    DwmHelper.getInstance().delShow(['playlist_popover']);
  }
})

watch([
  () => playerControl_rct.commonNoticeVisible,
  // 内容变化，ui 宽度会变化
  () => playerControl_rct.commonNotice,
  () => playerControl_rct.commonNoticeIcon,
], async () => {
  if (playerControl_rct.commonNoticeVisible) {
    await sleep(20);
    DwmHelper.getInstance().addShow([{ name: 'commonNotice_container', obj: commonNotice_container }]);
  } else {
    DwmHelper.getInstance().delShow(['commonNotice_container']);
  }
})
// 菜单隐藏，commonNotice 位置变化
watch([
  () => playerControl_rct.showMenu,
], async () => {
  if (playerControl_rct.commonNoticeVisible) {
    DwmHelper.getInstance().delShow(['commonNotice_container']);
    await sleep(20);
    DwmHelper.getInstance().addShow([{ name: 'commonNotice_container', obj: commonNotice_container }]);
  }
})

watch([
  () => playerControl_rct.showLastPlayNotice,
], async () => {
  if (playerControl_rct.showLastPlayNotice) {
    await sleep(20);
    DwmHelper.getInstance().addShow([{ name: 'lastPlayNotice_container', obj: lastPlayNotice_container }]);
  } else {
    DwmHelper.getInstance().delShow(['lastPlayNotice_container']);
  }
})
// 菜单隐藏，showLastPlayNotice 位置变化
watch([
  () => playerControl_rct.showMenu,
], async () => {
  if (playerControl_rct.showLastPlayNotice) {
    DwmHelper.getInstance().delShow(['lastPlayNotice_container']);
    await sleep(20);
    DwmHelper.getInstance().addShow([{ name: 'lastPlayNotice_container', obj: lastPlayNotice_container }]);
  }
})


watch(() => isAudioNoCoverUiShow_computed.value, async () => {
  if (isAudioNoCoverUiShow_computed.value) {
    await sleep(20);
    DwmHelper.getInstance().addShow([{ name: 'player_audio_ui', obj: $audioContainer }]);
  } else {
    DwmHelper.getInstance().delShow(['player_audio_ui']);
  }
})
watch(() => isPlayErrorUiShow_computed.value, async () => {
  if (isPlayErrorUiShow_computed.value) {
    await sleep(20);
    DwmHelper.getInstance().addShow([{ name: 'player_error_ui', obj: $errorContainer }]);
  } else {
    DwmHelper.getInstance().delShow(['player_error_ui']);
  }
})
watch(() => playerControl_rct.mediaChangeLoading, async () => {
  if (playerControl_rct.mediaChangeLoading) {
    await sleep(20);
    DwmHelper.getInstance().addShow([{ name: 'player_loading_full', obj: $xmpPlayerContainer }]);
  } else {
    DwmHelper.getInstance().delShow(['player_loading_full']);
  }
})

async function updateMenuRect() {
  if (playerControl_rct.show) {
    win7Logger.log('updateMenuRect', playerControl_rct.showMenu)
    if (playerControl_rct.showMenu) {
      await nextTick()
      DwmHelper.getInstance().addShow([{ name: 'player_menu_top', obj: $playerHeader }, { name: 'player_menu_bottom', obj: $playerMenu }])

      if (playerControl_rct.subtitlesPopoverState.visible) {
        DwmHelper.getInstance().addShow([{ name: 'subtitle_popover', obj: subtitle_popover }]);
      }
      if (playerControl_rct.playlistPopoverState.visible) {
        DwmHelper.getInstance().addShow([{ name: 'playlist_popover', obj: playlist_popover }]);
      }
    } else {
      DwmHelper.getInstance().delShow(['player_menu_top', 'player_menu_bottom']);
      // 菜单隐藏时，连带隐藏的 popover, 自己实现的 popover 才有问题
      DwmHelper.getInstance().delShow(['subtitle_popover', 'playlist_popover']);
    }
  }
}

watch(
  () => playerControl_rct.showMenu,
  updateMenuRect,
)
watch(() => playerControl_rct.show, () => {
  if (playerControl_rct.show) {
    updateMenuRect()
    // TODO
    // AplayerStack.GetInstance().addFloatShowRect([{ name: 'player', x: 0, y: 0, w: 0, h: 0 }])
  } else {
    // AplayerStack.GetInstance().addFloatShowRect([{ name: 'player', x: 0, y: 0, w: document.body.clientWidth, h: document.body.clientHeight }])
  }
})
// * ===== 旧系统窗口兼容 ===== end

function plusSetCurrentTimeCount() {
  if (Number.isSafeInteger(playerControl_rct.setCurrentTimeCount + 1)) {
    playerControl_rct.setCurrentTimeCount += 1
  } else {
    playerControl_rct.setCurrentTimeCount = 0
  }
}

// ===== 事件监听 =====
client.registerFunctions({
  [MainToRenderer_Player_Channel.hidePlayer]: async (ctx) => {
    playerAction.hidePlayer()
  }
})
onBeforeUnmount(() => {
  client.unRegisterFunctions([MainToRenderer_Player_Channel.hidePlayer])
})
const isNoticeBottom_ref = ref(false)
watch(() => playerControl_rct.showMenu, async () => {
  if (playerControl_rct.showMenu) {
    // 菜单展示立即弹高
    isNoticeBottom_ref.value = false
  } else {
    // 菜单隐藏，等隐藏动画结束再收下去
    if (platform.isWindows) {
      await sleep(Player_UI_Const.Player_Ani_Fade_Menu_Second * 1000)
      if (!playerControl_rct.showMenu) {
        isNoticeBottom_ref.value = true
      }
    } else {
      isNoticeBottom_ref.value = true
    }
  }
})

// ===== 对外对象和api =====
defineExpose({
  playerControl: readonly(playerControl_rct),
  playerAction,
  playerControlAction,
})

client
</script>

<style scoped lang="scss">
@import './xmp-player-mixin.scss';

.fade-enter-active,
.fade-leave-active {
  transition: var(--Player_Ani_Fade_Second);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-player-menu-enter-active,
.fade-player-menu-leave-active {
  transition: var(--Player_Ani_Fade_Menu_Second);
}

.fade-player-menu-enter-from,
.fade-player-menu-leave-to {
  opacity: 0;
}

.fade-popover-enter-active,
.fade-popover-leave-active {
  transition: var(--Player_Ani_Fade_Popover_Second);
}

.fade-popover-enter-from,
.fade-popover-leave-to {
  opacity: 0;
}

.scaleIn-player-play-icon-enter-from {
  opacity: 1;
  scale: 0.5;
}

.scaleIn-player-play-icon-enter-to {
  opacity: 0;
  scale: 1;
}

.scaleIn-player-play-icon-enter-active {
  transition: 0.5s;
}

@keyframes diffusion {
  0% {
    scale: 1;
    opacity: 1;
  }

  100% {
    scale: 2;
    opacity: 0;
  }
}

.video-dom {
  width: 100%;
  height: 100%;
  background: black;
}

.player-container {
  width: 100%;
  height: 100%;
}

.xmp-player {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  &.is-dwm {
    background: #000;
  }

  &.is-audio {
    background: #000;
  }

  &.is-media-loading {
    background: #000;
  }

  &.is-mac {
    &:not(.is-fullscreen) {
      .player-header-logo-container {
        margin-left: 70px;
      }
    }

    .player-header-container {
      height: 40px;
    }

    .ic-logo {
      width: 20px;
      height: 20px;
    }
  }
}

.video-player {
  background: transparent;
}

.player-header-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 70px;
  overflow: hidden;

  padding: 0 18px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  background: linear-gradient(180deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%);
}

.player-header-offset {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.player-header-logo {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: var(--radius-6);

  &:hover {
    background: var(--font-white-5);
  }
}

.header-logo_text {
  margin-left: 4px;
}

.ic-logo {
  margin-left: 10px;
}

.player-header-text {
  @include absolute-center();

  width: 600px;

  @include Body-M();

  text-align: center;
  color: var(--font-white-100);

  @include truncate();
}

.player-header-option {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 2px;
}

.player-header-fullscreen-option {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 16px;

  @include Body-L();

  color: var(--font-white-70);
}

.player-header-fullscreen-container {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;

  .ic-header-fullscreen-off {
    color: var(--font-white-70);
  }

  &:hover {
    color: var(--font-white-100);
  }
}

.option-btn {
  cursor: pointer;
  padding: 6px;
  border-radius: var(--radius-6);

  &:hover {
    background: var(--font-white-5);
  }
}


.player-menu-container {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 104px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 100%);
}

$progress-anchor-size: 12px;
$progress-anchor-size-hover: 18px;
$progress-bar-height: 4px;
$progress-bar-height-hover: 6px;

.player-progress-bar-container {
  position: absolute;
  bottom: 82px;
  left: 20px;
  right: 20px;
  height: 26px;
  cursor: pointer;

  &:hover {
    .player-progress-bar {
      height: $progress-bar-height-hover;
    }

    .player-progress-anchor {
      width: $progress-anchor-size-hover;
      height: $progress-anchor-size-hover;
    }

    .player-progress-hover-anchor {
      display: block;
    }

    .player-progress-hover-tooltip {
      display: block;
    }
  }
}

.player-progress-bar {
  height: $progress-bar-height;
  background: var(--font-white-40);

  position: absolute;
  left: 0;
  width: 100%;
  top: 50%;
  transform: translateY(-50%);

  transition: height 0.15s;
}

.player-progress-inner {
  background: var(--primary-def);

  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
}

.player-progress-preload {
  background: var(--font-white-70, #FFFFFFB2);

  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
}

.player-progress-hover-anchor {
  display: none;
  position: absolute;
  width: 2px;
  background: var(--font-white-100);
  top: 0;
  height: 100%;
  transform: translateX(-50%);
}

.player-progress-anchor {
  height: $progress-anchor-size;
  width: $progress-anchor-size;
  border-radius: 50%;
  background: var(--primary-def);

  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: width 0.15s, height 0.15s;
}

.player-progress-hover-tooltip {
  display: none;
  padding: 6px 12px 6px 12px;
  border-radius: var(--radius-6);
  height: 30px;
  background: var(--background-neutral-6);

  position: absolute;

  z-index: var(--z-index-1);

  transform: translateX(-50%);

  @include Body-M();
  text-align: center;
  color: var(--font-white-100);
}

.player-controls {
  position: absolute;
  bottom: 30px;
  left: 20px;
  right: 20px;

  display: flex;
  align-items: center;
  justify-content: space-between;
}

.player-controls-play {
  display: flex;
  gap: 30px;
}

.player-controls-text {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);

  padding: 0 20px;

  @include Body-L();

  text-align: center;
  color: var(--font-white-100);
}

.player-controls-tool {
  display: flex;
  gap: 30px;
}

.controls-btn-container {
  display: flex;
}

.controls-btn {
  height: 30px;
  line-height: 30px;

  cursor: pointer;
  transition: transform 0.15s;

  &:not(.is-disabled):hover {
    transform: scale(1.33);
  }

  &.is-disabled {
    cursor: none;
    color: var(--font-white-30);
  }
}

.player-notice-container {
  position: absolute;
  bottom: 110px;
  left: 20px;
  right: 20px;

  &.menu-hide {
    bottom: 20px;
  }
}

.player-notice {
  position: absolute;
  left: 0;
  bottom: 0;

  padding: 10px 20px;
  border-radius: var(--radius-6);
  background: var(--background-neutral-6);
  height: 40px;
  overflow: hidden;
  color: var(--font-white-100);

  @include Body-L();

  display: inline-flex;
  align-items: center;
}

.player-notice-lastplay-replay {
  cursor: pointer;
  color: var(--secondary-blue-def, #3C9BFF);

  &:hover {
    color: var(--secondary-blue-hov, #307CCC);
  }
}

.player-notice-lastplay-nomore {
  cursor: pointer;
  color: var(--font-white-60, #FFFFFF99);
  margin-left: 20px;

  display: flex;
  align-items: center;

  &:hover {
    color: var(--font-white-100);
  }
}

.player-notice-lastplay-cancel {
  cursor: pointer;
  margin-left: 10px;

  color: var(--font-white-60, #FFFFFF99);

  &:hover {
    color: var(--font-white-100);
  }
}


.speed-popover {
  @include Controls-Popover();
  @include Controls-Popover-List();
}

.speed-item {
  @include Controls-Popover-Item();
}

.quality-popover {
  @include Controls-Popover();
  @include Controls-Popover-List();
}

.quality-item {
  @include Controls-Popover-Item();
}

.btn-subtitles-off-lottie {
  width: 30px;
  height: 30px;
}

.subtitles-container {
  position: relative;

  .popover-container {
    @include Controls-Popover-Fixed-Right();
    width: 380px;
  }

  .popover-interactive {
    height: 33px;
  }

  .popover-content {
    @include Controls-Popover();
    width: 100%;
    height: 100%;
  }
}

.is-dwm {
  .subtitles-container {
    .popover-interactive {
      height: 54px;
    }
  }

  .playlist-container {
    .popover-interactive {
      height: 54px;
    }

    .popover-content {
      height: calc(100% - 54px);
    }
  }
}


.subtitles-list-container {
  @include Controls-Popover-List();
}

.subtitles-header {
  @include Controls-Popover-Header();
}

.subtitles-title {
  @include Controls-Popover-Title();
}

.subtitles-item-container {
  @include Controls-Popover-List();

  max-height: 454px;
}

.subtitles-item {
  @include Controls-Popover-Item();

  display: block;
  line-height: 40px;

  @include truncate();
}

.subtitles-form {
  padding: 6px 0;

  display: flex;
  flex-direction: column;
  gap: 16px;
}

.playview-popover {
  @include Controls-Popover();
  width: 380px;
}

.playview-header {
  @include Controls-Popover-Header();
}

.playview-title {
  @include Controls-Popover-Title();
}

.playview-form {
  padding: 6px 0;

  display: flex;
  flex-direction: column;
  gap: 16px;
}

.playview-aspect-ratio-radio {
  @include Subtitle-Timing-Radio();
  // flex: 1;
  height: 40px;
}

.playview-rotate-btn {
  @include Subtitle-Timing-Btn();
  width: 80px;
  height: 78px;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;

  padding: 11px 0;
}

.playview-reset {
  @include Subtitle-Timing-Btn();

  flex: 1;
  height: 40px;

  color: var(--font-white-90);

  &:hover {
    color: var(--font-white-100);
  }
}

.ic-playview-reset {
  margin-right: 6px;
}

.form-item {}

.form-label {
  display: flex;
  align-items: center;
  justify-content: space-between;

  height: 30px;

  padding: 0 12px;

  @include Body-S();
  color: var(--font-white-50, rgba(255, 255, 255, 0.5));
}

.form-label-btn {
  color: var(--secondary-blue-def, rgba(60, 155, 255, 1));
  cursor: pointer;

  &:hover {
    color: var(--secondary-blue-hov, rgba(48, 124, 204, 1));
  }
}

.form-content {
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 6px;
  gap: 8px;
}


.ic-subtitles-config {
  box-sizing: content-box;
  padding: 6px;
  border-radius: var(--radius-6);
  cursor: pointer;

  color: var(--font-white-70);

  &:hover {
    &:not(.ic-subtitles-config-disabled) {
      color: var(--font-white-100);
      background: var(--font-white-5);
    }
  }

  &.ic-subtitles-config-disabled {
    cursor: not-allowed;
  }
}

// 字幕延迟
.subtitle-timing-dec,
.subtitle-timing-plus {
  @include Subtitle-Timing-Btn();

  width: 74px;
  height: 40px;
}

.subtitle-timing-input-container {
  width: 180px;
  height: 40px;
}

.subtitle-timing-input-btn {
  @include Subtitle-Timing-Btn();
  width: 100%;
  height: 100%;
}

.subtitle-timing-input {
  width: 100%;
  height: 100%;

  @include resetInput();

  @include Body-M();

  border-radius: var(--radius-6);
  background: var(--font-white-5, rgba(255, 255, 255, 0.05));
  border: 1px solid var(--secondary-blue-def, rgba(60, 155, 255, 1));
  color: var(--font-white-100);

  text-align: center;
}

// 字幕字体大小
.subtitle-font-size-radio {
  @include Subtitle-Timing-Radio();

  flex: 1;
  height: 40px;
}

.subtitle-position-radio {
  @include Subtitle-Timing-Radio();

  flex: 1;
  height: 40px;
}

.subtitle-position-custom {
  padding: 0 12px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  @include Body-M();
  color: var(--font-white-90);
}

.subtitle-reset {
  @include Subtitle-Timing-Btn();

  flex: 1;
  height: 40px;

  color: var(--font-white-90);

  &:hover {
    color: var(--font-white-100);
  }
}

.ic-subtitle-reset {
  margin-right: 6px;
}

.subtitle-position-slider {
  position: relative;
  display: flex;
  align-items: center;

  height: 18px;
  width: 264px;
}

.subtitle-position-slider-track {
  background: var(--font-white-30);
  position: relative;
  flex-grow: 1;
  height: 4px;
}

.subtitle-position-slider-range {
  position: absolute;
  background: var(--primary-def, rgba(4, 112, 233, 1));
  height: 100%;
}

.subtitle-position-slider-thumb {}

.subtitle-position-slider-thumb-inner {
  background: var(--primary-def, rgba(4, 112, 233, 1));
  border-radius: 50%;
  width: 12px;
  height: 12px;

  &:hover {
    scale: 1.33;
  }
}

.playlist-container {
  position: relative;

  .popover-container {
    @include Controls-Popover-Fixed-Right();
    width: 398px;
    top: 65px;
  }

  .popover-interactive {
    height: 33px;
  }

  .popover-content {
    @include Controls-Popover();
    width: 100%;
    height: calc(100% - 33px);
    padding: 12px 0;

    display: flex;
    flex-direction: column;
  }
}

.playlist-title {
  @include Controls-Popover-Title();
  color: var(--font-white-100);
  padding: 0 20px;

  flex-shrink: 0;
}

.playlist-item-container {
  // height: 453px; // 
  flex: 1;
}


.voice-popover {
  width: 42px;
  height: 188px;
  background: var(--background-neutral-6, #131416E5);
  border-radius: var(--radius-6);
  padding: 6px 0 12px;

  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.voice-popover-text {
  @include Body-S();
  color: var(--font-white-100);
}

.voice-slider {
  position: relative;
  display: flex;
  align-items: center;
}

.voice-slider[data-orientation="vertical"] {
  flex-direction: column;
  width: 20px;
  height: 144px;
}

.voice-slider-track {
  background: var(--font-white-30);
  position: relative;
  flex-grow: 1;
  width: 4px;
}

.voice-slider-range {
  position: absolute;
  background: var(--primary-def, rgba(4, 112, 233, 1));
  width: 100%;
  left: 0;
}

.voice-slider-thumb {}

.voice-slider-thumb-inner {
  background: var(--primary-def, rgba(4, 112, 233, 1));
  border-radius: 50%;
  width: 18px;
  height: 18px;

  &:hover {
    scale: 1.33;
  }
}

.ic-notice-forward,
.ic-notice-backward {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.player-status-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.player-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.player-loading-speed {
  @include Body-M();
  text-shadow: 0px 0px 4px 0px #000000CC;
  color: #FFFFFF;
  margin-top: 16px;
}

.player-error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.player-status-title {
  @include Heading-H4();
  color: var(--font-white-100);
}

.player-status-subtitle {
  @include Body-L();
  color: var(--font-white-70, #FFFFFFB2);
}

.ic-player-loading {
  width: 48px;
  height: 48px;
}

.player-audio-container {
  width: 702px;
  height: 669px;

  background: url('@root/common/assets/img/player/img-player-audio-bg.png') no-repeat center center / 100% auto;

  display: flex;
  align-items: center;
  justify-content: center;
}

.img-player-audio {
  width: 200px;
  height: 200px;
  animation: spin 2s linear infinite;

  &.is-pause {
    animation-play-state: paused;
  }

}
</style>