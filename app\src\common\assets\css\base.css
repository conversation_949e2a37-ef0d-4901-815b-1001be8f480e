*,
*::before,
*::after {
  box-sizing: border-box;
  -webkit-user-drag: none;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.draggable {
  -webkit-app-region: drag;
}

.none-draggable {
  -webkit-app-region: no-drag;
}

.flex-x {
  display: flex;
  align-items: center;
}

.flex-x-y-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.click-disable {
  /* ? 目前只做了个简单的cursor样式, 不可点击的逻辑在js中处理 */
  cursor: not-allowed;
}

.dev-hide {
  display: none;
}

/* PerfectScrollbar */
.ps {
  .ps__rail-y {
    background-color: transparent !important;
  }

  .ps__thumb-y {
    border-radius: var(--border-radius-S) !important;
    background-color: var(--font-font-2) !important;
  }
}

/* 避免显示出裂图 */
img[src=""],
img:not([src]) {
  opacity: 0;
}

/* inline-svg 组件的svg，设置颜色跟随 */
.inline-svg path[fill] {
  fill: currentColor;
}

.inline-svg path[stroke] {
  stroke: currentColor;
}

.hide {
  display: none;
}

.unvisible {
  visibility: hidden;
}

.opacity-0 {
  opacity: 0;
}

.link-btn {
  cursor: pointer;
  color: var(--primary-def);

  &:hover {
    color: var(--primary-hov);
  }
}