export namespace API_DECOMPRESS {
  interface ListDecompressRequest {
    /* 压缩文件内展开目录, 需要包含完整路径, 为空则为首层目录, 如打开首层test文件夹, 值为"test/" */
    path: string;
    /* 偏移量(2020.10.09 分页功能待定)  */
    page_token?: string;
    /* 返回列表长度(2020.10.09 分页功能待定, 默认返回所有) */
    limit?: number;
    /* 压缩文件id */
    file_id: string;
    /* 压缩文件gcid */
    gcid: string;
    /* 解压密码 */
    password: string;
    /* 压缩文件的文件空间 */
    file_space: string;
  }

  interface ListDecompressResponse {
    /** 查询状态, 详情见DecompressStatus */
    status: string;
    /** 解压标题(压缩包文件名) */
    title: string;
    /** 压缩包文件大小 */
    file_size: string;
    /** 压缩包gcid */
    gcid: string;
    /** 任务id, 当前压缩文件正在进行中的解压任务, status=RUNNING_TASK时返回 */
    task_id: string;
    /** 当前文件夹 */
    current_path: string;
    /**
     * 文件数组
     */
    files: API_DECOMPRESS.DecompressFile[];
  }

  interface DecompressRequest {
    /** 压缩文件gcid */
    gcid: string;
    /** 压缩文件file_id */
    file_id: string;
    /** 压缩文件的文件空间 */
    file_space: string;
    /** 解压密码, 无加密则为空 */
    password: string;
    /** 是否使用默认解压文件夹 */
    default_parent: boolean;
    /** 解压到的文件夹id, 若default_parent为true, 则可为空 */
    parent_id: string;
    /** 解压到的文件夹完整目录, 根目录则传空值 */
    parent_full_path: string[];
    /** 解压目录的文件空间 */
    parent_space: string;
    /* 选中需要解压文件; 数组为空则全部解压; 最大限制500条; */
    files: {
      /* 文件索引, 仅文件类型有值, 文件夹类型则为-1 */
      index: number;
      /* 文件类型, 文件: drive#file, 文件夹: drive#folder */
      kind: string;
      /* 文件的完整路径 */
      path: string;
    }[];
  }

  interface DecompressResponse {
    /** DecompressStatus */
    status: string;
    /** 解压任务id, 查任务进度用 */
    task_id: string;
    /** 解压子文件个数 */
    files_num: number;
    /** 解压权限不足，跳转引导链接 */
    redirect_link: string;
  }

  interface DownloadRequest {
    /* 压缩文件gcid */
    gcid: string;
    /* 压缩文件file_id */
    file_id: string;
    /* 压缩文件的文件空间 */
    file_space: string;
    /* 解压密码, 无加密则为空 */
    password: string;
    /* 是否使用默认解压文件夹 */
    default_parent: boolean;
    /* 解压到的文件夹id, 若default_parent为true, 则可为空 */
    parent_id: string;
    /* 解压到的文件夹完整目录, 根目录则传空值 */
    parent_full_path: string[];
    /* 解压目录的文件空间 */
    parent_space: string;
    /* 选中需要解压文件, 数组为空则全部解压, 最大限制500条 */
    files: {
      /* 文件索引, 仅文件类型有值, 文件夹类型则为-1 */
      index: number;
      /* 文件类型, 文件: drive#file, 文件夹: drive#folder */
      kind: string;
      /* 文件的完整路径 */
      path: string;
    }[];
  }

  interface DownloadResponse {
    /* DecompressStatus */
    status: string;
    /* 解压任务id, 查任务进度用 */
    task_id: string;
    /* 解压子文件个数 */
    files_num: number;
    /* 解压权限不足，跳转引导链接 */
    redirect_link: string;
  }

  interface DecompressProgressRequest{
    /** 解压任务id */
    task_id: string;
  }

  /**
   * 已完成任务, process = 100, phase = PHASE_TYPE_COMPLETE, 此时expires_in为一个很大的值, 意味着不需要再查询.
   */
  interface DecompressProgressResponse {
    /** 当前进度, %百分比 */
    progress: number;
    /** 任务状态, 见PhaseType对应关系 */
    phase: PhaseType;
    /** 下次查询间隔, 单位秒 */
    expires_in: number;
    /** 对应文件id, 用于任务完成后给到客户端下载 */
    file_id: string;
    /** 压缩包文件大小 */
    file_size: string;
    /** 压缩包gcid */
    gcid: string;
    /** 压缩包文件名/解压任务名称 */
    file_name: string;
    /** 任务总大小, 单位字节 */
    task_size: string;
    /** 已完成的任务大小, 单位字节 */
    task_size_completed: string;
    /** 任务类型: 0:解压任务,1:解压并下载任务 */
    task_type: string;
    /** 解压到的文件目录位置 */
    decompress_path: string[];
    /* 任务扩展信息（失败原因等等） */
    params: JSON;
  }

  interface DecompressFile {
    /** 文件类型, 文件: drive#file, 文件夹: drive#folder */
    kind: string;
    /** 文件索引, 解压缩/下载用, 仅文件类型有值, 文件夹类型则为-1 */
    index: number;
    /** 文件名 */
    filename: string;
    /** 文件大小 */
    filesize: number;
    /** 文件gcid, 中台解压过的子文件才有会值 */
    gcid: string;
    /** 文件MIME类型 */
    mime_type: string;
    /** 文件图标 */
    icon_link: string;
    /** 文件完整路径, 可用于解压/下载的请求参数 */
    path: string;
  }

  interface DecompressCheckResponse {
    errcode: number;
    file_id: string;
    file_size: string;
    gcid: string;
  }

  type PhaseType = 'PHASE_TYPE_UNKNOW' | 'PHASE_TYPE_PENDING' | 'PHASE_TYPE_RUNNING' | 'PHASE_TYPE_ERROR' | 'PHASE_TYPE_COMPLETE'
}
