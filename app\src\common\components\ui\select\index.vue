<script setup lang="ts">
import {
  ComboboxAnchor,
  ComboboxContent,
  ComboboxGroup,
  ComboboxInput,
  ComboboxItem,
  ComboboxRoot,
  ComboboxTrigger,
  ComboboxViewport,
} from 'reka-ui'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'


import './style.css'
import { HTMLAttributes, ref } from 'vue'

export interface ISelectItem {
  name: string
  value: string
  disable?: boolean
}

const props = withDefaults(defineProps<{
  modelValue: string
  options: ISelectItem[]
  placeholder?: string
  contentClass?: HTMLAttributes['class']
  contentItemClass?: HTMLAttributes['class']
  anchorClass?: HTMLAttributes['class']
  defaultValue?: string
  clearable?: boolean
  maxHeight?: number
  divider?: boolean
  disable?: boolean
}>(), {
  placeholder: '请选择',
  clearable: false,
  divider: false,
  disable: false
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'onSelect', value: string): void
  (e: 'onClear', item: ISelectItem): void
  (e: 'onClearAll'): void
}>()

const openContent = ref(false)

const handleUpdateModelValue = (value: string) => {
  emit('update:modelValue', value)
}

</script>

<template>
  <ComboboxRoot class="ComboboxRoot" :open="openContent" :ignore-filter="true" :default-value="defaultValue"
    :model-value="modelValue" @update:model-value="handleUpdateModelValue" :disabled="disable">
    <ComboboxAnchor :class="['ComboboxAnchor', anchorClass]">
      <ComboboxInput class="ComboboxInput" :placeholder="placeholder" @update:model-value="handleUpdateModelValue" />
      <div class="ComboboxDivider" v-if="divider"></div>
      <ComboboxTrigger @click="openContent = !openContent">
        <i class="xl-icon-general-direction-caret-down-s"></i>
      </ComboboxTrigger>
    </ComboboxAnchor>

    <ComboboxContent :class="['ComboboxContent', contentClass]" :side-offset="2" position="popper" :body-lock="true"
      :style="{ maxHeight: maxHeight ? `${maxHeight}px` : 'none' }" @interact-outside="openContent = false">
      <PerfectScrollbar :style="{ height: '100%', width: '100%' }" :options="{ wheelPropagation: true }">
        <ComboboxViewport class="ComboboxViewport">
          <ComboboxGroup>
            <ComboboxItem v-for="(option) in options" :key="option.value" :class="['ComboboxItem', contentItemClass]"
              :value="option.value" @select="emit('onSelect', option.value)">
              <span>{{ option.name }}</span>
              <span class="ComboboxClose" v-if="clearable" @click.stop="emit('onClear', option)">
                <i class="xl-icon-general-close-m"></i>
              </span>
            </ComboboxItem>
          </ComboboxGroup>
          <ComboboxGroup v-if="clearable">
            <div class="ComboboxClear" @click="emit('onClearAll')">
              <span>清空历史记录</span>
            </div>
          </ComboboxGroup>
        </ComboboxViewport>
      </PerfectScrollbar>
    </ComboboxContent>
  </ComboboxRoot>
</template>
