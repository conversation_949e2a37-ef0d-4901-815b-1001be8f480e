.xly-icon-type {
  display: inline-block;
  width: 32px;
  height: 32px;
  background: url(../img/icons/xly-icon-type.png) -85px -205px no-repeat;
  background-size: 210px auto;
  vertical-align: top;
  border-radius: 6px;

  &.is-middle {
    zoom: 0.8;
  }

  &.is-small-middle {
    zoom: 0.6;
  }

  &.is-small {
    zoom: 0.5333;
  }

  &.is-large {
    zoom: 1.5;
  }

  &.xly-type-apk {
    background-position: -5px -5px;
  }

  &.xly-type-bt-file {
    background-position: -45px -5px;
  }

  &.xly-type-bt-link {
    background-position: -5px -45px;
  }

  &.xly-type-doc {
    background-position: -45px -45px;
  }

  &.xly-type-group {
    background-position: -85px -5px;
  }

  &.xly-type-link {
    background-position: -85px -45px;
  }

  &.xly-type-magnetic {
    background-position: -5px -85px;
  }

  &.xly-type-music {
    background-position: -45px -85px;
  }

  &.xly-type-pdf {
    background-position: -85px -85px;
  }

  &.xly-type-pic {
    background-position: -125px -5px;
  }

  &.xly-type-ppt {
    background-position: -125px -45px;
  }

  &.xly-type-rar {
    background-position: -125px -85px;
  }

  &.xly-type-resource {
    background-position: -5px -125px;
  }

  &.xly-type-save {
    background-position: -45px -125px;
  }

  &.xly-type-txt {
    background-position: -85px -125px;
  }

  &.xly-type-unknown {
    background-position: -125px -125px;
  }

  &.xly-type-video,
  &.xly-type-mp4,
  &.xly-type-rmvb,
  &.xly-type-wmv,
  &.xly-type-mpg,
  &.xly-type-mkv,
  &.xly-type-mov,
  &.xly-type-rm,
  &.xly-type-avi,
  &.xly-type-flv {
    background-position: -165px -5px;
  }

  &.xly-type-word {
    background-position: -165px -45px;
  }

  &.xly-type-xls {
    background-position: -165px -85px;
  }

  &.xly-type-saveto {
    background-position: -45px -125px;
  }

  &.xly-type-resource {
    background-position: -5px -125px;
  }

  &.xly-type-gif {
    background-position: -165px -125px;
  }

  &.xly-type-ipa {
    background-position: -5px -165px;
  }

  &.xly-type-ipsw {
    background-position: -45px -165px;
  }

  &.xly-type-dll {
    background-position: -85px -165px;
  }

  &.xly-type-chm {
    background-position: -125px -165px;
  }

  &.xly-type-text {
    background-position: -165px -165px;
  }

  &.xly-type-install,
  &.xly-type-exe {
    background-position: -5px -205px;
  }

  &.xly-type-iso {
    background-position: -45px -205px;
  }

  &.xly-type-default {
    background-position: -85px -205px;
  }
  &.xly-type-epub {
    background-position: -125px -205px;
  }
  &.xly-type-mobi {
    background-position: -165px -205px;
  }
  &.xly-type-m3u8 {
    background-position: -5px -240px;
  }
}
