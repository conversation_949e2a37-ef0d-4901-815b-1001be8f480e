<template>
  <Dialog
    :open="visible"
    :showTitleIcon="true"
    @update:open="handleOpenChange"
    :title="dialogTitle"
    :show-trigger="false"
    :show-cancel="false"
    :show-actions="true"
    :show-close-button="true"
    @close="() => handleClose('dialog-close')"
    :prevent-default-close="true"
    class-prefix="pre-new-task pre-new-multi-task"
    :isCreateTask="true"
  >
    <div class="multi-task-content">
      <TaskList
        :task-data="taskData"
        :data-map="dataMap"
        :container-height="358"
        :show-selection-count="true"
        :auto-expand-all="props.options?.autoExpandAll || false"
        @checkedFileIndexes="handleCheckedFileIndexes"
        @retryMagnetTask="handleRetryMagnetTask"
      />
    </div>

    <template #actions>
      <div class="task-actions">
        <TaskLaterButton
          :task-data="taskData"
          :data-map="dataMap"
          :options-ext-data="optionsExtData || {}"
          :has-valid-tasks="hasValidTasks"
          :checked-file-indexes="checkedFileIndexes"
          variant="secondary"
          size="lg"
          scene="pre-new-multi-task"
          @success="handleLaterSuccess"
          @error="handleLaterError"
          @cancel="handleLaterCancel"
        >
          稍后
        </TaskLaterButton>

        <div class="right-actions">
          <PlayButton
            class="play-button"
            :checked-file-indexes="checkedFileIndexes"
            :data-map="dataMap || {}"
            :options-ext-data="optionsExtData || {}"
            :task-data="taskData"
            scene="pre-new-multi-task"
            size="lg"
          />

          <DownloadButton
            scene="pre-new-multi-task"
            :selected-path-type="selectedPathType"
            :disabled="isDownloadButtonDisabled"
            @submit="handleDownloadButtonSubmit"
          />
        </div>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import TaskList from '@root/modal-renderer/src/components/new-task/task-list/task-list.vue'
import DownloadButton from '@root/modal-renderer/src/components/new-task/download-button/download-button.vue'
import TaskLaterButton from '@root/modal-renderer/src/components/new-task/task-later-button/TaskLaterButton.vue'
import PlayButton from '@root/modal-renderer/src/components/new-task/play-button/PlayButton.vue'
import { PopUpNS } from '@root/common/pop-up'

import type {
  IUrlWithType,
  IUrlDataMap,
  TaskFileSelectionMap,
  DownloadEventParams,
  DownloadButtonSubmitParams,
  TaskExtDataMap,
} from '@root/modal-renderer/types/new-task.type'
import { DownloadPathType } from '@root/modal-renderer/types/new-task.type'

import * as PopUpTypes from '@root/common/pop-up/types'
import { usePositionMixinComponent } from '@root/modal-renderer/src/common/mixins'

// Provide/Inject composables
import {
  useDownloadHandler,
  useCloseWindowHandler,
} from '@root/modal-renderer/src/composables/useTaskDownload'

// 使用基类的逻辑
const { overridePosition, resizeToFitContent } = usePositionMixinComponent()

// Props 定义
interface Props {
  taskData: IUrlWithType[]
  dataMap?: IUrlDataMap
  visible?: boolean
  options?: any
  optionsExtData?: TaskExtDataMap
}

// Emits 定义
interface Emits {
  cancel: []
  'update:visible': [value: boolean]
}

const props = withDefaults(defineProps<Props>(), {
  taskData: () => [],
  dataMap: () => ({}),
  visible: true,
  options: () => ({}),
  optionsExtData: () => ({}),
})

const emit = defineEmits<Emits>()

// Provide/Inject 获取下载与关闭处理函数
const { handleDownload } = useDownloadHandler()
const { handleCloseWindow } = useCloseWindowHandler()

// 窗口尺寸与展示控制
const defaultPositionOptions = ref({
  autoSize: false,
  show: false,
  windowWidth: 680,
  windowHeight: 550,
  relatePos: PopUpTypes.RelatePosType.CenterParent,
  selector: '.dialog-content',
})

// 记录 TaskList 返回的选中索引（统一用新格式）
const checkedFileIndexes = ref<TaskFileSelectionMap>({})

// 计算属性
const hasValidTasks = computed(() => props.taskData.length > 0)

const dialogTitle = computed(() => props.options?.title || '新建任务')

// 将 options.selectedPathType 转换为 DownloadPathType
const selectedPathType = computed(() => {
  const pathType = props.options?.selectedPathType
  if (pathType === 'local') return DownloadPathType.Local
  if (pathType === 'cloud') return DownloadPathType.Cloud
  return undefined
})

// 统计所有选中文件数量
const selectedFileCount = computed(() => {
  let total = 0
  Object.values(checkedFileIndexes.value).forEach(selection => {
    if (selection?.fileIndexes && Array.isArray(selection.fileIndexes)) {
      total += selection.fileIndexes.length
    }
  })
  return total
})

const isDownloadButtonDisabled = computed(() => selectedFileCount.value <= 0)

// 事件处理
const handleOpenChange = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = (scene: string = 'default') => {
  try {
    handleCloseWindow(scene)
  } catch (error) {
    console.error('[MultiTask] 关闭窗口失败:', error)
    emit('cancel')
  }
}

const handleCheckedFileIndexes = (checkedFiles: TaskFileSelectionMap) => {
  checkedFileIndexes.value = checkedFiles || {}
}

const handleRetryMagnetTask = (_taskInfo: any) => {
  // 预留：重试磁力链逻辑（如有需要由上层注入具体实现）
}

const handleDownloadButtonSubmit = (params: DownloadButtonSubmitParams) => {
  const downloadParams: DownloadEventParams = {
    checkedFileIndexes: checkedFileIndexes.value,
    type: params.type === DownloadPathType.Local ? 'download' : 'cloud',
    path: params.path,
  }

  handleDownload(downloadParams)
  handleClose('download-submit')
}

const handleLaterSuccess = (_result: {
  success: boolean
  message: string
  savedCount?: number
  failedCount?: number
}) => {
  // 预留：埋点或后续逻辑
}

const handleLaterError = (error: string) => {
  console.error('[MultiTask] TaskLaterButton 失败:', error)
}

const handleLaterCancel = () => {
  handleClose('task_later_cancel')
}

function updateWindowSize() {
  overridePosition(defaultPositionOptions.value)
  resizeToFitContent()
}

function showCurrentWindow() {
  const currentWindow = PopUpNS.getCurrentWindow()
  if (currentWindow) {
    currentWindow.show()
  } else {
    console.error('🔍 [showCurrentWindow] 获取当前窗口失败')
  }
}

onMounted(() => {
  nextTick(() => {
    updateWindowSize()
    setTimeout(() => {
      showCurrentWindow()
    }, 100)
  })
})
</script>

<style lang="scss" scoped>
@import '@root/modal-renderer/src/components/new-task/pre-new-task/pre-new-task.scss';
.play-button {
  width: 154px;
  padding: 0;
}
</style>
