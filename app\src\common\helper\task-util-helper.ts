import * as DownloadKernel from '../task/base'
import { getFileExtension } from '../thunder-pan-manager/pan-sdk/utils/drive'
import { FileOperationHelper } from './file-operation-helper'

export const fileIconMap = {
  "folder": "folder",
  "folder-restore": "folder-restore",
  "folder-download": "folder-download",
  "folder-collection": "folder-collection",
  "folder-safe": "folder-safe",
  "folder-bt": "folder-bt",
  "folder-compress": "folder-compress",
  "folder-thunder-box": "folder-thunder-box",
  "folder-smb": "folder-smb",
  "folder-fluent": "folder-fluent",
  "folder-privilege": "folder-privilege",
  "video": "video",
  "m3u8": "m3u8",
  "img": "img",
  "bt": "bt",
  "link": "link",
  "zip": "zip",
  "music": "music",
  "subtitle": "subtitle",
  "html": "html",
  "code": "code",
  "gif": "gif",
  "text": "text",
  "document": "document",
  "pdf": "pdf",
  "word": "word",
  "ppt": "ppt",
  "excel": "excel",
  "keynote": "keynote",
  "epub": "epub",
  "mobi": "mobi",
  "chm": "chm",
  "apk": "apk",
  "exe": "exe",
  "dmg": "dmg",
  "iso": "iso",
  "ipa": "ipa",
  "ipsw": "ipsw",
  "dll": "dll",
  "pkg": "pkg",
  "unknown": "unknown",
  "default": "default"
}

// 图标相关的后缀及图标定义
const iconList: string[] =
  [
    fileIconMap.ppt, fileIconMap.word,
    fileIconMap.gif, fileIconMap.pdf,
    fileIconMap.iso, fileIconMap.exe, fileIconMap.dmg, fileIconMap.pkg, fileIconMap.apk, fileIconMap.ipa, fileIconMap.ipsw,
    fileIconMap.dll, fileIconMap.chm,
    fileIconMap.epub, fileIconMap.mobi,
  ]

const videoExts: string = `.xv;.xlmv;.3gp;.3gp2;.3gpp;.3gpp2;.3mm;.3p2;.60d;.787;.aaf;.aep;.aepx;.aet;.aetx;.ajp;.ale;.amv;.amx;.arf;
  .asf;.asx;.avb;.avd;.avi;.avp;.avs;.avs;.axm;.bdm;.bdmv;.bik;.bix;.bmk;.bnp;.box;.bs4;.bsf;.byu;.camproj;.camrec;.clpi;.cmmp;
  .cmmtpl;.cmproj;.cmrec;.cpi;.cst;.cvc;.d2v;.d3v;.dat;.dav;.dce;.dck;.ddat;.dif;.dir;.divx;.dlx; .dmb;.dmsm;.dmsm3d;.dmss;.dnc;.dpg;
  .dream;.dsy;.dv;.dv-avi;.dv4;.dvdmedia;.dvr-ms;.dvx;.dxr;.dzm;.dzp;.dzt;.edl;.evo;.eye;.f4p;.f4v;.fbr;.fbr;.fbz;.fcp;.flc;.flh;
  .fli;.flv;.flx;.gfp;.gl;.grasp;.gts;.gvi;.gvp;.hdmov;.hkm;.ifo;.imovieproj;.imovieproject;.iva;.ivf;.ivr;.ivs;.izz;.izzy;.jts;.jtv;
  .k3g;.lrec;.lsf;.lsx;.m15;.m1pg;.m1v;.m21;.m21;.m2a;.m2p;.m2t;.m2ts;.m2v;.m4e;.m4u;.m4v;.m75;.meta;.mgv;.mj2;.mjp;.mjpg;.mkv;.mmv;
  .mnv;.mod;.modd;.moff;.moi;.moov;.mov;.movie;.mp21;.mp2v;.mp4;.mp4v;.mpe;.mpeg;.mpeg4;.mpf;.mpg;.mpg2;.mpgindex;.mpl;.mpls;
  .mpsub;.mpv;.mpv2;.mqv;.msdvd;.msh;.mswmm;.mts;.mtv;.mvb;.mvc;.mvd;.mve;.mvp;.mvy;.mxf;.mys;.ncor;.nsv;.nuv;.nvc;.ogm;.ogv;.ogx;.osp;
  .par;.pds;.pgi;.piv;.pjs;.pmf;.pns;.ppj;.prel;.pro;.prproj;.prtl;.psh;.pssd;.pva;.pvr;.pxv;.qt;.qtch;.qtl;.qtm;.qtz;
  .r3d;.rcproject;.rdb;.rec;.rm;.rmd;.rmp;.rms;.rmvb;.roq;.rp;.rts;.rts;.rum;.rv;.sbk;.sbt;.scc;.scm;.scn;.screenflow;.sec;.seq;.sfd;
  .sfvidcap;.smk;.sml;.smv;.spl;.ssm;.stl;.str;.stx;.svi;.swf;.swi;.swt;.tda3mt;.tivo;.tix;.tod;.tp;.tp0;.tpd;
  .tpr;.trp;.ts;.tts;.tvs;.vc1;.vcpf;.vcr;.vcv;.vdo;.vdr;.veg;.vem;.vf;.vfw;.vfz;.vgz;.vid;.viewlet;.viv;.vivo;.vlab;.vob;.vp3;.vp6;.vp7;
  .vro;.vs4;.vse;.vsp;.w32;.wcp;.webm;.wlmp;.wm;.wmd;.wmmp;.wmv;.wmx;.wp3;.wpl;.wtv;.wvx;.xfl;.xvid;.yuv;.zm1;.zm2;.zm3;.zmv;.m3u8;.mpg;`
const softwareExts: string = '.exe;.com;.bat;.msi;.apk;.ipa;.iso;.mds;.bin;.img;.ipsw;'
const docExts: string = `.txt;.html;.htm;.shtml;.xhtml;.chm;.hlp;.inf;.rtf;.tex;.ltx;.doc;.docx;.wps;.ppt;.pptx;.odf;.pdf;.xls;.xlsx;.docm;.
  dot;.dotm;.dotx;.email;.rp;.pps;.et;.ett;.xlt;.dbf;.prn;.csv;.mht;.mhtml;.xml;.wpt;.dps;.dpt;.pot;.ppsx;.epub;.mobi;.lit;.wdl;.ceb;.abm;
  .pdg;.umb;.ibooks;`
const musicExts: string = `.aiff;.cue;.m3u;.au;.cdda;.raw;.wav;.flac;.tak;.mp3;.aac;.wma;.m4a;.mid;.mka;.mp2;.mpa;.mpc;.ape;.ofr;
  .ogg;.ra;.wv;.tta;.ac3;.dts;.nsf;.mod;.s3m;.xm;.it;.vst;`
const picExts: string = `.psd;.tga;.gif;.jpeg;.jpg;.jp2;.bmp;.ico;.pcx;.png;.pbm;.pgm;.ppm;.pnm;.pgf;.arw;.cr2;.crw;.dcr;.dng;.erf;.kdc;.mef;
  .mos;.mrw;.nef;.nrw;.orf;.pef;.ptx;.r3d;.raf;.raw;.rw2;.srf;.srw;.x3f;.ras;.tiff;.tif;.wbmp;.ilbm;.lbm;.iff;.ico;`
const zipExts: string = '.zip;.zipx;.rar;.7z;.isz;.cab;.arj;.ace;.alz;.uue;.tar;.gz; .gzip;.tgz;.tpz;.bzip2;.bz2;.bz;.tbz;.tbz2;.xz;.txz;' +
  '.lzh;.lha;.zt;.az; .xpi;.jar;.wim;.swm;.rpm;.xar;.deb;.dmg;.hfs;.cpio;.lzma;.lzma86;.split;.001;'
const btExts: string = '.torrent;'
const subtitleExt: string = '.idx;.smi;.sub;.psb;.ssa;.ass;.usf;.ssf;.srt;.sup'

// 任务组后缀相关
const groupVideoExts: string = '.3gp;.asf;.avi;.divx;.f4v;.flv;.mkv;.mov;.movie;.mp4;.mpeg;.mpeg4;.mpg;.mpg2;.rm;.rmvb;.rp;.swf;.tp;.tp0;.ts;.wmv'
const groupsoftwareExts: string = '.exe;.com;.bat;.msi'
const groupMusicExts: string = '.wav;.flac;.mp3;.aac;.wma;.m4a;.mid;.ape;.ogg;.ra;.mod'
const groupPicExts: string = `.psd;.tga;.gif;.jpeg;.jpg;.jp2;.bmp;.ico;.pcx;.pdf;.png;.pbm;.pgm;.ppm;.pnm;.pgf;.arw;.cr2;.crw;.dcr;.dng;.erf;.kdc;
  .mef;.mos;.mrw;.nef;.nrw;.orf;.pef;.ptx;.r3d;.raf;.raw;.rw2;.srf;.srw;.x3f;.ras;.tiff;.tif;.wbmp;.ilbm;.lbm;.iff;.ico`
const groupDocExts: string = '.txt;.html;.htm;.shtml;.xhtml;.chm;.hlp;.inf;.rtf;.tex;.ltx;.doc;.docx;.wps;.ppt;.pptx;.odf;.pdf;.xls;.xlsx;.docm;.dot;.dotm;.dotx;.email;.rp;.pps'
const groupZipExts: string = '.rar;.tar;.zip;.gzip;.cab;.uue;.arj;.bz2;.lzh;.jar;.ace;.iso;.7-zip;.7z'

// const playVideoExt: string = '.asf;.mpg;.rmvb;.rm;.wmv;.avi;.mp4;.mpeg;.mkv;.mov;.ts;.flv;.3gp;.m2ts;.mts;';

const executableExt: string = '.exe;.com;.bat;.scr;.lnk;.pif;.wsh;'

const isoExt: string = '.iso;'

const videoExt = 'mp4;mov;avi;mkv;wmv;flv;f4v;rmvb;rm;3gp;3g2;ogv;webm;m4v;asf;mpeg;mpg;m2v;ts;mts;qt;dat;vob;amv;divx;xvid;hevc;av1;gifv;yuv;jts;wtv;mod;tod;ifo;k3g;tp;trp;wm;rec;tivo;vcd;vro;y4m;ogm;wpl;dvr-ms;pvr;m3u8;3gp2;3gpp;3gpp2;m2ts;xlmv;asx;swf;mpga;m3u;'
const audioExt = 'mp3;wav;flac;aac;ogg;wma;m4a;amr;aiff;ape;au;ac3;dts;mka;oga;tta;wv;cda;mid;midi;rmi;kar;flp;it;mod;s3m;xm;tak;vqf;amb;eac3;m4b;m4p;mpa;opus;voc;vox;dff;dsf;ncm;ofs;mp2;mpc;'
const imageExt = 'jpg;jpeg;png;gif;bmp;tiff;tif;webp;ico;svg;psd;ai;eps;raw;nef;arw;cr2;dng;orf;sr2;heic;heif;jfif;jpe;jif;pjp;pjpeg;bpg;dds;exr;jng;ktx;mng;pbm;pcx;pgm;ppm;psb;ptg;pnm;ras;rgb;rgba;rle;sct;sun;tga;wal;wbmp;xpm;xwd;jp2;crw;dcr;erf;kdc;mef;mos;mrw;nef;rw2;srf;srw;x3f;'
const zipExt = '7z;zip;tar;jar;cbr;cbz;rpm;iso;xz;rar;'
const softwareExt = 'exe;msi;dmg;iso;deb;ipa;apk;crx;img;'
const documentExt = 'doc;docx;dot;dotx;docm;dotm;xls;xlsx;xlsm;xlt;xltx;xltm;ppt;pptx;pptm;pot;potx;potm;pps;ppsx;ppsm;sldx;sldm;csv;pdf;txt;md;rtf;wps;odt;ods;odp;odm;ots;mobi;epub;xmind;vsd;vsdx;mmap;ics;tpz;numbers;key;'
const btExt = 'torrent;'
export namespace TaskUtilHelper {
  export enum FileExtType {
    Unkown = 0,
    Video,
    Software,
    Doc,
    Music,
    Pic,
    Zip,
    Bt
  }

  export function getFileType (fileName: string) {
    let ext: string = FileOperationHelper.getSuffix(fileName)
    ext = ext.toLowerCase()+';'
    if (ext === '' || ext.length < 2) {
      return 'other'
    }
    if (videoExt.indexOf(ext) > -1) {
      return 'video'
    }
    if (audioExt.indexOf(ext) > -1) {
      return'audio'
    }
    if (imageExt.indexOf(ext) > -1) {
      return 'img'
    }
    if (zipExt.indexOf(ext) > -1) {
      return'zip'
    }
    if (softwareExt.indexOf(ext) > -1) {
      return'software'
    }
    if (documentExt.indexOf(ext) > -1) {
      return 'doc'
    }
    if (btExt.indexOf(ext) > -1) {
      return 'bt'
    }
    return 'other'
  }

  // 获取任务的图标类型
  export function getTaskIcon(fileName: string, taskType?: DownloadKernel.TaskType, classPrefix?: string): string {
    classPrefix = classPrefix ? classPrefix : 'file-type-'
    let icon: string = 'unknown'
    do {
      if (taskType === DownloadKernel.TaskType.Bt) {
        icon = fileIconMap['folder-bt']
        break
      } else if (taskType === DownloadKernel.TaskType.Group) {
        icon = fileIconMap.folder
        break
      }

      icon = fileIconMap.unknown
      let ext: string = FileOperationHelper.getSuffix(fileName)
      if (ext === '' || ext.length < 2) {
        break
      }
      ext = ext.toLowerCase()
      if (iconList.indexOf(ext) > -1) {
        if (ext === 'doc') {
          icon = fileIconMap.word
        } else {
          icon = ext
        }
        break
      }

      ext = '.' + ext + ';'
      // 视频默认 xlx-icon-type-video
      if (videoExts.indexOf(ext) > -1) {
        if (ext === '.m3u8;') {
          icon = fileIconMap.m3u8
        } else {
          icon = fileIconMap.video
        }
        break
      }

      // 安装程序类默认 xlx-icon-type-install
      if (softwareExts.indexOf(ext) > -1) {
        icon = fileIconMap.exe
        // exe、ipa、apk、ipsw、iso已经在上面过滤了; mds、bin、img 为iso图标
        const isoExtraExt: string[] = ['.mds;', '.bin;', '.img;']
        if (isoExtraExt.indexOf(ext) > -1) {
          icon = fileIconMap.iso
        }
        break
      }

      // 文档类默认为 xlx-icon-type-doc
      if (docExts.indexOf(ext) > -1) {
        icon = fileIconMap.document
        // htm, html, mht 为 html
        const webExtraExt: string[] = ['.htm;', '.html;', '.mht;']
        if (webExtraExt.indexOf(ext) > -1) {
          icon = fileIconMap.html
          break
        }
        // doc 已经过滤; docx 为 xlx-icon-type-word
        if (['.docx;', '.doc;'].includes(ext)) {
          icon = fileIconMap.word
          break
        }
        // xls 已经过滤; xlsx 为 xlx-icon-type-xls
        if (['.xlsx;', '.xls'].includes('ext')) {
          icon = fileIconMap.excel
          break
        }

        // epub + mobi 已经过滤

        // ppt 已经过滤; pptx 为 xlx-icon-type-ppt
        if (ext === '.pptx;') {
          icon = fileIconMap.ppt
          break
        }

        if (ext === '.txt;') {
          icon = fileIconMap.text
          break
        }

        // pdf 已经过滤;  chm 已经过滤
        break
      }

      // 音乐类的全部为 xlx-icon-type-music
      if (musicExts.indexOf(ext) > -1) {
        icon = fileIconMap.music
        break
      }

      // 图片类全部为 xlx-icon-type-pic
      if (picExts.indexOf(ext) > -1) {
        icon = fileIconMap.img
        break
      }

      // 压缩包全部为 xlx-icon-type-rar
      if (zipExts.indexOf(ext) > -1) {
        icon = fileIconMap.zip
        break
      }

      // BT和种子全部为 xlx-icon-type-bt-link
      if (btExts.indexOf(ext) > -1) {
        icon = fileIconMap.bt
        break
      }

      if ('magnetic' === ext) {
        icon = fileIconMap.link
        break
      }

      if (subtitleExt.indexOf(ext) > -1) {
        // 字幕
        icon = fileIconMap.subtitle
        break
      }
    } while (0)
    return `${classPrefix}${icon}`
  }

  // 获取文件后缀的类型
  export function getTaskFileType(fileName: string='', ext?: string): FileExtType {
    let ret: FileExtType = FileExtType.Unkown
    if (ext === undefined && fileName) {
      ext = getFileExtension(fileName)
    }
    if (ext !== null && ext !== undefined && ext.length >= 2) {
      ext = ext.toLowerCase()
      ext = ext + ';'
    }
    if (ext === undefined || ext === '' || ext.length < 3) {
      ret = FileExtType.Unkown
    } else if (videoExts.indexOf(ext) > -1) {
      ret = FileExtType.Video
    } else if (softwareExts.indexOf(ext) > -1) {
      ret = FileExtType.Software
    } else if (docExts.indexOf(ext) > -1) {
      ret = FileExtType.Doc
    } else if (musicExts.indexOf(ext) > -1) {
      ret = FileExtType.Music
    } else if (picExts.indexOf(ext) > -1) {
      ret = FileExtType.Pic
    } else if (zipExts.indexOf(ext) > -1) {
      ret = FileExtType.Zip
    } else if (btExts.indexOf(ext) > -1) {
      ret = FileExtType.Bt
    }
    // 分段压缩文件 .z01-0.z%d+
    if (ext) {
      if (ext.length > 1) {
        if (ext.slice(0, 2) === '.z') {
          if (/[0-9]+/.test(ext.substring(2))) {
            ret = FileExtType.Zip
          }
        }
      }
    }
    return ret
  }


  export function getGroupFileType(fileName: string=''): FileExtType {
    let ret: FileExtType = FileExtType.Unkown
    let ext: string = FileOperationHelper.getSuffix(fileName)
    if (ext !== null && ext !== undefined && ext.length >= 2) {
      ext = ext.toLowerCase()
    }
    if (ext === undefined || ext === '' || ext.length < 2) {
      ret = FileExtType.Unkown
    } else if (groupVideoExts.indexOf(ext) > -1) {
      ret = FileExtType.Video
    } else if (groupsoftwareExts.indexOf(ext) > -1) {
      ret = FileExtType.Software
    } else if (groupDocExts.indexOf(ext) > -1) {
      ret = FileExtType.Doc
    } else if (groupMusicExts.indexOf(ext) > -1) {
      ret = FileExtType.Music
    } else if (groupPicExts.indexOf(ext) > -1) {
      ret = FileExtType.Pic
    } else if (groupZipExts.indexOf(ext) > -1) {
      ret = FileExtType.Zip
    }

    // 分段压缩文件 .z01-0.z%d+
    if (ext.length > 1) {
      if (ext.slice(0, 2) === '.z') {
        if (/[0-9]+/.test(ext.substring(2))) {
          ret = FileExtType.Zip
        }
      }
    }
    return ret
  }

  export function formatFileSize(size: number): string {
    if (size === 0 || !size) return '0B'
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let index = 0
    let fileSize = size
    while (fileSize >= 1024 && index < units.length - 1) {
      fileSize /= 1024
      index++
    }
    return `${fileSize.toFixed(2)} ${units[index]}`
  }
}