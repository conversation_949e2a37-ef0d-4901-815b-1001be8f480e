#include "StdAfx.h"
#include "ThunderStartWnd.h"
#include "Resource.h"


UINT count = 0;
HWND g_hwnd = NULL;
CThunderStartWnd::CThunderStartWnd(void)
{
	GdiplusStartupInput gdiplusStartupInput;
	GdiplusStartup(&m_gdiplusToken, &gdiplusStartupInput, NULL);

}

VOID CALLBACK UpdateIndex(
	HWND hWnd, // handle of window for timer messages
	UINT uMsg, // WM_TIMER message
	UINT idEvent, // timer identifier
	DWORD dwTime // current system time
)
{
	UNREFERENCED_PARAMETER(hWnd);
	UNREFERENCED_PARAMETER(uMsg);
	UNREFERENCED_PARAMETER(idEvent);
	UNREFERENCED_PARAMETER(dwTime);
	count = (count + 1) % 56;
	::SendMessage(g_hwnd, WM_PAINT, 0, 0);
	return;
}


STDMETHODIMP CThunderStartWnd::Create(HWND hWndParent, _U_RECT rect, LPCTSTR szWindowName,
	DWORD dwStyle, DWORD dwExStyle,
	_U_MENUorID MenuOrID, LPVOID lpCreateParam)
{
	CWindowImpl<CThunderStartWnd, CWindow, CControlWinTraits>::Create(hWndParent, rect, szWindowName, dwStyle, dwExStyle, MenuOrID, lpCreateParam);
	HWND hWnd = NULL;
	GetHWND(&hWnd);
	::SetTimer(NULL, 0, 40, (TIMERPROC)UpdateIndex);
	return S_OK;
}

STDMETHODIMP CThunderStartWnd::GetHWND(HWND* phWnd)
{
	*phWnd = m_hWnd;
	g_hwnd = m_hWnd;
	return S_OK;
}

STDMETHODIMP CThunderStartWnd::Invalidate(BOOL bErase)
{
	if (m_hWnd)
		CWindowImpl<CThunderStartWnd, CWindow, CControlWinTraits>::Invalidate(bErase);
	return S_OK;
}

STDMETHODIMP CThunderStartWnd::InvalidateRect(CONST LPRECT lpRect, BOOL bErase)
{
	if (m_hWnd)
		CWindowImpl<CThunderStartWnd, CWindow, CControlWinTraits>::InvalidateRect(lpRect, bErase);
	return S_OK;
}

STDMETHODIMP CThunderStartWnd::DestroyWindow()
{

	GdiplusShutdown(m_gdiplusToken);
	if (m_hWnd)
		CWindowImpl<CThunderStartWnd, CWindow, CControlWinTraits>::DestroyWindow();
	return S_OK;
}


void CThunderStartWnd::OnDrawBkgnd(HDC hDC)
{
	UNREFERENCED_PARAMETER(hDC);
	ETW_LEVEL_INFORMATION(L"OnDrawBkgnd");

}

void CThunderStartWnd::OnDraw(HDC hDC)
{
	ETW_LEVEL_INFORMATION(L"OnDraw");

	Graphics graphics(hDC);

	IStream *s;
	Image* pImage = NULL;

	HRSRC hResource = ::FindResource(GetModuleHandle(NULL), MAKEINTRESOURCE(IDP_PNG_0 + count), L"PNG");
	DWORD dwResourceSize = ::SizeofResource(GetModuleHandle(NULL), hResource);
	const void* pResourceData = ::LockResource(LoadResource(GetModuleHandle(0), hResource));

	HGLOBAL hResourceBuffer = GlobalAlloc(GMEM_MOVEABLE, dwResourceSize);
	if (!hResourceBuffer)
	{
		GlobalFree(hResourceBuffer);
	}
	void* pResourceBuffer = GlobalLock(hResourceBuffer);
	if (!pResourceBuffer)
	{
		GlobalUnlock(hResourceBuffer);
		GlobalFree(hResourceBuffer);
	}
	CopyMemory(pResourceBuffer, pResourceData, dwResourceSize);
	if (CreateStreamOnHGlobal(pResourceBuffer, false, &s) != S_OK)
	{
		return;
	}
	else
	{
		pImage = Image::FromStream(s);
		UINT width = pImage->GetWidth();
		UINT height = pImage->GetHeight();
		graphics.DrawImage(pImage, RectF(180, 80, width, height));
	}
	s->Release();
	GlobalUnlock(hResourceBuffer);
	GlobalFree(hResourceBuffer);
	delete pImage;
	pImage = NULL;
}