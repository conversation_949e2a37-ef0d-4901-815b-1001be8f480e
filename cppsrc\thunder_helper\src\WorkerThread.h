#ifndef XL_61B710D2_2F24_44B0_B147_DEA06BC08EC0
#define XL_61B710D2_2F24_44B0_B147_DEA06BC08EC0

#include <xlcommon.h>

class WorkerThread {
public:
	WorkerThread();

	static WorkerThread* GetInstance() {
		static WorkerThread g_WorkerThread;
		return &g_WorkerThread;
	}

public:
	void Init(std::shared_ptr<xl::thread::ThreadAffinity> threadAffinity);
	xl::coroutine::CommonAwaitable<void> ResumeOnMainUIThread();
	xl::coroutine::CommonAwaitable<void> ResumeOnThreadPool();

private:
	std::shared_ptr<xl::multithread::ThreadPool> m_pool{ nullptr };
	std::shared_ptr<xl::thread::ThreadAffinity> m_pMainThreadAffinity{ nullptr };

};

#endif