import { CallApiProxy } from '@root/common/call-api';
import { EventEmitter } from 'events';
import * as BaseType from '../base';

export class AplayerSubTitleManager {
  private apiProxy: CallApiProxy;
  private id: string;
  private eventContainor: EventEmitter = new EventEmitter();
  private subtitlePreparedCookie: number = 0;
  private subtitleSelectChangeCookie: number = 0;
  private subtitleManualAddCookie: number = 0;

  constructor(apiProxy: CallApiProxy, id: string) {
    this.apiProxy = apiProxy;
    this.id = id;
  }

  public init() {
    this.subtitlePreparedCookie = this.apiProxy.AttachServerEvent('AplayerSubtitleManagerPrepared', (id: string) => {
      if (this.id !== id) {
        return;
      }

      this.eventContainor.emit('AplayerSubtitleManagerPrepared');
    });

    this.subtitleSelectChangeCookie = this.apiProxy.AttachServerEvent('AplayerSubtitleManagerSelectChange', (id: string, subtilteId: string, type: BaseType.SubtitleCategory, isAuto: boolean) => {
      if (this.id !== id) {
        return;
      }

      this.eventContainor.emit('AplayerSubtitleManagerSelectChange', subtilteId, type, isAuto);
    });

    this.subtitleManualAddCookie = this.apiProxy.AttachServerEvent('AplayerSubtitleManagerManualAdd', (id: string, subtileId: string) => {
      if (this.id !== id) {
        return;
      }

      this.eventContainor.emit('AplayerSubtitleManagerManualAdd', subtileId);
    });
  }

  public unInit() {
    this.apiProxy.DetachServerEvent('AplayerSubtitleManagerPrepared', this.subtitlePreparedCookie);
    this.subtitlePreparedCookie = 0;
    this.apiProxy.DetachServerEvent('AplayerSubtitleManagerSelectChange', this.subtitleSelectChangeCookie);
    this.subtitleSelectChangeCookie = 0;
    this.apiProxy.DetachServerEvent('AplayerSubtitleManagerManualAdd', this.subtitleManualAddCookie);
    this.subtitleManualAddCookie = 0;
  }

  public async setPosition(pos: number): Promise<void> {
    await this.apiProxy!.CallApi('AplayerSubtitleManagerSetPosition', this.id, pos);
  }
  public async getPosition(): Promise<number> {
    let info = await this.apiProxy!.CallApi('AplayerSubtitleManagerGetPosition', this.id);
    if (info.bSucc) {
      return info.result as number;
    }

    return 0;
  }
  public async setTimming(num: number): Promise<void> {
    await this.apiProxy!.CallApi('AplayerSubtitleManagerSetTimming', this.id, num);
  }
  public async getTimming(): Promise<number> {
    let info = await this.apiProxy!.CallApi('AplayerSubtitleManagerGetTimming', this.id);
    if (info.bSucc) {
      return info.result as number;
    }

    return 0;
  }
  public async setVisible(visible: boolean): Promise<void> {
    await this.apiProxy!.CallApi('AplayerSubtitleManagerSetVisible', this.id, visible);
  }
  public async getVisible(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerSubtitleManagerGetVisible', this.id);
    if (info.bSucc) {
      return info.result as boolean;
    }

    return false;
  }
  public async setFontSize(size: number): Promise<void> {
    await this.apiProxy!.CallApi('AplayerSubtitleManagerSetFontSize', this.id, size);
  }
  public async setFontColor(fontColor: string): Promise<void> {
    await this.apiProxy!.CallApi('AplayerSubtitleManagerSetFontColor', this.id, fontColor);
  }
  public async setFontFamily(fontFamily: string): Promise<void> {
    await this.apiProxy!.CallApi('AplayerSubtitleManagerSetFontFamily', this.id, fontFamily);
  }
  public async setFontStyle(fontStyle: BaseType.SubtitleFontStyle) {
    await this.apiProxy!.CallApi('AplayerSubtitleManagerSetFontStyle', this.id, fontStyle);
  }
  public async getFontStyle(): Promise<BaseType.SubtitleFontStyle> {
    let info = await this.apiProxy!.CallApi('AplayerSubtitleManagerGetFontStyle', this.id);
    if (info.bSucc) {
      return info.result as BaseType.SubtitleFontStyle;
    }

    return { fontSize: 12, fontColor: '', fontName: '' };
  }
  public async getList(category: BaseType.SubtitleCategory): Promise<BaseType.SubtitleItemDisplayInfo[]> {
    let info = await this.apiProxy!.CallApi('AplayerSubtitleManagerGetList', this.id, category);
    if (info.bSucc) {
      return info.result as BaseType.SubtitleItemDisplayInfo[];
    }

    return [];
  }
  public async getSubtitleCategoryItemCount(): Promise<number> {
    let info = await this.apiProxy!.CallApi('AplayerSubtitleManagerGetSubtitleCategoryItemCount', this.id);
    if (info.bSucc) {
      return info.result as number;
    }

    return 0;
  }
  public async getSubtitleById(id: string): Promise<BaseType.SubtitleItemDisplayInfo> {
    let info = await this.apiProxy!.CallApi('AplayerSubtitleManagerGetSubtitleById', this.id, id);
    if (info.bSucc) {
      return info.result as BaseType.SubtitleItemDisplayInfo;
    }

    return { id: '', displayName: '', filePath: '', gcid: '', category: BaseType.SubtitleCategory.Min };
  }

  public attachPreparedEvent(cb: () => void): void {
    this.eventContainor.on('AplayerSubtitleManagerPrepared', cb);
  }
  public detachPreparedEvent(cb: () => void): void {
    this.eventContainor.off('AplayerSubtitleManagerPrepared', cb);
  }
  public async select(id: string): Promise<void> {
    await this.apiProxy!.CallApi('AplayerSubtitleManagerSelect', this.id, id);
  }
  public async getSelect(): Promise<string> {
    let info = await this.apiProxy!.CallApi('AplayerSubtitleManagerGetSelect', this.id);
    if (info.bSucc) {
      return info.result as string;
    }

    return '';
  }
  public async getSelectSubtitleCategory(): Promise<BaseType.SubtitleCategory> {
    let info = await this.apiProxy!.CallApi('AplayerSubtitleManagerGetSelectSubtitleCategory', this.id);
    if (info.bSucc) {
      return info.result as BaseType.SubtitleCategory;
    }

    return BaseType.SubtitleCategory.Min;
  }
  public attachSelectChangeEvent(cb: (id: string, type: BaseType.SubtitleCategory, isAuto: boolean) => void): void {
    this.eventContainor.on('AplayerSubtitleManagerSelectChange', cb);
  }
  public detachSelectChangeEvent(cb: (id: string, type: BaseType.SubtitleCategory, isAuto: boolean) => void): void {
    this.eventContainor.off('AplayerSubtitleManagerSelectChange', cb);
  }
  public attachManualAdd(cb: (id: string) => void): void {
    this.eventContainor.on('AplayerSubtitleManagerManualAdd', cb);
  }
  public detachManualAdd(cb: (id: string) => void): void {
    this.eventContainor.off('AplayerSubtitleManagerManualAdd', cb);
  }
}