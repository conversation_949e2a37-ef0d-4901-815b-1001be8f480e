
import { platform } from '@root/common/env'
import { type MaybeRefOrGetter, type ShallowRef } from 'vue'
import { Logger } from '@root/common/logger'
import { useMouseDragAndClick } from './useMouseDragMoveAndClick'
import { AplayerStack } from '@root/common/player/impl/aplayer-stack'
import {WindowMessage} from '@root/common/window-define'
import requireNodeFile from '@root/common/require-node-file'
import { GetXxxNodePath } from '@root/common/xxx-node-path'
import path from 'path'
import { useEventListener } from '@vueuse/core'

const logger = new Logger({ tag: 'Drag_Win' })

type TMouseMoveAndClickProps = {
  isSetWindowDraggable?: boolean

  onClick?: (e?: MouseEvent) => void
  onDbClick?: (e?: MouseEvent) => void
  onMouseDown?: (e?: MouseEvent) => void
  onMouseUp?: (e?: MouseEvent) => void
  // onDrag?: (e?: MouseEvent) => void
  onMouseMove?: (e?: MouseEvent) => void
  onMouseLeave?: (e?: MouseEvent) => void
  onKeyDown?: (code: number) => void
  onMouseWheel?:(delta: number) =>void
}

export function useMouseDragAndClickViaMain(
  target: Readonly<ShallowRef<HTMLElement | null>>,
  props?: TMouseMoveAndClickProps,
  eventOptions?: MaybeRefOrGetter<boolean | AddEventListenerOptions>,
) {
  if (!platform.isWindows) {
    // ? mac 走原来逻辑
    useMouseDragAndClick(target, props, eventOptions)
    return
  }

  let thunderHelper = requireNodeFile(
    path.join(GetXxxNodePath(), 'thunder_helper.node')
  );

  let checkDbClickTimer: NodeJS.Timeout | undefined;
  let bLButtonDown: boolean = false;
  let bMouseMoveBetweenClick: boolean = false;
  let bDbClick: boolean = false;
  let oldCursorPosDrag: {x: number, y: number} = {x: 0, y: 0}
  let oldCursorPosMouseMove: {x: number, y: number} = {x: 0, y: 0}

  // function handleClickViaMain(data) {
  //   if (clickCount === 1) {
  //     isSingleClickTrigger = false
  //   }
  //   clickTimer && clearTimeout(clickTimer)

  //   switch (clickCount) {
  //     case 1: {
  //       clickTimer = setTimeout(() => {
  //         isSingleClickTrigger = true
  //         props?.onClick?.()
  //       }, 200)
  //       break
  //     }
  //     case 2: {
  //       // ? 鼠标双击全屏
  //       props?.onDbClick?.()
  //       if (isSingleClickTrigger) {
  //         props?.onClick?.()
  //       }
  //       break
  //     }
  //   }
  // }

  // const onMouseDownViaMain = (data) => {
  //   logger.log('mousedown', data.mousex, data.mousey)
  //   props?.onMouseDown?.()
  // }

  // let clearClickTimer: NodeJS.Timeout | undefined

  // const onMouseUpViaMain = (data) => {
  //   logger.log('mouseup', data.mousex, data.mousey, isClick)
  //   clearClickTimer && clearTimeout(clearClickTimer)

  //   clickCount++

  //   if (clickCount === 1) {
  //     isClick = true
  //   }

  //   props?.onMouseUp?.()
  //   if (isClick) {
  //     logger.log('handle click')
  //     handleClickViaMain(data)
  //     if (clickCount % 2 === 0) {
  //       isClick = false
  //     }
  //   } else {
  //     logger.log('handle drag', isClick)
  //     // props?.onDrag?.(data)
  //   }
  //   clearClickTimer = setTimeout(() => {
  //     // ? 清除点击状态
  //     logger.log('clear isClick')
  //     isClick = false
  //     clickCount = 0
  //   }, 200) // 200ms内算连续点击
  // }

  // // window.api!.onMessageFromMain((data) => {
  // //   switch (data.event) {
  // //     case 513: {
  // //       onMouseDownViaMain(data)
  // //       break
  // //     }
  // //     case 514: {
  // //       onMouseUpViaMain(data)
  // //       break
  // //     }
  // //   }
  // // })
   useEventListener(target,'mousemove', (e) => {
    props?.onMouseMove?.()
   });
   useEventListener(target,'mouseleave', (e) => {
    props?.onMouseLeave?.()
   });
   useEventListener('keydown', (e) => {
    props?.onKeyDown?.(e.keyCode);
   })
  AplayerStack.GetInstance().attachWndMessageEvent((msg: number, w: number, l: number) => {
    if (msg === WindowMessage.WM_LBUTTONDBLCLK) {
      if (checkDbClickTimer) {
        clearTimeout(checkDbClickTimer);
        checkDbClickTimer = undefined;
      }
      bDbClick = true;
      props?.onDbClick?.()
    } else if (msg === WindowMessage.WM_LBUTTONDOWN) {
      bLButtonDown = true;
      bMouseMoveBetweenClick = false;
      bDbClick = false;
      oldCursorPosDrag = thunderHelper.getCursorPos();
    } else if (msg === WindowMessage.WM_LBUTTONUP) {
      bLButtonDown = false;
      if (!bMouseMoveBetweenClick) {
        // 可能有双击行为
        checkDbClickTimer = setTimeout(() => {
          checkDbClickTimer = undefined;
          if (!bDbClick) {
            props?.onClick?.()
          }
        }, 200);
      }
      bMouseMoveBetweenClick = false;
    } else if (msg === WindowMessage.WM_MOUSEMOVE) {
      let currPos = thunderHelper.getCursorPos();
      if(bLButtonDown) {
        if (Math.abs(oldCursorPosDrag.x - currPos.x) >= 5 || Math.abs(oldCursorPosDrag.y - currPos.y) >= 5) {
          oldCursorPosDrag = currPos;
          bMouseMoveBetweenClick = true;
          // TODO move wnd
        }
      }
      if (oldCursorPosMouseMove.x !== currPos.x || oldCursorPosMouseMove.y !== currPos.y) {
        props?.onMouseMove?.(currPos as any)
      }
      oldCursorPosMouseMove = currPos
    } else if (msg === WindowMessage.WM_MOUSELEAVE) {
      let currPos = thunderHelper.getCursorPos();
      oldCursorPosMouseMove = currPos
      props?.onMouseLeave?.(currPos as any)
    } else if (msg === WindowMessage.WM_KEYDOWN) {
      props?.onKeyDown?.(w);
    } else if (msg === WindowMessage.WM_MOUSEWHEEL) {
      props?.onMouseWheel?.(w);
    }
  });
}
