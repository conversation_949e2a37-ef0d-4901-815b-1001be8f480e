#pragma once
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN             // 从 Windows 头文件中排除极少使用的内容
#endif
// Windows 头文件
#include <windows.h>
#include <winhttp.h>
#include <ShlObj.h>
#include <shellapi.h>
#include <Shlwapi.h>
#include <string>
#include <vector>
#include <node_api.h>
#include <node.h>
#include <v8.h>
#include <AddonOpt.h>
#include <peer_id/peer_id.h>
#include "./cmdline/command_line.h"
#include "./demide_code/dmidecode.h"
#include "./xmp_helper_common.h"
#include "asnywork/Asnywork.h"
#include <XmpAssociate/xmp-associate.h>
#include <dwmapi.h>
#include "./dpi_interface.h"
#include "../WorkerThread.h"

#pragma comment(lib, "PowrProf.lib")
#ifdef __cplusplus
extern "C"
{
#endif
#include <Powrprof.h>
#ifdef __cplusplus
}
#endif

char szTitle[1024];                  // 标题栏文本
char szWindowClass[] = "test_wnd_class";            // 主窗口类名
HWND g_playerWnd;
HWND g_floatWnd;
HWND g_parentWnd;
bool g_bEraseBkg = true;
std::map<std::string, std::vector<RECT>> g_playerWndShowRect;
BOOL g_enableDwm = TRUE;
double g_dpi = 1;

typedef LRESULT(__stdcall* WndProcCallback)(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam);
WndProcCallback oldFloatProc = nullptr;
WndProcCallback oldParentProc = nullptr;
LRESULT __stdcall ParentWndProcNew(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case 0x02E0:
        return 0;
    default:
        break;
    }
    if (oldParentProc) {
        return CallWindowProc(oldParentProc, hWnd, message, wParam, lParam);
    }
    return 0;
}
LRESULT __stdcall FloatWndProcNew(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case 0x02E0:
        return 0;
    case WM_SYSCOMMAND:
    {
        /*if (wParam == SC_MINIMIZE) {
            ::SendMessage(g_parentWnd, WM_SYSCOMMAND, SC_MINIMIZE, 0);
        }
        else if (wParam == SC_MAXIMIZE) {
            ::SendMessage(g_parentWnd, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
        }*/

        break;
    }
    case WM_WINDOWPOSCHANGING:
    {
        RECT rect;
        ::GetWindowRect(hWnd, &rect);
        ::SetWindowPos(g_parentWnd, 0, rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, SWP_NOZORDER | SWP_NOACTIVATE);
        ::SetWindowPos(g_playerWnd, 0, 0,0, rect.right - rect.left, rect.bottom - rect.top, SWP_NOZORDER | SWP_NOACTIVATE);
        ::SetWindowPos(g_parentWnd, g_floatWnd, 0, 0, 0, 0, SWP_NOZORDER | SWP_NOACTIVATE | SWP_NOMOVE | SWP_NOSIZE);
        break;
    }
    case WM_WINDOWPOSCHANGED:
    {
        RECT rect;
        ::GetWindowRect(hWnd, &rect);
        ::SetWindowPos(g_parentWnd, 0, rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, SWP_NOZORDER | SWP_NOACTIVATE);
        ::SetWindowPos(g_playerWnd, 0, 0, 0, rect.right - rect.left, rect.bottom - rect.top, SWP_NOZORDER | SWP_NOACTIVATE);
        ::SetWindowPos(g_parentWnd, g_floatWnd, 0, 0, 0, 0, SWP_NOZORDER | SWP_NOACTIVATE | SWP_NOMOVE | SWP_NOSIZE);
        break;
    }
    case WM_SIZE:
    case WM_MOVE:
    {
        if (wParam != SC_MINIMIZE && wParam != SC_MAXIMIZE) {
            RECT rect;
            ::GetWindowRect(hWnd, &rect);
            /*char sz[2048] = { 0 };
            sprintf(sz, "======left=%d, top=%d, right=%d, bottom=%d", rect.left, rect.top, rect.right, rect.bottom);
            ::OutputDebugString(sz);*/
            ::SetWindowPos(g_parentWnd, 0, rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, SWP_NOZORDER | SWP_NOACTIVATE);
            ::SetWindowPos(g_playerWnd, 0, 0, 0, rect.right - rect.left, rect.bottom - rect.top, SWP_NOZORDER | SWP_NOACTIVATE);
        }
        break;
    }
    case WM_EXITSIZEMOVE: {
        break;
    }
    default:
        break;
    }
    if (oldFloatProc) {
        return CallWindowProc(oldFloatProc, hWnd, message, wParam, lParam);
    }
    return 0;
}

LRESULT __stdcall PlayerWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
	switch (message)
	{
	case WM_ERASEBKGND:
	{
		if (!g_bEraseBkg) {
			return 0;
		}
		g_bEraseBkg = false;
        break;
	}
	case WM_RBUTTONUP:
	{
        break;
	}
	}
    return DefWindowProc(hWnd, message, wParam, lParam);
}

ATOM PlayerWndRegisterClass(HINSTANCE hInstance)
{
    WNDCLASSEXW wcex;

    wcex.cbSize = sizeof(WNDCLASSEXW);

    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = PlayerWndProc;
    wcex.cbClsExtra = 0;
    wcex.cbWndExtra = 0;
    wcex.hInstance = hInstance;
    wcex.hIcon = NULL;
    wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground = CreateSolidBrush(RGB(0, 0, 0));
    wcex.lpszMenuName = NULL;
    wcex.lpszClassName = L"player_wnd_class";
    wcex.hIconSm = NULL;

    return RegisterClassExW(&wcex);
}


LRESULT __stdcall SnapshotWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

ATOM SnapshotRegisterClass(HINSTANCE hInstance)
{
    WNDCLASSEXW wcex;

    wcex.cbSize = sizeof(WNDCLASSEXW);

    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = SnapshotWndProc;
    wcex.cbClsExtra = 0;
    wcex.cbWndExtra = 0;
    wcex.hInstance = hInstance;
    wcex.hIcon = NULL;
    wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground = nullptr;
    wcex.lpszMenuName = NULL;
    wcex.lpszClassName = L"snapshot_wnd_class";
    wcex.hIconSm = NULL;

    return RegisterClassExW(&wcex);
}

static napi_value CreatePlayerWnd(napi_env env, napi_callback_info info) {
    auto hInst = GetModuleHandle(NULL);
    //OH_LOG_Print(LOG_APP, LOG_INFO, 0xFF00, " plugin manager", "player control InitAddon");
    size_t argc = 2;
    napi_value argv[2] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    int64_t h1 = 0;
    AddonBaseOpt::ParseInt64(env, argv[0], h1);
    int64_t h2 = 0;
    AddonBaseOpt::ParseInt64(env, argv[1], h2);
    HWND hFloatWnd = (HWND)h1;
    HWND hFather = (HWND)h2;
    auto n = PlayerWndRegisterClass(hInst);

    g_parentWnd = hFather;
    g_floatWnd = hFloatWnd;

    if (DPIInterface::GetDPIAwareSupport()) {
        g_dpi = DPIInterface::GetMonitorDPIFactor(g_floatWnd);
    }
    else {
        g_dpi = DPIInterface::GetSysDPIFactor();
    }

    RECT rc = { 0 };
    GetWindowRect(hFather, &rc);

    auto nStyle = GetWindowLongPtr(hFather, GWL_STYLE);
    ::SetWindowLongPtr(hFather, GWL_STYLE, nStyle | WS_CLIPCHILDREN);

    //auto nFloatStyle = GetWindowLongPtr(hFloatWnd, GWL_STYLE);
    //::SetWindowLongPtr(hFloatWnd, GWL_STYLE, nFloatStyle | WS_CHILD);
    //::SetParent(hFloatWnd, hFather);

    ::SetWindowPos(hFloatWnd, 0, rc.left, rc.top, rc.right - rc.left, rc.bottom - rc.top, SWP_NOACTIVATE | SWP_NOZORDER);

    oldParentProc = (WndProcCallback)::SetWindowLongPtr((HWND)hFather, GWLP_WNDPROC, (LONG_PTR)ParentWndProcNew);

    auto proc = (WndProcCallback)::SetWindowLongPtr((HWND)hFloatWnd, GWLP_WNDPROC, (LONG_PTR)FloatWndProcNew);
    oldFloatProc = proc;

    //HWND hWnd = ::CreateWindow("player_wnd_class", szTitle, WS_CHILD,
    //    0, 0, rc.right - rc.left, rc.bottom - rc.top, hFather, nullptr, hInst, nullptr);

    //if (!hWnd)
    //{
    //    return FALSE;
    //}
    ///*HBRUSH hBrush = ::CreateSolidBrush(0);
    //::SetClassLongPtr(hWnd, GCLP_HBRBACKGROUND, (LONG)(LONG_PTR)hBrush);*/

    ///*DWORD dwStyle = ::GetWindowLong(hWnd, GWL_STYLE);
    //::SetWindowLongPtr(hWnd, GWL_STYLE, dwStyle & ~WS_CLIPSIBLINGS);*/
    ///*DWORD dwStyle = ::GetWindowLong(hWnd, GWL_STYLE);
    //DWORD dwNewStyle = (dwStyle & ~WS_POPUP) & ~WS_CAPTION & ~WS_MINIMIZEBOX & ~WS_MAXIMIZEBOX | WS_CHILD | WS_CLIPCHILDREN;
    //if (dwStyle != dwNewStyle)
    //{
    //    ::SetWindowLong(hWnd, GWL_STYLE, dwNewStyle);
    //}*/
    //::SetParent(hWnd, hFather);

    //g_playerWnd = hWnd;
    //::SetWindowPos(g_playerWnd, hFloatWnd, 0, 0, 0, 0, SWP_NOSIZE | SWP_NOMOVE);

    //ShowWindow(hWnd, SW_SHOW);
    //UpdateWindow(hWnd);

    //napi_value obj;
    //napi_create_int64(env, (int64_t)g_playerWnd, &obj);

    //return obj;
    return nullptr;
}

static napi_value BindWnd(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    int64_t h = 0;
    AddonBaseOpt::ParseInt64(env, argv[0], h);
    HWND hFather = (HWND)h;
    g_parentWnd = hFather;
    h = 0;
    AddonBaseOpt::ParseInt64(env, argv[1], h);
    HWND hFloatWnd = (HWND)h;
    g_floatWnd = hFloatWnd;
    h = 0;
    AddonBaseOpt::ParseInt64(env, argv[2], h);
    HWND hPlayerWnd = (HWND)h;
    g_playerWnd = hPlayerWnd;

    if (DPIInterface::GetDPIAwareSupport()) {
        g_dpi = DPIInterface::GetMonitorDPIFactor(hFloatWnd);
    }
    else {
        g_dpi = DPIInterface::GetSysDPIFactor();
    }

    RECT rc = { 0 };
    GetWindowRect(hFather, &rc);

    auto nStyle = GetWindowLongPtr(hFather, GWL_STYLE);
    ::SetWindowLongPtr(hFather, GWL_STYLE, nStyle | WS_CLIPCHILDREN);

    ::SetWindowPos(hFloatWnd, 0, rc.left, rc.top, rc.right - rc.left, rc.bottom - rc.top, SWP_NOACTIVATE | SWP_NOZORDER);

    oldParentProc = (WndProcCallback)::SetWindowLongPtr((HWND)hFather, GWLP_WNDPROC, (LONG_PTR)ParentWndProcNew);
    oldFloatProc = (WndProcCallback)::SetWindowLongPtr((HWND)hFloatWnd, GWLP_WNDPROC, (LONG_PTR)FloatWndProcNew);

    return nullptr;
}

static napi_value CreateSnapshotWnd(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value argv[2] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    auto hInst = GetModuleHandle(NULL);
    SnapshotRegisterClass(hInst);

    HWND hWnd = ::CreateWindowEx( WS_EX_TOOLWINDOW,"snapshot_wnd_class", szTitle, WS_POPUP,
        -100000, -100000, 10000, 10000, GetDesktopWindow(), nullptr, hInst, nullptr);

    if (!hWnd)
    {
        return FALSE;
    }

    ShowWindow(hWnd, SW_SHOW);
    UpdateWindow(hWnd);

    napi_value obj;
    napi_create_int64(env, (int64_t)hWnd, &obj);
    return obj;
}


static napi_value SetPlayerWndVisible(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    bool b = false;
    AddonBaseOpt::ParseBool(env, argv[0], b);
    ::ShowWindow(g_playerWnd, b ? SW_SHOW : SW_HIDE);

    return nullptr;
}

static napi_value InvalidPlayerWnd(napi_env env, napi_callback_info info) {
    if (g_playerWnd) {
        g_bEraseBkg = true;
        RECT rc = { 0 };
        GetWindowRect(g_playerWnd, &rc);
        ::InvalidateRect(g_playerWnd, &rc, true);
    }
    return nullptr;
}

static napi_value GetPeerId(napi_env env, napi_callback_info info) {
    auto id = peer_id::acquire_peerid(flag::XUNLEI_X);
    napi_value obj;
    napi_create_string_utf8(env, id.c_str(), id.length(), &obj);
    return obj;
}

static napi_value ReadRegString(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    std::string strRegkey, strRegPath, strRegName;
    AddonBaseOpt::ParseString(env, argv[0], strRegkey);
    AddonBaseOpt::ParseString(env, argv[1], strRegPath);
    AddonBaseOpt::ParseString(env, argv[2], strRegName);
    std::wstring wstrRegPath, wstrRegName;
    xl::text::transcode::UTF8_to_Unicode(strRegPath.c_str(), strRegPath.length(), wstrRegPath);
    xl::text::transcode::UTF8_to_Unicode(strRegName.c_str(), strRegName.length(), wstrRegName);
    HKEY hkey = NULL;
    if (stricmp(strRegkey.c_str(), "HKEY_CLASSES_ROOT") == 0) {
        hkey = HKEY_CLASSES_ROOT;
    }
    else if (stricmp(strRegkey.c_str(), "HKEY_CURRENT_USER") == 0) {
        hkey = HKEY_CURRENT_USER;
    }
    else if (stricmp(strRegkey.c_str(), "HKEY_LOCAL_MACHINE") == 0) {
        hkey = HKEY_LOCAL_MACHINE;
    }
    else if (stricmp(strRegkey.c_str(), "HKEY_USERS") == 0) {
        hkey = HKEY_USERS;
    }
    napi_value valueObj;
    napi_create_object(env, &valueObj);
    bool bSucc = false;
    std::string strMsg = "ok";
    HKEY hOpenKey = NULL;
    do {
        if (!hkey) {
            strMsg = "key error";
            break;
        }
        LONG result = RegOpenKeyExW(hkey, wstrRegPath.c_str(), 0, KEY_READ, &hOpenKey);
        if (result != ERROR_SUCCESS) {
            strMsg = "open key failed";
            break;
        }
        DWORD dwType;
        DWORD dataSize = 0;
        // ????ε??? RegQueryValueEx ??????????С
        result = RegQueryValueExW(hOpenKey, wstrRegName.c_str(), 0, &dwType, NULL, &dataSize);
        if (result != ERROR_SUCCESS) {
            strMsg = "get type and size failed";
            break;
        }
        if (dwType == REG_DWORD) {
            DWORD dwValue = 0;
            result = RegQueryValueExW(hOpenKey, wstrRegName.c_str(), 0, 0, (LPBYTE)&dwValue, nullptr);
            if (result != ERROR_SUCCESS) {
                strMsg = "read dword value failed";
                break;
            }
            bSucc = true;
            AddonBaseOpt::PushObjectUint32(env, valueObj, "v", dwValue);
            break;
        }
        else if (dwType == REG_SZ) {
            //auto ptr = std::make_unique<char>(dataSize + 1);
            wchar_t* ptr = (wchar_t*)new BYTE[dataSize + 2];
            memset(ptr, 0, dataSize + 2);
            result = RegQueryValueExW(hOpenKey, wstrRegName.c_str(), 0, 0, (LPBYTE)(ptr), &dataSize);
            if (result == ERROR_SUCCESS) {
                bSucc = true;
                std::string str;
                xl::text::transcode::Unicode_to_UTF8(ptr, wcslen(ptr), str);
                AddonBaseOpt::PushObjectString(env, valueObj, "v", str);
            }
            else {
                strMsg = "read string value failed";
            }
            delete[]ptr;
        }
        else {
            strMsg = "not support type";
        }
    } while (false);
    if (hOpenKey) {
        RegCloseKey(hOpenKey);
    }
    AddonBaseOpt::PushObjectString(env, valueObj, "msg", strMsg);
    AddonBaseOpt::PushObjectBool(env, valueObj, "succ", bSucc);

    return valueObj;
}

static napi_value WriteRegValue(napi_env env, napi_callback_info info) {
    napi_value napi_result;
    bool bSucc = false;
    std::string strMsg = "ok";

    size_t argc = 5;
    napi_value argv[5] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    do
    {
        if (argc < 5)
        {
            break;
        }

        std::string strRegkey, strRegPath, strRegName, strRegValue;
        AddonBaseOpt::ParseString(env, argv[0], strRegkey);
        AddonBaseOpt::ParseString(env, argv[1], strRegPath);
        AddonBaseOpt::ParseString(env, argv[2], strRegName);
        AddonBaseOpt::ParseString(env, argv[3], strRegValue);
        std::wstring wstrRegkey, wstrRegPath, wstrRegName, wstrRegValue;
        xl::text::transcode::UTF8_to_Unicode(strRegkey.c_str(), strRegkey.length(), wstrRegkey);
        xl::text::transcode::UTF8_to_Unicode(strRegPath.c_str(), strRegPath.length(), wstrRegPath);
        xl::text::transcode::UTF8_to_Unicode(strRegName.c_str(), strRegName.length(), wstrRegName);
        xl::text::transcode::UTF8_to_Unicode(strRegValue.c_str(), strRegValue.length(), wstrRegValue);
        HKEY hkey = NULL;
        if (stricmp(strRegkey.c_str(), "HKEY_CLASSES_ROOT") == 0) {
            hkey = HKEY_CLASSES_ROOT;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_CURRENT_USER") == 0) {
            hkey = HKEY_CURRENT_USER;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_LOCAL_MACHINE") == 0) {
            hkey = HKEY_LOCAL_MACHINE;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_USERS") == 0) {
            hkey = HKEY_USERS;
        }

        if (hkey == NULL)
        {
            break;
        }

        HKEY hKey;
        if (::RegOpenKeyExW(hkey, wstrRegPath.c_str(), 0, KEY_WRITE, &hKey) != ERROR_SUCCESS)
        {
            strMsg = "open key failed";
            break;
        }

        UINT32 dwType;
        AddonBaseOpt::ParseUint32(env, argv[4], dwType);

        if (ERROR_SUCCESS == ::SHSetValueW(hkey, wstrRegPath.c_str(), wstrRegName.c_str(),
            dwType, wstrRegValue.c_str(), (wstrRegValue.length() + 1) * sizeof(wchar_t)))
        {
            bSucc = true;
        }
        else
        {
            strMsg = "write value failed";
        }
    } while (0);

    napi_create_object(env, &napi_result);
    AddonBaseOpt::PushObjectString(env, napi_result, "msg", strMsg);
    AddonBaseOpt::PushObjectBool(env, napi_result, "succ", bSucc);
    return napi_result;
}

static napi_value DeleteRegKey(napi_env env, napi_callback_info info) {
    napi_value napi_result;
    bool bSucc = false;
    std::string strMsg = "ok";

    size_t argc = 2;
    napi_value argv[2] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    do
    {
        if (argc < 2)
        {
            break;
        }

        std::string strRegkey, strRegPath;
        AddonBaseOpt::ParseString(env, argv[0], strRegkey);
        AddonBaseOpt::ParseString(env, argv[1], strRegPath);
        HKEY hkey = NULL;
        if (stricmp(strRegkey.c_str(), "HKEY_CLASSES_ROOT") == 0) {
            hkey = HKEY_CLASSES_ROOT;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_CURRENT_USER") == 0) {
            hkey = HKEY_CURRENT_USER;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_LOCAL_MACHINE") == 0) {
            hkey = HKEY_LOCAL_MACHINE;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_USERS") == 0) {
            hkey = HKEY_USERS;
        }

        if (hkey == NULL)
        {
            break;
        }

        std::string strKeyName(strRegPath);
        std::string strSubKey;
        size_t pos = strKeyName.find_last_of('\\');
        if (pos != std::string::npos)
        {
            strSubKey = strKeyName.substr(pos + 1);
            strKeyName = strKeyName.substr(0, pos);
        }
        else
        {
            strKeyName = "";
            strSubKey = strRegPath;
        }

        CRegKey regKey;
        REGSAM samDesired = KEY_SET_VALUE;
        LONG status = regKey.Open(hkey, strKeyName.c_str(), samDesired);
        if (ERROR_SUCCESS != status)
        {
            strMsg = "open key failed";
            break;
        }

        if (regKey.RecurseDeleteKey(strSubKey.c_str()) == ERROR_SUCCESS)
        {
            bSucc = true;
        }
        regKey.Close();
    } while (0);

    napi_create_object(env, &napi_result);
    AddonBaseOpt::PushObjectString(env, napi_result, "msg", strMsg);
    AddonBaseOpt::PushObjectBool(env, napi_result, "succ", bSucc);
    return napi_result;
}

static napi_value DeleteRegValue(napi_env env, napi_callback_info info) {
    napi_value napi_result;
    bool bSucc = false;
    std::string strMsg = "ok";

    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    do
    {
        if (argc < 3)
        {
            break;
        }

        std::string strRegkey, strRegPath, strRegName;
        AddonBaseOpt::ParseString(env, argv[0], strRegkey);
        AddonBaseOpt::ParseString(env, argv[1], strRegPath);
        AddonBaseOpt::ParseString(env, argv[2], strRegName);
        HKEY hkey = NULL;
        if (stricmp(strRegkey.c_str(), "HKEY_CLASSES_ROOT") == 0) {
            hkey = HKEY_CLASSES_ROOT;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_CURRENT_USER") == 0) {
            hkey = HKEY_CURRENT_USER;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_LOCAL_MACHINE") == 0) {
            hkey = HKEY_LOCAL_MACHINE;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_USERS") == 0) {
            hkey = HKEY_USERS;
        }

        if (hkey == NULL)
        {
            strMsg = "key error";
            break;
        }

        HKEY hKey;
        if (::RegOpenKeyEx(hkey, strRegPath.c_str(), 0, KEY_WRITE, &hKey) != ERROR_SUCCESS)
        {
            strMsg = "open key failed";
            break;
        }

        if (::RegDeleteValue(hKey, strRegName.c_str()) == ERROR_SUCCESS)
        {
            bSucc = true;
        }
        ::RegCloseKey(hKey);
    } while (0);

    napi_create_object(env, &napi_result);
    AddonBaseOpt::PushObjectString(env, napi_result, "msg", strMsg);
    AddonBaseOpt::PushObjectBool(env, napi_result, "succ", bSucc);
    return napi_result;
}

static napi_value CreateRegKey(napi_env env, napi_callback_info info) {
    napi_value napi_result;
    bool bSucc = false;
    std::string strMsg = "ok";

    size_t argc = 2;
    napi_value argv[2] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    do
    {
        if (argc < 2)
        {
            break;
        }

        std::string strRegkey, strRegPath;
        AddonBaseOpt::ParseString(env, argv[0], strRegkey);
        AddonBaseOpt::ParseString(env, argv[1], strRegPath);
        std::wstring wstrRegPath;
        xl::text::transcode::UTF8_to_Unicode(strRegPath.c_str(), strRegPath.length(), wstrRegPath);
        HKEY hkey = NULL;
        if (stricmp(strRegkey.c_str(), "HKEY_CLASSES_ROOT") == 0) {
            hkey = HKEY_CLASSES_ROOT;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_CURRENT_USER") == 0) {
            hkey = HKEY_CURRENT_USER;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_LOCAL_MACHINE") == 0) {
            hkey = HKEY_LOCAL_MACHINE;
        }
        else if (stricmp(strRegkey.c_str(), "HKEY_USERS") == 0) {
            hkey = HKEY_USERS;
        }

        if (hkey == NULL)
        {
            break;
        }

        HKEY hResult = NULL;
        DWORD dwDisposition = 0;
        LSTATUS lsRet = ::RegCreateKeyExW(hkey, wstrRegPath.c_str(), NULL, NULL, REG_OPTION_NON_VOLATILE, KEY_ALL_ACCESS, NULL, &hResult, &dwDisposition);
        if (lsRet == ERROR_SUCCESS)
        {
            bSucc = true;
            break;
        }
        else
        {
            strMsg = "create key failed";
            break;
        }
    } while (0);

    napi_create_object(env, &napi_result);
    AddonBaseOpt::PushObjectString(env, napi_result, "msg", strMsg);
    AddonBaseOpt::PushObjectBool(env, napi_result, "succ", bSucc);
    return napi_result;
}

static napi_value SetCommandLineCallback(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    auto pFunc = NapiFunctionWarp::NewObject(env, argv[0]);
    auto pNewTaskFunc = NapiFunctionWarp::NewObject(env, argv[1]);
    CommandLine::GetInstance()->Init(env, pFunc, pNewTaskFunc);
    return nullptr;
}
static napi_value BringWndToTop(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    int64_t h1 = 0;
    AddonBaseOpt::ParseInt64(env, argv[0], h1);
    
    ::BringWindowToTop((HWND)h1);
    return nullptr;
}

static napi_value SetForegroundWindow(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    int64_t h1 = 0;
    AddonBaseOpt::ParseInt64(env, argv[0], h1);
    HWND dstWindow = (HWND)h1;
    if (::IsWindow(dstWindow))
    {
        HWND currentForegroundWindow = ::GetForegroundWindow();
        if (dstWindow != currentForegroundWindow)
        {

            DWORD currentForegroundId = ::GetWindowThreadProcessId(currentForegroundWindow, NULL);
            DWORD currentId = ::GetCurrentThreadId();
            if (::AttachThreadInput(currentId, currentForegroundId, TRUE))
            {
                ::ShowWindow(dstWindow, SW_SHOWNORMAL);
                DWORD style = GetWindowLong(dstWindow, GWL_EXSTYLE);

                ::SetWindowPos(dstWindow, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOSIZE | SWP_NOMOVE);
                if (!(style & WS_EX_TOPMOST))
                {
                    ::SetWindowPos(dstWindow, HWND_NOTOPMOST, 0, 0, 0, 0, SWP_NOSIZE | SWP_NOMOVE);
                }
               ::SetForegroundWindow(dstWindow);
                ::AttachThreadInput(currentId, currentForegroundId, FALSE);
            }
        }
    }

    return nullptr;
}

static napi_value GetFileVersion(napi_env env, napi_callback_info info) {
    v8::Isolate* isolate = v8::Isolate::GetCurrent();
    v8::HandleScope scope(isolate);

    std::string str_version = "";

    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    std::string strFile;
    AddonBaseOpt::ParseString(env, argv[0], strFile);
    std::string strVersion;
    do
    {
        auto nInfoSize = ::GetFileVersionInfoSize(strFile.c_str(), 0);
        if (nInfoSize < 0) {
            break;
        }

        void* buffer = new char[nInfoSize + 4];
        if (::GetFileVersionInfo(strFile.c_str(), 0, nInfoSize, buffer))
        {
            uint32_t file_info_size = 0;
            VS_FIXEDFILEINFO* file_info = NULL;
            if (::VerQueryValue(buffer, "\\", (void**)&file_info, &file_info_size))
            {
                int ver1 = (int)(((uint64_t)file_info->dwFileVersionMS) >> 16);
                int ver2 = (int)(((uint64_t)file_info->dwFileVersionMS) & 0xFFFF);
                int ver3 = (int)(((uint64_t)file_info->dwFileVersionLS) >> 16);
                int ver4 = (int)(((uint64_t)file_info->dwFileVersionLS) & 0xFFFF);
                char ver_buf[64] = { 0 };
                sprintf(ver_buf, "%d.%d.%d.%d", ver1, ver2, ver3, ver4);
                strVersion = ver_buf;
            }
        }
        delete[] buffer;

    } while (false);
    napi_value obj;
    napi_create_string_utf8(env, strVersion.c_str(), strVersion.length(), &obj);
    return obj;
}

static std::string GetVersionBlockString(const char* block)
{
    static std::string str;
    if (str.length() <= 0)
    {
        do {
            const char* module = "Xmp.exe";
            DWORD dwSize, _dummy1;

            dwSize = GetFileVersionInfoSize(module, &_dummy1);
            if (dwSize == 0) {
                break;
            }

            unsigned char* pData = new unsigned char[dwSize];

            if (GetFileVersionInfo(module, 0, dwSize, pData))
            {
                struct LANGANDCODEPAGE
                {
                    WORD wLanguage;
                    WORD wCodePage;
                } *lpTranslate;

                UINT cbTranslate;
                if (VerQueryValue(pData, "\\VarFileInfo\\Translation", (LPVOID*)&lpTranslate, &cbTranslate))
                {
                    for (int i = 0; i < (int)(cbTranslate / sizeof(LANGANDCODEPAGE)); i++) {
                        char subblock[128] = { 0 };
                        char* buf;
                        UINT buf_size;

                        _snprintf(&subblock[0], _countof(subblock), "\\StringFileInfo\\%04x%04x\\%s",
                            lpTranslate[i].wLanguage, lpTranslate[i].wCodePage, block);

                        if (VerQueryValue(pData, subblock, (void**)&buf, &buf_size)) {
                            str = buf;
                            break;
                        }
                    }
                }
            }

            delete[] pData;
        } while (0);
        if (str.empty())
        {
            str = "100001";
        }
    }
    return str;
}
static napi_value GetInstallChannel(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    char szExePath[MAX_PATH] = { 0 };
    GetModuleFileName(NULL, szExePath, MAX_PATH);
    char szProfilePath[MAX_PATH] = { 0 };
    ::PathCombine(szProfilePath, szExePath, "..\\..\\Profiles\\BindingConfig.ini");

    char szChannelId[128] = { 0 };
    DWORD result = ::GetPrivateProfileString("ThunderInstall", "ChannelId", "", szChannelId, 128, szProfilePath);
    std::string install_channel;
    if (strlen(szChannelId) <= 0)
    {
        install_channel = GetVersionBlockString("SpecialBuild");
    }
    else
    {
        install_channel = szChannelId;
    }

    napi_value obj;
    napi_create_string_utf8(env, install_channel.c_str(), install_channel.length(), &obj);
    return obj;
}

static napi_value GetPublicUserDataPath(napi_env env, napi_callback_info info) {
    PWSTR pszPath;
    wchar_t path[MAX_PATH] = { 0 };
    ::SHGetKnownFolderPath(FOLDERID_Public, 0, NULL, &pszPath);
    ::wcscpy(path, pszPath);
    ::CoTaskMemFree(pszPath);
    std::string str;
    xl::text::transcode::Unicode_to_UTF8(path, wcslen(path), str);

    napi_value obj;
    napi_create_string_utf8(env, str.c_str(), str.length(), &obj);
    return obj;
}

static napi_value GetAppDataLocalLowPath(napi_env env, napi_callback_info info) {
    PWSTR pszPath;
    wchar_t path[MAX_PATH] = { 0 };
    ::SHGetKnownFolderPath(FOLDERID_LocalAppDataLow, 0, NULL, &pszPath);
    ::wcscpy(path, pszPath);
    ::CoTaskMemFree(pszPath);
    std::string str;
    xl::text::transcode::Unicode_to_UTF8(path, wcslen(path), str);

    napi_value obj;
    napi_create_string_utf8(env, str.c_str(), str.length(), &obj);
    return obj;
}

static napi_value ExportGetSystemDirectory(napi_env env, napi_callback_info info) {
    TCHAR path[MAX_PATH];
    ::GetSystemDirectory(path, MAX_PATH);

    napi_value obj;
    napi_create_string_utf8(env, path, strlen(path), &obj);
    return obj;
}

static napi_value ReadINI(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    std::string strResult = "";
    do
    {
        if (argc < 3)
        {
            break;
        }

        std::string strFilePath, strAppName, strKeyName;
        AddonBaseOpt::ParseString(env, argv[0], strFilePath);
        AddonBaseOpt::ParseString(env, argv[1], strAppName);
        AddonBaseOpt::ParseString(env, argv[2], strKeyName);

        if (strFilePath.empty() || !::PathFileExists(strFilePath.c_str()))
        {
            break;
        }

        char resultBuffer[4 * 1024] = { 0 };
        DWORD result = ::GetPrivateProfileString(strAppName.c_str(), strKeyName.c_str(), "", resultBuffer, 4 * 1024, strFilePath.c_str());
        if (result > 0)
        {
            std::wstring wstr;
            xl::text::transcode::ANSI_to_Unicode(resultBuffer, strlen(resultBuffer), wstr);
            xl::text::transcode::Unicode_to_UTF8(wstr.c_str(), wstr.length(), strResult);
        }

    } while (false);
    napi_value obj;
    napi_create_string_utf8(env, strResult.c_str(), strResult.length(), &obj);
    return obj;
}

static napi_value WriteINI(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value argv[4] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    DWORD result = 0;
    do
    {
        if (argc < 3)
        {
            break;
        }

        std::string strFilePath, strAppName, strKeyName, strBuffer;
        AddonBaseOpt::ParseString(env, argv[0], strFilePath);
        AddonBaseOpt::ParseString(env, argv[1], strAppName);
        AddonBaseOpt::ParseString(env, argv[2], strKeyName);
        AddonBaseOpt::ParseString(env, argv[3], strBuffer);

        std::wstring wstr;
        xl::text::transcode::UTF8_to_Unicode(strBuffer.c_str(), strBuffer.length(), wstr);
        std::string str;
        xl::text::transcode::Unicode_to_ANSI(wstr.c_str(), wstr.length(), str);
        result = ::WritePrivateProfileString(strAppName.c_str(), strKeyName.c_str(), str.c_str(), strFilePath.c_str());
    } while (false);
    napi_value obj;
    napi_create_int64(env, result, &obj);
    return obj;
}

static napi_value GetDmideCode(napi_env env, napi_callback_info info) {
    char* szDemideCode = NULL;
    get_dmide_code(&szDemideCode);
    std::string strDemideCode;
    if (szDemideCode) {
        strDemideCode = szDemideCode;
        delete[] szDemideCode;
        szDemideCode = NULL;
    }

    napi_value obj;
    napi_create_string_utf8(env, strDemideCode.c_str(), strDemideCode.length(), &obj);
    return obj;
}

static napi_value GetExeCommandLine(napi_env env, napi_callback_info info) {
    auto cmdLine = ::GetCommandLineW();
    std::string str;
    if (cmdLine) {
        xl::text::transcode::Unicode_to_UTF8(cmdLine, wcslen(cmdLine), str);
    }

    napi_value obj;
    napi_create_string_utf8(env, str.c_str(), str.length(), &obj);
    return obj;
}

static bool GetLogicalDriveStrings_(std::vector< std::string >& drivers)
{
    LPWSTR buffer = new WCHAR[MAX_PATH + 1];
    DWORD ret = ::GetLogicalDriveStringsW(MAX_PATH, buffer);
    if (ret == 0)
    {
        delete[]buffer;
        return false;
    }
    else if (ret > MAX_PATH)
    {
        delete[]buffer;
        buffer = new WCHAR[ret + 1];
        ret = ::GetLogicalDriveStringsW(ret, buffer);
        if (ret == 0)
        {
            delete[]buffer;
            return false;
        }
    }
    LPWSTR temp = buffer;
    std::wstring str_temp(temp);
    std::string strDriver;
    while (!str_temp.empty())
    {
        xl::text::transcode::Unicode_to_UTF8(str_temp.c_str(), str_temp.length(), strDriver);
        drivers.push_back(strDriver);
        temp += strDriver.length() + 1;
        str_temp = temp;
    }
    delete[]buffer;
    return true;
}
static napi_value ExportGetLogicalDriveStrings(napi_env env, napi_callback_info info) {
    std::vector<std::string> drivers;
    napi_value obj = nullptr;
    GetLogicalDriveStrings_(drivers);
    AddonBaseOpt::PushStringArray(env, &obj, drivers);
    return obj;
}

static napi_value ExportGetDriveType(napi_env env, napi_callback_info info) {
    napi_value obj = nullptr;
    do
    {
        size_t argc = 1;
        napi_value argv[1] = { nullptr };
        napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
        assert(napi_ok == status);
        if (argc < 1)
        {
            break;
        }

        std::string strPath;
        AddonBaseOpt::ParseString(env, argv[0], strPath);
        UINT type = ::GetDriveType(strPath.c_str());
        AddonBaseOpt::PushUint32(env, &obj, type);
    } while (false);

    return obj;
}

static long GetPartitionSpace(const wchar_t* file_path, signed __int64& free_space, signed __int64& total_space)
{
    free_space = -1;
    total_space = -1;
    do {
        if (file_path == NULL)
        {
            break;
        }

        if (wcslen(file_path) < 3)
        {
            break;
        }

        std::wstring path(file_path);
        std::wstring driver = path.substr(0, 3);

        ULARGE_INTEGER total_number_of_bytes;
        ULARGE_INTEGER total_number_of_freebytes;
        if (GetDiskFreeSpaceExW(driver.c_str(), NULL, (PULARGE_INTEGER)&total_number_of_bytes, (PULARGE_INTEGER)&total_number_of_freebytes))
        {
            free_space = total_number_of_freebytes.QuadPart;
            total_space = total_number_of_bytes.QuadPart;
        }
    } while (0);
    return 0;
}
static napi_value ExportGetFreePartitionSpace(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value argv[4] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    signed __int64 total_space = -1;
    signed __int64 free_space = -1;
    do
    {
        if (argc < 1)
        {
            break;
        }
        
        std::string strPath;
        AddonBaseOpt::ParseString(env, argv[0], strPath);
        std::wstring wstrPath;
        xl::text::transcode::UTF8_to_Unicode(strPath.c_str(), strPath.length(), wstrPath);
        GetPartitionSpace(wstrPath.c_str(), free_space, total_space);
    } while (false);

    napi_value obj = nullptr;
    AddonBaseOpt::PushInt64(env, &obj, free_space);
    return obj;
}

static napi_value ExportGetPartitionSpace(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value argv[4] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    signed __int64 total_space = -1;
    signed __int64 free_space = -1;
    do
    {
        if (argc < 1)
        {
            break;
        }

        std::string strPath;
        AddonBaseOpt::ParseString(env, argv[0], strPath);
        std::wstring wstrPath;
        xl::text::transcode::UTF8_to_Unicode(strPath.c_str(), strPath.length(), wstrPath);
        GetPartitionSpace(wstrPath.c_str(), free_space, total_space);
    } while (false);

    napi_value napi_result = nullptr;
    napi_create_object(env, &napi_result);
    AddonBaseOpt::PushObjectInt64(env, napi_result, "total", total_space);
    AddonBaseOpt::PushObjectInt64(env, napi_result, "free", free_space);
    return napi_result;
}

static napi_value ExportTerminateProcess(napi_env env, napi_callback_info info) {
    auto h = ::GetCurrentProcess();
    ::TerminateProcess(h, 0);
    return nullptr;
}



static napi_value StartAutoUpdate(napi_env env, napi_callback_info info) {
    xmp_helper_common::StartUpdateByCmd("-UpdateMode:a");
    return nullptr;
}

static napi_value StartLocalUpdate(napi_env env, napi_callback_info info) {
    xmp_helper_common::StartUpdateByCmd("-UpdateMode:l");
    return nullptr;
}

xl::coroutine::AsyncTask<void> StartUpdateAsync(std::shared_ptr<NapiFunctionWarp> pFunc) {

    uv_loop_t* pLoop{ nullptr };
    auto status = napi_get_uv_event_loop(pFunc->GetEnv(), &pLoop);
    auto mainThreadMessage = xl::thread::LibuvThreadMessage::NewObject(pLoop);

	auto awaitCb = [mainThreadMessage](xl::coroutine::CommonAwaitable<bool>::_CB&& cb) {
		auto mt = std::thread([cb = std::move(cb), mainThreadMessage]() {
			auto bRet = xmp_helper_common::StartUpdateByCmd("-UpdateMode:m");

			// do while 循环4s 超时
			int i = 0;
			bool isShow = false;
			while (i < 40) {
				Sleep(100);
				i++;
				std::wstring name = L"\u8FC5\u96F7\u5F71\u97F3\u5347\u7EA7\u7A0B\u5E8F";  //"迅雷影音升级程序"
				HWND hWnd = ::FindWindowW(NULL, name.c_str());
				if (hWnd != NULL) {
					//置顶窗口并显示
					::BringWindowToTop(hWnd);
					isShow = true;
					break;
				}
			}

			if (bRet && !isShow) {     //4S 没有启动就kill
				xmp_helper_common::TerminateProcessByName("XmpLiveUD.exe");
			}
			mainThreadMessage->PostTask([cb, bRet]() {cb(bRet); });
		});
		mt.detach();
    };
    auto bRet = co_await xl::coroutine::CommonAwaitable<bool>{awaitCb};
    auto pArgv = pFunc->NewArgv(1);
    pArgv->PushBool(bRet);
    pFunc->Call(std::move(pArgv), nullptr);
}

static napi_value StartManaulUpdate(napi_env env, napi_callback_info info) {
	napi_status status;
	size_t argc = 1;
	napi_value argv[1];
	napi_value _this = nullptr;
    StartUpdate_callback_data* callbackData = nullptr;
	status = napi_get_cb_info(env, info, &argc, argv, &_this, nullptr);
	if (argc < 1) {
		XL_SPDLOG_ERROR("failed, for argc < 1, count={:d}", argc);
		assert(false);
		return nullptr;
	}

    auto pFunc = NapiFunctionWarp::NewObject(env, argv[0]);
    StartUpdateAsync(pFunc);
	return nullptr;
}

static napi_value IsAutoRun(napi_env env, napi_callback_info info) {
    HKEY hkey = HKEY_CURRENT_USER;
    HKEY hOpenKey = NULL;
    bool bAutoRun = false;
    do
    {
        LONG result = RegOpenKeyExW(hkey, L"Software\\Microsoft\\Windows\\CurrentVersion\\Run", 0, KEY_READ, &hOpenKey);
        if (result != ERROR_SUCCESS) {
            break;
        }
        std::wstring wstrRegName = L"Thunder";
        DWORD dwType;
        DWORD dataSize = 0;
        // 第一次调用 RegQueryValueEx 来获取数据大小
        result = RegQueryValueExW(hOpenKey, wstrRegName.c_str(), 0, &dwType, NULL, &dataSize);
        if (result != ERROR_SUCCESS) {
            break;
        }
        if (dwType == REG_SZ) {
            std::wstring wstrPath;
            wchar_t wszXmpPath[4096] = { 0 };
            ::GetModuleFileNameW(NULL, wszXmpPath, 4096);
            wstrPath = wszXmpPath;
            if (wstrPath.empty()) {
                break;
            }
            wstrPath += L" -silent -StartType:AutoRun";

            //auto ptr = std::make_unique<char>(dataSize + 1);
            wchar_t* ptr = new wchar_t[dataSize + 1];
            result = RegQueryValueExW(hOpenKey, wstrRegName.c_str(), 0, 0, (LPBYTE)(ptr), &dataSize);
            if (result == ERROR_SUCCESS) {
                ptr[dataSize] = L'\0';
                if (wcsicmp(ptr, wstrPath.c_str()) == 0) {
                    bAutoRun = true;
                }
            }
            delete[]ptr;
        }
    } while (false);
    if (hOpenKey) {
        RegCloseKey(hOpenKey);
    }
    napi_value obj;
    AddonBaseOpt::PushBool(env, &obj, bAutoRun);
    return obj;
}

static napi_value SetAutoRun(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value argv[4] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    bool bAutoRun = true;
    AddonBaseOpt::ParseBool(env, argv[0], bAutoRun);
    HKEY hkey = HKEY_CURRENT_USER;
    HKEY hOpenKey = NULL;
    bool bSucc = false;
    do
    {
        std::wstring wstrRegPath = L"Software\\Microsoft\\Windows\\CurrentVersion\\Run";
        LONG result = RegOpenKeyExW(hkey, wstrRegPath.c_str(), 0, KEY_WRITE, &hOpenKey);
        if (result != ERROR_SUCCESS) {
            break;
        }
        std::wstring wstrRegName = L"Thunder";
        if (!bAutoRun) {
            result = ::RegDeleteValueW(hOpenKey, wstrRegName.c_str());
            if (result == ERROR_SUCCESS) {
                bSucc = true;
            }
        }
        else {
            std::wstring wstrPath;
            wchar_t wszXmpPath[4096] = { 0 };
            ::GetModuleFileNameW(NULL, wszXmpPath, 4096);
            wstrPath = wszXmpPath;
            if (wstrPath.empty()) {
                break;
            }
            wstrPath += L" -silent -StartType:AutoRun";
            if (ERROR_SUCCESS == ::SHSetValueW(hkey, wstrRegPath.c_str(), wstrRegName.c_str(), REG_SZ, wstrPath.c_str(), (wstrPath.length() + 1) * sizeof(wchar_t))) {
                bSucc = true;
            }
        }
    } while (false);

    if (hOpenKey) {
        RegCloseKey(hOpenKey);
    }
    napi_value obj;
    AddonBaseOpt::PushBool(env, &obj, bSucc);
    return obj;
}

static napi_value ExportSetScreenSaveActive(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value argv[4] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    bool bActive = true;
    AddonBaseOpt::ParseBool(env, argv[0], bActive);
    if (!bActive)
    {
        EXECUTION_STATE esFlags = ES_SYSTEM_REQUIRED | ES_CONTINUOUS | ES_DISPLAY_REQUIRED;
        ::SetThreadExecutionState(esFlags);
    }
    else
    {
        ::SetThreadExecutionState(ES_CONTINUOUS);
    }
    return nullptr;
}

static napi_value XlShellExecEx(napi_env env, napi_callback_info info) {
    int dwRet = 0;
    do
    {
        size_t argc = 6;
        napi_value argv[6] = { nullptr };
        napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
        assert(napi_ok == status);

        // 检测传入的参数个数是否正确
        if (argc < 6)
        {
            break;
        }
        int64_t n = 0;
        AddonBaseOpt::ParseInt64(env, argv[0], n);
        HWND hWnd = (HWND)n;
        std::string strOperation, strFile, strParameters, strDir, strCmd;
        std::wstring wstrOperation, wstrFile, wstrParameters, wstrDir;
        AddonBaseOpt::ParseString(env, argv[1], strOperation);
        xl::text::transcode::UTF8_to_Unicode(strOperation.c_str(), strOperation.length(), wstrOperation);
        AddonBaseOpt::ParseString(env, argv[2], strFile);
        xl::text::transcode::UTF8_to_Unicode(strFile.c_str(), strFile.length(), wstrFile);
        AddonBaseOpt::ParseString(env, argv[3], strParameters);
        xl::text::transcode::UTF8_to_Unicode(strParameters.c_str(), strParameters.length(), wstrParameters);
        AddonBaseOpt::ParseString(env, argv[4], strDir);
        xl::text::transcode::UTF8_to_Unicode(strDir.c_str(), strDir.length(), wstrDir);
        AddonBaseOpt::ParseString(env, argv[5], strCmd);
        INT nShowCmd = SW_SHOW;
        if (AddonBaseOpt::IsNumber(env, argv[5]))
        {
            nShowCmd = AddonBaseOpt::ParseInt32(env, argv[5], nShowCmd);
        }

        dwRet = (int)ShellExecuteW(hWnd, wstrOperation.c_str(), wstrFile.c_str(), wstrParameters.c_str(), wstrDir.c_str(), nShowCmd);
        if (SE_ERR_NOASSOC == dwRet)
        {
            std::wstring strParam = L"shell32, OpenAs_RunDLL " + wstrFile;
            dwRet = (int)ShellExecuteW(NULL, L"open", L"rundll32.exe", strParam.c_str(), NULL, nShowCmd);
        }
    } while (false);

    napi_value obj;
    AddonBaseOpt::PushInt64(env, &obj, dwRet);
    return obj;
}

static napi_value AsyncShellExecute(napi_env env, napi_callback_info info) {
    do
    {
        size_t argc = 7;
        napi_value argv[7] = { nullptr };
        napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
        assert(napi_ok == status);

        // 检测传入的参数个数是否正确
        if (argc < 7)
        {
            break;
        }
        int64_t n = 0;
        AddonBaseOpt::ParseInt64(env, argv[0], n);
        HWND hWnd = (HWND)n;
        std::string strOperation, strFile, strParameters, strDir, strCmd;
        std::wstring wstrOperation, wstrFile, wstrParameters, wstrDir;
        AddonBaseOpt::ParseString(env, argv[1], strOperation);
        xl::text::transcode::UTF8_to_Unicode(strOperation.c_str(), strOperation.length(), wstrOperation);
        AddonBaseOpt::ParseString(env, argv[2], strFile);
        xl::text::transcode::UTF8_to_Unicode(strFile.c_str(), strFile.length(), wstrFile);
        AddonBaseOpt::ParseString(env, argv[3], strParameters);
        xl::text::transcode::UTF8_to_Unicode(strParameters.c_str(), strParameters.length(), wstrParameters);
        AddonBaseOpt::ParseString(env, argv[4], strDir);
        xl::text::transcode::UTF8_to_Unicode(strDir.c_str(), strDir.length(), wstrDir);
        AddonBaseOpt::ParseString(env, argv[5], strCmd);
        INT nShowCmd = SW_SHOW;
        if (AddonBaseOpt::IsNumber(env, argv[5]))
        {
            nShowCmd = AddonBaseOpt::ParseInt32(env, argv[5], nShowCmd);
        }
        auto pFunc = NapiFunctionWarp::NewObject(env, argv[6]);


        auto f = [](decltype(pFunc) pFunc, HWND hWnd, const std::wstring& wstrOperation, const std::wstring& wstrFile, const std::wstring& wstrParameters, const std::wstring& wstrDir, INT nShowCmd)->xl::coroutine::AsyncTask<void> {
            std::wstring wstrTempOperation = wstrOperation;
            std::wstring wstrTempFile = wstrFile;
            std::wstring wstrTempParam = wstrParameters;
            std::wstring wstrTempDir = wstrDir;
            co_await WorkerThread::GetInstance()->ResumeOnThreadPool();
            auto dwRet = (int)ShellExecuteW(hWnd, wstrTempOperation.c_str(), wstrTempFile.c_str(), wstrTempParam.c_str(), wstrTempDir.c_str(), nShowCmd);
            if (SE_ERR_NOASSOC == dwRet)
            {
                std::wstring strParam = L"shell32, OpenAs_RunDLL " + wstrFile;
                dwRet = (int)ShellExecuteW(NULL, L"open", L"rundll32.exe", strParam.c_str(), NULL, nShowCmd);
            }
            co_await WorkerThread::GetInstance()->ResumeOnMainUIThread();
            auto pArgv = pFunc->NewArgv(1);
            pArgv->PushInt64(dwRet);
            pFunc->Call(std::move(pArgv), nullptr);
        };
        f(pFunc, hWnd, wstrOperation, wstrFile, wstrParameters, wstrDir, nShowCmd);
    } while (false);
    return nullptr;
}

static napi_value Associate(napi_env env, napi_callback_info info) {
    size_t argc = 5;
    napi_value argv[5] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    std::string strCmd;
    bool bForce = true;
    AddonBaseOpt::ParseString(env, argv[0], strCmd);
    AddonBaseOpt::ParseBool(env, argv[1], bForce);
    XmpAssociate::Associate(strCmd, bForce);
    return nullptr;
}

void UpdateFloatWndShowRect() {
    /*do {
        HRGN rootRgn = ::CreateRectRgn(0, 0, 0, 0);
        if (!rootRgn) {
            break;
        }
        for (const auto& pair : g_playerWndShowRect) {
            for (const auto& item : pair.second) {
                HRGN rgn = ::CreateRectRgn(item.left * g_dpi, item.top * g_dpi, item.right * g_dpi + 300, item.bottom * g_dpi);
                if (rgn) {
                    ::CombineRgn(rootRgn, rootRgn, rgn, RGN_OR);
                    ::DeleteObject(rgn);
                }
            }
        }
        ::SetWindowRgn(g_floatWnd, rootRgn, TRUE);
        ::DeleteObject(rootRgn);
    } while (false);*/
}
static napi_value AddFloatWinodwShowRect(napi_env env, napi_callback_info info) {
    if (g_enableDwm) {
        return nullptr;
    }

    size_t argc = 5;
    napi_value argv[5] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    std::string strName;
    AddonBaseOpt::ParseString(env, argv[0], strName);
    uint32_t nCount = 0;
    napi_get_array_length(env, argv[1], &nCount);
    if (nCount > 0) {
        std::vector<RECT> rects;
        rects.reserve(nCount);
        for (uint32_t i = 0; i < nCount; i++) {
            napi_value element;
            if (napi_ok == napi_get_element(env, argv[1], i, &element)) {
                RECT rc = { 0 };
                int32_t n = 0;
                bool b = AddonBaseOpt::ParseObjectInt32(env, element, "x", n);
                rc.left = n;
                b |= AddonBaseOpt::ParseObjectInt32(env, element, "y", n);
                rc.top = n;
                b |= AddonBaseOpt::ParseObjectInt32(env, element, "w", n);
                rc.right = n + rc.left;
                b |= AddonBaseOpt::ParseObjectInt32(env, element, "h", n);
                rc.bottom = n + rc.top;
                if (b) {
                    XL_SPDLOG_INFO("AddFloatWinodwShowRect name={:s},left={:d}, top={:d},right={:d},bottom={:d}", strName, rc.left, rc.top, rc.right, rc.bottom);
                    rects.push_back(rc);
                }
            }
        }
        if (!rects.empty()) {
            g_playerWndShowRect.insert(decltype(g_playerWndShowRect)::value_type(strName, rects));
            UpdateFloatWndShowRect();
        }
    }
    return nullptr;
}

static napi_value DelFloatWinodwShowRect(napi_env env, napi_callback_info info) {
    size_t argc = 5;
    napi_value argv[5] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    std::string strName;
    AddonBaseOpt::ParseString(env, argv[0], strName);
    XL_SPDLOG_INFO("DelFloatWinodwShowRect name={:s}", strName);
    if (g_playerWndShowRect.find(strName) != g_playerWndShowRect.end()) {
        g_playerWndShowRect.erase(strName);
        UpdateFloatWndShowRect();
    }
    return nullptr;
}

static napi_value GetDPIAwareSupport(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    bool dpi_aware_support = DPIInterface::GetDPIAwareSupport();
    AddonBaseOpt::PushBool(env, &napi_result, dpi_aware_support);
    return napi_result;
}

static napi_value GetMonitorDPIFactor(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    do {
        size_t argc = 1;
        napi_value argv[1];
        napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

        if (argc < 1) {
            break;
        }

        int64_t wnd = 0;
        AddonBaseOpt::ParseInt64(env, argv[0], wnd);
        HWND hwnd = (HWND)wnd;
        double dpi_factor = DPIInterface::GetMonitorDPIFactor(hwnd);
        AddonBaseOpt::PushDouble(env, &napi_result, dpi_factor);
    } while (0);
    
    return napi_result;
}

static napi_value GetSysDPIFactor(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    double sys_dpi_factor = DPIInterface::GetSysDPIFactor();
    AddonBaseOpt::PushDouble(env, &napi_result, sys_dpi_factor);
    return napi_result;
}

static napi_value ExportGetWindowRect(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    do {
        size_t argc = 1;
        napi_value argv[1];
        napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

        if (argc < 1) {
            break;
        }

        int64_t wnd = 0;
        AddonBaseOpt::ParseInt64(env, argv[0], wnd);
        HWND hwnd = (HWND)wnd;
        RECT rc;
        ZeroMemory(&rc, sizeof(RECT));
        if (hwnd)
        {
            ::GetWindowRect(hwnd, &rc);
        }

        napi_create_object(env, &napi_result);
        AddonBaseOpt::PushObjectUint32(env, napi_result, "x", rc.left);
        AddonBaseOpt::PushObjectUint32(env, napi_result, "y", rc.top);
        AddonBaseOpt::PushObjectUint32(env, napi_result, "width", rc.right - rc.left);
        AddonBaseOpt::PushObjectUint32(env, napi_result, "height", rc.bottom - rc.top);
    } while (0);

    return napi_result;
}

static napi_value ExportSetWindowPos(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    bool ret = false;
    do {
        size_t argc = 7;
        napi_value argv[7];
        napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

        if (argc < 7) {
            break;
        }

        if (!AddonBaseOpt::IsNumber(env, argv[0]) ||
            !AddonBaseOpt::IsNumber(env, argv[1]) ||
            !AddonBaseOpt::IsNumber(env, argv[2]) ||
            !AddonBaseOpt::IsNumber(env, argv[3]) ||
            !AddonBaseOpt::IsNumber(env, argv[4]) ||
            !AddonBaseOpt::IsNumber(env, argv[5]) ||
            !AddonBaseOpt::IsNumber(env, argv[6])) {
            break;
        }

        int64_t wnd = 0;
        AddonBaseOpt::ParseInt64(env, argv[0], wnd);
        HWND hwnd = (HWND)wnd;
        if (!::IsWindow(hwnd)) {
            break;
        }

        int64_t param1;
        AddonBaseOpt::ParseInt64(env, argv[1], param1);
        HWND hwnd_insert_after = (HWND)param1;

        int x;
        AddonBaseOpt::ParseInt32(env, argv[2], x);
        int y;
        AddonBaseOpt::ParseInt32(env, argv[3], y);
        int width;
        AddonBaseOpt::ParseInt32(env, argv[4], width);
        int height;
        AddonBaseOpt::ParseInt32(env, argv[5], height);
        UINT flags = 0;
        AddonBaseOpt::ParseUint32(env, argv[6], flags);
        if (!::SetWindowPos(hwnd, hwnd_insert_after, x, y, width, height, flags))
        {
            break;
        }
        ret = true;
    } while (0);

    AddonBaseOpt::PushBool(env, &napi_result, ret);
    return napi_result;
}

static napi_value ExportShowWindow(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    bool ret = false;
    do {
        size_t argc = 2;
        napi_value argv[2];
        napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

        if (argc < 2) {
            break;
        }

        if (!AddonBaseOpt::IsNumber(env, argv[0]) ||
            !AddonBaseOpt::IsNumber(env, argv[1])) {
            break;
        }

        int64_t wnd = 0;
        AddonBaseOpt::ParseInt64(env, argv[0], wnd);
        HWND hwnd = (HWND)wnd;
        if (!::IsWindow(hwnd)) {
            break;
        }
        
        int nCmdShow = 0;
        AddonBaseOpt::ParseInt32(env, argv[1], nCmdShow);

        if (!::ShowWindow(hwnd, nCmdShow)) {
            break;
        }
        ret = true;
    } while (0);

    AddonBaseOpt::PushBool(env, &napi_result, ret);
    return napi_result;
}

static napi_value ExportGetNextWindow(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    HWND ret = NULL;
    do {
        size_t argc = 2;
        napi_value argv[2];
        napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

        if (argc < 2) {
            break;
        }

        if (!AddonBaseOpt::IsNumber(env, argv[0]) ||
            !AddonBaseOpt::IsNumber(env, argv[1])) {
            break;
        }

        int64_t wnd = 0;
        AddonBaseOpt::ParseInt64(env, argv[0], wnd);
        HWND hwnd = (HWND)wnd;
        if (!::IsWindow(hwnd)) {
            break;
        }

        int wcmd = 0;
        AddonBaseOpt::ParseInt32(env, argv[1], wcmd);

        ret = ::GetNextWindow(hwnd, wcmd);
    } while (0);

    AddonBaseOpt::PushInt64(env, &napi_result, (int64_t)ret);
    return napi_result;
}

static napi_value ExportCompareStr(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    do
    {
        size_t argc = 2;
        napi_value argv[2];
        napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

        if (argc < 2) {
            break;
        }

        if (!AddonBaseOpt::IsString(env, argv[0]) ||
            !AddonBaseOpt::IsString(env, argv[1])) {
            break;
        }

        std::string str1;
        std::string str2;
        AddonBaseOpt::ParseString(env, argv[0], str1);
        AddonBaseOpt::ParseString(env, argv[1], str2);

        std::wstring wstr1;
        std::wstring wstr2;
        xl::text::transcode::UTF8_to_Unicode(str1.c_str(), str1.length(), wstr1);
        xl::text::transcode::UTF8_to_Unicode(str2.c_str(), str2.length(), wstr2);
        int ret = xmp_helper_common::CompareStr(wstr1, wstr2);
        AddonBaseOpt::PushInt32(env, &napi_result, ret);
    } while (0);
    return napi_result;
}

static napi_value ExportEmptyClipBoard(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    BOOL result = 0;
    if (OpenClipboard(NULL))
    {
        result = EmptyClipboard();
        DWORD err = ::GetLastError();
        CloseClipboard();
    }
    AddonBaseOpt::PushBool(env, &napi_result, result);
    return napi_result;
}

static napi_value ExportIsFilePlayable(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    bool result = false;
    do {
        size_t argc = 1;
        napi_value argv[1];
        napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

        if (argc < 1) {
            break;
        }

        std::string str;
        AddonBaseOpt::ParseString(env, argv[0], str);
        result = xl::path::IsPlayableExt(str);
    } while (false);
    AddonBaseOpt::PushBool(env, &napi_result, result);
    return napi_result;
}

static napi_value RefreshIcon(napi_env env, napi_callback_info info) {
    napi_value napi_result = nullptr;
    ::SHChangeNotify(SHCNE_ASSOCCHANGED, SHCNF_DWORD, NULL, NULL);
    return napi_result;
}

BOOL EnablePrivilege(HANDLE hProcess, LPCTSTR lpszName, BOOL fEnable)
{
    // Enabling the debug privilege allows the application to see
    // information about service applications
    BOOL fOk = FALSE; // Assume function fails
    HANDLE hToken;

    // Try to open this process's access token
    if (OpenProcessToken(hProcess, TOKEN_ADJUST_PRIVILEGES, &hToken))
    {
        // Attempt to modify the "Debug" privilege
        TOKEN_PRIVILEGES tp;
        tp.PrivilegeCount = 1;
        LookupPrivilegeValue(NULL, lpszName, &tp.Privileges[0].Luid);

        tp.Privileges[0].Attributes = fEnable ? SE_PRIVILEGE_ENABLED : 0;
        AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(tp), NULL, NULL);

        fOk = (GetLastError() == ERROR_SUCCESS);
        CloseHandle(hToken);
    }
    return (fOk);
}
BOOL DoShutDownMachine()
{
    if (!EnablePrivilege(::GetCurrentProcess(), SE_SHUTDOWN_NAME, TRUE))
    {
        return FALSE;
    }

    UINT uShutDownFlags = EWX_POWEROFF | EWX_SHUTDOWN | EWX_FORCE;

    //DWORD dwVersion = GetVersion();
    //DWORD dwMajorVersion = (DWORD)(LOBYTE(LOWORD(dwVersion)));
    //DWORD dwMinorVersion = (DWORD)(HIBYTE(LOWORD(dwVersion)));
    // 如果是win8以上环境，则加上EWX_HYBRID_SHUTDOWN标识，用于快速启动机器
    //if (dwMajorVersion >= 6 && dwMinorVersion >= 2)
    if (IsWindows8OrGreater())
    {
#define EWX_HYBRID_SHUTDOWN 0x00400000
        uShutDownFlags = uShutDownFlags | EWX_HYBRID_SHUTDOWN;
    }
#pragma warning(disable: 28159)
    return ExitWindowsEx(uShutDownFlags, 0);
#pragma warning(default: 28159)
}

//挂起计算机，挂起是系统将机器的硬盘、显示器等外部设备停止工作，而CPU、内存仍然工作，等待用户随时唤醒
//这里的挂起计算机相当于win7和vista系统下的睡眠
BOOL DoSuspendMachine()
{
    if (!EnablePrivilege(::GetCurrentProcess(), SE_SHUTDOWN_NAME, TRUE))
    {
        return FALSE;
    }

    return SetSuspendState(FALSE, TRUE, FALSE);
}

static napi_value ShutDownMachine(napi_env env, napi_callback_info info) {
    BOOL ret = DoShutDownMachine();
    napi_value napi_result = nullptr;
    AddonBaseOpt::PushBool(env, &napi_result, ret);
    return napi_result;
}

static napi_value SuspendMachine(napi_env env, napi_callback_info info) {
    BOOL ret = DoSuspendMachine();
    napi_value napi_result = nullptr;
    AddonBaseOpt::PushBool(env, &napi_result, ret);
    return napi_result;
}

static napi_value RebootMachine(napi_env env, napi_callback_info info) {
    BOOL ret = FALSE;
    if (EnablePrivilege(::GetCurrentProcess(), SE_SHUTDOWN_NAME, TRUE))
    {
        UINT flag = EWX_REBOOT;
        ret = ::ExitWindowsEx(flag, 0);
    }
    napi_value napi_result = nullptr;
    AddonBaseOpt::PushBool(env, &napi_result, ret);
    return napi_result;
}

static napi_value OpenURLByDefault(napi_env env, napi_callback_info info) {
    BOOL ret = 0;
    do
    {
        size_t argc = 1;
        napi_value argv[1];
        napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

        if (argc < 1) {
            break;
        }
        std::string str;
        AddonBaseOpt::ParseString(env, argv[0], str);
        std::wstring wstrUrl;
        xl::text::transcode::UTF8_to_Unicode(str.c_str(), str.size(), wstrUrl);

        wchar_t explorer[1024] = { 0 };
        DWORD cbSize = 0;
        LONG lRet = -1;
        if (IsWindows7OrGreater())
        {
            HKEY hKey;
            lRet = RegOpenKeyExW(HKEY_CURRENT_USER, L"Software\\Microsoft\\Windows\\Shell\\Associations\\UrlAssociations\\http\\UserChoice",
                0, KEY_QUERY_VALUE, &hKey);
            if (lRet == ERROR_SUCCESS)
            {
                wchar_t progid[1024] = { 0 };
                cbSize = 1024;
                lRet = RegQueryValueExW(hKey, L"Progid", NULL, NULL, (BYTE*)progid, &cbSize);
                RegCloseKey(hKey);
                if (lRet == ERROR_SUCCESS && cbSize != 0)
                {
                    std::wstring keyName = progid;
                    keyName = keyName + L"\\shell\\open\\command";
                    lRet = RegOpenKeyExW(HKEY_CLASSES_ROOT, keyName.c_str(), 0, KEY_QUERY_VALUE, &hKey);
                    if (lRet == ERROR_SUCCESS)
                    {
                        cbSize = 1024;
                        lRet = RegQueryValueExW(hKey, L"", NULL, NULL, (BYTE*)explorer, &cbSize);
                        RegCloseKey(hKey);
                    }
                }
            }
        }

        if (lRet != ERROR_SUCCESS)
        {
            HKEY hKey;
            lRet = RegOpenKeyExW(HKEY_CLASSES_ROOT, L"http\\shell\\open\\command", 0, KEY_QUERY_VALUE, &hKey);
            if (lRet == ERROR_SUCCESS)
            {
                cbSize = 1024;
                lRet = RegQueryValueExW(hKey, L"", NULL, NULL, (BYTE*)explorer, &cbSize);
                RegCloseKey(hKey);
            }
        }

        if (ERROR_SUCCESS == lRet)
        {
            std::wstring exp(explorer);
            std::wstring strParam;
            if (!exp.empty())
            {
                std::wstring::size_type pos = exp.find('"');
                if (std::wstring::npos != pos)
                {
                    exp = exp.substr(pos + 1);
                }
                pos = exp.find('"');
                if (std::wstring::npos != pos)
                {
                    strParam = exp.substr(pos + 1);
                    exp = exp.substr(0, pos);
                }

                pos = strParam.find(L"%1");
                if (std::wstring::npos != pos)
                {
                    strParam.replace(pos, 2, wstrUrl.c_str());
                }
                else
                {
                    strParam = wstrUrl;
                }

                if (xl::path::IsFileExist(exp))
                {
                    if ((int)ShellExecuteW(NULL, L"open", exp.c_str(), strParam.c_str(), NULL, SW_SHOWNORMAL) > 32) //浏览成功?
                    {
                        ret = 1;
                        break;
                    }
                }
            }
        }

        std::wstring strTemp = L"\"";
        strTemp += wstrUrl.c_str();
        strTemp += L"\"";

        //用IE 来尝试
        memset(explorer, 0, sizeof(explorer));
        if (::SHGetSpecialFolderPathW(::GetDesktopWindow(), explorer, CSIDL_PROGRAM_FILES, FALSE))
        {
            wcsncat(explorer, L"\\Internet Explorer\\iexplore.exe", 64);
            if ((int)ShellExecuteW(NULL, L"open", explorer, strTemp.c_str(), NULL, SW_SHOWNORMAL) > 32) //浏览成功?
            {
                ret = 1;
                break;
            }
        }
    } while (false);
    napi_value napi_result = nullptr;
    AddonBaseOpt::PushBool(env, &napi_result, ret);
    return napi_result;
}

// 通知启动动画进程退出
#define XL_WINDOW_THUNDERSTARTCOMMAND_NAME		L"XLStartCommandWindow"
#define XL_WM_COMMANDQUIT						(WM_USER + 0x301)
static napi_value EndThunderStartProcess(napi_env env, napi_callback_info info) {
    HWND hwndCommand = ::FindWindowW(XL_WINDOW_THUNDERSTARTCOMMAND_NAME, XL_WINDOW_THUNDERSTARTCOMMAND_NAME);
    if (hwndCommand != NULL)
    {
        BOOL ret = ::PostMessage(hwndCommand, XL_WM_COMMANDQUIT, 0, (LPARAM)0);
        DWORD dwErr = ::GetLastError();
    }
    return nullptr;
}

std::string GetIEProxyInfo()
{
    std::string ret = "";
    WINHTTP_CURRENT_USER_IE_PROXY_CONFIG proxy_config;
    if (::WinHttpGetIEProxyConfigForCurrentUser(&proxy_config))
    {
        if (proxy_config.lpszProxy != NULL)
        {
            xl::text::transcode::Unicode_to_UTF8(proxy_config.lpszProxy, wcslen(proxy_config.lpszProxy), ret);
        }
    }

    return ret;
}
static napi_value GetIEProxy(napi_env env, napi_callback_info info) {
    std::string proxy_info = GetIEProxyInfo();
    napi_value napi_result = nullptr;
    napi_create_string_utf8(env, proxy_info.c_str(), proxy_info.length(), &napi_result);
    return napi_result;
}

static napi_value GetFreeTime(napi_env env, napi_callback_info info) {
    ULONGLONG free_time = 0;

    typedef BOOL(__stdcall* MyGetLastInputInfo)(PLASTINPUTINFO);
    MyGetLastInputInfo GetLastInputInfo;
    LASTINPUTINFO stInputInfo;
    stInputInfo.cbSize = sizeof(LASTINPUTINFO);
    do
    {
        HMODULE hModule = NULL;
        wchar_t  dll_path[MAX_PATH];
        DWORD  buf_count = MAX_PATH;
        int ret = ::GetSystemDirectoryW(dll_path, buf_count);
        if (ret == 0)
        {
            DWORD err = ::GetLastError();
            hModule = ::LoadLibraryW(L"User32.dll");
        }
        else
        {
            ::PathAppendW(dll_path, L".\\User32.dll");
            hModule = ::LoadLibraryExW(dll_path, NULL, LOAD_WITH_ALTERED_SEARCH_PATH);
        }

        GetLastInputInfo = (MyGetLastInputInfo)GetProcAddress(hModule, "GetLastInputInfo");
        if (GetLastInputInfo == NULL)
        {
            break;
        }

        if (!GetLastInputInfo(&stInputInfo))
        {
            break;
        }
        free_time = (::GetTickCount64() - stInputInfo.dwTime) / 1000;

    } while (0);
    napi_value napi_result = nullptr;
    AddonBaseOpt::PushInt64(env, &napi_result, free_time);
    return napi_result;
}


static napi_value GetCursorPosition(napi_env env, napi_callback_info info) {
    POINT pt;
    BOOL bRet = ::GetCursorPos(&pt);

    napi_value obj = nullptr;
    if (bRet)
    {
        napi_create_object(env, &obj);
        AddonBaseOpt::PushObjectInt64(env, obj, "x", pt.x);
        AddonBaseOpt::PushObjectInt64(env, obj, "y", pt.y);
    }
    else
    {
        napi_create_object(env, &obj);
        AddonBaseOpt::PushObjectInt64(env, obj, "x", 0);
        AddonBaseOpt::PushObjectInt64(env, obj, "y", 0);
    }
    return obj;
}

static napi_value TsShowCursor(napi_env env, napi_callback_info info) {
    bool show = true;
    size_t argc = 1;
    napi_value argv[1];
    napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

    if (argc >= 1) {
        AddonBaseOpt::ParseBool(env, argv[0], show);
    }
    ::ShowCursor(show);
    return nullptr;
}


void InitPlatformApi(napi_env env, napi_value exports) {
#if (NTDDI_VERSION >= NTDDI_VISTA)
    DwmIsCompositionEnabled(&g_enableDwm);
#else
    g_enableDwm = false;
#endif // (NTDDI_VERSION >= NTDDI_VISTA)
    napi_property_descriptor desc[] = {
        {"readRegString", nullptr, ReadRegString, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"writeRegValue", nullptr, WriteRegValue, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"deleteRegKey", nullptr, DeleteRegKey, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"deleteRegValue", nullptr, DeleteRegValue, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"createRegKey", nullptr, CreateRegKey, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"createPlayerWnd", nullptr, CreatePlayerWnd, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"bindWnd", nullptr, BindWnd, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setPlayerWndVisible", nullptr, SetPlayerWndVisible, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"createSnapshotWnd", nullptr, CreateSnapshotWnd, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getPeerId", nullptr, GetPeerId, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setCommandLineCallback", nullptr, SetCommandLineCallback, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"bringWndToTop", nullptr, BringWndToTop, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setForegroundWindow", nullptr, SetForegroundWindow, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getFileVersion", nullptr, GetFileVersion, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getInstallChannel", nullptr, GetInstallChannel, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getPublicUserDataPath", nullptr, GetPublicUserDataPath, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getAppDataLocalLowPath", nullptr, GetAppDataLocalLowPath, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getSystemDirectory", nullptr, ExportGetSystemDirectory, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"readINI", nullptr, ReadINI, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"writeINI", nullptr, WriteINI, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getDmideCode", nullptr, GetDmideCode, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getCommandLine", nullptr, GetExeCommandLine, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"invalidPlayerWnd", nullptr, InvalidPlayerWnd, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getFreePartitionSpace", nullptr, ExportGetFreePartitionSpace, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getDriveType", nullptr, ExportGetDriveType, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getPartitionSpace", nullptr, ExportGetPartitionSpace, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getLogicalDriveStrings", nullptr, ExportGetLogicalDriveStrings, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"terminateProcess", nullptr, ExportTerminateProcess, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"startAutoUpdate", nullptr, StartAutoUpdate, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"startLocalUpdate", nullptr, StartLocalUpdate, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"isAutoRun", nullptr, IsAutoRun, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setAutoRun", nullptr, SetAutoRun, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"startManaulUpdate", nullptr, StartManaulUpdate, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setScreenSaveActive", nullptr, ExportSetScreenSaveActive, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"shellExecute", nullptr, XlShellExecEx, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"asyncShellExecute", nullptr, AsyncShellExecute, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"associate", nullptr, Associate, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"addFloatWinodwShowRect", nullptr, AddFloatWinodwShowRect, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"delFloatWinodwShowRect", nullptr, DelFloatWinodwShowRect, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getDPIAwareSupport", nullptr, GetDPIAwareSupport, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getMonitorDPIFactor", nullptr, GetMonitorDPIFactor, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getSysDPIFactor", nullptr, GetSysDPIFactor, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getWindowRect", nullptr, ExportGetWindowRect, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setWindowPos", nullptr, ExportSetWindowPos, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"showWindow", nullptr, ExportShowWindow, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getNextWindow", nullptr, ExportGetNextWindow, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"compareStr", nullptr, ExportCompareStr, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"emptyClipBoard", nullptr, ExportEmptyClipBoard, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"isFilePlayable", nullptr, ExportIsFilePlayable, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"refreshIcon", nullptr, RefreshIcon, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"shutDownMachine", nullptr, ShutDownMachine, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"suspendMachine", nullptr, SuspendMachine, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"rebootMachine", nullptr, RebootMachine, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"openURLByDefault", nullptr, OpenURLByDefault, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"endThunderStartProcess", nullptr, EndThunderStartProcess, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getIEProxy", nullptr, GetIEProxy, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getFreeTime", nullptr, GetFreeTime, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getCursorPos", nullptr, GetCursorPosition, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"showCursor", nullptr, TsShowCursor, nullptr, nullptr, nullptr, napi_default, nullptr},
    };
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
}