import axios from 'axios'

import { PopUpNS } from '@root/common/pop-up'
import * as PopUpTypes from '@root/common/pop-up/types'
import { ApplicationManager } from '@root/common/thunder-pan-manager/manager/application'
import { getFileExtension } from '@root/common/thunder-pan-manager/pan-sdk/utils/drive'

import * as DownloadKernel from '../base'
import { DkThunderHelper } from './dk-thunder-helper'
import { ParseUrlFileNameNS } from './parse-helper'

let threadCount: number = 5

export namespace ThunderNewTaskHelperNS {
  export const taskOptFileNameFixed: number = 0x2 // 文件名固定，不允许下载库改变

  /**
   * 下载路径类型枚举
   */
  export enum DownloadPathType {
    Local = 'local',
    Cloud = 'cloud',
  }

  export interface INewTaskData {
    url: string
    cookie?: string
    referer?: string
    fileName?: string
    browser?: string
    statClick?: string
    specialType?: number
    fileSize?: number
    fileType?: string
    fileHash?: string
    subDir?: string
    gcid?: string
    cid?: string
    thread?: number
    nameFixedFlag?: number
    birdkeyChars?: string
    searchFrom?: string
    clickFrom?: string
    userAgent?: string
    taskType?: DownloadKernel.TaskType
    userdata?: { callbackId: number; processId: number } // 12.1.8添加的字段，支持ThunderAgent传递自定义字段，内容为JSON.stringify()的字符串
    taskIcon?: string
    httpHeaderField?: string // 设置http头的 Authorization
  } // 这个字段用于专用链excludePath对应的子文件保存的目录

  export interface IP2spTaskSetting {
    loginFtp?: boolean // 登陆ftp服务器
    ftpInfo?: ParseUrlFileNameNS.IUrlInfo
    onlyOrigin?: boolean // 只从原始地址下载
    thread?: number // 原始地址线程数
    note?: string // 注释
    openAfterDownload?: boolean // 下载完成后打开
  }

  export interface ICreateShortcut {
    name?: string
    targetFile?: string
    runParams?: string
    startIn?: string // 启动时的生效路径
  }

  export interface ICloudTaskExtra {
    user_id: string
    id: string
    save_id: string
  }

  export interface ISingleTaskUI {
    taskType?: DownloadKernel.TaskType
    data?: INewTaskData
    setting?: IP2spTaskSetting
    selected?: boolean
    index?: number
    cloudInfo?: ICloudTaskExtra
  }

  export interface IResponseP2sp {
    result?: string
    gcid?: string
    fileSize?: number
    errCode?: number
  }

  interface IRemoteUrlDataInfoResponse {
    code: number
    file_size: number
    gcid: string
    result: string
  }

  export interface ITaskExtData {
    checkedFileIndexes?: string[];  // 选中的文件索引
    fileName?: string;              // 自定义文件名
  }

  /**
   * 通过服务接口获取 p2p 任务文件的大小、gcid 等相关信息
   * @param url
   * @returns
   */
  export async function getRemoteUrlDataInfo(url: string): Promise<IResponseP2sp> {
    try {
      if (await ParseUrlFileNameNS.isThunderPrivateUrl(url)) {
        url = await ParseUrlFileNameNS.parseThunderPrivateUrl(url)
      }
      if (url.indexOf('ed2k://') === 0 && url.indexOf('|') === -1) {
        url = url.replace(/%7C/g, '|')
      }

      const reqUrl = `https://api-shoulei-ssl.xunlei.com/xlppc.resinfo.api/v1/queryresinfo?type=url&res=${url}`
      const res = await axios(reqUrl, {
        method: 'GET',
        headers: {
          'content-type': 'application/x-www-form-urlencoded',
          'x-client-id': ApplicationManager.getCurrentDeviceClientId(),
          'x-device-id': ApplicationManager.getCurrentDeviceDeviceId(),
          'x-client-version-code': ApplicationManager.getCurrentDeviceClientVersion(),
        },
      })

      if (res.status === 200) {
        const response = res.data as IRemoteUrlDataInfoResponse
        console.log('response111', response)
        if (response) {
          return {
            result: response.result,
            fileSize: response.file_size,
            gcid: response.gcid,
            errCode: response.code,
          }
        }
      }
      return {}
    } catch {
      return {}
    }
  }

  export async function constructTaskByUrl(
    url: string,
    source: string,
    taskType: DownloadKernel.TaskType,
    birdkeyChars?: string
  ): Promise<ISingleTaskUI> {
    const data: INewTaskData = await getNewTaskDataByUrl(url, source, taskType, birdkeyChars)

    let nThread: number = 5
    if (data.thread !== null && data.thread !== undefined) {
      nThread = data.thread
    } else {
      nThread = threadCount
    }
    const ret: ISingleTaskUI = {
      taskType: taskType,
      data: data,
      setting: {
        loginFtp: false,
        ftpInfo: { userName: '', password: '' },
        onlyOrigin: false,
        thread: nThread,
        note: '',
        openAfterDownload: false,
      },
      selected: true,
    }
    return ret
  }

  export async function getNewTaskDataByUrl(
    url: string,
    source?: string,
    taskType?: DownloadKernel.TaskType,
    birdkeyChars?: string
  ): Promise<INewTaskData> {
    if (taskType === undefined) {
      taskType = await DkThunderHelper.getTaskTypeFromUrl(url)
    }

    let ret: INewTaskData | undefined = undefined
    let fileSize: number | undefined = 0
    let fileType: string | undefined = ''
    let fileHash: string | undefined = ''
    do {
      if (taskType !== DownloadKernel.TaskType.Emule && taskType !== DownloadKernel.TaskType.P2sp) {
        break
      }
      const fileName: string = await ParseUrlFileNameNS.getNameFromUrl(url)
      const ext: string = getFileExtension(fileName)
      if (ext !== '') {
        fileType = ext.substring(1)
      }
      if (taskType === DownloadKernel.TaskType.Emule) {
        const ed2kInfo: ParseUrlFileNameNS.IEd2kUrlInfo = await ParseUrlFileNameNS.parseEd2kUrl(url)
        fileSize = ed2kInfo.fileSize
        fileHash = ed2kInfo.fileHash
      }
      source = source === undefined ? '' : source
      ret = {
        url: url,
        cookie: '',
        referer: '',
        fileName: fileName,
        browser: '',
        statClick: source,
        fileSize: fileSize,
        fileHash: fileHash,
        fileType: fileType,
        birdkeyChars: birdkeyChars,
      }
      break
    } while (0)

    return ret!
  }

  export function getNewTaskTrackFrom(str: string, isBt: boolean) {
    switch (str) {
      case 'manual': {
        if (isBt) return 'add_bt'
        return 'add_link'
      }
      case 'plate':
        return 'plate'
      case 'browser':
        return 'browser'
      default:
        return str
    }
  }

  export interface IShowPreNewTaskWindowOptions {
    windowWidth?: number
    windowHeight?: number
    relatePos?: PopUpTypes.RelatePosType
    parentId?: number
    saveCloudPath?: string
    saveLocalPath?: string
    selectedPathType?: DownloadPathType
    defaultCheckedFileIndexes?: string[]
    extData?: Record<string, ITaskExtData> // key 是 url，value 是该 url 对应的扩展数据
    autoExpandAll?: boolean // 是否自动展开所有文件
    title?: string // 窗口标题
    showLaterButton?: boolean // 是否显示稍后按钮
    showPlayButton?: boolean // 是否显示播放按钮
  }

  export type IShowNewTaskWindowOptions = IShowPreNewTaskWindowOptions

  export async function showNewTaskWindow(
    options?: IShowNewTaskWindowOptions
  ): Promise<PopUpTypes.ResolvePayload> {
    const defaultOptions = {
      parentId: -1, // 传递-1，表示不以主窗口为父窗口
      windowWidth: 680,
      windowHeight: 316,
      relatePos: PopUpTypes.RelatePosType.CenterParent,
      replaceView: false, // 该值设置为true，则如果已经存在窗口，则更新数据到当前窗口
      singleton: false, // 关键：确保只有一个创建任务窗口
    }

    const finalOptions = { ...defaultOptions, ...options }

    return await PopUpNS.popup('CreateTask', finalOptions, { resizable: false, alwaysOnTop: true })
  }

  /**
   * 创建任务窗口（单例模式）
   * 如果创建任务窗口已存在，则不会重复创建，而是激活现有窗口
   * @param taskData 任务数据数组
   * @param options 可选的窗口配置
   * @returns Promise<PopUpTypes.ResolvePayload>
   */
  export async function showPreCreateTaskWindow(
    taskData: INewTaskData[],
    options?: IShowPreNewTaskWindowOptions,
  ): Promise<PopUpTypes.ResolvePayload> {

    // 获取当前窗口作为父窗口
    const currentWindow = PopUpNS.getCurrentWindow()
    const parentId = currentWindow?.id || -1
    
    console.log('📋 [showPreCreateTaskWindow] 当前窗口信息:', {
      currentWindow: currentWindow,
      parentId: parentId,
      currentWindowId: currentWindow?.id
    })
    
    const defaultOptions = {
      parentId: parentId, // 使用当前窗口作为父窗口
      replaceView: true, // 该值设置为true，则如果已经存在窗口，则更新数据到当前窗口
      singleton: true, // 关键：确保只有一个创建任务窗口
      taskData,
      // 固定窗口尺寸，避免动态调整导致的抖动
      windowWidth: 680,
      windowHeight: 160, // 使用传入的高度或默认高度
      // 使用固定位置，避免居中计算导致的抖动
      relatePos: PopUpTypes.RelatePosType.CenterParent,
      // 禁用自动大小调整
      autoSize: false,
      // 初始不显示，等组件完全加载后再显示
      show: false,
    }

    const finalOptions = { ...defaultOptions, ...options }

    // 优化的窗口配置，减少抖动
    const dialogConf = {
      resizable: false, // 禁用窗口大小调整
      alwaysOnTop: true, // 保持置顶
      show: false, // 初始不显示
      // 设置最小和最大尺寸，防止意外调整
      minWidth: 680,
      maxWidth: 680,
      // 禁用最大化最小化
      maximizable: false,
      minimizable: false,
      // 设置窗口边框样式
      frame: false,
      // 设置背景色，避免透明导致的闪烁
      // backgroundColor: '#ffffff',
      // 禁用动画，减少视觉抖动
      // skipTaskbar: false,
      // 设置窗口类型
      // type: 'toolbar',
    }

    console.log('🎯 [showPreCreateTaskWindow] 窗口配置:', {
      dialogConf: dialogConf,
      dialogConfKeys: Object.keys(dialogConf),
      showValue: dialogConf.show,
      alwaysOnTop: dialogConf.alwaysOnTop
    })

    console.log('🔄 [showPreCreateTaskWindow] 准备调用 PopUpNS.popup，参数:', {
      componentName: 'PreCreateTask',
      options: finalOptions,
      dialogConf: dialogConf,
      dialogConfKeys: Object.keys(dialogConf),
      showValue: dialogConf.show,
      alwaysOnTop: dialogConf.alwaysOnTop
    })

    try {
      // 调用 PopUpNS.popup 前的状态检查
      console.log('🔍 [showPreCreateTaskWindow] 调用前状态检查:', {
        processType: process.type,
        isRenderer: process.type === 'renderer',
        currentWindowExists: !!currentWindow,
        parentIdValid: typeof parentId === 'number' && parentId > 0
      })

      const result = await PopUpNS.popup('PreCreateTask', finalOptions, dialogConf)
      
      console.log('✅ [showPreCreateTaskWindow] PopUpNS.popup 调用成功，返回结果:', {
        result: result,
        action: result?.action,
        args: result?.args,
        resultType: typeof result
      })

      return result
    } catch (error) {
      console.error('❌ [showPreCreateTaskWindow] PopUpNS.popup 调用失败:', {
        error: error,
        errorMessage: (error as Error)?.message,
        errorStack: (error as Error)?.stack,
        componentName: 'PreCreateTask',
        options: finalOptions,
        dialogConf: dialogConf
      })
      throw error
    }
  }

  /**
   * 创建任务窗口（单例模式）- 用于磁力链接和其他任务类型
   * 如果创建任务窗口已存在，则不会重复创建，而是激活现有窗口
   * @param taskData 任务数据数组
   * @param options 可选的窗口配置
   * @returns Promise<PopUpTypes.ResolvePayload>
   */
  export async function showPreLinkCreateTaskWindow(
    taskData: INewTaskData[],
    options?: IShowPreNewTaskWindowOptions
  ): Promise<PopUpTypes.ResolvePayload> {
    return await showPreCreateTaskWindow(taskData, options)
  }
}
