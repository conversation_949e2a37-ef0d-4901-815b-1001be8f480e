<script setup lang="ts">
import { computed, ref } from 'vue'
import Progress from '@root/common/components/ui/progress/index.vue'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import { IBranch, ILeaf } from '@/types/taskDetails'
import { ThunderUtil } from '@root/common/utils'
import * as BaseType from '@root/common/task/base'
import { useTaskErrorCode } from '@/stores/taskErrorCode'
import { divideAndFormat } from '../../utils'
const taskErrorCode = useTaskErrorCode()
// BtFileDownloadInfo
const props = withDefaults(defineProps<{
  task: IBranch | ILeaf,
  currentTaskIsDown: boolean,
}>(), {})

const emits = defineEmits<{
  (e: 'customFile', id: number):void // 播放
  (e: 'right-click', event: MouseEvent, file: IBranch | ILeaf): void
}>()

/*** 是否error任务 */
const isErrorTask = computed(() => {
  return isBranchFail.value || isLeafFail.value
})

const isBranchFail = computed(() => {
  if (isBranch.value) {
    return props.task?.data?.downFailNum > 0
  }
  return false
})

const isLeafFail = computed(() => {
  if (isTask.value) {
    return props.task?.data.status === BaseType.TaskStatus.Failed
  } else {
    return props.task?.data?.status === BaseType.BtSubFileStatus.Failed
  }
})

const isTask = computed(() => {
  return props.task.data?.taskId
})

const isBranch = computed(() => {
  return props.task.type === 'branch'
})

const isCompleted = computed(() => {
  return props.task.data?.isComplete
})

// const isDownloading = computed(() => {
//   const fileStatus = props.task.data?.status
//   if (isTask.value) {
//     return fileStatus === BaseType.TaskStatus.Started
//   } else {
//     return fileStatus === BaseType.BtSubFileStatus.Downloading || fileStatus === BaseType.BtSubFileStatus.Waiting
//   }
// })

const calculatePer = computed(() => {
  return divideAndFormat(props.task.data.downloadSize, props.task.data.fileSize, 2)
})



const taskIcon = computed(() => {
  if (props.task.type === 'branch' || props.task.type === 'bt') {
    return TaskUtilHelper.getTaskIcon('', BaseType.TaskType.Group)
  }
  return TaskUtilHelper.getTaskIcon(props.task.name, props.task.data?.taskType)
})

// const taskSize = computed(() => {
//   if ('branch' === props.task.type || 'bt' === props.task.type) {
//     return `已下载 ${ props.task.data.completedNum }/${ props.task.data?.downloadTotal }`
//   } else if ('leaf' === props.task.type) {
//     return ThunderUtil.bytesToSize(props.task.data.fileSize, 2)
//   }
//   return ''
// })

const taskSize = computed(() => {
  if ('leaf' === props.task.type) {
    return ThunderUtil.bytesToSize(props.task.data.fileSize, 2)
  }
  return ''
})

const downFailNum = computed(() => {
  return props.task.data?.downFailNum || 0
})

const taskDownDesc = computed(() => {
  if ('branch' === props.task.type || 'bt' === props.task.type) {
    return `已下载 ${ props.task.data.completedNum }/${ props.task.data?.downloadTotal }`
  } else if ('leaf' === props.task.type) {
    if (isCompleted.value) {
      return ''
    }
    return `已下载 ${calculatePer.value}%`
  }
})

const infoText = computed(() => {
  if (isErrorTask.value) {
    if (isBranch.value) {
      if (downFailNum.value > 0) {
        if (downFailNum.value === props.task.data?.downloadTotal) {
          return '下载失败'
        }
        return `${downFailNum.value}个文件下载失败`
      }
    }
    let errCode = props.task.data?.errCode || 0
    return taskErrorCode.getTaskErrorInfoByCode(errCode) || '下载失败'
  }
  return ''
})

const handleContextMenu = (event: MouseEvent) => {
  emits('right-click', event, props.task)
}

</script>

<template>
  <div
    class="detail-task-item"
    @click.right.stop="handleContextMenu($event)"
  >
    <div class="detail-task-item__pic">
      <div class="file-icon-type" :class="taskIcon"></div>
    </div>
    <div class="detail-task-item__right">
      <p class="detail-task-item__name" v-tooltip="task.name">
        {{ task.name }}
      </p>
      <div class="detail-task-item__info">
        <div
          v-if="!isLeafFail"
          class="detail-task-item__status"
        >
          <i v-if="isCompleted" class="xl-icon-finish"></i>
          <Progress
            v-else
            type="circle"
            :width="16"
            :height="16"
            :percentage="calculatePer"
            :strokeWidth="1.5"
          />
        </div>
        <div v-if="taskSize" class="detail-task-item__size">{{ taskSize }}</div>
        <div v-if="!isLeafFail && taskDownDesc" class="detail-task-item__desc">{{ taskDownDesc }}</div>
        <div v-if="isErrorTask" class="detail-task-item__text" :class="{'is-error': isErrorTask}">
          <i class="xl-icon-general-details-m"></i>
          <span class="error-text">{{ infoText }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.detail-task-item {
  height: 80px;
  background: var(--background-background-elevated);
  border-radius: var(--border-radius-M2);
  display: flex;
  padding: 0 12px;
  align-items: center;
  cursor: pointer;
  margin-top: 4px;
  &:first-of-type {
    margin-top: 0px;
  }
  
  &:hover {
    background: var(--fill-fill-3);
  }
  &__pic {
    flex-shrink: 0;
    width: 98px;
    height: 56px;
    border-radius: var(--border-radius-S);
    background: rgba(254, 245, 238, 1);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__right {
    margin-left: 16px;
  }
  &__name {
    color: var(--font-font-1, rgba(39, 46, 59, 1));
    font-size: 13px;
    line-height: 22px;
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 1;
    -webkit-line-clamp: 1;
  }
  &__info {
    display: flex;
    align-items: center;
    color: var(--font-font-3);
    font-size: 12px;
    margin-top: 4px;
  }
  &__size {
    flex-shrink: 0;
    margin-left: 4px;
    margin-right: 8px;
  }
  &__desc {
    flex-shrink: 0;
    margin-left: 4px;
    margin-right: 8px;
  }
  &__status {
    flex-shrink: 0;
    height: 16px;
    width: 16px;
    color: var(--functional-success-default);
    // &.is-error {
    //   color: var(--functional-error-default);
    // }
  }
  &__text {
    // margin-left: 8px;
    display: flex;
    align-items: center;
    &.is-error {
      color: var(--functional-error-default);
    }
    .error-text {
      display: -webkit-box;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      line-clamp: 1;
      -webkit-line-clamp: 1;
      margin-left: 4px;
    }
  }
}
</style>