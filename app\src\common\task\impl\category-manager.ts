import * as BaseType from '../base'
import {Category} from './category'

export class CategoryManager {
  private nativeCategoryManager: any;
  // �Ƿ��Ѿ�load���
  private isLoaded: boolean = false;
  private categorys: Map<number, Category> = new Map();

  constructor(nativeCategoryManager: any) {
    this.nativeCategoryManager = nativeCategoryManager;
  }

  // 获取分类，默认下载-1
  public async getCategoryById(id: number): Promise<Category | undefined> {
    await this.waitLoadFinish();
    if (this.categorys.has(id)) {
      return this.categorys.get(id);
    }
    let category = this.nativeCategoryManager.getCategoryById(id);
    if (!category) {
      return undefined;
    }
    let newCategory = new Category(id, category);
    this.categorys.set(id, newCategory);

    return newCategory;
  }

  // 设置用户信息，如果当前用户没有分类信息，会新生成分类信息
  public setCurrentPanUserId(userId: string): void {
    this.nativeCategoryManager.setCurrentPanUserId(userId);
  }

  // 获取云盘所在的分类
  public async getCurrentPanCategory(): Promise<Category | undefined> {
    let id = await new Promise((v: (id: number)=>void) => {
      this.nativeCategoryManager.getCurrentPanCategoryId((id: number) => {
        v(id);
      });
    });

    if (id === BaseType.FaultCategoryId) {
      return undefined;
    }

    return await this.getCategoryById(id);
  }

  // 任务插入事件
  // cb:回调函数
  //  categoryId:分类id
  //  viewId：分类的view
  //  reason: 插入的原因
  //  ids:插入的任务
  public attachInsertEvent(cb: (categoryId: number, viewId: BaseType.CategoryViewID, reason: BaseType.TaskInsertReason, ids: number[]) => void): number {
    return this.nativeCategoryManager.attachInsertEvent(cb);
  }

  public detachInsertEvent(cookie: number): void {
    return this.nativeCategoryManager.detachInsertEvent(cookie);
  }

  // 任务删除回调，参数解释同attachInsertEvent
  public attachRemoveEvent(cb: (categoryId: number, viewId: BaseType.CategoryViewID, ids: number[]) => void): number {
    return this.nativeCategoryManager.attachRemoveEvent(cb);
  }

  public detachRemoveEvent(cookie: number): void {
    this.nativeCategoryManager.detachRemoveEvent(cookie);
  }

  private async waitLoadFinish(): Promise<void> {
    if (this.isLoaded) {
      return;
    }
    await new Promise((v: (p: boolean)=>void) => {
      this.nativeCategoryManager.waitLoadFinish(() => {
        this.isLoaded = true;
        v(true);
      });
    });
  }
}