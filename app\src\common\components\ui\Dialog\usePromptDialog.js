import { h, ref, render, watch } from 'vue'

import Prompt from './Prompt.vue'

/**
 * PromptDialog 编程式API
 * 专门用于处理输入对话框的功能
 */
export function usePromptDialog() {
  // 当前打开的对话框DOM容器
  let container = null

  /**
   * 打开输入对话框
   * @param {Object} options 配置选项
   * @param {string} options.title 标题
   * @param {string} options.placeholder 输入框占位符
   * @param {string} options.defaultValue 默认值
   * @param {string} options.hint 提示
   * @param {Function} options.validator 验证函数
   * @param {boolean} options.validateOnInput 是否在输入时实时验证（默认false）
   * @param {boolean} options.modal 是否模态窗（默认false）
   * @param {boolean} options.draggable 是否窗体可拖动（默认false）
   * @param {boolean} options.disableHeaderDraggable 是否标题栏可拖动（默认false）
   * @param {string} options.variant 变体
   * @param {Object|string} options.inputStyle 自定义输入框样式（对象或CSS字符串）
   * @param {string} options.inputClass 自定义输入框CSS类名
   * @param {Object|string} options.containerStyle 自定义容器样式
   * @param {string} options.containerClass 自定义容器CSS类名
   * @param {boolean} options.fixedHeight 输入框是否固定高度
   * @param {boolean} options.selectAll 是否全部选中
   * @param {string} options.inputType 输入框类型（默认 'text'）
   * @param {Object} options.inputProps 额外的输入框属性
   * @param {Function} options.onChange 输入内容变化时的回调函数 (value, event) => void
   * @param {Function} options.onConfirm 确认按钮点击时的异步回调函数 (value) => Promise<boolean> | boolean
   * @returns {Promise<string|false>} 返回输入的值或false
   */
  function prompt(options = {}) {
    return new Promise(resolve => {
      // 创建DOM容器
      if (!container) {
        container = document.createElement('div')
        document.body.appendChild(container)
      }

      const visible = ref(true)
      const inputValue = ref(options.defaultValue || '')
      const errorMessage = ref('')
      const isLoading = ref(false)

      // 验证函数
      const validate = value => {
        if (options.validator) {
          return options.validator(value)
        }
        return { valid: true, message: '' }
      }

      const handleConfirm = () => {
        // 现在确认逻辑在 Prompt.vue 中处理，这里只需要关闭对话框
        visible.value = false
        resolve(inputValue.value)
        setTimeout(() => unmount(), 300)
      }

      const handleValidationError = message => {
        errorMessage.value = message
      }

      const handleLoadingStart = () => {
        isLoading.value = true
      }

      const handleLoadingEnd = () => {
        isLoading.value = false
      }

      const handleCancel = () => {
        visible.value = false
        resolve(false)
        setTimeout(() => unmount(), 300)
      }

      const handleClose = () => {
        visible.value = false
        resolve(false)
        setTimeout(() => unmount(), 300)
      }

      const handleUpdateOpen = value => {
        visible.value = value
        if (!value) {
          resolve(false)
          setTimeout(() => unmount(), 300)
        }
      }

      const handleInputChange = (value, event) => {
        inputValue.value = value

        // 调用用户的 onChange 回调
        if (options.onChange) {
          options.onChange(value, event)
        }

        // 如果启用了实时验证
        if (options.validateOnInput) {
          const result = validate(value)
          errorMessage.value = result.valid ? '' : result.message
        } else {
          // 清除错误信息
          if (errorMessage.value) {
            errorMessage.value = ''
          }
        }
      }

      const unmount = () => {
        if (container) {
          render(null, container)
          document.body.removeChild(container)
          container = null
        }
      }

      // 创建响应式渲染函数来处理状态变化
      const renderDialog = () => {
        const vnode = h(Prompt, {
          // Dialog 相关 props
          title: options.title || '输入',
          variant: options.variant || 'thunder',
          confirmText: options.confirmText || '确定',
          cancelText: options.cancelText || '取消',
          open: visible.value,
          loading: isLoading.value,
          modal: options.modal,
          draggable: options.draggable,
          disableHeaderDraggable: options.disableHeaderDraggable,

          // 输入框相关 props
          modelValue: inputValue.value,
          placeholder: options.placeholder || '',
          inputType: options.inputType || 'text',
          inputStyle: options.inputStyle,
          inputClass: options.inputClass || '',
          containerStyle: options.containerStyle,
          containerClass: options.containerClass || '',
          inputProps: options.inputProps || {},
          fixedHeight: options.fixedHeight,

          // 验证和提示
          errorMessage: errorMessage.value,
          currentValue: options.currentValue || '',
          hint: options.hint || '',
          selectAll: options.selectAll !== false,

          // 回调函数 props
          onConfirmCallback: options.onConfirm,
          validator: options.validator,

          // 事件处理
          onConfirm: handleConfirm,
          onCancel: handleCancel,
          onClose: handleClose,
          'onUpdate:open': handleUpdateOpen,
          'onUpdate:modelValue': value => {
            inputValue.value = value
          },
          'onInput-change': handleInputChange,
          onValidationError: handleValidationError,
          onLoadingStart: handleLoadingStart,
          onLoadingEnd: handleLoadingEnd,
        })

        render(vnode, container)
      }

      // 监听状态变化并重新渲染，确保异步 loading 状态能正确更新
      watch([visible, isLoading, inputValue, errorMessage], renderDialog, { immediate: true })
    })
  }

  // 返回可用方法
  return {
    prompt,
  }
}

export default usePromptDialog
