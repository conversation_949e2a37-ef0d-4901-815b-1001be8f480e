/* 7zMem.h -- MemStream
2013-06-27 : <PERSON> : Public domain */
#ifndef __7Z_MEM_H
#define  __7Z_MEM_H

#ifdef _WIN32
#define USE_WINDOWS_FILE
#endif

#ifdef USE_WINDOWS_FILE
#include <windows.h>
#else
#include <stdio.h>
#endif

#include "c\\Types.h"

EXTERN_C_BEGIN

/*----------MemStruct----------------*/
typedef struct
{
	BYTE* pstart;
	BYTE* pcurrent;
	UINT64 size; 
}CSzMem;
WRes Mem_Init(CSzMem *p, void* const pdata, UINT64 size);

WRes Mem_Read(CSzMem *p, void *data, size_t *size);

WRes Mem_Seek(CSzMem *p, Int64 *pos, ESzSeek origin);

/*----------MemInStream--------------*/
typedef struct  
{
	ISeekInStream s;
	CSzMem m;
}CMemInStream;

void MemInStream_CreateVTable(CMemInStream *p);

SRes MemInStream_Init(CMemInStream *p,  const void* pdata, UINT64 size);

/*---------ResourceInStream-----------*/
typedef struct 
{
	CMemInStream m;
	short lock;
	HGLOBAL data;
	HRSRC res;
}CResInStream;

SRes ResInStream_Init(CResInStream *p, WORD resid, TCHAR* restype);

void ResInStream_Uninit(CResInStream *p);


EXTERN_C_END

#endif