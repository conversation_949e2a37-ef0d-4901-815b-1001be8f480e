.td-table__filter-header-wrapper {
  padding: 10px 16px;
  border-bottom: 1px solid #e6e6e6;
}

.td-table__filter-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.td-table__selection-info {
  display: flex;
  align-items: center;
  width: 100%;
}

.td-table__selection-filter {
  margin-left: auto;
}

.selection-text {
  margin-right: 8px;
  font-weight: 500;
}

.selection-count {
  margin-left: 12px;
  color: var(--font-font-1, #272e3b);
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
}

.td-table__filter-options {
  display: flex;
  align-items: center;
}

.filter-option-text {
  margin: 0 12px;
}

:deep(.td-tree-node__content) {
  display: flex !important;
}

:deep(.td-tree-node__label) {
  flex: 1;
  overflow: hidden;
}

:deep(.td-table__panel) {
  &::-webkit-scrollbar {
    display: none;
    /* WebKit */
  }
}

:deep(.td-table__text.actions-wrapper.td-table__text__file__name) {
  color: var(--font-font-1, #272e3b);
}

:deep(.td-table__text.actions-wrapper.td-table__text__file__name span) {
  display: flex;
  flex: 1;
  width: 100%;
  padding-right: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 已注释：复选框基础样式 - 临时禁用
/* .td-checkbox {
  display: flex;
  align-items: center;
} */

// 原始的table scss
// 已注释：复选框基础样式 - 临时禁用
/* .td-checkbox {
  display: inline-flex;
  align-items: center
} */

// 已注释：复选框内部容器样式 - 临时禁用
/* .td-checkbox__inner {
  position: relative;
  flex: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 14px;
  height: 14px;
  background: var(--td-fill-special-component);
  border: 1px solid var(--td-border-2);
  border-radius: var(--td-border-radius-xs, 3px);
  -webkit-appearance: none;
  cursor: pointer
} */

// 已注释：复选框状态样式（半选、选中、悬停） - 临时禁用
/* .td-checkbox.is-indeterminate .td-checkbox__inner,
.td-checkbox__inner:checked,
.td-checkbox__inner:hover {
  border-color: var(--td-primary-default)
} */

// 已注释：复选框图标样式 - 临时禁用
/* .td-checkbox__inner:before {
  text-align: center;
  color: var(--td-font-light);
  font-size: 14px;
  font-family: thunder-icon;
  line-height: 14px;
  transform: scale(0);
  transition: .1s;
  content: "\e8a0"
} */

// 已注释：复选框选中状态背景 - 临时禁用
/* .td-checkbox__inner:checked {
  background: var(--td-primary-default)
} */

// 已注释：序列图标渐变样式 - 临时禁用
/* .td-icon-sequence.is-ascending,
.td-icon-sequence.is-descending {
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent
} */

// 已注释：复选框选中状态图标显示 - 临时禁用
/* .td-checkbox__inner:checked:before {
  transform: scale(1)
} */

// 已注释：复选框焦点状态 - 临时禁用
/* .td-checkbox__inner:focus {
  outline: 0
} */

// 已注释：复选框标签样式 - 临时禁用
/* .td-checkbox__label {
  margin-left: 6px
} */

// 已注释：复选框半选状态背景 - 临时禁用
/* .td-checkbox.is-indeterminate .td-checkbox__inner {
  background-color: var(--td-primary-default)
} */

// 已注释：复选框半选状态图标 - 临时禁用
/* .td-checkbox.is-indeterminate .td-checkbox__inner:before {
  transform: rotate(0);
  content: "\e89f"
} */

// 已注释：复选框禁用状态 - 临时禁用
/* .td-checkbox.is-disabled {
  cursor: not-allowed
} */

// 已注释：复选框禁用状态内部样式 - 临时禁用
/* .td-checkbox.is-disabled .td-checkbox__inner {
  background-color: var(--td-fill-2);
  border-color: var(--td-border-2)
} */

// 已注释：复选框禁用状态选中和半选样式 - 临时禁用
/* .td-checkbox.is-disabled .td-checkbox__inner:checked,
.td-checkbox.is-disabled.is-indeterminate .td-checkbox__inner {
  background-color: var(--td-fill-1)
} */

// 已注释：复选框禁用状态文字和图标颜色 - 临时禁用
/* .td-checkbox.is-disabled .td-checkbox__inner:before,
.td-checkbox.is-disabled .td-checkbox__label {
  color: var(--td-font-4)
} */

// 已注释：字体图标定义 - 临时禁用
/* @font-face {
  font-family: thunder-icon;
  src: url('//at.alicdn.com/t/c/font_4922630_19bfl3ztd0d.woff?t=1747985694483') format('woff');
} */

// 已注释：图标基础样式 - 临时禁用
/* [class*=" td-icon-"],
[class^=td-icon-] {
  font-family: thunder-icon !important;
  font-size: 16px;
  font-style: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
} */

// 已注释：各种图标样式 - 临时禁用
/* .td-icon-hint:before {
  content: "\e627"
}

.td-icon-info1:before {
  content: "\e8a3"
}

.td-icon-arrow:before {
  content: "\e8a1"
}

.td-icon-indeterminate:before {
  content: "\e89f"
}

.td-icon-checked:before {
  content: "\e8a0"
}

.td-icon-new:before {
  content: "\e89c"
}

.td-icon-load:before {
  content: "\e615"
}

.td-icon-close-browser:before {
  content: "\e61d"
}

.td-icon-arrow-line:before {
  content: "\e61e"
}


.td-icon-rename:before {
  content: "\e623"
}

.td-icon-success:before {
  content: "\e638"
}

.td-icon-error:before {
  content: "\e637"
}

.td-icon-question:before {
  content: "\e63a"
}

.td-icon-warning:before {
  content: "\e639"
}

.td-icon-arrow-left:before {
  content: "\e651"
}

.td-icon-arrow-right:before {
  content: "\e652"
}

.td-icon-refresh:before {
  content: "\e653"
}

.td-icon-security:before {
  content: "\e67e"
}

.td-icon-sequence:before {
  content: "\e680"
}

.td-icon-info:before {
  content: "\e8a3"
}

.td-icon-choose:before {
  content: "\e686"
}

.td-icon-more-right:before {
  content: "\e687"
}

.td-icon-search:before {
  content: "\e696"
}

.td-icon-fav:before {
  content: "\e694"
}

.td-icon-fav1:before {
  content: "\e695"
}

.td-icon-setting:before {
  content: "\e6c4"
}

.td-icon-arrow-up:before {
  content: "\e6c5"
}

.td-icon-arrow-down:before {
  content: "\e6c6"
}

.td-icon-more:before {
  content: "\e71b"
}

.td-icon-plus:before {
  content: "\e740"
}

.td-icon-minus:before {
  content: "\e741"
}

.td-icon-prev:before {
  content: "\e747"
}

.td-icon-next:before {
  content: "\e748"
} */

// 已注释：SVG图标样式 - 临时禁用
/* .td-icon-svg {
  width: 20px
} */

.td-table,
.td-table__header {
  width: 100%;
}

// 已注释：关闭图标样式 - 临时禁用
/* .td-icon-close:before {
  content: "\e61d"
} */

// 已注释：序列图标样式 - 临时禁用
/* .td-icon-sequence {
  color: var(--td-font-3)
} */

// 已注释：序列图标升序降序样式 - 临时禁用
/* .td-icon-sequence.is-ascending {
  background-image: linear-gradient(to bottom, var(--td-font-1) 50%, var(--td-font-3) 51%)
}

.td-icon-sequence.is-descending {
  background-image: linear-gradient(to bottom, var(--td-font-3) 50%, var(--td-font-1) 51%)
} */

// 已注释：星形图标样式 - 临时禁用
/* .td-icon-star {
  position: relative
}

.td-icon-star:before {
  content: "\e695";
  color: #b3b3b3
} */

// 已注释：加载图标样式 - 临时禁用
/* .td-icon-loading {
  display: inline-block;
  animation: td-load 1.5s both linear infinite
}

.td-icon-loading:before {
  content: "\e71d"
} */

// 已注释：用户图标样式 - 临时禁用
/* .td-icon-user-line:before {
  content: "\e74a"
}

.td-icon-user:before {
  content: "\e74b"
} */

// 已注释：加载图标动画 - 临时禁用
/* .td-icon-load {
  animation: td-load 1.5s both linear infinite
} */

// 已注释：下拉图标样式 - 临时禁用
/* .xly-icon-more-down:before {
  content: "\e76a"
} */

@keyframes td-load {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 已注释：窗口控制图标样式 - 临时禁用
/* .td-icon-restore:before {
  content: "\e798"
}

.td-icon-maximize:before {
  content: "\e799"
}

.td-icon-minimize:before {
  content: "\e79a"
}

.td-icon-magnify:before {
  content: "\e794"
}

.td-icon-minify:before {
  content: "\e795"
}

.td-icon-ratio:before {
  content: "\e796"
}

.td-icon-ratio-optimal:before {
  content: "\e797"
}

.td-icon-download:before {
  content: "\e79b"
} */

// 已注释：SVG文件图标样式 - 临时禁用
/* .td-icon-svg-file {
  height: 17px
} */

.td-table {
  box-sizing: border-box;
  border: 1px solid var(--td-border-2);
  border-radius: 4px;
  table-layout: fixed;
}

.td-table td,
.td-table th {
  height: 40px;
  border-bottom: solid 1px var(--td-border-2);
}

.td-table .td-tree-node {
  // margin-left: 10px;
  // max-width: 100%
}

.td-table th {
  font-weight: 400;
  text-align: left;
}

.td-table .is-striped {
  background-color: var(--td-fill-3);
}

.td-table__header-wrapper {
  margin-right: 6px;
}

.td-table__phantom {
  z-index: -1;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.td-table__panel {
  position: relative;
  overflow-x: hidden;
  overflow-y: scroll;
}

.td-table__panel::-webkit-scrollbar {
  width: 8px;
  background: 0 0;
}

.td-table__panel::-webkit-scrollbar:horizontal {
  height: 8px;
}

.td-table__panel::-webkit-scrollbar-thumb {
  width: 8px;
  border-radius: 4px;
  background: 0 0;
}

.td-table__panel::-webkit-scrollbar-thumb:hover {
  background: var(--td-scrollbar-background-hover);
}

.td-table__panel:hover::-webkit-scrollbar-thumb {
  background: var(--td-scrollbar-background-default);
}

.td-table__body {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  width: 100%;
}

.td-table__body-tip-wrapper {
  box-sizing: border-box;
  width: 100%;
  // padding: 0 12px;
}

.td-table__body tr:nth-last-child(1) td {
  border-bottom: 0;
}

.td-table__footer-wrapper {
  z-index: 2;
  position: relative;
  margin-bottom: -1px;
  border-top: solid 1px var(--td-border-2);
  background: #f9faff;
}

.td-table__footer {
  width: 100%;
  height: 36px;
}

// 已注释：表格底部复选框样式 - 临时禁用
/* .td-table__footer .td-checkbox {
  padding: 0 10px
} */

.td-table__text {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 10px;
  color: var(--font-font-3, #86909c);
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
}

// 已注释：表格文本中的序列图标样式 - 临时禁用
/* .td-table__text .td-icon-sequence {
  opacity: 0
}

.td-table__text .td-icon-sequence.is-show,
.td-table__text:hover .td-icon-sequence {
  opacity: 1
} */

.td-table--stripe tr:nth-child(2n) {
  background-color: var(--td-fill-3);
}

.td-table--border td,
.td-table--border th {
  border-left: solid 1px var(--td-border-2);
}

.td-table--border .td-table__body-wrapper {
  margin-top: -1px;
  padding: 0 12px;
}

.td-table--border tr>td:nth-child(1),
.td-table--border tr>th:nth-child(1) {
  border-left: 0;
}

a.td-table__text {
  color: var(--font-font-3, #86909c);
  font-style: normal;
  font-weight: 400;

  font-size: 13px;
  line-height: 22px;
  // text-decoration: none;
}

.td-table-tree {
  width: 100%;
}

.td-table-tree .td-tree-node {
  display: flex;
}

.td-table-tree .td-tree-node__content {
  display: flex;
  align-items: center;
  height: auto;
}

// 已注释：树形表格底部复选框样式 - 临时禁用
/* .td-table-tree .td-table__footer .td-checkbox {
  margin-left: 18px
} */

.td-table-tree .td-tree-node__label {
  display: inline-block;
  display: -webkit-box;
  flex: 1;
  height: 34px;
  overflow: hidden;
  color: var(--font-font-1, #272e3b);
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 34px;
  @include lineClamp(1);
}

.td-table-tree__cell-inner {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

// .td-icon-arrow-drop {
//   &.td-tree-node__expand-icon {
//     &.is-expanded {}
//   }
// }

.td-table tbody tr {
  position: relative;

  // &.is-checked,
  &:hover {
    background-color: var(--fill-fill-3, rgba(12, 24, 49, 0.04));

    td:first-child {
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }

    td:last-child {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    :deep(.td-tree-node__content) {
      // color: red;
      // transition: color 0.2s ease;
      background-color: transparent;
    }
  }
}

// 或者为所有行添加圆角
.td-table tbody tr td {
  &:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  &:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }
}

// 操作按钮样式
.td-table__action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.td-table__action-btn {
  padding: 4px 12px;
  border: 1px solid transparent;
  border-radius: 4px;
  outline: none;
  background: transparent;
  font-weight: 400;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

.td-table__action-btn--select {
  border-color: var(--primary-primary-6, #1677ff);
  color: var(--primary-primary-6, #1677ff);

  &:hover {
    border-color: var(--primary-primary-7, #0958d9);
    background-color: var(--primary-primary-1, #e6f4ff);
    color: var(--primary-primary-7, #0958d9);
  }
}

.td-table__action-btn--rename {
  border-color: var(--warning-warning-6, #fa8c16);
  color: var(--warning-warning-6, #fa8c16);

  &:hover {
    border-color: var(--warning-warning-7, #d46b08);
    background-color: var(--warning-warning-1, #fff7e6);
    color: var(--warning-warning-7, #d46b08);
  }
}

// 新增：自适应列宽支持样式
.td-table {
  // 启用表格布局固定模式，支持自适应列宽
  table-layout: fixed;

  // 确保表格内容不会溢出
  .td-table__text {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    // 当需要显示完整内容时（如悬停状态），允许换行
    &.td-table__text--wrap {
      text-overflow: inherit;
      white-space: normal;
    }
  }

  // 自适应列的特殊处理
  .td-table__auto-width-column {
    .td-table__text {
      // 确保自适应列的文本正确截断
      min-width: 0;
      max-width: 100%;
    }
  }

  // 树形节点中的文本也需要处理
  .td-tree-node__content {
    min-width: 0;
    max-width: 100%;

    .td-tree-node__label {
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 为自适应列添加特定的类名标识
.td-table[data-auto-width] {
  table-layout: fixed;

  // 确保所有列都有合适的最小宽度
  col {
    min-width: 0;
  }

  // 自适应列不设置最大宽度限制
  col.auto-width {
    width: auto !important;
  }
}

// 新增：文件名文本截断的特殊支持
.td-table__text__file__name {
  box-sizing: border-box;
  display: block;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  // 在表格中的特殊处理
  .td-table & {
    flex: 1;
    min-width: 0;
  }
}

// 确保表格单元格支持文件名的省略号显示
.td-table__text {

  // 当包含文件名元素时，确保正确的布局
  .td-table__text__file__name {
    min-width: 0;
    max-width: 100%;
  }

  .td-table__sort {
    flex-direction: column;
    gap: 4px;
    display: flex;
    color: var(--font-font-4);
    i:first-of-type {
      height: 4px;
    }
    .is-light {
      color: var(--font-font-1);
    }
  }
}