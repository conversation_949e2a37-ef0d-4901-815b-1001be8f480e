<script setup lang="ts">
import { computed } from 'vue'

const props = withDefaults(defineProps<{
  type?: 'progress' | 'circle'
  percentage: number // 进度百分比
  background?: string // 进度条背景色
  color?: string // 进度条颜色
  height?: number // 进度条高度
  fillBorderRadius?: string // 进度填充圆角
  railBorderRadius?: string // 轨道进度填充圆角
  processing?: boolean // 进行中动画
  width?: number // 圆环宽度
  strokeWidth?: number
  linecap?: "round" | "inherit" | "butt" | "square" | undefined // 圆环端点类型
}>(), {
  type: 'progress',
  percentage: 0,
  background: '',
  color: '',
  height: 0,
  fillBorderRadius: '',
  railBorderRadius: '',
  processing: false,
  strokeWidth: 6,
  width: 0,
  linecap: 'round'
});

/** 圆环大小 */
const circleSize = computed(() => {
  return props.type === 'circle' ? { width: `${props.width}px`, height: `${props.height}px` } : {}
})
/** 半径 */
const radius = computed(() => {
  if (props.type === 'circle') {
    return props.width / 2 - props.strokeWidth
  }
  return 0
})
/** 圆环位置 */
const circleDistance = computed(() => {
  return radius.value + props.strokeWidth
})
/** 圆环周长 */
const perimeter = computed(() => { // 圆环周长
  return 2 * Math.PI * radius.value
})

const view = computed(() => { // 视图大小
  const view = `0 0 ${props.width} ${props.width}`
  return view
})
const circlePathStyle = computed(() => { // 绘制圆环
  let color = props.color
  if (props.percentage === 0) {
    color = 'transparent'
  }
  return {
    strokeDasharray: `${perimeter.value * (props.percentage / 100)}px, ${perimeter.value}px`,
    transform: 'rotate(-90deg)',
    stroke: color,
    transformOrigin: `${circleDistance.value}px ${circleDistance.value}px`,
    transition: 'stroke-dasharray 0.6s ease 0s, stroke 0.6s ease'
  }
})


</script>

<template>
  <div :style="circleSize">
    <div
      v-if="props.type === 'circle'"
      class="td-progress-circle"
    >
      <svg :viewBox="view" class="td-progress-circle__track">
        <circle class="td-progress-circle__track"
          :cx="circleDistance"
          :cy="circleDistance"
          :r="radius"
          :stroke-width="strokeWidth"
          :style="{ stroke: background }"
        />
        <circle class="td-progress-circle__path"
          :cx="circleDistance"
          :cy="circleDistance"
          :r="radius"
          :stroke-width="strokeWidth"
          :stroke-linecap="linecap"
          :style="circlePathStyle"
        />
      </svg>
    </div>
    <div
      v-else
      class="td-progress
    ">
      <div
        class="td-progress-rail"
        :style="{
          height: props.height+'px',
          background: props.background,
          borderRadius: props.fillBorderRadius+'px'
        }">
        <div
          class="td-progress-bar"
          :class="{ 'td-processing': props.processing }"
          :style="{
            maxWidth: `${props.percentage}%`,
            backgroundColor: props.color,
            borderRadius: props.railBorderRadius+'px'
          }"
        />
      </div>
    </div>
  </div>

</template>

<style lang="scss" scoped>
.td-progress {
  width: 100%;
  display: block;
}

.td-progress-rail {
  position: relative;
  overflow: hidden;
  height: 3px;
  border-radius: 3px;
  background-color: var(--border-border-2);
  transition: background-color .3s cubic-bezier(0, 0, .2, 1);
}

.td-progress-bar {
  background: var(--primary-primary-hover);
  position: relative;
  border-radius: 3px;
  height: inherit;
  width: 100%;
  max-width: 0%;
  transition: background-color .3s cubic-bezier(0, 0, .2, 1), max-width .2s cubic-bezier(0, 0, .2, 1);
}

.td-processing {
  &::after {
    content: "";
    background: linear-gradient(90deg, rgba(255, 255, 255, .3) 0%, rgba(255, 255, 255, .5) 100%);
    animation: progress-processing-animation 2s cubic-bezier(0, 0, .2, 1) infinite;
  }
}

.td-progress-circle {
  width: 100%;
  height: 100%;
}
.td-progress-circle__track {
  fill: none;
  stroke: var(--border-border-2);
}
.td-progress-circle__path {
  fill: none;
  stroke: var(--primary-primary-default);
}

@keyframes progress-processing-animation {
  0% {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 100%;
    opacity: 1;
  }

  66% {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    opacity: 0;
  }

  100% {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    opacity: 0;
  }
}
</style>