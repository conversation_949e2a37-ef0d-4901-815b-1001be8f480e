// export interface IUserInfo {
//   sub: string;
//   name: string;
//   picture: string;
//   phone_number: string;
//   providers: {
//     id: string;
//     provider_user_id: string;
//   }[];
//   password: string;
//   status: string;
//   gender: string;
//   birthdate: string;
//   group: {
//     id: string;
//     expires_at: string;
//   }[];
//   created_at: string;
//   password_updated_at: string;
//   id: string;
//   vips: {
//     id: string;
//     expires_at: string;
//   }[];
//   vip_info: IUserVipInfo[];
//   profile_folder: string;
// }

// export interface IUserVipInfo {
//   register: string;
//   autodeduct: string;
//   daily: string;
//   expire: string;
//   grow: string;
//   is_vip: string;
//   last_pay: string;
//   level: string;
//   pay_id: string;
//   pay_name: string;
//   remind: string;
//   is_year: string;
//   user_vas: string;
//   vas_type: string;
//   vip_detail: {
//     IsVIP: number;
//     VasType: string;
//     Start: string;
//     End: string;
//     SurplusDay: number;
//   }[];
//   vip_icon: {
//     general: string;
//     small: string;
//   };
//   is_new: string;
//   expire_time: string;
// }