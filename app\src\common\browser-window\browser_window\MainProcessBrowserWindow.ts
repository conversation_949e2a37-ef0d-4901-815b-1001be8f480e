import { <PERSON>rows<PERSON><PERSON>ind<PERSON>, type BrowserWindowConstructorOptions } from 'electron'
import {
  EventEmitter,
  type ISimpleBrowserWindow,
  SimpleBrowserWindowEvent,
} from '@xbase/electron_common_kit'
import { TBrowserWindowExtraConfig } from './type'

export class MainProcessBrowserWindow
  extends EventEmitter<SimpleBrowserWindowEvent>
  implements ISimpleBrowserWindow
{
  private _window: BrowserWindow | undefined = undefined
  private _options: BrowserWindowConstructorOptions | undefined
  private _extraConfig: TBrowserWindowExtraConfig | undefined

  constructor(
    options?: BrowserWindowConstructorOptions,
    extraConfig?: TBrowserWindowExtraConfig,
  ) {
    super()
    this._options = options
    this._extraConfig = extraConfig
  }

  async loadURL(url: string): Promise<void> {
    this._window = new BrowserWindow({
      show: true,
      autoHideMenuBar: true,
      width: 1024,
      height: 728,
      webPreferences: {
        webSecurity: true,
        nodeIntegration: false,
        // contextIsolation 先禁用
        contextIsolation: true,
      },
      ...this._options,
    })
    this._initWindowEvent()

    if (this._extraConfig?.executeJavaScriptCode) {
      await this._window?.webContents.executeJavaScript(
        this._extraConfig.executeJavaScriptCode,
      )
    }

    await this._window.loadURL(url)
  }

  async getURL(): Promise<string | undefined> {
    return this._window?.webContents.getURL()
  }

  async destroy(): Promise<void> {
    this._window?.close()
    this._window = undefined
  }

  async isDestroyed(): Promise<boolean> {
    return this._window?.webContents.isDestroyed() || true
  }

  private _initWindowEvent() {
    this._window?.webContents.on('did-navigate', () => {
      this.emit(SimpleBrowserWindowEvent.DID_NAVIGATE)
    })
    this._window?.on('closed', () => {
      this._window = undefined
      this.emit(SimpleBrowserWindowEvent.CLOSED)
    })
  }
}
