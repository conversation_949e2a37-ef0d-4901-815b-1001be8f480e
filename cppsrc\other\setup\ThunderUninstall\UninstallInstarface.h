#pragma once
#include <vector>

struct ReplaceTextRecord
{
	struct Info
	{
		Info(int nID, LPCTSTR lpszNewText):nButtonID(nID), strNewString(lpszNewText)
		{}
		int nButtonID;
		tstring strNewString;
	};

	void Push(int nID, LPCTSTR lpszNewText)
	{
		listReplaceInfo.push_back(Info(nID, lpszNewText));
	}
	void Clear()
	{
		listReplaceInfo.clear();
	}
	bool IsEmpty()
	{
		return listReplaceInfo.empty();
	}

	typedef std::vector<Info> ReplaceInfoList;
	typedef ReplaceInfoList::iterator ReplaceInfoListIte;
	ReplaceInfoList listReplaceInfo;
};

