<script lang="ts" setup>
import { ref, computed } from 'vue'
import TDCheckbox from '@root/common/components/ui/checkbox/index.vue'
import { ThunderUtil } from '@root/common/utils'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import { IHistoryTask }  from '../type'
import { TaskType } from '@root/common/task/base'
import { ContextmenuKey } from '../source'
import XMPMessage from '@root/common/components/ui/message/index'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'

const props = withDefaults(defineProps<{
  fileItem: IHistoryTask
  selectedFiles: string[]
  isCollection?: boolean
  expiresTime: number
}>(), {})

const emits = defineEmits<{
  (e: 'contextmenu',event: MouseEvent,data: IHistoryTask): void
  (e: 'singleClick',event: MouseEvent,data: IHistoryTask): void
  (e: 'delete', data: IHistoryTask): void // 删除任务
  (e: 'collection', data: IHistoryTask): void // 收藏任务
  (e: 'unCollection', data: IHistoryTask): void // 取消收藏任务
  (e: 'updateRename', data: IHistoryTask): void // 重命名任务
  (e: 'checkboxClick', isCheck: boolean, data: IHistoryTask): void // 选中任务
  (e: 'operationWithType', operation: string, data: IHistoryTask[]): void // 单个操作
}>()

const operationStyle = ref({})

const isSelected = computed(() => props.selectedFiles.includes(props.fileItem.key))

const fileIcon = computed(() => {
  return TaskUtilHelper.getTaskIcon(props.fileItem.filename, props.fileItem.filetype === 7 ? TaskType.Bt : undefined)
})

const fileSize = computed(() => {
  return ThunderUtil.bytesToSize(props.fileItem?.filesize)
})

const dropdownMenuList = computed(() => {
  return [
    {
      key: ContextmenuKey.copy,
      label: '复制下载链接',
      icon: 'xl-icon-general-copy-m',
      isShow: !isDisabled.value,
    },
    {
      key: ContextmenuKey.rename,
      label: '重命名',
      icon: 'xl-icon-general-edit-m',
      isShow: !isDisabled.value,
    },
    {
      key: ContextmenuKey.delete,
      label: '删除',
      icon: 'xl-icon-general-delete-m',
      isShow: !props.fileItem.collection_id,
    },
    // {
    //   key: ContextmenuKey.report,
    //   label: '举报',
    //   icon: '',
    //   isShow: true,
    // },
  ].filter(item => item.isShow)
})

const isDisabled = computed(() => {
  if (props.expiresTime <= 0) {
    return false
  } else {
    return (new Date(props.fileItem?.create_time ?? 0).getTime() + props.expiresTime) <= Date.now()
  }
})

const handleSelectMore = (operation: string) => {
  emits('operationWithType', operation, [props.fileItem])
  // operationStyle.value = {}
}


/** 操作栏 */
const handleOperationClick = (operation: string) => {
  console.log('>>>>>>>>>>> operation', operation)
  if (isDisabled.value) {
    if (operation === 'download') {
      XMPMessage({
        message: '任务已过期，无法下载',
        type: 'error',
        duration: 2000,
      })
      return
    } else if (operation === 'collection') {
      XMPMessage({
        message: '任务已过期，无法标记喜欢',
        type: 'error',
        duration: 2000,
      })
      return
    }
  }
  
  emits('operationWithType', operation, [props.fileItem])
}

const handleItemClick = (event: MouseEvent) => {
  emits('singleClick', event, props.fileItem)
}

const handleContextMenu = (event: MouseEvent) => {
  emits('contextmenu', event, props.fileItem)
}

function handleCheckChange (isCheck: boolean) {
  emits('checkboxClick', isCheck, props.fileItem)
}

function handleShowOperation () {
  operationStyle.value = { display: 'flex' }
}

function updateMenuStatus (val) {
  if (val) {
    operationStyle.value = { display: 'flex' }
  } else {
    operationStyle.value = {}
  }
}
</script>

<template>
  <div
    class="file-item"
    @click.stop="handleItemClick($event)"
    @click.right.stop="handleContextMenu($event)"
  >
    <div
      class="file-item-wrapper"
      :class="{
        'is-selected': isSelected,
        'is-disabled': isDisabled,
      }"
    >
      <div class="file-item-content">
        <div class="file-item-content-left">
          <div @click.stop>
            <TDCheckbox
              :model-value="isSelected"
              @update:model-value="handleCheckChange"
              class="file-item-checkbox"
            ></TDCheckbox>
          </div>
          
          <div
            class="file-item-icon file-icon-type"
            :class="{ [fileIcon]: fileIcon }"
          ></div>
          <div class="file-item-title" v-tooltip="fileItem.filename">
            {{ fileItem.filename }}
          </div>
        </div>
        <div class="file-item-operation" :style="operationStyle">
          <Button
            v-tooltip="'下载'"
            class="disable-drag-select"
            variant="ghost"
            size="sm"
            :is-icon="true"
            @click.stop="handleOperationClick(ContextmenuKey.download)"
          >
            <i class="xl-icon-download"></i>
          </Button>
          <Button
            v-if="!fileItem.collection_id"
            v-tooltip="'标记喜欢'"
            class="disable-drag-select"
            variant="ghost"
            size="sm"
            :is-icon="true"
            @click.stop="handleOperationClick(ContextmenuKey.collection)"
          >
            <i class="xl-icon-general-like-l"></i>
          </Button>
          <Button
            v-else
            v-tooltip="'取消喜欢'"
            class="disable-drag-select"
            variant="ghost"
            size="sm"
            :is-icon="true"
            @click.stop="handleOperationClick(ContextmenuKey.unCollection)"
          >
            <i class="xl-icon-general-addedlikes-l"></i>
          </Button>
          <div v-if="dropdownMenuList.length">
            <DropdownMenu
              :items="dropdownMenuList"
              @select="handleSelectMore"
              @open="updateMenuStatus"
              side="bottom"
              align="end"
            >
              <Button
                variant="ghost"
                size="sm"
                is-icon
              >
                <i class="xl-icon-general-more-l"></i>
              </Button>
            </DropdownMenu>
          </div>
        </div>
      </div>
      <div class="file-item-size">{{ fileSize }}</div>
      <div class="file-item-time">{{ fileItem.create_time }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.file-item {
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease;
  padding: 0 28px;
  height: 60px;
  padding-top: 4px;
  box-sizing: border-box;
}
.file-item-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  height: 56px;
  border-radius: var(--border-radius-M2);
  padding: 0 12px;
  gap: 32px;
  justify-content: space-between;
  &:hover {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
    .file-item-operation {
      display: flex;
    }
  }
  &.is-selected {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
  }
  &.is-disabled {
    .file-item-icon { opacity: 0.5; }
    .file-item-size { opacity: 0.5; }
    .file-item-time { opacity: 0.5; }
    .file-item-title { opacity: 0.5; }
  }
}
.file-item-icon {
  margin-left: 12px;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  position: relative;
  border-radius: 4px;

  img {
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 4px;
  }
}
.file-item-checkbox {
  flex-shrink: 0;
}
.file-item-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.file-item-content-left {
  display: flex;
  align-items: center;
}
.file-item-title {
  margin-left: 12px;
  font-size: 13px;
  color: var(--font-font-1);
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
.file-item-operation {
  // display: flex;
  display: none;
  flex-shrink: 0;
  margin-left: 24px;
  align-items: center;
  .xl-icon-general-addedlikes-l {
    color: #FF4D4F;
  }
}
.file-item-size {
  font-size: 12px;
  width: 65px;
  color: var(--font-font-3, rgba(137, 142, 151, 1));
}
.file-item-time {
  font-size: 12px;
  width: 120px;
  color: var(--font-font-3, rgba(137, 142, 151, 1));
}

.dropdown-more-wrapper {
  position: relative;
  .dropdown-menu-wrapper {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    min-width: 100px;
    background: var(--background-background-elevated);
    color: var(--font-font-1);
    border: 1px solid var(--border-border-2);
    box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
    border-radius: var(--border-radius-M);
    padding: 6px;
    font-size: 13px;
    width: 228px;

    .dropdown-menu-item {
      display: flex;
      align-items: center;
      height: 36px;
      cursor: pointer;
      border-radius: var(--border-radius-S);
      &:hover {
        background: var(--fill-fill-3)s;
      }
      .dropdown-menu-item-icon {
        margin-right: 8px;
        width: 16px;
        height: 16px;
      }
    }
  }
}
</style>