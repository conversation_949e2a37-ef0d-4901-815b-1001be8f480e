<template>
  <div
    class="tasks-list-container none-draggable"
    :style="{ height: containerHeightStyle }"
    ref="tasksListContainer"
  >
    <div class="tasks-content-wrapper xlx-dialog__down-info">
      <Table
        id="Etab"
        ref="table"
        :hover-actions="true"
        body-tip-enabled
        :columns="columns"
        :data="tree"
        :default-checked-keys="defaultCheckedKeys"
        :default-expanded-keys="defaultExpandedKeys"
        :select-leafs="selectLeafs"
        :disabled-keys="disabledKeys"
        checkable
        hoverable
        header-enabled
        tree-enabled
        @click-label="handleClickLabel"
        @checked-change="updateCheckedKeys"
        @checkAll="handleCheckAll"
        :height="tableAvailableHeight"
        :striped="true"
        auto-width-column="name"
        :show-selection-count="showSelectionCount"
      >
        <template #header-filter>
          <div class="td-table__filter-options">
            <check-box
              v-if="fileCategoryExists.video"
              v-model="fileTypeFilters.video"
            >
              <span class="filter-option-text">视频</span>
            </check-box>
            <check-box
              v-if="fileCategoryExists.image"
              v-model="fileTypeFilters.image"
            >
              <span class="filter-option-text">图片</span>
            </check-box>
            <check-box
              v-if="fileCategoryExists.zip"
              v-model="fileTypeFilters.zip"
            >
              <span class="filter-option-text">压缩包</span>
            </check-box>
            <check-box
              v-if="fileCategoryExists.other"
              v-model="fileTypeFilters.other"
            >
              <span class="filter-option-text">其他</span>
            </check-box>
          </div>
        </template>
        <template #body-tip>
          <div
            class="body-tip-content-wrapper"
            v-if="taskStatusTipText !== ''"
          >
            <p class="body-tip-content">{{ taskStatusTipText }}</p>
          </div>
        </template>
        <!-- 悬停操作插槽 -->
        <template #hover-actions="{ row, rowKey, column, columnIndex }">
          <div class="action-buttons-container">
            <!-- 第一个操作列：基础操作 -->
            <div
              class="action-group"
              v-if="columnIndex === columns.length - 2"
            >
              <span
                class="action-btn action-btn--select"
                v-if="shouldShowSelectOnly(row)"
                @click.stop="handleSelectOnly(row, rowKey)"
              >
                仅选此项
              </span>
              <span
                class="action-btn action-btn--select"
                v-if="shouldShowRename(row)"
                @click.stop="handleRename(row, rowKey)"
              >
                重命名
              </span>
              <span
                class="action-btn action-btn--select"
                v-if="shouldShowRetry(row)"
                @click.stop="handleRetryMagnetTask(row)"
              >
                重试
              </span>
              <span
                class="action-btn action-btn--select"
                v-if="row.taskType === TaskType.P2sp"
                @click.stop="handleEdit(row)"
              >
                设置
              </span>
            </div>
          </div>
        </template>
        <template #footer-checkbox="{ selectLeafCount }">
          {{ customSelectedFileCount > 0 ? `已选中${customSelectedFileCount}个文件` : '全选' }}
        </template>
        <!-- 图标插槽 -->
        <template #icon="{ prop, value, row }">
          <LoadingSpinner
            class="status-loading-spinner"
            v-if="getTaskIcon(row) === 'loading-spinner'"
            :size="16"
          />
          <i
            v-else
            :class="{
              'status-icon': isStatusIcon(row),
              'file-icon-type': !isStatusIcon(row),
              'is-small': true,
              [getTaskIcon(row)]: true,
            }"
          />
        </template>
        <!-- 标签插槽 -->
        <template #label="{ prop, value, row }">
          <div
            class="td-table__text actions-wrapper"
            v-if="row.isEditing"
          >
            <input
              class="edit-input"
              v-model="row[prop]"
              type="text"
              @blur="handleFinishEdit(row)"
              @keyup.enter="handleFinishEdit(row)"
              @keyup.esc="handleCancelEdit(row)"
              @click.stop
              ref="editInput"
            />
          </div>

          <div
            class="td-table__text actions-wrapper td-table__text__file__name"
            v-else
          >
            <span>{{ value }}</span>
            <!-- 特殊节点状态显示 -->
            <!-- <span v-if="row.isSpecialEmptyNode && row.status" :class="getStatusClass(row.status)"
              class="task-status-indicator">
              {{ getStatusText(row.status) }}
            </span> -->
          </div>
        </template>
        <!-- 其他列的内容 -->
        <template #default="{ prop, value, row }">
          <p
            class="td-table__text__file__name"
            v-if="prop === 'name'"
            :title="value"
          >
            {{ value }}
          </p>
          <!-- 文件大小 format -->
          <template v-else-if="prop === 'fileSize'">
            {{ FileOperationHelper.formatSize(getEnhancedFileSize(row, value)) }}
          </template>
          <template v-else>{{ value }}</template>
        </template>
      </Table>
    </div>

    <div class="tasks-config">
      <!-- 合并为任务组 -->
      <div
        class="task-group task-group-container"
        v-if="supportTaskGroupMerge"
      >
        <CheckBox
          :modelValue="isMergeTask"
          @update:modelValue="newTaskConfigStore.setIsMergeTask"
        >
          <i class="file-icon-type is-small file-type-folder"></i>
        </CheckBox>
        <EditableText
          class="editable-task-group-name"
          v-model="taskGroupName"
          :width="300"
          @update:modelValue="handleTaskGroupNameChange"
          placeholder="请输入任务组名称"
          @edit-start="handleTaskGroupEditStart"
          @edit-confirm="handleTaskGroupEditConfirm"
          @edit-cancel="handleTaskGroupEditCancel"
        ></EditableText>
      </div>
      <!-- 启用列表顺序下载 -->
      <div class="sequential-download-option">
        <CheckBox
          :modelValue="enableSequentialDownload"
          @update:modelValue="newTaskConfigStore.setEnableSequentialDownload"
        >
          启用列表顺序下载
        </CheckBox>

        <span
          v-tooltip="{
            content: '如不勾选，将优先下载大文件',
            arrow: true,
            theme: 'new-task',
          }"
        >
          <Icon type="question"></Icon>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  watch,
  nextTick,
  defineEmits,
  defineProps,
  toRaw,
  onMounted,
  onBeforeUnmount,
} from 'vue'
import Table from '@root/common/components/ui/table/table.vue'
import Icon from '@root/common/components/ui/icon/icon.vue'
import CheckBox from '@root/common/components/ui/checkbox/index.vue'
import EditableText from '@root/common/components/ui/editable-text/index.vue'
import { extractFileNameFromUrl } from '@root/modal-renderer/src/utils.help'
import LoadingSpinner from '@root/common/components/ui/loading-spinner/index.vue'
import { FileOperationHelper } from '@root/common/helper/file-operation-helper'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import { TaskType } from '@root/common/task/base'

// 导入共享的类型定义，替换原有的TaskStatus定义
import {
  TaskParseStatus,
  DownloadPathType,
  ILeafNode,
  TaskListProps,
  TaskListEmits,
} from '@root/modal-renderer/types/new-task.type'

// 导入 Pinia Store
import { useNewTaskConfigStore } from '@root/modal-renderer/src/stores/new-task-config'
import { useDownloadPathTypeStore } from '@root/modal-renderer/src/stores/download-path-type'
import { storeToRefs } from 'pinia'

// 导入任务设置相关
import { useTaskSetting } from '@root/modal-renderer/src/composables/useTaskSetting'
import { useTaskSettingStore } from '@root/modal-renderer/src/stores/task-setting'
import { useNewTaskStore } from '@root/modal-renderer/src/stores/new-task'

const props = withDefaults(defineProps<TaskListProps>(), {
  taskData: () => [],
  dataMap: () => ({}),
  optionsExtData: () => ({}),
  containerHeight: '100%',
  magnetFileListsOnly: false,
  customCheckedKeys: () => [],
  autoExpandAll: false,
  showSelectionCount: false,
})

const emit = defineEmits<TaskListEmits>()

// 使用 Pinia Store
const newTaskConfigStore = useNewTaskConfigStore()
const downloadPathTypeStore = useDownloadPathTypeStore()
const newTaskStore = useNewTaskStore()

// 使用 storeToRefs 来获取响应式状态
const { enableSequentialDownload, isMergeTask, taskGroupName } = storeToRefs(newTaskConfigStore)

// 使用任务设置相关功能
const taskSettingStore = useTaskSettingStore()
const { showTaskSettingDialog } = useTaskSetting()

const table = ref<any>(null)

// 用于测量容器高度并计算表格高度（容器高度 - 40px）
const tasksListContainer = ref<HTMLElement | null>(null)
const containerHeightPx = ref(0)
let containerResizeObserver: ResizeObserver | null = null

onMounted(() => {
  const el = tasksListContainer.value
  if (el) {
    // 初始化当前高度
    containerHeightPx.value = el.getBoundingClientRect().height

    // 监听容器尺寸变化
    containerResizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        if (entry.target === el) {
          containerHeightPx.value = entry.contentRect.height
        }
      }
    })
    containerResizeObserver.observe(el)
  }
})

onBeforeUnmount(() => {
  if (containerResizeObserver) {
    containerResizeObserver.disconnect()
    containerResizeObserver = null
  }
})

watch(props, newVal => {
  console.log('task-list props', newVal, props.dataMap, props.taskData)
})

/**
 * 表格实际可用高度
 * 从容器总高度中减去底部配置区域（任务组合并+顺序下载选项）的高度(40px)
 * 确保表格有足够的显示空间，同时保持最小高度为100px
 */
const tableAvailableHeight = computed(() => {
  let baseHeight = Number(props.containerHeight)
  baseHeight -= 40 // 底部配置区域（合并任务组+顺序下载选项）的高度
  return Math.max(baseHeight, 100) // 确保最小高度为100px
})

/**
 * 容器高度样式计算
 * 支持数字（像素）和字符串（百分比等）两种格式
 */
const containerHeightStyle = computed(() => {
  if (!props.containerHeight) {
    return 'auto'
  }

  // 如果是数字，添加px单位
  if (typeof props.containerHeight === 'number') {
    return `${props.containerHeight}px`
  }

  // 如果是字符串，直接返回（支持百分比等）
  return props.containerHeight
})

/**
 * 处理任务组名称变更
 */
const handleTaskGroupNameChange = (newName: string) => {
  newTaskConfigStore.setTaskGroupName(newName)
  console.log('任务组名称已更新:', newName)
}

/**
 * 文件类型过滤器配置
 */
const fileTypeFilters = reactive({
  video: true, // 视频文件
  image: true, // 图片文件
  zip: true, // 压缩文件
  other: true, // 其他文件
})

/**
 * 表格列配置
 */
const columns = ref([
  {
    label: '文件名',
    prop: 'name',
    sortable: true,
  },
  {
    label: '类型',
    prop: 'suffix',
    width: '100px',
    sortable: true,
  },
  {
    label: '大小',
    prop: 'fileSize',
    width: '100px',
    sortable: true,
  },
])

/**
 * 树形结构数据，用于表格展示
 */
const tree = ref<any[]>([])

/**
 * 分支节点映射表，存储所有目录节点
 * key: 完整路径, value: 分支节点对象
 */
const branchMap = ref<Record<string, any>>({})

/**
 * 叶子节点映射表，存储所有文件节点
 * key: 节点key (${hash}-${realIndex} 或 ${url}-${realIndex}), value: 文件节点对象
 */
const leafMap = ref<Record<string, any>>({})

/**
 * 路径到key的映射表，用于反向查找
 * key: 完整路径, value: 节点key
 */
const pathToKeyMap = ref<Record<string, string>>({})

/**
 * 当前选中的节点key数组
 */
const checkedKeys = ref<string[]>([])

/**
 * 默认选中的节点key数组
 */
const defaultCheckedKeys = ref<string[]>([])

/**
 * 默认展开的节点key数组
 * 当 autoExpandAll 为 true 或 magnetFileListsOnly 为 true 时，自动展开所有父级目录节点
 */
const defaultExpandedKeys = computed(() => {
  // 如果 autoExpandAll 为 true 或者 magnetFileListsOnly 为 true，则展开所有目录
  if (!props.autoExpandAll && !props.magnetFileListsOnly) {
    return []
  }

  // 收集所有分支节点（目录节点）的key
  const expandedKeys: string[] = []

  // 遍历branchMap收集所有目录节点的key
  Object.keys(branchMap.value).forEach(key => {
    const branch = branchMap.value[key]
    // 排除根节点（key为空字符串）
    if (key !== '' && branch && branch.type === 'branch') {
      expandedKeys.push(key)
    }
  })

  console.log(
    '[TaskList] 自动展开目录节点:',
    expandedKeys,
    'autoExpandAll:',
    props.autoExpandAll,
    'magnetFileListsOnly:',
    props.magnetFileListsOnly
  )
  return expandedKeys
})

/**
 * 文件缓存，用于快速查找文件信息
 */
const filesCache = ref<Record<number, any> | null>(null)

/**
 * 多任务文件缓存映射，按任务ID存储文件缓存
 * key: 任务ID, value: 文件缓存对象
 */
const multiTaskFilesCache = ref<Record<string, Record<number, any>>>({})

/**
 * 多任务选中状态映射，按任务ID存储选中的文件
 * key: 任务ID, value: 选中的文件key数组
 */
const multiTaskCheckedKeys = ref<Record<string, string[]>>({})

/**
 * 统一任务数据数组 - 统一的数据源
 * 支持磁力链和P2SP两种任务类型的统一处理
 */
const allTaskDataArray = computed(() => {
  const taskData = props.taskData || []
  console.log('[TaskList] allTaskDataArray', taskData, 'props', props)
  return taskData
})

/**
 * 预选择的叶子节点列表，用于显示prefix
 */
const selectLeafs = computed(() => {
  const baseKeys: string[] = []

  // 为每个磁力任务添加特殊节点的key
  const currentTaskData = allTaskDataArray.value
  if (Array.isArray(currentTaskData) && currentTaskData.length > 0) {
    currentTaskData.forEach(taskInfo => {
      if (taskInfo?.url) {
        baseKeys.push(taskInfo.url)
      }
    })
  }

  // 获取所有叶子节点的key
  const allLeafKeys = Object.keys(leafMap.value)

  // 返回基础keys + 所有叶子节点的keys
  return [...baseKeys, ...allLeafKeys]
})

/**
 * 任务状态统计计算属性 - 只统计磁力链任务
 */
const taskStatusSummary = computed(() => {
  // 先筛选出磁力链任务，提升性能
  const magnetTasks = allTaskDataArray.value.filter(
    task => task && task.taskType === TaskType.Magnet
  )

  const summary = {
    magnet: {
      loading: 0, // 磁力链解析中
      error: 0, // 磁力链解析失败
      success: 0, // 磁力链解析成功
      total: magnetTasks.length, // 磁力链总数
    },
    total: magnetTasks.length, // 总任务数（这里只统计磁力链）
  }

  // 只对磁力链任务进行状态统计
  magnetTasks.forEach(task => {
    // 从 dataMap 中获取状态信息
    const taskDetail = props.dataMap[task.url]
    const taskStatus = taskDetail?.status

    if (taskStatus) {
      // 有明确状态
      switch (taskStatus) {
        case TaskParseStatus.LOADING:
        case 'loading':
          summary.magnet.loading++
          break
        case TaskParseStatus.ERROR:
        case 'error':
          summary.magnet.error++
          break
        case TaskParseStatus.SUCCESS:
        case 'success':
          summary.magnet.success++
          break
      }
    }
  })

  console.log('task-list summary', summary)

  return summary
})

/**
 * 任务状态提示文本 - 只显示磁力链任务状态
 */
const taskStatusTipText = computed(() => {
  const { magnet, total } = taskStatusSummary.value

  console.log('task-list taskStatusTipText', total, magnet)

  if (total === 0) {
    return ''
  }

  const parts: string[] = []

  // 磁力链任务状态
  if (magnet.total > 0) {
    if (magnet.loading > 0) {
      parts.push(`${magnet.loading}个磁力链解析中`)
    }

    if (magnet.error > 0) {
      parts.push(`${magnet.error}个磁力链解析失败`)
    }

    if (magnet.success > 0) {
      parts.push(`${magnet.success}个磁力链解析成功`)
    }
  }

  if (parts.length === 0) {
    return ''
  }

  return parts.join('，')
})

function getTaskIcon(row) {
  // 优先处理特殊节点的状态 解析成功状态-使用文件图标
  if (row.isSpecialEmptyNode && row.status && row.status !== 'success') {
    const statusInfo = getStatusInfo(row.status)
    // 当状态是 LOADING 时，返回特殊字符串用于渲染 LoadingSpinner
    if (row.status === TaskParseStatus.LOADING) {
      return 'loading-spinner'
    }
    return statusInfo.icon
  }

  // 处理P2SP任务的文件图标
  if (row.taskType === TaskType.P2sp && !row.isSpecialEmptyNode) {
    // 根据fileCategory选择图标
    if (row.fileCategory === 'video') {
      return 'file-type-video'
    } else if (row.fileCategory === 'image') {
      return 'file-type-img'
    } else {
      // 如果没有fileCategory或者是其他类型，使用默认的文件图标
      return row.icon || 'file-type-default'
    }
  }

  // 原有逻辑：处理普通节点
  if (row.level === 0) {
    if (row.taskType === TaskType.Bt) {
      return `file-type-folder-bt`
    } else if (row.taskType === TaskType.P2sp) {
      // P2SP根节点（如果有的话，通常P2SP是直接文件节点）
      return `file-type-folder-p2sp`
    }
  }
  return row.icon
}

/**
 * 获取状态信息（包含图标和文字）
 * @param {string} status - 状态值
 * @returns {Object} 包含icon和text的对象
 */
function getStatusInfo(status) {
  switch (status) {
    case TaskParseStatus.LOADING:
      return {
        icon: 'xl-icon-down-hint',
        text: '解析中...',
      }
    case TaskParseStatus.ERROR:
      return {
        icon: 'file-type-error',
        text: '解析失败',
      }
    case TaskParseStatus.SUCCESS:
      return {
        icon: 'file-type-folder',
        text: '解析成功',
      }
    default:
      return {
        icon: 'file-type-folder',
        text: '',
      }
  }
}

/**
 * 判断是否为状态图标
 * @param {Object} row - 行数据
 * @returns {boolean} 是否为状态图标
 */
function isStatusIcon(row) {
  return row.isSpecialEmptyNode && row.status && row.status !== 'success'
}

// ==================== 计算属性 ====================

/**
 * 选中节点的缓存映射，用于快速查找节点是否被选中
 */
const checkedKeysCache = computed(() => {
  return checkedKeys.value.reduce((cache, key) => {
    cache[key] = true
    return cache
  }, {})
})

/**
 * 禁用的节点keys数组 - 只禁用特殊状态的节点
 */
const disabledKeys = computed(() => {
  const disabled: string[] = []

  if (tree.value && tree.value.length > 0) {
    // 使用toRaw来安全访问被冻结的对象
    const rawTree = toRaw(tree.value)

    // 检查是否有特殊的空节点需要禁用
    rawTree.forEach(node => {
      if (node.isSpecialEmptyNode) {
        // 只有loading和error状态的特殊节点才禁用
        if (node.status === TaskParseStatus.LOADING || node.status === TaskParseStatus.ERROR) {
          disabled.push(node.key)
        }
      }
    })

    // 移除原有的"无条件禁用第一行"逻辑
    // 现在只有特殊状态的节点才会被禁用
  }

  return disabled
})

/**
 * 获取文件后缀名
 * @param {string} filename - 文件名
 * @returns {string} 文件后缀名
 */
const getSuffix = filename => {
  let matcher = filename.match(/\.([^.]+)$/)
  return matcher ? matcher[1] : ''
}

/**
 * 获取文件分类
 * @param {string} fileName - 文件名
 * @returns {string} 文件分类：'video' | 'image' | 'other'
 */
const getFileCategory = (fileName: string): string => {
  if (!fileName) return 'other'

  const fileType = TaskUtilHelper.getTaskFileType(fileName)

  switch (fileType) {
    case TaskUtilHelper.FileExtType.Video:
      return 'video'
    case TaskUtilHelper.FileExtType.Pic:
      return 'image'
    case TaskUtilHelper.FileExtType.Zip:
      return 'zip'
    default:
      return 'other'
  }
}

/**
 * 根据任务状态获取显示名称
 * @param {string} status - 任务状态
 * @param {string} url - 磁力链URL
 * @param {number} from - 调用来源标识，用于调试
 * @returns {string} 显示名称
 */
const getStatusDisplayName = (status, url, from) => {
  console.log('🚀 获取显示名称:', status, url, from)
  switch (status) {
    case TaskParseStatus.LOADING:
    case 'loading':
      return `正在努力获取种子文件（${url || ''}）`
    case TaskParseStatus.ERROR:
    case 'error':
      return `获取失败（${url || ''}）`
    case TaskParseStatus.SUCCESS:
    case 'success':
      return `${url || ''}`
    default:
      return `正在努力获取种子文件（${url || ''}）`
  }
}

/**
 * 获取P2SP任务的显示名称
 * @param {Object} taskData - P2SP任务数据
 * @param {string} taskTitle - 任务标题
 * @param {Object} dataMap - 数据映射
 * @returns {string} 显示名称
 */
const getP2spDisplayName = (taskData, taskTitle, dataMap) => {
  // 优先使用dataMap中的fileName
  const detailInfo = dataMap[taskData.url]
  if (detailInfo?.fileName) {
    return decodeURIComponent(detailInfo.fileName)
  }

  // 其次使用taskData中的fileName
  if (taskData.fileName) {
    return decodeURIComponent(taskData.fileName)
  }

  // 再次尝试从URL提取文件名
  const extractedName = extractFileNameFromUrl(taskData.url)
  if (extractedName && extractedName !== 'unknown-file') {
    return decodeURIComponent(extractedName)
  }

  // 最后使用任务标题
  return decodeURIComponent(taskTitle)
}

/**
 * 获取Emule任务的显示名称
 * @param {Object} taskData - Emule任务数据
 * @param {string} taskTitle - 任务标题
 * @param {Object} dataMap - 数据映射
 * @returns {string} 显示名称
 */
const getEmuleDisplayName = (taskData, taskTitle, dataMap) => {
  // 优先使用dataMap中的fileName
  const detailInfo = dataMap[taskData.url]
  if (detailInfo?.fileName) {
    return detailInfo.fileName
  }

  // 其次使用taskData中的fileName
  if (taskData.fileName) {
    return taskData.fileName
  }

  // 再次尝试从URL提取文件名
  const extractedName = extractFileNameFromUrl(taskData.url)
  if (extractedName && extractedName !== 'unknown-file') {
    return extractedName
  }

  // 最后使用任务标题
  return taskTitle
}

watch(
  tree,
  newVal => {
    console.log('🌳 根节点tree:', newVal, newVal?.length || 0)
  },
  { immediate: true }
)

// ==================== 数据结构构建函数 ====================

/**
 * 设置分支节点（目录节点）
 * @param {string} pwd - 父目录路径
 * @param {string} branchName - 分支名称
 * @param {TaskType} taskType - 任务类型
 * @param {number} level - 树的层级，默认为0
 * @returns {Object} 分支节点对象
 */
const setBranch = (pwd, branchName, taskType = TaskType.Unkown, level = 0) => {
  let parent = branchMap.value[pwd]
  let fullPath = pwd ? `${pwd}\\${branchName}` : branchName
  let branch = branchMap.value[fullPath]

  // console.log(
  //   'pwd, branchName',
  //   pwd === '' && branchName === '' ? 'root !!!!' : 'not root',
  //   'branch',
  //   branch,
  //   'fullpath',
  //   fullPath,
  //   'parent',
  //   parent,
  //   'pwd, branchName, ',
  //   pwd,
  //   branchName,
  //   'level',
  //   level
  // )
  if (!branch) {
    const fileIcon = `file-type-folder`
    branch = branchMap.value[fullPath] = {
      type: 'branch',
      name: branchName,
      children: [],
      fullPath,
      key: fullPath,
      parent,
      icon: fileIcon,
      level, // 添加层级属性
      taskType, // 添加任务类型标识
      fileSize: 0, // 分支节点初始大小为0，会在caculateBranchSize中计算
      isRoot: level === 0, // 添加isRoot属性，根节点为true
    }

    // console.log('branch parent121', parent)
    if (parent) {
      parent.children.push(branch)
    }
  } else {
    // console.log('branch already exists', branch)
  }

  return branch
}

/**
 * 获取任务的hash值
 * @param {Object} taskData - 任务数据
 * @param {string} url - 任务URL
 * @returns {string} hash值，如果获取不到则返回URL
 */
const getTaskHash = (taskData, url) => {
  // 对于BT任务，优先从taskData的infoId获取hash
  if (taskData && taskData.taskType === TaskType.Bt) {
    if (taskData.infoId) {
      return taskData.infoId
    }
    // 如果没有infoId，尝试从dataMap获取
    const dataInfo = props.dataMap[url]
    if (dataInfo && dataInfo.infoId) {
      return dataInfo.infoId
    }
  }

  // 如果获取不到hash，fallback到URL
  return url
}

/**
 * 设置叶子节点（文件节点）
 * @param {Object} options - 配置选项
 * @param {string} options.pwd - 父目录路径
 * @param {Object} options.leafData - 文件数据
 * @param {TaskType} options.taskType - 任务类型，默认为TaskType.Unkown
 * @param {number} options.level - 树的层级，默认为0
 * @param {string} options.url - 任务URL，可选
 * @param {number} options.realIndex - 文件真实索引，可选
 * @param {Object} options.taskData - 完整的任务数据，用于获取hash，可选
 * @returns {Object} 叶子节点对象
 */
const setLeaf = ({
  pwd,
  leafData,
  taskType = TaskType.Unkown,
  level = 0,
  url = null,
  realIndex = null,
  taskData = null,
}) => {
  // console.log(
  //   'setLeaf',
  //   { pwd, leafData, taskType, level, url, realIndex, taskData },
  //   props.dataMap
  // )
  let fullPath = pwd ? `${pwd}\\${leafData.fileName}` : leafData.fileName
  let branch = branchMap.value[pwd]

  // 根据任务类型生成不同的key格式
  let nodeKey
  if (taskType === TaskType.Bt && url && realIndex !== null && realIndex !== undefined) {
    // BT任务使用hash-realIndex格式
    const hash = getTaskHash(taskData, url)
    nodeKey = `${hash}-${realIndex}`
  } else {
    // 其他任务类型保持原有格式
    nodeKey = `${url}-${realIndex}`
  }

  // 检查是否已存在该key的叶子节点
  let leaf = leafMap.value[nodeKey]

  if (!leaf) {
    let suffix = getSuffix(leafData.fileName)
    const fileIcon = TaskUtilHelper.getTaskIcon(leafData.fileName)
    const fileCategory = getFileCategory(leafData.fileName) // 添加文件分类

    leaf = leafMap.value[nodeKey] = Object.assign({}, leafData, {
      type: 'leaf',
      fullPath,
      realIndex,
      name: leafData.fileName,
      fileSize: leafData.fileSize, // 只使用 fileSize
      suffix,
      key: nodeKey,
      parent: branch,
      default: true,
      icon: fileIcon,
      level, // 添加层级属性
      isRoot: false, // 叶子节点永远不是根节点
      taskType, // 添加任务类型标识
      url: url || leafData.url, // 添加url字段
      fileCategory, // 添加文件分类属性
    }) as ILeafNode

    // 建立路径到key的映射
    pathToKeyMap.value[fullPath] = nodeKey

    branch.children.push(leaf)
  }

  return leaf
}

/**
 * 删除叶子节点（同时清理leafMap和pathToKeyMap）
 * @param {string} nodeKey - 节点key
 */
const deleteLeaf = nodeKey => {
  const leaf = leafMap.value[nodeKey]
  if (leaf) {
    // 从pathToKeyMap中删除对应的路径映射
    delete pathToKeyMap.value[leaf.fullPath]
    // 从leafMap中删除节点
    delete leafMap.value[nodeKey]
  }
}

/**
 * 初始化默认选中的keys
 */
const initializeDefaultCheckedKeys = () => {
  console.log('[TaskList] 初始化默认选中的keys:', props)
  if (props.customCheckedKeys && props.customCheckedKeys.length > 0) {
    // 使用外部传入的 customCheckedKeys 初始化
    const validKeys = props.customCheckedKeys.filter(key => {
      // 先检查 branchMap
      if (branchMap.value[key]) {
        return true
      }
      // 检查 leafMap，现在可以直接通过key查找
      if (leafMap.value[key]) {
        return true
      }
      return false
    })
    defaultCheckedKeys.value = validKeys
    console.log('[TaskList] 使用外部传入的customCheckedKeys初始化defaultCheckedKeys:', validKeys)

    // 跳转逻辑已移至数据初始化完成后执行，确保 table 组件已渲染
  } else {
    // 使用原有的默认逻辑：调用 checkDefault 函数
    checkDefault()
    // 同时将选中的结果同步到 defaultCheckedKeys
    defaultCheckedKeys.value = checkedKeys.value
    console.log('[TaskList] 调用checkDefault并同步到defaultCheckedKeys:', defaultCheckedKeys.value)
  }
}

/**
 * 跳转到指定的行（带重试机制）
 * @param {string} key - 要跳转到的行的key
 * @param {string} context - 跳转的上下文信息，用于日志
 */
const jumpToRowWithRetry = (key: string, context: string = '') => {
  if (!key || !table.value) {
    console.log(`[TaskList] ${context} 跳转失败: key=${key}, table=${!!table.value}`)
    return
  }

  if (typeof table.value.jumpToRow === 'function') {
    console.log(`[TaskList] ${context} 跳转到key: ${key}`)
    table.value.jumpToRow(key)
  } else {
    console.log(`[TaskList] ${context} table组件jumpToRow方法不可用，延迟重试`)
    // 延迟重试
    nextTick(() => {
      if (table.value && typeof table.value.jumpToRow === 'function') {
        console.log(`[TaskList] ${context} 延迟重试成功，跳转到key: ${key}`)
        table.value.jumpToRow(key)
      } else {
        console.log(`[TaskList] ${context} 延迟重试失败，table组件仍未准备好`)
      }
    })
  }
}

/**
 * 设置默认选中的文件
 */
const checkDefault = () => {
  // 使用原有的默认逻辑：选中所有 default 为 true 的叶子节点
  const selectedKeys: string[] = []

  // 遍历 leafMap，现在key就是节点的key
  for (const nodeKey in leafMap.value) {
    const leaf = leafMap.value[nodeKey]
    if (leaf && leaf.default) {
      // console.log('📌 [checkDefault] 默认选中文件:', {
      //   key: leaf.key,
      //   taskType: leaf.taskType,
      //   realIndex: leaf.realIndex,
      //   url: leaf.url,
      //   fileName: leaf.name,
      // })
      selectedKeys.push(nodeKey) // 直接使用 nodeKey
    }
  }
  checkedKeys.value = selectedKeys
  console.log('[TaskList] 使用内部默认逻辑选中文件:', checkedKeys.value)
}

/**
 * 计算分支节点的总大小（递归计算）
 * @param {Object} branch - 分支节点对象
 * @returns {number} 分支总大小
 */
const caculateBranchSize = branch => {
  branch.fileSize = branch.children.reduce((prev, cur) => {
    if (cur.type === 'branch') {
      // 递归计算子分支的大小
      caculateBranchSize(cur)
      // 分支节点的大小：只计算已选中的子节点的大小
      // 这样更符合用户的选择行为
      const selectedSize = calculateSelectedSize(cur)
      return prev + selectedSize
    } else {
      // 叶子节点：只计算已选中的节点
      return prev + (checkedKeysCache.value[cur.key] ? cur.fileSize : 0)
    }
  }, 0)

  return branch.fileSize
}

/**
 * 计算节点及其子节点中已选中节点的总大小
 * @param {Object} node - 节点对象
 * @returns {number} 已选中节点的总大小
 */
const calculateSelectedSize = node => {
  if (node.type === 'leaf') {
    // 叶子节点：如果被选中则返回其大小，否则返回0
    return checkedKeysCache.value[node.key] ? node.fileSize : 0
  } else if (node.type === 'branch') {
    // 分支节点：递归计算其子节点中已选中的节点大小
    return node.children.reduce((total, child) => {
      return total + calculateSelectedSize(child)
    }, 0)
  }
  return 0
}

/**
 * 处理磁力链任务
 * @param {Object} taskData - 磁力链任务数据
 * @param {number} taskIndex - 任务索引
 * @param {string} currentTaskId - 任务ID
 * @param {string} taskTitle - 任务标题
 * @param {number} currentTaskType - 任务类型
 * @param {Object} existingNode - 已存在的节点
 * @param {number} existingNodeIndex - 已存在节点的索引
 */
const processMagnetTask = (
  taskData,
  taskIndex,
  currentTaskId,
  taskTitle,
  currentTaskType,
  existingNode,
  existingNodeIndex
) => {
  console.log(`🧲 处理磁力链任务 (任务 ${taskIndex}): ${taskTitle}`)

  const hasFileLists =
    taskData.fileLists && Array.isArray(taskData.fileLists) && taskData.fileLists.length > 0

  // 如果磁力链任务没有文件列表，创建特殊空节点
  if (!hasFileLists) {
    const taskStatus = taskData.status || TaskParseStatus.SUCCESS

    if (existingNode) {
      // 如果节点已存在，检查是否需要转换类型或更新信息
      if (existingNode.type === 'branch') {
        console.log(`🔄 磁力链：需要将分支节点转换为特殊空节点: ${currentTaskId}`)
        // 移除现有的分支节点
        branchMap.value[''].children.splice(existingNodeIndex, 1)
        existingNode = null // 重置，后续会重新创建
      } else if (existingNode.isSpecialEmptyNode) {
        console.log(`✏️ 磁力链：更新现有特殊空节点: ${currentTaskId}`)
        // 获取显示名称，优先从 optionsExtData 中获取
        let displayName = getStatusDisplayName(taskStatus, taskData.url, 1)

        // 更新现有特殊空节点的信息
        existingNode.name = displayName
        existingNode.status = taskStatus
        existingNode.taskIndex = taskIndex
        existingNode.taskType = currentTaskType
        existingNode.url = taskData.url // 添加url字段
        return // 更新完成，继续下一个任务
      }
    }

    // 创建新的特殊空节点
    if (!existingNode) {
      console.log('🚀 磁力链：创建新的特殊空节点:', taskStatus, taskData.url)

      // 获取显示名称，优先从 optionsExtData 中获取
      let displayName = getStatusDisplayName(taskStatus, taskData.url, 2)

      const specialNodeData = {
        fileName: taskTitle,
        filePath: taskTitle,
        fileSize: 0,
        realIndex: -1,
        isSpecialEmptyNode: true,
        status: taskStatus,
        taskId: currentTaskId,
        taskIndex: taskIndex,
      }

      // 特殊空节点使用hash作为key（如果有的话）
      const hash = getTaskHash(taskData, taskData.url)
      const specialKey = hash !== taskData.url ? hash : currentTaskId
      const specialLeaf = (leafMap.value[specialKey] = Object.assign({}, specialNodeData, {
        type: 'leaf',
        fullPath: specialKey,
        name: displayName, // 使用优先级的显示名称
        fileSize: 0, // 只使用 fileSize
        suffix: '',
        key: specialKey,
        parent: branchMap.value[''],
        default: false,
        icon: 'file-type-folder',
        level: 0,
        isRoot: false,
        taskType: currentTaskType,
        isSpecialEmptyNode: true,
        status: taskStatus,
        taskId: currentTaskId,
        taskIndex: taskIndex,
        url: taskData.url, // 添加url字段
        fileCategory: 'other', // 添加文件分类属性
      }))

      console.log(`✅ 磁力链：创建新特殊空节点 (任务 ${taskIndex}):`, specialLeaf)
      branchMap.value[''].children.push(specialLeaf)
    }
  } else {
    // 磁力链任务有文件列表，创建任务分支
    const taskBranchName = `${taskTitle}`

    if (existingNode) {
      if (existingNode.isSpecialEmptyNode) {
        console.log(`🔄 磁力链：需要将特殊空节点转换为任务分支: ${currentTaskId}`)
        // 移除现有的特殊空节点
        branchMap.value[''].children.splice(existingNodeIndex, 1)
        // 从leafMap中也移除（使用可能的hash key或currentTaskId）
        const oldHash = getTaskHash(taskData, taskData.url)
        const oldSpecialKey = oldHash !== taskData.url ? oldHash : currentTaskId
        deleteLeaf(oldSpecialKey)
        deleteLeaf(currentTaskId) // 同时删除URL key作为备份
        existingNode = null // 重置，后续会重新创建
      } else if (existingNode.type === 'branch') {
        console.log(`✏️ 磁力链：更新现有任务分支: ${currentTaskId}`)
        // 更新现有分支的基本信息
        existingNode.name = taskBranchName
        existingNode.taskIndex = taskIndex
        existingNode.taskType = currentTaskType
        existingNode.url = taskData.url // 添加url字段
        // 清理现有的子节点，重新构建文件列表
        existingNode.children = []
        console.log(`🧹 磁力链：清理现有分支的子节点: ${currentTaskId}`)
      }
    }

    // 如果不存在分支节点，创建新的分支节点
    let taskBranch = existingNode
    if (!taskBranch || taskBranch.type !== 'branch') {
      console.log(`📁 磁力链：创建新任务分支 (任务 ${taskIndex}): ${taskBranchName}`)
      taskBranch = setBranch('', taskBranchName, currentTaskType, 0)
      taskBranch.taskIndex = taskIndex
      taskBranch.taskId = currentTaskId
      taskBranch.url = taskData.url // 添加url字段

      console.log(`✅ 磁力链：新任务分支创建完成 (任务 ${taskIndex}):`, {
        name: taskBranch.name,
        taskIndex: taskBranch.taskIndex,
        taskId: taskBranch.taskId,
        taskType: taskBranch.taskType,
        url: taskBranch.url,
      })
    }

    // 处理任务的文件列表
    taskData.fileLists.forEach(file => {
      const prefixedFilePath = `${taskBranchName}\\${file.filePath}`

      // 构建目录层级
      let pwd = taskBranchName
      let currentLevel = 1

      file.filePath.split('\\').forEach(dirname => {
        if (dirname !== '') {
          setBranch(pwd, dirname, currentTaskType, currentLevel)
          pwd = pwd ? `${pwd}\\${dirname}` : dirname
          currentLevel++
        }
      })

      // 创建文件的副本，添加任务ID和URL，使用优先级的文件名
      const fileWithTaskId = {
        ...file,
        filePath: prefixedFilePath,
        taskId: currentTaskId,
        url: taskData.url, // 添加url字段到文件数据
      }

      // 计算文件的层级
      const fileLevel = prefixedFilePath.split('\\').length - 1

      // 设置文件节点
      setLeaf({
        pwd: prefixedFilePath.slice(0, -1),
        leafData: fileWithTaskId,
        taskType: currentTaskType,
        level: fileLevel,
        url: taskData.url,
        realIndex: file.realIndex,
        taskData: taskData, // 传递完整的任务数据以获取hash
      })

      // 添加到任务文件缓存
      multiTaskFilesCache.value[currentTaskId][file.realIndex] = file
    })
  }
}
const processBtTask = (
  taskData,
  taskIndex,
  currentTaskId,
  taskTitle,
  currentTaskType,
  existingNode,
  existingNodeIndex
) => {
  const hasFileLists =
    taskData.fileLists && Array.isArray(taskData.fileLists) && taskData.fileLists.length > 0

  // 如果磁力链任务没有文件列表，创建特殊空节点
  if (!hasFileLists) {
    const taskStatus = taskData.status || TaskParseStatus.SUCCESS

    if (existingNode) {
      // 如果节点已存在，检查是否需要转换类型或更新信息
      if (existingNode.type === 'branch') {
        console.log(`🔄 磁力链：需要将分支节点转换为特殊空节点: ${currentTaskId}`)
        // 移除现有的分支节点
        branchMap.value[''].children.splice(existingNodeIndex, 1)
        existingNode = null // 重置，后续会重新创建
      } else if (existingNode.isSpecialEmptyNode) {
        console.log(`✏️ 磁力链：更新现有特殊空节点: ${currentTaskId}`)
        // 获取显示名称，优先从 optionsExtData 中获取
        let displayName = getStatusDisplayName(taskStatus, taskData.url, 1)

        // 更新现有特殊空节点的信息
        existingNode.name = displayName
        existingNode.status = taskStatus
        existingNode.taskIndex = taskIndex
        existingNode.taskType = currentTaskType
        existingNode.url = taskData.url // 添加url字段
        return // 更新完成，继续下一个任务
      }
    }

    // 创建新的特殊空节点
    if (!existingNode) {
      console.log('🚀 磁力链：创建新的特殊空节点:', taskStatus, taskData.url)

      // 获取显示名称，优先从 optionsExtData 中获取
      let displayName = getStatusDisplayName(taskStatus, taskData.url, 2)

      const specialNodeData = {
        fileName: taskTitle,
        filePath: taskTitle,
        fileSize: 0,
        realIndex: -1,
        isSpecialEmptyNode: true,
        status: taskStatus,
        taskId: currentTaskId,
        taskIndex: taskIndex,
      }

      // 特殊空节点使用hash作为key（如果有的话）
      const hash = getTaskHash(taskData, taskData.url)
      const specialKey = hash !== taskData.url ? hash : currentTaskId
      const specialLeaf = (leafMap.value[specialKey] = Object.assign({}, specialNodeData, {
        type: 'leaf',
        fullPath: specialKey,
        name: displayName, // 使用优先级的显示名称
        fileSize: 0, // 只使用 fileSize
        suffix: '',
        key: specialKey,
        parent: branchMap.value[''],
        default: false,
        icon: 'file-type-folder',
        level: 0,
        isRoot: false,
        taskType: currentTaskType,
        isSpecialEmptyNode: true,
        status: taskStatus,
        taskId: currentTaskId,
        taskIndex: taskIndex,
        url: taskData.url, // 添加url字段
        fileCategory: 'other', // 添加文件分类属性
      }))

      console.log(`✅ 磁力链：创建新特殊空节点 (任务 ${taskIndex}):`, specialLeaf)
      branchMap.value[''].children.push(specialLeaf)
    }
  } else {
    // 磁力链任务有文件列表，创建任务分支
    const taskBranchName = `${taskTitle}`

    if (existingNode) {
      if (existingNode.isSpecialEmptyNode) {
        console.log(`🔄 磁力链：需要将特殊空节点转换为任务分支: ${currentTaskId}`)
        // 移除现有的特殊空节点
        branchMap.value[''].children.splice(existingNodeIndex, 1)
        // 从leafMap中也移除（使用可能的hash key或currentTaskId）
        const oldHash = getTaskHash(taskData, taskData.url)
        const oldSpecialKey = oldHash !== taskData.url ? oldHash : currentTaskId
        deleteLeaf(oldSpecialKey)
        deleteLeaf(currentTaskId) // 同时删除URL key作为备份
        existingNode = null // 重置，后续会重新创建
      } else if (existingNode.type === 'branch') {
        console.log(`✏️ 磁力链：更新现有任务分支: ${currentTaskId}`)
        // 更新现有分支的基本信息
        existingNode.name = taskBranchName
        existingNode.taskIndex = taskIndex
        existingNode.taskType = currentTaskType
        existingNode.url = taskData.url // 添加url字段
        // 清理现有的子节点，重新构建文件列表
        existingNode.children = []
        console.log(`🧹 磁力链：清理现有分支的子节点: ${currentTaskId}`)
      }
    }

    // 如果不存在分支节点，创建新的分支节点
    let taskBranch = existingNode
    if (!taskBranch || taskBranch.type !== 'branch') {
      console.log(`📁 磁力链：创建新任务分支 (任务 ${taskIndex}): ${taskBranchName}`)
      taskBranch = setBranch('', taskBranchName, currentTaskType, 0)
      taskBranch.taskIndex = taskIndex
      taskBranch.taskId = currentTaskId
      taskBranch.url = taskData.url // 添加url字段

      console.log(`✅ 磁力链：新任务分支创建完成 (任务 ${taskIndex}):`, {
        name: taskBranch.name,
        taskIndex: taskBranch.taskIndex,
        taskId: taskBranch.taskId,
        taskType: taskBranch.taskType,
        url: taskBranch.url,
      })
    }

    // 处理任务的文件列表
    taskData.fileLists.forEach(file => {
      const prefixedFilePath = `${taskBranchName}\\${file.filePath}`

      // 构建目录层级
      let pwd = taskBranchName
      let currentLevel = 1

      file.filePath.split('\\').forEach(dirname => {
        if (dirname !== '') {
          setBranch(pwd, dirname, currentTaskType, currentLevel)
          pwd = pwd ? `${pwd}\\${dirname}` : dirname
          currentLevel++
        }
      })

      // 创建文件的副本，添加任务ID和URL，使用优先级的文件名
      const fileWithTaskId = {
        ...file,
        filePath: prefixedFilePath,
        taskId: currentTaskId,
        url: taskData.url, // 添加url字段到文件数据
      }

      // 计算文件的层级
      const fileLevel = prefixedFilePath.split('\\').length - 1

      // 设置文件节点
      setLeaf({
        pwd: prefixedFilePath.slice(0, -1),
        leafData: fileWithTaskId,
        taskType: currentTaskType,
        level: fileLevel,
        url: taskData.url,
        realIndex: file.realIndex,
        taskData: taskData, // 传递完整的任务数据以获取hash
      })

      // 添加到任务文件缓存
      multiTaskFilesCache.value[currentTaskId][file.realIndex] = file
    })
  }
}

/**
 * 处理P2SP任务
 * @param {Object} taskData - P2SP任务数据
 * @param {number} taskIndex - 任务索引
 * @param {string} currentTaskId - 任务ID
 * @param {string} taskTitle - 任务标题
 * @param {number} currentTaskType - 任务类型
 * @param {Object} existingNode - 已存在的节点
 * @param {number} existingNodeIndex - 已存在节点的索引
 */
const processP2spTask = (
  taskData,
  taskIndex,
  currentTaskId,
  taskTitle,
  currentTaskType,
  existingNode,
  existingNodeIndex
) => {
  console.log(`📁 处理P2SP任务 (任务 ${taskIndex}): ${taskTitle}`)

  // 从dataMap中获取详细信息
  const detailInfo = props.dataMap[taskData.url]

  if (detailInfo) {
    console.log(`📊 从dataMap获取到P2SP任务详细信息:`, detailInfo)

    // 使用dataMap中的信息增强任务数据
    taskData.fileName = detailInfo.fileName || taskData.fileName
    taskData.fileSize = detailInfo.fileSize || taskData.fileSize || 0
    taskData.fileType = detailInfo.fileType || taskData.fileType
    taskData.protocol = detailInfo.protocol
    taskData.addTime = detailInfo.addTime

    // P2SP特有信息
    if (detailInfo.taskType === TaskType.P2sp) {
      taskData.hostName = (detailInfo as any).hostName
      taskData.port = (detailInfo as any).port
      taskData.fullPath = (detailInfo as any).fullPath
      taskData.userName = (detailInfo as any).userName
      taskData.schema = (detailInfo as any).schema
    }

    console.log(`✅ P2SP任务数据已从dataMap增强:`, {
      fileName: taskData.fileName,
      fileSize: taskData.fileSize,
      fileType: taskData.fileType,
      protocol: taskData.protocol,
      hostName: taskData.hostName,
      port: taskData.port,
    })
  } else {
    console.log(`⚠️ 在dataMap中未找到P2SP任务的详细信息: ${taskData.url}`)
  }

  if (existingNode) {
    if (existingNode.isSpecialEmptyNode) {
      console.log(`🔄 P2SP：需要将特殊空节点转换为P2SP文件节点: ${currentTaskId}`)
      // 移除现有的特殊空节点
      branchMap.value[''].children.splice(existingNodeIndex, 1)
      // 从leafMap中也移除
      deleteLeaf(currentTaskId)
      existingNode = null // 重置，后续会重新创建
    } else if (existingNode.type === 'leaf' && !existingNode.isSpecialEmptyNode) {
      console.log(`✏️ P2SP：更新现有P2SP文件节点: ${currentTaskId}`)
      // 更新现有文件节点的信息
      const fileName = getP2spDisplayName(taskData, taskTitle, props.dataMap)
      existingNode.name = fileName
      existingNode.fileSize = taskData.fileSize || 0 // 只使用 fileSize
      existingNode.taskIndex = taskIndex
      existingNode.taskType = currentTaskType
      existingNode.fileCategory = getFileCategory(fileName) // 更新文件分类
      existingNode.status = taskData.status
      existingNode.url = taskData.url // 添加url字段
      existingNode.protocol = taskData.protocol // 添加协议信息
      existingNode.addTime = taskData.addTime // 添加时间信息

      // 更新文件缓存
      multiTaskFilesCache.value[currentTaskId][0] = {
        fileName: existingNode.name,
        filePath: '',
        fileSize: existingNode.fileSize, // 使用 fileSize
        realIndex: 0,
      }
      return // 更新完成，继续下一个任务
    }
  }

  // 创建新的P2SP文件节点
  if (!existingNode) {
    const fileName = getP2spDisplayName(taskData, taskTitle, props.dataMap)

    // 优先从dataMap获取fileSize，如果没有则使用taskData中的值
    const detailInfo = props.dataMap[taskData.url]
    const fileSize = detailInfo?.fileSize || taskData.fileSize || 0

    console.log(`🔍 P2SP创建节点时的文件大小检查:`, {
      dataMap中的fileSize: detailInfo?.fileSize,
      'taskData.fileSize': taskData.fileSize,
      extractedFileName: extractFileNameFromUrl(taskData.url),
      finalFileName: fileName,
      finalFileSize: fileSize,
      detailInfo: detailInfo,
      taskData完整对象: taskData,
    })

    const p2spFileData = {
      fileName: fileName,
      filePath: '', // P2SP任务文件路径为空
      fileSize: fileSize,
      realIndex: 0,
      taskId: currentTaskId,
      url: taskData.url, // 添加url字段到文件数据
      protocol: taskData.protocol, // 添加协议信息
      addTime: taskData.addTime, // 添加时间信息
    }

    const p2spKey = currentTaskId
    const fileCategory = getFileCategory(fileName) // 获取文件分类
    const p2spLeaf = (leafMap.value[p2spKey] = Object.assign({}, p2spFileData, {
      type: 'leaf',
      fullPath: p2spKey,
      name: fileName,
      fileSize: fileSize, // 只使用 fileSize
      suffix: getSuffix(fileName),
      key: p2spKey,
      parent: branchMap.value[''], // 直接挂载到根节点
      default: true, // P2SP任务默认选中
      icon: TaskUtilHelper.getTaskIcon(fileName),
      level: 0,
      isRoot: false,
      taskType: currentTaskType,
      isSpecialEmptyNode: false,
      taskId: currentTaskId,
      taskIndex: taskIndex,
      fileCategory: fileCategory, // 添加文件分类属性
      status: taskData.status || TaskParseStatus.SUCCESS,
      url: taskData.url, // 添加url字段
      protocol: taskData.protocol, // 添加协议信息
      addTime: taskData.addTime, // 添加时间信息
    }))

    console.log(`✅ P2SP：创建新P2SP文件节点 (任务 ${taskIndex}):`, {
      leafNode: p2spLeaf,
      节点的fileSize: p2spLeaf.fileSize,
      原始fileSize: taskData.fileSize,
      文件分类: fileCategory,
    })
    branchMap.value[''].children.push(p2spLeaf)

    // 添加到任务文件缓存
    multiTaskFilesCache.value[currentTaskId][0] = p2spFileData
  }
}

/**
 * 处理Emule任务
 * @param {Object} taskData - Emule任务数据
 * @param {number} taskIndex - 任务索引
 * @param {string} currentTaskId - 任务ID
 * @param {string} taskTitle - 任务标题
 * @param {number} currentTaskType - 任务类型
 * @param {Object} existingNode - 已存在的节点
 * @param {number} existingNodeIndex - 已存在节点的索引
 */
const processEmuleTask = (
  taskData,
  taskIndex,
  currentTaskId,
  taskTitle,
  currentTaskType,
  existingNode,
  existingNodeIndex
) => {
  console.log(`🔗 ====== 开始处理Emule任务 (任务 ${taskIndex}) ======`)
  console.log(`🔗 Emule任务标题: ${taskTitle}`)
  console.log(`🔗 Emule任务ID: ${currentTaskId}`)
  console.log(
    `🔗 Emule任务类型: ${currentTaskType} (${currentTaskType === TaskType.Emule ? 'Emule' : '非Emule'})`
  )
  console.log(`🔗 Emule任务状态: ${taskData.status || '未设置'}`)

  // 从dataMap中获取详细信息
  const detailInfo = props.dataMap[taskData.url]

  if (detailInfo) {
    console.log(`📊 从dataMap获取到Emule任务详细信息:`, detailInfo)

    // 使用dataMap中的信息增强任务数据
    taskData.fileName = detailInfo.fileName || taskData.fileName
    taskData.fileSize = detailInfo.fileSize || taskData.fileSize || 0
    taskData.fileType = detailInfo.fileType || taskData.fileType
    taskData.protocol = detailInfo.protocol
    taskData.addTime = detailInfo.addTime

    console.log(`✅ Emule任务数据已从dataMap增强:`, {
      fileName: taskData.fileName,
      fileSize: taskData.fileSize,
      fileType: taskData.fileType,
      protocol: taskData.protocol,
      addTime: taskData.addTime,
    })
  } else {
    console.log(`⚠️ 在dataMap中未找到Emule任务的详细信息: ${taskData.url}`)
  }

  // 检查现有节点的情况
  if (existingNode) {
    console.log(`🔗 Emule：发现已存在的节点，索引: ${existingNodeIndex}`, existingNode)

    if (existingNode.isSpecialEmptyNode) {
      // 移除现有的特殊空节点
      branchMap.value[''].children.splice(existingNodeIndex, 1)
      // 从leafMap中也移除
      deleteLeaf(currentTaskId)

      existingNode = null // 重置，后续会重新创建
      console.log(`🔗 Emule：已重置existingNode为null，准备创建新节点`)
    } else if (existingNode.type === 'leaf' && !existingNode.isSpecialEmptyNode) {
      console.log(`✏️ Emule：更新现有Emule文件节点: ${currentTaskId}`)

      // 记录更新前的状态
      const beforeUpdate = {
        name: existingNode.name,
        fileSize: existingNode.fileSize, // 使用 fileSize
        taskIndex: existingNode.taskIndex,
        fileCategory: existingNode.fileCategory,
        status: existingNode.status,
        url: existingNode.url,
      }
      console.log(`🔗 Emule：更新前的节点状态:`, beforeUpdate)

      // 更新现有文件节点的信息
      const newFileName = getEmuleDisplayName(taskData, taskTitle, props.dataMap)
      const newFileSize = taskData.fileSize || 0
      const newFileCategory = getFileCategory(newFileName) // 更新文件分类

      existingNode.name = newFileName
      existingNode.fileSize = newFileSize // 只使用 fileSize
      existingNode.taskIndex = taskIndex
      existingNode.taskType = currentTaskType
      existingNode.fileCategory = newFileCategory
      existingNode.status = taskData.status
      existingNode.url = taskData.url // 添加url字段
      existingNode.protocol = taskData.protocol // 添加协议信息
      existingNode.addTime = taskData.addTime // 添加时间信息

      // 记录更新后的状态
      const afterUpdate = {
        name: existingNode.name,
        fileSize: existingNode.fileSize, // 使用 fileSize
        taskIndex: existingNode.taskIndex,
        fileCategory: existingNode.fileCategory,
        status: existingNode.status,
        url: existingNode.url,
        protocol: existingNode.protocol,
        addTime: existingNode.addTime,
      }
      console.log(`🔗 Emule：更新后的节点状态:`, afterUpdate)

      // 更新文件缓存
      const cacheData = {
        fileName: existingNode.name,
        filePath: '',
        fileSize: existingNode.fileSize, // 使用 fileSize
        realIndex: 0,
      }
      multiTaskFilesCache.value[currentTaskId][0] = cacheData

      return // 更新完成，继续下一个任务
    } else {
      // console.log(`⚠️ Emule：发现未预期的现有节点类型:`, {
      //   type: existingNode.type,
      //   isSpecialEmptyNode: existingNode.isSpecialEmptyNode,
      //   是否为分支节点: existingNode.type === 'branch',
      //   是否为叶子节点: existingNode.type === 'leaf',
      // })
    }
  } else {
    console.log(`🔗 Emule：未发现已存在的节点，将创建新节点`)
  }

  // 创建新的Emule文件节点
  if (!existingNode) {
    console.log(`🚀 Emule：开始创建新的Emule文件节点`)

    const fileName = getEmuleDisplayName(taskData, taskTitle, props.dataMap)

    // 优先从dataMap获取fileSize，如果没有则使用taskData中的值
    const detailInfo = props.dataMap[taskData.url]
    const fileSize = detailInfo?.fileSize || taskData.fileSize || 0

    console.log(`🔗 Emule：文件名解析结果:`, {
      原始fileName: taskData.fileName,
      从URL提取: taskData.url ? extractFileNameFromUrl(taskData.url) : '无URL',
      fallback标题: taskTitle,
      最终fileName: fileName,
    })
    console.log(`🔗 Emule：文件大小: ${fileSize} 字节 (${fileSize / 1024 / 1024} MB)`)

    const emuleFileData = {
      fileName: fileName,
      filePath: '', // Emule任务文件路径为空
      fileSize: fileSize,
      realIndex: 0,
      taskId: currentTaskId,
      url: taskData.url, // 添加url字段到文件数据
      protocol: taskData.protocol, // 添加协议信息
      addTime: taskData.addTime, // 添加时间信息
    }
    console.log(`🔗 Emule：构建的文件数据对象:`, emuleFileData)

    const emuleKey = currentTaskId
    console.log(`🔗 Emule：使用的节点键值: ${emuleKey}`)

    const fileCategory = getFileCategory(fileName) // 获取文件分类
    const emuleLeaf = (leafMap.value[emuleKey] = Object.assign({}, emuleFileData, {
      type: 'leaf',
      fullPath: emuleKey,
      name: fileName,
      fileSize: fileSize, // 只使用 fileSize
      suffix: getSuffix(fileName),
      key: emuleKey,
      parent: branchMap.value[''], // 直接挂载到根节点
      default: true, // Emule任务默认选中
      icon: TaskUtilHelper.getTaskIcon(fileName),
      level: 0,
      isRoot: false,
      taskType: currentTaskType,
      isSpecialEmptyNode: false,
      taskId: currentTaskId,
      taskIndex: taskIndex,
      fileCategory: fileCategory, // 添加文件分类属性
      status: taskData.status || TaskParseStatus.SUCCESS,
      url: taskData.url, // 添加url字段
      protocol: taskData.protocol, // 添加协议信息
      addTime: taskData.addTime, // 添加时间信息
    }))

    // 添加到根节点
    console.log(`🔗 Emule 叶子节点}`, 'emuleLeaf', emuleLeaf)
    branchMap.value[''].children.push(emuleLeaf)

    multiTaskFilesCache.value[currentTaskId][0] = emuleFileData

    // 验证节点是否正确添加到leafMap
    // console.log(`🔗 Emule：leafMap验证:`, {
    //   键存在: emuleKey in leafMap.value,
    //   值匹配: leafMap.value[emuleKey] === emuleLeaf,
    //   leafMap总数: Object.keys(leafMap.value).length,
    // })

    console.log(`🔗 ====== Emule任务处理完成 (任务 ${taskIndex}) ======`)
  }
}

/**
 * 解析多个任务的文件列表，构建统一的树形结构
 * @param {Array} taskInfoArray - 任务信息数组
 * @param {TaskType} defaultTaskType - 默认任务类型（当任务数据中没有taskType时使用）
 */
const parseMultipleTasksFileList = (taskDataArrayWithDetail, defaultTaskType = TaskType.Unkown) => {
  // console.log('🚀 解析多个任务的文件列表:', taskDataArrayWithDetail)
  // console.log('📋 任务数量:', taskDataArrayWithDetail?.length)

  // // 详细打印每个任务的基本信息
  // taskDataArrayWithDetail?.forEach((task, index) => {
  //   console.log(`📋 任务预览: ${index}`, {
  //     title: task?.title || task?.fileName,
  //     taskId: task?.url,
  //     status: task?.status,
  //     taskType: task?.taskType || defaultTaskType,
  //     fileCount: task?.fileLists?.length || 0,
  //     hasFiles: !!(task?.fileLists && task.fileLists.length > 0),
  //     url: task?.url,
  //   })
  // })

  // 初始化映射表
  branchMap.value = {}
  leafMap.value = {}
  pathToKeyMap.value = {}
  multiTaskFilesCache.value = {}
  multiTaskCheckedKeys.value = {}

  // 为根节点创建分支
  setBranch('', '', defaultTaskType, 0)
  console.log('🌳 根节点创建完成，初始children长度:', branchMap.value['']?.children?.length || 0)

  // 如果没有任务数据，创建空任务提示
  if (!taskDataArrayWithDetail || taskDataArrayWithDetail.length === 0) {
    console.log('没有任务数据，创建空任务提示')
    const emptyTaskData = {
      fileName: '无任务',
      filePath: '无任务',
      fileSize: 0,
      realIndex: -1,
      isSpecialEmptyNode: true,
      status: TaskParseStatus.LOADING,
    }

    const emptyKey = 'empty-all-tasks'
    const emptyLeaf = (leafMap.value[emptyKey] = Object.assign({}, emptyTaskData, {
      type: 'leaf',
      fullPath: emptyKey,
      name: '暂无解析任务',
      fileSize: 0, // 只使用 fileSize
      suffix: '',
      key: emptyKey,
      parent: branchMap.value[''], // 直接挂载到根节点
      default: false,
      icon: 'file-type-folder',
      level: 0,
      isRoot: false,
      taskType: defaultTaskType,
      isSpecialEmptyNode: true,
      status: TaskParseStatus.LOADING,
      fileCategory: 'other', // 添加默认分类
    }))

    branchMap.value[''].children.push(emptyLeaf)
    tree.value = Object.freeze(branchMap.value[''].children)
    return
  }

  // 遍历每个任务，根据任务类型分别处理
  taskDataArrayWithDetail.forEach((taskData, taskIndex) => {
    if (!taskData) return

    //   以url作为task的key
    const currentTaskId = taskData.url || `task-${taskIndex}`

    // 获取任务标题，优先从 optionsExtData 中获取 fileName

    const fileNameInExtData = props.optionsExtData[taskData.url]?.fileName
    console.log(
      `🔍 获取 optionsExtData 中的 fileName: ${fileNameInExtData}`,
      'taskTitle',
      taskData.title
    )

    const taskTitle = fileNameInExtData || taskData.title || `任务 ${taskIndex + 1}`

    // 从任务数据中获取taskType，如果没有则使用默认值
    const currentTaskType = taskData.taskType !== undefined ? taskData.taskType : defaultTaskType

    // 初始化任务的文件缓存
    multiTaskFilesCache.value[currentTaskId] = {}
    multiTaskCheckedKeys.value[currentTaskId] = []

    console.log(
      `🔄 处理任务 ${taskIndex}: ${taskTitle}, taskId: ${currentTaskId}, 状态: ${taskData.status}, 类型: ${currentTaskType}`
    )

    // 检查该taskId的节点是否已经存在于根节点的children中
    let existingNodeIndex = branchMap.value[''].children.findIndex(
      child => child.taskId === currentTaskId
    )
    let existingNode =
      existingNodeIndex !== -1 ? branchMap.value[''].children[existingNodeIndex] : null

    console.log(
      `🔍 检查已存在节点: taskId=${currentTaskId}, 存在=${!!existingNode}, 索引=${existingNodeIndex}`,
      'currentTaskType',
      currentTaskType,
      currentTaskType === TaskType.Emule,
      TaskType.Emule
    )

    // 根据任务类型分别处理
    switch (currentTaskType) {
      case TaskType.Magnet:
        // 磁力链任务处理
        processMagnetTask(
          taskData,
          taskIndex,
          currentTaskId,
          taskTitle,
          currentTaskType,
          existingNode,
          existingNodeIndex
        )
        break
      case TaskType.Bt:
        // BT种子任务处理
        processBtTask(
          taskData,
          taskIndex,
          currentTaskId,
          taskTitle,
          currentTaskType,
          existingNode,
          existingNodeIndex
        )
        break

      case TaskType.P2sp:
        // P2SP任务处理
        processP2spTask(
          taskData,
          taskIndex,
          currentTaskId,
          taskTitle,
          currentTaskType,
          existingNode,
          existingNodeIndex
        )
        break

      case TaskType.Emule:
        // Emule 任务处理
        processEmuleTask(
          taskData,
          taskIndex,
          currentTaskId,
          taskTitle,
          currentTaskType,
          existingNode,
          existingNodeIndex
        )
        break

      default:
        console.warn(`⚠️ 未知的任务类型: ${currentTaskType}, 使用默认处理`)
        // 对于未知类型，按照磁力链任务处理
        processMagnetTask(
          taskData,
          taskIndex,
          currentTaskId,
          taskTitle,
          currentTaskType,
          existingNode,
          existingNodeIndex
        )
        break
    }
  })

  console.log(
    `🔍 遍历完成，准备排序。根节点children长度:`,
    branchMap.value['']?.children?.length || 0
  )

  // 确保根节点的children按照taskIndex排序
  if (branchMap.value[''] && branchMap.value[''].children) {
    branchMap.value[''].children.sort((a, b) => {
      const aIndex = a.taskIndex !== undefined ? a.taskIndex : 999
      const bIndex = b.taskIndex !== undefined ? b.taskIndex : 999
      return aIndex - bIndex
    })
    console.log(
      '📊 任务节点排序后的顺序:',
      branchMap.value[''].children.map(child => ({
        name: child.name,
        taskIndex: child.taskIndex,
        taskId: child.taskId,
        type: child.type,
        taskType: child.taskType,
        fileCategory: child.fileCategory, // 添加分类信息到日志
      }))
    )
  }

  // 设置默认选中项
  initializeDefaultCheckedKeys()

  // 根据初始化的defaultCheckedKeys设置选中状态
  if (defaultCheckedKeys.value && defaultCheckedKeys.value.length > 0) {
    checkedKeys.value = defaultCheckedKeys.value
    console.log('[TaskList] 根据defaultCheckedKeys设置选中状态:', defaultCheckedKeys.value)
  }
  // 计算分支大小
  caculateBranchSize(branchMap.value[''])

  // 冻结树形数据
  tree.value = Object.freeze(branchMap.value[''].children.slice())

  console.log(
    '✅ 多任务树形结构构建完成，最终tree.value:',
    tree.value.map((child, index) => ({
      UI索引: index,
      name: child.name,
      taskIndex: child.taskIndex,
      type: child.type,
      taskType: child.taskType,
      fileCategory: child.fileCategory, // 添加分类信息到日志
    })),
    branchMap.value[''].children.slice()
  )

  // 在数据初始化完成后，执行跳转逻辑
  if (defaultCheckedKeys.value && defaultCheckedKeys.value.length > 0) {
    const firstKey = defaultCheckedKeys.value[0]
    nextTick(() => {
      jumpToRowWithRetry(firstKey, '数据初始化完成后')
    })
  }
}

/**
 * 更新选中的节点keys
 * @param {Array} checkedKey - 选中的key数组
 * @param {string} key - 当前操作的key
 * @param {boolean} checked - 是否选中
 */
const updateCheckedKeys = (checkedKey, key, checked) => {
  checkedKeys.value = checkedKey

  console.log('✅ [updateCheckedKeys] 更新完成:', {
    操作后选中项数量: checkedKeys.value.length,
    操作后选中项: checkedKeys.value,
    状态变化: checkedKeys.value.length !== checkedKey.length ? '有变化' : '无变化',
  })

  caculateBranchSize({ children: tree.value })
}

/**
 * 计算选中文件的下标（通用函数，支持多任务）
 * @param {Array} keys - 选中的key数组
 * @returns {Object} 包含按任务ID分组的 indexes 和 validLeaves 的对象
 */
const calculateSelectedFileIndexes = keys => {
  const result = {}

  // console.log('🔍 [calculateSelectedFileIndexes] 开始计算选中文件下标:', keys)

  const validLeaves = keys
    .map(key => {
      // 直接通过key查找叶子节点
      const leaf = leafMap.value[key]
      if (leaf) {
        // console.log('🎯 [calculateSelectedFileIndexes] 找到匹配的leaf:', {
        //   key: key,
        //   leafKey: leaf.key,
        //   taskType: leaf.taskType,
        //   realIndex: leaf.realIndex,
        //   url: leaf.url,
        //   taskId: leaf.taskId,
        //   isSpecialEmptyNode: leaf.isSpecialEmptyNode
        // })
        return leaf
      }
      console.log('❌ [calculateSelectedFileIndexes] 未找到匹配的leaf:', key)
      return null
    })
    .filter(leaf => {
      // 只保留文件节点（排除目录）且不是特殊的空节点
      return leaf && leaf.type === 'leaf' && !leaf.isSpecialEmptyNode && leaf.realIndex !== -1
    })

  // console.log(
  //   '✅ [calculateSelectedFileIndexes] 有效的叶子节点:',
  //   validLeaves.map(leaf => ({
  //     key: leaf.key,
  //     taskType: leaf.taskType,
  //     realIndex: leaf.realIndex,
  //     taskId: leaf.taskId,
  //   }))
  // )

  // 按任务ID分组处理
  validLeaves.forEach(leaf => {
    const taskId = leaf.taskId || 'default'

    if (!result[taskId]) {
      result[taskId] = {
        indexes: [],
        validLeaves: [],
      }
    }

    result[taskId].validLeaves.push(leaf)

    // 获取真实的文件索引
    if (leaf.realIndex !== undefined && leaf.realIndex !== null && leaf.realIndex !== -1) {
      result[taskId].indexes.push(leaf.realIndex)
    }
  })

  console.log('📊 [calculateSelectedFileIndexes] 最终结果:', result)
  return result
}

/**
 * 监听选中文件变化（支持多任务）
 */
watch(checkedKeys, val => {
  // 使用通用函数计算选中文件下标和调试信息
  const resultByTask = calculateSelectedFileIndexes(val)

  // 更新多任务选中状态缓存
  Object.keys(resultByTask).forEach(taskId => {
    multiTaskCheckedKeys.value[taskId] = resultByTask[taskId].validLeaves.map(leaf => leaf.key)
  })

  // 准备发送给父组件的数据
  const emitData = {}

  // 如果是多任务模式
  if (Array.isArray(allTaskDataArray.value) && allTaskDataArray.value.length > 0) {
    allTaskDataArray.value.forEach(taskInfo => {
      const taskId = taskInfo.url
      if (taskId && resultByTask[taskId]) {
        emitData[taskId] = {
          fileIndexes: resultByTask[taskId].indexes,
          fileKeys: resultByTask[taskId].validLeaves.map(leaf => leaf.key),
        }
      } else if (taskId) {
        // 如果任务存在但没有选中文件，也要发送空数组
        emitData[taskId] = {
          fileIndexes: [],
          fileKeys: [],
        }
      }
    })
  } else {
    // 兼容空数组情况
    const defaultTaskId = 'default'
    const taskResult = resultByTask[defaultTaskId] || { indexes: [], validLeaves: [] }

    emitData[defaultTaskId] = {
      fileIndexes: taskResult.indexes,
      fileKeys: taskResult.validLeaves.map(leaf => leaf.key),
    }
  }

  // 将index传递给父组件
  emit('checkedFileIndexes', emitData)
})

/**
 * 公共的任务处理函数
 * @param {Array} taskDataValue - 任务数据数组
 * @param {string} source - 数据来源，用于调试日志
 */
const processTaskData = (allUrlsWithType, source = 'unknown') => {
  console.log(`TaskTable 监听到数据变化 (${source}):`, allUrlsWithType)

  // 现在支持磁力链和P2SP混合的任务数组，直接处理多任务模式
  if (Array.isArray(allUrlsWithType)) {
    //   把数组拼接的又像是之前的啦
    const validTasks = allUrlsWithType.map(urlWithType => {
      const { url } = urlWithType
      const dataInfo = props.dataMap[url]
      return {
        ...urlWithType,
        ...dataInfo,
      }
    })

    parseMultipleTasksFileList(validTasks)

    // 清空文件缓存并重新构建
    filesCache.value = {}

    // 为每个任务构建文件缓存
    validTasks.forEach(taskData => {
      const { url = 'unknown-task', taskType, fileName, fileSize = 0, fileLists = [] } = taskData
      // 以url作为key
      const taskId = url

      if (!multiTaskFilesCache.value[url]) {
        multiTaskFilesCache.value[taskId] = {}
      }

      const isP2spTask = taskType === TaskType.P2sp || taskType === 1
      const isEmuleTask = taskType === TaskType.Emule || taskType === 4

      if (isP2spTask || isEmuleTask) {
        // P2SP任务：创建单文件缓存
        if (fileName || url) {
          const realFileName =
            taskData.fileName || extractFileNameFromUrl(taskData.url) || taskData.title
          multiTaskFilesCache.value[taskId][0] = {
            fileName: realFileName,
            filePath: '',
            fileSize,
            realIndex: 0,
          }
          // 同时也添加到全局缓存以保持兼容性
          if (filesCache.value) {
            filesCache.value[0] = multiTaskFilesCache.value[taskId][0]
          }
        }
      } else {
        // 磁力链任务：使用fileLists
        if (fileLists && fileLists.length > 0) {
          fileLists.forEach(file => {
            multiTaskFilesCache.value[taskId][file.realIndex] = file
            // 同时也添加到全局缓存以保持兼容性
            if (filesCache.value) {
              filesCache.value[file.realIndex] = file
            }
          })
        }
      }
    })
  } else {
    console.log(`TaskTable 接收到非数组数据 (${source})，忽略处理`)
  }
}

/**
 * 事件处理函数
 */
const handleClickLabel = key => {
  if (table.value) {
    table.value.check(key)
  }
}

/**
 * 处理全选/全不选事件
 * @param {boolean} isChecked - 是否全选（true=全选，false=全不选）
 */
const handleCheckAll = (isChecked: boolean) => {
  // 如果是全不选，重置所有文件类型过滤器
  if (!isChecked) {
    console.log('🔄 [handleCheckAll] 全不选操作，重置所有文件类型过滤器')
    fileTypeFilters.video = false
    fileTypeFilters.image = false
    fileTypeFilters.zip = false
    fileTypeFilters.other = false
  }
}

const handleSelectOnly = (row, rowKey) => {
  console.log('✅ [handleSelectOnly] 开始执行仅选此项操作', 'row', row, 'rowKey', rowKey)

  // 使用Table组件的selectOnly方法
  if (table.value && typeof table.value.selectOnly === 'function') {
    table.value.selectOnly(rowKey)
  } else {
    console.warn('⚠️ [handleSelectOnly] Table组件缺少selectOnly方法，使用备用方案')
    // 备用方案：直接操作数据
    checkedKeys.value = [rowKey]
    updateCheckedKeys([rowKey], rowKey, true)
  }
}

const handleRename = (row, rowKey) => {
  // 验证是否符合重命名条件
  if (!shouldShowRename(row)) {
    console.warn('只有解析成功的磁力链根节点才能重命名')
    return
  }

  // 保存原始名称，用于取消编辑时恢复
  row.originalName = row.name
  // 设置编辑状态
  row.isEditing = true

  // 下一帧聚焦到输入框并选中文本
  nextTick(() => {
    // 查找当前编辑状态的输入框
    const editInput = document.querySelector('.edit-input') as HTMLInputElement
    if (editInput) {
      editInput.focus()
      editInput.select()
    }
  })
}

// 编辑任务
const handleEdit = row => {
  console.log('✏️ 点击编辑按钮:', row)

  const taskUrl = row.url || row.taskId
  if (!taskUrl) {
    console.warn('[TaskList] 无法获取任务URL，无法打开设置')
    return
  }

  // 获取当前任务的设置
  const currentTaskSetting = taskSettingStore.getTaskSettings(taskUrl)

  // 直接调用任务设置对话框
  showTaskSettingDialog(taskUrl, currentTaskSetting, {
    useCustomPosition: true, // 使用自定义位置
    selector: '.url-input-container',
  })
}

const handleRetryMagnetTask = async row => {
  console.log('重新解析磁力链', row)

  try {
    await newTaskStore.parseMagnetLinkInfo({
      url: row.url || '',
      forceReparse: true,
    })

    console.log('磁力链重新解析完成')
  } catch (error) {
    console.error('重新解析磁力链失败:', error)
  }
}

const handleFinishEdit = row => {
  console.log('handleFinishEdit', row)
  // 验证新名称
  if (!row.name || row.name.trim() === '') {
    // 如果名称为空，恢复原始名称
    row.name = row.originalName
  } else {
    // 更新名称（这里可以添加更多验证逻辑）
    const newName = row.name.trim()

    // 🔥 关键修复：保存自定义任务名称到 task-setting store
    if (row.url) {
      taskSettingStore.setCustomTaskName(row.url, newName)
      console.log(`[TaskList] 自定义任务名称已保存到store: ${row.url} -> ${newName}`)
    } else {
      console.warn('[TaskList] 无法保存自定义任务名称：缺少任务URL')
    }

    row.name = newName
  }

  // 退出编辑状态
  row.isEditing = false
  delete row.originalName
}

const handleCancelEdit = row => {
  // 恢复原始名称
  row.name = row.originalName
  // 退出编辑状态
  row.isEditing = false
  delete row.originalName
}

/**
 * 判断是否应该显示重命名按钮
 *
 * 重命名按钮显示条件：
 * 1. 必须是根节点（level = 0）- 只有任务根节点才能重命名
 * 2. 不能是特殊空节点（!isSpecialEmptyNode）- 排除解析中/失败的特殊提示节点
 * 3. 必须是支持重命名的任务类型（磁力链任务或P2SP任务）
 *
 * 使用场景：
 * - 用户想要重命名已解析的磁力链任务或P2SP任务
 * - 悬停操作栏中动态显示重命名按钮
 *
 * @param {Object} row - 表格行数据对象
 * @param {string} row.name - 节点名称
 * @param {number} row.level - 节点层级（0=根节点，1+=子节点）
 * @param {boolean} row.isSpecialEmptyNode - 是否为特殊空节点（解析中/失败的提示节点）
 * @param {number} row.taskType - 任务类型（1=P2SP，2=磁力链，参考TaskType枚举）
 * @param {string} [row.taskId] - 任务ID（可选）
 *
 * @returns {boolean} true=显示重命名按钮，false=隐藏重命名按钮
 *
 */
const shouldShowRename = row => {
  // 必须是根节点（level = 0）
  if (row.level !== 0) {
    // console.log('shouldShowRename: 非根节点，不显示重命名按钮', { level: row.level })
    return false
  }

  // 特殊的空节点不显示重命名按钮
  if (row.isSpecialEmptyNode) {
    // console.log('shouldShowRename: 特殊空节点，不显示重命名按钮')
    return false
  }

  // 必须是支持重命名的任务类型（P2SP任务 = 1 或 磁力链任务 magnet ）
  if (row.taskType !== TaskType.P2sp && row.taskType !== TaskType.Magnet) {
    // console.log('shouldShowRename: 不支持重命名的任务类型，不显示重命名按钮', {
    //   taskType: row.taskType,
    // })
    return false
  }

  return true
}

/**
 * 判断是否应该显示重试按钮
 *
 * 重试按钮显示条件：
 * 1. 必须是磁力链任务（taskType === TaskType.Bt）
 * 2. 在dataMap中对应的status必须是'error'
 *
 * @param {Object} row - 表格行数据对象
 * @returns {boolean} true=显示重试按钮，false=隐藏重试按钮
 */
const shouldShowRetry = row => {
  // 必须是磁力链任务
  if (row.taskType !== TaskType.Bt) {
    return false
  }

  // 检查dataMap中对应的状态
  const taskUrl = row.url || row.taskId
  if (!taskUrl || !props.dataMap[taskUrl]) {
    return false
  }

  const taskDetail = props.dataMap[taskUrl]
  // 只有在dataMap中status为'error'时才显示重试按钮
  return taskDetail.status === 'error'
}

/**
 * 判断是否应该显示"仅选此项"按钮
 * 新逻辑： 所有节点都显示 “仅选此项” 按钮 ！！！！
 * 旧逻辑显示条件：
 * 1. 必须是叶子节点（type === 'leaf'）
 * 2. 不能是特殊空节点（!isSpecialEmptyNode）
 * 3. 必须是真实文件节点（realIndex !== -1）
 * 4. 必须是磁力链任务（taskType === TaskType.Magnet）
 *
 * @param {Object} row - 表格行数据对象
 * @returns {boolean} true=显示按钮，false=隐藏按钮
 */
const shouldShowSelectOnly = row => {
  // console.log('shouldShowSelectOnly: 判断是否显示"仅选此项"按钮', 'row', row)

  // 必须是叶子节点（文件节点）
  if (row.type !== 'leaf') {
    // console.log('shouldShowSelectOnly: 非叶子节点，不显示"仅选此项"按钮', { type: row.type })
    return false
  }

  // 特殊的空节点不显示"仅选此项"按钮
  if (row.isSpecialEmptyNode) {
    // console.log('shouldShowSelectOnly: 特殊空节点，不显示"仅选此项"按钮')
    return false
  }

  // 必须是真实文件节点（realIndex !== -1）
  if (row.realIndex === -1) {
    // console.log('shouldShowSelectOnly: 无效文件节点，不显示"仅选此项"按钮', {
    //   realIndex: row.realIndex,
    // })
    return false
  }

  // 非磁力链任务也显示 “仅选此项”  暂不删除-产品逻辑可能会更改
  // 必须是磁力链任务（taskType === TaskType.Magnet）
  // if (row.taskType !== TaskType.Magnet) {
  //   // console.log('shouldShowSelectOnly: 非磁力链任务，不显示"仅选此项"按钮', {
  //   //   taskType: row.taskType,
  //   //   expectedTaskType: TaskType.Magnet,
  //   // })
  //   return false
  // }
  return true
}

// 任务组名称编辑事件处理
const handleTaskGroupEditStart = value => {
  console.log('任务组名称开始编辑:', value)
}

const handleTaskGroupEditConfirm = (oldValue, newValue) => {
  console.log('任务组名称编辑确认:', oldValue, '->', newValue)
  // computed变量的setter会自动处理store和emit的同步
  taskGroupName.value = newValue
}

const handleTaskGroupEditCancel = value => {
  console.log('任务组名称编辑取消:', value)
}

/**
 * 获取增强的文件大小
 * @param {Object} row - 表格行数据
 * @param {string|number} originalSize - 原始大小值
 * @returns {number} 增强后的文件大小
 */
const getEnhancedFileSize = (row, originalSize) => {
  // console.log('row, originalSize', row, originalSize, row.fileSize)

  // 优先使用 row.fileSize
  if (row.fileSize && row.fileSize > 0) {
    return row.fileSize
  }

  // 如果原始大小不为0，直接返回
  if (originalSize && originalSize > 0) {
    return originalSize
  }

  // 尝试从dataMap中获取文件大小
  if (row.url && props.dataMap[row.url]) {
    const detailInfo = props.dataMap[row.url]
    if (detailInfo.fileSize && detailInfo.fileSize > 0) {
      return detailInfo.fileSize
    }
  }

  console.log(
    `⚠️ 无法获取文件大小信息:`,
    {
      rowUrl: row.url,
      taskId: row.taskId,
      originalSize: originalSize,
      hasDataMap: !!props.dataMap[row.url || row.taskId],
    },
    row.name,
    row.fileSize
  )

  return originalSize || 0
}

/**
 * 统一的任务数据处理函数
 * 主要监听dataMap变化，taskData提供URL列表
 * @param {string} source - 数据来源，用于调试日志
 */
function processAllTaskData(source = 'unknown') {
  console.log(`🔄 统一数据处理触发，来源: ${source}`)

  const currentTaskData = allTaskDataArray.value
  if (currentTaskData && currentTaskData.length > 0) {
    console.log(`📋 处理 ${currentTaskData.length} 个任务`)

    // 主要从dataMap获取详细信息，taskData提供基础URL和taskType
    const enhancedTasks = currentTaskData.map(urlWithType => {
      const { url, taskType } = urlWithType
      const dataInfo = props.dataMap[url] || {}

      // 先使用dataMap的所有信息，然后确保关键字段正确
      const enhanced = {
        ...dataInfo, // dataMap包含所有详细信息
        url: url, // 明确设置url，避免被dataMap覆盖
        taskType: dataInfo.taskType || taskType, // taskType优先使用dataMap中的值
      }

      return enhanced
    })

    console.log(`📊 合并后的任务数据:`, enhancedTasks)

    // 调用原有的处理逻辑
    processTaskData(enhancedTasks, source)
  } else {
    console.log(`⚠️ 无任务数据可处理`)
  }
}

// 监听dataMap变化 - 统一数据源监听
watch(
  () => props.dataMap,
  newDataMap => {
    console.log('📊 监听dataMap变化，重新处理所有任务数据')
    // 打印详细的dataMap信息
    if (newDataMap && Object.keys(newDataMap).length > 0) {
      Object.keys(newDataMap).forEach(url => {
        const detailInfo = newDataMap[url]
        console.log(`📋 URL: ${url}`, detailInfo, props.dataMap)
      })
    }

    processAllTaskData('dataMapChange')
  },
  { immediate: true, deep: true }
)

// 监听外部传入的默认选中keys变化
watch(
  () => props.customCheckedKeys,
  (newCustomCheckedKeys, oldCustomCheckedKeys) => {
    console.log('[TaskList] 监听到外部customCheckedKeys变化:', {
      new: newCustomCheckedKeys,
      old: oldCustomCheckedKeys,
    })

    // 如果树形结构已经构建完成，重新初始化defaultCheckedKeys
    if (Object.keys(leafMap.value).length > 0 || Object.keys(branchMap.value).length > 0) {
      initializeDefaultCheckedKeys()

      // 根据新的defaultCheckedKeys设置选中状态
      if (defaultCheckedKeys.value && defaultCheckedKeys.value.length > 0) {
        checkedKeys.value = defaultCheckedKeys.value
        console.log('[TaskList] 根据新的defaultCheckedKeys设置选中状态:', defaultCheckedKeys.value)

        // 当外部传入的选中项变化时，也需要跳转到对应的行
        const firstKey = defaultCheckedKeys.value[0]
        nextTick(() => {
          jumpToRowWithRetry(firstKey, 'customCheckedKeys变化后')
        })
      }
    }
  },
  { immediate: false, deep: true }
)
/**
 * 计算选中的任务数量
 * 基于选中的文件来统计涉及的不同任务数
 */
const selectedTaskCount = computed(() => {
  if (!checkedKeys.value || checkedKeys.value.length === 0) {
    return 0
  }

  // 使用 calculateSelectedFileIndexes 获取按任务分组的选中文件
  const resultByTask = calculateSelectedFileIndexes(checkedKeys.value)

  // 只统计有选中文件的任务数量
  const tasksWithSelectedFiles = Object.keys(resultByTask).filter(taskId => {
    const taskResult = resultByTask[taskId]
    return taskResult && taskResult.validLeaves && taskResult.validLeaves.length > 0
  })

  console.log('[TaskList] selectedTaskCount 计算:', {
    checkedKeysLength: checkedKeys.value.length,
    resultByTask,
    tasksWithSelectedFiles,
    selectedTaskCount: tasksWithSelectedFiles.length,
  })

  return tasksWithSelectedFiles.length
})

/**
 * 自定义选中文件数量计算
 * 包括选中的叶子节点和磁力链分支节点
 * 待计算TODO：数值不准确
 */
const customSelectedFileCount = computed(() => {
  if (!checkedKeys.value || checkedKeys.value.length === 0) {
    return 0
  }

  let count = 0

  checkedKeys.value.forEach(key => {
    // 直接通过key查找叶子节点
    const foundLeaf = leafMap.value[key]

    if (foundLeaf) {
      // 真正的文件节点（不是特殊空节点）
      if (foundLeaf.type === 'leaf' && !foundLeaf.isSpecialEmptyNode) {
        count++
      }
    } else {
      // 检查分支节点（磁力链文件夹）
      // const branch = branchMap.value[key]
      // if (branch && branch.type === 'branch') {
      //   // 磁力链分支节点也算作一个文件
      //   count++
      // }
    }
  })

  console.log('[TaskList] customSelectedFileCount 计算:', {
    checkedKeysLength: checkedKeys.value.length,
    customSelectedFileCount: count,
    checkedKeys: checkedKeys.value,
  })

  return count
})

/**
 * 是否支持合并为任务组
 * 条件：选中的任务数大于 1 且 download-path-type 的类型不是 cloud
 */
const supportTaskGroupMerge = computed(() => {
  const selectedCount = selectedTaskCount.value
  const currentPathType = downloadPathTypeStore.getCurrentPathType()

  console.log('[TaskList] supportTaskGroupMerge 计算:', {
    selectedTaskCount: selectedCount,
    currentPathType,
    isCloud: currentPathType === DownloadPathType.Cloud,
  })

  // 如果选中的任务数小于等于 1，不支持合并为任务组
  if (selectedCount <= 1) {
    console.log('[TaskList] 不支持合并为任务组: 选中的任务数 <= 1')
    return false
  }

  // 如果 download-path-type 的类型是 cloud，不支持合并为任务组
  if (currentPathType === DownloadPathType.Cloud) {
    console.log('[TaskList] 不支持合并为任务组: 路径类型为云盘')
    return false
  }

  console.log('[TaskList] 支持合并为任务组')
  return true
})

// 统一的文件类型过滤器监听器
watch(
  () => fileTypeFilters,
  (newFilters, oldFilters) => {
    console.log('[TaskList] fileTypeFilters 变化:', {
      newFilters,
      oldFilters,
    })
  },
  { deep: true }
)

// 分别监听各个文件类型过滤器的变化
watch(
  () => fileTypeFilters.video,
  (newValue, oldValue) => {
    // 排除初始化时的触发（oldValue 为 undefined 的情况）
    if (oldValue !== undefined && newValue !== oldValue) {
      console.log(`🎬 视频文件过滤器变化: ${oldValue} -> ${newValue}`)
      toggleFileSelectionByCategoryOptimal('video', newValue)
    }
  }
)

watch(
  () => fileTypeFilters.image,
  (newValue, oldValue) => {
    if (oldValue !== undefined && newValue !== oldValue) {
      console.log(`🖼️ 图片文件过滤器变化: ${oldValue} -> ${newValue}`)
      toggleFileSelectionByCategoryOptimal('image', newValue)
    }
  }
)

watch(
  () => fileTypeFilters.zip,
  (newValue, oldValue) => {
    if (oldValue !== undefined && newValue !== oldValue) {
      console.log(`🗜️ 压缩包文件过滤器变化: ${oldValue} -> ${newValue}`)
      toggleFileSelectionByCategoryOptimal('zip', newValue)
    }
  }
)

watch(
  () => fileTypeFilters.other,
  (newValue, oldValue) => {
    if (oldValue !== undefined && newValue !== oldValue) {
      console.log(`📄 其他文件过滤器变化: ${oldValue} -> ${newValue}`)
      toggleFileSelectionByCategoryOptimal('other', newValue)
    }
  }
)

/**
 * 文件分类存在性计算属性
 * 用于判断tree中是否存在video/image/other类型的文件
 */
const fileCategoryExists = computed(() => {
  const exists = { video: false, image: false, zip: false, other: false }
  const check = (nodes: any[]) => {
    nodes.forEach(node => {
      if (node.type === 'branch') {
        check(node.children)
      } else if (node.type === 'leaf' && !node.isSpecialEmptyNode) {
        if (node.fileCategory === 'video') exists.video = true
        if (node.fileCategory === 'image') exists.image = true
        if (node.fileCategory === 'zip') exists.zip = true
        if (node.fileCategory === 'other') exists.other = true
      }
    })
  }
  check(tree.value)
  console.log('[TaskList] fileCategoryExists 计算:', exists)
  return exists
})

// 用于控制是否暂停 checkedKeys 的 watch 监听器

/**
 * 根据文件分类切换文件的选中状态（长期优化版本）
 * 使用Table组件新增的batchCheck方法，更优雅和安全
 * @param {string} category - 文件分类 ('video' | 'image' | 'other')
 * @param {boolean} shouldSelect - 是否选中该分类的文件
 */
const toggleFileSelectionByCategoryOptimal = (category, shouldSelect) => {
  console.log(
    `🎯 [toggleFileSelectionByCategoryOptimal] ${shouldSelect ? '选中' : '取消选中'} ${category} 类型文件`
  )

  // 检查table组件引用是否可用
  if (!table.value) {
    console.warn('[toggleFileSelectionByCategoryOptimal] Table组件引用不可用')
    return
  }

  // 使用缓存优化选中状态检查 - O(1) 查找
  const checkedKeysSet = new Set(checkedKeys.value)

  let hasChanges = false
  const keysToUpdate: Array<{ key: string; checked: boolean }> = []

  // 遍历所有叶子节点，收集需要更新的keys
  Object.keys(leafMap.value).forEach(nodeKey => {
    const leaf = leafMap.value[nodeKey]

    // 只处理真实文件节点（排除特殊空节点）
    if (
      leaf &&
      leaf.type === 'leaf' &&
      !leaf.isSpecialEmptyNode &&
      leaf.fileCategory === category
    ) {
      const isCurrentlySelected = checkedKeysSet.has(nodeKey)

      if (shouldSelect && !isCurrentlySelected) {
        keysToUpdate.push({ key: nodeKey, checked: true })
        hasChanges = true
      } else if (!shouldSelect && isCurrentlySelected) {
        keysToUpdate.push({ key: nodeKey, checked: false })
        hasChanges = true
      }
    }
  })

  if (!hasChanges) {
    console.log(`📊 [toggleFileSelectionByCategoryOptimal] ${category} 分类无需变化`)
    return
  }

  console.log(
    `🚀 [toggleFileSelectionByCategoryOptimal] 使用Table.batchCheck批量更新 ${keysToUpdate.length} 个 ${category} 文件`
  )

  const startTime = performance.now()

  // 使用Table组件的batchCheck方法进行批量更新
  table.value.batchCheck(keysToUpdate)

  const endTime = performance.now()
  console.log(
    `✅ [toggleFileSelectionByCategoryOptimal] ${category} 分类批量更新完成，用时: ${(endTime - startTime).toFixed(2)}ms`
  )
}

/**
 * 将选中的keys按照文件分类进行分组
 * @param {Array} keys - 选中的key数组，可选参数，默认使用当前选中的keys
 * @returns {Object} 按文件分类分组的结果
 */
const categorizeSelectedKeys = (keys: string[] | null = null) => {
  const targetKeys = keys || checkedKeys.value

  console.log('🔍 [categorizeSelectedKeys] 开始分类选中的keys:', targetKeys)

  const categorizedResult = {
    video: {
      keys: [] as string[],
      nodes: [] as any[],
      count: 0,
    },
    image: {
      keys: [] as string[],
      nodes: [] as any[],
      count: 0,
    },
    zip: {
      keys: [] as string[],
      nodes: [] as any[],
      count: 0,
    },
    other: {
      keys: [] as string[],
      nodes: [] as any[],
      count: 0,
    },
    total: {
      keys: targetKeys,
      count: targetKeys.length,
    },
  }

  targetKeys.forEach(key => {
    // 直接通过key查找叶子节点
    const leaf = leafMap.value[key]

    if (leaf && leaf.type === 'leaf' && !leaf.isSpecialEmptyNode) {
      // 只处理真实文件节点（排除特殊空节点）
      const category = leaf.fileCategory || 'other'

      if (category === 'video' || category === 'image' || category === 'other') {
        categorizedResult[category].keys.push(key)
        categorizedResult[category].nodes.push(leaf)
        categorizedResult[category].count++

        // console.log(`📁 [categorizeSelectedKeys] 文件分类: ${leaf.name} -> ${category}`)
      } else {
        // 如果分类不存在，归类到other
        categorizedResult.other.keys.push(key)
        categorizedResult.other.nodes.push(leaf)
        categorizedResult.other.count++

        // console.log(`📁 [categorizeSelectedKeys] 未知分类归入other: ${leaf.name} -> ${category}`)
      }
    } else {
      // 处理分支节点或特殊节点
      const branch = branchMap.value[key]
      if (branch && branch.type === 'branch') {
        // 分支节点归类到other
        categorizedResult.other.keys.push(key)
        categorizedResult.other.nodes.push(branch)
        categorizedResult.other.count++

        // console.log(`📁 [categorizeSelectedKeys] 分支节点归入other: ${branch.name}`)
      }
    }
  })

  console.log('📊 [categorizeSelectedKeys] 分类结果:', {
    video: categorizedResult.video.count,
    image: categorizedResult.image.count,
    zip: categorizedResult.zip.count,
    other: categorizedResult.other.count,
    total: categorizedResult.total.count,
  })

  return categorizedResult
}

/**
 * 当前选中keys的分类结果（计算属性）
 * 实时更新选中文件的分类统计
 */
const selectedKeysByCategory = computed(() => {
  return categorizeSelectedKeys()
})

// 暴露给父组件使用的函数和属性
defineExpose({
  categorizeSelectedKeys,
  selectedKeysByCategory,
})
</script>

<style scoped lang="scss">
@import '@root/modal-renderer/src/components/new-task/task-list/task-list.scss';

.tasks-content-wrapper {
  flex: 1;
}
</style>
