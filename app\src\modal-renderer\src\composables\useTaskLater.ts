import { computed, type ComputedRef } from 'vue'
import { useNewTaskConfigStore } from '@root/modal-renderer/src/stores/new-task-config'
import { storeToRefs } from 'pinia'
import { LinkSaver } from '@root/modal-renderer/src/utils/link-saver'
import { LinkSaveScene, type LinkSaveOptions } from '@root/modal-renderer/types/link-saver.type'
import type { 
  HandleTaskLaterParams,
  HandleTaskLaterResult,
} from '@root/modal-renderer/types/new-task.type'

/**
 * "稍后"功能相关的组合式函数
 * 处理稍后按钮的状态和逻辑
 */
export function useTaskLater() {
  const newTaskConfigStore = useNewTaskConfigStore()
  const { isMergeTask } = storeToRefs(newTaskConfigStore)

  /**
   * 计算稍后按钮是否应该禁用
   * 当选择了合并为任务组时，稍后按钮应该禁用
   */
  const isLaterButtonDisabled = computed(() => {
    return isMergeTask.value
  })

  /**
   * 处理稍后操作
   * 保存所有待处理任务到链接中心
   */
  const handleTaskLater = async (params: HandleTaskLaterParams): Promise<HandleTaskLaterResult> => {
    const {
      allUrlsWithType,
      dataMap,
      urlExtraDataMap,
      checkedFileIndexes,
      hasValidTasks = false,
      optionsExtData,
      scene = ''
    } = params

    console.log('[useTaskLater] 开始处理稍后操作', params)

    // 检查是否有有效任务
    if (!hasValidTasks || allUrlsWithType.length === 0) {
      console.warn('[useTaskLater] 没有有效任务可以稍后处理')
      return {
        success: false,
        message: '没有有效任务可以稍后处理'
      }
    }

    // 检查是否选择了合并为任务组
    if (isMergeTask.value) {
      console.warn('[useTaskLater] 选择了合并为任务组，不允许稍后操作')
      return {
        success: false,
        message: '选择了合并为任务组时，不支持稍后操作'
      }
    }

    try {
      const linkSaver = LinkSaver.getInstance()
      
      const options: LinkSaveOptions = {
        scene: scene || LinkSaveScene.PRE_NEW_TASK,
        actions: ['ACTION_TODO'],
        ignoreEvent: false,
        waitForCompletion: true, // 等待所有任务执行完成，获取真实的保存结果
        status: 'STATUS_DEFERRED', // 稍后处理
      }

      const result = await linkSaver.savePendingAllTasks({
        allUrlsWithType,
        dataMap,
        urlExtraDataMap,
        checkedFileIndexes,
        options,
        optionsExtData
      })

      console.log('[useTaskLater] 稍后处理结果:', result)

      if (result.success) {
        return {
          success: true,
          message: `成功保存 ${result.savedCount} 个任务到链接中心`,
          savedCount: result.savedCount,
          failedCount: result.failedCount
        }
      } else {
        return {
          success: false,
          message: `保存失败，成功: ${result.savedCount}，失败: ${result.failedCount}`,
          savedCount: result.savedCount,
          failedCount: result.failedCount
        }
      }
    } catch (error) {
      console.error('[useTaskLater] 稍后处理失败:', error)
      return {
        success: false,
        message: `稍后处理失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 获取稍后按钮的提示文本
   */
  const getLaterButtonTooltip = computed(() => {
    if (isMergeTask.value) {
      return {
        title: '无法稍后处理',
        text: '选择了合并为任务组时，不支持稍后操作。请取消合并任务组选择后重试。'
      }
    } else {
      return {
        title: '存在没有解析成功的链接，是否继续？',
        text: '如果继续操作，将不会添加这些链接。'
      }
    }
  })

  return {
    // 状态
    isMergeTask,
    isLaterButtonDisabled,
    getLaterButtonTooltip,

    // 方法
    handleTaskLater
  }
}

/**
 * 创建专门用于稍后按钮禁用状态的计算属性
 * 这个函数用于需要更细粒度控制的场景
 */
export function createLaterButtonDisabledComputed(
  hasValidTasksGetter: () => boolean
): ComputedRef<boolean> {
  const { isLaterButtonDisabled } = useTaskLater()

  return computed(() => {
    // 当没有有效任务时禁用
    if (!hasValidTasksGetter()) {
      return true
    }
    
    // 当选择了合并为任务组时禁用
    return isLaterButtonDisabled.value
  })
} 