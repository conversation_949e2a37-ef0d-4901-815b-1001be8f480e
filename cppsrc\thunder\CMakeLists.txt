﻿if (${CMAKE_SYSTEM_NAME} STREQUAL OHOS)
    cmake_minimum_required(VERSION 3.4.1)
elseif (${CMAKE_SYSTEM_NAME} STREQUAL Android)
    cmake_minimum_required(VERSION 3.4.1)
elseif (${CMAKE_SYSTEM_NAME} STREQUAL Linux)
    cmake_minimum_required(VERSION 3.4.1)
elseif (${CMAKE_SYSTEM_NAME} STREQUAL Darwin)
    cmake_minimum_required(VERSION 3.4.1)
elseif (${CMAKE_SYSTEM_NAME} STREQUAL iOS)
    cmake_minimum_required(VERSION 3.4.1)
elseif (${CMAKE_SYSTEM_NAME} STREQUAL Windows)
    cmake_minimum_required(VERSION 3.16)
else()
    message(STATUS, "download kernel not support platform: ${CMAKE_SYSTEM_NAME}")
endif()

project(thunder)
#set(CMAKE_CXX_STANDARD 20)
set(CMAKE_INCLUDE_CURRENT_DIR ON)
set_property(GLOBAL PROPERTY USE_FOLDERS ON)

include_directories(SYSTEM ${CMAKE_CURRENT_SOURCE_DIR}/src)
include_directories(SYSTEM ${CMAKE_CURRENT_SOURCE_DIR}/../include)
include_directories(SYSTEM ${CMAKE_CURRENT_SOURCE_DIR}/../third_part/include)
include_directories(SYSTEM ${CMAKE_CURRENT_SOURCE_DIR}/../common/include)

file(GLOB_RECURSE ALL_SOURCE ${CMAKE_CURRENT_SOURCE_DIR}/*)
foreach(fileItem ${ALL_SOURCE})
    # Get the directory of the source file
    get_filename_component(PARENT_DIR "${fileItem}" DIRECTORY)
    # Remove common directory prefix to make the group
    string(REPLACE "${CMAKE_CURRENT_SOURCE_DIR}" "" GROUP "${PARENT_DIR}")
    # Make sure we are using windows slashes
    string(REPLACE "/" "\\" GROUP "${GROUP}")
    # Group into "Source Files" and "Header Files"
    set(GROUP "${GROUP}")
    source_group("${GROUP}" FILES "${fileItem}")
endforeach()

message(STATUS, "CMAKE_SOURCE_DIR=${CMAKE_SOURCE_DIR}, CMAKE_CURRENT_SOURCE_DIR=${CMAKE_CURRENT_SOURCE_DIR}, PROJECT_BINARY_DIR=${PROJECT_BINARY_DIR}")
message(STATUS, "Current machine architecture: ${CMAKE_SYSTEM_PROCESSOR}")
if (${CMAKE_SYSTEM_NAME} STREQUAL Darwin)
    add_compile_options(-finput-charset=UTF-8)
    add_compile_options(-fexec-charset=UTF-8)
    add_definitions(-DRAPIDJSON_HAS_STDSTRING)
    link_directories("${PROJECT_BINARY_DIR}/common")
    link_directories("${PROJECT_BINARY_DIR}/libuv")
    set(DK_NAME "")
    if (BUSINESS_TYPE STREQUAL OpenSdk)
        find_package(Git)
        execute_process(COMMAND ${GIT_EXECUTABLE} describe --abbrev=40 --dirty --tags --always
            OUTPUT_VARIABLE GIT_REPO_VERSION
            OUTPUT_STRIP_TRAILING_WHITESPACE
            )
        # The variable will be used when file is configured
        configure_file("${CMAKE_CURRENT_SOURCE_DIR}/sdk/dk_version.h.in" "${CMAKE_CURRENT_SOURCE_DIR}/sdk/dk_version.h")
        list(APPEND ALL_SOURCE ${CMAKE_CURRENT_SOURCE_DIR}/sdk/sdk.cpp)
        #给开发平台编译
        add_definitions(-DOPEN_SDK)
        set(DK_NAME "libxl_sdk.a")
        include_directories(SYSTEM ${CMAKE_CURRENT_SOURCE_DIR}/../download_lib/${BUSINESS_TYPE}/macos_universal)
        link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../download_lib/${BUSINESS_TYPE}/macos_universal/${CMAKE_BUILD_TYPE})
        link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../download_lib/${BUSINESS_TYPE}/macos_universal)
    elseif(BUSINESS_TYPE STREQUAL OneThing)
        list(APPEND ALL_SOURCE ${CMAKE_CURRENT_SOURCE_DIR}/sdk/one_thing.cpp)
        #给网心编译
        add_definitions(-DONE_THING)
        set(DK_NAME "libDownloadSDK.so")
        include_directories(SYSTEM ${CMAKE_CURRENT_SOURCE_DIR}/../download_lib/include)
        link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../../../macos/one_thing)
    else()
        #给可能的linux平台编译
        file(GLOB_RECURSE NO_NEED_SOURCE ${CMAKE_CURRENT_SOURCE_DIR}/sdk/*)
        list(REMOVE_ITEM ALL_SOURCE ${NO_NEED_SOURCE})
        set(DK_NAME "libDownloadSDK.so")
        include_directories(SYSTEM ${CMAKE_CURRENT_SOURCE_DIR}/../download_lib/include)
        link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../../../macos/normal)
    endif()
    message(STATUS, "DK_NAME=${DK_NAME}")
    add_library(${PROJECT_NAME} SHARED
            ${ALL_SOURCE}
        )
    target_link_libraries(${PROJECT_NAME} PRIVATE "common" "iconv" ${DK_NAME})

elseif (${CMAKE_SYSTEM_NAME} STREQUAL Windows)
    link_directories("${PROJECT_BINARY_DIR}/../common")
    #link_directories("${PROJECT_BINARY_DIR}/../libuv")

    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Zi /MTd /utf-8")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi /MT /utf-8")

    set(CMAKE_SHARED_LINKER_FLAGS_DEBUG "/INCREMENTAL:NO")
    set(CMAKE_EXE_LINKER_FLAGS_DEBUG "${CMAKE_EXE_LINKER_FLAGS_DEBUG} /DEBUG /OPT:REF /OPT:ICF /PDB:\"${CMAKE_SOURCE_DIR}/pdb/debug/\"")

    set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "/INCREMENTAL:NO")
    set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF /PDB:\"${CMAKE_SOURCE_DIR}/pdb/release/\"")


    include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/third_part/include)
    include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/common/include)
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/include)
    include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/libuv1.44/include)
    include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../include)
    include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../win_common/src)

    if(MSVC)
    set_source_files_properties(resources.rc PROPERTIES LANGUAGE RC)
        # 多处理器编译
        OPTION(USE_MP "use multiple" ON)
        OPTION(ProjectConfig_Global_COMPILE_FLAGS_WITH_MP
            "Set The Global Option COMPILE_FLAGS /MP to target." ON)
        if(ProjectConfig_Global_COMPILE_FLAGS_WITH_MP OR USE_MP)
            set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /MP")
            set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /MP")
        endif()
        set(VS_STARTUP_PROJECT ${PROJECT_NAME})
    endif()

    add_definitions(
                    -DXL_SDK_EXPORTS
                    -D_CRT_SECURE_NO_WARNINGS
                    -D_CRT_NONSTDC_NO_WARNINGS
                    -DWIN32_LEAN_AND_MEAN # 解决windows.h和winsock2.h的冲突问题
                    -DGLOG_NO_ABBREVIATED_SEVERITIES
                    -DRAPIDJSON_HAS_STDSTRING)

    link_directories("${CMAKE_SOURCE_DIR}/../third_part/lib")
    link_directories("$<$<CONFIG:Debug>:${CMAKE_ARCHIVE_OUTPUT_DIRECTORY_DEBUG}>$<$<CONFIG:Release>:${CMAKE_ARCHIVE_OUTPUT_DIRECTORY_RELEASE}>")
    link_directories("${CMAKE_CURRENT_SOURCE_DIR}/../native_core/win/x64/${CMAKE_BUILD_TYPE}")
    # add_library(${PROJECT_NAME} SHARED
    #                     ${ALL_SOURCE}
    #                     ${XL_APP_RC}
    #                     )
    add_executable(${PROJECT_NAME} ${ALL_SOURCE})
    set_target_properties(${PROJECT_NAME} PROPERTIES
        COMPILE_OPTIONS "/MT$<$<CONFIG:Debug>:d>"
    )
    set_target_properties(${PROJECT_NAME} PROPERTIES WIN32_EXECUTABLE TRUE)
    set_target_properties(${PROJECT_NAME} PROPERTIES LINK_FLAGS "/MANIFEST /MANIFESTFILE:${CMAKE_CURRENT_SOURCE_DIR}/res/thunder.manifest")

    target_link_libraries(${PROJECT_NAME} PRIVATE
                                win_common
                                libuv
                                common
                                  dk
                                  thunder_helper
                                  pc_addon
                                  dk_addon
                                  playercontrol
                                  ws2_32.lib
                                  Kernel32.lib
                                  Psapi.lib
                                  IPHLPAPI.lib
                                  Shlwapi.lib
                                  Userenv.lib
                                  )
                                  target_compile_options(${PROJECT_NAME} PRIVATE 
        /wd4828
        /wd4091
    )
else()
    message(FATAL_ERROR, "not support platform: ${CMAKE_SYSTEM_NAME}")
endif()
