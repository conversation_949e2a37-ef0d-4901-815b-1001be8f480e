// =================================================================
// @description: 统计接口
// @date:        2018.05.16
// =================================================================

import * as os from 'os';
import { ThunderHelper } from '@root/common/thunder-helper';

// 拼接成字符串
function stringifyExtData(data: string | { [key: string]: string } = ''): string {
  // let mem: any = process.memoryUsage();
  let retArr: string[] = [];
  retArr.push('cpu_model=' + os.cpus()[0].model);
  retArr.push(`mi_ts=${(new Date()).getTime()}`);
  // for (const key in mem) {
  //   if (isDef(mem[key])) {
  //     retArr.push('mi_' + key + '=' + encodeURIComponent(mem[key]));
  //   }
  // }

  // let sysMem: any = process.getSystemMemoryInfo();
  // for (const key in sysMem) {
  //   if (isDef(sysMem[key])) {
  //     retArr.push('mi_' + key + '=' + encodeURIComponent(sysMem[key]));
  //   }
  // }

  if (typeof data === 'string') {
    if (data.length > 0) {
      retArr.push(data);
    }
  } else if (isDef(data) && typeof data === 'object') {
    for (const key in data) {
      if (isDef(data[key])) {
        retArr.push(key + '=' + encodeURIComponent(data[key]));
      }
    }
  }

  return retArr.join(',');
}

function isDef(v: any): boolean {
  return v !== undefined && v !== null;
}

export namespace XLStatNS {
  export interface IInitParam {
    appKey: string;
    appName: string;
    appVer: string;
    peerId: string;
    installChannel: string;
    configFile: string;
    storageFile: string;
    useServerXml: number;
  }

  // 上报用，就以最终上报的字段命名了
  export interface IXLStatCommonTasksParams {
    total_main_task_num: number; // 总主任务数(所有任务卡片数量)
    bt_main_task_num: number; // BT主任务数
    global_speed: number; // 瞬时总下载速度B/s
    // download_contentlist: { // 正在下载的任务信息
    //   name?: string; //任务名称
    //   url: string; // 下载的url，BT任务用infohash，需要encode编码
    //   type: string; // 任务类型
    //   total_filesize: number; // 任务大小
    //   download_filesize: number; // 已下载大小
    //   duration: number; // 下载用时间
    //   sample_speed: number; // 瞬时速度， B/s
    //   gcid?: string; // BT没有
    // }[];
    download_contentlist: string;
  }

  export interface IXLStatCommonLoginInfo {
    is_login: boolean;
    is_vip: boolean;
    vip_type: string;
    user_id: string;
  }

  export interface IXLStatCommon {
    getTaskInfo(key: string, attr1: string, extData: string | { [key: string]: any }): Promise<IXLStatCommonTasksParams>;
    getLoginInfo(): Promise<IXLStatCommonLoginInfo>;
  }

  // interface IXLStat {
  //   // 用于渲染进程，异步没有返回值
  //   trackEvent(
  //     key: string,
  //     attr1: string,
  //     attr2: string,
  //     cost1: number,
  //     cost2: number,
  //     cost3: number,
  //     cost4: number,
  //     extData: string,
  //     cookie: number
  //   ): number;
  //   trackClick(key: string, cookie: number): number;
  //   trackShow(key: string, cookie: number): number;
  //   setUserID(uid: number, cookie: number): number;

  //   // 用于渲染进程远程调用，需要返回值
  //   initParamRemote(param: IInitParam, callback: (cookie: number) => void): number;
  //   uninitRemote(cookie: number, callback: (cookie: number) => void): void;

  //   // 用于后台进程异步调用，需要返回值
  //   asyncTrackEvent(
  //     key: string,
  //     attr1: string,
  //     attr2: string,
  //     cost1: number,
  //     cost2: number,
  //     cost3: number,
  //     cost4: number,
  //     extData: string,
  //     cookie: number,
  //     callback: (result: number) => void
  //   ): number;
  //   asyncInitParam(param: IInitParam, callback: (result: boolean, cookie: number) => void): void;
  //   asyncUninit(cookie: number, callback: (cookie: number) => void): void;

  //   // 只需在后台进程调用
  //   waitFinish(): void;

  //   // 设置哪些统计字段需要上报任务基础信息
  //   setStatCommonFilter(filters: string[]): void;
  //   // 设置任务基础信息得获取接口
  //   setStatCommonTasksGetter(statCommonGetter: IXLStatCommon): void;
  // }

  let gStatFilters: string[] = [];
  let gStatCommonGetter: IXLStatCommon | undefined = undefined;
  export function setStatCommonFilter(filters: string[]): void {
    gStatFilters = filters;
  }
  export function setStatCommonTasksGetter(statCommonGetter: IXLStatCommon): void {
    gStatCommonGetter = statCommonGetter;
  }

  // 通用统计上报，key必须传
  export async function asyncTrackEvent(
    key: string,
    attr1: string = '',
    attr2: string = '',
    cost1: number = 0,
    cost2: number = 0,
    cost3: number = 0,
    cost4: number = 0,
    extData: string | { [key: string]: any } = '',
    cookie: number = 0
  ): Promise<number> {
    let ret: number = 0;
    do {
      if (key === undefined) {
        ret = 1;
        break;
      }
      let extDataStr: string = stringifyExtData(extData);
      let infos: string[] = [];
      infos.push(extDataStr);
      if (gStatCommonGetter) {
        let loginInfos: IXLStatCommonLoginInfo = await gStatCommonGetter.getLoginInfo();
        if (loginInfos) {
          infos.push('is_login=' + (loginInfos.is_login ? '1' : '0'));
          infos.push('is_vip=' + (loginInfos.is_vip ? '1' : '0'));
          infos.push('vip_type=' + loginInfos.vip_type);
          infos.push('userid=' + loginInfos.user_id);
        }
      }
      // 某些统计下需要把任务的一些相关信息上报上去
      if (attr1 && attr1.length > 0 && gStatFilters.includes(attr1) && gStatCommonGetter) {
        let params: IXLStatCommonTasksParams = await gStatCommonGetter.getTaskInfo(key, attr1, extData);
        if (params) {
          if (params.bt_main_task_num && (typeof params.bt_main_task_num === 'number')) {
            infos.push('bt_main_task_num=' + params.bt_main_task_num);
          }
          if (params.global_speed && (typeof params.global_speed === 'number')) {
            infos.push('global_speed=' + params.global_speed);
          }
          if (params.total_main_task_num && (typeof params.total_main_task_num === 'number')) {
            infos.push('total_main_task_num=' + params.total_main_task_num);
          }
          if (params.download_contentlist) {
            infos.push('download_contentlist=' + params.download_contentlist);
          } else {
            infos.push('download_contentlist=');
          }
        }
      }
      extDataStr = infos.join(',');
      if (process.type === 'browser') {
        // 后台进程需要异步
        ret = await new Promise<number>((resolve: (value: number | PromiseLike<number>) => void): void => {
          ThunderHelper.xlstat?.asyncTrackEvent?.(
            key,
            attr1,
            attr2,
            cost1,
            cost2,
            cost3,
            cost4,
            extDataStr,
            cookie,
            (result: number) => {
              resolve(result);
            }
          );
        });
      } else {
        ret = ThunderHelper.xlstat?.trackEvent?.(key, attr1, attr2, cost1, cost2, cost3, cost4, extDataStr, cookie) ?? 0;
      }
    } while (false);
    return ret;
  }

  // 通用统计上报，key必须传
  export function trackEvent(
    key: string,
    attr1: string = '',
    attr2: string = '',
    cost1: number = 0,
    cost2: number = 0,
    cost3: number = 0,
    cost4: number = 0,
    extData: string | { [key: string]: any } = '',
    cookie: number = 0
  ): void {
    asyncTrackEvent(key, attr1, attr2, cost1, cost2, cost3, cost4, extData, cookie).catch();
  }

  // 通用统计上报，key必须传
  export function trackEventEx(
    key: string,
    attr1: string = '',
    extData: string | { [key: string]: any } = '',
    cookie: number = 0
  ): void {
    asyncTrackEvent(key, attr1, '', 0, 0, 0, 0, extData, cookie).catch();
  }

  // 统计点击次数，key必须传
  // 只能非后台进程调用
  export function trackClick(key: string, cookie: number = 0): void {
    do {
      if (key === undefined) {
        break;
      }
      if (process.type !== 'browser') {
        ThunderHelper.xlstat?.trackClick?.(key, cookie);
      }
    } while (false);
  }

  // 统计展现次数
  // 只能非后台进程调用
  export function trackShow(key: string, cookie: number = 0): void {
    trackClick(key, cookie);
  }

  // 设置userID公共字段
  // 只能非后台进程调用
  export function setUserID(userID: number = 0, cookie: number = 0): void {
    if (process.type !== 'browser') {
      ThunderHelper.xlstat?.setUserID?.(userID, cookie);
    }
  }

  export async function initParam(param: IInitParam): Promise<number> {
    let statCookie: number = -1;
    if (process.type === 'browser') {
      statCookie = await new Promise<number>((resolve: (value: number | PromiseLike<number>) => void): void => {
        ThunderHelper.xlstat?.asyncInitParam?.<IInitParam>(param, (result: boolean, cookie: number) => {
          resolve(result ? cookie : -1);
        });
      });
    } else {
      statCookie = await new Promise<number>((resolve: (value: number | PromiseLike<number>) => void): void => {
        ThunderHelper.xlstat?.initParamRemote?.<IInitParam>(param, (cookie: number) => {
          resolve(cookie);
        });
      });
    }
    return statCookie;
  }

  export async function asyncUninit(cookie: number): Promise<void> {
    if (process.type === 'browser') {
      await new Promise<void>((resolve: (value?: void | PromiseLike<void>) => void): void => {
        ThunderHelper.xlstat?.asyncUninit?.(cookie, () => {
          resolve();
        });
      });
    }
    /*else {
      await new Promise<void>((resolve: (value?: void | PromiseLike<void>) => void): void => {
          ThunderHelper.xlstat?.uninitRemote?.(cookie, () => {
            resolve();
          });
        resolve();
      });
    }*/
  }

  // 只需在后台进程调用
  export function uninit(): void {
    if (process.type === 'browser') {
      ThunderHelper.xlstat?.waitFinish?.();
    }
  }
}
