.td-tree--hover .td-tree-node__content:hover,
.td-tree-node__content.is-chosen {
  background: var(--td-primary-background-hover);
}

.td-tree {
  // overflow-y: auto;
}

.td-tree--hover .td-tree-node__content.is-leaf .td-icon-arrow-drop {
  opacity: 0;
  pointer-events: none;
}

.td-tree-node {
  display: table;
  width: 100%;
}

.td-tree-node__content {
  position: relative;
  height: 32px;
  border-radius: 4px;
  line-height: 32px;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;

  &:hover {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));

    // hover时文字颜色变化
    .td-tree-node__label {
      // color: var(--primary-primary-default, #226DF5);
    }

    // hover时展开图标颜色变化
    .td-tree-node__expand-icon {
      // color: var(--primary-primary-default, #226DF5);
    }

    // hover时自定义图标区域可以有轻微的变化
    .td-tree-node__image-icon {
      opacity: 0.9;
    }
  }

  &.is-chosen {
    background: var(--td-primary-background-hover, #eff7ff);

    .td-tree-node__label {
      color: var(--primary-primary-default, #226df5);
      font-weight: 500;
    }
  }
}

.td-tree-node__label {
  display: inline-block;
  margin: 0 7px;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  // max-width: 240px;
  white-space: nowrap;
  transition: color 0.2s ease;
  flex-grow: 1;
}

.td-tree-node > .td-tree-node__children {
  position: relative;
  height: 0;
  overflow: hidden;
}

.td-tree-node__image-icon {
  display: inline-block;
  position: relative;
  width: 28px;
  height: 100%;
  transition: opacity 0.2s ease;

  :deep(i) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.td-tree-node__expand-icon {
  display: inline-block;
  margin: 0 0 0 6px;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  color: var(--font-font-3, #86909c);
  vertical-align: -3px;
  transition:
    -webkit-transform 0.2s,
    color 0.2s ease;
  transition:
    transform 0.2s,
    color 0.2s ease;
  transition:
    transform 0.2s,
    -webkit-transform 0.2s,
    color 0.2s ease;
}

.td-tree-node__expand-icon.is-hidden {
  visibility: hidden;
}

.td-tree-node__expand-icon.is-expanded {
  -webkit-transform: rotate(0);
  transform: rotate(0);
  color: var(--font-font-3, #86909c);
}

.td-tree-node__prefix {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin: 0 0 0 6px;
}

.td-tree-node__mark-hint-icon {
  display: block;
  color: var(--td-warning-default);
}

.td-tree-node__children {
  transition: 0.2s;
}

.td-tree-node.is-expanded > .td-tree-node__children {
  height: auto;
  overflow: visible;
}

// .td-tree-node {
//   .td-checkbox {
//     margin-left: 4px;
//     vertical-align: -2px;
//   }

// }

.td-tree-node__checkbox {
  display: inline-block;
  position: relative;
  width: 16px;
  height: 100%;

  :deep(.td-checkbox) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.td-tree-node__expand_icon {
  display: inline-block;
  position: relative;
  width: 12px;
  height: 100%;
  margin-right: 10px;
  flex-shrink: 0;

  :deep(.td-tree-node__expand-icon.is-expanded) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(0deg);
  }

  :deep(.td-tree-node__expand-icon) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-90deg);
  }

  /* 为旋转状态添加特定样式 */
  // &.is-collapsed :deep(i) {
  //   transform: translate(-50%, -50%) rotate(-90deg);
  // }

  // &.is-expanded :deep(i) {
  //   transform: translate(-50%, -50%) rotate(0deg);
  // }
}

.is-hidden {
  visibility: hidden;
}

.td-tree-node .td-checkbox__label {
  display: flex;
  align-items: center;
}

.td-tree-node .td-icon-loading {
  margin-left: 6px;
}

// 选中状态样式 - 使用直接子选择器避免影响子节点
.td-tree-node.is-selected {
  // 只对当前节点的直接内容容器应用样式，不影响子节点
  > .td-tree-node__content {
    background: var(--fill-fill-2, rgba(12, 24, 49, 0.02));

    .td-tree-node__label {
      // color: #ff4d4f; // 红色文字
      font-weight: 600;
    }
  }

  // hover状态下保持样式 - 也使用直接子选择器
  > .td-tree-node__content:hover {
    .td-tree-node__label {
      // color: #ff4d4f; // 红色文字
      font-weight: 600;
    }
  }
}
