import { electronApp, is, optimizer } from '@electron-toolkit/utils';
import { Channels, MainToRenderer_Window_Channel, RendererToMain_Window_Channel } from '@root/common/constant';
import { port } from '@root/common/env';
import { AplayerStack } from '@root/common/player/client/aplayer-stack';
import requireNodeFile from '@root/common/require-node-file';
import { GetLogsPath, GetPlayerControlAddonNodeName, GetProfilesPath, GetIcoPath, GetXxxNodePath } from '@root/common/xxx-node-path';
import { reuserWindowManager } from '@root/main/reuse-browser-window';
import '@xunlei/async-remote';
import { mainProcessContext } from '@xunlei/node-net-ipc/dist/ipc-base';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { app, BrowserWindow, ipcMain, shell, nativeImage, NativeImage, ipc<PERSON>enderer } from 'electron';
import path, { join } from 'node:path';
import { getPackageJson } from 'utils';
import { attachShadowWindow, detachShadowWindow } from '@root/main/shadow-window';
import { ThunderHelper } from '@root/common/thunder-helper';
import { Dialog_Channels } from '@root/common/constant';
import { AccountHelper } from '@root/common/account/client/accountHelper'
import { PopUpNS } from '@root/common/pop-up';
import { AccountHelperEventKey } from '@root/common/account/account-type'
import { CmdShow } from '@root/common/window-define';

app.disableHardwareAcceleration();

const { crashReporter } = require('electron')
crashReporter.start({ uploadToServer: false })

const wndMinWidth: number = 960
const wndMinHeight: number = 640
const wndWidth: number = 1080
const wndHeight: number = 675
let g_appWillQuit: boolean = false;
function CreatePlayer() {
  AplayerStack.GetInstance().initApiProxy();
  let playerParentWnd = new BrowserWindow({
    width: wndWidth,
    height: wndHeight,
    minWidth: wndMinWidth,
    minHeight: wndMinHeight,
    show: false,
    frame: false,
    title: '迅雷播放组件',
    icon: GetIcoPath(),
    autoHideMenuBar: true,
    webPreferences: {
      nodeIntegration: true,
      devTools: true,
      webviewTag: true,
      contextIsolation: false,
      nodeIntegrationInSubFrames: true,
      backgroundThrottling: false,
    },
  });
  let parentWndBuf: Buffer = playerParentWnd.getNativeWindowHandle()
  let parentHandle = parentWndBuf.readBigUInt64LE(0)

  let playerControlWnd = new BrowserWindow({
    width: wndWidth ,
    height: wndHeight,
    minWidth: wndMinWidth,
    minHeight: wndMinHeight,
    show: false,
    frame: false,
    closable: true,
    parent: playerParentWnd ? playerParentWnd : undefined,
    transparent: true,
    title: 'playercontrol',
    autoHideMenuBar: true,
    webPreferences: {
      nodeIntegration: true,
      devTools: true,
      webviewTag: true,
      contextIsolation: false,
      nodeIntegrationInSubFrames: true,
      backgroundThrottling: false,
    },
  });
  let controlWndBuf: Buffer = playerControlWnd.getNativeWindowHandle()
  let controlHandle = controlWndBuf.readBigUInt64LE(0)

  playerParentWnd.on('close', (event: Event) => {
    if (!g_appWillQuit) {
      AplayerStack.GetInstance().closePlayWindow();
      event.preventDefault();
    }
  });
  playerControlWnd.on('close', (event: Event) => {
    if (!g_appWillQuit) {
      AplayerStack.GetInstance().closePlayWindow();
      event.preventDefault();
    }
  });

  playerControlWnd.on('enter-full-screen', () => {
    playerControlWnd.webContents.send(MainToRenderer_Window_Channel.onFullscreen, true)
  })
  playerControlWnd.on('leave-full-screen', () => {
    playerControlWnd.webContents.send(MainToRenderer_Window_Channel.onFullscreen, false)
  })
  playerControlWnd.on('maximize', () => {
    playerControlWnd.webContents.send(MainToRenderer_Window_Channel.onMaximize, true)
  })
  playerControlWnd.on('unmaximize', () => {
    playerControlWnd.webContents.send(MainToRenderer_Window_Channel.onMaximize, false)
  })
  playerControlWnd.on('minimize', () => {
    playerControlWnd.webContents.send(MainToRenderer_Window_Channel.onMinimize)
  })
  playerControlWnd.on('restore', () => {
    playerControlWnd.webContents.send(MainToRenderer_Window_Channel.onRestore)
  })

  let thunderHelper = requireNodeFile(
    path.join(GetXxxNodePath(), 'thunder_helper.node')
  )
  ipcMain.on('AplayerWndBind', (event: any, playerWnd: number) => {
    thunderHelper.bindWnd(parentHandle, controlHandle, playerWnd);
  });
  ipcMain.on('AplayerWndShow', () => {
    ThunderHelper.showWindow(playerParentWnd);
    ThunderHelper.showWindow(playerControlWnd);
    // playerParentWnd.show();
    // playerControlWnd.show();
    thunderHelper.bringWndToTop(parentHandle);

    if (playerParentWnd && playerControlWnd) {
      if (playerParentWnd.isMinimized() || playerControlWnd.isMinimized()) {
        playerParentWnd.restore();
        playerControlWnd.restore();
      }
      playerParentWnd.show();
      playerControlWnd.show()
      if (!playerControlWnd.isFocused()) {
        playerControlWnd.focus()
      }
      if (parentHandle) {
        try {
          thunderHelper.setForegroundWindow(parentHandle);
        } catch (e) {
          console.error(e)
        }
      }
    }
  })
  ipcMain.on('AplayerWndHide', () => {
    // playerParentWnd.hide();
    // playerControlWnd.hide();
    ThunderHelper.hideWindow(playerParentWnd);
    ThunderHelper.hideWindow(playerControlWnd);
  })
  ipcMain.on(RendererToMain_Window_Channel.hideOrShowPlayerControlWnd, (event: any, show: boolean) => {
    if (show) {
      ThunderHelper.showWindow(playerControlWnd);
    } else {
      ThunderHelper.hideWindow(playerControlWnd)
    }
  })

  ipcMain.on(RendererToMain_Window_Channel.minizeWindow, (event) => {
    const { sender } = event
    const browserWindow = BrowserWindow.fromWebContents(sender)
    if (browserWindow) {
      const parentWindow = browserWindow.getParentWindow()
      if (parentWindow) {
        if (parentWindow.minimizable) {
          parentWindow.minimize()
        }
      }
    }
  })
  let wndBounds: any = undefined;
  ipcMain.on(RendererToMain_Window_Channel.maxWindow, (event) => {
    const { sender } = event
    const browserWindow = BrowserWindow.fromWebContents(sender)
    if (browserWindow) {
      if (browserWindow.isMaximized()) {
        playerParentWnd.setMovable(true);
        playerControlWnd.setMovable(true);
        browserWindow.unmaximize()
        if (wndBounds) {
          playerControlWnd.setBounds(wndBounds);
        }
      } else {
        wndBounds = playerControlWnd.getBounds();
        browserWindow.maximize()
        playerParentWnd.setMovable(false);
        playerControlWnd.setMovable(false);
      }     
    }
  })

  ipcMain.on(RendererToMain_Window_Channel.fullscreen, (event) => {
    const { sender } = event
    const browserWindow = BrowserWindow.fromWebContents(sender)
    if (browserWindow) {
      browserWindow.setFullScreen(true)
    }
    if (parentHandle) {
      try {
        thunderHelper.bringWndToTop(parentHandle);
        thunderHelper.setForegroundWindow(parentHandle);
      } catch (e) {
        console.error(e)
      }
    }
  })
  ipcMain.on(RendererToMain_Window_Channel.fullscreenOff, (event) => {
    const { sender } = event
    const browserWindow = BrowserWindow.fromWebContents(sender)
    if (browserWindow) {
      browserWindow.setFullScreen(false)
    }
  })
  ipcMain.on(RendererToMain_Window_Channel.windowPin, (event, isPin: boolean) => {
    const { sender } = event
    const browserWindow = BrowserWindow.fromWebContents(sender)
    if (browserWindow) {
      const parentWindow = browserWindow.getParentWindow()
      if (parentWindow) {
        parentWindow.setAlwaysOnTop(isPin)
      }
      browserWindow.setAlwaysOnTop(isPin)
    }
  })
  ipcMain.on(RendererToMain_Window_Channel.showCursor, (event, show: boolean) => {
    thunderHelper.showCursor(show);
  });
  // if (process.platform === "darwin") {
  //   ipcMain.on(RendererToMain_Window_Channel.setTrafficLightPosition, (event, position: Parameters<TBrowserWindowIns['setTrafficLightPosition']>[0]) => {
  //     const { sender } = event
  //     const browserWindow = BrowserWindow.fromWebContents(sender)
  //     if (browserWindow) {
  //       browserWindow.setTrafficLightPosition(position)
  //     }
  //   })
  //   ipcMain.on(RendererToMain_Window_Channel.setWindowButtonVisibility, (event, isVisible: Parameters<TBrowserWindowIns['setWindowButtonVisibility']>[0]) => {
  //     const { sender } = event
  //     const browserWindow = BrowserWindow.fromWebContents(sender)
  //     if (browserWindow) {
  //       browserWindow.setWindowButtonVisibility(isVisible)
  //     }
  //   })
  // }
  let url: string = `file://${path.resolve(__dirname, '../plugins/player-plugin.asar/', 'renderer.html')}?ph=${parentHandle}&ch=${controlHandle}`;
  playerControlWnd.loadURL(url);


  // let playercontrol = requireNodeFile(
  //   path.join(GetXxxNodePath(), GetPlayerControlAddonNodeName()),
  // )
  // let xmpHelper = requireNodeFile(
  //   path.join(GetXxxNodePath(), 'thunder_helper.node')
  // )

  // let playerInitParam: any = {
  //   xmp: false,
  //   openPlayerLog: false,
  //   logDir: GetLogsPath(),
  //   codecPath: '',
  //   subtitleCachePath: path.join(GetProfilesPath(), 'subtitle'),
  //   peerid: '1111111111',
  //   version: '',
  //   versionCode: 10,
  //   configPath: path.join(GetProfilesPath(), 'player_config.json'),
  //   dbDir: GetProfilesPath(),
  //   dbName: 'player_record.db',
  //   panPlayCache: path.join(GetProfilesPath(), 'yun_play'),
  //   downloadServerDir: GetSdkPath()
  // }
  // playercontrol.initAddon(playerInitParam)
  // setTimeout(() => {
  //   playercontrol.setWnd(parentHandle)
  // }, 20000)
}


let mainWindow: BrowserWindow

// 退出应用前的清理动作
let uninited: boolean = false;
function appUninit(): void {
  if (!uninited) {
    uninited = true;
    reuserWindowManager.uninit();
    ThunderHelper.uninitGDIPlus();
  }
}

function createWindow(): void {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 1080,
    height: 720,
    minWidth: 1000,
    minHeight: 680,
    show: false,
    frame: false,
    autoHideMenuBar: true,
    webPreferences: {
      preload: join(__dirname, './preload/index.js'),
      nodeIntegration: true,
      devTools: true,
      webviewTag: true,
      contextIsolation: false,
      nodeIntegrationInSubFrames: true,
      backgroundThrottling: false,
    },
  })

  // mainWindow.title = 'Electron-Rsbuild app'
  mainWindow.on('ready-to-show', async () => {
    ThunderHelper.endThunderStartProcess();
    // 某些机器上 new BrowserWindow指定ico参数可能导致窗口创建失败
    const iconPath = GetIcoPath();
    try {
      const image: NativeImage = nativeImage.createFromPath(iconPath);
      mainWindow.setIcon(image);
    } catch (e) {
      console.warn('createFromPath failed!', e);
    }

    await AccountHelper.getInstance().isSdkInitReady();
    const isSignIn = await AccountHelper.getInstance().isSignIn();
    if (!isSignIn) {
      PopUpNS.showLoginDlg();
    } else {
      mainWindow.show();
      ThunderHelper.setForegroundWindow(mainWindow);
    }

    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, () => {
      mainWindow.show();
      ThunderHelper.setForegroundWindow(mainWindow);
    })


    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_OUT, () => {
      console.log('SIGN_OUT')
      PopUpNS.showLoginDlg();
      mainWindow.hide();
    })


    reuserWindowManager.init(mainWindow);

    // 阴影窗口
    if (process.platform !== 'darwin') {
      setTimeout(async () => {
        if (mainWindow && !mainWindow.isDestroyed()) {
          attachShadowWindow(mainWindow, null, false, false);
        }
      }, 500);
    }
  })

  mainWindow.on('close', () => {
    if (process.platform !== 'darwin') {
      detachShadowWindow(mainWindow.id);
    }
  });

  mainWindow.webContents.setWindowOpenHandler((details) => {
    console.log('setWindowOpenHandler', details.url)
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  const userAgent: string = mainWindow.webContents.session.getUserAgent();
  const uaArr: string[] = userAgent.split(' ');
  for (let index: number = 0; index < uaArr.length; index++) {
    const sub = uaArr[index];
    if (sub.toLowerCase().indexOf('thunder/') === 0) {
      uaArr.unshift(uaArr.splice(index, 1)[0]);
      break;
    }
  }
  // HMR for renderer base on electron-rsbuild cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev) {
    mainWindow.loadURL(`http://localhost:${port}`, { userAgent: uaArr.join(' ') })
  } else {
    mainWindow.loadURL(
      `file://${path.resolve(__dirname, './main-renderer/', 'index.html')}`, { userAgent: uaArr.join(' ') }
    )
  }

  mainWindow.on('session-end', () => {
    appUninit();
    mainWindow.webContents.send('OnQueryEndSession');
  });

  mainWindow.on('resize', async () => {
    mainWindow.webContents.send(MainToRenderer_Window_Channel.onMaximize, mainWindow.isMaximized())
  })

  // initialWindow(mainWindow)
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  ipcMain.on(Channels.ShellExecute, async (event, operation: string, filePath: string, params: string, workDir: string, showCmd: CmdShow, answerName: string) => {
    let hWnd = 0;
    if (mainWindow) {
      let hwndBuf = mainWindow.getNativeWindowHandle();
      hWnd = hwndBuf.readUIntLE(0, 4);
    }
    let nRet = await ThunderHelper.asyncShellExecute(hWnd, operation, filePath, params, workDir, showCmd);
    event.sender.send(answerName, nRet);
  })
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron.thunder')
  ThunderHelper.initGDIPlus();

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  ipcMain.on('ipc-server-ready', () => {
    client.start({ name: mainProcessContext })
    CreatePlayer()
  });

  // IPC test
  ipcMain.on('ping', (event) => {
    event.reply('pong', { message: 'copy that.', time: new Date().getTime() })
    console.log('pong')
  })

  createWindow()

  ipcMain.handle(Channels.GetPackageJson, getPackageJson)

  // Dialog相关处理程序 - 开始
  ipcMain.handle(Dialog_Channels.openDirectory, async (event, options) => {
    const { dialog } = require('electron');
    const result = await dialog.showOpenDialog(mainWindow, {
      title: options?.title || '选择文件夹',
      defaultPath: options?.defaultPath,
      buttonLabel: options?.buttonLabel,
      properties: ['openDirectory', ...(options?.properties || [])],
      ...options
    });
    return result;
  });

  ipcMain.handle(Dialog_Channels.openFile, async (event, options) => {
    const { dialog } = require('electron');
    const result = await dialog.showOpenDialog(mainWindow, {
      title: options?.title || '选择文件',
      defaultPath: options?.defaultPath,
      buttonLabel: options?.buttonLabel,
      filters: options?.filters,
      properties: options?.properties || ['openFile'],
      ...options
    });
    return result;
  });

  ipcMain.handle(Dialog_Channels.saveFile, async (event, options) => {
    const { dialog } = require('electron');
    const result = await dialog.showSaveDialog(mainWindow, {
      title: options?.title || '保存文件',
      defaultPath: options?.defaultPath,
      buttonLabel: options?.buttonLabel,
      filters: options?.filters,
      ...options
    });
    return result;
  });
  // Dialog相关处理程序 - 结束

  // events
  ipcMain.on(Channels.Close, (event) => {
    g_appWillQuit = true;
    // // TODO 先强杀
    // ThunderHelper.terminateProcess();
    const { sender } = event

    const browserWindow = BrowserWindow.fromWebContents(sender)
    if (browserWindow?.id === mainWindow.id) {
      ThunderHelper.hideWindow(mainWindow);
    }
  })

  ipcMain.on(Channels.Maximize, (event) => {
    const { sender } = event
    const browserWindow = BrowserWindow.fromWebContents(sender)
    if (browserWindow?.maximizable) {
      mainWindow.webContents.send(MainToRenderer_Window_Channel.onMaximize, browserWindow.isMaximized())
      if (browserWindow.isMaximized()) {
        browserWindow.unmaximize()
      } else {
        browserWindow.maximize()
      }
    }
  })

  ipcMain.on(Channels.Minimize, (event) => {
    const { sender } = event
    const browserWindow = BrowserWindow.fromWebContents(sender)
    browserWindow?.minimizable && browserWindow.minimize()
  })

  app.on('activate', () => {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

app.on('before-quit', () => {
  appUninit();
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.
