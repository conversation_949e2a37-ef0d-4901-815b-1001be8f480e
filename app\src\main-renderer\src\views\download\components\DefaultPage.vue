<script lang="ts" setup>
import { computed } from 'vue';

const props = defineProps<{
  isDownload: Boolean,
}>();

const emits = defineEmits<{
  (e: 'createTask'): void,
}>();

const pic = computed(() => {
  return '';
});

const handleChangeAllTaskStatus = () => {
  // window.electron.ipcRenderer.send('create-task');
  console.log('>>>>>>>>>>> 新建任务')
  emits('createTask')
};
</script>

<template>
  <div class="default-page">
    <div class="default-page-content">
      <div class="default-page-logo"></div>
      <p class="default-page-title">
        {{ isDownload ? '暂无下载任务' : '暂无内容' }}
      </p>
      <!-- <p v-if="isDownload" class="default-page-subtitle">
        暂无下载任务，点击新建可创建下载任务
      </p> -->
      <div class="default-page-btn" v-if="isDownload">
        <Button variant="primary" size="sm" @click="handleChangeAllTaskStatus">
          <i class="xl-icon-general-plus-m"></i>
          新建任务
        </Button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.default-page {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;

  &-logo {
    background: url(@/assets/img/download-default.svg) no-repeat center center;
    background-size: contain;
    width: 140px;
    height: 140px;
    margin: auto;
  }

  &-content {
    text-align: center;
    margin-top: -56px;
  }

  &-title {
    color: var(--font-font-1);
    font-size: 13px;
    line-height: 22px;
    text-align: center;
    margin-top: 10px;
  }

  &-subtitle {
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    color: var(--font-font-3);
    margin-top: 8px;
  }

  &-btn {
    margin-top: 18px;
    text-align: center;
  }
}
</style>