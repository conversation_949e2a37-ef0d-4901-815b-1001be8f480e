<script lang="ts" setup>
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import Button from '@root/common/components/ui/button/index.vue'
import TDCheckbox from '@root/common/components/ui/checkbox/index.vue'

import { computed, onMounted, reactive, ref, watch } from 'vue';
import { PopUpNS } from '@root/common/pop-up';
import * as PopUpTypes from '@root/common/pop-up/types';
import { config } from '@root/common/config/config';
import { API_FILE } from '@root/common/thunder-pan-manager/pan-sdk/types';
import { formatSize } from '@root/common/thunder-pan-manager/pan-sdk/utils/drive';
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client';
import { AsyncRemoteCall } from '@root/common/renderer-async-remote-call';

export interface IFetchBackPropsOptions {
  files: API_FILE.DriveFile[]
}

const props = defineProps<{options: IFetchBackPropsOptions}>()

const isCreatingTask = ref(false)
const dialogVisible = ref(true)
const isPreventPop = ref(false)
const current = reactive<{
  path: string
  options: string[]
}>({
  path: '',
  options: []
})
const filesSizeText = ref('')
const forbidDownload = ref(false)

const firstFile = computed(() => props.options.files[0])
const filesNameText = computed(() => props.options.files.map(f => f.name).join('，'))
// const isSomeZeroSize = computed(() => props.options.files.some(file => Number(file.size ?? 0) === 0))
// const totalSize = computed(() => props.options.files.map(f => Number(f.size)).reduce((pre, current) => { return pre + current }, 0))
// const filesSizeText = computed(() => formatSize(totalSize.value))
const buttonText = computed(() => {
  if (isCreatingTask.value) return '正在创建任务'

  const pathText = current.path
  if (pathText.length >= 9) {
    return pathText.slice(0, 3) + '...' + pathText.slice(-2)
  }
  return `下载到（${pathText}）`
})

async function handleClose () {
  const currentWindow = PopUpNS.getCurrentWindow();
  await currentWindow.close(PopUpTypes.Action.Close);
}

async function handleConfirm () {
  if (isCreatingTask.value) return
  isCreatingTask.value = true

  // 保留最近2条记录
  current.options = Array.from(new Set([current.path, ...current.options])).slice(0, 2)
  config.setValue('ThunderPanPlugin', 'downloadPaths', current.options)

  const currentWindow = PopUpNS.getCurrentWindow();
  // const res = await ThunderPanClientSDK.getInstance().batchCreateDownloadTaskWithFiles(props.options.files)
  const res = await ThunderPanClientSDK.getInstance().startCreateDownloadTaskByPreprocess(current.path)

  if (res.success && res.data && res.data.taskIds.length) {
    config.setValue('ThunderPanPlugin', 'defaultDownloadPath', current.path)
    await currentWindow.close(PopUpTypes.Action.OK, res.data);
  } else if (res.data?.repeatTaskAction === 'ignore') {
    // 重复任务忽略不处理
  } else {
    const isAllOverlimit = !!res.data?.overlimitList.length && res.data?.overlimitList.length === res.data?.taskIds.length

    let message = '创建下载任务失败'
    if (isAllOverlimit) {
      message = '当前文件夹文件数量超过限制，创建下载任务失败'
    } else if (!res.data?.needToDownloadFileCount) {
      message = `无法下载文件`
    }

    window.__VueGlobalProperties__.$message({
      message: message,
      type: 'error'
    })
  }
  isCreatingTask.value = false
}

async function handleCancel () {
  const currentWindow = PopUpNS.getCurrentWindow();
  await currentWindow.close(PopUpTypes.Action.Cancel);
}

function handleDialogUpdateOpen (isOpen: boolean) {
  dialogVisible.value = isOpen
  if (!isOpen) {
    handleClose()
  }
}

async function handleChoosePath () {
  const dialog = await AsyncRemoteCall.GetInstance().getDialog()
  const wnd = await AsyncRemoteCall.GetInstance().getCurrentWindow()
  const path = (await dialog.showOpenDialog(wnd, {
    tite: '下载文件到',
    properties: ['openDirectory']
  }))?.filePaths?.[0] as string

  if (path) {
    current.path = path.trim()
    // 保留最近2条记录
    current.options = Array.from(new Set([path, ...current.options])).slice(0, 2)
    config.setValue('ThunderPanPlugin', 'downloadPaths', current.options)
  }
}

async function handleStartPreprocess () {
  filesSizeText.value = '文件大小计算中...'

  const res = await ThunderPanClientSDK.getInstance().startDownloadPreprocess(props.options.files)
  if (res.success && res.data) {
    if (res.data.isAllOverlimit) {
      forbidDownload.value = true
      isCreatingTask.value = false
      filesSizeText.value = '文件数超过下载限制，无法下载'
    } else if (res.data.isAllError) {
      forbidDownload.value = true
      isCreatingTask.value = false
      filesSizeText.value = '获取文件信息失败，无法下载'
    } else {
      filesSizeText.value = res.data.totalFileSize > 0 ? formatSize(res.data.totalFileSize!) : '未知大小'
    }
  } else {
    filesSizeText.value = '未知大小'
  }
}

watch(isPreventPop, () => {
  config.setValue('ThunderPanPlugin', 'useDefault', isPreventPop.value)
})

onMounted(async () => {
  (window as any).__config__ = config;
  handleStartPreprocess()
  current.path = await config.getValue('ThunderPanPlugin', 'defaultDownloadPath', 'C:\\迅雷云盘') as string

  // 可选下拉列表数据
  let paths = await config.getValue('ThunderPanPlugin', 'downloadPaths', []) as string[]
  paths = [ current.path, ...paths ]
  current.options = paths.slice(0, 2)
})
</script>

<template>
  <Dialog
    title="下载云盘文件"
    :open="dialogVisible"
    :showCancel="false"
    :preventDefaultClose="true"
    :show-title-icon="false"
    @update:open="handleDialogUpdateOpen"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    @close="handleClose"
  >
    <div class="fetch-back-container">
      <div class="file-icon">
        <img :src="firstFile.icon_link" alt="" >
      </div>

      <div class="file-info">
        <div class="file-name">
          <div class="prefix">{{ filesNameText }}</div>
          <div v-if="options.files.length > 1" class="suffix">等 {{ options.files.length }} 个文件</div>
        </div>

        <div
          class="file-size"
          :class="{
            'is-warning': forbidDownload
          }"
        >
          {{ filesSizeText }}
        </div>
      </div>
    </div>

    <template #actions>
      <div class="fetch-back-actions none-draggable">
        <TDCheckbox v-model="isPreventPop" label="">下次直接下载，不展示此弹窗</TDCheckbox>

        <div class="right-actions">
          <Button
            size="lg"
            variant="default"
            :disabled="isCreatingTask || forbidDownload"
            :hasRightIcon="!isCreatingTask"
            @click="handleConfirm"
            @right-icon-click="handleChoosePath"
          >
            {{ buttonText }}
          </Button>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<style>
.dialog-content {
  width: 100% !important;
  height: 100% !important;
  -webkit-app-region: drag;
}
</style>

<style lang="scss" scoped>
.fetch-back-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  margin-top: 20px;

  .file-icon {

    img {
      width: 64px;
      height: 64px;
    }
  }

  .file-info {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;

    .file-name {
      display: flex;
      padding: 4px 8px;
      color: var(--font-font-1);

      .prefix {
        flex-grow: 1;
        -webkit-line-clamp: 1;
        display: -webkit-box;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        white-space: pre-wrap;
      }

      .suffix {
        flex-shrink: 0;
      }
    }

    .file-size {
      font-size: 12px;
      padding: 4px 8px;
      color: var(--font-font-3);

      &.is-warning {
        color: var(--functional-error-default);
      }
    }
  }
}

.fetch-back-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    :deep(.button-right-icon) {
      height: 100%;
    }
  }
}
</style>
