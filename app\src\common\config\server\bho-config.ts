import * as path from 'path';
import { Logger } from '@root/common/logger';
import { IniNS } from '@root/common/config/ini';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { ThunderHelper } from '@root/common/thunder-helper';
import { configServer, ConfigServer } from '@root/common/config/server/config';
import configRemoteGlobal from '@root/common/config/server/config-remote-global';
import { ConfigValueType, IConfigSectionMap, IConfigKeyMap, ConfigInitState } from '@root/common/config/types';

const logger = new Logger({ tag: 'BHOConfig' })

let userAddtion: { [ext: string]: boolean } = {};
let userDeleted: { [ext: string]: boolean } = {};
// 手动删除过某个后缀如mp3，那么之后不管全局配置如何更改，针对用户删除的这一项，不再给用户增加mp3
// 手动增加过某个后缀如mp3，那么之后不管全局配置如何更改，针对用户增加的这一项，不再给用户删除mp3
// 同时除去用户手动改动的数据，其余的全局配置变动点，不管是删除还是增加一个后缀，都需要同步给用户
interface IExtensionConfigOption {
  [ type: string ]: // 'video' | 'document' | 'image' | 'audio' | 'archive' | 'software' | 'other'
  {
    display_name: string; // '视频' | '文档' | '图片' | '音频' | '压缩包' | '软件' | '其他';
    exts: string; // like: 3g2;3gp;asf;asx;avi;divx;dv;flv;f4v;m2ts;m4v;mkv;mov;mp4;mpe;mpeg;mpg;qt;rm;rmvb;ts;vob;webm;wmv;xv;swf;tp;dat;m3u8;xvx;
    onekey_check: boolean; // 一键下载是否默认勾选
    monitor_check: boolean; // 接管类型是否默认勾选
  }
}

export class BHOConfig extends ConfigServer {
  private async initExtensionNameUserChange(): Promise<void> {
    const added: string = await configServer.getValue('Monitor', 'ExtendNamesAdded', '') as string;
    const removed: string = await configServer.getValue('Monitor', 'ExtendNamesRemoved', '') as string;

    added.split(';').forEach((i) => {
      userAddtion[i] = true;
    });

    removed.split(';').forEach((i) => {
      userDeleted[i] = true;
    });
  }

  protected mergeConfigData(configData: IConfigSectionMap): void {
    if (configData) {
      if (this.configData_ === null) {
        this.configData_ = configData;
      } else {
        for (const section in configData) {
          const keyMap: IConfigKeyMap = configData[section];
          if (!keyMap) {
            break;
          }

          for (const key in keyMap) {
            const value: ConfigValueType = keyMap[key];
            const sectionKey: IConfigKeyMap = this.configData_[section];
            if (sectionKey) {
              sectionKey[key] = value;
            } else {
              const sectionKey: IConfigKeyMap = {};
              sectionKey[key] = value;
              this.configData_[section] = sectionKey;
            }
          }
        }
      }
    }
  }

  public async init(): Promise<void> {
    do {
      if (this.initState_ === ConfigInitState.Done) {
        break;
      }

      if (this.initState_ === ConfigInitState.Ing) {
        return new Promise<void>(
          (resolve: () => void): void => {
            this.waitInitCallback_.push(() => {
              resolve();
            });
          }
        );
      }

      this.initState_ = ConfigInitState.Ing;

      do {
        const localLowPath: string = ThunderHelper.getAppDataLocalLowPath();
        if (!localLowPath) {
          break;
        }
        this.configPath_ = path.join(localLowPath, 'Thunder Network\\Thunder\\BHO\\config.ini');
        const exist: boolean = await FileSystemAWNS.existsAW(this.configPath_);
        let configData: IConfigSectionMap | null = null;
        if (exist) {
          const buffer: Buffer = await FileSystemAWNS.readFileAW(this.configPath_);
          if (!buffer) {
            break;
          }
          try {
            configData = IniNS.parse(buffer.toString());
          } catch (error) {
            //
          }
        } else {
          const dir: string = path.dirname(this.configPath_);
          await FileSystemAWNS.mkdirsAW(dir);
        }

        // 确保BHO初始化完成时 全程配置也初始化完成
        await configRemoteGlobal.init();
        await this.initExtensionNameUserChange();
        if (configData) {
          this.mergeConfigData(configData);
        }
      } while (0);

      // 初始化完成，并处理等待回调
      this.initState_ = ConfigInitState.Done;
      for (let func of this.waitInitCallback_) {
        func?.();
      }
      this.waitInitCallback_ = [];      
    } while (0);
  }

  public async save(): Promise<void> {
    if (this.configPath_ !== null && this.configPath_ !== undefined && this.configPath_ !== '' && this.isInit) {
      const iniString: string = IniNS.stringify(this.configData_);
      await FileSystemAWNS.writeFileAW(this.configPath_, iniString);
    }
  }
}

const bhoConfig: BHOConfig = new BHOConfig();
export namespace BHOConfigNS {
  export let videoDefaultExtendNames: string = '.3g2;.3gp;.asf;.asx;.avi;.divx;.dv;.flv;.f4v;.m2ts;.m4v;.mkv;.mov;.mp4;.mpe;.mpeg;.mpg;.qt;.rm;.rmvb;.ts;.vob;.webm;.wmv;.xv;.swf;.tp;.dat;.m3u8;.xvx;';
  export let docDefaultExtendNames: string = '.csv;.html;.htm;.ini;.json;.tsv;.xml;.yaml;.yml;.md;.markdown;.cnf;.conf;.cfg;.log;.txt;.epub;.mobi;.chm;.prc;.doc;.docx;.ppt;.pptx;.xls;.xlsx;.pdf;.xmind;.bat;.bin;.odt;';
  export let imageDefaultExtendNames: string = '.ai;.bmp;.cdr;.cr2;.dwg;.dxf;.eps;.exif;.fpx;.gif;.hdri;.heif;.ico;.jfif;.jif;.jpe;.jpeg;.jpeg2000;.jpg;.jxr;.pcd;.pcx;.png;.psd;.raw;.svg;.tga;.tif;.tiff;.ufo;.webp;.wmf;.webp;.apng;';
  export let audioDefaultExtendNames: string = '.aac;.aiff;.amr;.cda;.flac;.m4a;.mid;.mp3;.ogg;.vqf;.wav;.wma;.ra;.ape;.mpga;';
  export let archiveDefaultExtendNames: string = '.rar;.7z;.ar;.bz2;.cab;.crx;.dcm;.elf;.eot;.gz;.iso;.lz;.nes;.ps;.rar;.rtf;.sqlite;.tar;.xz;.z;.zip;.arj;.sit;.lzh;.gz;.tgz;.rar5;';
  export let softwareDefaultExtendNames: string = '.apk;.rpm;.deb;.ipa;.pxl;.pkg;.dmg;.msi;.exe;.appx;.msix;.iso;.bin;.jar;.sisx;';
  export let otherDefaultExtendNames: string = '.dll;.hqx;.msu;.pth;.pt;.ckpt‌;.bin‌;.json‌;.onnx‌;.gguf‌;.dds;.otf;.safetensors;.srt;.web;.vtt;.stl;.sbv;.ass;.dfxp;.ttml;.ssa;.sub;.idx;.vtt;.mpl2;.smi;.otf;.ttf;.woff;.woff2;';

  export async function getRemoteExtensionCfg(): Promise<IExtensionConfigOption>{
    // 接管，默认都勾选；但是静默，默认图片和文档
    let cfg: IExtensionConfigOption = await configRemoteGlobal.getConfigValue('main', 'suffixDispatch');
    do {
      if (!cfg) {
        cfg = {};
      }

      if (!cfg?.['video']) {
        cfg['video'] = {
          display_name: '视频',
          exts: videoDefaultExtendNames,
          onekey_check: false,
          monitor_check: true,
        }
      }
      cfg['video'].onekey_check = cfg['video'].onekey_check ?? false;
      cfg['video'].monitor_check = cfg['video'].monitor_check ?? true;

      if (!cfg?.['document']) {
        cfg['document'] = {
          display_name: '文档',
          exts: docDefaultExtendNames,
          onekey_check: true,
          monitor_check: true,
        }
      }
      cfg['document'].onekey_check = cfg['document'].onekey_check ?? true;
      cfg['document'].monitor_check = cfg['document'].monitor_check ?? true;

      if (!cfg?.['image']) {
        cfg['image'] = {
          display_name: '图片',
          exts: imageDefaultExtendNames,
          onekey_check: true,
          monitor_check: true,
        }
      }
      cfg['image'].onekey_check = cfg['image'].onekey_check ?? true;
      cfg['image'].monitor_check = cfg['image'].monitor_check ?? true;

      if (!cfg?.['audio']) {
        cfg['audio'] = {
          display_name: '音频',
          exts: audioDefaultExtendNames,
          onekey_check: false,
          monitor_check: true,
        }
      }
      cfg['audio'].onekey_check = cfg['audio'].onekey_check ?? false;
      cfg['audio'].monitor_check = cfg['audio'].monitor_check ?? true;

      if (!cfg?.['archive']) {
        cfg['archive'] = {
          display_name: '压缩包',
          exts: archiveDefaultExtendNames,
          onekey_check: false,
          monitor_check: true,
        }
      }
      cfg['archive'].onekey_check = cfg['archive'].onekey_check ?? false;
      cfg['archive'].monitor_check = cfg['archive'].monitor_check ?? true;

      if (!cfg?.['software']) {
        cfg['software'] = {
          display_name: '软件',
          exts: softwareDefaultExtendNames,
          onekey_check: false,
          monitor_check: true,
        }
      }
      cfg['software'].onekey_check = cfg['software'].onekey_check ?? false;
      cfg['software'].monitor_check = cfg['software'].monitor_check ?? true;

      if (!cfg?.['other']) {
        cfg['other'] = {
          display_name: '其他',
          exts: otherDefaultExtendNames,
          onekey_check: false,
          monitor_check: true,
        }
      }
      cfg['other'].onekey_check = cfg['other'].onekey_check ?? false;
      cfg['other'].monitor_check = cfg['other'].monitor_check ?? true;

    } while (0);
    return cfg;
  }

  let allDefaultExtendNames: string = '' ;
  // 该函数初始化了各种类型的值
  export async function getDefaultExtendNames(): Promise<string> {
    let ret: string = allDefaultExtendNames;
    do {
      if (allDefaultExtendNames) {
        break;
      }

      const remoteCfg = await getRemoteExtensionCfg();
      for (let k in remoteCfg) {
        if (remoteCfg[k].monitor_check) {
          ret += remoteCfg[k].exts;
        }
      }
      allDefaultExtendNames = ret;
    } while (0);

    logger.log('bhoconfig init default', ret);
    return ret;
  }

  // http://wiki.xunlei.cn/pages/viewpage.action?pageId=161221731
  // 手动删除过某个后缀如mp3，那么之后不管全局配置如何更改，针对用户删除的这一项，不再给用户增加mp3
  // 手动增加过某个后缀如mp3，那么之后不管全局配置如何更改，针对用户增加的这一项，不再给用户删除mp3
  // 同时除去用户手动改动的数据，其余的全局配置变动点，不管是删除还是增加一个后缀，都需要同步给用户
  export function updateMonitorRemoteAddition(currentCfg: string, status: { bVideoCheck: boolean; bDocCheck: boolean; bPicCheck: boolean; bMusicCheck: boolean; bArchiveCheck: boolean; bSoftwareCheck: boolean; bExtraCheck: boolean; }): void {
    // 确保已初始化
    do {
      if (currentCfg === allDefaultExtendNames) {
        logger.log('extendname updateMonitorExtAddition break alreay all');
        break;
      }

      const allRemoteExts: string[] = allDefaultExtendNames?.trim()?.split(';') ?? [];
      const allCurLocalExts: string[] = currentCfg?.trim()?.split(';') ?? [];

      const newLocalMap: { [ext: string]: boolean } = {};

      const remoteMap: { [ext: string]: boolean } = {};
      const curLocalMap: { [ext: string]: boolean } = {};
      for (let i of allRemoteExts) {
        if (!i) {
          continue;
        }
        remoteMap[i] = true;
      }
      for (let i of allCurLocalExts) {
        if (!i) {
          continue;
        }
        curLocalMap[i] = true;
      }

      const remoteCfg = getRemoteExtensionCfg();

      if (status.bVideoCheck) {
        if (remoteCfg['video']?.monitor_check) {
          const kindMap: { [ext: string]: boolean } = {};
          (remoteCfg['video'].exts?.split(';') ?? []).forEach((i) => {
            i && (kindMap[i] = true);
          });

          for (let k in kindMap) {
            // 远程配置的只要用户未主动删除过，则添加
            if (!userDeleted[k]) {
              // 如果该后缀本地配置不存在，且用户未主动删除，则添加；
              newLocalMap[k] = true;
            }
          }
        }
      }

      if (status.bDocCheck) {
        if (remoteCfg['document']?.monitor_check) {
          const kindMap: { [ext: string]: boolean } = {};
          (remoteCfg['document'].exts?.split(';') ?? []).forEach((i) => {
            i && (kindMap[i] = true);
          });

          for (let k in kindMap) {
            // 远程配置的只要用户未主动删除过，则添加
            if (!userDeleted[k]) {
              // 如果该后缀本地配置不存在，且用户未主动删除，则添加；
              newLocalMap[k] = true;
            }
          }
        }
      }

      if (status.bPicCheck) {
        if (remoteCfg['image']?.monitor_check) {
          const kindMap: { [ext: string]: boolean } = {};
          (remoteCfg['image'].exts?.split(';') ?? []).forEach((i) => {
            i && (kindMap[i] = true);
          });

          for (let k in kindMap) {
            // 远程配置的只要用户未主动删除过，则添加
            if (!userDeleted[k]) {
              // 如果该后缀本地配置不存在，且用户未主动删除，则添加；
              newLocalMap[k] = true;
            }
          }
        }
      }

      if (status.bMusicCheck) {
        if (remoteCfg['audio']?.monitor_check) {
          const kindMap: { [ext: string]: boolean } = {};
          (remoteCfg['audio'].exts?.split(';') ?? []).forEach((i) => {
            i && (kindMap[i] = true);
          });

          for (let k in kindMap) {
            // 远程配置的只要用户未主动删除过，则添加
            if (!userDeleted[k]) {
              // 如果该后缀本地配置不存在，且用户未主动删除，则添加；
              newLocalMap[k] = true;
            }
          }
        }
      }

      if (status.bArchiveCheck) {
        if (remoteCfg['archive']?.monitor_check) {
          const kindMap: { [ext: string]: boolean } = {};
          (remoteCfg['archive'].exts?.split(';') ?? []).forEach((i) => {
            i && (kindMap[i] = true);
          });

          for (let k in kindMap) {
            // 远程配置的只要用户未主动删除过，则添加
            if (!userDeleted[k]) {
              // 如果该后缀本地配置不存在，且用户未主动删除，则添加；
              newLocalMap[k] = true;
            }
          }
        }
      }

      if (status.bSoftwareCheck) {
        if (remoteCfg['software']?.monitor_check) {
          const kindMap: { [ext: string]: boolean } = {};
          (remoteCfg['software'].exts?.split(';') ?? []).forEach((i) => {
            i && (kindMap[i] = true);
          });

          for (let k in kindMap) {
            // 远程配置的只要用户未主动删除过，则添加
            if (!userDeleted[k]) {
              // 如果该后缀本地配置不存在，且用户未主动删除，则添加；
              newLocalMap[k] = true;
            }
          }
        }
      }

      if (status.bExtraCheck) {
        if (remoteCfg['other']?.monitor_check) {
          const kindMap: { [ext: string]: boolean } = {};
          (remoteCfg['other'].exts?.split(';') ?? []).forEach((i) => {
            i && (kindMap[i] = true);
          });

          for (let k in kindMap) {
            // 远程配置的只要用户未主动删除过，则添加
            if (!userDeleted[k]) {
              // 如果该后缀本地配置不存在，且用户未主动删除，则添加；
              newLocalMap[k] = true;
            }
          }
        }
      }

      const newExts = Object.keys({ ...(newLocalMap ?? {}), ...(userAddtion ?? {}) }).join(';');
      configServer.setValue('Monitor', 'ExtendNames', newExts).catch();
      BHOConfigNS.setValue('Monitor', 'ExtendNames', newExts);
      BHOConfigNS.save().catch();

      logger.log('extendname updateMonitorRemoteAddition leave', newLocalMap, userAddtion);
    } while (0);
  }

  export function updateExtendNamesUserChange(preExtendNames: string, newExtendNames: string) {
    const allOldExts: string[] = preExtendNames?.trim()?.split(';') ?? [];
    const allExts: string[] = newExtendNames?.trim()?.split(';') ?? [];
    const oldMap: { [ext: string]: boolean } = {};
    const newMap: { [ext: string]: boolean } = {};
    for (let o of allOldExts) {
      if (!o) {
        continue;
      }
      oldMap[o] = true;
    }
    for (let n of allExts) {
      if (!n) {
        continue;
      }
      newMap[n] = true;
    }

    for (let k in newMap) {
      if (!oldMap[k]) {
        // 新增后缀
        userAddtion[k] = true;
        delete userDeleted[k];
      }
    }

    for (let k in oldMap) {
      if (!newMap[k]) {
        // 删除后缀
        userDeleted[k] = true;
        delete userAddtion[k];
      }
    }

    configServer.setValue('Monitor', 'ExtendNamesAdded', Object.keys(userAddtion ?? {}).join(';'), false);
    configServer.setValue('Monitor', 'ExtendNamesRemoved', Object.keys(userDeleted ?? {}).join(';'), false);
  }

  function updateUserAddtionExt(partExtendNames: string) {
    const allExts: string[] = partExtendNames.split(';');
    for (let k of allExts) {
      userAddtion[k] = true;
      delete userDeleted[k];
    }
    configServer.setValue('Monitor', 'ExtendNamesAdded', Object.keys(userAddtion ?? {}).join(';'), false);
    configServer.setValue('Monitor', 'ExtendNamesRemoved', Object.keys(userDeleted ?? {}).join(';'), false);
  }

  function updateUserRemoveExt(partExtendNames: string) {
    const allExts: string[] = partExtendNames.split(';');
    for (let k of allExts) {
      userDeleted[k] = true;
      delete userAddtion[k];
    }
    configServer.setValue('Monitor', 'ExtendNamesAdded', Object.keys(userAddtion ?? {}).join(';'), false);
    configServer.setValue('Monitor', 'ExtendNamesRemoved', Object.keys(userDeleted ?? {}).join(';'), false);
  }

  export function updateFilterCheck(bhoExtendNames: string): { bVideoCheck: boolean; bDocCheck: boolean; bPicCheck: boolean; bMusicCheck: boolean; bArchiveCheck: boolean; bSoftwareCheck: boolean; bExtraCheck: boolean; } {
    // 针对类型筛选，决定配置的勾选项
    let bVideoCheck = false;
    let bDocCheck = false;
    let bPicCheck = false;
    let bMusicCheck = false;
    let bArchiveCheck = false;
    let bSoftwareCheck = false;
    let bExtraCheck = false;
    if (bhoExtendNames) {
      const allExts: string[] = bhoExtendNames.split(';');
      for (const item of allExts) {
        if (!item) {
          continue;
        }
        const ext = item + ';';
        
        if (videoDefaultExtendNames.indexOf(ext) !== -1) {
          bVideoCheck = true;
        } else if (docDefaultExtendNames.indexOf(ext) !== -1) {
          bDocCheck = true;
        } else if (imageDefaultExtendNames.indexOf(ext) !== -1) {
          bPicCheck = true;
        } else if (audioDefaultExtendNames.indexOf(ext) !== -1) {
          bMusicCheck = true;
        } else if (archiveDefaultExtendNames.indexOf(ext) !== -1) {
          bArchiveCheck = true;
        } else if (softwareDefaultExtendNames.indexOf(ext) !== -1) {
          bSoftwareCheck = true;
        } else if (otherDefaultExtendNames.indexOf(ext) !== -1) {
          bExtraCheck = true;
        }

        if (bVideoCheck && bDocCheck && bMusicCheck && bSoftwareCheck && bExtraCheck && bArchiveCheck && bPicCheck) {
          break;
        }
      }
    }

    // 这几个字段只有手动点击设置才在config-handler里处理，最后一个参数传递false,防止发通知，重置ExtendNames的内容
    configServer.setValue('Monitor', 'VideoExtendNames', bVideoCheck, false);
    configServer.setValue('Monitor', 'WordExtendNames', bDocCheck, false);
    configServer.setValue('Monitor', 'ImageExtendNames', bPicCheck, false);
    configServer.setValue('Monitor', 'MusicExtendNames', bMusicCheck, false);
    configServer.setValue('Monitor', 'ZipExtendNames', bArchiveCheck, false);
    configServer.setValue('Monitor', 'ExeExtendNames', bSoftwareCheck, false);
    configServer.setValue('Monitor', 'ExtraExtendNames', bExtraCheck, false);
    logger.log('update filter check', bVideoCheck, bDocCheck, bPicCheck, bMusicCheck, bArchiveCheck, bSoftwareCheck, bExtraCheck);
    return { bVideoCheck, bDocCheck, bPicCheck, bMusicCheck, bArchiveCheck, bSoftwareCheck, bExtraCheck };
  }

  export async function updateExtendNames(check: boolean, partExtendNames: string): Promise<void> {
    logger.log('updateExtendNames enter', check, partExtendNames);
    const defaultExt = await getDefaultExtendNames();
    const bhoExtendNames: string = await bhoConfig.getValue(
      'Monitor',
      'ExtendNames',
      defaultExt
    ) as string;
    if (check) {
      // 某个类型勾选，直接在末尾加上这个类型
      let newExtendNames: string = '';
      if (!bhoExtendNames || bhoExtendNames.endsWith(';')) {
        newExtendNames = bhoExtendNames + partExtendNames;
      } else {
        newExtendNames = bhoExtendNames + ';' + partExtendNames;
      }
      configServer.setValue('Monitor', 'ExtendNames', newExtendNames, false);
      bhoConfig.setValue('Monitor', 'ExtendNames', newExtendNames);
      bhoConfig.save().catch();

      // 记录用户主动勾选的类型
      updateUserAddtionExt(partExtendNames);
    } else {
      // 反选某个类型，则去掉所有该类型的后缀 (只要有部分后缀选择，则会显示选中，所以反选可能仅取消部分后缀)
      let newExtendNames: string = '';
      let removedExtendNames: string = '';
      const allExts: string[] = bhoExtendNames.split(';');
      for (const item of allExts) {
        if (!item) {
          continue;
        }
        const ext = item + ';';
        if (partExtendNames.indexOf(ext) === -1) {
          newExtendNames = newExtendNames + ext;
        } else {
          removedExtendNames += ext;
        }
      }
      configServer.setValue('Monitor', 'ExtendNames', newExtendNames, false);
      bhoConfig.setValue('Monitor', 'ExtendNames', newExtendNames);
      bhoConfig.save().catch();

      // 记录用户主动取消的类型
      updateUserRemoveExt(removedExtendNames);
    }
  }

  export function getDefaultFilterSitesForUI(): string {
    return `*.mail.live.com
*.mail.yahoo.com
*.mail.qq.com
*.mail.sohu.com
*.mail.google.com
*.sinamail.sina.com.cn
*.mail.sina.com.cn
*.mail.163.com
*.mail.10086.cn
*.webmail30.189.cn
*.www.126.com
*.passport.alipay.com
*.login.live.com
*.mail.sogou.com
*.web.mail.tom.com
*.www.2980.com
*.mail.21cn.com
*.www.188.com
*.yeah.net
*.mail.wo.com.cn
*.263.net
*.ecitic.com
*.pos.baidu.com`;
  }

  export async function save(): Promise<void> {
    await bhoConfig.save();
  }

  export async function getValue(section: string, key: string, defValue: ConfigValueType): Promise<ConfigValueType> {
    return bhoConfig.getValue(section, key, defValue);
  }

  export async function setValue(section: string, key: string, value: ConfigValueType): Promise<void> {
    await bhoConfig.setValue(section, key, value);
  }
}