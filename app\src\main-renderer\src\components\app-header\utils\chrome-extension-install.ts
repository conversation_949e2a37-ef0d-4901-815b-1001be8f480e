// chrome自动安装插件的逻辑管理
import { exec } from 'child_process'
import { promises as fs } from 'fs'
import path from 'path'

const xunleiKey = 'ncennffkjdiamlpmcbajkmaiiiddgioo'
const exeName = 'chrome.exe'

export async function killRunningChrome(): Promise<boolean> {
  return new Promise((resolve) => {
    exec('TASKKILL /F /IM chrome.exe', (error) => {
      if (!error || error.code === 0) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}

export async function findProcess(name: string): Promise<any[]> {
  // 这里需要根据thunder_2025的架构适配进程查找逻辑
  // 临时返回空数组，需要实际实现
  return []
}

export async function verifyKillChrome(
  silent: boolean,
  browserPath: string,
  confirmCallback?: () => Promise<boolean>,
  retryTimes: number = 3
): Promise<boolean> {
  let verify = false
  
  do {
    let findResult = await findProcess(exeName)
    if (!findResult?.length) {
      verify = true
      break
    }

    if (silent) {
      break
    }

    if (!confirmCallback) {
      // 使用默认确认对话框
      const confirmed = confirm('Chrome浏览器正在运行，请关闭浏览器后重试')
      if (!confirmed) {
        break
      }
    } else {
      if (!(await confirmCallback())) {
        break
      }
    }

    for (let i = 0; i < retryTimes; i++) {
      await killRunningChrome()
      await new Promise(resolve => setTimeout(resolve, 1000))
      findResult = await findProcess(exeName)
      if (!findResult?.length) {
        verify = true
        break
      }
    }

    if (verify) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  } while (0)
  
  return verify
}

// 删除Preferences文件中的插件卸载记录
async function resetPreferencesSetting(subName: string): Promise<void> {
  try {
    const localAppDataDir = process.env.LOCALAPPDATA || ''
    const preferences = path.join(localAppDataDir, subName, 'User Data', 'Default', 'Preferences')
    
    if (!(await fs.access(preferences).then(() => true).catch(() => false))) {
      return
    }
    
    const data = await fs.readFile(preferences, 'utf-8')
    const jsondata = JSON.parse(data)

    const uninstalls = jsondata?.extensions?.external_uninstalls
    if (!Array.isArray(uninstalls)) {
      return
    }

    const index = uninstalls.findIndex(key => key.toLowerCase() === xunleiKey)
    if (index !== -1) {
      jsondata.extensions.external_uninstalls.splice(index, 1)
      await fs.writeFile(preferences, JSON.stringify(jsondata))
    }
  } catch (error) {
    console.error('resetPreferencesSetting error:', error)
  }
}

export async function resetSecurePreferencesSetting(
  addonInstalled: boolean,
  subName: string
): Promise<void> {
  try {
    if (!addonInstalled) {
      await resetPreferencesSetting(subName)
    }

    const localAppDataDir = process.env.LOCALAPPDATA || ''
    const securePreferences = path.join(localAppDataDir, subName, 'User Data', 'Default', 'Secure Preferences')
    
    if (!(await fs.access(securePreferences).then(() => true).catch(() => false))) {
      return
    }
    
    const data = await fs.readFile(securePreferences, 'utf-8')
    const jsondata = JSON.parse(data)

    if (!jsondata?.extensions?.settings) {
      return
    }
    
    const value = jsondata.extensions.settings[xunleiKey]
    if (!value || value.state === 1) {
      return
    }

    jsondata.extensions.settings[xunleiKey].state = 1
    delete jsondata.extensions.settings[xunleiKey].disable_reasons
    await fs.writeFile(securePreferences, JSON.stringify(jsondata))
  } catch (error) {
    console.error('resetSecurePreferencesSetting error:', error)
  }
} 