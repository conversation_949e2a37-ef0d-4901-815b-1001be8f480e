import { Account<PERSON><PERSON><PERSON> } from "@root/common/account/client/accountHelper";
import { GlobalEventHelper } from "../utils/global-event-helper";
import { BaseManager } from "./base-manager";
import { Phase } from "@root/common/thunder-pan-manager/pan-sdk/utils/drive";
import { AccountHelperEventKey } from '@root/common/account/account-type'
import { safeJsonParse, sleep } from "@root/common/thunder-pan-manager/pan-sdk/utils/basic";
import { ThunderPanClientSDK } from "@root/common/thunder-pan-manager/client";
import { MessageType } from "@root/@types/global";

export type TMessageCenterPublishType =
  | 'offline'
  | 'decompress'
  | 'trash'
  | 'untrash'
  | 'emptytrash'
  | 'deletefile'
  | 'copy'
  | 'move'
  | 'upload'

export interface IPayloadData {
    callback: string;
    created_time: string;
    file_id: string;
    file_name: string;
    file_size: string;
    icon_link: string;
    id: string;
    kind: string;
    message: string;
    name: string;
    params: Record<string, any>
    phase: string;
    progress: number;
    reference_resource: any;
    space: string;
    status_size: number;
    statuses: any[];
    third_task_id: string;
    type: string;
    updated_time: string;
    user_id: string;
}

export interface IMQTTMessage {
  data: IPayloadData;
  datacontenttype: string;
  id: string;
  source: string;
  specversion: string;
  subject: string;
  time: string;
  type: string;
}

export class MessageCenterManager {
  private static _instance: MessageCenterManager;

  static getInstance () {
    if (MessageCenterManager._instance) {
      return MessageCenterManager._instance;
    } else {
      MessageCenterManager._instance = new MessageCenterManager();
      return MessageCenterManager._instance;
    }
  }

  private readonly accountHelperInstance = AccountHelper.getInstance();
  private readonly topicGroup = ['pan', 'drive/tasks'];

  constructor() {}

  init () {
    // 在 MQTT 通道连接成功后，监听相应的 topic
    this.accountHelperInstance.attachEvent(AccountHelperEventKey.SYNC_CLIENT_EVENT_CONNECTED, () => {
      this.topicGroup.forEach(topic => {
        this.subscribe(topic);
      });
    });
    // 监听 MQTT 推送
    this.accountHelperInstance.attachEvent(AccountHelperEventKey.SYNC_CLIENT_EVENT_MESSAGE_ARRIVED, (originMessage: any) => {
      console.log('[message-center]', originMessage)
      if (originMessage) {
        const payload: IMQTTMessage = originMessage._payload as IMQTTMessage;
        // 仅处理本业务需要监听的 topic
        if (this.topicGroup.includes(originMessage.subTopic)) {
          this.typeHandler(payload.type, payload);
        }
      }
    });
    // 主动获取一次连接状态
    this.accountHelperInstance.syncClientIsConnected()
      .then(isConnected => {
        if (isConnected) {
          this.topicGroup.forEach(topic => {
            this.subscribe(topic);
          });
        }
      })
      .catch();
  }

  reset () {
    this.topicGroup.forEach(topic => {
      this.unsubscribe(topic);
    });
  }

  /**
   * 订阅 MQTT topic
   * @param topic 由于 xbase 内部会处理 /user/me 的拼接，因此如： /user/me/drive/tasks 只需要传 drive/tasks
   */
  private subscribe (topic: string) {
    this.accountHelperInstance.syncClientSubscribe({
      subTopic: topic
    });
  }

  /**
   * 取消订阅 MQTT topic
   * @param topic
   */
  private unsubscribe (topic: string) {
    this.accountHelperInstance.syncClientUnsubscribe({
      subTopic: topic
    });
  }

  /**
   * 推送具体类型的处理函数
   * @param type
   * @param data
   */
  private async typeHandler (type: TMessageCenterPublishType | string, payload: IMQTTMessage) {
    const data: IPayloadData = payload.data;

    switch (type) {
      // 云添加
      case 'offline': {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.CLOUD_ADD_LIST_REFRESH);
        // reference_resource 兜底策略，推送过来的数据如果没有 reference_resource，则通过 getFileInfo 获取对应的文件信息
        let reference_resource = data.reference_resource
        if (data.file_id && !data.reference_resource) {
          const res = await ThunderPanClientSDK.getInstance().getFileInfo(data.file_id)

          if (res.success && res.data) {
            reference_resource = res.data
          }
        }
        data.reference_resource = reference_resource

        let message = '', messageType: MessageType = 'success'
        if (data.phase === Phase.ERROR) {
          message = `${data.name}-云添加失败`
          messageType = 'error'
          GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.CLOUD_ADD_TASK_FAIL, data);
        } else {
          message = `${data.name}-云添加成功`
          messageType = 'success'
          GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.CLOUD_ADD_TASK_SUCCESS, data);
        }

        // window.__VueGlobalProperties__.$message({
        //   message: message,
        //   type: messageType
        // })
        break;
      }
      // 移动或复制文件时，对应的请求在服务端是异步的，需要通过 MQTT 来判断是否移动或复制成功
      // 复制文件
      case 'copy': {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_COPY, data);
        break;
      }
      // 移动文件
      case 'move': {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_MOVE, data);
        break;
      }
      case 'deletefile':
      case 'trash': {
        if (data.params && data.params.data) {
          const trashIds = safeJsonParse(data.params.data, [])

          if (trashIds) {
            GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_TRASH, trashIds);
          }
        }
        break;
      }
      case 'untrash': {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_UNTRASH, data);
        break;
      }
      case 'decompress': {
        if (data.file_id) {
          const res = await ThunderPanClientSDK.getInstance().getFileInfo(data.file_id)

          if (res.success && res.data) {
            GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_DECOMPRESS, res.data.parent_id);
          }
        }
        break;
      }
      case 'upload': {
        if (data.file_id) {
          const res = await ThunderPanClientSDK.getInstance().getFileInfo(data.file_id)

          if (res.success && res.data) {
            GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_UPLOADED, res.data.parent_id);
          }
        }
        break;
      }
      case 'emptytrash': {
        break;
      }
    }

    // 刷新云盘空间占用情况
    if (data.phase === Phase.COMPLETE) {
      // 延迟 2s
      await sleep(2000)
      BaseManager.getInstance().getBase()
    }
  }
}
