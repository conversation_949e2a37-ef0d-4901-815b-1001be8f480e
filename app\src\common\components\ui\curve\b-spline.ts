/**
 * @description bspline 类
 * B-样条曲线, https://zhuanlan.zhihu.com/p/50626506
 * 必须重新计算, 因为根据后面的点的大小, 会导致前面的点出现变动
 * 返回的点数量, 与points数量及tessellation大小相关
 * 总体来说, 保持在70以上
 */
export class BSpline {
  points: any; // 控制点
  degree: number; // 阶数
  tessellation: number;
  curvePoints: any;
  knotVector: any;
  constructor ({ points = [], degree = 4, tessellation = 10 }: {points: any, degree: number, tessellation: number}) {
    if (points.length >= 2) {
      this.points = [
        this.getExtendPoint(points[0], points[1]),
        ...points,
        this.getExtendPoint(points[points.length - 1], points[points.length - 2])
      ];
    } else {
      this.points = points;
    }

    this.degree = degree;
    this.tessellation = tessellation;
    this.curvePoints = [];
    this.knotVector = [];

    this.uniformRender();
  }

  // 获取一个新的点, 在曲线前后调用
  getExtendPoint (a: any, b: any): any {
    return {
      x: b.x - 2 * (b.x - a.x),
      y: b.y - 2 * (b.y - a.y)
    };
  }

  uniformRender (): void {
    this.createKnots();

    if (this.points.length < 2) {
      return;
    }

    const delta: number = 1 / this.tessellation;

    for (
      let t: number = this.knotVector[this.degree];
      t < this.knotVector[this.knotVector.length - this.degree - 1];
      t += delta
    ) {
      const p: any = this.deBoor(t);
      this.curvePoints.push(p);
    }

    const p: any = this.deBoor(
      this.knotVector[this.knotVector.length - this.degree - 1]
    );

    this.curvePoints.push(p);
  }

  // 创建节点表, 大小严格等于控制点数量+阶数+1
  createKnots (): void {
    this.knotVector = [];

    if (this.points.length >= 2) {
      const countOfKnots: number = this.degree + this.points.length + 1;
      const d: number = 1 / (countOfKnots - 1);
      for (let i: number = 0; i < countOfKnots; i++) {
        this.knotVector.push(i * d);
      }
    }
  }

  deBoor (t: number): any {
    let x: number = 0;
    let y: number = 0;
    for (let i: number = 0; i < this.points.length; i++) {
      const scale: number = this.basisFunction(i, this.degree, t);

      if (scale > 0) {
        x += this.points[i].x * scale;
        y += this.points[i].y * scale;
      }
    }

    return [x, y];
  }

  // 基本函数表
  basisFunction (i: number, k: number, t: number): number {
    // i : control point index
    // k : degree
    // t : interval certain value
    if (k === 0) {
      if (this.knotVector[i] <= t && t < this.knotVector[i + 1]) {
        return 1;
      } else {
        return 0;
      }
    }

    const a: number =
      (t - this.knotVector[i]) / (this.knotVector[i + k] - this.knotVector[i]);
    const b: number =
      (this.knotVector[i + k + 1] - t) /
      (this.knotVector[i + k + 1] - this.knotVector[i + 1]);

    return (
      a * this.basisFunction(i, k - 1, t) +
      b * this.basisFunction(i + 1, k - 1, t)
    );
  }
}
