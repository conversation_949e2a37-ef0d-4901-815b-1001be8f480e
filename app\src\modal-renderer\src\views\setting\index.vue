<script setup lang="ts">
import { usePositionMixinComponent } from '@root/modal-renderer/src/common/mixins';
import * as PopUpTypes from '@root/common/pop-up/types'
import { PopUpNS } from '@root/common/pop-up';
import SettingSidebar from './components/SettingSidebar.vue'
import SettingContent from './components/setting-content/index.vue'
import { ref, provide } from 'vue'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

// 创建 PerfectScrollbar 的 ref
const scrollbarRef = ref<InstanceType<typeof PerfectScrollbar>>()
// 提供滚动容器给子组件
provide('scrollContainer', scrollbarRef)

// 添加当前选中的设置项状态
const activeSettingId = ref('basic-settings')
// 提供选中的设置项给子组件
provide('activeSettingId', activeSettingId)

// 使用基类的逻辑
const { overridePosition } = usePositionMixinComponent();

// 重写控制位置基类非响应式数据
overridePosition({
  relatePos: PopUpTypes.RelatePosType.CenterParent,
  autoSize: true,
  show: true,
  selector: '.setting-container',
})


const handleClose = async () => {
  console.log('setting dialog closed')
  const currentWindow = PopUpNS.getCurrentWindow()
  currentWindow.close()
}

</script>

<template>
  <div class="setting-container">
    <div class="setting-container-sidebar">
      <SettingSidebar />
    </div>
    <div class="setting-container-content">
      <div class="setting-container-content-header">
        <i class="xl-icon-general-close-m" @click="handleClose"></i>
      </div>
      <PerfectScrollbar ref="scrollbarRef" class="setting-container-content-body">
        <SettingContent />
      </PerfectScrollbar>
    </div>

  </div>
</template>

<style scoped lang="scss">
.setting-container {
  position: relative;
  margin: 0 auto;
  width: 768px;
  height: 512px;
  box-sizing: border-box;
  user-select: none;
  background: var(--background-background-elevated, #FFF);
  box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
  display: flex;

  &-sidebar {
    height: 100%;
    width: 128px;
  }

  &-content {
    flex: 1;
    height: 100%;
    padding-bottom: 40px;

    &-header {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 32px;
      padding: 0 8px;

      i {
        color: var(--font-font-3, #86909C);
        cursor: pointer;
      }
    }

    &-body {
      height: 100%;
      width: 100%;
      padding: 0 40px 20px;
    }
  }
}
</style>