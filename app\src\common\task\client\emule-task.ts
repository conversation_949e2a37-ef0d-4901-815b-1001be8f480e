import { CallApiProxyImplWithIpcClient } from '../call-api-impl';
import * as BaseType from '../base'
export class EmuleTask {
    private apiProxy: CallApiProxyImplWithIpcClient | null = null;
    taskId: number = 0;
    constructor(apiProxy: CallApiProxyImplWithIpcClient, id: number) {
        this.apiProxy = apiProxy;
        this.taskId = id;
    }

    public async getFileHash(): Promise<string> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskEmuleTaskGetFileHash');
        if (info.bSucc) {
            return info.result as string;
        }

        return '';
    }
}