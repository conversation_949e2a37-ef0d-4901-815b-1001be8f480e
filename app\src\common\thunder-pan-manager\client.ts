import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { IPC_API_NAME, IPC_REMOTE_ID } from './common/ipc-define'
import { API_EVENT, API_FILE, API_PASSWORD, API_SHARE, API_TASK, IRequestHeader } from './pan-sdk'
import { IDriveApisCommonOptions, IFlowAboutResponse, IGetAboutParams, IGetEventsParams, IGetUrlTasksParams, IGetUrlTaskStatusParams, TPrivilegeString } from './pan-sdk/services/drive'
import { EFileSpaceFolderType, ICleanTrashResponse, ICreateFolderParams, IFileApisGetFilesOptions, IGetAllCategoryFileCountResponse, IGetFileAncestorsRequest, IGetFileBySpaceResponse, IGetFileInfoParams, IGetFilesParams, IGetFolderFilesParams } from './pan-sdk/services/file'
import { IGetRestoreListParams, IGetShareListParams, IShareApisCommonOptions } from './pan-sdk/services/share'
import { IAddUrlToDriveTaskData, IAddUrlToDriveOptions, IGetAllFilesInDirectoryOptions, IGetSameFilesInDirectoryOptions, IPathSelectorPropsOptions, IPathSelectorResponse, IBatchAddUrlsToDriveOptions, IGetCurrentUserQuotasResponse, IOpenDirectoryOptions, IOpenDirectoryResponse } from '@root/thunder-pan-plugin/src/utils/business-helper'
import { ICheckSafeHasInitParams, ICheckVerificationCodeParams, IPasswordParams, IResetPasswordParams, ISafeApisCommonOptions, ISendVerificationCodeParams } from './pan-sdk/services/safe'
import { IBatchCreateDownloadTaskResponse, IStartPreprocessResult } from '@root/thunder-pan-plugin/src/utils/retrieval-task-helper'
import { IRecentDownloadPathData } from '@root/thunder-pan-plugin/src/utils/recent-download-path-helper'

export interface IProxyCallResponse<D> {
  success: boolean
  data?: D
  error?: any
}

export interface IThunderPanClientSDKInitConfig {
  useServer: boolean
}

class ThunderPanClientSDK {
  private useServer: boolean = false

  static _instance: ThunderPanClientSDK

  static getInstance() {
    if (ThunderPanClientSDK._instance) return ThunderPanClientSDK._instance
    ThunderPanClientSDK._instance = new ThunderPanClientSDK()
    return ThunderPanClientSDK._instance
  }

  constructor() { }

  init(config: IThunderPanClientSDKInitConfig) {
    this.useServer = config.useServer
  }

  private async _ipcProxyServerCall<T>(api: string, ...args: any): Promise<IProxyCallResponse<T>> {
    try {
      const res = await client.callRemoteClientFunction(IPC_REMOTE_ID, api, ...args)

      return {
        success: true,
        data: res[0],
      }
    } catch (err) {
      return {
        success: false,
        error: err,
      }
    }
  }

  private async _ipcProxyClientCall<T>(api: string, ...args: any): Promise<IProxyCallResponse<T>> {
    try {
      const res = await client.callRemoteClientFunction(IPC_REMOTE_ID, api, ...args)

      if (res[1]) {
        throw res[1]
      }

      return {
        success: true,
        data: res[0],
      }
    } catch (err) {
      return {
        success: false,
        error: err,
      }
    }
  }

  private async _ipcProxyCall<T>(api: string, ...args: any): Promise<IProxyCallResponse<T>> {
    if (this.useServer) {
      return this._ipcProxyServerCall(api, ...args)
    }
    return this._ipcProxyClientCall(api, ...args)
  }

  async getFileInfo(fileId: string, options: IFileApisGetFilesOptions<IGetFileInfoParams> = {}) {
    return this._ipcProxyCall<API_FILE.DriveFile>(IPC_API_NAME.GET_FILE_INFO, fileId, options)
  }

  async getFolderList(parentId: string, space: string, page_token: string = '', options: IFileApisGetFilesOptions<IGetFilesParams> = {}) {
    return this._ipcProxyCall<API_FILE.DriveListFilesResponse>(IPC_API_NAME.GET_FOLDER_LIST, parentId, space, page_token, options)
  }

  async getAllFileList(parentId: string, space: string, page_token: string = '', options: IFileApisGetFilesOptions<IGetFilesParams> = {}) {
    return this._ipcProxyCall<API_FILE.DriveListFilesResponse>(IPC_API_NAME.GET_ALL_FILE_LIST, parentId, space, page_token, options)
  }

  async getAllFlattenSubFilesByParentId(parentId: string, space: string, page_token: string = '', options: IFileApisGetFilesOptions<IGetFolderFilesParams> = {}) {
    return this._ipcProxyCall<API_FILE.DriveListFilesResponse>(IPC_API_NAME.GET_ALL_FLATTEN_SUB_FILES, parentId, space, page_token, options)
  }

  async getEvents(options: IDriveApisCommonOptions<IGetEventsParams> = {}) {
    return this._ipcProxyCall<API_EVENT.DriveListEventsResponse>(IPC_API_NAME.GET_EVENTS, options)
  }

  async reportEvent(options: IDriveApisCommonOptions<API_EVENT.DriveReportEventRequest> = {}) {
    return this._ipcProxyCall<API_EVENT.DriveReportEventResponse>(IPC_API_NAME.REPORT_EVENT, options)
  }

  async getFiles(options: IDriveApisCommonOptions<IGetFileInfoParams> = {}) {
    return this._ipcProxyCall<API_FILE.DriveListFilesResponse>(IPC_API_NAME.GET_FILES, options)
  }

  async searchFileTreePathById(id: string) {
    return this._ipcProxyCall<API_FILE.DriveFile[]>(IPC_API_NAME.SEARCH_FILE_TREE_PATH_BY_ID, id)
  }

  async serverSearchFileTreePathById(id: string) {
    return this._ipcProxyCall<API_FILE.DriveFile[]>(IPC_API_NAME.SERVER_SEARCH_FILE_TREE_PATH_BY_ID, id)
  }

  async getShareList(options: IShareApisCommonOptions<IGetShareListParams> = {}) {
    return this._ipcProxyCall<API_SHARE.DriveListShareResponse>(IPC_API_NAME.GET_SHARE_LIST, options)
  }

  async getAllTrashFiles (page_token: string = '', options: IFileApisGetFilesOptions<IGetFilesParams> = {}) {
    return this._ipcProxyCall<API_FILE.DriveListFilesResponse>(IPC_API_NAME.GET_TRASH_LIST, page_token, options)
  }

  async cleanTrash () {
    return this._ipcProxyCall<ICleanTrashResponse>(IPC_API_NAME.CLEAN_TRASH)
  }

  async getAbout(options: IDriveApisCommonOptions<IGetAboutParams> = {}) {
    return this._ipcProxyCall<API_FILE.DriveGetAboutResponse>(IPC_API_NAME.GET_ABOUT, options)
  }

  async getPrivilege(privilege: TPrivilegeString, headers?: IRequestHeader) {
    return this._ipcProxyCall<API_FILE.DriveCheckPrivilegeResponse>(IPC_API_NAME.GET_PRIVILEGE, privilege, headers)
  }

  async getFlowAbout(headers?: IRequestHeader) {
    return this._ipcProxyCall<IFlowAboutResponse>(IPC_API_NAME.GET_FLOW_ABOUT, headers)
  }

  async getUrlTaskList(options?: IDriveApisCommonOptions<IGetUrlTasksParams>) {
    return this._ipcProxyCall<API_TASK.DriveListTasksResponse>(IPC_API_NAME.GET_URL_TASK_LIST, options)
  }

  async getUrlTaskById(taskId: string, options: IDriveApisCommonOptions<null> = {}) {
    return this._ipcProxyCall<API_TASK.DriveTask>(IPC_API_NAME.GET_URL_TASK_BY_ID, taskId, options)
  }

  async getUrlTaskStatusById(taskId: string, options?: IDriveApisCommonOptions<IGetUrlTaskStatusParams>) {
    return this._ipcProxyCall<API_TASK.DriveGetTaskStatusesResponse>(IPC_API_NAME.GET_URL_TASK_CHILDREN, taskId, options)
  }

  async batchDeleteUrlTasks(taskIds: string[], options?: IDriveApisCommonOptions<null>) {
    return this._ipcProxyCall<API_TASK.DriveDeleteTasksResponse>(IPC_API_NAME.BATCH_DELETE_URL_TASKS, taskIds, options)
  }

  async batchGetUrlTaskStatuses(taskIds: string[], options?: IDriveApisCommonOptions<null>) {
    return this._ipcProxyCall<API_TASK.DriveGetTaskStatusesResponse>(IPC_API_NAME.BATCH_GET_TASKS_STATUSES, taskIds, options)
  }

  async batchTrashFiles(options: IFileApisGetFilesOptions<API_FILE.DriveBatchTrashFilesRequest>) {
    return this._ipcProxyCall<API_FILE.DriveBatchUntrashFilesResponse>(IPC_API_NAME.BATCH_TRASH_FILES, options)
  }

  async batchUnTrash(options: IFileApisGetFilesOptions<API_FILE.DriveBatchUntrashFilesRequest>) {
    return this._ipcProxyCall<API_FILE.DriveBatchUntrashFilesResponse>(IPC_API_NAME.BATCH_UNTRASH_FILES, options)
  }

  async batchDeleteFiles(options: IFileApisGetFilesOptions<API_FILE.DriveBatchDeleteFilesRequest>) {
    return this._ipcProxyCall<API_FILE.DriveBatchDeleteFilesResponse>(IPC_API_NAME.BATCH_DELETE_FILES, options)
  }

  async createFolder(parentId: string, name: string, options?: IFileApisGetFilesOptions<ICreateFolderParams>) {
    return this._ipcProxyCall<API_FILE.DriveCreateFileResponse>(IPC_API_NAME.CREATE_FOLDER, parentId, name, options)
  }

  async batchCopyFiles(options: IFileApisGetFilesOptions<API_FILE.DriveBatchCopyFilesRequest>) {
    return this._ipcProxyCall<API_FILE.DriveBatchCopyFilesResponse>(IPC_API_NAME.BATCH_COPY_FILES, options)
  }

  async batchMoveFiles(options: IFileApisGetFilesOptions<API_FILE.DriveBatchMoveFilesRequest>) {
    return this._ipcProxyCall<API_FILE.DriveBatchMoveFilesResponse>(IPC_API_NAME.BATCH_MOVE_FILES, options)
  }

  async batchUpdate(options: IFileApisGetFilesOptions<API_FILE.DriveBatchUpdateFilesRequest>) {
    return this._ipcProxyCall<API_FILE.DriveBatchUpdateFilesResponse>(IPC_API_NAME.BATCH_UPDATE_FILES, options)
  }

  async batchDeleteShare(options: IShareApisCommonOptions<API_SHARE.DriveBatchDeleteSharesRequest>) {
    return this._ipcProxyCall<API_SHARE.DriveBatchDeleteSharesResponse>(IPC_API_NAME.BATCH_DELETE_SHARE, options)
  }

  async createShare(options: IShareApisCommonOptions<API_SHARE.DriveCreateShareRequest>) {
    return this._ipcProxyCall<API_SHARE.DriveCreateShareResponse>(IPC_API_NAME.CREATE_SHARE, options)
  }

  async getAllCategoryFileCount(options?: IFileApisGetFilesOptions<null>) {
    return this._ipcProxyCall<IGetAllCategoryFileCountResponse>(IPC_API_NAME.GET_ALL_CATEGORY_FILE_COUNT, options)
  }

  async getFileAncestors(fileId: string, options?: IFileApisGetFilesOptions<IGetFileAncestorsRequest>) {
    return this._ipcProxyCall<API_FILE.DriveGetFileAncestorsAdminResponse>(IPC_API_NAME.GET_File_ANCESTORS, fileId, options)
  }

  async createFile (options?: IFileApisGetFilesOptions<API_FILE.DriveCreateFileRequest>) {
    return this._ipcProxyCall<API_FILE.DriveCreateFileResponse>(IPC_API_NAME.CREATE_FILE, options)
  }

  async getFileBySpace (space: string, folder_type: EFileSpaceFolderType, options?: IFileApisGetFilesOptions<null>) {
    return this._ipcProxyCall<IGetFileBySpaceResponse>(IPC_API_NAME.GET_FILE_BY_SPACE, space, folder_type, options)
  }

  async getRestoreList (options?: IShareApisCommonOptions<IGetRestoreListParams>) {
    return this._ipcProxyCall<API_SHARE.DriveRestoreListResponse>(IPC_API_NAME.GET_RESTORE_LIST, options)
  }

  async deleteRestoreById (id: string, options?: IFileApisGetFilesOptions<API_SHARE.DriveRestoreDeleteRequest>) {
    return this._ipcProxyCall<API_SHARE.DriveRestoreDeleteResponse>(IPC_API_NAME.DELETE_RESTORE_BY_ID, id, options)
  }

  async checkSafeHasInit (options?: ISafeApisCommonOptions<ICheckSafeHasInitParams>) {
    return this._ipcProxyCall<API_PASSWORD.IPasswordQueryResponse>(IPC_API_NAME.SAFE_BOX_CHECK_HAS_INIT, options)
  }

  async checkPassword (options?: ISafeApisCommonOptions<IPasswordParams>) {
    return this._ipcProxyCall<API_PASSWORD.IPasswordCheckResponse>(IPC_API_NAME.SAFE_BOX_CHECK_PASSWORD, options)
  }

  async initPassword (options?: ISafeApisCommonOptions<IPasswordParams>) {
    return this._ipcProxyCall<API_PASSWORD.IPasswordInitResponse>(IPC_API_NAME.SAFE_BOX_INIT_PASSWORD, options)
  }

  async resetPassword (options?: ISafeApisCommonOptions<IResetPasswordParams>) {
    return this._ipcProxyCall<API_PASSWORD.IPasswordResetResponse>(IPC_API_NAME.SAFE_BOX_RESET_PASSWORD, options)
  }

  async sendVerificationCode (options: ISafeApisCommonOptions<ISendVerificationCodeParams> = {}) {
    return this._ipcProxyCall<API_PASSWORD.IPasswordSendVerificationCodeResponse>(IPC_API_NAME.SAFE_BOX_SEND_VERIFICATION_CODE, options)
  }

  async checkVerificationCode (options: ISafeApisCommonOptions<ICheckVerificationCodeParams> = {}) {
    return this._ipcProxyCall<API_PASSWORD.IPasswordVerifyVerificationCodeResponse>(IPC_API_NAME.SAFE_BOX_CHECK_VERIFICATION_CODE, options)
  }

  /**
   * 以下接口在 thunder-pan-plugin 中注册
   */

  async getAllFilesInDirectory(options: IGetAllFilesInDirectoryOptions) {
    return this._ipcProxyCall<API_FILE.DriveFile[]>(IPC_API_NAME.GET_ALL_FILES_IN_DIRECTORY, options);
  }

  async getSameFilesInDirectory(options: IGetSameFilesInDirectoryOptions) {
    return this._ipcProxyCall<API_FILE.DriveFile[]>(IPC_API_NAME.GET_SAME_FILES_IN_DIRECTORY, options);
  }

  async getSafeBoxToken() {
    return this._ipcProxyCall<string>(IPC_API_NAME.GET_SAFE_BOX_TOKEN)
  }

  async addUrlToDrive(task: IAddUrlToDriveTaskData, options?: IAddUrlToDriveOptions) {
    return this._ipcProxyCall<API_FILE.DriveCreateFileResponse>(IPC_API_NAME.ADD_URL_TO_DRIVE, task, options)
  }

  async batchAddUrlsToDrive(tasks: IAddUrlToDriveTaskData[], options?: IBatchAddUrlsToDriveOptions) {
    return this._ipcProxyCall<API_FILE.DriveCreateFileResponse[]>(IPC_API_NAME.BATCH_ADD_URLS_TO_DRIVE, tasks, options)
  }

  async batchCreateDownloadTaskWithFiles(files: API_FILE.DriveFile[], savePath: string = '') {
    return this._ipcProxyCall<IBatchCreateDownloadTaskResponse>(IPC_API_NAME.BATCH_CREATE_DOWNLOAD_TASK, files, savePath)
  }

  async startDownloadPreprocess(files: API_FILE.DriveFile[]) {
    return this._ipcProxyCall<IStartPreprocessResult>(IPC_API_NAME.START_DOWNLOAD_PREPROCESS, files)
  }

  async startCreateDownloadTaskByPreprocess(savePath: string = '') {
    return this._ipcProxyCall<IBatchCreateDownloadTaskResponse>(IPC_API_NAME.START_CREATE_DOWNLOAD_TASK_BY_PREPROCESS, savePath)
  }

  async openPathSelectorDialog(options: IPathSelectorPropsOptions = {}) {
    return this._ipcProxyCall<IPathSelectorResponse | undefined>(IPC_API_NAME.OPEN_PATH_SELECTOR_DIALOG, options)
  }

  async openDirectory (fileId: string, options: IOpenDirectoryOptions = { fileSpace: '' }) {
    return this._ipcProxyCall<IOpenDirectoryResponse>(IPC_API_NAME.OPEN_PAN_DIRECTORY, fileId, options)
  }

  async getCurrentUserTrashPrivilegeDuration () {
    return this._ipcProxyCall<number>(IPC_API_NAME.GET_CURRENT_USER_PRIVILEGE_TRASH_DURATION)
  }

  async getCurrentUserCloudAddQuotas () {
    return this._ipcProxyCall<IGetCurrentUserQuotasResponse>(IPC_API_NAME.GET_CURRENT_USER_CLOUD_ADD_QUOTAS)
  }

  async getCurrentUserDriveQuotas () {
    return this._ipcProxyCall<IGetCurrentUserQuotasResponse>(IPC_API_NAME.GET_CURRENT_USER_DRIVE_QUOTAS)
  }

  async getRecentSaveFolders () {
    return this._ipcProxyCall<IRecentDownloadPathData[]>(IPC_API_NAME.RECENT_SAVE_GET_FOLDERS)
  }

  async setRecentSaveFolders (folders: IRecentDownloadPathData[]) {
    return this._ipcProxyCall<void>(IPC_API_NAME.RECENT_SAVE_SET_FOLDERS, folders)
  }

  async getRecentSaveDefaultFolder () {
    return this._ipcProxyCall<IRecentDownloadPathData>(IPC_API_NAME.RECENT_SAVE_GET_DEFAULT)
  }

  async setRecentSaveDefaultFolder (defaultPath: IRecentDownloadPathData) {
    return this._ipcProxyCall<void>(IPC_API_NAME.RECENT_SAVE_SET_DEFAULT, defaultPath)
  }

  async clearRecentSaveFolder () {
    return this._ipcProxyCall<void>(IPC_API_NAME.RECENT_SAVE_CLEAR_FOLDERS)
  }

  async deleteRecentSaveFolders (folderIds: string[]) {
    return this._ipcProxyCall<void>(IPC_API_NAME.RECENT_SAVE_DELETE_FOLDER, folderIds)
  }

  async openCloudAddTab () {
    return this._ipcProxyCall<void>(IPC_API_NAME.OPEN_PAN_CLOUD_ADD_TAB)
  }

  async appendNewFilesToDrive (parentId: string, files: API_FILE.DriveFile[]) {
    return this._ipcProxyCall<void>(IPC_API_NAME.APPEND_NEW_FILES_TO_DRIVE, parentId, files)
  }

  async consumeFileById (fileId: string, space: string = '') {
    return this._ipcProxyCall<void>(IPC_API_NAME.CONSUME_FILE_BY_ID, fileId, space)
  }
}

export { ThunderPanClientSDK }

