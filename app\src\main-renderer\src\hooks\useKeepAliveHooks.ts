import { onActivated, onBeforeMount, onDeactivated, onMounted, onUnmounted } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'

export interface KeepAliveHooksOptions {
  onActivated?: (route: RouteLocationNormalized) => void
  onDeactivated?: (route: RouteLocationNormalized) => void
  onMounted?: (route: RouteLocationNormalized) => void
  onBeforeMount?: (route: RouteLocationNormalized) => void
  onUnmounted?: (route: RouteLocationNormalized) => void
}

// 全局注册的页面生命周期结果回调
const pageLifecycleResultHandlers: Record<string, {
  onSuccess?: () => void,
  onFail?: (error: any) => void
}> = {}

/**
 * 注册某个页面的生命周期成功/失败回调
 * @param routeName 路由 name
 * @param onSuccess 成功回调
 * @param onFail 失败回调
 */
export function registerPageLifecycleResultHandler(
  routeName: string,
  onSuccess?: () => void,
  onFail?: (error: any) => void
) {
  pageLifecycleResultHandlers[routeName] = { 
    onSuccess: onSuccess ? () => { onSuccess(); delete pageLifecycleResultHandlers[routeName]; } : undefined,
    onFail: onFail ? (err) => { onFail(err); delete pageLifecycleResultHandlers[routeName]; } : undefined
  }
}

/**
 * Keep-Alive 生命周期钩子工具
 * @param route 当前路由对象
 * @param options 配置选项
 */
export function useKeepAliveHooks(
  route: RouteLocationNormalized,
  options: KeepAliveHooksOptions = {}
) {
  const {
    onActivated: customOnActivated,
    onDeactivated: customOnDeactivated,
    onMounted: customOnMounted,
    onBeforeMount: customOnBeforeMounted,
    onUnmounted: customOnUnmounted
  } = options

  // 首次 onActivated 不执行，后续才执行
  let isFirstActivated = true

  // 封装执行并处理成功/失败回调
  async function execWithResult(fn: ((route: RouteLocationNormalized) => void) | undefined, hookType: string) {
    if (!fn) return
    try {
      await fn(route)
      const handler = pageLifecycleResultHandlers[route.name as string]
      if (handler && handler.onSuccess) {
        handler.onSuccess()
        // 刷新时，keep-alive 会失效会重新激活，所以需要设置为 false
        isFirstActivated = false
      }
    } catch (err) {
      const handler = pageLifecycleResultHandlers[route.name as string]
      if (handler && handler.onFail) {
        handler.onFail(err)
        // 刷新时，keep-alive 会失效会重新激活，所以需要设置为 false
        isFirstActivated = false
      }
    }
  }

  // 页面初次加载前触发
  if (customOnBeforeMounted) {
    onBeforeMount(() => {
      execWithResult(customOnBeforeMounted, 'onBeforeMount')
    })
  }

  // 页面初次加载时触发
  if (customOnMounted) {
    onMounted(() => {
      execWithResult(customOnMounted, 'onMounted')
    })
  }

  // 页面卸载时触发
  if (customOnUnmounted) {
    onUnmounted(() => {
      execWithResult(customOnUnmounted, 'onUnmounted')
    })
  }

  // 页面激活时触发
  onActivated(() => {
    if (isFirstActivated) {
      isFirstActivated = false
      return
    }
    execWithResult(customOnActivated, 'onActivated')
  })

  // 页面失活时触发
  onDeactivated(() => {
    if (customOnDeactivated) {
      customOnDeactivated(route)
    }
  })
}

/**
 * 创建特定页面的 Keep-Alive 钩子
 * @param route 路由对象
 * @param onActivated 激活回调
 * @param onDeactivated 失活回调
 * @param onMounted 初次加载调用
 * @param onBeforeMount 初次加载前调用
 * @param onUnmounted 页面卸载调用
 */
export function createKeepAliveHooks(
  route: RouteLocationNormalized,
  onActivated?: (route: RouteLocationNormalized) => void,
  onDeactivated?: (route: RouteLocationNormalized) => void,
  onMounted?: (route: RouteLocationNormalized) => void,
  onBeforeMount?: (route: RouteLocationNormalized) => void,
  onUnmounted?: (route: RouteLocationNormalized) => void
) {
  return useKeepAliveHooks(route, {
    onActivated,
    onDeactivated,
    onMounted,
    onBeforeMount,
    onUnmounted
  })
}

/**
 * 常用的 Keep-Alive 处理器
 */
export const keepAliveHandlers = {
  // 刷新数据
  refreshData: (refreshFn: () => void) => (route: RouteLocationNormalized) => {
    console.log(`🔄 [${String(route.name)}] 刷新数据`)
    refreshFn()
  },

  // 保存/恢复滚动位置
  scrollPosition: (element: HTMLElement, key: string) => {
    return {
      save: (route: RouteLocationNormalized) => {
        const position = element.scrollTop
        sessionStorage.setItem(`scroll_${key}`, position.toString())
        console.log(`💾 [${String(route.name)}] 保存滚动位置: ${position}`)
      },
      restore: (route: RouteLocationNormalized) => {
        const saved = sessionStorage.getItem(`scroll_${key}`)
        if (saved) {
          element.scrollTop = parseInt(saved)
          console.log(`📖 [${String(route.name)}] 恢复滚动位置: ${saved}`)
        }
      }
    }
  },

  // WebSocket 连接管理
  webSocket: (connectFn: () => void, disconnectFn: () => void) => {
    return {
      connect: (route: RouteLocationNormalized) => {
        console.log(`🔗 [${String(route.name)}] 连接 WebSocket`)
        connectFn()
      },
      disconnect: (route: RouteLocationNormalized) => {
        console.log(`🔌 [${String(route.name)}] 断开 WebSocket`)
        disconnectFn()
      }
    }
  },

  // 定时器管理
  timer: (timerRef: { value: number | null }, startFn: () => void) => {
    return {
      start: (route: RouteLocationNormalized) => {
        if (!timerRef.value) {
          startFn()
          console.log(`⏰ [${String(route.name)}] 启动定时器`)
        }
      },
      stop: (route: RouteLocationNormalized) => {
        if (timerRef.value) {
          clearInterval(timerRef.value)
          timerRef.value = null
          console.log(`⏹️ [${String(route.name)}] 停止定时器`)
        }
      }
    }
  }
} 