#pragma once
#include "liveupdate.h"
#include <map>
#include <string>
#include <list>
#include <assert.h>

#include "xl_lib/xml/xml_dom_parser.h"

using namespace xl::xml;

struct PatchInfo 
{
    PATCH_TYPE type;
    std::string param;
    std::string url;
    std::string desc;
    std::string md5;
    std::string httpurl;
    unsigned long filesize; 
};

struct NewUpdateInfo
{
    std::string name;
    std::string newver;
    std::string param;
    std::string url;
    std::string desc;
    std::string md5;
    std::string httpurl;
    unsigned long filesize; 
};

struct InstalledPatchInfo
{
    std::string name;
    bool InstallRet;
};

class PatchManager
{
public:
    PatchManager(void);
    ~PatchManager(void);

    static PatchManager* PatchManager::GetInstance()
    {
        static PatchManager s_instance;

        return &s_instance;
    }

    void Init(const wchar_t* ProductName, const wchar_t* ProductVer, const wchar_t* ProductPathMD5);
    void UnInit();
    void AddPatchToInstallList(const char* PatchId);
    void RemovePatchFromInstallList(const char* PatchId);
    void RemoveAllPatchFromInstallList();
    void SetPatchMonopolize(bool monopolize);
    bool GetPatchMonopolize();

    void SetThisPatchId(const char* id);
    void GetThisPatchId(std::string& id);

    void SavePatchInfo();

    void InsertPatchInfo(const char* id,
        PATCH_TYPE type,
        const char* param,
        const char* url,
        const char* httpurl,
        const char* desc,
        const char* md5,
        unsigned long filesize);

    void SetUpdateInfo(const char* name,
        const char* newver,
        const char* param,
        const char* url,
        const char* httpurl,
        const char* desc,
        const char* md5,
        unsigned long filesize);
    NewUpdateInfo* GetUpdateInfo();

    bool GetPatchInfo(const char* id, PatchInfo* & pInfo);

    void BeginEnumInstallList();
    bool InstallListHasNext(std::string& id);

    size_t GetInstalledPatchCount();
    bool GetInstalledPatchId(size_t index, std::string& PatchId, bool& InstallRet);
    void AddInstalledPatchId(const char* id, const char* InstallTime, bool installResult = false);
    bool IsPatchInstalled(const char* id);

    const char* GetNewUpdateVer();
    const char* GetNewUpdateDesc();
private:
    void LoadInstalledPatchInfo();

    bool m_bInit;
    std::map<std::string, PatchInfo> m_PackInfos;
    std::list<std::string> m_InstallList;
    std::list<std::string>::iterator m_InstallListIterator;
    bool m_bBeginEnum;

    std::vector<InstalledPatchInfo> m_InstalledPatchId;

    bool m_bHaveUpdate;
    NewUpdateInfo m_UpdateInfo;

    std::wstring m_ProductName;
    std::wstring m_ProductVer;
    std::wstring m_ProductPathMD5;

    xml_element* m_InstalledPatchRoot;

    bool m_bPatchMonopolize;

    std::string m_nowPatchId;

    bool m_bPatchInfoDirty;
};
