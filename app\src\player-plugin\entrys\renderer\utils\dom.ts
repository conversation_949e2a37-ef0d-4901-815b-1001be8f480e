import { MaybeElementRef, unrefElement } from "@vueuse/core";
import { MaybeRefOrGetter, toValue } from "vue";

function clickElement(element: HTMLElement) {
  if (!element) return

  // 创建点击事件
  const clickEvent = new PointerEvent('click', { bubbles: true })

    // React15 hack
    ; (clickEvent as any).simulated = true

  // 触发点击事件
  element.dispatchEvent(clickEvent)
}

const changeInputValue = (
  inputElement: HTMLInputElement | HTMLTextAreaElement,
  value: string,
) => {
  if (!inputElement) return

  inputElement.setRangeText(value, 0, inputElement.value.length, 'end')

  // 创建input事件
  const inputEvent = new Event('input', { bubbles: true })
  inputElement.dispatchEvent(inputEvent)

  // 创建change事件
  const changeEvent = new Event('change', { bubbles: true })

  // 触发change事件
  inputElement.dispatchEvent(changeEvent)
}

function searchDomByXPath(xpath: string): Element | null {
  const result = document.evaluate(xpath, document)
  return result.iterateNext() as Element
}

function searchDomBySelector<T extends HTMLElement>(
  selector: string,
): T | null {
  return document.querySelector<T>(selector)
}

/**
 * 根据xpath列表查找dom, 只要找到1个就返回
 * @param xpaths
 * @returns
 */
function waitForDomByXPathList(xpaths: string[]): Promise<Element> {
  return new Promise((resolve, reject) => {
    const observer = new MutationObserver(() => {
      for (const xpath of xpaths) {
        const element = searchDomByXPath(xpath)
        if (element) {
          observer.disconnect()
          resolve(element)
          return
        }
      }
    })

    observer.observe(document, {
      childList: true,
      subtree: true,
    })

    // 初始检查一次
    for (const xpath of xpaths) {
      const element = searchDomByXPath(xpath)
      if (element) {
        observer.disconnect()
        resolve(element)
        return
      }
    }

    setTimeout(() => {
      reject(new Error(`Selector not found`))
      observer.disconnect()
    }, 5000) // 超时时间设置为5秒
  })
}

/**
 * 根据selector列表查找dom, 只要找到1个就返回
 * @param selectors
 * @returns
 */
function waitForDomBySelectorList(selectors: string[]): Promise<Element> {
  return new Promise((resolve, reject) => {
    const observer = new MutationObserver(() => {
      for (const selector of selectors) {
        const element = searchDomBySelector(selector)
        if (element) {
          observer.disconnect()
          resolve(element)
          return
        }
      }
    })

    observer.observe(document, {
      childList: true,
      subtree: true,
    })

    // 初始检查一次
    for (const selector of selectors) {
      const element = searchDomBySelector(selector)
      if (element) {
        observer.disconnect()
        resolve(element)
        return
      }
    }
  })
}

export {
  clickElement,
  changeInputValue,
  searchDomByXPath,
  waitForDomByXPathList,
  waitForDomBySelectorList,
}

export const domUtils = {
  parentUntil(
    elem: HTMLElement,
    selectorOrEl: string | HTMLElement,
  ): HTMLElement | null {
    if (elem.nodeName === 'BODY') {
      return null
    }

    const parent = elem.parentElement
    if (parent === null) {
      return null
    }

    if (typeof selectorOrEl === 'string') {
      if (parent.matches(selectorOrEl)) {
        // 找到，并返回
        return parent
      }
    } else {
      if (parent.isEqualNode(selectorOrEl)) {
        // 找到，并返回
        return parent
      }
    }

    // 继续查找，递归
    return domUtils.parentUntil(parent, selectorOrEl)
  },
}

export function addClass(el: HTMLElement, className: string) {
  if (hasClass(el, className)) return

  const newClass = el.className.split(' ')
  newClass.push(className)
  el.className = newClass.join(' ')
}
export function removeClass(el: HTMLElement, className: string) {
  if (!hasClass(el, className)) return

  let newClass = el.className.split(' ')
  newClass = newClass.filter((it) => it !== className)
  el.className = newClass.join(' ')
}
export function hasClass(el: HTMLElement, className: string) {
  const reg = new RegExp(`(^|\\s)${className}(\\s|$)`)
  return el && reg.test(el.className)
}

export function getComposedPath(e: Event): EventTarget[] {
  return (
    // @ts-ignore
    e.path || //
    (e.composedPath && e.composedPath()) ||
    []
  )
}

// fork from https://github.com/vueuse/vueuse/blob/ac72c11372dfd791abbfe3449f912d92d3e50dd7/packages/core/onClickOutside/index.ts#L94
export function isComposedPathMatch(event: Event, list: MaybeRefOrGetter<(MaybeElementRef | string)[]>) {
  return toValue(list).some((target) => {
    if (typeof target === 'string') {
      return Array.from(window.document.querySelectorAll(target))
        .some(el => el === event.target || getComposedPath(event).includes(el))
    }
    else {
      const el = unrefElement(target)
      return el && (event.target === el || getComposedPath(event).includes(el))
    }
  })
}