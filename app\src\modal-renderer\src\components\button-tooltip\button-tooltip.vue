<script setup lang="ts">
import { ref, computed } from 'vue'
import { PopoverArrow, PopoverContent, PopoverPortal, PopoverRoot, PopoverTrigger } from 'reka-ui'
import Button from '@root/common/components/ui/button/index.vue'

// 定义组件属性
interface Props {
  variant?: 'primary' | 'secondary' | 'warning' | 'weak-lead' | 'outline' | 'ghost'
  size?: 'sm' | 'lg'
  popoverTitle?: string
  popoverText?: string
  disabled?: boolean
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'sm',
  popoverTitle: '存在没有解析成功的链接，是否继续？',
  popoverText: '如果继续操作，将不会添加这些链接。',
  disabled: false,
  class: '',
})

// 定义 emit 事件
const emit = defineEmits<{
  cancel: []
  submit: []
}>()

const isOpen = ref(false)

const handleContinue = () => {
  isOpen.value = false
  emit('submit')
}

const handleLater = () => {
  isOpen.value = false
  emit('cancel')
}

// 处理按钮点击事件
const handleButtonClick = (event: Event) => {
  // 当按钮被禁用时，阻止事件传播，不显示弹出框
  if (props.disabled) {
    event.preventDefault()
    event.stopPropagation()
    return
  }
  // 当按钮未被禁用时，显示弹出框
  isOpen.value = true
}

// 合并 class 属性
const buttonClass = computed(() => {
  const baseClass = 'later-button'
  return props.class ? `${baseClass} ${props.class}` : baseClass
})
</script>

<template>
  <PopoverRoot v-model:open="isOpen">
    <PopoverTrigger :as-child="true">
      <Button
        :class="buttonClass"
        :variant="props.variant"
        :size="props.size"
        :disabled="props.disabled"
        @click="handleButtonClick"
      >
        <slot>稍后</slot>
      </Button>
    </PopoverTrigger>

    <PopoverPortal>
      <PopoverContent
        class="popover-content"
        side="top"
        :side-offset="8"
        align="start"
      >
        <div class="popover-inner">
          <div class="popover-header">
            <h3 class="popover-title">{{ props.popoverTitle }}</h3>
          </div>

          <div class="popover-body">
            <p class="popover-text">{{ props.popoverText }}</p>
          </div>

          <div class="popover-footer">
            <Button
              class="wait-btn"
              variant="secondary"
              size="sm"
              @click="handleLater"
            >
              暂不
            </Button>

            <Button
              class="continue-btn"
              variant="secondary"
              size="sm"
              @click="handleContinue"
            >
              继续操作
            </Button>
          </div>
        </div>

        <PopoverArrow
          class="popover-arrow"
          :width="8"
          :height="4"
        />
      </PopoverContent>
    </PopoverPortal>
  </PopoverRoot>
</template>

<style lang="scss" scoped>
.later-button {
  padding: 8px 16px;
}

:global(.popover-content) {
  z-index: 10000 !important;
  position: relative;
  min-width: 280px;
  max-width: 320px;
  padding: 0;
  border: 1px solid var(--border-border-2);
  border-radius: var(--border-radius-M);
  background: var(--background-background-elevated);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.popover-inner {
  display: flex;
  flex-direction: column;
}

.popover-header {
  padding: 16px 16px 12px 16px;
  border-bottom: 1px solid var(--border-border-3);
}

.popover-title {
  margin: 0;
  color: var(--font-font-1);
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
}

.popover-body {
  padding: 12px 16px;
}

.popover-text {
  margin: 0;
  color: var(--font-font-2);
  font-size: 13px;
  line-height: 1.5;
}

.popover-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px 16px 16px;
  gap: 8px;
}

.continue-btn {
  min-width: 60px;
}

.wait-btn {
  min-width: 60px;
}

:global(.popover-arrow) {
  fill: var(--background-background-elevated);
  z-index: 10001 !important;
}

/* 确保 popover content 不被遮挡 */
:global([data-reka-portal]) {
  z-index: 10000 !important;
}

:deep(.playButton) {
  width: 154px;
  padding: 0;
}

.playButton {
  width: 154px;
  padding: 0;
}
</style>
