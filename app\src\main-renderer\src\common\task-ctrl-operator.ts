import path from 'path';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { taskExtraFunc } from '@/common/task-extra'
import * as BaseType from '@root/common/task/base';

export namespace TaskCtrlOperatorNS {
  export interface IPlayUrl {
    fileName: string;
    fullPath: string;
    index?: number;
    cid?: string;
    gcid?: string;
    isForbidden?: boolean;
    taskId?: number;
    taskUrl?: string;
    playPosition?: number;
    duration?: number;
    isFinish: boolean;
  }

  // 实现 isNullOrUndefined
  export function isNullOrUndefined (obj: any): boolean {
    return obj === null || obj === undefined;
  }

  export async function isExistTaskFile(taskBase: BaseType.TaskBase): Promise<boolean> {
    let exist: boolean = false;
    do {
      if (isNullOrUndefined(taskBase)) {
        break;
      }

      const savePath: string = taskBase.savePath || '';
      let fileName: string = taskBase.taskName || '';
      if (fileName !== undefined && fileName.indexOf('.') === fileName.length - 1) {
        fileName = fileName.substring(0, fileName.length - 1);
      }
      let filePath: string = path.join(savePath, fileName);
      if (taskBase.taskType === BaseType.TaskType.P2sp || taskBase.taskType === BaseType.TaskType.Emule) {
        // do nothing
      } else if (taskBase.taskType === BaseType.TaskType.Bt) {
        const downloadCount = await taskExtraFunc.getDownloadCount(taskBase.taskId);
        const btFileList = await taskExtraFunc.getBtFileInfos(taskBase.taskId);
        if (downloadCount === 1) {
          const isSingleFile: boolean = await taskExtraFunc.isSingleBT(taskBase.taskId);
          if (isSingleFile) {
            const btFile = btFileList[0];
            filePath = path.join(btFile.filePath, btFile.fileName);;
          } else {
            const btFileList = await taskExtraFunc.getBtFileInfos(taskBase.taskId);
            for (const btFile of btFileList) {
              if (!btFile.download) { continue }
              const btFilePath: string = btFile.filePath;
              const btFileName: string = btFile.fileName;
              const dirWithTaskName: string = path.join(filePath, btFilePath);
              filePath = path.join(filePath, btFilePath, btFileName);
              if (btFilePath && !(await FileSystemAWNS.dirExistsAW(dirWithTaskName))) {
                const dirWithoutTaskName: string = path.join(savePath, btFilePath);
                if (await FileSystemAWNS.dirExistsAW(dirWithoutTaskName)) {
                  filePath = path.join(savePath, btFilePath, btFileName);
                }
              }
              break;
            }
          }
        } else {
          // 判断是否空文件夹
          exist = !(await FileSystemAWNS.isDirectoryEmptyAW(filePath));
          break;
        }
      } else if (taskBase.taskType === BaseType.TaskType.Group) {
        filePath = savePath;
        const subTaskIds: number[] = await taskExtraFunc.getGroupSubTaskIds(taskBase.taskId)
        for (const subTaskId of subTaskIds) {
          const subTask: BaseType.TaskBase | undefined = await taskExtraFunc.getTaskBase(subTaskId)
          if (subTask && subTask.downloadSubTask) {
            const subSavePath: string = subTask.savePath || '';
            const subFileName: string = subTask.taskName || '';
            const fullPath: string = path.join(subSavePath, subFileName);
            if (await FileSystemAWNS.existsAW(fullPath)) {
              exist = true;
              break;
            }
          }
        }
        break;
      }
      exist = await FileSystemAWNS.existsAW(filePath);
    } while (0);
    return exist;
  }

}
