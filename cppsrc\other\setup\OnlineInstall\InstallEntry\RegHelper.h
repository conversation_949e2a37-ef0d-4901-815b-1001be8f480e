#pragma once
#include <string>

class CRegHelper
{
public:
	static HRESULT ReadRegString(<PERSON><PERSON><PERSON> hkey, LPCTSTR pszSubKey, LPCTSTR pszKeyName, std::wstring& strValue);
	static HRESULT WriteRegString(<PERSON>E<PERSON> hkey, LPCTSTR pszSubKey, LPCTSTR pszKeyName, const std::wstring& strValue);

	static HRESULT ReadRegDword(<PERSON>E<PERSON> hKey, LPCTSTR pszSubKey, LPCTSTR pszKeyName, DWORD& dwValue, DWORD dwDefault = 0);
	static HRESULT WriteRegDword(<PERSON>EY hkey, LPCTSTR pszSubKey, LPCTSTR pszKeyName, DWORD dwValue);

	static HRESULT DeleteRegKey(<PERSON>E<PERSON> hKey, LPCTSTR pszSubKey);
	static HRESULT DeleteRegValue(<PERSON><PERSON><PERSON> hKey, LPCTSTR pszSubKey, LPCTSTR pszKeyName);

};


