'use strict';

const path = require('path');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');

const isBuildProduction = true;

const packageJSON = require('../package.json');
const outDir = path.join(__dirname, '../out');

let mainConfig = {
  entry: {
    'asar': [path.join(__dirname, '../bin/asar.js')]
  },
  module: {
    rules: [{
        enforce: 'pre',
        test: /\.js$/,
        loader: 'source-map-loader',
        include: path.resolve(__dirname, '../lib')
      },
      {
        test: /\.ts$/,
        loader: 'ts-loader',
        include: path.resolve(__dirname, '../lib'),
        exclude: /node_modules/,
        options: {
          transpileOnly: true,
          experimentalWatchApi: true
        }
      }
    ]
  },
  output: {
    filename: '[name].js',
    libraryTarget: 'commonjs2',
    path: outDir
  },
  node: false,
  mode: isBuildProduction ? 'production' : 'development',
  devtool: 'source-map',
  optimization: {
    removeAvailableModules: false,
    removeEmptyChunks: false,
    splitChunks: false,
    ...(isBuildProduction ? {
      minimizer: [
        new UglifyJsPlugin({
          sourceMap: false,
          cache: true,
          parallel: true,
          uglifyOptions: {
            ecma: 6,
            compress: {
              warnings: false
            }
          }
        })
      ]
    } : {
      minimize: false
    })
  },
  plugins: [],
  resolve: {
    extensions: ['.js', '.json', '.ts'],
    symlinks: false,
    alias: {
      '@': path.join(__dirname, '../src')
    }
  },
  target: 'node',
  stats: 'errors-only',
  bail: true
};

module.exports = mainConfig;
