<script lang="ts" setup>
import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
import Curve from '../curve/index.vue'
import { useTaskDetailStore } from '@/stores/taskDetail'
import * as BaseType from '@root/common/task/base'
import { TimeHelperNS } from '@root/common/helper/time-helper'
import { divideAndFormat } from '../../utils'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { clipboard } from 'electron'
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'
import XMPMessage from '@root/common/components/ui/message/index'
import { IAddUrlToDriveTaskData } from '@root/thunder-pan-plugin/src/utils/business-helper'
import { useUserStore } from '@/stores/user'
import { PopUpNS } from '@root/common/pop-up'
import { selectPanPath } from '@/common/thunder-pan-task'
import { taskExtraFunc } from '@/common/task-extra'
import { ThunderUtil } from '@root/common/utils'


const taskDetailStore = useTaskDetailStore()
const userStore = useUserStore()

const autoSelectTab = ref<boolean>(true)

const taskInfo = computed(() => {
  return taskDetailStore.taskInfo
})

const isGroupTask = computed(() => {
  return taskInfo.value.taskType === BaseType.TaskType.Group
})

const createTime = computed(() => { 
  return taskInfo.value.createTime ? TimeHelperNS.formatDate(new Date(taskInfo.value.createTime), 'yyyy-MM-dd hh:mm:ss') : ''
})

const isDownload = computed(() => {
  return !!(taskInfo.value.taskStatus && [BaseType.TaskStatus.StartWaiting, BaseType.TaskStatus.StartPending, BaseType.TaskStatus.Started].includes(taskInfo.value.taskStatus))
})

const isPanTask = computed(() => {
  return taskDetailStore.isPanTask
})

const isShowLink = computed(() => {
  return !(isPanTask.value || isGroupTask.value) && taskInfo.value.url
})

const downloadPeriod = computed(() => {
  if (taskInfo.value.downloadPeriod) {
    return TimeHelperNS.formatSeconds(taskInfo.value.downloadPeriod)
  }
  return 0
})

const calculatePer = computed(() => {
  if (taskInfo.value.taskStatus === BaseType.TaskStatus.Succeeded || taskInfo.value.taskStatus === BaseType.TaskStatus.Seeding) {
    return 100
  }
  if (taskInfo.value.fileSize && taskInfo.value.downloadSize) {
    return divideAndFormat(taskInfo.value.downloadSize,taskInfo.value.fileSize)
  }
  return 0
})

const residueTime = computed(() => {
  const { downloadSize, downloadSpeed, fileSize } = taskInfo.value
  if (!(typeof downloadSpeed === 'number' && typeof downloadSize === 'number' && typeof fileSize === 'number' && downloadSpeed > 0)) {
    return '--:--:--'
  }
  const residueSize = fileSize - downloadSize
  const time = residueSize / downloadSpeed
  console.log('>>>>>>>>>>>>>>> time', time)
  if (time) {
    return `${TimeHelperNS.formatSeconds(time)}`
  }
  return '--:--:--'
})




function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

const changeGroupAddPan = async (task, panSavePathId: string, savePanPath: string) => { // 组任务
  const { taskId, taskName, savePath = '' } = task
  const rootPath = savePath + '\\'
  const escapedRootPath = ThunderUtil.escapeRegExp(rootPath)
  const regexp = new RegExp(`^${escapedRootPath}`)
  const params: IAddUrlToDriveTaskData[] = []
  const groupTaskIds = await taskExtraFunc.getGroupSubTaskIds(taskId)
  
  for (const subTaskId of groupTaskIds) {

    const subTask = await taskExtraFunc.getTaskBase(subTaskId)

    // 判断是否勾选
    if (!subTask?.downloadSubTask) { continue }
    console.log('>>>>>>>>>>>>>> subTask', subTask)

    const subPath = subTask?.savePath ?? ''
    const changeSubPath = subPath.replace(regexp, '')
    console.log('>>>>>>>>>>>>>> changeSubPath', changeSubPath)
    // 判断是否为bt文件
    if (subTask.taskType === BaseType.TaskType.Bt) {
      const btFileInfos = await taskExtraFunc.getBtFileInfos(subTask.taskId)
      // 处理bt子文件
      const name = subTask.taskName || ''
      const addInfo = {
        name,
        url: subTask.url || '',
        path: changeSubPath,
        parentId: panSavePathId,
        files: [] as string[],
      }
      btFileInfos.forEach(item => {
        if (item.download) {
          addInfo.files.push(String(item.realIndex))
        }
      })
      params.push(addInfo)
    } else if (subTask && subTask.url) {
      params.push({
        name: subTask.taskName,
        url: subTask.url,
        parentId: panSavePathId,
        path: changeSubPath
      })
    }
  }
  console.log('>>>>>>>>>>>>> params', params)
  if (params.length > 0) {
    const res = await ThunderPanClientSDK.getInstance().batchAddUrlsToDrive(params, {
      groupName: taskName,
      autoSelectTab: autoSelectTab.value
    })
    if (res.success && res.data) {
      autoSelectTab.value = false
      XMPMessage({
        type: 'success',
        message: '已加入云添加列表'
      })
    }
    // changeResPanInfo(res, savePanPath, taskId, panSavePathId)
  }
}

/** 同步云盘 */
const handleSyncPan = () => {
  autoSelectTab.value = true
  if (!userStore.uid) {
    PopUpNS.showLoginDlg()
    XMPMessage({
      message: '请先登录',
      type: 'warning'
    })
    return
  }
  ThunderPanClientSDK.getInstance().openPathSelectorDialog({}).then(async (res) => {
    console.log('>>>>> res', res)
    if (!(res && res.success && res.data)) {
      return;
    }
    const savePanPath = selectPanPath(res)

    if (!res.data?.selectedItem) { return }

    const panSavePathId = res.data.selectedItem.id || ''
    // let isToastHint = false
    const task = taskDetailStore.taskInfo

    const { taskId, taskType, taskName, savePath = '' } = task

    if (taskType === BaseType.TaskType.Group) {
      changeGroupAddPan(task, panSavePathId, savePanPath)

    } else if (task.url) {

      const params = {
        files: [] as string[],
        name: task.taskName,
        url: task.url,
        parentId: panSavePathId,
        path: ''
      }
      // 判断是否为bt文件
      if (task.taskType === BaseType.TaskType.Bt) {
        const btFileInfos = await taskExtraFunc.getBtFileInfos(taskId)
        console.log('>>>>>>>>>>>>>>> btFileInfos', btFileInfos)
        btFileInfos.forEach(item => {
          if (item.download) {
            params.files.push(String(item.realIndex))
          }
        })
      }
      const res = await ThunderPanClientSDK.getInstance().batchAddUrlsToDrive([params], {
        autoSelectTab: autoSelectTab.value
      })
      if (res.success && res.data) {
        autoSelectTab.value = false
        XMPMessage({
          type: 'success',
          message: '已加入云添加列表'
        })
      }
      // changeResPanInfo(res, savePanPath, taskId)
    }
      // 存储路径
  })
}

const continueDownload = () => {
  taskDetailStore.startTask(taskInfo.value)
}

const handleRetryDownload = () => {
  taskDetailStore.retryDownload(taskInfo.value)
}

const handleCopyLink = () => {
  console.log('> handleCopyLink', taskInfo.value.url)
  if (!taskInfo.value.url) {
    XMPMessage({
      message: '链接不存在',
      type: 'warning'
    })
    return
  }
  clipboard.writeText(taskInfo.value.url)
  XMPMessage({
    message: '复制成功',
    type: 'success'
  })
}

const openFolder = () => {
  console.log('> openFolder', taskInfo.value)
// 展示文件选择对话框
  ConsumeManagerNs.openTaskFolder(taskInfo.value.taskId)
}

const play = () => {
  console.log('> handlePlay', taskInfo.value)
  ConsumeManagerNs.consumeTask(taskInfo.value.taskId, -1)
}



onMounted(() => {
})
</script>

<template>
  <div :class=$style.detailWrapper>
    <div :class="$style.speedCurve">
      <div :class="$style.curveWrapper">
        <Curve
          :isDownload="isDownload"
          @play="play"
          @openFolder="openFolder"
          @continueDownload="continueDownload"

        ></Curve>
        <!-- <div v-if="!isDownload" :class="$style.curveBtn">
          <Button v-if="isStop" variant="primary" size="sm" @click="continueDownload">
            <i class="xl-icon-download"></i>继续下载
          </Button>
          <Button v-else-if="taskDetailStore.isSupportPlay && isCompleted" variant="primary" size="sm" @click="handlePlay">
            <i class="xl-icon-general-play-m"></i>播放
          </Button>
          <Button v-else-if="!taskDetailStore.isSupportPlay && isCompleted" variant="primary" size="sm" @click="openFolder">
            <i class="xl-icon-general-openfolder-m"></i>打开文件夹
          </Button>
        </div> -->
      </div>
      <div :class="$style.downInfo">
        <div :class="$style.item">下载进度{{ calculatePer }}%</div>
        <div v-if="isDownload" :class="$style.item">{{ residueTime }}</div>
        <div v-if="isDownload" :class="$style.item">资源数{{ taskInfo.srcUsing || 0 }}/{{ taskInfo.srcTotal || 0 }}</div>
      </div>
    </div>
    <div :class="$style.infoWrapper">
      <div v-if="!isPanTask" :class="$style.infoItem">
        <div :class="$style.infoLeft">
          <div :class="$style.infoLabel">云盘位置</div>
          <div :class="$style.infoVal">保存到云盘，可随时查看</div>
        </div>
        <div v-if="taskInfo.url || !isPanTask"  :class="$style.infoBtn">
          <Button variant="outline" size="sm" @click="handleSyncPan">
            <i class="xl-icon-dump"></i>同步云盘
          </Button>
        </div>
      </div>
      <div v-if="isShowLink" :class="$style.infoItem">
        <div :class="[$style.infoLeft, $style.flexStart]">
          <div :class="$style.infoLabel">下载链接</div>
          <div :class="$style.link">{{taskInfo.url}}</div>
        </div>
        <div :class="[$style.infoBtn, $style.flexStart]">
          <Button variant="outline" size="sm" @click="handleCopyLink">
            <i class="xl-icon-topbar-revert"></i>复制链接
          </Button>
        </div>
      </div>
      <div v-if="!isPanTask" :class="$style.infoItem">
        <div :class="$style.infoLeft">
          <div :class="$style.infoLabel">网址记录</div>
          <div :class="$style.infoVal">{{ taskInfo.refUrl || '暂无网址记录'}}</div>
        </div>
      </div>
      <div :class="$style.infoItem">
        <div :class="$style.infoLeft">
          <div :class="$style.infoLabel">保存目录</div>
          <div :class="$style.infoVal">{{ taskInfo.savePath }}</div>
        </div>
          <Button variant="outline" size="sm" @click="openFolder">
            <i class="xl-icon-general-openfolder-m"></i>打开目录
          </Button>
        </div>
    </div>
    <div :class="$style.infoItem">
      <div :class="$style.infoLeft">
        <div :class="$style.infoLabel">创建时间</div>
        <div :class="$style.infoVal">{{ createTime }}</div>
      </div>
    </div>
    <div :class="$style.infoItem">
      <div :class="$style.infoLeft">
        <div :class="$style.infoLabel">下载历时</div>
        <div :class="$style.infoVal">{{ downloadPeriod }}</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" module>
.detailWrapper {
  padding: 0 24px;
  .speedCurve {
    width: 452px;
    height: 210px;
    border: 1px solid var(--border-border-2);
    border-radius: var(--border-radius-M);
    overflow: hidden;
    box-sizing: border-box;
  }
  .curveWrapper {
    height: 178px;
    position: relative;
  }
  .curveBtn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }
  .downInfo {
    display: flex;
    align-items: center;
    height: 30px;
    color: var(--font-font-3);
    font-size: 12px;
    padding-left: 8px;
  }
  .item {
    position: relative;
    margin: 0 10px;
    &:first-of-type {
      margin-left: 0;
    }
    &:last-of-type {
      margin-right: 0;
      &:after {
        content: none;
      }
    }
    &:after {
      content: "";
      background: var(--border-border-2);
      width: 1px;
      height: 10px;
      position: absolute;
      right: -10px;
      top: 3px;
    }
  }
  .infoWrapper {
    margin-top: 16px;
  }
  .infoItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 32px;
    margin-top: 8px;
    &:first-of-type {
      margin-top: 0px;
    }
  }
  .infoLeft {
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 20px;
  }
  .infoLabel {
    color: var(--font-font-1);
    flex-shrink: 0;
  }
  .infoVal {
    color: var(--font-font-3, rgba(134, 144, 156, 1));
    margin-left: 12px;
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 1;
    -webkit-line-clamp: 1;
  }
  .flexStart {
    align-items: flex-start;
  }
  .link {
    color: var(--font-font-3, rgba(134, 144, 156, 1));
    margin-left: 12px;
    word-break: break-all;
  }
  .showAll {
    line-clamp: unset;
    -webkit-line-clamp: unset;
  }
  .infoBtn {
    margin-left: 14px;
    flex-shrink: 0;
  }
}
</style>