import { ComponentInternalInstance, createVNode, render, shallowReactive, VNode } from 'vue'
import DialogConstructor from './component.vue'

let seed = 1

export interface DialogHandler {
  close: () => void
}

export type DialogContext = {
  id: string
  vnode: VNode
  handler: DialogHandler
  vm: ComponentInternalInstance
  props: any
}

export type TButtonVariant = 'default' | 'primary' | 'secondary' | 'warning' | 'weak-lead' | 'outline' | 'ghost'
export interface IConfirmDialogOptions {
  id?: string
  headerTitle: string
  bodyText: string
  variant?: string
  leftButtonText?: string
  leftButtonWarning?: boolean
  cancelButtonText?: string
  confirmButtonText?: string
  cancelButtonDisabled?: boolean
  confirmButtonDisabled?: boolean
  leftButtonVariant?: TButtonVariant
  cancelButtonVariant?: TButtonVariant
  confirmButtonVariant?: TButtonVariant
  onClose?: () => any
  onConfirm: () => any
  onLeftButtonClick?: () => any
}

export const instances: any[] = shallowReactive([])

export const getInstance = (id: string) => {
  const idx = instances.findIndex((instance) => instance.id === id)
  const current = instances[idx]
  let prev: DialogContext | undefined
  if (idx > 0) {
    prev = instances[idx - 1]
  }
  return { current, prev }
}

const closeDialog = (instance: DialogContext) => {
  const idx = instances.indexOf(instance)
  if (idx === -1) return

  instances.splice(idx, 1)
  const { handler } = instance
  handler.close()
}

const createDialog = (options: IConfirmDialogOptions): DialogContext => {
  const id = `confirm_dialog_${seed++}`
  const container = document.createElement('div')

  const props = {
    ...options,
    id,
    onClose: () => {
      if (options.onClose) {
        options.onClose()
      }
      closeDialog(instance)
    },
    onDestroy: () => {
      render(null, container)
    },
  }

  const vnode = createVNode(DialogConstructor, props)
  render(vnode, container)
  document.body.appendChild(container.firstElementChild!)

  const vm = vnode.component!
  const handler = {
    close: () => {
      vm.exposed!.visible.value = false
    },
  }

  const instance = {
    id,
    vnode,
    vm,
    handler,
    props: (vnode.component as any).props,
  }

  return instance
}

export const CreateConfirmDialog = (options: IConfirmDialogOptions) => {
  const instance = createDialog(options)

  instances.push(instance)
  return instance.handler
}

export const closeAllConfirmDialog = () => {
  instances.forEach(instance => {
    instance.handler.close()
  })
}
