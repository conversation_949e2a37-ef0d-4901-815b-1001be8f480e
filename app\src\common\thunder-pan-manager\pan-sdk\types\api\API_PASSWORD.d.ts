export namespace API_PASSWORD {
  export interface IPasswordQueryResponse {
    /**
     * 是否已经初始化
     */
    has_init?: boolean;
  }

  export interface IPasswordInitResponse {
    /**
     * 成功提示
     */
    msg?: string;
  }

  export interface IPasswordCheckResponse {
    /**
     * 校验成功 token，用于短时间内识别成功校验过密码
     */
    token?: string;
  }

  export interface IPasswordResetResponse {
    /**
     * 成功提示
     */
    msg?: string;
  }

  export interface IPasswordSendVerificationCodeResponse {
    verification_id?: string;
    is_user?: boolean;
    expires_in?: number;
  }

  export interface IPasswordVerifyVerificationCodeResponse {
    verification_token?: string;
    expires_in?: number;
  }
}
