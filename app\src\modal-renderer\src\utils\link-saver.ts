/// <reference path="../../../common/link-hub/impl/thunder-client-api.d.ts" />
import { LinkHubHelper } from '@root/common/link-hub/client/link-hub-helper'
import * as BaseType from '@root/common/task/base'
import { useNewTaskStore } from '@root/modal-renderer/src/stores/new-task'
import {
  type ITaskExtraDataType,
  type IUrlDataMap,
  type IUrlWithTypeArray,
  type TaskExtDataMap,
  type TaskFileSelectionMap,
} from '@root/modal-renderer/types/new-task.type'
import {
  LinkSaveScene,
  LinkSaveOptions,
  SavePendingAllTasksParams,
  LinkSaveResult,
} from '@root/modal-renderer/types/link-saver.type'

/**
 * 链接保存工具类
 * 提供统一的链接保存功能，支持多种场景和任务类型
 */
export class LinkSaver {
  private static instance: LinkSaver | null = null
  private linkHubHelper: LinkHubHelper
  private newTaskStore: ReturnType<typeof useNewTaskStore>

  constructor() {
    this.linkHubHelper = LinkHubHelper.getInstance()
    this.newTaskStore = useNewTaskStore()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LinkSaver {
    if (!LinkSaver.instance) {
      LinkSaver.instance = new LinkSaver()
    }
    return LinkSaver.instance
  }

  /**
   * 保存所有待处理的任务到链接中心（新版本 - 使用对象参数）
   * @param params 参数对象
   * @returns 保存结果（根据 waitForCompletion 参数决定是否等待Promise执行完成）
   */
  public async savePendingAllTasks(params: SavePendingAllTasksParams): Promise<LinkSaveResult> {
    const {
      allUrlsWithType,
      dataMap,
      urlExtraDataMap,
      checkedFileIndexes,
      options = {
        scene: LinkSaveScene.CUSTOM,
        actions: ['ACTION_UNKNOWN'], // 默认未知
        status: 'STATUS_NORMAL' as ThunderClientAPI.dataStruct.dataModals.LinkStatus, // 默认未知状态
        waitForCompletion: false, // 默认立即返回
      },
      optionsExtData = {},
    } = params

    const result: LinkSaveResult = {
      success: true,
      savedCount: 0,
      failedCount: 0,
      errors: [],
      scene: options.scene,
    }

    console.log(
      `[LinkSaver] 开始保存链接，场景: ${options.scene}，操作类型: ${options.actions.join(', ')}，任务数量: ${allUrlsWithType.length}，等待完成: ${options.waitForCompletion}`
      , params)

    // 如果设置了并发控制，使用并发控制版本
    if (options.maxConcurrency && options.maxConcurrency > 0) {
      return this.savePendingAllTasksWithConcurrency(params)
    }

    // 创建所有保存任务的Promise数组
    const savePromises = allUrlsWithType.map(async urlWithType => {
      const { url, taskType } = urlWithType

      try {
        const taskInfo = dataMap[url]
        if (!taskInfo) {
          console.warn(`[LinkSaver] 找不到任务数据: ${url}`)
          return { success: false, url, error: '找不到任务数据' }
        }

        let saveSuccess = false

        switch (taskType) {
          case BaseType.TaskType.Magnet:
            saveSuccess = await this.saveMagnetTask(taskInfo, options, checkedFileIndexes, url, optionsExtData)
            break
          case BaseType.TaskType.P2sp:
            saveSuccess = await this.saveP2spTask(taskInfo, options, optionsExtData)
            break
          case BaseType.TaskType.Emule:
            saveSuccess = await this.saveEmuleTask(taskInfo, options, optionsExtData)
            break
          default:
            console.warn(`[LinkSaver] 未知的任务类型: ${taskType}, URL: ${url}`)
            return { success: false, url, error: `未知的任务类型: ${taskType}` }
        }

        if (saveSuccess) {
          return { success: true, url }
        } else {
          return { success: false, url, error: `保存${BaseType.TaskType[taskType]}任务失败` }
        }
      } catch (error) {
        console.error(`[LinkSaver] 保存任务时出错: ${url}`, error)
        return {
          success: false,
          url,
          error: error instanceof Error ? error.message : '未知错误',
        }
      }
    })

    // 根据 waitForCompletion 参数决定是否等待结果
    if (options.waitForCompletion) {
      // 等待所有Promise执行完成
      console.log(`[LinkSaver] 等待所有任务执行完成...`)
      const results = await Promise.allSettled(savePromises)

      // 统计结果
      results.forEach((promiseResult, index) => {
        if (promiseResult.status === 'fulfilled') {
          const taskResult = promiseResult.value
          if (taskResult.success) {
            result.savedCount++
          } else {
            result.failedCount++
            result.errors.push({
              url: taskResult.url,
              error: taskResult.error || '未知错误',
            })
          }
        } else {
          // Promise被拒绝的情况
          const urlWithType = allUrlsWithType[index]
          result.failedCount++
          result.errors.push({
            url: urlWithType.url,
            error:
              promiseResult.reason instanceof Error
                ? promiseResult.reason.message
                : 'Promise执行失败',
          })
        }
      })

      // 如果有失败的任务，整体标记为失败
      result.success = result.failedCount === 0

      console.log(`[LinkSaver] 所有任务执行完成，成功: ${result.savedCount}，失败: ${result.failedCount}`)
      return result
    } else {
      // 在后台执行所有Promise，但不等待结果
      Promise.allSettled(savePromises).then(results => {
        // 后台统计结果（仅用于日志）
        let successCount = 0
        let failureCount = 0
        const errors: Array<{ url: string; error: string }> = []

        results.forEach((promiseResult, index) => {
          console.log('promiseResult', promiseResult)
          if (promiseResult.status === 'fulfilled') {
            const taskResult = promiseResult.value
            if (taskResult.success) {
              successCount++
            } else {
              failureCount++
              errors.push({
                url: taskResult.url,
                error: taskResult.error || '未知错误',
              })
            }
          } else {
            // Promise被拒绝的情况
            const urlWithType = allUrlsWithType[index]
            failureCount++
            errors.push({
              url: urlWithType.url,
              error:
                promiseResult.reason instanceof Error
                  ? promiseResult.reason.message
                  : 'Promise执行失败',
            })
          }
        })

        console.warn(`[LinkSaver] 后台任务执行完成，成功: ${successCount}，失败: ${failureCount}`)
        if (errors.length > 0) {
          console.warn(`[LinkSaver] 后台执行错误:`, errors)
        }
      }).catch(error => {
        console.error(`[LinkSaver] 后台任务执行出错:`, error)
      })

      // 立即返回，表示任务已启动
      return result
    }
  }

  /**
   * 带并发控制的批量保存任务
   * @param params 参数对象
   * @returns 保存结果（根据 waitForCompletion 参数决定是否等待Promise执行完成）
   */
  private async savePendingAllTasksWithConcurrency(
    params: SavePendingAllTasksParams
  ): Promise<LinkSaveResult> {
    const {
      allUrlsWithType,
      dataMap,
      urlExtraDataMap,
      checkedFileIndexes,
      options = {
        scene: LinkSaveScene.CUSTOM,
        actions: ['ACTION_TODO'],
        status: 'STATUS_NORMAL' as ThunderClientAPI.dataStruct.dataModals.LinkStatus, // 默认未知状态
        waitForCompletion: false, // 默认立即返回
      },
      optionsExtData = {},
    } = params

    const result: LinkSaveResult = {
      success: true,
      savedCount: 0,
      failedCount: 0,
      errors: [],
      scene: options.scene,
    }

    const maxConcurrency = options.maxConcurrency || 10
    console.log(`[LinkSaver] 使用并发控制保存链接，最大并发数: ${maxConcurrency}，等待完成: ${options.waitForCompletion}`)

    if (options.waitForCompletion) {
      // 等待所有批次执行完成
      console.log(`[LinkSaver] 等待所有批次执行完成...`)
      await this.executeBatchesInBackground(allUrlsWithType, dataMap, options, checkedFileIndexes, optionsExtData, maxConcurrency)

      // 注意：这里返回的结果仍然是初始状态，因为 executeBatchesInBackground 只用于日志
      // 如果需要真实的执行结果，需要修改 executeBatchesInBackground 的返回值
      console.log(`[LinkSaver] 所有批次执行完成`)
      return result
    } else {
      // 立即返回结果，不等待Promise执行完成
      console.log(`[LinkSaver] 已启动 ${allUrlsWithType.length} 个保存任务，将在后台分批执行`)

      // 在后台分批执行所有Promise，但不等待结果
      this.executeBatchesInBackground(allUrlsWithType, dataMap, options, checkedFileIndexes, optionsExtData, maxConcurrency)

      // 立即返回，表示任务已启动
      return result
    }
  }

  /**
   * 在后台分批执行任务
   */
  private async executeBatchesInBackground(
    allUrlsWithType: IUrlWithTypeArray,
    dataMap: IUrlDataMap,
    options: LinkSaveOptions,
    checkedFileIndexes?: TaskFileSelectionMap,
    optionsExtData?: TaskExtDataMap,
    maxConcurrency: number = 10
  ): Promise<void> {
    let totalSuccessCount = 0
    let totalFailureCount = 0
    const totalErrors: Array<{ url: string; error: string }> = []

    try {
      // 分批处理任务
      for (let i = 0; i < allUrlsWithType.length; i += maxConcurrency) {
        const batch = allUrlsWithType.slice(i, i + maxConcurrency)
        const batchNumber = Math.floor(i / maxConcurrency) + 1

        console.log(`[LinkSaver] 开始执行批次 ${batchNumber}/${Math.ceil(allUrlsWithType.length / maxConcurrency)}`)

        // 创建当前批次的Promise数组
        const batchPromises = batch.map(async urlWithType => {
          const { url, taskType } = urlWithType

          try {
            const taskInfo = dataMap[url]
            if (!taskInfo) {
              console.warn(`[LinkSaver] 找不到任务数据: ${url}`)
              return { success: false, url, error: '找不到任务数据' }
            }

            let saveSuccess = false

            switch (taskType) {
              case BaseType.TaskType.Magnet:
                saveSuccess = await this.saveMagnetTask(taskInfo, options, checkedFileIndexes, url, optionsExtData)
                break
              case BaseType.TaskType.P2sp:
                saveSuccess = await this.saveP2spTask(taskInfo, options, optionsExtData)
                break
              case BaseType.TaskType.Emule:
                saveSuccess = await this.saveEmuleTask(taskInfo, options, optionsExtData)
                break
              default:
                console.warn(`[LinkSaver] 未知的任务类型: ${taskType}, URL: ${url}`)
                return { success: false, url, error: `未知的任务类型: ${taskType}` }
            }

            if (saveSuccess) {
              return { success: true, url }
            } else {
              return { success: false, url, error: `保存${BaseType.TaskType[taskType]}任务失败` }
            }
          } catch (error) {
            console.error(`[LinkSaver] 保存任务时出错: ${url}`, error)
            return {
              success: false,
              url,
              error: error instanceof Error ? error.message : '未知错误',
            }
          }
        })

        // 等待当前批次完成
        const batchResults = await Promise.allSettled(batchPromises)

        // 统计当前批次结果
        let batchSuccessCount = 0
        let batchFailureCount = 0
        const batchErrors: Array<{ url: string; error: string }> = []

        batchResults.forEach((promiseResult, batchIndex) => {
          if (promiseResult.status === 'fulfilled') {
            const taskResult = promiseResult.value
            if (taskResult.success) {
              batchSuccessCount++
              totalSuccessCount++
            } else {
              batchFailureCount++
              totalFailureCount++
              batchErrors.push({
                url: taskResult.url,
                error: taskResult.error || '未知错误',
              })
              totalErrors.push({
                url: taskResult.url,
                error: taskResult.error || '未知错误',
              })
            }
          } else {
            // Promise被拒绝的情况
            const urlWithType = batch[batchIndex]
            batchFailureCount++
            totalFailureCount++
            const error = {
              url: urlWithType.url,
              error:
                promiseResult.reason instanceof Error
                  ? promiseResult.reason.message
                  : 'Promise执行失败',
            }
            batchErrors.push(error)
            totalErrors.push(error)
          }
        })

        console.log(
          `[LinkSaver] 批次 ${batchNumber} 完成，成功: ${batchSuccessCount}，失败: ${batchFailureCount}`
        )
        if (batchErrors.length > 0) {
          console.warn(`[LinkSaver] 批次 ${batchNumber} 错误:`, batchErrors)
        }
      }

      console.log(`[LinkSaver] 所有批次执行完成，总成功: ${totalSuccessCount}，总失败: ${totalFailureCount}`)
      if (totalErrors.length > 0) {
        console.warn(`[LinkSaver] 总错误:`, totalErrors)
      }
    } catch (error) {
      console.error(`[LinkSaver] 后台批次执行出错:`, error)
    }
  }

  /**
   * 保存单个磁力链任务
   */
  private async saveMagnetTask(
    taskInfo: any,
    options: LinkSaveOptions,
    checkedFileIndexes?: TaskFileSelectionMap,
    url?: string,
    optionsExtData?: TaskExtDataMap
  ): Promise<boolean> {
    try {
      // 获取选中的文件索引
      let selectedFileIndexes: number[] = []
      if (checkedFileIndexes && url && checkedFileIndexes[url]) {
        selectedFileIndexes = checkedFileIndexes[url].fileIndexes || []
        console.log(`[LinkSaver] 磁力链任务 ${url} 选中的文件索引:`, selectedFileIndexes)
      }

      // 筛选出选中的文件列表
      let filteredFileLists: any[] = []
      if (taskInfo.fileLists && Array.isArray(taskInfo.fileLists)) {
        if (selectedFileIndexes.length > 0) {
          // 根据选中的索引筛选文件
          filteredFileLists = taskInfo.fileLists.filter((file: any) =>
            selectedFileIndexes.includes(file.realIndex)
          )
          console.log(
            `[LinkSaver] 筛选后的文件列表数量: ${filteredFileLists.length}/${taskInfo.fileLists.length}`
          )
        } else {
          // 如果没有选中索引信息，使用所有文件
          filteredFileLists = taskInfo.fileLists
          console.log(`[LinkSaver] 未找到选中索引信息，使用所有文件: ${filteredFileLists.length}`)
        }
      }

      // 构造FileItem数组，基于筛选后的文件列表
      const fileList: ThunderClientAPI.dataStruct.dataModals.FileItem[] = filteredFileLists.map(
        (file: any, index: number) => ({
          url_hash: '', // 暂时留空，由系统生成
          file_index: file.realIndex || index,
          file_ext: file.fileName?.split('.').pop() || '',
          file_name: decodeURIComponent(file.fileName || ''),
          file_path: file.filePath || '',
          local_saved_path: '', // 本地保存路径暂时留空
          gcid: '', // 暂时留空
          file_size: file.fileSize || 0,
        })
      )

      // 计算选中文件的总大小
      const totalSize = filteredFileLists.reduce(
        (sum: number, file: any) => sum + (file.fileSize || 0),
        0
      )

      // 使用 new-task store 的 getTaskDisplayName 方法获取任务名称
      const taskName = this.newTaskStore.getTaskDisplayName(taskInfo.url)
      console.log('✅ 保存磁力链任务: 任务名称', taskName)

      // 构造LinkInfo参数
      const linkInfo: ThunderClientAPI.dataStruct.dataModals.LinkInfo = {
        url: taskInfo.url || '',
        url_type: 'magnet' as ThunderClientAPI.dataStruct.dataModals.UrlType,
        name: decodeURIComponent(taskName),
        gcid: '', // 暂时留空
        size: totalSize,
        create_time: taskInfo.addTime || Date.now(),
        file_ext: '', // 磁力链接没有特定扩展名
        actions: options.actions || ['ACTION_TODO'],
        local_saved_path: '', // 本地保存路径暂时留空
        status: options.status, // 从options中获取status

        // 文件夹相关信息
        is_folder: filteredFileLists.length > 1,
        total_count: filteredFileLists.length,
        selected_count: filteredFileLists.length, // 筛选后的文件都是选中的
        selected_size: totalSize,
        fileList: fileList,
      }

      // 构造上报参数
      const saveParam: ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordParam = {
        link: linkInfo,
        ignoreEvent: options.ignoreEvent || false,
      }

      console.log(`[LinkSaver] 保存磁力链任务`, saveParam)

      // 调用saveLink进行上报
      const result = await this.linkHubHelper.saveLink(saveParam)

      if (result.error && result.error.result !== 0) {
        console.error(
          `[LinkSaver] 保存磁力链任务失败: ${result.error.message}, URL: ${taskInfo.url}`
        )
        return false
      } else {
        console.log(`[LinkSaver] 保存磁力链任务成功: ${linkInfo.name}`)
        return true
      }
    } catch (error) {
      console.error(`[LinkSaver] 保存磁力链任务时出错:`, error)
      return false
    }
  }

  /**
   * 保存单个P2SP任务
   */
  private async saveP2spTask(
    taskInfo: any,
    options: LinkSaveOptions,
    optionsExtData?: TaskExtDataMap
  ): Promise<boolean> {
    // 使用 new-task store 的 getTaskDisplayName 方法获取任务名称
    const taskName = this.newTaskStore.getTaskDisplayName(taskInfo.url)
    console.log('✅ 保存P2SP任务: 任务名称', taskName)

    try {
      // 构造LinkInfo参数
      const linkInfo: ThunderClientAPI.dataStruct.dataModals.LinkInfo = {
        url: taskInfo.url,
        url_type: 'http' as ThunderClientAPI.dataStruct.dataModals.UrlType,
        name: decodeURIComponent(taskName),
        gcid: '', // 暂时留空
        size: taskInfo.fileSize || 0,
        create_time: taskInfo.addTime || Date.now(),
        file_ext: taskInfo.fileName?.split('.').pop() || '',
        actions: options.actions || ['ACTION_TODO'],
        local_saved_path: '', // 本地保存路径暂时留空
        status: options.status, // 从options中获取status

        // 文件夹相关信息
        is_folder: false,
        total_count: 0,
        selected_count: 0,
        selected_size: 0,
        fileList: [],
      }

      // 构造上报参数
      const saveParam: ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordParam = {
        link: linkInfo,
        ignoreEvent: options.ignoreEvent || false,
      }

      console.log(
        `[LinkSaver] 保存P2SP任务: ${linkInfo.name}，操作类型: ${options.actions.join(', ')}`, linkInfo
      )

      // 调用saveLink进行上报
      const result = await this.linkHubHelper.saveLink(saveParam)

      if (result.error && result.error.result !== 0) {
        console.error(`[LinkSaver] 保存P2SP任务失败: ${result.error.message}, URL: ${taskInfo.url}`)
        return false
      } else {
        console.log(`[LinkSaver] 保存P2SP任务成功: ${linkInfo.name}`)
        return true
      }
    } catch (error) {
      console.error(`[LinkSaver] 保存P2SP任务时出错:`, error)
      return false
    }
  }

  /**
   * 保存单个Emule任务
   */
  private async saveEmuleTask(taskInfo: any, options: LinkSaveOptions, optionsExtData?: TaskExtDataMap): Promise<boolean> {
    // 使用 new-task store 的 getTaskDisplayName 方法获取任务名称
    const taskName = this.newTaskStore.getTaskDisplayName(taskInfo.url)
    console.log('✅ 保存Emule任务: 任务名称', taskName)

    try {
      // 构建上报数据
      const linkInfo: ThunderClientAPI.dataStruct.dataModals.LinkInfo = {
        url: taskInfo.url,
        url_type: 'ed2k' as ThunderClientAPI.dataStruct.dataModals.UrlType,
        name: decodeURIComponent(taskName),
        gcid: '',
        size: taskInfo.fileSize || 0,
        create_time: taskInfo.addTime || Date.now(),
        file_ext: taskInfo.fileName?.split('.').pop() || '',
        actions: options.actions || ['ACTION_TODO'],
        local_saved_path: '',
        status: options.status, // 从options中获取status
        is_folder: false,
        total_count: 0,
        selected_count: 0,
        selected_size: 0,
        fileList: [],
      }

      // 上报到LinkHub
      const saveParam: ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordParam = {
        link: linkInfo,
        ignoreEvent: options.ignoreEvent || false,
      }

      console.log(
        `[LinkSaver] 保存Emule任务: ${linkInfo.name}，操作类型: ${options.actions.join(', ')}`
      )

      const result = await this.linkHubHelper.saveLink(saveParam)

      if (result.error && result.error.result !== 0) {
        console.error(
          `[LinkSaver] 保存Emule任务失败: ${result.error.message}, URL: ${taskInfo.url}`
        )
        return false
      } else {
        console.log(`[LinkSaver] 保存Emule任务成功: ${linkInfo.name}`)
        return true
      }
    } catch (error) {
      console.error(`[LinkSaver] 保存Emule任务时出错:`, error)
      return false
    }
  }

  /**
   * 批量保存指定类型的任务
   * @param allUrlsWithType 所有URL及其类型的数组
   * @param dataMap 任务数据映射
   * @param taskTypes 要保存的任务类型数组
   * @param options 保存选项
   * @returns 保存结果
   */
  public async savePendingTasksByType(
    allUrlsWithType: IUrlWithTypeArray,
    dataMap: IUrlDataMap,
    taskTypes: BaseType.TaskType[],
    options: LinkSaveOptions = {
      scene: LinkSaveScene.CUSTOM,
      actions: ['ACTION_TODO'],
      status: 'STATUS_NORMAL' as ThunderClientAPI.dataStruct.dataModals.LinkStatus, // 默认未知状态
    }
  ): Promise<LinkSaveResult> {
    // 过滤出指定类型的任务
    const filteredTasks = allUrlsWithType.filter(item => taskTypes.includes(item.taskType))

    return this.savePendingAllTasks({
      allUrlsWithType: filteredTasks,
      dataMap,
      urlExtraDataMap: undefined,
      options: {
        ...options,
        status: options.status || 'STATUS_NORMAL' as ThunderClientAPI.dataStruct.dataModals.LinkStatus,
      },
    })
  }
}

/**
 * 便捷函数：保存所有待处理任务
 * 支持新的对象参数格式，同时保持向后兼容
 */
export const savePendingAllTasks = (
  paramsOrAllUrlsWithType: SavePendingAllTasksParams | IUrlWithTypeArray,
  dataMap?: IUrlDataMap,
  urlExtraDataMap?: ITaskExtraDataType,
  options: LinkSaveOptions = {
    scene: LinkSaveScene.CUSTOM,
    actions: ['ACTION_TODO'],
    status: 'STATUS_NORMAL' as ThunderClientAPI.dataStruct.dataModals.LinkStatus, // 默认未知状态
    waitForCompletion: false, // 默认立即返回
  }
): Promise<LinkSaveResult> => {
  // 检查第一个参数是否为新的对象格式
  if (
    paramsOrAllUrlsWithType &&
    typeof paramsOrAllUrlsWithType === 'object' &&
    !Array.isArray(paramsOrAllUrlsWithType)
  ) {
    // 新的对象参数格式
    const params = paramsOrAllUrlsWithType as SavePendingAllTasksParams
    return LinkSaver.getInstance().savePendingAllTasks(params)
  } else {
    // 向后兼容的参数格式
    const allUrlsWithType = paramsOrAllUrlsWithType as IUrlWithTypeArray
    if (!dataMap) {
      throw new Error('dataMap is required when using legacy parameter format')
    }
    return LinkSaver.getInstance().savePendingAllTasks({
      allUrlsWithType,
      dataMap,
      urlExtraDataMap,
      options,
    })
  }
}

/**
 * 便捷函数：根据类型保存任务
 */
export const savePendingTasksByType = (
  allUrlsWithType: IUrlWithTypeArray,
  dataMap: IUrlDataMap,
  taskTypes: BaseType.TaskType[],
  options: LinkSaveOptions = {
    scene: LinkSaveScene.CUSTOM,
    actions: ['ACTION_TODO'],
    status: 'STATUS_NORMAL' as ThunderClientAPI.dataStruct.dataModals.LinkStatus, // 默认未知状态
    waitForCompletion: false, // 默认立即返回
  }
): Promise<LinkSaveResult> => {
  return LinkSaver.getInstance().savePendingTasksByType(
    allUrlsWithType,
    dataMap,
    taskTypes,
    options
  )
}
