// 公共文本溢出样式
.text-ellipsis-single {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.download-button {
    position: relative;

    .path-text {
        display: inline-block;
        flex-shrink: 0;
        max-width: 84px;
        @extend .text-ellipsis-single;
    }

    .select-path-type {
        z-index: 1000;
        position: absolute;
        right: 0;
        bottom: 100%;
        width: 152px;
        margin-bottom: 8px;
        overflow: hidden;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        background-color: #ffffff;
        box-shadow:
            0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06);

        .path-type-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:not(:last-child) {
                border-bottom: 1px solid #e5e7eb;
            }

            &:hover {
                background-color: #f9fafb;
            }

            .path-type-text {
                color: #374151;
                font-weight: 400;
                font-size: 14px;
            }

            .path-type-icon {
                color: #9ca3af;
                font-size: 12px;
            }
        }
    }

    // 下拉菜单过渡动画
    .dropdown-fade-enter-active,
    .dropdown-fade-leave-active {
        transform-origin: top center;
        transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
    }

    .dropdown-fade-enter-from {
        transform: translateY(-8px) scale(0.95);
        opacity: 0;
    }

    .dropdown-fade-leave-to {
        transform: translateY(-8px) scale(0.95);
        opacity: 0;
    }

    .dropdown-fade-enter-to,
    .dropdown-fade-leave-from {
        transform: translateY(0) scale(1);
        opacity: 1;
    }

    // 下载提示样式
    .space-warning {
        .download-tip-cloud {
            .content {
                cursor: pointer;
            }
        }

        .download-tip-container {
            display: flex;
            position: absolute;
            top: -30px;
            justify-content: flex-end;
            width: 280px;
            height: 22px;
            right: 0;

            .download-tip-cloud,
            .download-tip-local {
                .content {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: var(--font-font-3, #86909c);
                    font-style: normal;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 22px;
                    gap: 4px;
                }
            }

            .warning-tip {
                color: var(--functional-error-default, #ff4d4f);
            }

            .highlight {
                color: var(--primary-primary-default, #226df5);
            }

            .arrow-right {
                margin-left: 4px;
            }
        }
    }

    .disk-space-warning {
        display: flex;
        z-index: 100;
        position: absolute;
        top: 0;
        left: 0;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        border: 1px solid #f3c300;
        border-radius: 6px;
        background-color: rgba(255, 255, 255, 0.9);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .warning-content {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #d97706;
            font-weight: 500;
            font-size: 14px;

            .warning-icon {
                color: #f59e0b;
                font-size: 16px;
            }

            .warning-text {
                color: #d97706;
            }
        }
    }
}