#include "xlipc_factory.h"

XLIPCFactory::XLIPCFactory()
{
}

XLIPCFactory::~XLIPCFactory()
{
    DestroyAllIPCClient();
}

XLIPCFactory* XLIPCFactory::GetInstance()
{
    static XLIPCFactory xlipc_factory;
    return &xlipc_factory;
}

XLIPCClient* XLIPCFactory::CreateIPCClient(const wchar_t* id, const wchar_t* client_id)
{
	MapIPCClientPair pair_iter = map_ipcclients.equal_range(id);
	while (pair_iter.first != pair_iter.second)
	{
		if (wcscmp(pair_iter.first->second->GetClientId(), client_id) == 0)
		{
			pair_iter.first->second->AddRef();
			return pair_iter.first->second;
		}
		pair_iter.first++;
	}
    XLIPCClient* xlipc_client = new XLIPCClient(id, client_id);
    if (xlipc_client != NULL)
    {
        map_ipcclients.insert(MapIPCClient::value_type(id, xlipc_client));
    }
    return xlipc_client;
}

bool XLIPCFactory::DestroyIPCClient(XLIPCClient* xlipc_client)
{
    bool ret = false;
    do
    {
        if (xlipc_client == NULL)
        {
            break;
        }
        xlipc_client->Close();
        xlipc_client->SetCallback(NULL);
        MapIPCClient::iterator it = map_ipcclients.find(xlipc_client->GetId());
        if (it != map_ipcclients.end())
        {
            map_ipcclients.erase(it);
        }
        xlipc_client->Release();
        ret = true;
    } while (0);
    return ret;
}

void XLIPCFactory::DestroyAllIPCClient()
{
    MapIPCClient::iterator it = map_ipcclients.begin();
    for (; it != map_ipcclients.end(); ++it)
    {
        it->second->Close();
        it->second->SetCallback(NULL);
        it->second->Release();
    }
    map_ipcclients.clear();
}
