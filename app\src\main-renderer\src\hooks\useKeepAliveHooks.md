# useKeepAliveHooks 使用指南

`useKeepAliveHooks` 是一个用于监听 Vue 3 Keep-Alive 组件生命周期钩子的工具函数。

## 基本用法

```typescript
import { useKeepAliveHooks } from '@root/common/utils/useKeepAliveHooks'
import { useRoute } from 'vue-router'

const route = useRoute()

// 基本使用 - 包含所有生命周期钩子
useKeepAliveHooks(route, {
  onBeforeMount: (route) => {
    console.log('页面加载前')
    // 执行加载前的准备工作
  },
  onMounted: (route) => {
    console.log('页面初次挂载')
    // 执行初始化逻辑
  },
  onActivated: (route) => {
    console.log('页面被激活')
    // 执行激活时的逻辑（从缓存中恢复）
  },
  onDeactivated: (route) => {
    console.log('页面被失活')
    // 执行失活时的逻辑（进入缓存）
  },
  onUnmounted: (route) => {
    console.log('页面卸载')
    // 执行清理工作
  }
})
```

## 生命周期说明

### 完整生命周期钩子

- **onBeforeMount**: 页面组件挂载前触发，仅执行一次
- **onMounted**: 页面组件首次挂载完成后触发，仅执行一次
- **onActivated**: 页面从缓存中激活时触发（不包括首次挂载）
- **onDeactivated**: 页面失活进入缓存时触发
- **onUnmounted**: 页面组件真正卸载时触发

### 执行顺序

1. **首次加载**: `onBeforeMount` → `onMounted`
2. **切换到其他页面**: `onDeactivated`
3. **从其他页面切回**: `onActivated`
4. **页面销毁**: `onUnmounted`

> **注意**: `onActivated` 在首次挂载时不会触发，只有从缓存中恢复时才触发

## 便捷创建方法

```typescript
import { createKeepAliveHooks } from '@root/common/utils/useKeepAliveHooks'

// 使用便捷方法创建
createKeepAliveHooks(
  route,
  // onActivated
  (route) => console.log('激活'),
  // onDeactivated  
  (route) => console.log('失活'),
  // onMounted
  (route) => console.log('挂载'),
  // onBeforeMount
  (route) => console.log('挂载前'),
  // onUnmounted
  (route) => console.log('卸载')
)
```

## 生命周期结果回调

### 注册成功/失败回调

```typescript
import { registerPageLifecycleResultHandler } from '@root/common/utils/useKeepAliveHooks'

// 为特定页面注册生命周期执行结果回调
registerPageLifecycleResultHandler(
  'download', // 路由名称
  () => {
    // 生命周期执行成功回调
    console.log('下载页面生命周期执行成功')
  },
  (error) => {
    // 生命周期执行失败回调
    console.error('下载页面生命周期执行失败:', error)
  }
)

// 然后在页面中使用
useKeepAliveHooks(route, {
  onActivated: async (route) => {
    // 如果这里抛出异常，会触发失败回调
    throw new Error('激活失败')
  }
})
```

> **注意**: 结果回调只会执行一次，执行后会自动清理，避免内存泄漏

## 高级用法

### 1. 使用预定义的处理器

```typescript
import { useKeepAliveHooks, keepAliveHandlers } from '@root/common/utils/useKeepAliveHooks'

// 刷新数据
useKeepAliveHooks(route, {
  onActivated: keepAliveHandlers.refreshData(() => {
    // 刷新数据的逻辑
    fetchData()
  })
})

// 滚动位置管理
const scrollElement = document.querySelector('.scroll-container')
const scrollHandlers = keepAliveHandlers.scrollPosition(scrollElement, 'my-page')

useKeepAliveHooks(route, {
  onActivated: scrollHandlers.restore,
  onDeactivated: scrollHandlers.save
})

// WebSocket 连接管理
const wsHandlers = keepAliveHandlers.webSocket(
  () => connectWebSocket(),
  () => disconnectWebSocket()
)

useKeepAliveHooks(route, {
  onActivated: wsHandlers.connect,
  onDeactivated: wsHandlers.disconnect
})

// 定时器管理
const timerRef = ref<number | null>(null)
const timerHandlers = keepAliveHandlers.timer(timerRef, () => {
  timerRef.value = setInterval(() => {
    // 定时器逻辑
  }, 1000)
})

useKeepAliveHooks(route, {
  onActivated: timerHandlers.start,
  onDeactivated: timerHandlers.stop
})
```

### 2. 组合多个处理器

```typescript
useKeepAliveHooks(route, {
  onActivated: (route) => {
    // 刷新数据
    keepAliveHandlers.refreshData(fetchData)(route)
    // 恢复滚动位置
    keepAliveHandlers.scrollPosition(scrollElement, 'complex-page').restore(route)
    // 连接WebSocket
    keepAliveHandlers.webSocket(connectWS, disconnectWS).connect(route)
  },
  onDeactivated: (route) => {
    // 保存滚动位置
    keepAliveHandlers.scrollPosition(scrollElement, 'complex-page').save(route)
    // 断开WebSocket
    keepAliveHandlers.webSocket(connectWS, disconnectWS).disconnect(route)
  }
})
```

## 实际应用场景

### 1. 列表页面（如最近播放）

```typescript
// recentplay/index.vue
useKeepAliveHooks(route, {
  onBeforeMount: (route) => {
    console.log('[recentplay] 准备加载播放记录')
    // 预加载必要数据
  },
  onMounted: (route) => {
    console.log('[recentplay] 首次加载播放记录')
    initRecentPlay() // 初始化播放记录
  },
  onActivated: (route) => {
    console.log('[recentplay] 页面被激活，刷新播放记录')
    refreshRecentPlay() // 刷新播放记录
  },
  onDeactivated: (route) => {
    console.log('[recentplay] 页面被失活，保存当前状态')
    // 保存滚动位置、暂停数据更新等
    saveCurrentState()
  },
  onUnmounted: (route) => {
    console.log('[recentplay] 页面卸载，清理资源')
    // 清理定时器、事件监听器等
    cleanupResources()
  }
})
```

### 2. 云盘页面（常驻组件）

```typescript
// cloud/index.vue
useKeepAliveHooks(route, {
  onBeforeMount: (route) => {
    console.log('[cloud] 准备加载云盘组件')
    // 预加载云盘配置
  },
  onMounted: (route) => {
    console.log('[cloud] 云盘组件初始化')
    // 初始化云盘管理器
    initCloudManager()
  },
  onActivated: (route) => {
    console.log('[cloud] 云盘页面被激活')
    // 通知云盘组件页面激活
    GlobalEventHelper.getInstance().emit('CLOUD_PAGE_ACTIVATED')
  },
  onDeactivated: (route) => {
    console.log('[cloud] 云盘页面被失活')
    // 通知云盘组件页面失活
    GlobalEventHelper.getInstance().emit('CLOUD_PAGE_DEACTIVATED')
  },
  onUnmounted: (route) => {
    console.log('[cloud] 云盘组件卸载')
    // 清理云盘资源
    cleanupCloudResources()
  }
})
```

### 3. 下载页面（数据刷新）

```typescript
// download/index.vue
useKeepAliveHooks(route, {
  onActivated: keepAliveHandlers.refreshData(() => {
    // 刷新下载列表
    refreshDownloadList()
  })
})
```

### 4. 完整生命周期示例（下载页面）

```typescript
// download/index.vue
import { registerPageLifecycleResultHandler } from '@root/common/utils/useKeepAliveHooks'

// 注册页面生命周期结果回调
registerPageLifecycleResultHandler(
  'download',
  () => {
    console.log('✅ 下载页面生命周期执行成功')
    showSuccessMessage('页面加载完成')
  },
  (error) => {
    console.error('❌ 下载页面生命周期执行失败:', error)
    showErrorMessage('页面加载失败')
  }
)

useKeepAliveHooks(route, {
  onBeforeMount: async (route) => {
    // 预检查下载权限
    await checkDownloadPermission()
  },
  onMounted: async (route) => {
    // 初始化下载列表
    await initDownloadList()
  },
  onActivated: async (route) => {
    // 刷新下载状态
    await refreshDownloadStatus()
  },
  onDeactivated: (route) => {
    // 暂停刷新定时器
    pauseRefreshTimer()
  },
  onUnmounted: (route) => {
    // 清理WebSocket连接
    closeWebSocketConnection()
  }
})
```

## 注意事项

1. **路由对象**: 必须传入 `useRoute()` 返回的路由对象
2. **Keep-Alive**: 只有在 `<keep-alive>` 组件内的页面才会触发 `onActivated/onDeactivated` 钩子
3. **首次激活**: `onActivated` 在页面首次挂载时不会触发，只有从缓存中恢复时才触发
4. **异步处理**: 所有生命周期钩子都支持异步函数，异常会被自动捕获并触发失败回调
5. **内存管理**: 结果回调会在执行后自动清理，避免内存泄漏
6. **性能考虑**: 避免在激活钩子中执行过于耗时的操作
7. **错误处理**: 建议为重要页面注册结果回调来处理生命周期执行异常

## 日志输出示例

```
🟢 最近播放页面 被激活 (onActivated) {
  route: "recent_play",
  path: "/recent_play",
  params: {},
  query: {},
  time: "14:30:25"
}

🔴 最近播放页面 被失活 (onDeactivated) {
  route: "recent_play", 
  path: "/recent_play",
  params: {},
  query: {},
  time: "14:30:30"
} 