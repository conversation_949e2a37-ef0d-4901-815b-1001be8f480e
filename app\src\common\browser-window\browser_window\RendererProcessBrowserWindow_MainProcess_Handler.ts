import { UUIDUtility } from '@xbase/electron_base_kit'
import { SimpleBrowserWindowEvent } from '@xbase/electron_common_kit'

import {
  buildBrowserWindowNamespaceChannelString,
  buildFullBrowserWindowNamespaceChannelString,
} from './Common'
import { MainProcessBrowserWindow } from './MainProcessBrowserWindow'
import { mainProcessRemote } from '../process_remote/MainProcessRemote'
import { BrowserWindowConstructorOptions } from 'electron'
import { TBrowserWindowExtraConfig } from './type'

export class RendererProcessBrowserWindow_MainProcess_Handler {
  static readonly shared: RendererProcessBrowserWindow_MainProcess_Handler =
    new RendererProcessBrowserWindow_MainProcess_Handler()

  private _uuidSet: Set<string> = new Set()
  private _browserWindowMap: Map<string, MainProcessBrowserWindow> = new Map()

  constructor() {}

  prepare() {
    this._initMainProcessRemoteHandler()
  }

  private _initMainProcessRemoteHandler() {
    mainProcessRemote.on(
      buildBrowserWindowNamespaceChannelString(
        'new-RendererProcessBrowserWindow',
      ),
      (
        event,
        options?: BrowserWindowConstructorOptions,
        extraConfig?: TBrowserWindowExtraConfig,
      ) => {
        event.returnValue = this._initBrowserWindow(event, options, extraConfig)
      },
    )
  }

  private _initBrowserWindow(
    event: Electron.IpcMainEvent,
    options?: BrowserWindowConstructorOptions,
    extraConfig?: TBrowserWindowExtraConfig,
  ): string {
    const objectId = this._generateObjectId()
    const browserWindow = new MainProcessBrowserWindow(options, extraConfig)
    this._browserWindowMap.set(objectId, browserWindow)
    this._initBrowserWindowListeners(event, objectId, browserWindow)
    this._initBrowserWindowMethods(objectId, browserWindow)
    return objectId
  }

  // 监听 renderer 进程发送的事件并将它们转发给 MainProcessBrowserWindow
  private _initBrowserWindowListeners(
    event: Electron.IpcMainEvent,
    objectId: string,
    browserWindow: MainProcessBrowserWindow,
  ) {
    const sender = event.sender
    Object.values(SimpleBrowserWindowEvent).forEach((event) => {
      browserWindow.on(event, (...args: any[]) => {
        sender.send(
          buildFullBrowserWindowNamespaceChannelString(`${objectId}-${event}`),
          ...args,
        )
        // todo 此释放方式过于简单，需要进一步优化
        // 考虑renderer进程主动发起资源释放？？
        if (event === SimpleBrowserWindowEvent.CLOSED) {
          this._deinitBrowserWindow(objectId)
        }
      })
    })
  }

  // 设置方法的转发：loadURL, getURL, destroy, isDestroyed
  private _initBrowserWindowMethods(
    objectId: string,
    browserWindow: MainProcessBrowserWindow,
  ) {
    // loadURL 转发
    mainProcessRemote.handle(
      buildBrowserWindowNamespaceChannelString(`${objectId}-loadURL`),
      async (_event, url: string) => {
        await browserWindow.loadURL(url)
      },
    )

    // getURL 转发
    mainProcessRemote.handle(
      buildBrowserWindowNamespaceChannelString(`${objectId}-getURL`),
      async () => {
        return await browserWindow.getURL()
      },
    )

    // destroy 转发
    mainProcessRemote.handle(
      buildBrowserWindowNamespaceChannelString(`${objectId}-destroy`),
      async () => {
        await browserWindow.destroy()
      },
    )

    // isDestroyed 转发
    mainProcessRemote.handle(
      buildBrowserWindowNamespaceChannelString(`${objectId}-isDestroyed`),
      async () => {
        return await browserWindow.isDestroyed()
      },
    )
  }

  private _deinitBrowserWindow(objectId: string) {
    this._deinitBrowserWindowMethods(objectId)
    this._browserWindowMap.delete(objectId)
  }

  // private _deinitBrowserWindowListener(
  //   event: Electron.IpcMainEvent,
  //   objectId: string,
  //   browserWindow: MainProcessBrowserWindow,
  // ) {}

  private _deinitBrowserWindowMethods(objectId: string) {
    // loadURL 转发
    mainProcessRemote.removeHandler(
      buildBrowserWindowNamespaceChannelString(`${objectId}-loadURL`),
    )

    // getURL 转发
    mainProcessRemote.removeHandler(
      buildBrowserWindowNamespaceChannelString(`${objectId}-getURL`),
    )

    // destroy 转发
    mainProcessRemote.removeHandler(
      buildBrowserWindowNamespaceChannelString(`${objectId}-destroy`),
    )

    // isDestroyed 转发
    mainProcessRemote.removeHandler(
      buildBrowserWindowNamespaceChannelString(`${objectId}-isDestroyed`),
    )
  }

  // 生成唯一的 ObjectId
  private _generateObjectId() {
    let uuid: string = ''
    do {
      uuid = UUIDUtility.uuidv4()
      if (!this._uuidSet.has(uuid)) {
        this._uuidSet.add(uuid)
        break
      }
    } while (true)
    return uuid
  }
}

export default RendererProcessBrowserWindow_MainProcess_Handler
