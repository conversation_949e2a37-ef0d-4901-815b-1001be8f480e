/// <reference path="../impl/thunder-client-api.d.ts" />
import { getThunderClient } from '../impl/thunder-client'

export class Playback<PERSON>ecord<PERSON>elper implements ThunderClientAPI.biz.IPlaybackRecordPresenter {
    private static instance: PlaybackRecordHelper | null = null;
    private constructor() {}
    public static getInstance(): PlaybackRecordHelper {
        if (!PlaybackRecordHelper.instance) {
            if (global.PlaybackRecordHelperClientInstance) {
                PlaybackRecordHelper.instance = global.PlaybackRecordHelperClientInstance;
            } else {
                PlaybackRecordHelper.instance = new PlaybackRecordHelper();
                global.PlaybackRecordHelperClientInstance = PlaybackRecordHelper.instance;
            }
        }
        return PlaybackRecordHelper.instance!;
    }

    public async fetchFirstPage(param: ThunderClientAPI.dataStruct.common.FetchFirstPageParam)
        : Promise<ThunderClientAPI.dataStruct.common.FetchFirstPageResult> {
        return getThunderClient().getBizProvider().getPlaybackRecordPresenter().fetchFirstPage(JSON.stringify(param));
    }
    public async fetchNextPage(param: ThunderClientAPI.dataStruct.common.FetchNextPageParam)
        : Promise<ThunderClientAPI.dataStruct.common.FetchNextPageResult> {
        return getThunderClient().getBizProvider().getPlaybackRecordPresenter().fetchNextPage(JSON.stringify(param));
    }
    public async removeAllPlaybackRecords(param: ThunderClientAPI.dataStruct.dataModals.RemoveAllPlaybackRecordsParam)
        : Promise<any> {
        return getThunderClient().getBizProvider().getPlaybackRecordPresenter().removeAllPlaybackRecords(JSON.stringify(param));
    }
    public async loadPlaybackRecords(param: ThunderClientAPI.dataStruct.dataModals.LoadPlaybackRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.LoadPlaybackRecordsResult> {
        return getThunderClient().getBizProvider().getPlaybackRecordPresenter().loadPlaybackRecords(JSON.stringify(param));
    }
    public async removePlaybackRecords(param: ThunderClientAPI.dataStruct.dataModals.RemovePlaybackRecordsParam)
        : Promise<any> {
        return getThunderClient().getBizProvider().getPlaybackRecordPresenter().removePlaybackRecords(JSON.stringify(param));
    }

    
    public async getRecordsFromServer(param: {limit: number, next_page_token: string}): Promise<any> {
        return (getThunderClient().getBizProvider().getPlaybackRecordPresenter() as any).getRecordsFromServer(JSON.stringify(param));
    }
}