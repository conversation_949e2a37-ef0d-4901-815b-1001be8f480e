<script lang="ts" setup>
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import Button from '@root/common/components/ui/button/index.vue'

import { ref } from 'vue';
import { IConfirmDialogOptions } from '.';

defineOptions({
  name: 'ConfirmDialog',
})

const props = withDefaults(defineProps<IConfirmDialogOptions>(), {
  variant: 'error',
  cancelButtonText: '取消',
  confirmButtonText: '确定',
  cancelButtonVariant: 'secondary',
  confirmButtonVariant: 'default',
  leftButtonVariant: 'warning',
  leftButtonWarning: true,
  onClose: () => {},
  onConfirm: () => {},
  onLeftButtonClick: () => {},
})

const visible = ref(true)


function handleConfirm () {
  if (props.confirmButtonDisabled) return;

  props.onConfirm()
}

function handleClose () {
  if (props.cancelButtonDisabled) return;

  props.onClose()
}

function handleLeftButtonClick () {
  props.onLeftButtonClick()
}

function handleDialogUpdateOpen (isOpen: boolean) {
  visible.value = isOpen
  if (!isOpen) {
    handleClose()
  }
}

defineExpose({
  visible,
})
</script>

<template>
  <Dialog
    :open="visible"
    :title="headerTitle"
    :variant="variant"
    :modal="true"
    :draggable="false"
    :show-title-icon="false"
    :prevent-default-close="true"
    :disable-header-draggable="true"
    @update:open="handleDialogUpdateOpen"
    @close="handleClose"
  >
    <div class="confirm-dialog-content">
      <p>{{ bodyText }}</p>
    </div>

    <template #actions>
      <div class="confirm-dialog-actions">
        <Button
          size="default"
          class="left-button"
          :class="{
            'is-warning': leftButtonWarning,
            'is-visible': leftButtonText !== '',
          }"
          :variant="leftButtonVariant"
          @click="handleLeftButtonClick"
        >
          {{ leftButtonText }}
        </Button>

        <div class="right-actions">
          <Button
            size="default"
            :variant="cancelButtonVariant"
            :disabled="cancelButtonDisabled"
            @click="handleClose"
          >
            {{ cancelButtonText }}
          </Button>

          <Button
            size="default"
            :variant="confirmButtonVariant"
            :disabled="confirmButtonDisabled"
            @click="handleConfirm"
          >
            {{ confirmButtonText }}
          </Button>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<style lang="scss" scoped>
.confirm-dialog-content {
  p {
    font-size: 13px;
    line-height: 22px;
    color: var(--font-font-2);
  }
}
.confirm-dialog-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .left-button {
    visibility: hidden;

    &.is-visible {
      visibility: visible;
    }
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    button {
      min-width: 84px;
    }
  }

  .is-warning {
    color: var(--functional-error-default);
  }
}
</style>
