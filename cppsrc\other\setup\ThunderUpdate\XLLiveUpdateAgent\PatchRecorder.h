#pragma once

#include <list>
#include <string>

using std::list;

struct PatchRecoderInfo
{
	std::string PatchVersion;
	std::string PatchMD5;
};

class PatchRecorder
{
protected:
	PatchRecorder();
	~PatchRecorder();

public:
	static PatchRecorder& Instance();

	void Init(const char* ProductName, const char* ProductVersion);

	bool IsPatchInstalled(const char* PatchVersion, const char* PatchMD5);
	void AddInstalledPatch(const char* PatchVersion, const char* PatchMD5);

private:
	bool LoadFromFile();
	bool SaveToFile();

	std::string m_ProductName;
	std::string m_ProductVersion;

	std::wstring m_PatchInfoJsonFilePath;

	list<PatchRecoderInfo> m_listInstalledPatchInfo;
};
