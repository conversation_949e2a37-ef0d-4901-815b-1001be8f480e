export const IPC_REMOTE_ID = 'ThunderPanPlugin'

export const IPC_CONTEXT_NAME = 'Thunder_Pan_SDK_'

export enum IPC_API_NAME {
  // 获取文件详情
  GET_FILE_INFO = IPC_CONTEXT_NAME + 'getFileInfo',
  // 获取文件夹列表
  GET_FOLDER_LIST = IPC_CONTEXT_NAME + 'getFolderList',
  // 获取全部的文件列表
  GET_ALL_FILE_LIST = IPC_CONTEXT_NAME + 'getAllFileList',
  // 获取指定目录（parent_id）下的所有文件（平铺展示）
  GET_ALL_FLATTEN_SUB_FILES = IPC_CONTEXT_NAME + 'getAllFlattenSubFilesByParentId',
  // 批量创建云添加任务
  BATCH_CLOUD_ADD = IPC_CONTEXT_NAME + 'batchCloudAdd',
  // 获取单个云添加任务的详情（子文件）
  GET_CA_TASK_STATUS = IPC_CONTEXT_NAME + 'getTaskStatus',
  // 获取最近相关数据
  GET_EVENTS = IPC_CONTEXT_NAME + 'getEvents',
  // 上报最近数据
  REPORT_EVENT = IPC_CONTEXT_NAME + 'reportEvent',
  // 批量获取文件信息
  GET_FILES = IPC_CONTEXT_NAME + 'getFiles',
  // 按名称搜索云盘文件
  GET_ALL_FILES_IN_DIRECTORY = IPC_CONTEXT_NAME + 'getAllFilesInDirectory',
  // 按名称搜索云盘文件
  GET_SAME_FILES_IN_DIRECTORY = IPC_CONTEXT_NAME + 'getSameFilesInDirectory',
  // 获取目录列表
  SEARCH_FILE_TREE_PATH_BY_ID = IPC_CONTEXT_NAME + 'searchFileTreePathById',
  // 服务端获取目录列表
  SERVER_SEARCH_FILE_TREE_PATH_BY_ID = IPC_CONTEXT_NAME + 'server_searchFileTreePathById',
  // 获取我的分享列表
  GET_SHARE_LIST = IPC_CONTEXT_NAME + 'getShareList',
  // 获取回收站列表
  GET_TRASH_LIST = IPC_CONTEXT_NAME + 'getAllTrashList',
  // 清空回收站
  CLEAN_TRASH = IPC_CONTEXT_NAME + 'cleanTrash',
  // 获取基础信息
  GET_ABOUT = IPC_CONTEXT_NAME + 'getAbout',
  // 获取特权信息
  GET_PRIVILEGE = IPC_CONTEXT_NAME + 'getPrivilege',
  // 获取高速流量信息
  GET_FLOW_ABOUT = IPC_CONTEXT_NAME + 'getFlowAbout',
  // 获取云添加列表
  GET_URL_TASK_LIST = IPC_CONTEXT_NAME + 'getUrlTaskList',
  // 根据云添加任务 id 获取详情
  GET_URL_TASK_BY_ID = IPC_CONTEXT_NAME + 'getUrlTaskById',
  // 获取云添加任务子任务列表
  GET_URL_TASK_CHILDREN = IPC_CONTEXT_NAME + 'getUrlTaskStatusById',
  // 批量删除云添加任务
  BATCH_DELETE_URL_TASKS = IPC_CONTEXT_NAME + 'batchDeleteUrlTasks',
  // 批量获取云添加任务状态
  BATCH_GET_TASKS_STATUSES = IPC_CONTEXT_NAME + 'batchGetUrlTaskStatuses',
  // 批量将文件放入回收站
  BATCH_TRASH_FILES = IPC_CONTEXT_NAME + 'batchTrashFiles',
  // 批量将文件移出回收站
  BATCH_UNTRASH_FILES = IPC_CONTEXT_NAME + 'batchUntrashFiles',
  // 批量删除文件
  BATCH_DELETE_FILES = IPC_CONTEXT_NAME + 'batchDeleteFiles',
  // 创建文件夹
  CREATE_FOLDER = IPC_CONTEXT_NAME + 'createFolder',
  // 批量复制文件
  BATCH_COPY_FILES = IPC_CONTEXT_NAME + 'batchCopyFiles',
  // 批量移动文件
  BATCH_MOVE_FILES = IPC_CONTEXT_NAME + 'batchMoveFiles',
  // 批量更新文件
  BATCH_UPDATE_FILES = IPC_CONTEXT_NAME + 'batchUpdateFiles',
  // 批量取消分享
  BATCH_DELETE_SHARE = IPC_CONTEXT_NAME + 'batchDeleteShare',
  // 创建分享
  CREATE_SHARE = IPC_CONTEXT_NAME + 'createShare',
  // 获取全类型文件总数
  GET_ALL_CATEGORY_FILE_COUNT = IPC_CONTEXT_NAME + 'getAllCategoryFileCount',
  // 获取文件祖先（面包屑）
  GET_File_ANCESTORS = IPC_CONTEXT_NAME + 'getFileAncestors',
  // 创建文件
  CREATE_FILE = IPC_CONTEXT_NAME + 'createFile',
  // 获取指定空间目录id
  GET_FILE_BY_SPACE = IPC_CONTEXT_NAME + 'getFileBySpace',
  // 获取转存列表
  GET_RESTORE_LIST = IPC_CONTEXT_NAME + 'getRestoreList',
  // 按 id 删除指定转存记录
  DELETE_RESTORE_BY_ID = IPC_CONTEXT_NAME + 'deleteRestoreById',
  // 保险箱发送验证码
  SAFE_BOX_SEND_VERIFICATION_CODE = IPC_CONTEXT_NAME + 'sendVerificationCode',
  // 保险箱校验验证码是否正确
  SAFE_BOX_CHECK_VERIFICATION_CODE = IPC_CONTEXT_NAME + 'checkVerificationCode',

  // 获取保险箱 token
  GET_SAFE_BOX_TOKEN = IPC_CONTEXT_NAME + 'getSafeBoxToken',
  // 云添加
  ADD_URL_TO_DRIVE = IPC_CONTEXT_NAME + 'addUrlToDrive',
  // 批量云添加
  BATCH_ADD_URLS_TO_DRIVE = IPC_CONTEXT_NAME + 'batchAddUrlsToDrive',
  // 批量创建云盘下载任务
  BATCH_CREATE_DOWNLOAD_TASK = IPC_CONTEXT_NAME + 'batchCreateDownloadTaskWithFiles',
  // 开始云盘下载文件预处理
  START_DOWNLOAD_PREPROCESS = IPC_CONTEXT_NAME + 'startDownloadPreprocess',
  // 开始创建云盘下载文件预处理的下载任务
  START_CREATE_DOWNLOAD_TASK_BY_PREPROCESS = IPC_CONTEXT_NAME + 'startCreateDownloadTaskByPreprocess',
  // 打开云盘路径选择器
  OPEN_PATH_SELECTOR_DIALOG = IPC_CONTEXT_NAME + 'openPathSelectorDialog',
  // 打开云盘定位到指定文件
  OPEN_PAN_DIRECTORY = IPC_CONTEXT_NAME + 'openDirectory',
  // 校验是否初始化过保险箱
  SAFE_BOX_CHECK_HAS_INIT = IPC_CONTEXT_NAME + 'checkSafeHasInit',
  // 校验保险箱密码
  SAFE_BOX_CHECK_PASSWORD = IPC_CONTEXT_NAME + 'checkPassword',
  // 初始化保险箱密码
  SAFE_BOX_INIT_PASSWORD = IPC_CONTEXT_NAME + 'initPassword',
  // 重置保险箱密码
  SAFE_BOX_RESET_PASSWORD = IPC_CONTEXT_NAME + 'resetPassword',
  // 获取当前用户回收站特权保留天数
  GET_CURRENT_USER_PRIVILEGE_TRASH_DURATION = IPC_CONTEXT_NAME + 'getCurrentUserTrashPrivilegeDuration',
  // 获取当前用户云添加使用情况
  GET_CURRENT_USER_CLOUD_ADD_QUOTAS = IPC_CONTEXT_NAME + 'getCurrentUserCloudAddQuotas',
  // 获取当前用户云盘空间使用情况
  GET_CURRENT_USER_DRIVE_QUOTAS = IPC_CONTEXT_NAME + 'getCurrentUserDriveQuotas',
  // 新建窗口获取最近保存的目录
  RECENT_SAVE_GET_FOLDERS = IPC_CONTEXT_NAME + 'getRecentSaveFolders',
  // 新建窗口设置最近保存的目录
  RECENT_SAVE_SET_FOLDERS = IPC_CONTEXT_NAME + 'setRecentSaveFolders',
  // 新建窗口获取默认保存的目录
  RECENT_SAVE_GET_DEFAULT = IPC_CONTEXT_NAME + 'getDefaultSaveFolder',
  // 新建窗口设置默认保存的目录
  RECENT_SAVE_SET_DEFAULT = IPC_CONTEXT_NAME + 'setDefaultSaveFolder',
  // 新建窗口清空最近保存的目录
  RECENT_SAVE_CLEAR_FOLDERS = IPC_CONTEXT_NAME + 'clearRecentSaveFolders',
  // 新建窗口删除最近保存的目录
  RECENT_SAVE_DELETE_FOLDER = IPC_CONTEXT_NAME + 'deleteRecentSaveFolder',
  // 打开云盘选中云添加 tab
  OPEN_PAN_CLOUD_ADD_TAB = IPC_CONTEXT_NAME + 'openCloudAddTab',
  // 新增文件到云盘（从云盘路径选择器中新建文件夹）
  APPEND_NEW_FILES_TO_DRIVE = IPC_CONTEXT_NAME + 'appendNewFilesToDrive',
  // 消费云盘文件
  CONSUME_FILE_BY_ID = IPC_CONTEXT_NAME + 'consumeFileById',
}
