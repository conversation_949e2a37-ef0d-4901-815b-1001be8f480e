<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue'
import { BSpline } from './b-spline';
import { IHistoryTaskSpeedInfo, IHistorySpeedInfo, IHistorySpeedInfoMap } from '@/types/taskDetails'

import { TimeHelperNS } from '@root/common/helper/time-helper'
import { useTaskDetailStore } from '@/stores/taskDetail'
import * as BaseType from '@root/common/task/base'
import { useSaveHistorySpeed } from '../../saveHistorySpeed'
import { TaskCtrlOperatorNS } from '@/common/task-ctrl-operator'
import { useTaskErrorCode } from '@/stores/taskErrorCode'


const taskDetailStore = useTaskDetailStore()
const taskErrorCode = useTaskErrorCode()

const { getTaskSpeedInfo }= useSaveHistorySpeed()

const emits = defineEmits<{
  (e: 'play'):void // 播放
  (e: 'openFolder'):void // 打开文件夹
  (e: 'continueDownload'):void // 继续下载
}>()
/**
 * 速度条曲线颜色
 */
interface ISpeedCurveColor {
  gradientColorEnd: string;
  gradientColorStart: string;
  lineColorEnd: string;
  lineColorStart: string;
}
interface IHistorySpeedMap {
  [index: number]: number;
}
interface ITaskSpeedInfo {
  maxSpeed?: number;
  historySpeedMap?: IHistorySpeedMap;
  historyIncreaseSpeedMap?: IHistorySpeedMap;
  historyVipSpeedColorMap?: IHistorySpeedMap;
}
interface ITransformedData {
  xValue: number;
  yValue: number;
  x: number;
  y: number;
}
interface IDataPoint  {
  x: number
  y: number
}
interface IProps {
  // dataList: IDataPoint[];
  curveColor?: string;
  pointColor?: string;
  bk1?: string;
  bk2?: string;
  scale?: number;
  isDownload?: boolean;
  step?: number
  paddingTopPercent?: number
  paddingBottomPercent?: number
}

const speedCurveColor: ISpeedCurveColor = { // 普通速度的曲线颜色
  gradientColorEnd: 'rgba(63,133,255,.1)',
  gradientColorStart: 'rgba(63,133,255,.5)',
  lineColorEnd: '#3F85FF',
  lineColorStart: '#3F85FF'
}
const props = withDefaults(defineProps<IProps>(), {
  // dataList: () => [],
  isDownload: false,
  width: 450,
  height: 178,
  curveColor: "rgba(34, 109, 245, 0.7)",
  pointColor: "#fff",
  bk1: "rgba(34, 109, 245, 0.5)",
  bk2: "rgba(34, 109, 245, 0.05)",
  scale: Math.max(window.devicePixelRatio, 2), // 分辨率 window.webkitDevicePixelRatio
  step: 1,
  paddingTopPercent: 8,
  paddingBottomPercent: 0,
})

const width = ref(0)
const height = ref(0)
const curveTop = ref(155)
const curveLeft = ref(0)
const requireAnimation = ref<number>(-1)
const options = ref<any[]>([])
const maxValue = ref(0) // 记录最大值
const userSpeedCurveColor = ref({ // 根据登录用户判断Vip加速曲线颜色
  gradientColorEnd: 'rgba(63,133,255,.1)',
  gradientColorStart: 'rgba(63,133,255,.5)',
  lineColorEnd: '#3f85ff',
  lineColorStart: '#3f85ff'
})
const progress = ref(0) // 记录当前进度
const points = ref<IDataPoint[]>([]) // 记录当前进度的点
const lastData = ref<any>([])
const xPoint = ref<number>(0)
const yPoint = ref<number>(0)
const lastPointX = ref<number>(0)
const lastPointY = ref<number>(0)
const lastPoint = ref<number>(0)
const lastMaxValue = ref<number>(0)
const curTaskIsTryTask = ref<boolean>(false)
const historyVipSpeedColorMap = ref<any>({})
const frame = ref(10)
const historyMaxValue = ref(0) // 当前节点前的历史数据中的最大值
const maxValueIncrease = ref(0)
const canvasBox = ref<HTMLCanvasElement>()
const curveCanvas = ref<HTMLCanvasElement>()
const maxSpeed = ref<number>(0)
const downloadPeriod = ref<number>(1)
const taskSpeedInfo = ref<ITaskSpeedInfo>({})
const taskFileExist = ref<boolean>(true)
const isBtOrGroup = ref<boolean>(false)
const avgSpeed = ref<number>(0)
const downloadSpeed = ref<number>(0)
const remain = ref<string>('--:--:--')

// ===============================================================


const taskInfo = computed(() => {
  return taskDetailStore.taskInfo
})

const isStop = computed(() => {
  return taskInfo.value.taskStatus && [BaseType.TaskStatus.Stopped].includes(taskInfo.value.taskStatus)
})

const isCompleted = computed(() => {
  return taskInfo.value.taskStatus && BaseType.TaskStatus.Succeeded === taskInfo.value.taskStatus
})

const isError = computed(() => {
  return taskInfo.value.errorCode !== 0 || BaseType.TaskStatus.Failed === taskInfo.value.taskStatus
})

const failMessage = computed(() => {
  if (isError.value) {
    return taskErrorCode.getTaskErrorInfoByCode(taskInfo.value.errorCode || 0)
  }
  return ''
})

// 从data[0]开始, 画一条线
const renderPath = (ctx: any, data: any): number => {
  if (data.length === 0) return 0;
  ctx.moveTo(data[0].x, data[0].y);
  let n: number = 0;
  for (let i: number = 1; i < data.length; i++) {
    if (data[i]) {
      ctx.lineTo(data[i].x, data[i].y);
      n = i;
    }
  }
  return n;
}

// 画渐变线和渐变色填充
const drawLineAndGradient = (ctx: any, data: any, colorStart: any, colorEnd: any, gradientStart: any, gradientEnd: any, lineWidth: any, points: any): void  => {
  // console.log('>>>>>>>>>> drawLineAndGradient', {ctx,
  //           data,
  //           colorStart,
  //           colorEnd,
  //           lineWidth,
  //           gradientStart,
  //           gradientEnd,
  //           points})
  if (!data.length || !points.length) {
    curveTop.value = 20;
    return;
  }
  ctx.lineWidth = lineWidth * props.scale;
  // 绘制曲线
  ctx.beginPath();
  ctx.moveTo((points[0] || data[0]).x, (points[0] || data[0]).y);
  const n: number = renderPath(ctx, points);
  if (points[n]) {
    curveLeft.value = points[n].x / props.scale - 3;
    curveTop.value = (points[n].y) / props.scale - 4;
  } else if (points.length === 0) { // 只有一个点时, 恢复初始化值
    curveLeft.value = 0;
    curveTop.value = height.value / props.scale - 4;
  }
  const lineStroke: any = ctx.createLinearGradient(
    0,
    0,
    data[data.length - 1].x,
    0
  );
  lineStroke.addColorStop(0, colorStart);
  lineStroke.addColorStop(1, colorEnd);
  ctx.strokeStyle = lineStroke;

  ctx.stroke();
  // 渐变色填充
  // let n: number = this.renderPath(ctx, points);

  // let n: number = points.length - 1;
  ctx.lineTo(points[n].x, height.value);
  ctx.lineTo(points[0].x, height.value);
  ctx.lineTo(points[0].x, points[0].y);
  const gradient: any = ctx.createLinearGradient(0, 0, 0, height.value);
  gradient.addColorStop(0, gradientStart);
  gradient.addColorStop(1, gradientEnd);
  ctx.fillStyle = gradient;
  ctx.closePath();
  ctx.fill();
}

// 清空canvas
function clearCanvas (): void {
  const canvas: HTMLCanvasElement|undefined = curveCanvas.value;

  if (canvas && canvas.getContext) {
    const ctx: any = canvas.getContext('2d');
    ctx.clearRect(0, 0, width.value, height.value);
  }
}

const downloadSpeedSize = computed(() => {
  return formatSize(taskDetailStore.downloadSpeed)
})

const filteredData = computed(() => {
  // console.log('>>>>>>>>>>>>>>>filteredData', options.value)
  return filterData(options.value) || [];
})
/**
 * 将下载进度转换为点集
 * 数据结构如下, 0表示总速度, 1表示加速度, x, y表示坐标, xValue,yValue分别表示下载进度,下载速度,
 * {0: {0: {x:0, xValue:0, y: 191, yValue: 1204224}, 1: {x: 12.08, xValue: 1, y: 201, yValue:861789}},
 * 1: {0: {x:0,xValue:0,y:228, yValue:0}, 1: {x:12, xValue:1, y:215, yValue:421888}}}
 */
function transformedData (): any {
  // return [[{"xValue":0,"yValue":6465806,"x":0,"y": 2},{"xValue":1,"yValue":5789929,"x":7.64,"y":115.69466963542396},{"xValue":2,"yValue":4488153,"x":15.28,"y":155.78386584848226},{"xValue":3,"yValue":5183121,"x":22.92,"y":134.38179013515165},{"xValue":4,"yValue":4790937,"x":30.56,"y":146.45939897693552},{"xValue":5,"yValue":3973313,"x":38.2,"y":171.6387608368143},{"xValue":6,"yValue":4947382,"x":45.84,"y":141.64155450787794},{"xValue":7,"yValue":3981070,"x":53.48,"y":171.39987803745043},{"xValue":8,"yValue":2202323,"x":61.12,"y":226.17776416869637},{"xValue":9,"yValue":6857946,"x":68.76,"y":82.80426216756325},{"xValue":10,"yValue":5147951,"x":76.4,"y":135.46487741807377},{"xValue":11,"yValue":4740243,"x":84.04,"y":148.02055981630016},{"xValue":12,"yValue":3371188,"x":91.68,"y":190.18166473870505},{"xValue":13,"yValue":4729510,"x":99.32,"y":148.35109083158602},{"xValue":14,"yValue":4539332,"x":106.96,"y":154.2077690599502},{"xValue":15,"yValue":4800339,"x":114.6,"y":146.16985713348845},{"xValue":16,"yValue":4823369,"x":122.24,"y":145.4606305163233},{"xValue":17,"yValue":4329828,"x":129.88,"y":160.65960636792067},{"xValue":18,"yValue":3914655,"x":137.52,"y":173.44517920023904},{"xValue":19,"yValue":3680111,"x":145.16,"y":180.6681426260477},{"xValue":20,"yValue":5204441,"x":152.8,"y":133.7252242872159},{"xValue":21,"yValue":4928377,"x":160.44,"y":142.22682814483943},{"xValue":22,"yValue":4383732,"x":168.08,"y":158.9995911021079},{"xValue":23,"yValue":4772407,"x":175.72,"y":147.0300446224444},{"xValue":24,"yValue":4737972,"x":183.36,"y":148.09049701332938},{"xValue":25,"yValue":4952479,"x":191,"y":141.48458846064864},{"xValue":26,"yValue":4719757,"x":198.64,"y":148.65144199082232},{"xValue":27,"yValue":4900149,"x":206.28,"y":143.09613118215321},{"xValue":28,"yValue":2724919,"x":213.92,"y":210.08401717677194},{"xValue":29,"yValue":3990253,"x":221.56,"y":171.1170804679573},{"xValue":30,"yValue":5993205,"x":229.2,"y":109.43462839222572},{"xValue":31,"yValue":5092993,"x":236.84,"y":137.15735142702562},{"xValue":32,"yValue":5307922,"x":244.48,"y":130.53844705878066},{"xValue":33,"yValue":5205579,"x":252.12,"y":133.69017869927262},{"xValue":34,"yValue":4900085,"x":259.76,"y":143.09810211152788},{"xValue":35,"yValue":3902867,"x":267.4,"y":173.8081997544354},{"xValue":36,"yValue":5226237,"x":275.04,"y":133.05399965205606},{"xValue":37,"yValue":3875539,"x":282.68,"y":174.64978659741797},{"xValue":38,"yValue":5154898,"x":290.32,"y":135.25093919360805},{"xValue":39,"yValue":5439515,"x":297.96,"y":126.48593910252322},{"xValue":40,"yValue":4999024,"x":305.6,"y":140.05119927715103},{"xValue":41,"yValue":4836834,"x":313.24,"y":145.0459654533564},{"xValue":42,"yValue":4769742,"x":320.88,"y":147.11211535343637},{"xValue":43,"yValue":4742372,"x":328.52,"y":147.95499561882104},{"xValue":44,"yValue":4802478,"x":336.16,"y":146.1039849782945},{"xValue":45,"yValue":4878457,"x":343.8,"y":143.76415305707923},{"xValue":46,"yValue":8394976,"x":351.44,"y":35.47023753094607},{"xValue":47,"yValue":8486224,"x":359.08,"y":32.660184975015454},{"xValue":48,"yValue":1744686,"x":366.72,"y":240.27104864110586},{"xValue":49,"yValue":5089386,"x":374.36,"y":137.26843177475095},{"xValue":50,"yValue":5130787,"x":382,"y":135.99345603974214},{"xValue":51,"yValue":5391442,"x":389.64,"y":127.96638422484102},{"xValue":52,"yValue":5327185,"x":397.28,"y":129.94522811277756},{"xValue":53,"yValue":5121675,"x":404.92,"y":136.27406710946025},{"xValue":54,"yValue":3393468,"x":412.56,"y":189.4955349501493},{"xValue":55,"yValue":4634433,"x":420.2,"y":151.27906039651037},{"xValue":56,"yValue":6155554,"x":427.84,"y":104.43496568835518},{"xValue":57,"yValue":7658543,"x":435.48,"y":58.149259908660156},{"xValue":58,"yValue":868461,"x":443.12,"y":267.25507350543506},{"xValue":59,"yValue":4792869,"x":450.76,"y":146.39990154643777},{"xValue":60,"yValue":4652967,"x":458.4,"y":150.70829156791555},{"xValue":61,"yValue":4788176,"x":466.04,"y":146.54442610198947},{"xValue":62,"yValue":4645264,"x":473.68,"y":150.94551139561955},{"xValue":63,"yValue":5047828,"x":481.32,"y":138.54824244588198},{"xValue":64,"yValue":5501248,"x":488.96,"y":124.5848237418001},{"xValue":65,"yValue":8729044,"x":496.6,"y":25.18235574444518},{"xValue":66,"yValue":8006839,"x":504.24,"y":47.42321588555377},{"xValue":67,"yValue":4897617,"x":511.88,"y":143.17410607553845},{"xValue":68,"yValue":3639662,"x":519.52,"y":181.91380078660836},{"xValue":69,"yValue":5060805,"x":527.16,"y":138.14860571939687},{"xValue":70,"yValue":4498851,"x":534.8,"y":155.4544126851982},{"xValue":71,"yValue":4608133,"x":542.44,"y":152.08898918641236},{"xValue":72,"yValue":4606673,"x":550.08,"y":152.13395101277192},{"xValue":73,"yValue":3648146,"x":557.72,"y":181.65252946137915},{"xValue":74,"yValue":5009437,"x":565.36,"y":139.7305229087385},{"xValue":75,"yValue":3548825,"x":573,"y":184.71119628046105},{"xValue":76,"yValue":4894421,"x":580.64,"y":143.27252936118586},{"xValue":77,"yValue":7884880,"x":588.28,"y":51.17903737938095},{"xValue":78,"yValue":6379510,"x":595.92,"y":97.53806789096784},{"xValue":79,"yValue":5658360,"x":603.56,"y":119.74643849316591},{"xValue":80,"yValue":4038418,"x":611.2,"y":169.63380213466343},{"xValue":81,"yValue":4706530,"x":618.84,"y":149.05877766017718},{"xValue":82,"yValue":5775855,"x":626.48,"y":116.12808932322172},{"xValue":83,"yValue":4300619,"x":634.12,"y":161.55912005705554},{"xValue":84,"yValue":8258083,"x":641.76,"y":39.685963076043045},{"xValue":85,"yValue":7375436,"x":649.4,"y":66.86775838481144},{"xValue":86,"yValue":6064253,"x":657.04,"y":107.24665042017419},{"xValue":87,"yValue":8273885,"x":664.68,"y":39.199328295129334},{"xValue":88,"yValue":5213846,"x":672.32,"y":133.43559005645437},{"xValue":89,"yValue":7822366,"x":679.96,"y":53.10420423762932},{"xValue":90,"yValue":8172230,"x":687.6,"y":42.3298724448436},{"xValue":91,"yValue":8783024,"x":695.24,"y":23.52},{"xValue":92,"yValue":6622361,"x":702.88,"y":90.05928399148176},{"xValue":93,"yValue":0,"x":710.52,"y":294},{"xValue":94,"yValue":6622962,"x":718.16,"y":90.04077573282277},{"xValue":95,"yValue":2969202,"x":725.8,"y":202.56113373252765},{"xValue":96,"yValue":4209515,"x":733.44,"y":164.36473802189315},{"xValue":97,"yValue":4802550,"x":741.08,"y":146.101767682748},{"xValue":98,"yValue":4882951,"x":748.72,"y":143.62575686005187},{"xValue":99,"yValue":2552908,"x":756.36,"y":215.38122862467418},{"xValue":100,"yValue":69614,"x":764,"y":291.85618316424956}]]
  return filteredData.value.map((data: any) => transformData(data));
}
// 根据下载速度信息, 生成坐标点
function transformData (obj: any = {}): any {
  return Object.keys(obj)
    .map((item: any) => parseInt(item, 10))
    .sort((a: number, b: number) => a - b)
    .map((key: number) => {
      return {
        xValue: key,
        yValue: parseInt(obj[key], 10),
        x: width.value * key / 100,
        y:
          height.value * props.paddingTopPercent / 100 +
          height.value *
            (100 - props.paddingTopPercent - props.paddingBottomPercent) /
            100 *
            (1 - parseInt(obj[key], 10) / (maxValue.value || 1))
      };
    });
}
// 计算最大值
function getMaxValue (datas: any = []): number {
  let maxValue: number = 0;
  datas.forEach((data: any) => {
    maxValue = Math.max(maxValue, parseInt(data, 10));
  });
  return maxValue;
}

// 根据step 过滤一些点(step=1时没有用)
function filterData (lines: any): any {
  return lines.map((lineOption: any) => {
    const data: any = {};
    Object.keys(lineOption.data).forEach((percent: any, i: any, arr: any) => {
      if (percent % props.step === 0 || percent === arr.length) {
        data[percent] = lineOption.data[percent];
      }
    });
    return data;
  });
}

// 根据speedmap的值, 使用bspline算法生成一条线的点集
function bSplineRender (data: ITransformedData[]): {x: number, y: number }[] {
  const spline: BSpline = new BSpline({
    degree: 3,
    points: data,
    tessellation: Math.round(width.value * progress.value / 100 * 1.5)
  });
  // console.log('>>>>>>>>>>>>>>>>>>> spline', spline)
  return spline.curvePoints.map(([x, y]: any) => Object.freeze({
    x,
    y
  }));
}

// 初始化设置canvas
function initCanvas(): void {
  const canvas: HTMLCanvasElement|undefined = curveCanvas.value
  if (!canvasBox.value) { return }
  width.value = Math.floor(canvasBox.value.clientWidth) * props.scale
  // 40表示底部的下载进度等位置的高度
  height.value = Math.floor(canvasBox.value.clientHeight) * props.scale

  canvas && canvas.setAttribute('width', String(width.value))
  canvas && canvas.setAttribute('height', String(height.value))
  userSpeedCurveColor.value = speedCurveColor
  // if (props.curUserId === '') {
  //   this.vipInfo = {}
  // } else {
  //   this.vipInfo = LoginHelperNS.loginHelper.getVipInfo();
  //   if (this.vipInfo.isVip === '0') {
  //     return;
  //   }
  //   if (this.vipInfo.vasType === '3' || this.vipInfo.vasType === '2') {
  //     this.userSpeedCurveColor = this.whiteVipSpeedCurveColor;
  //   } else if (this.vipInfo.vasType === '5') {// 超级会员
  //     this.userSpeedCurveColor = this.superVipSpeedCurveColor;
  //   }
  // }
}

function draw (needAnimation: boolean = true): void {
  const canvas: HTMLCanvasElement|undefined = curveCanvas.value;
  clearCanvas();
  cancelAnimationFrame(requireAnimation.value);
  // console.log('draw canvas: ', JSON.stringify(transformedData()));
  if (width.value === 0) {
    initCanvas();
  }
  if (canvas && canvas.getContext) {
    const ctx: any = canvas.getContext('2d');
    options.value.forEach((item: any, i: number) => {
      const data: any = transformedData()[i];
      // console.log('>>>>>>>>>>>>>> data', data)
      let tempPoints: any = bSplineRender(data);
      // console.log('>>>>>>>>>>>>>>>>> tempPoints', tempPoints)
      points.value = tempPoints;
      const dataEnd = data ? data[data.length - 1] : {}
      if (!needAnimation) { // 切换任务的时候初始化
        lastData.value = data;
        lastMaxValue.value = maxValue.value;
        lastPoint.value = dataEnd.xValue || 0;
        lastPointX.value = dataEnd.x || 0;
        lastPointY.value = dataEnd.y || 0;
      } else { // 计算是否出现进度回退, 如果出现了进度回退, 此时不再渲染动画并重新初始化
        if (lastPoint.value > (dataEnd.xValue || 0)) {
          needAnimation = true;
          lastData.value = data;
          lastMaxValue.value = maxValue.value;
          lastPoint.value = dataEnd.xValue || 0;
          lastPointX.value = dataEnd.x || 0;
          lastPointY.value = dataEnd.y || 0;
        }
      }
      ctx.setLineDash([]);
      // 正常绘制
      if (!curTaskIsTryTask.value && !needAnimation) { // 只有不需要动画时才使用draw渲染, 否则都依赖drawpoints渲染
        drawLineAndGradient(
          ctx,
          data,
          userSpeedCurveColor.value.lineColorStart,
          userSpeedCurveColor.value.lineColorEnd,
          userSpeedCurveColor.value.gradientColorStart,
          userSpeedCurveColor.value.gradientColorEnd,
          1.5,
          tempPoints
        );
      } else if (!needAnimation) {
        // 会员试用速度曲线绘制 只绘制一条线
        if (i === 0) {
          const changePoints: any = [];
          const typeArr: any = [];
          const typeColor: any = {
            0: {
              gradientColorEnd: 'rgba(63,133,255,.1)',
              gradientColorStart: 'rgba(63,133,255,.5)',
              lineColorEnd: '#3F85FF',
              lineColorStart: '#3F85FF'
            },
            1: {
              gradientColorEnd: 'rgba(235,121,113,.1)',
              gradientColorStart: 'rgba(240,134,127,.5)',
              lineColorEnd: '#eb7971',
              lineColorStart: '#eb7971'
            },
            2: {
              gradientColorEnd: 'rgba(235,176,73,.1)',
              gradientColorStart: 'rgba(235,176,73,.5)',
              lineColorEnd: '#ebb049',
              lineColorStart: '#ebb049'
            }
          };
          const data: any = transformedData()[0];
          const tempPoints: any = bSplineRender(data);

          // Object.keys(historyVipSpeedColorMap.value || {}).forEach((item: any) => {
          //   let vipType: any = historyVipSpeedColorMap.value[item] || 0;
          //   if (this.vipInfo.isVip === '1' && vipType === 0) {
          //     vipType = 1;
          //   }
          //   if (typeArr[typeArr.length - 1] !== vipType) {
          //     const transfromItem: any = data.find((item: any) => {
          //       return item.xValue === + item;
          //     });
          //     if (transfromItem) {
          //       changePoints.push((transfromItem || { x: 0 })['x']);
          //       typeArr.push(vipType);
          //     }
          //   }
          // });
          for (let i: number = 0 ; i < changePoints.length; i++) {
            let partPoints: any = [];
            if (i === changePoints.length - 1) {
              partPoints = tempPoints;
            } else {
              const pointIndex: any = tempPoints.findIndex((item: any) => {
                return item.x >= changePoints[i + 1];
              });
              partPoints = tempPoints.splice(0, pointIndex - 1);
              partPoints.push(tempPoints[0]);
            }

            ctx.setLineDash([]);
            drawLineAndGradient(
              ctx,
              data,
              typeColor[typeArr[i]].lineColorStart,
              typeColor[typeArr[i]].lineColorEnd,
              typeColor[typeArr[i]].gradientColorStart,
              typeColor[typeArr[i]].gradientColorEnd,
              2,
              partPoints
            );
          }
        }
      }
    });
    if (needAnimation) {
      requireAnimation.value = requestAnimationFrame(() => drawPoints(ctx, 0));
    }
  }
}

/**
 * 以动画绘制后面部分的曲线
 */
function drawPoints (ctx: any, num: number): void {
  if (num >= 60) {
    return ;
  }
  if (num % (60 / frame.value) !== 0) {
    num += 1;
    requireAnimation.value = requestAnimationFrame(() => drawPoints(ctx, num));
    return;
  } else if (num === 59) {
    maxValue.value = lastMaxValue.value;
  }
  options.value.forEach((item: any, i: any) => {
    // 分次更新maxValue, lastMaxValue 不小于historyMaxValue
    if (num === 0) {
      maxValue.value = lastMaxValue.value;
      const allDatas: any = filteredData.value[0] ? []
        .concat(Object.values(Object.values(filteredData.value[0]))) : [];
      historyMaxValue.value = getMaxValue(allDatas.slice(0, allDatas.length - 1));
      const value: number = allDatas[allDatas.length - 1]; // 当前下载速度
      if (value <= historyMaxValue.value) {
        if (lastMaxValue.value > historyMaxValue.value) { // 变小, 从lastMaxValue 变动到historyMaxValue
          maxValueIncrease.value = (historyMaxValue.value - lastMaxValue.value) / frame.value;
        } else {
          maxValueIncrease.value = 0;
        }
      } else if (value > lastMaxValue.value) { // 变大, 从lastMaxValue 变动到value
        maxValueIncrease.value = (value - lastMaxValue.value) / frame.value;
      }
      lastMaxValue.value = getMaxValue(allDatas);
    }
    maxValue.value += maxValueIncrease.value;
    // let data: any = this.transformedData()[i];
    const getFilteredData: any = Object.assign({}, filteredData.value[0]);
    getFilteredData[progress.value] = Math.min(getFilteredData[progress.value], maxValue.value);
    const data: any = transformData(getFilteredData);

    if (data.length === 0) {
      return;
    }
    const n: number = data.length - 1;
    // console.warn('#### data1: ', JSON.stringify(data[n]));
    // 动画开始时, 计算最后一个点坐标的增量, 用于动画
    if (num === 0) {
      lastData.value = data.slice(0, n);
      if (lastPoint.value === data[n].xValue || n === 0) { // 进度不变或者只有一个点
        xPoint.value = 0;
        lastData.value[n] = { x: lastPointX.value, y: lastPointY.value }; // 上次渲染的最后一个点
        yPoint.value = (data[n].y - lastPointY.value) / frame.value;
      } else {
        xPoint.value = (data[n].x - data[n - 1].x) / frame.value;
        yPoint.value = (data[n].y - data[n - 1].y) / frame.value;
        lastData.value[n] = { xValue: data[n - 1].xValue, yValue: data[n - 1].yValue, x: data[n - 1].x, y: data[n - 1].y }; // 取最后第二个点
      }
      lastPoint.value = data[n].xValue;
      lastPointX.value = data[n].x;
      lastPointY.value = data[n].y;
    } else {
      // 重新赋值data, 用于前面的点的上下移动, 否则前面的点会出现上下跳动
      data[n].x = lastData.value[n].x;
      data[n].y = lastData.value[n].y;
      lastData.value = data;
    }
    lastData.value[n].x = lastData.value[n].x + xPoint.value;
    lastData.value[n].y = lastData.value[n].y + yPoint.value;
    const tempPoints: any = bSplineRender(lastData.value);
    // ctx.setLineDash([]); 绘制虚线, 废弃
    clearCanvas();
    // 当前任务 不是试用任务
    if (curTaskIsTryTask.value === false) {
      drawLineAndGradient(
        ctx,
        lastData.value,
        userSpeedCurveColor.value.lineColorStart,
        userSpeedCurveColor.value.lineColorEnd,
        userSpeedCurveColor.value.gradientColorStart,
        userSpeedCurveColor.value.gradientColorEnd,
        2,
        tempPoints
      );
    } else {
      // 试用曲线只画一条线
      if (i === 0) {
        // 会员试用速度曲线绘制
        const changePoints: any = [];
        const typeArr: any = [];
        const typeColor: any = {
          0: {
            gradientColorEnd: 'rgba(63,133,255,.1)',
            gradientColorStart: 'rgba(63,133,255,.5)',
            lineColorEnd: '#3F85FF',
            lineColorStart: '#3F85FF'
          },
          1: {
            gradientColorEnd: 'rgba(235,121,113,.1)',
            gradientColorStart: 'rgba(240,134,127,.5)',
            lineColorEnd: '#eb7971',
            lineColorStart: '#eb7971'
          },
          2: {
            gradientColorEnd: 'rgba(235,176,73,.1)',
            gradientColorStart: 'rgba(235,176,73,.5)',
            lineColorEnd: '#ebb049',
            lineColorStart: '#ebb049'
          }
        };
        // Object.keys(this.historyVipSpeedColorMap || {}).forEach((progress: any) => {
        //   let vipType: any = this.historyVipSpeedColorMap[progress] || 0;
        //   if (this.vipInfo.isVip === '1' && vipType === 0) {
        //     vipType = 1;
        //   }
        //   if (typeArr[typeArr.length - 1] !== vipType) {
        //     const transfromItem: any = lastData.value.find((item: any) => {
        //       return item.xValue === + progress;
        //     });
        //     if (transfromItem) {
        //       changePoints.push((transfromItem || { x: 0 })['x']);
        //       typeArr.push(vipType);
        //     }
        //   }
        // });
        for (let index: number = 0 ; index < changePoints.length; index++) {
          let partPoints: any = [];
          if (index === changePoints.length - 1) {
            partPoints = tempPoints;
          } else {
            const pointIndex: any = tempPoints.findIndex((item: any) => {
              return item.x >= changePoints[index + 1];
            });
            partPoints = tempPoints.splice(0, pointIndex - 1);
            partPoints.push(tempPoints[0]);
          }
          drawLineAndGradient(
            ctx,
            lastData.value,
            typeColor[typeArr[index]].lineColorStart,
            typeColor[typeArr[index]].lineColorEnd,
            typeColor[typeArr[index]].gradientColorStart,
            typeColor[typeArr[index]].gradientColorEnd,
            2,
            partPoints
          );
        }
      }
    }
  });
  num += 1;
  requireAnimation.value = requestAnimationFrame(() => drawPoints(ctx, num));

}

function formatSize(size: number, fractionDigits?: number, addSpace: boolean = false): string {
  if (fractionDigits === 0) {
    //
  } else {
    fractionDigits = fractionDigits ? fractionDigits : 2;
  }
  let ret: string = '0B';
  if (typeof size === 'number' && size > 0) {
    const subFIx: string[] = ['B', 'KB', 'MB', 'GB', 'TB'];
    let fixIndex: number = 0;
    let remain: number = size;
    while (remain >= 1000) {
      if (fixIndex >= 4) {
        break;
      }
      remain = remain / 1024;
      fixIndex += 1;
    }

    if (String(remain).indexOf('.') === -1) {
      ret = remain + (addSpace ? ' ' : '') + subFIx[fixIndex];
    } else {
      const sizeStr: string = remain.toFixed(fractionDigits);
      ret = sizeStr + (addSpace ? ' ' : '') + subFIx[fixIndex];
    }
  }

  return ret;
}

// 收到任务更新事件时, 更新下载任务信息
function updateDownloadInfo (): void {
  const downloadSize = taskDetailStore.taskInfo.downloadSize || 0;
  maxSpeed.value = Math.max(maxSpeed.value, taskDetailStore.downloadSpeed);
  downloadPeriod.value = taskDetailStore.taskInfo.downloadPeriod || 1
  avgSpeed.value = Math.round(downloadSize / downloadPeriod.value)
  
  if (avgSpeed.value > maxSpeed.value) { // 修正平均速度大于峰值速度的问题
    maxSpeed.value = avgSpeed.value;
  }
  downloadSpeed.value = taskDetailStore.taskInfo.downloadSpeed || 0;
  const taskStatus = taskDetailStore.taskStatus;
  if (taskStatus === BaseType.TaskStatus.Succeeded || taskStatus === BaseType.TaskStatus.Seeding) {
    progress.value = 100;
  } else {
    const fileSize: number = taskDetailStore.taskInfo.fileSize || 0;
    if (fileSize !== 0) {
      progress.value = Math.floor((downloadSize / fileSize) * 100)
      // progress.value = Math.floor(this.progress);
      if (progress.value > 100) {
        progress.value = 100;
      } else if (progress.value < 0) {
        progress.value = 0;
      }
    }
    if (downloadSpeed.value > 0 && fileSize > 0) { // 计算剩余时间
      let ret: string = '--:--:--';
      const remainSize: number = fileSize - downloadSize;
      if (remainSize > 0) {
        const seconds: number = remainSize / downloadSpeed.value;
        ret = TimeHelperNS.formatSeconds(seconds);
      }
      remain.value = ret;
    }
  }
  if (TaskCtrlOperatorNS.isNullOrUndefined(taskSpeedInfo.value) || !taskSpeedInfo.value.historySpeedMap) { // 在少数情况中会出现taskSpeedInfo未初始化就尝试设置的情况
    return
  }
  if (progress.value === 100) { // 进度为100时取第一个值
    if (!taskSpeedInfo.value.historySpeedMap[progress.value]) {
      taskSpeedInfo.value.historySpeedMap[progress.value] = taskDetailStore.downloadSpeed;
    }
  } else {
    taskSpeedInfo.value.historySpeedMap[progress.value] = taskDetailStore.downloadSpeed;
  }
  // 在开始下载时, 如果待下载的任务获取到的filesize出现从小到大的变化时, 会出现在一开始时计算出一个较大的百分比的现象, 此时需要删除错误的点
  const keys: string[] = Object.keys(taskSpeedInfo.value.historySpeedMap);
  for (let i: number = keys.length - 1; i >= 0; i--) {
    if (Number(keys[i]) > progress.value) {
      delete taskSpeedInfo.value.historySpeedMap[Number(keys[i])];
    } else {
      break;
    }
  }
  updateOptions();
}

function updateOptions(): void {
  options.value.splice(0, 1, {
    speedCurveColor: speedCurveColor,
    data: taskSpeedInfo.value.historySpeedMap
  })
}

async function isDestroyed(): Promise<void> {
  // 对于正在下载完成切换到已完成，应该是默认存在的，就不去监听curTaskStatus改变了
  let isExist: boolean = true;
  if (taskDetailStore.taskStatus === BaseType.TaskStatus.Seeding || taskDetailStore.taskStatus === BaseType.TaskStatus.Succeeded) {
    isExist = await TaskCtrlOperatorNS.isExistTaskFile(taskDetailStore.taskInfo);
  }
  taskFileExist.value = isExist;
}

async function onTaskChange (): Promise<void> {
  // 触发任务重新下载时, 先重置一下速度曲线点
  curveLeft.value = 0;
  curveTop.value = 202;
  lastPoint.value = 0;
  const curTask = taskDetailStore.taskInfo;
  if (!curTask) {
    return;
  }
  isDestroyed() // 校验文件
  
  // historyMap中如果是bt任务已经完成过, 然后选中新文件重新下载的话, 会存在100的点
  const downloadSize: number = curTask.downloadSize || 0
  const fileSize: number = curTask.fileSize || 0
  const taskStatus: BaseType.TaskStatus = curTask.taskStatus || 0
  const taskType: BaseType.TaskType = curTask.taskType || 0
  let progress: number = 0;
  if (fileSize !== 0 && downloadSize !== 0) {
    if (
      taskStatus === BaseType.TaskStatus.Succeeded ||
      taskStatus === BaseType.TaskStatus.Seeding ||
      downloadSize >= fileSize
    ) {
      progress = 100;
    } else {
      progress = Math.floor(downloadSize / fileSize * 100);
    }
  }

  taskSpeedInfo.value = await getHistorySpeedInfo(curTask.taskId, progress);
  // await this.getHistoryVipSpeedColor();
  maxSpeed.value = taskSpeedInfo.value.maxSpeed || 0
  isBtOrGroup.value = taskType === BaseType.TaskType.Bt || taskType === BaseType.TaskType.Group;
  updateDownloadInfo();
  const allDatas: any = filteredData.value[0] ? []
    .concat(Object.values(Object.values(filteredData.value[0]))) : [];
  maxValue.value = getMaxValue(allDatas);
  draw(false); // 切换任务时渲染不需要动画
}

async function getHistorySpeedInfo(taskId: number, curProgress: number): Promise<ITaskSpeedInfo> {
  let historySpeedInfo: ITaskSpeedInfo = {
    maxSpeed: 0,
    historySpeedMap: { 0: 1 }, // 占位第一个点, 避免出现保存失败时的问题
    historyIncreaseSpeedMap: { 0: 0 }
  };
  const speedInfo: IHistoryTaskSpeedInfo | undefined = await getTaskSpeedInfo(taskId);
  do {
    if (!speedInfo) {
      break;
    }

    if (!speedInfo.maxSpeed || speedInfo.maxSpeed < 0) {
      break;
    }
    historySpeedInfo.maxSpeed = speedInfo.maxSpeed;
    const speedInfoMap: IHistorySpeedInfoMap|undefined = speedInfo.speedInfoMap;
    if (!speedInfoMap) {
      break;
    }
    if (curProgress === undefined) {
      curProgress = 0;
    }
    curProgress = parseInt(curProgress.toString(), 10);
    if (!curProgress || curProgress < 0) {
      curProgress = 0;
    }

    if (curProgress > 100) {
      curProgress = 100;
    }

    const downloadSpeeds: IHistorySpeedMap = { 0: 0 }; // 占位第一个点, 避免出现保存失败时的问题
    const addSpeeds: IHistorySpeedMap = { 0: 0 };
    const vipSpeedColors: IHistorySpeedMap = {};

    for (const progress in speedInfoMap) {
      if (speedInfoMap[progress] !== undefined) {
        const progressInt: number = parseInt(progress, 10);
        if (progressInt <= curProgress) {
          const speedInfo: IHistorySpeedInfo = speedInfoMap[progress];
          downloadSpeeds[progressInt] = speedInfo.downloadSpeed || 0;
          addSpeeds[progressInt] = speedInfo.addSpeed || 0;
          vipSpeedColors[progressInt] = speedInfo.vipSpeedColor || 0;
        }
      }
    }

    historySpeedInfo.historySpeedMap = JSON.parse(JSON.stringify(downloadSpeeds)) || {};
    historySpeedInfo.historyIncreaseSpeedMap = addSpeeds || {};
    historySpeedInfo.historyVipSpeedColorMap = vipSpeedColors || {};
  } while (0);
  return JSON.parse(JSON.stringify(historySpeedInfo))
}

const onResize = () => {
  console.log('>>>>>>>>>>>> 执行')
  lastPoint.value = 0;
  initCanvas();
  draw(false);
}

function unInit(): void {
  window.removeEventListener('resize', onResize);
  taskSpeedInfo.value = {};
}

function handleContinueDownload() {
  emits('continueDownload')
}

function handleOpenFolder() {
  emits('openFolder')
}

function handlePlay() {
  emits('play')
}

watch(() => taskDetailStore.taskInfo, (val) => {
  console.log('>>>>>>>>>>>>>>>> 监听', val)
  updateDownloadInfo();
  nextTick(() => {
    onTaskChange()
    draw()
  })
}, { deep: true } )

onMounted(() => {
  unInit()
  updateDownloadInfo()
  initCanvas()
  window.addEventListener('resize', onResize)
  curveTop.value = height.value / props.scale - 10
  onTaskChange()
})

onUnmounted(() => {
  unInit()
})


</script>



<template>
  <div ref="canvasBox" class="curve-chart">
    <canvas
      ref="curveCanvas"
      :style="`transform: scale(${1/scale});`"
      class="curve-canvas"
    ></canvas>
    <div v-if="isDownload" class="line" :style="{top: `${curveTop}px`, left: `${curveLeft}px`}"></div>
    <div v-if="isDownload" class="point" :style="{top: `${curveTop}px`, left: `${curveLeft}px`}"></div>
    <div v-if="isDownload" class="number" :style="{top: `${curveTop}px`, right: '2px'}">
      {{ downloadSpeedSize }}/s
    </div>
    <div v-if="!isDownload" class="curve-btn">
      <div class="curve-btn__error" v-if="isError && failMessage">
        <i class="xl-icon-general-exclamation-circle-l"></i>
        <div class="curve-btn__text">{{ failMessage }}</div>
      </div>
      <div v-else-if="!taskFileExist && isCompleted" class="curve-btn__error">
        <i class="xl-icon-general-exclamation-circle-l"></i>
        <div class="curve-btn__text">文件已删除</div>
      </div>
      <!-- <Button v-if="failMessage" variant="primary" size="sm">
        <i class="xl-icon-down-hint"></i>{{ failMessage }}
      </Button> -->
      <Button v-else-if="isStop" variant="primary" size="sm" @click="handleContinueDownload">
        <i class="xl-icon-download"></i>继续下载
      </Button>
      <Button v-else-if="taskDetailStore.isSupportPlay && isCompleted" variant="primary" size="sm" @click="handlePlay">
        <i class="xl-icon-general-play-m"></i>播放
      </Button>
      <Button v-else-if="!taskDetailStore.isSupportPlay && isCompleted" variant="primary" size="sm" @click="handleOpenFolder">
        <i class="xl-icon-general-openfolder-m"></i>打开文件夹
      </Button>
    </div>
  </div>
</template>


<style lang="scss" scoped>
.curve-chart {
  height: calc(100% - 12px);
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
}
.curve-canvas {
  transform-origin: left bottom;
  bottom: 0;
  left: 0;
  position: absolute!important;
}
.line {
  position: absolute;
  right: 0;
  height: 1px;
  margin: 3px 0 0 8px;
  border-bottom: var(--border-border-primary) 1px dashed;
}
.point {
  position: absolute;
  width: 7px;
  height: 7px;
  background: var(--border-border-primary);
  border-radius: 50%;
}
.number {
  color: var(--font-font-3);
  font-size: 12px;
  line-height: 20px;
  position: absolute;
  right: 0;
  display: flex;
  align-items: center;
  text-align: right;
  margin-top: -20px;
  white-space: nowrap;
  overflow: hidden;
}
.curve-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
.curve-btn__error {
  display: flex;
  align-items: center;
  color: var(--functional-error-default);
  font-weight: 400;
  font-size: 12px;
  background: var(--background-background-elevated);
  height: 40;
  border-radius: 90px;
  box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.3);
  padding: 0 20px;
}
.curve-btn__text {
  margin-left: 5px;
}
</style>
