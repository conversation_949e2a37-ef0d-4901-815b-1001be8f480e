import path from 'node:path'
import { ThunderUtil } from '@root/player-plugin/entrys/renderer/utils/thunder-util'
import { FileSystemAWNS } from '@root/common/fs-utilities'
import { extractFileNameFromUrl } from '@root/modal-renderer/src/utils.help'
import { MAX_MAGNET_FILE_COUNT } from '@root/modal-renderer/src/utils/constants'
import type { TaskErrorType, TaskError, CreateTaskErrorOptions } from '@root/modal-renderer/types/new-task.type'

/**
 * 获取torrent文件保存路径并确保目录存在
 * @param torrentTaskName torrent文件名
 * @param fallbackPath 回退路径（可选）
 * @returns Promise<string> 返回完整的torrent文件路径
 */
export async function getTorrentFilePath(
  torrentTaskName: string, 
  fallbackPath?: string
): Promise<string> {
  try {
    // 1. 获取系统目录
    const systemDirectory = await ThunderUtil.getXmpTempPath()
    console.log('[NewTaskUtil] 获取系统目录:', systemDirectory)

    // 2. 构建torrent文件保存路径
    const torrentDir = path.join(systemDirectory, 'xunlei', 'torrents')
    const torrentFilePath = path.join(torrentDir, torrentTaskName)

    console.log('[NewTaskUtil] torrent文件路径:', torrentFilePath)

    // 3. 检查目录是否存在，不存在则创建
    const dirExists = await FileSystemAWNS.dirExistsAW(torrentDir)
    if (!dirExists) {
      console.log('[NewTaskUtil] torrent目录不存在，开始创建:', torrentDir)
      const createResult = await FileSystemAWNS.mkdirsAW(torrentDir)
      if (createResult) {
        console.log('[NewTaskUtil] torrent目录创建成功:', torrentDir)
      } else {
        console.error('[NewTaskUtil] torrent目录创建失败:', torrentDir)
        // 创建失败时返回空字符串
        return ''
      }
    } else {
      console.log('[NewTaskUtil] torrent目录已存在:', torrentDir)
    }

    return torrentFilePath
  } catch (error) {
    console.error('[NewTaskUtil] 获取torrent文件路径时出错:', error)
    // 出错时返回空字符串
    return ''
  }
}

/**
 * 创建任务错误的工厂函数
 * @param type 错误类型
 * @param message 错误消息
 * @param options 可选的错误选项
 * @returns TaskError 任务错误对象
 */
export function createTaskError(
  type: TaskErrorType,
  message: string,
  options?: CreateTaskErrorOptions
): TaskError {
  const error = new Error(message) as TaskError
  error.type = type
  if (options) {
    error.taskType = options.taskType
    error.taskId = options.taskId
    error.url = options.url
  }
  return error
}

/**
 * 获取任务的显示名称
 * 优先级：自定义任务名称 > optionsExtData中的fileName > dataMap中的文件名/title > URL提取的文件名
 * @param url 任务URL
 * @param dataInfo 可选的任务数据对象
 * @param customTaskName 自定义任务名称（可选）
 * @param optionsExtDataInfo optionsExtData中的信息（可选）
 * @param dataMapInfo dataMap中的信息（可选）
 * @returns 最终的任务名称
 */
export function getTaskDisplayName(
  url: string, 
  dataInfo?: any,
  customTaskName?: string,
  optionsExtDataInfo?: any,
  dataMapInfo?: any
): string {
  // 1. 优先使用自定义任务名称
  if (customTaskName) {
    console.log(`[NewTaskUtil] 使用自定义任务名称: ${customTaskName}`)
    return customTaskName
  }

  // 2. 优先使用 optionsExtData 中的 fileName
  if (optionsExtDataInfo && optionsExtDataInfo.fileName) {
    console.log(`[NewTaskUtil] 使用 optionsExtData 中的文件名: ${optionsExtDataInfo.fileName}`)
    return optionsExtDataInfo.fileName
  }

  // 3. 使用传入的 dataInfo 或从 dataMap 中获取
  const taskDataInfo = dataInfo || dataMapInfo
  if (taskDataInfo) {
    // 磁力链任务的名称在title里，其他任务的在fileName里
    const fileName = taskDataInfo.fileName || taskDataInfo.title
    if (fileName) {
      console.log(`[NewTaskUtil] 使用任务数据中的文件名: ${fileName}`)
      return fileName
    }
  }

  // 4. 最后使用从 URL 提取的文件名
  const urlFileName = extractFileNameFromUrl(url) || url.split('/').pop() || 'unknown-file'
  console.log(`[NewTaskUtil] 使用 URL 提取的文件名: ${urlFileName}`)
  return urlFileName
}

/**
 * 提取 P2SP 特定字段
 * 这是一个纯函数，便于测试和复用
 * @param parseResult 解析结果
 * @param remoteData 远程数据
 * @returns P2SP字段对象
 */
export function extractP2spFields(parseResult: any, remoteData: any) {
  console.log('[NewTaskUtil] remoteData', remoteData)
  return {
    // 从 parseResult 中提取字段（基于 P2spUrlParseResult 接口）
    fullPath: parseResult?.fullPath || '',
    hostName: parseResult?.hostName || '',
    password: parseResult?.password || '',
    port: parseResult?.port || 443,
    schema: parseResult?.schema || 'https://',
    userName: parseResult?.userName || '',

    // 从远程数据中提取额外信息
    ...(remoteData.fileSize && { fileSize: remoteData.fileSize }),
    ...(remoteData.gcid && { gcid: remoteData.gcid }),
    ...(remoteData.result && { remoteResult: remoteData.result }),
  }
}

/**
 * 显示超出限制提醒
 * @param alertDialog 弹窗实例
 * @param maxCount 最大数量限制
 */
export function showOverLimitAlert(alertDialog: any, maxCount: number = MAX_MAGNET_FILE_COUNT) {
  alertDialog.open({
    title: '单次任务上限提醒',
    content: `单次最多添加 ${maxCount} 个任务，超出部分请分批提交`,
    confirmText: '我知道了',
    showCancel: false,
    showCloseButton: true,
    variant: 'thunder',
    showTitleIcon: true,
    alwaysOnTop: true,
  })
}

/**
 * 检查磁力链文件数量是否超出限制
 * @param magnetCount 磁力链数量
 * @param maxCount 最大数量限制
 * @param alertDialog 弹窗实例
 * @returns boolean 返回true表示文件数量在限制内，false表示超出限制
 */
export function checkMagnetFileCountLimit(
  magnetCount: number, 
  maxCount: number = MAX_MAGNET_FILE_COUNT,
  alertDialog?: any
): boolean {
  try {
    console.log('[NewTaskUtil] 开始检查磁力链文件数量限制')

    // 检查是否超出最大文件数量的限制
    if (magnetCount > maxCount) {
      console.warn(
        `[NewTaskUtil] 磁力链文件数量超出限制: ${magnetCount} > ${maxCount}`
      )
      
      if (alertDialog) {
        showOverLimitAlert(alertDialog, maxCount)
      }

      return false
    }

    console.log('[NewTaskUtil] 磁力链文件数量在限制内，可以继续操作')
    return true
  } catch (error) {
    console.error('[NewTaskUtil] 检查磁力链文件数量限制时出错:', error)
    // 如果检查过程中出错，为了安全起见，提示超出限制
    if (alertDialog) {
      showOverLimitAlert(alertDialog, maxCount)
    }
    return false
  }
}

/**
 * 判断有效的路径
 * 如果path的长度小于3且以:结尾，则在path后面拼接两个反斜杠
 * 例如：D: -> D:\\
 * @param path - 要验证的路径
 * @returns 验证后的路径
 */
export function validatePath(path: string): string {
  if (path.length < 3 && path.endsWith(':')) {
    return path + '\\\\'
  }
  return path
}
