function padLeftZero(str: string): string {
  return ('00' + str).substr(str.length);
}
export namespace TimeHelperNS {
  export function formatDate(date: Date, fmt: string): string {
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    const o: { [key: string]: number } = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'h+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds()
    };
    for (const k in o) {
      // eslint-disable-next-line prefer-const
      let str: string = String(o[k]);
      if (new RegExp(`(${k})`).test(fmt)) {
        fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str));
      }
    }
    if (/(f+)/.test(fmt)) {
      const msStr: string = date.getMilliseconds() + '';
      fmt = fmt.replace(RegExp.$1, ('000' + msStr).substr(msStr.length));
    }
    return fmt;
  }

  /**
   * 格式化时间
   * @param {number} time - 时间，单位毫秒
   * @return {string} 格式化后字符串
   */
  export function getPubTime(time: number): string {
    const date: Date = new Date(+time);
    const now: number = new Date().getTime();
    const t: number = now - time * 1;
    const dayCount: number = dateDiff(now, date.getTime()); // 距离今天的天数
    let result: string = '';
    if (t < 1000 * 60 * 60) {
      // 1小时内
      const minute: number = Math.floor(t / (1000 * 60));
      result = minute >= 1 ? minute + '分钟前' : '刚刚';
    } else if (t < 1000 * 60 * 60 * 24) {
      // 24小时内
      result = Math.floor(t / (1000 * 60 * 60)) + '小时前';
    } else if (dayCount === 1) {
      // 昨天
      result = '昨天' + timeFormat(time);
    } else if (dayCount === 2) {
      // 前天
      result = '前天' + timeFormat(time);
    } else if (date.getFullYear() === new Date().getFullYear()) {
      // 当前年份
      result = `${pad(date.getMonth() + 1, 2)}-${pad(date.getDate(), 2)} ${pad(date.getHours(), 2)}:${pad(date.getMinutes(), 2)}`;
    } else {
      result = createTimeFormat(time);
    }
    return result;
  }

  /**
   * 格式化创建时间，格式为：YYYY-MM-DD hh:mm
   * @param {number} time - 时间
   * @return {string} 格式化后字符串
   */
  export function createTimeFormat(time: number): string {
    const date: Date = new Date(+time);

    if (date.toString() === 'Invalid Date') return '';

    return `${date.getFullYear()}-${pad(date.getMonth() + 1, 2)}-${pad(date.getDate(), 2)} ${pad(date.getHours(), 2)}:${pad(date.getMinutes(), 2)}`;
  }

  /**
   * 格式化时间，格式为：hh:mm
   * @param {number} time - 毫秒
   * @return {string} 格式化后字符串 比如12:45代表12点45分
   */
  export function timeFormat(time: number): string {
    const date: Date = new Date(+time);
    if (date.toString() === 'Invalid Date') return '';
    return pad(date.getHours(), 2) + ':' + pad(date.getMinutes(), 2);
  }

  /**
   * 两个日期的天数差
   * @param {date} d1 - 时间，日期1
   * @param {date} d2 - 时间 日期2
   * @return {number} 天数
   */
  export function dateDiff(d1: number, d2: number): number {
    const day1: Date = new Date(d1);
    d1 = day1.setHours(0, 0, 0, 0);
    const day2: Date = new Date(d2);
    d2 = day2.setHours(0, 0, 0, 0);
    return (d1 - d2) / 1000 / 60 / 60 / 24;
  }

  /**
   * 补位
   * @param {number} num - 数字
   * @param {number} n - 期望位数
   * @return {string} 补位后字符串
   */
  export function pad(num: number, n: number): string {
    let len: number = num.toString().length;
    let res: string = num.toString();
    while (len < n) {
      res = '0' + res;
      len++;
    }
    return res;
  }

  /**
   * @description 秒转换成时分秒
   */
  export function formatSeconds(seconds: number): string {
    let ret: string = '';
    do {
      if (typeof seconds !== 'number' || seconds <= 0) {
        ret = '00:00:00';
        break;
      }

      const hours: number = Math.floor(seconds / 3600);
      const mins: number = Math.floor(seconds / 60) % 60;
      const secs: number = Math.floor(seconds % 60);

      ret = hours < 10 ? '0' + hours + ':' : '' + hours + ':';
      ret = ret + (mins < 10 ? '0' + mins + ':' : '' + mins + ':');
      ret = ret + (secs < 10 ? '0' + secs : '' + secs);
    } while (0);

    return ret;
  }

  // 精简化显示规则：小时最大为99，大于显示天，天最大为999
  export function formatSecondsCustom(seconds: number): string {
    let ret: string = '';
    do {
      if (seconds <= 0) {
        ret = '00:00:00';
        break;
      }

      const hours: number = Math.floor(seconds / 3600);
      const mins: number = Math.floor(seconds / 60) % 60;
      const secs: number = Math.floor(seconds % 60);

      if (hours <= 99) {
        ret = hours < 10 ? '0' + hours + ':' : '' + hours + ':';
        ret = ret + (mins < 10 ? '0' + mins + ':' : '' + mins + ':');
        ret = ret + (secs < 10 ? '0' + secs : '' + secs);
      } else {
        let days: number = Math.floor(seconds / (3600 * 24));
        days = days > 999 ? 999 : days;
        ret = `剩${days}天`;
      }
    } while (0);

    return ret;
  }

  export function convertTimeToMinutes(hour: number, minute: number, second: number): number {
    const seconds: number = 3600 * hour + 60 * minute + second;
    return seconds;
  }

  export function convertMinuteToTime(seconds: number): number[] {
    const hour: number = Math.floor(seconds / 3600);
    const minute: number = Math.floor((seconds / 60) % 60);
    const second: number = Math.floor(seconds % 60);
    return [hour, minute, second];
  }
}
