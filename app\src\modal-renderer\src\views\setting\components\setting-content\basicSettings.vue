<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import CheckboxGroup, { ICheckoutGroupOptions } from '@root/common/components/ui/checkbox-group/index.vue'
import RadioGroup, { IRadioGroupOptions } from '@root/common/components/ui/radio-group/index.vue'
import { PopUpNS } from '@root/common/pop-up'
import { useDebounceFn } from '@vueuse/core'
import { setSettingConfig, getSettingConfig, START_SETTING_NAME_MAP, DOWNLOAD_SPEED_SETTING_NAME_MAP } from '@root/modal-renderer/src/views/setting'
import { useAlertDialog } from '@root/common/components/ui/Dialog/useAlertDialog'
import { config } from '@root/common/config/config'
import path from 'path'
import { ThunderHelper, RegistryHKey, RegistryDataType } from '@root/common/thunder-helper';
import { GetExePath } from '@root/common/xxx-node-path';

const alertDialog = useAlertDialog()


const handleAutoRun = (preValue: boolean, newValue: boolean) => {
  if (newValue) {
    const thunderPath: string = path.join(GetExePath(), 'Thunder.exe')
    const param: string = thunderPath + ' -silent -StartType:AutoRun'
    ThunderHelper.writeRegValue(
      RegistryHKey.HKEY_CURRENT_USER,
      'Software\\Microsoft\\Windows\\CurrentVersion\\Run',
      'Thunder',
      param,
      RegistryDataType.REG_SZ
    );
  } else {
    ThunderHelper.deleteRegValue(RegistryHKey.HKEY_CURRENT_USER, 'Software\\Microsoft\\Windows\\CurrentVersion\\Run', 'Thunder')
  }
}


// 开机与启动相关
const selectedStartName = ref<string[]>([])
const startOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '开启免打扰模式',
    name: START_SETTING_NAME_MAP.AntiDisturb,
    tip: '打扰模式下，当您运行全屏程序时，迅雷将\n自动帮您取消提示消息弹出，避免打扰您的其\n它体验。',
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    defaultValue: false
  },
  {
    label: '启动老板键',
    name: START_SETTING_NAME_MAP.BossKeySwitch,
    tip: '如果老板来了，按老板键，迅雷将自动隐身',
    action: {
      type: 'input',
      width: 160,
      value: 'Alt+D',
      name: START_SETTING_NAME_MAP.BossKeyName,
      onKeyDown: (e: KeyboardEvent, optionName: string) => {
        handleBossKeyDown(e, optionName)
      }
    },
    onChange: (checked: boolean, optionName: string) => {
      handleBossKeyChange(checked, optionName)
    },
    defaultValue: false,
    errorText: '' // 添加错误文本属性
  },
  {
    label: '新建任务时显示主界面',
    name: START_SETTING_NAME_MAP.NewTaskDlgWithoutMainWnd,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    defaultValue: false
  },
  {
    label: '开机启动迅雷',
    name: START_SETTING_NAME_MAP.AutoRun,
    onChange: async (checked: boolean, optionName: string) => {
      if (!checked) {
        const result = await alertDialog.info({
          title: '推荐保留开机启动',
          content: '可自动识别剪贴板下载链接、可提升云盘播放速度',
          confirmText: '保留启动',
          cancelText: '仍然取消',
          showTitleIcon: false,
        })
        if (!result) {
          setSettingConfig(optionName, checked)
          handleAutoRun(!checked, checked)
        } else {
          selectedStartName.value.push(optionName)
        }
      } else {
        setSettingConfig(optionName, checked)
        handleAutoRun(!checked, checked)
      }
    }
  },
  {
    label: '开启自动更新',
    name: START_SETTING_NAME_MAP.AutoUpdate,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    defaultValue: false
  },
])

// 下载模式相关
const selectedDownloadModeType = ref<string>('')
const downloadModeOptions = ref<IRadioGroupOptions[]>([
  {
    label: '全速下载',
    value: '1',
  },
  {
    label: '限速下载',
    value: '0',
    action: {
      type: 'button',
      label: '修改配置',
      onAction: () => {
        PopUpNS.showLimitSpeedSettingDlg()
      }
    }
  },
])

// 老板键组合状态
const bossKeyCombination = ref<string[]>([])
const isRecordingBossKey = ref(false)

// 验证老板键输入的函数
const validateBossKey = useDebounceFn((value: string, optionName: string) => {

  const bossKeyOption = startOptions.value.find(option => option.name === START_SETTING_NAME_MAP.BossKeySwitch)
  if (!bossKeyOption || !bossKeyOption.action) return

  if (value.indexOf('+') === -1) {
    bossKeyOption.errorText = '注册老板键失败,请选择功能键 + 字母键/数字键'
    bossKeyOption.action.value = '无'
  } else {
    bossKeyOption.errorText = ''

    setSettingConfig(optionName, value)
  }
}, 500)

// 处理键盘按下事件
const handleBossKeyDown = (event: KeyboardEvent, optionName: string) => {
  event.preventDefault()

  const key = event.key
  const bossKeyOption = startOptions.value.find(option => option.name === START_SETTING_NAME_MAP.BossKeySwitch)
  if (!bossKeyOption || !bossKeyOption.action) return

  // 如果按下 ESC 键，重置组合键
  if (key === 'Escape') {
    bossKeyCombination.value = []
    bossKeyOption.action.value = '无'
    isRecordingBossKey.value = false
    return
  }

  // 如果按下 Backspace，删除所有的组合键
  if (key === 'Backspace') {
    bossKeyCombination.value = []
    isRecordingBossKey.value = false
    updateBossKeyDisplay()
    validateBossKey('', optionName)
    return
  }

  // 开始记录组合键
  if (!isRecordingBossKey.value) {
    isRecordingBossKey.value = true
    bossKeyCombination.value = []
  }

  // 处理修饰键
  if (['Control', 'Alt', 'Shift'].includes(key)) {
    const modifierMap: { [key: string]: string } = {
      'Control': 'Ctrl+',
      'Alt': 'Alt+',
      'Shift': 'Shift+',
    }
    const modifier = modifierMap[key]
    if (modifier && !bossKeyCombination.value.includes(modifier)) {
      bossKeyCombination.value.push(modifier)
    }
  } else {
    // 处理普通键（字母、数字等）
    const upperKey = key.toUpperCase()
    if (!bossKeyCombination.value.includes(upperKey)) {
      bossKeyCombination.value.push(upperKey)
    }
  }

  updateBossKeyDisplay()
}

// 更新老板键显示
const updateBossKeyDisplay = () => {
  const bossKeyOption = startOptions.value.find(option => option.name === START_SETTING_NAME_MAP.BossKeySwitch)
  if (!bossKeyOption || !bossKeyOption.action) return

  if (bossKeyCombination.value.length === 0) {
    bossKeyOption.action.value = '无'
  } else {
    const keyCombination = bossKeyCombination.value.join('')
    bossKeyOption.action.value = keyCombination
    validateBossKey(keyCombination, START_SETTING_NAME_MAP.BossKeyName)
  }
}

// 监听老板键选项变化
const handleBossKeyChange = (checked: boolean, optionName: string) => {
  if (!checked) {
    // 当取消选中时，重置组合键状态
    bossKeyCombination.value = []
    isRecordingBossKey.value = false
  }
  setSettingConfig(optionName, checked)
}

const initBossKey = async () => {

  const bossKeyName = await getSettingConfig(START_SETTING_NAME_MAP.BossKeyName, 'Ctrl+D') as string
  if (bossKeyName) {
    bossKeyCombination.value = [bossKeyName]
    isRecordingBossKey.value = true
    updateBossKeyDisplay()
  }
}

const initDefaultValue = async () => {

  selectedStartName.value = await Promise.all(startOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))

  const downloadType = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.Type, '1') as string
  if (downloadType) {
    selectedDownloadModeType.value = downloadType
  }
}

const handleDownloadModeChange = (value: string) => {
  setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.Type, value)

  if (value === '1') {
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.DownloadSpeedChk, false)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.UploadSpeedChk, false)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeSwitch, false)
  }
}

const handleConfigValueChanged = (section: string, key: string, preValue: any, newValue: any, isClick: boolean) => {

  if (`${section}-${key}` === DOWNLOAD_SPEED_SETTING_NAME_MAP.Type) {
    selectedDownloadModeType.value = newValue.toString()
  }
}

onMounted(() => {
  initBossKey()
  initDefaultValue()

  config.on('OnConfigValueChanged', handleConfigValueChanged)
})

onUnmounted(() => {
  config.off('OnConfigValueChanged', handleConfigValueChanged)
})

</script>

<template>
  <CheckboxGroup :options="startOptions" orientation="vertical" v-model="selectedStartName" title="开机与启动"
    :default-value="selectedStartName" />
  <div class="settings-content-divider"></div>
  <RadioGroup :options="downloadModeOptions" orientation="vertical" title="下载模式" @change="handleDownloadModeChange"
    v-model="selectedDownloadModeType" />
  <div class="settings-content-divider"></div>
</template>