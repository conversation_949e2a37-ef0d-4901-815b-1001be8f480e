{"name": "thunder_2025", "version": "0.0.1", "author": "", "description": "", "main": "dist/main.js", "private": true, "packageManager": "npm@10.8.0", "only-allow": "npm", "scripts": {"check-upgrades": "node scripts/check-upgrades.js", "diagnose": "node scripts/diagnose-build-env.js", "prepare-sass": "node scripts/prepare-sass-build.js", "build:ci": "node scripts/build-ci.js", "auto-build": "node scripts/auto-build.js", "update-bin": "cd ../script/ && update_bin.bat", "update-color-variable": "node ./src/common/assets/css/source/gen-color-scss.js", "start": "cd ../bin/Release/ && Thunder.exe --inspect=5858", "start:log": "cd ../bin/Release/ && Thunder.exe --enable-logging --v=1", "patch": "patch-package", "postinstall": "npm run patch", "format": "biome format --write .", "format:prettier": "prettier --write \"src/modal-renderer/src/components/new-task/**/*.{ts,tsx,js,jsx,vue,css,scss,json,md}\" \"src/modal-renderer/src/views/create-task/**/*.{ts,tsx,js,jsx,vue,css,scss,json,md}\"", "format:prettier:check": "prettier --check \"src/modal-renderer/**/*.{ts,tsx,js,jsx,vue,css,scss,json,md}\"", "lint:prettier": "prettier --list-different \"src/modal-renderer/**/*.{ts,tsx,js,jsx,vue,css,scss,json,md}\"", "prepare": "husky install", "clean": "rm -rf dist && rm -rf release", "clean-install": "rm -rf ./src/*/node_modules node_modules", "dev:main": "cross-env PUBLIC_LOGGER=1 npm --prefix src/electron/main run dev", "dev:renderer": "cross-env PUBLIC_LOGGER=1 npm --prefix src/electron/preload run dev && npm --prefix src/electron run dev:renderer", "prebuild": "node scripts/prepare-sass-build.js --silent", "build": "npm run build:electron && npm run build:pan:plugin && npm run build:player:plugin", "build:electron": "node src/scripts/build-electron.js", "build:start": "npm run build && npm run start", "build:pan": "node src/common/thunder-pan-manager/pan-script/genPubKeys.js", "prebuild:pan:plugin": "node scripts/prepare-sass-build.js --silent", "build:pan:plugin": "npm run build:pan && cd ./src/thunder-pan-plugin && cross-env SASS_IMPLEMENTATION=sass NODE_OPTIONS=--max-old-space-size=4096 npm run build", "prebuild:player:plugin": "node scripts/prepare-sass-build.js --silent", "build:player:plugin": "cd ./src/player-plugin && cross-env SASS_IMPLEMENTATION=sass NODE_OPTIONS=--max-old-space-size=4096 npm run build", "postbuild": "cross-env DEBUG_ASYNC_REMOTE=1 BIN_TARGET=Release node asar/build-asar.js", "package:local": "npm run build && electron-builder build -c ./build.config.json --publish never", "package": "npm run build && electron-builder build -c ./build.config.json --publish always"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@popperjs/core": "^2.11.8", "@rsbuild/core": "1.2.15", "@rsbuild/plugin-sass": "^1.2.1", "@rsbuild/plugin-vue": "^1.0.5", "@vueuse/core": "^13.2.0", "@vueuse/electron": "^13.3.0", "@xbase/electron_account_kit": "^0.0.7", "@xbase/electron_auth_kit": "^0.0.7", "@xbase/electron_auth_types_kit": "^0.0.7", "@xbase/electron_base_kit": "^0.0.7", "@xbase/electron_captcha_kit": "^0.0.7", "@xbase/electron_captcha_types_kit": "^0.0.7", "@xbase/electron_common_kit": "^0.0.7", "@xbase/electron_default_plugins_kit": "^0.0.7", "@xbase/electron_sync_kit": "^0.0.7", "@xunlei/async-remote": "^3.22.3-dev", "@xunlei/node-net-ipc": "1.0.32", "@xunlei/sget": "^1.0.3", "@xunlei/sort-by": "^1.1.0", "axios": "^1.9.0", "blueimp-md5": "^2.19.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "deep-object-diff": "^1.1.9", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "load-json-file": "^7.0.1", "lodash-es": "^4.17.21", "lottie-web": "^5.13.0", "lottie-web-vue": "^2.0.7", "p-timeout": "^6.1.4", "pinia": "^3.0.2", "qrcode": "^1.5.4", "reka-ui": "^2.2.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-inline-svg": "^4.0.1", "vue-router": "^4.5.1", "vue-tippy": "6.6.0", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-perfect-scrollbar": "^2.0.0", "write-json-file": "^6.0.0"}, "devDependencies": {"@electron-toolkit/tsconfig": "^1.0.1", "@prettier/plugin-pug": "^3.4.0", "@rsbuild/plugin-svgr": "^1.0.6", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^20.17.47", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "cross-env": "^7.0.3", "electron": "22.3.27", "electron-builder": "^24.13.3", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-vue": "^10.2.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "patch-package": "^8.0.0", "prettier": "^3.5.3", "prettier-plugin-css-order": "^2.1.2", "prettier-plugin-organize-attributes": "^1.0.0", "prettier-plugin-packagejson": "^2.5.15", "prettier-plugin-vue": "^1.1.6", "sass": "^1.89.2", "typescript": "^5.5.2", "vitest": "^2.1.4"}, "license": "MIT", "lint-staged": {"src/modal-renderer/**/*.{ts,tsx,js,jsx,vue,css,scss,json,md}": ["prettier --write"]}}