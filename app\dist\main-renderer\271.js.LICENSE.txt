/*!
     Transformation Matrix v2.0
     (c) Epistemex 2014-2015
     www.epistemex.com
     By <PERSON>
     Contributions by le<PERSON><PERSON>.
     License: MIT, header required.
     */

/*!
  * vue-tippy v6.6.0
  * (c) 2024 
  * @license MIT
  */

/*!
 * perfect-scrollbar v1.5.6
 * Copyright 2024 <PERSON><PERSON><PERSON>, MDBootstrap and Contributors
 * Licensed under MIT
 */

/*!
 * pinia v3.0.2
 * (c) 2025 <PERSON> <PERSON>
 * @license MIT
 */

/*! #__NO_SIDE_EFFECTS__ */

/*! ../../../../response_error */

/*! ../../../ReporterUtility */

/*! ../../../TraceInfoUtility */

/*! ../../../models */

/*! ../../../response_error */

/*! ../../../response_error/ErrorDetailUtility */

/*! ../../AsyncUtility */

/*! ../../DeviceInfoUtility */

/*! ../../ResponseErrorUriUtility */

/*! ../../Result */

/*! ../../Utility */

/*! ../../analytics */

/*! ../../database_manager */

/*! ../../error_details */

/*! ../../log */

/*! ../../part_analytics_info */

/*! ../../request */

/*! ../../response_error */

/*! ../../types */

/*! ../Consts */

/*! ../DeviceInfoUtility */

/*! ../EnvManager */

/*! ../Interface */

/*! ../Interfaces */

/*! ../ReporterUtility */

/*! ../WordArrayUtility */

/*! ../analytics */

/*! ../analytics/analysis_report_service/AnalyticsInfoRecordProcesserHandler */

/*! ../analytics/analysis_report_service/AnalyticsInfoReportProcesserHandler */

/*! ../analytics_utility */

/*! ../auth/Consts */

/*! ../base_request */

/*! ../database_manager */

/*! ../event_emitter */

/*! ../i18n */

/*! ../log */

/*! ../models */

/*! ../request */

/*! ../resources */

/*! ../response_error */

/*! ./AES */

/*! ./Account */

/*! ./AnalyticsInfoTableManager */

/*! ./AnalyticsManager */

/*! ./AnalyticsRecordProcesser */

/*! ./AnalyticsReportProcesser */

/*! ./AnalyticsUtility */

/*! ./AsyncUtility */

/*! ./BackoffTaskRunner */

/*! ./BaseDecoratorUtility */

/*! ./BaseDecorators */

/*! ./BaseRequest */

/*! ./BaseRequestUtility */

/*! ./CaptchaInterface */

/*! ./CaptchaTokenInfo */

/*! ./CipherAlgorithms */

/*! ./ClassDecorators */

/*! ./Clonable */

/*! ./Codable */

/*! ./Common */

/*! ./Consts */

/*! ./Credentials */

/*! ./CredentialsManager */

/*! ./CredentialsTableManager */

/*! ./CryptoUtility */

/*! ./DatabaseManager */

/*! ./DebugInfoDetail */

/*! ./Decorators */

/*! ./DefaultBrowserWindow */

/*! ./DefaultConsole */

/*! ./DefaultRequest */

/*! ./DeviceInfoUtility */

/*! ./DeviceSignUtility */

/*! ./EnvManager */

/*! ./Error */

/*! ./ErrorCode */

/*! ./ErrorDetailUtility */

/*! ./ErrorDetails */

/*! ./ErrorInfoDetail */

/*! ./ErrorUtility */

/*! ./Event */

/*! ./EventEmitter */

/*! ./EventEmitterHelper */

/*! ./EventPublisherSpecificPartAnalyticsInfo */

/*! ./FileSystemUtility */

/*! ./FunctionBeginCallSpecificPartAnalyticsInfo */

/*! ./FunctionResultSpecificPartAnalyticsInfo */

/*! ./FunctionUtility */

/*! ./IAnalyticsAble */

/*! ./IOAuth2Client */

/*! ./IOAuth2ClientGetCredentialsHandler */

/*! ./IOAuth2ClientRequestHandler */

/*! ./IOAuth2ClientSignInHandler */

/*! ./InitStateManager */

/*! ./Interface */

/*! ./Interfaces */

/*! ./IntervalScheduler */

/*! ./JSONUtility */

/*! ./JumpingQueuePromise */

/*! ./JumpingQueuePromiseGroup */

/*! ./KeyValueTableManager */

/*! ./LocalCaptchaToken */

/*! ./LocalCredentials */

/*! ./LocalizationReader */

/*! ./LocalizedMessageDetail */

/*! ./Lock */

/*! ./LogFunctionCallInfo */

/*! ./LogUtility */

/*! ./Logger */

/*! ./MDAlgorithms */

/*! ./MainProcessBrowserWindow */

/*! ./MainProcessRemote */

/*! ./MemoryStorage */

/*! ./Models */

/*! ./MqttClient2 */

/*! ./NetworkManager */

/*! ./OAuth2Client */

/*! ./ObjectUtility */

/*! ./OldAccountClient */

/*! ./OldAccountSessionInfo */

/*! ./OldAccountSessionInfoData */

/*! ./OperatorUtility */

/*! ./OriginalErrorInfoDetail */

/*! ./PartialURL */

/*! ./PromiseTask */

/*! ./QueuePromise */

/*! ./QueuePromiseGroup */

/*! ./RecordAbleEventEmitter */

/*! ./RecordFunctionCallInfo */

/*! ./Recorder */

/*! ./RendererProcessBrowserWindow */

/*! ./RendererProcessBrowserWindowMainProcessHandler */

/*! ./RendererProcessRemote */

/*! ./ReporterUtility */

/*! ./RequestClient */

/*! ./RequestClientUtility */

/*! ./RequestFunctionWrapper */

/*! ./RequestSpecificPartAnalyticsInfo */

/*! ./ResponseError */

/*! ./ResponseErrorUriUtility */

/*! ./Result */

/*! ./SignInStateManager */

/*! ./SimpleBrowserWindow */

/*! ./SimpleConsole */

/*! ./SimpleStorage */

/*! ./Sqlite3DatabaseClient */

/*! ./Sqlite3StatementClient */

/*! ./Sqlite3StatementExtUtility */

/*! ./Sqlite3StatementUtility */

/*! ./StatInfoUtils */

/*! ./SyncClient */

/*! ./SyncMqttClient */

/*! ./SyncMqttMessage */

/*! ./SyncMqttMessagePayload */

/*! ./SyncTopicBuilder */

/*! ./Topic */

/*! ./TraceInfoUtility */

/*! ./TypeUtility */

/*! ./URLUtility */

/*! ./UUIDUtility */

/*! ./UpdateResponseError */

/*! ./Utility */

/*! ./WeakRefWrapper */

/*! ./WordArrayUtility */

/*! ./_CommonShared */

/*! ./account */

/*! ./aes */

/*! ./analysis_report_service */

/*! ./analytics */

/*! ./analytics_manager */

/*! ./analytics_utility */

/*! ./auth/Apis */

/*! ./auth/Consts */

/*! ./auth/Models */

/*! ./base */

/*! ./base_request */

/*! ./blowfish */

/*! ./browser_window */

/*! ./captcha-token-info-manager/CaptchaTokenInfoManager */

/*! ./cipher-core */

/*! ./cipher_algorithms */

/*! ./class */

/*! ./common */

/*! ./core */

/*! ./crypto_utility */

/*! ./database_manager */

/*! ./decorators */

/*! ./en_US */

/*! ./enc-base64 */

/*! ./enc-base64url */

/*! ./enc-utf16 */

/*! ./error_details */

/*! ./error_details/DebugInfoDetail */

/*! ./errror_details */

/*! ./event_emitter */

/*! ./evpkdf */

/*! ./exts */

/*! ./exts/errror_details */

/*! ./format-hex */

/*! ./function */

/*! ./hmac */

/*! ./i18n */

/*! ./interfaces */

/*! ./json */

/*! ./lang */

/*! ./lib-typedarrays */

/*! ./lock */

/*! ./log */

/*! ./main */

/*! ./main/Captcha */

/*! ./md5 */

/*! ./mode-cfb */

/*! ./mode-ctr */

/*! ./mode-ctr-gladman */

/*! ./mode-ecb */

/*! ./mode-ofb */

/*! ./models */

/*! ./oauth2client */

/*! ./old_account_system */

/*! ./pad-ansix923 */

/*! ./pad-iso10126 */

/*! ./pad-iso97971 */

/*! ./pad-nopadding */

/*! ./pad-zeropadding */

/*! ./part_analytics_info */

/*! ./pbkdf2 */

/*! ./process_remote */

/*! ./queue_promise */

/*! ./rabbit */

/*! ./rabbit-legacy */

/*! ./rc4 */

/*! ./record_function_call_info */

/*! ./request */

/*! ./request_client */

/*! ./resources */

/*! ./response_error */

/*! ./response_error2 */

/*! ./ripemd160 */

/*! ./sha1 */

/*! ./sha224 */

/*! ./sha256 */

/*! ./sha3 */

/*! ./sha384 */

/*! ./sha512 */

/*! ./simple_interfaces */

/*! ./single_promise */

/*! ./sqlite3_utility */

/*! ./sync_client */

/*! ./text-handler/ErrorTextHandler */

/*! ./tripledes */

/*! ./type */

/*! ./type_utility */

/*! ./update_response_error */

/*! ./x64-core */

/*! ./zh_CN */

/*! @xbase/electron_auth_kit */

/*! @xbase/electron_auth_types_kit */

/*! @xbase/electron_base_kit */

/*! @xbase/electron_captcha_kit */

/*! @xbase/electron_common_kit */

/*! @xbase/electron_sync_kit */

/*! Bundled license information:

@jspm/core/nodelibs/browser/buffer.js:
  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/

/*! axios */

/*! buffer */

/*! crypto */

/*! crypto-js */

/*! electron */

/*! fs */

/*! i18next */

/*! mqtt */

/*! os */

/*! path */

/*! sqlite3 */

/*! util */

/*! uuid */

/*!*********************!*\
  !*** external "fs" ***!
  \*********************/

/*!*********************!*\
  !*** external "os" ***!
  \*********************/

/*!**********************!*\
  !*** ./src/index.ts ***!
  \**********************/

/*!***********************!*\
  !*** ./src/Consts.ts ***!
  \***********************/

/*!***********************!*\
  !*** ./src/Result.ts ***!
  \***********************/

/*!***********************!*\
  !*** external "path" ***!
  \***********************/

/*!***********************!*\
  !*** external "util" ***!
  \***********************/

/*!***********************!*\
  !*** external "uuid" ***!
  \***********************/

/*!************************!*\
  !*** external "axios" ***!
  \************************/

/*!*************************!*\
  !*** ./src/Clonable.ts ***!
  \*************************/

/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/

/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/

/*!**************************!*\
  !*** ./src/auth/Apis.ts ***!
  \**************************/

/*!**************************!*\
  !*** ./src/lock/Lock.ts ***!
  \**************************/

/*!**************************!*\
  !*** ./src/log/index.ts ***!
  \**************************/

/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/

/*!***************************!*\
  !*** ./src/PartialURL.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/URLUtility.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/i18n/index.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/json/index.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/lock/index.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/log/Logger.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/log/Models.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/main/index.ts ***!
  \***************************/

/*!***************************!*\
  !*** external "electron" ***!
  \***************************/

/*!****************************!*\
  !*** ./src/TypeUtility.ts ***!
  \****************************/

/*!****************************!*\
  !*** ./src/UUIDUtility.ts ***!
  \****************************/

/*!****************************!*\
  !*** ./src/auth/Consts.ts ***!
  \****************************/

/*!****************************!*\
  !*** ./src/auth/Models.ts ***!
  \****************************/

/*!****************************!*\
  !*** ./src/i18n/Models.ts ***!
  \****************************/

/*!****************************!*\
  !*** ./src/main/Common.ts ***!
  \****************************/

/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/

/*!*****************************!*\
  !*** ./src/AsyncUtility.ts ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/ErrorUtility.ts ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/json/Codable.ts ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/main/Captcha.ts ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/models/Event.ts ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/models/index.ts ***!
  \*****************************/

/*!******************************!*\
  !*** ./src/MemoryStorage.ts ***!
  \******************************/

/*!******************************!*\
  !*** ./src/ObjectUtility.ts ***!
  \******************************/

/*!******************************!*\
  !*** ./src/request/index.ts ***!
  \******************************/

/*!*******************************!*\
  !*** ./src/DefaultConsole.ts ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/DefaultRequest.ts ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/NetworkManager.ts ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/WeakRefWrapper.ts ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/function/index.ts ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/log/Decorators.ts ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/log/LogUtility.ts ***!
  \*******************************/

/*!********************************!*\
  !*** ./src/OperatorUtility.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/analytics/index.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/main/EnvManager.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/main/Interfaces.ts ***!
  \********************************/

/*!*********************************!*\
  !*** ./src/InitStateManager.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/decorators/index.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/interfaces/index.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/json/JSONUtility.ts ***!
  \*********************************/

/*!**********************************!*\
  !*** ./src/DeviceInfoUtility.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/FileSystemUtility.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/IntervalScheduler.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/main/models/index.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/sync_client/Topic.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/sync_client/index.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/type_utility/type.ts ***!
  \**********************************/

/*!***********************************!*\
  !*** ./src/main/_CommonShared.ts ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/main/account/index.ts ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/models/Credentials.ts ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/oauth2client/index.ts ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/type_utility/index.ts ***!
  \***********************************/

/*!************************************!*\
  !*** ./src/event_emitter/index.ts ***!
  \************************************/

/*!*************************************!*\
  !*** ./src/browser_window/index.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/crypto_utility/index.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/i18n/resources/index.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/main/account/Account.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/process_remote/index.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/response_error/Error.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/response_error/index.ts ***!
  \*************************************/

/*!**************************************!*\
  !*** ./src/browser_window/Common.ts ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/decorators/base/index.ts ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/process_remote/Common.ts ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/response_error2/index.ts ***!
  \**************************************/

/*!***************************************!*\
  !*** ./src/analytics/models/index.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/database_manager/index.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/decorators/class/index.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/models/SyncMqttMessage.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/response_error/Utility.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/response_error2/Result.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/sync_client/SyncClient.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/types/CaptchaInterface.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/types/CaptchaTokenInfo.ts ***!
  \***************************************/

/*!****************************************!*\
  !*** ./src/analytics/models/Models.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/analytics_utility/index.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/i18n/LocalizationReader.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/log/LogFunctionCallInfo.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/oauth2client/Interfaces.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/simple_interfaces/index.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/sync_client/MqttClient2.ts ***!
  \****************************************/

/*!*****************************************!*\
  !*** ./src/function/FunctionUtility.ts ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/i18n/resources/Interface.ts ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/interfaces/IOAuth2Client.ts ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/old_account_system/index.ts ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/response_error/ErrorCode.ts ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/type_utility/TypeUtility.ts ***!
  \*****************************************/

/*!******************************************!*\
  !*** ./src/analytics/ReporterUtility.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/i18n/resources/lang/en_US.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/i18n/resources/lang/index.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/i18n/resources/lang/zh_CN.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/oauth2client/OAuth2Client.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/oauth2client/models/index.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/old_account_system/Consts.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/response_error/exts/index.ts ***!
  \******************************************/

/*!*******************************************!*\
  !*** ../../node_modules/crypto-js/aes.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ../../node_modules/crypto-js/md5.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ../../node_modules/crypto-js/rc4.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/analytics/TraceInfoUtility.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/event_emitter/EventEmitter.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/oauth2client/StatInfoUtils.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/oauth2client/models/Models.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/request/base_request/index.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/sync_client/SyncMqttClient.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** external "@xbase/electron_auth_kit" ***!
  \*******************************************/

/*!*******************************************!*\
  !*** external "@xbase/electron_base_kit" ***!
  \*******************************************/

/*!*******************************************!*\
  !*** external "@xbase/electron_sync_kit" ***!
  \*******************************************/

/*!********************************************!*\
  !*** ../../node_modules/crypto-js/core.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ../../node_modules/crypto-js/hmac.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ../../node_modules/crypto-js/sha1.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ../../node_modules/crypto-js/sha3.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./node_modules/mqtt/dist/mqtt.esm.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./src/crypto_utility/MDAlgorithms.ts ***!
  \********************************************/

/*!*********************************************!*\
  !*** ../../node_modules/crypto-js/index.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/crypto_utility/CryptoUtility.ts ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/function/queue_promise/index.ts ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/request/request_client/index.ts ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/response_error/ResponseError.ts ***!
  \*********************************************/

/*!*********************************************!*\
  !*** external "@xbase/electron_common_kit" ***!
  \*********************************************/

/*!**********************************************!*\
  !*** ../../node_modules/crypto-js/evpkdf.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ../../node_modules/crypto-js/pbkdf2.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ../../node_modules/crypto-js/rabbit.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ../../node_modules/crypto-js/sha224.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ../../node_modules/crypto-js/sha256.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ../../node_modules/crypto-js/sha384.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ../../node_modules/crypto-js/sha512.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/function/single_promise/index.ts ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/main/account/SyncTopicBuilder.ts ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/models/SyncMqttMessagePayload.ts ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/oauth2client/LocalCredentials.ts ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/sync_client/BackoffTaskRunner.ts ***!
  \**********************************************/

/*!**********************************************!*\
  !*** external "@xbase/electron_captcha_kit" ***!
  \**********************************************/

/*!***********************************************!*\
  !*** ./src/decorators/base/BaseDecorators.ts ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./src/sync_client/EventEmitterHelper.ts ***!
  \***********************************************/

/*!************************************************!*\
  !*** ../../node_modules/crypto-js/blowfish.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ../../node_modules/crypto-js/mode-cfb.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ../../node_modules/crypto-js/mode-ctr.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ../../node_modules/crypto-js/mode-ecb.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ../../node_modules/crypto-js/mode-ofb.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ../../node_modules/crypto-js/x64-core.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/crypto_utility/WordArrayUtility.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/main/account/EventEmitterHelper.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/oauth2client/CredentialsManager.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/oauth2client/SignInStateManager.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/old_account_system/models/index.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/response_error/decorators/index.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/simple_interfaces/SimpleConsole.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/simple_interfaces/SimpleStorage.ts ***!
  \************************************************/

/*!*************************************************!*\
  !*** ../../node_modules/crypto-js/enc-utf16.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ../../node_modules/crypto-js/ripemd160.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ../../node_modules/crypto-js/tripledes.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/analytics_utility/IAnalyticsAble.ts ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/database_manager/DatabaseManager.ts ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/decorators/class/ClassDecorators.ts ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/process_remote/MainProcessRemote.ts ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/request/base_request/BaseRequest.ts ***!
  \*************************************************/

/*!*************************************************!*\
  !*** external "@xbase/electron_auth_types_kit" ***!
  \*************************************************/

/*!**************************************************!*\
  !*** ../../node_modules/crypto-js/enc-base64.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ../../node_modules/crypto-js/format-hex.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/analytics/analytics_manager/index.ts ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/request/request_client/Interfaces.ts ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/response_error/ErrorDetailUtility.ts ***!
  \**************************************************/

/*!***************************************************!*\
  !*** ../../node_modules/crypto-js/cipher-core.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./src/analytics_utility/AnalyticsUtility.ts ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./src/function/queue_promise/PromiseTask.ts ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./src/i18n/text-handler/ErrorTextHandler.ts ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./src/response_error/error_details/index.ts ***!
  \***************************************************/

/*!****************************************************!*\
  !*** ../../node_modules/crypto-js/pad-ansix923.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ../../node_modules/crypto-js/pad-iso10126.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ../../node_modules/crypto-js/pad-iso97971.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./src/browser_window/DefaultBrowserWindow.ts ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./src/function/queue_promise/QueuePromise.ts ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./src/old_account_system/OldAccountClient.ts ***!
  \****************************************************/

/*!*****************************************************!*\
  !*** ../../node_modules/crypto-js/enc-base64url.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ../../node_modules/crypto-js/pad-nopadding.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ../../node_modules/crypto-js/rabbit-legacy.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/crypto_utility/cipher_algorithms/AES.ts ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/decorators/base/BaseDecoratorUtility.ts ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/event_emitter/RecordAbleEventEmitter.ts ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/oauth2client/CredentialsTableManager.ts ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/old_account_system/DeviceSignUtility.ts ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/process_remote/RendererProcessRemote.ts ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/request/request_client/RequestClient.ts ***!
  \*****************************************************/

/*!******************************************************!*\
  !*** ../../node_modules/i18next/dist/cjs/i18next.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./src/database_manager/KeyValueTableManager.ts ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./src/interfaces/IOAuth2ClientSignInHandler.ts ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./src/simple_interfaces/SimpleBrowserWindow.ts ***!
  \******************************************************/

/*!*******************************************************!*\
  !*** ../../node_modules/crypto-js/lib-typedarrays.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ../../node_modules/crypto-js/pad-zeropadding.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/crypto_utility/cipher_algorithms/index.ts ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/index.ts ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/interfaces/IOAuth2ClientRequestHandler.ts ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/response_error/ResponseErrorUriUtility.ts ***!
  \*******************************************************/

/*!********************************************************!*\
  !*** ../../node_modules/crypto-js/mode-ctr-gladman.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./src/analytics/analysis_report_service/index.ts ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./src/browser_window/MainProcessBrowserWindow.ts ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./src/request/base_request/BaseRequestUtility.ts ***!
  \********************************************************/

/*!*********************************************************!*\
  !*** ./src/function/queue_promise/QueuePromiseGroup.ts ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/response_error/exts/errror_details/index.ts ***!
  \*********************************************************/

/*!**********************************************************!*\
  !*** ./src/response_error/error_details/ErrorDetails.ts ***!
  \**********************************************************/

/*!***********************************************************!*\
  !*** ./src/analytics/analysis_report_service/Recorder.ts ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./src/function/queue_promise/JumpingQueuePromise.ts ***!
  \***********************************************************/

/*!************************************************************!*\
  !*** ./src/browser_window/RendererProcessBrowserWindow.ts ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./src/request/request_client/RequestClientUtility.ts ***!
  \************************************************************/

/*!*************************************************************!*\
  !*** ./src/analytics/analytics_manager/AnalyticsManager.ts ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./src/response_error/error_details/DebugInfoDetail.ts ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./src/response_error/error_details/ErrorInfoDetail.ts ***!
  \*************************************************************/

/*!**************************************************************!*\
  !*** ./src/interfaces/IOAuth2ClientGetCredentialsHandler.ts ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./src/request/request_client/RequestFunctionWrapper.ts ***!
  \**************************************************************/

/*!****************************************************************!*\
  !*** ./src/function/queue_promise/JumpingQueuePromiseGroup.ts ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./src/old_account_system/models/OldAccountSessionInfo.ts ***!
  \****************************************************************/

/*!******************************************************************!*\
  !*** ./src/crypto_utility/cipher_algorithms/CipherAlgorithms.ts ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./src/main/captcha-token-info-manager/LocalCaptchaToken.ts ***!
  \******************************************************************/

/*!*******************************************************************!*\
  !*** ./src/analytics/analysis_report_service/decorators/index.ts ***!
  \*******************************************************************/

/*!********************************************************************!*\
  !*** ./src/old_account_system/models/OldAccountSessionInfoData.ts ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./src/response_error/error_details/LocalizedMessageDetail.ts ***!
  \********************************************************************/

/*!*********************************************************************!*\
  !*** ./src/analytics/analytics_manager/AnalyticsRecordProcesser.ts ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./src/analytics/analytics_manager/AnalyticsReportProcesser.ts ***!
  \*********************************************************************/

/*!**********************************************************************!*\
  !*** ./src/response_error/decorators/update_response_error/index.ts ***!
  \**********************************************************************/

/*!***********************************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/Sqlite3DatabaseClient.ts ***!
  \***********************************************************************/

/*!************************************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/Sqlite3StatementClient.ts ***!
  \************************************************************************/

/*!************************************************************************!*\
  !*** ./src/main/captcha-token-info-manager/CaptchaTokenInfoManager.ts ***!
  \************************************************************************/

/*!*************************************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/Sqlite3StatementUtility.ts ***!
  \*************************************************************************/

/*!***************************************************************************!*\
  !*** ./src/response_error/exts/errror_details/OriginalErrorInfoDetail.ts ***!
  \***************************************************************************/

/*!****************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/AnalyticsInfoTableManager.ts ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/index.ts ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/Sqlite3StatementExtUtility.ts ***!
  \****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/common.ts ***!
  \*****************************************************************************/

/*!******************************************************************************!*\
  !*** ./src/browser_window/RendererProcessBrowserWindowMainProcessHandler.ts ***!
  \******************************************************************************/

/*!************************************************************************************!*\
  !*** ./src/response_error/decorators/update_response_error/UpdateResponseError.ts ***!
  \************************************************************************************/

/*!**************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/AnalyticsInfoRecordProcesserHandler.ts ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/AnalyticsInfoReportProcesserHandler.ts ***!
  \**************************************************************************************/

/*!*********************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/decorators/record_function_call_info/index.ts ***!
  \*********************************************************************************************/

/*!*******************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/RequestSpecificPartAnalyticsInfo.ts ***!
  \*******************************************************************************************************/

/*!**************************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/decorators/record_function_call_info/RecordFunctionCallInfo.ts ***!
  \**************************************************************************************************************/

/*!**************************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/EventPublisherSpecificPartAnalyticsInfo.ts ***!
  \**************************************************************************************************************/

/*!**************************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/FunctionResultSpecificPartAnalyticsInfo.ts ***!
  \**************************************************************************************************************/

/*!*****************************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/FunctionBeginCallSpecificPartAnalyticsInfo.ts ***!
  \*****************************************************************************************************************/

/**
 * @license
 * Copyright 2019 Xbase labs
 *
 * @description
 * The auth service of xbase.
 */

/**
* @vue/compiler-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/

/**
* @vue/compiler-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/

/** @preserve
	 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
	 * derived from CryptoJS.mode.CTR
	 * <NAME_EMAIL>
	 */

/** @preserve
	(c) 2012 by Cédric Mesnil. All rights reserved.

	Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

	    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
	    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
	*/

/**!
* tippy.js v6.3.7
* (c) 2017-2021 atomiks
* MIT License
*/