# pack with zip 
SET(<PERSON><PERSON><PERSON>_<PERSON><PERSON>KAGE_NAME THUNDER)
SET(CPACK_PACKAGE_VERSION ${THUNDER_PACKAGE_VERSION}) 
set(CPACK_PACKAGE_CONTACT  "THUNDER team")
set(<PERSON><PERSON><PERSON>_<PERSON><PERSON>KAGE_VENDOR   "THUNDER team")
set(CPACK_PA<PERSON>KAGE_DESCRIPTION_SUMMARY "Wondershare business layer.")
set(CPACK_PACKAGE_DESCRIPTION "Wondershare business layer.")
# set(CPACK_RESOURCE_FILE_LICENSE ${CMAKE_SOURCE_DIR}/LICENSE)
set(CPACK_RESOURCE_FILE_README ${CMAKE_SOURCE_DIR}/README.md)
SET(CPACK_DEBIAN_PACKAGE_MAINTAINER "THUNDER-TEARM")
SET(CPACK_OUTPUT_FILE_PREFIX ${CMAKE_SOURCE_DIR}/packages)

if (WIN32)
	set(CPACK_GENERATOR "ZIP")
elseif (APPLE)
	set(CP<PERSON><PERSON>_GENERATOR "ZIP")
	set(CP<PERSON>K_SYSTEM_NAME "OSX")
else (UNIX)
	message (STATUS "Not supported packaging for the ${CMAKE_SYSTEM_NAME} system.")
endif ()

include(CPack)