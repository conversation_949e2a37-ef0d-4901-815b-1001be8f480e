#pragma  once

class CVerifyDigitalSignature
{
public:
	CVerifyDigitalSignature();
	~CVerifyDigitalSignature();

	static BOOL CheckFileTrust( LPCWSTR lpFileName );

	//static BOOL CheckSerialNumber(const TCHAR *ptchPath, const TCHAR* ptchSerialNumber);

	static BOOL GetOwnerName(const TCHAR* fileName, TCHAR* ownerBuff);

	static BOOL CheckOwnerName(const TCHAR* patchPath, const TCHAR* patchOwnerName);

	static BOOL GetFileSign(const TCHAR* filename, TCHAR* buff, DWORD len);

private:
	static int	   s_nCheckSign;
};