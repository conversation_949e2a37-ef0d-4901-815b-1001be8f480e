
export function isEmpty(val): boolean {
  if (Array.isArray(val)) return !val.length
  if (isPlainObject(val)) return !Object.keys(val).length
  return val === '' || val === null || typeof val === 'undefined'
}

export function isGolangEmpty(val): boolean {
  if (Array.isArray(val)) return !val.length
  if (isPlainObject(val)) return !Object.keys(val).length
  if (isNumber(val)) return val === 0
  return val === '' || val === null || typeof val === 'undefined'
}

export function isArray(val: any): val is Array<any> {
  return Array.isArray(val)
}
export function isNull(value: any): value is null {
  return value === null
}

/**
 * 生成 isXXX 类型的检测函数
 * @param {string} type 类型名称
 * @returns {Function} 类型检测函数
 */
function isType<T>(type: string) {
  return function isTypeMethod(value: any): value is T {
    return typeof value === type
  }
}

function isTypeString<T = any>(type: string) {
  return function isTypeStringMethod(value: any): value is T {
    return Object.prototype.toString.call(value) === `[object ${type}]`
  }
}

export const isUndefined = isType<undefined>('undefined')

export const isString = isType<string>('string')

export const isNumber = isType<number>('number')

export const isFunction = isType<Function>('function')

export function isNil(value: any): value is null | undefined {
  return isNull(value) || isUndefined(value)
}

export function isObject(value: any) {
  return typeof value === 'object' && !isNull(value)
}

export const isPlainObject = isTypeString('Object')

export const isDate = isTypeString<Date>('Date')

export const isRegExp = isTypeString<RegExp>('RegExp')

/**
 * 判断对象上是否存在某个属性
 * @param {object} obj 检测的对象
 * @param {string} key 检测的属性
 * @returns {boolean} true: 存在, false: 不存在
 */
export function isKey(obj: object, key: string) {
  return key in obj
}

export function isStringEmpty(str: any): boolean {
  if (isNil(str)) return true
  if (isString(str) && str.length === 0) return true
  return false
}

export function isPromise(val: any): val is Promise<any> {
  if (!isNil(val) && typeof val === 'object' && typeof val.then === 'function') {
    return true
  }
  return false
}
