import { CallApiProxy } from '@root/common/call-api';
import { EventEmitter } from 'events';
import * as BaseType from '../base';

export class AplayerPlayList {
  private apiProxy: CallApiProxy | null = null;
  private eventContainor: EventEmitter = new EventEmitter();

  public async initApiProxy(apiProxy: CallApiProxy): Promise<void> {
    this.apiProxy = apiProxy;
    this.apiProxy!.AttachServerEvent('AplayerPlayListPrepared', () => {
      this.eventContainor.emit('AplayerPlayListPrepared');
    });
    this.apiProxy!.AttachServerEvent('AplayerPlayListSelectChange', (id: string) => {
      this.eventContainor.emit('AplayerPlayListSelectChange', id);
    });
    this.apiProxy!.AttachServerEvent('AplayerPlayListItemAdd', (bSucc: boolean, item: BaseType.PlayListItem) => {
      this.eventContainor.emit('AplayerPlayListItemAdd', bSucc, item);
    });
    this.apiProxy!.AttachServerEvent('AplayerPlayListItemDelete', (id: string) => {
      this.eventContainor.emit('AplayerPlayListItemDelete', id);
    });
  }

  public attachPlayListPreparedEvent(cb: () => void): void {
    this.eventContainor.on('AplayerPlayListPrepared', cb);
  }

  public detachPlayListPreparedEvent(cb: () => void): void {
    this.eventContainor.off('AplayerPlayListPrepared', cb);
  }

  public attachPlayListSelectChangeEvent(cb: (id: string) => void): void {
    this.eventContainor.on('AplayerPlayListSelectChange', cb);
  }

  public detachPlayListSelectChangeEvent(cb: (id: string) => void): void {
    this.eventContainor.off('AplayerPlayListSelectChange', cb);
  }

  public attachPlayListItemDeleteEvent(cb: (id: string) => void): void {
    this.eventContainor.on('AplayerPlayListItemDelete', cb);
  }

  public detachPlayListItemDeleteEvent(cb: (id: string) => void): void {
    this.eventContainor.off('AplayerPlayListItemDelete', cb);
  }

  public attachPlayListItemAddEvent(cb: (bSucc: boolean, item?: BaseType.PlayListItem) => void): void {
    this.eventContainor.on('AplayerPlayListItemAdd', cb);
  }

  public detachPlayListItemAddEvent(cb: (bSucc: boolean, item?: BaseType.PlayListItem) => void): void {
    this.eventContainor.off('AplayerPlayListItemAdd', cb);
  }

  public async getPlayList(): Promise<BaseType.PlayListItem[]> {
    let info = await this.apiProxy!.CallApi('AplayerPlayListGetPlayList');
    if (info.bSucc) {
      return info.result as BaseType.PlayListItem[];
    }
    return [];
  }

  public async deletePlayListItem(id: string): Promise<void> {
    await this.apiProxy!.CallApi('AplayerPlayListDeletePlayListItem', id);
  }

  public async playItem(id: string): Promise<void> {
    await this.apiProxy!.CallApi('AplayerPlayListPlayItem', id);
  }

  public async playNext(): Promise<void> {
    await this.apiProxy!.CallApi('AplayerPlayListPlayNext');
  }

  public async playPrev(): Promise<void> {
    await this.apiProxy!.CallApi('AplayerPlayListPlayPrev');
  }

  public async getPlayingItemId(): Promise<string> {
    let info = await this.apiProxy!.CallApi('AplayerPlayListGetPlayingItemId');
    if (info.bSucc) {
      return info.result as string;
    }
    return '';
  }

  public async addLocalItem(name: string, filePath: string): Promise<void> {
    await this.apiProxy!.CallApi('AplayerPlayListAddLocalItem', name, filePath);
  }

  public async clear(): Promise<void> {
    await this.apiProxy!.CallApi('AplayerPlayListClear');
  }

  public async getItemMediaInfo(id: string): Promise<BaseType.PlayListItemMediaInfo> {
    let info = await this.apiProxy!.CallApi('AplayerPlayListGetItemMediaInfo', id);
    if (info.bSucc) {
      return info.result as BaseType.PlayListItemMediaInfo;
    }
    return { duration: 0, pos: 0, size: 0, local: false, snapshot: '' };
  }

  public async isNextLocalPlay(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerPlayListIsNextLocalPlay');
    if (info.bSucc) {
      return info.result as boolean;
    }
    return false;
  }

  public async isPrevLocalPlay(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerPlayListIsPrevLocalPlay');
    if (info.bSucc) {
      return info.result as boolean;
    }
    return false;
  }

  public async isLocalPlay(id: string): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerPlayListIsLocalPlay', id);
    if (info.bSucc) {
      return info.result as boolean;
    }
    return false;
  }
}