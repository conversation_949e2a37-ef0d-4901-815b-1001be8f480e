/**
 * Converts a file size in bytes (B) to a human-readable format (B, KB, MB, GB, TB, etc.).
 * @param bytes - The file size in bytes.
 * @param decimals - The number of decimal places to include (default is 2).
 * @returns A string representing the human-readable file size.
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = bytes / Math.pow(k, i);

  return `${size.toFixed(decimals)}${sizes[i]}`;
}

