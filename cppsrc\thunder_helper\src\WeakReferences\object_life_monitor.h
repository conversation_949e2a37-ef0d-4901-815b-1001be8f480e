#ifndef OBJECT_LIFE_MONITOR_H_
#define OBJECT_LIFE_MONITOR_H_

#include <map>
#include <v8.h>
#include <node.h>
#include <uv.h>
#include <node_object_wrap.h>
#include "node_helper.h"
#include <AddonOpt.h>


class ObjectLifeMonitor {

public:
	static void BindTo(const v8::FunctionCallbackInfo<v8::Value>& args);
	static napi_value BindTo1(napi_env env, napi_callback_info info);
	static void FireDestructorCallbacks(uv_timer_t* handle);

protected:
    ObjectLifeMonitor(v8::Isolate* isolate, v8::Local<v8::Value> target, const v8::Local<v8::Function>& cbf, v8::Local<v8::Value> user_data);
    virtual ~ObjectLifeMonitor();

private:
    static void OnObjectGC(const v8::WeakCallbackInfo<ObjectLifeMonitor>& data);
    static void Free(const v8::WeakCallbackInfo<ObjectLifeMonitor>& data);
	void RunDestructorCallback();

protected:
	v8::Isolate *isolate_;
	v8::Persistent<v8::Function> cbf_;
	v8::Persistent<v8::Value> user_data_;
private:
    v8::Global<v8::Value> target_;
	//v8::Global<v8::Context> context_;

	typedef std::vector<ObjectLifeMonitor*> CacheObjects;
	typedef std::map< uv_timer_t*, CacheObjects> MapObjects;
	static MapObjects objects_;
};

#endif  // OBJECT_LIFE_MONITOR_H_
