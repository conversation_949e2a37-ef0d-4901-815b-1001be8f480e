<script setup lang="ts">
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import ContextMenu from '@root/common/components/ui/context-menu/index.vue'
import { ThunderUtil } from '@root/common/utils'
import { computed } from 'vue'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'
import { useThrottleFn } from '@vueuse/core'


const props = defineProps<{
  linkDetail: ThunderClientAPI.dataStruct.dataModals.LinkRecord
  index: number
  highlighted?: boolean
}>()

const emit = defineEmits<{
  (e: 'selectItem'): void
  (e: 'playItemLink'): void
  (e: 'removeItem'): void
}>()

const contextMenuList = computed(() => [
  ...(props?.linkDetail?.can_video_play ? [{
    key: 'play',
    label: '播放',
    icon: 'xl-icon-general-play-m1',
  }] : [{
    key: 'open',
    label: '打开',
    icon: 'xl-icon-general-openfile-m',
  }]),
  {
    key: ThunderNewTaskHelperNS.DownloadPathType.Local,
    label: '下载',
    icon: 'xl-icon-general-download-l"',
  },
  {
    key: ThunderNewTaskHelperNS.DownloadPathType.Cloud,
    label: '添加到云盘',
    icon: 'xl-icon-general-dump-m',
  },
  // {
  //   key: 'remove',
  //   label: '从链接中移除',
  //   icon: 'xl-icon-general-remove-m',
  // },
])

const disabled = computed(() => {
  return !!props?.linkDetail?.audit?.message
})

const handleDownload = (type: ThunderNewTaskHelperNS.DownloadPathType) => {
  console.log('handleDownload', props)
  const url = props?.linkDetail.url
  const realIndex = props?.linkDetail.file_index
  ThunderNewTaskHelperNS.showPreLinkCreateTaskWindow([{
    url: props?.linkDetail.url
  }], {
    selectedPathType: type,
    defaultCheckedFileIndexes: [`${url}-${realIndex}`],
    autoExpandAll: true,
    title: '添加文件',
  });
}

const handleItemAction = useThrottleFn((action: string) => {
  switch (action) {
    case 'open':
      emit('selectItem')
      break
    case 'play':
      emit('playItemLink')
      break
    case 'remove':
      emit('removeItem')
      break
    case ThunderNewTaskHelperNS.DownloadPathType.Local:
    case ThunderNewTaskHelperNS.DownloadPathType.Cloud:
      handleDownload(action)
      break
    default:
      break
  }
}, 1000, false, true)

const handleMenuSelect = (key: string) => {
  console.log('LinkDetailItem handleMenuSelect', key)
  handleItemAction(key)
}

const itemInfo = computed(() => {
  return `${ThunderUtil.bytesToSize(props?.linkDetail?.size, 2)}${props?.linkDetail?.is_dir ? ` · 共 ${props?.linkDetail?.selected_count}/${props?.linkDetail?.child_count} 个文件` : ''}`
})

</script>

<template>
  <ContextMenu :items="contextMenuList" @select="handleMenuSelect">
    <div class="detail-item" :class="{ 'highlighted': highlighted }" @dblclick="handleItemAction('open')">
      <div class="detail-item-left">
        <img v-if="linkDetail.icon_link" :src="linkDetail?.icon_link" />
        <div v-else class="file-icon-type file-type-default">
        </div>
      </div>
      <div class="detail-item-middle">
        <Tooltip :side-offset="25" :max-width="398" :align="'start'" :trigger-by-pointer="true" :delay-duration="1000"
          triggerClass="detail-item-middle-title">
          <template #trigger>
            <span class="detail-item-middle-title" @click.stop="handleItemAction('open')" @dblclick.stop>{{
              linkDetail.name
            }}</span>
          </template>
          <template #content>
            <span>{{ linkDetail.name }}</span>
          </template>
        </Tooltip>
        <span class="detail-item-middle-info">{{ itemInfo }}</span>
      </div>
      <div class="detail-item-right">

        <Button v-if="linkDetail.can_video_play" variant="ghost" :is-icon="true" size="sm" :disabled="disabled"
          @click.stop="handleItemAction('play')" @dblclick.stop v-tooltip="{
            content: '播放',
            placement: 'bottom',
          }">
          <i class="xl-icon-general-play-m1"></i>
        </Button>
        <Button v-else variant="ghost" :is-icon="true" size="sm" :disabled="disabled"
          @click.stop="handleItemAction('open')" @dblclick.stop v-tooltip="{
            content: '打开',
            placement: 'bottom',
          }">
          <i class="xl-icon-general-openfile-m"></i>
        </Button>
        <Button variant="ghost" :is-icon="true" size="sm"
          @click.stop="handleItemAction(ThunderNewTaskHelperNS.DownloadPathType.Local)" @dblclick.stop v-tooltip="{
            content: '下载',
            placement: 'bottom',
          }">
          <i class="xl-icon-download-l"></i>
        </Button>
        <Button variant="ghost" :is-icon="true" size="sm"
          @click.stop="handleItemAction(ThunderNewTaskHelperNS.DownloadPathType.Cloud)" @dblclick.stop v-tooltip="{
            content: '添加到云盘',
            placement: 'bottom',
          }">
          <i class="xl-icon-general-dump-m"></i>
        </Button>
      </div>
    </div>
  </ContextMenu>

</template>

<style scoped lang="scss">
.detail-item {
  display: flex;
  align-items: center;
  padding: 0px 18px;
  border-radius: var(--border-radius-L, 12px);
  height: 66px;

  &:hover {
    cursor: pointer;
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
  }

  &.highlighted {
    background: var(--button-button-lead-default, rgba(34, 109, 245, 0.10));
  }

  &-left {
    width: 40px;
    height: 40px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

  }

  &-middle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    min-width: 0;
    margin-left: 14px;
    margin-right: 32px;

    &-info {
      font-size: 12px;
      line-height: 16px;
      color: var(--font-font-3, #86909C);
    }

  }

  &-right {
    display: flex;
    align-items: center;
    gap: 16px;

    i {
      font-size: 20px;
    }
  }
}
</style>

<style lang="scss">
.detail-item-middle-title {
  font-size: 13px;
  line-height: 20px;
  color: var(--font-font-1, #272E3B);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;

  &:hover {
    color: var(--primary-primary-default, #226DF5);
  }
}

.link-item-tip-content {
  background: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
  height: 32px !important;
  padding: 0 12px !important;
  color: var(--white-white-900, #FFF) !important;
  line-height: 20px !important;
}

.link-item-tip-arrow {
  fill: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
}
</style>
