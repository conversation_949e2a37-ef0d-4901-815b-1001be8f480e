#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('=== 构建环境诊断 ===\n');

// 检查操作系统
console.log('操作系统信息:');
console.log(`  平台: ${os.platform()}`);
console.log(`  架构: ${os.arch()}`);
console.log(`  版本: ${os.release()}`);
console.log();

// 检查Node.js和npm版本
console.log('运行时信息:');
console.log(`  Node.js版本: ${process.version}`);
console.log(`  npm版本: ${process.env.npm_version || '未知'}`);
console.log();

// 检查SASS相关依赖
console.log('SASS依赖检查:');

// 检查sass
try {
  const sass = require('sass');
  console.log(`  ✓ sass: ${sass.info.split('\t')[0]}`);
} catch (e) {
  console.log(`  ✗ sass: 未安装 (${e.message})`);
}

// 检查sass-embedded
try {
  const sassEmbedded = require('sass-embedded');
  console.log(`  ✓ sass-embedded: 可用`);
} catch (e) {
  console.log(`  ✗ sass-embedded: 不可用 (${e.message})`);
}

console.log();

// 检查关键文件
console.log('关键文件检查:');
const keyFiles = [
  'src/common/assets/css/mixins.scss',
  'src/player-plugin/rsbuild.config.ts',
  'src/thunder-pan-plugin/rsbuild.config.ts',
  'src/player-plugin/entrys/renderer/xmp-player/components/playlist-item.vue'
];

keyFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`  ✓ ${file}`);
  } else {
    console.log(`  ✗ ${file}: 文件不存在`);
  }
});

console.log();

// 检查node_modules
console.log('依赖目录检查:');
const depDirs = [
  'node_modules/sass',
  'node_modules/sass-embedded',
  'node_modules/@rsbuild/plugin-sass'
];

depDirs.forEach(dir => {
  const dirPath = path.join(process.cwd(), dir);
  if (fs.existsSync(dirPath)) {
    console.log(`  ✓ ${dir}`);
  } else {
    console.log(`  ✗ ${dir}: 目录不存在`);
  }
});

console.log();

// Windows特定检查
if (os.platform() === 'win32') {
  console.log('Windows特定检查:');
  
  // 检查路径长度
  const currentPath = process.cwd();
  console.log(`  工作目录长度: ${currentPath.length} 字符`);
  if (currentPath.length > 200) {
    console.log(`  ⚠️  路径较长，可能导致问题`);
  }
  
  // 检查路径中的特殊字符
  if (/[^\x00-\x7F]/.test(currentPath)) {
    console.log(`  ⚠️  路径包含非ASCII字符，可能导致问题`);
  }
  
  // 检查权限 (简单检查)
  try {
    const testFile = path.join(process.cwd(), '.write-test');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    console.log(`  ✓ 写入权限正常`);
  } catch (e) {
    console.log(`  ✗ 写入权限问题: ${e.message}`);
  }
}

console.log('\n=== 诊断完成 ===');

// 提供建议
console.log('\n建议:');
console.log('1. 确保sass和sass-embedded都已正确安装');
console.log('2. 在Windows上，尽量使用较短的路径');
console.log('3. 确保有足够的磁盘空间和权限');
console.log('4. 如果使用杀毒软件，将项目目录加入白名单'); 