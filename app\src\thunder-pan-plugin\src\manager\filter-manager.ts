import EventEmitter from "events"
import { reactive } from "vue"

export interface IFilterData {
  type: string
  filters: string[]
  isCheckAll: boolean
}

export interface IFilterItem {
  val: string
  label: string
}

export interface IFilterSource {
  title: string
  checkAllTitle: string
  filters: IFilterItem[]
}

export interface IGetFilterForServiceResponse {
  phase?: any
  mime_type?: any
}

export interface IFilterSetting {
  enable: boolean
}

export interface ICurrentFilterData {
  sources: IFilterSource[]
  categoryFilter: IFilterData[]
  setting: IFilterSetting
  count: number
}

/**
 * 全局过滤器管理器
 */
export class FilterManager extends EventEmitter {
  private static _instance: FilterManager

  static getInstance () {
    if (FilterManager._instance) {
      return FilterManager._instance
    } else {
      FilterManager._instance = new FilterManager()
      return FilterManager._instance
    }
  }

  static EventKey = {
    FILTERS_UPDATED: 'filters_updated',
    SETTING_UPDATED: 'setting_updated',
  }

  static taskStatusFilter = [
    {
      title: '文件状态',
      checkAllTitle: '全部状态',
      filters: [
        { val: 'PHASE_TYPE_COMPLETE', label: '已完成' },
        { val: 'PHASE_TYPE_PENDING,PHASE_TYPE_RUNNING', label: '添加中' },
        { val: 'PHASE_TYPE_ERROR', label: '添加失败' },
      ],
    }
  ]

  static fileTypeFilter = [
    {
      title: '文件类型',
      checkAllTitle: '全部类型',
      filters: [
        { val: 'video', label: '视频' },
        { val: 'audio', label: '音频' },
        { val: 'image', label: '图片' },
        { val: 'text', label: '文本' },
        { val: 'font', label: '字体' },
        { val: 'subtitle', label: '字幕' },
        { val: 'installer', label: '安装包' },
        { val: 'archive', label: '压缩包' },
        { val: 'document', label: '文档' },
        { val: 'bt', label: '种子' },
        { val: 'unknown', label: '其他' },
      ],
    }
  ]

  static defaultFileTypeCategoryFilterMap = [{ type: 'file-type', filters: [], isCheckAll: true, }]
  static defaultTaskStatusCategoryFilterMap = [{ type: 'task-status', filters: [], isCheckAll: true, }]
  static defaultFilterSetting = { enable: true }

  // 当前所在 tab/文件夹id 的过滤数据情况
  private current: ICurrentFilterData = reactive<ICurrentFilterData>({
    sources: FilterManager.fileTypeFilter,
    categoryFilter: FilterManager.defaultFileTypeCategoryFilterMap,
    setting: FilterManager.defaultFilterSetting,
    count: 0
  })
  // 过滤面板展示的过滤数据项
  private sourcesMap: Map<string, IFilterSource[]> = new Map()
  // 过滤面板设置（是否启用）
  private settingMap: Map<string, IFilterSetting> = new Map()
  // 实际选中的过滤条件
  private categoryFilterMap: Map<string, IFilterData[]> = new Map()

  constructor () {
    super()
  }

  init () {
    // 初始化
    this.categoryFilterMap.set('all_', FilterManager.defaultFileTypeCategoryFilterMap)
    this.categoryFilterMap.set('cloud-add_', FilterManager.defaultTaskStatusCategoryFilterMap)
    this.categoryFilterMap.set('transfer-file_', FilterManager.defaultFileTypeCategoryFilterMap)

    this.sourcesMap.set('all_', FilterManager.fileTypeFilter)
    this.sourcesMap.set('cloud-add_', FilterManager.taskStatusFilter)
    this.sourcesMap.set('transfer-file_', FilterManager.fileTypeFilter)

    this.settingMap.set('all_', FilterManager.defaultFilterSetting)
    this.settingMap.set('cloud-add_', FilterManager.defaultFilterSetting)
    this.settingMap.set('transfer-file_', FilterManager.defaultFilterSetting)
  }

  reset () {
    this.sourcesMap.clear()
    this.settingMap.clear()
    this.categoryFilterMap.clear()
  }

  resetByKey (categoryKey: string, doNotEmit: boolean = false) {
    const oldFilter = this.categoryFilterMap.get(categoryKey)

    if (oldFilter) {
      const newFilter = oldFilter.map(category => ({ ...category, filters: [], isCheckAll: true }))
      this.updateFilterByKey(categoryKey, newFilter, doNotEmit)
    }
  }

  getCurrentFilterData () {
    return this.current
  }

  setCurrentCategory (categoryKey: string) {
    const sources = this.sourcesMap.get(categoryKey) || FilterManager.fileTypeFilter
    const setting = this.settingMap.get(categoryKey) || FilterManager.defaultFilterSetting
    const categoryFilter = this.categoryFilterMap.get(categoryKey) || FilterManager.defaultFileTypeCategoryFilterMap

    this.current.sources = sources
    this.current.setting = setting
    this.current.categoryFilter = categoryFilter
    this.current.count = this._getFilterCount(categoryFilter)
  }

  updateSetting (categoryKey: string, newSetting: IFilterSetting) {
    // 更新记录
    this.settingMap.delete(categoryKey)
    this.settingMap.set(categoryKey, newSetting)
    // 更新当前设置
    this.current.setting = newSetting
    // 事件通知
    this.emit(FilterManager.EventKey.SETTING_UPDATED, categoryKey, newSetting)
  }

  updateFilterByKey (categoryKey: string, newFilter: IFilterData[], doNotEmit: boolean = false) {
    // 更新记录
    this.categoryFilterMap.delete(categoryKey)
    this.categoryFilterMap.set(categoryKey, newFilter)
    // 更新当前具体的过滤器值
    this.current.categoryFilter = newFilter
    this.current.count = this._getFilterCount(newFilter)
    // 事件通知
    if (!doNotEmit) {
      this.emit(FilterManager.EventKey.FILTERS_UPDATED, categoryKey, newFilter)
    }
  }

  getCurrentFilterForService (categoryKey: string) {
    const filterSet = this.categoryFilterMap.get(categoryKey) || FilterManager.defaultFileTypeCategoryFilterMap
    const filterResponse: IGetFilterForServiceResponse = {}

    filterSet.forEach(item => {
      if (!item.isCheckAll && item.filters.length) {
        const res = this._genFilterForService(item.type, item.filters)

        if (res.key) {
          Reflect.set(filterResponse, res.key, res.value)
        }
      }
    })

    return filterResponse
  }

  private _getFilterCount (filter: IFilterData[]) {
    return filter.map(c => c.filters.length).reduce((pre, cur) => (pre + cur), 0)
  }

  // eq：等于
  // ne：不等于
  // in：包含
  // gt：大于
  // ge：大于等于
  // lt：小于
  // le：小于等于
  private _genFilterForService (type: string, filters: string[]) {
    switch (type) {
      case 'task-status': {
        return {
          key: 'phase',
          value: {
            in: filters.join(',')
          }
        }
      }
      case 'file-type': {
        return {
          key: 'category',
          value: {
            in: filters
          }
        }
      }
      default: {
        return {}
      }
    }
  }
}
