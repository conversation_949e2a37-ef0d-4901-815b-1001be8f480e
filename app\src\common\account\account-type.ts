import {
  IUserProfile,
  IUserProfileVipInfoItem
} from '@xbase/electron_auth_kit'

import {
  ResponseError,
} from '@xbase/electron_common_kit'

export interface ISignUpParams {
  // 手机号
  phoneNumber: string
  // 密码
  password: string
  // 验证码
  verificationCode: string
  // 发送验证码的 id
  verification_id: string
}

export interface ISignWithVerificationParams {
  // 是否为账号
  isUser: boolean
  // 账号
  username: string
  // 验证码
  verificationCode: string
  // 发送验证码的 id
  verification_id: string
}

export interface ISignInWithPasswordParams {
  // 账号
  username: string
  // 密码
  password: string
}

// 支付类型
enum IProductType {
  CONSUMABLE = 0,
  NONCONSUMABLE,
  AUTORENEWABLE,
}

// 业务层创建支付参数
export interface INativeCreatePurchaseParams {
  userId: string
  /**
   * sessionId或者accessToken。
   */
  sessionId: string
  productId: string
  productType: IProductType
  //extInfo: ICreatePurchaseExtInfo;
  // developerPayload?: string;
  // reservedInfo?: string;
}

export enum AccountHelperEventKey {
  // 登录成功事件
  SIGN_IN_SUCCESS = 'AccountHelper_EventKey_SignInSuccess',
  // 登录失败事件
  SIGN_IN_FAILURE = 'AccountHelper_EventKey_SignInFailure',
  // 登出事件
  SIGN_OUT = 'AccountHelper_EventKey_SignOut',
  // 用户信息变更
  USER_INFO_CHANGE = 'AccountHelper_EventKey_UserInfoChange',
  // SDK 初始化完成
  SDK_INIT_READY = 'AccountHelper_EventKey_SDK_InitReady',
  // 支付SDK 初始化完成
  PAY_SDK_INIT_READY = 'AccountHelper_EventKey_Pay_SDK_InitReady',
  // 刷新 CREDENTIALS
  REFRESH_CREDENTIALS = 'AccountHelper_EventKey_Refresh_Credentials',
  // 设备授权
  DEVICE_AUTHORIZATION = 'AccountHelper_EventKey_Device_Authorization',
  // mqtt 通道连接事件
  SYNC_CLIENT_EVENT_CONNECTED = 'AccountHelper_EventKey_Sync_Client_Connected',
  // mqtt 通道收到消息通知事件
  SYNC_CLIENT_EVENT_MESSAGE_ARRIVED = 'AccountHelper_EventKey_Sync_Client_Message_Arrived',
}

export interface UserInfo extends IUserProfile { }

export interface IUserVipInfo extends IUserProfileVipInfoItem { }

export enum VipType {
  normal = '0',
  normalVip = '2',
  platinum = '3',
  superv = '5',
}

export interface VipDetail {
  IsVIP: string
  VasType: VipType
  Start: string
  End: string
  SurplusDay: number
}

export interface VipsInfo {
  id: string
  expires_at: string
}

/**
 * session信息
 */
export interface SessionInfo {
  // 会话标志
  sessionId?: string
  // login key
  loginKey?: string
  secureKey?: string
  userId?: string
}

/**
 * 三方登录回调信息
 */
export interface ThirdLoginResultInfo {
  //response?: IProviderAccountAuthorizeResponseData;
  error?: ResponseError
}