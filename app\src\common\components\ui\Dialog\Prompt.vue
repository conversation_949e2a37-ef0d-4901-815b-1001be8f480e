<template>
  <Dialog
    :title="title"
    :variant="variant"
    :show-cancel="true"
    :confirm-text="confirmText"
    :cancel-text="cancelText"
    :show-title-icon="false"
    :open="open"
    :modal="modal"
    :show-trigger="false"
    :loading="loading"
    :prevent-default-close="true"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    @close="handleClose"
    @update:open="handleUpdateOpen"
  >
    <div
      :style="containerStyle"
      :class="containerClass"
    >
      <!-- 显示当前值提示（如果有） -->
      <div
        v-if="currentValue"
        style="margin-bottom: 12px; color: #666; font-size: 14px"
      >
        当前: {{ currentValue }}
      </div>

      <!-- 输入框 -->
      <input
        v-if="inputType !== 'textarea'"
        v-bind="inputProps"
        ref="inputRef"
        :type="inputType"
        :value="modelValue"
        :placeholder="placeholder"
        :style="computedInputStyle"
        :class="inputClass"
        @input="handleInput"
        @keydown="handleKeydown"
      />

      <!-- 文本区域 -->
      <textarea
        v-else
        v-bind="inputProps"
        ref="inputRef"
        :value="modelValue"
        :placeholder="placeholder"
        :style="computedInputStyle"
        :class="inputClass"
        @input="handleInput"
        @keydown="handleKeydown"
      ></textarea>

      <!-- 错误信息 -->
      <div
        class="error-message-container"
        :class="{
          'has-error': errorMessage,
          'fixed-height': fixedHeight,
        }"
      >
        <div
          class="error-message-content"
          v-if="errorMessage"
        >
          {{ errorMessage }}
        </div>
      </div>

      <!-- 提示信息 -->
      <div
        v-if="hint"
        style="margin-top: 8px; color: #666; font-size: 12px"
      >
        {{ hint }}
      </div>
    </div>
  </Dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import Dialog from './Dialog.vue'

// 定义 props
const props = defineProps({
  // Dialog 相关 props
  title: {
    type: String,
    default: '输入',
  },
  variant: {
    type: String,
    default: 'thunder',
  },
  confirmText: {
    type: String,
    default: '确定',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  open: {
    type: Boolean,
    default: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  modal: {
    type: Boolean,
    default: false,
  },

  // 输入框相关 props
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  inputType: {
    type: String,
    default: 'text',
  },
  inputStyle: {
    type: [Object, String],
    default: null,
  },
  inputClass: {
    type: String,
    default: '',
  },
  containerStyle: {
    type: [Object, String],
    default: () => ({ margin: '20px 0' }),
  },
  containerClass: {
    type: String,
    default: '',
  },
  inputProps: {
    type: Object,
    default: () => ({}),
  },

  // 验证相关 props
  errorMessage: {
    type: String,
    default: '',
  },

  // 其他 props
  currentValue: {
    type: String,
    default: '',
  },
  hint: {
    type: String,
    default: '',
  },
  selectAll: {
    type: Boolean,
    default: true,
  },

  // 布局控制 props
  fixedHeight: {
    type: Boolean,
    default: false,
  },

  // 回调函数 props
  onConfirmCallback: {
    type: Function,
    default: null,
  },
  validator: {
    type: Function,
    default: null,
  },
})

// 定义 emits
const emit = defineEmits([
  'update:modelValue',
  'update:open',
  'confirm',
  'cancel',
  'close',
  'input-change',
  'keydown',
  'validation-error',
  'loading-start',
  'loading-end',
])

// 引用
const inputRef = ref(null)

// 计算属性 - 输入框样式
const computedInputStyle = computed(() => {
  const defaultStyle = {
    width: '100%',
    padding: '14px',
    border: props.errorMessage ? '2px solid #dc3545' : '2px solid #4a90e2',
    borderRadius: '8px',
    fontSize: '16px',
    boxSizing: 'border-box',
    outline: 'none',
    transition: 'all 0.3s ease',
    backgroundColor: props.errorMessage ? '#fff8f8' : '#f8f9fa',
  }

  // 为textarea添加特殊的默认样式
  if (props.inputType === 'textarea') {
    defaultStyle.resize = 'vertical'
    defaultStyle.minHeight = '120px'
    defaultStyle.lineHeight = '1.5'
    defaultStyle.fontFamily = 'inherit'
  }

  if (!props.inputStyle) {
    return defaultStyle
  }

  if (typeof props.inputStyle === 'string') {
    return props.inputStyle
  }

  return { ...defaultStyle, ...props.inputStyle }
})

// 事件处理
const handleInput = e => {
  const value = e.target.value
  emit('update:modelValue', value)
  emit('input-change', value, e)
}

const handleKeydown = e => {
  emit('keydown', e)

  // 保留原有的快捷键功能
  if (props.inputType === 'textarea') {
    // 在textarea中，Ctrl+Enter 或 Cmd+Enter 触发确认
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault() // 阻止默认的换行行为
      handleConfirm()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  } else {
    // 在普通input中，Enter触发确认
    if (e.key === 'Enter') {
      handleConfirm()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }
}

const handleConfirm = async () => {
  // 如果有验证函数，先进行验证
  if (props.validator) {
    const result = props.validator(props.modelValue)
    if (!result.valid) {
      // 通过事件传递验证错误，让父组件更新 errorMessage
      emit('validation-error', result.message)
      return
    }
  }

  // 如果有自定义的 onConfirm 回调，执行异步逻辑
  if (props.onConfirmCallback) {
    try {
      emit('loading-start') // 通知父组件开始加载
      const shouldClose = await props.onConfirmCallback(props.modelValue)

      // 如果回调返回 false，则不关闭对话框
      if (shouldClose === false) {
        emit('loading-end') // 通知父组件结束加载
        return
      }
    } catch (error) {
      console.error('确认回调执行失败:', error)
      emit('loading-end') // 通知父组件结束加载
      return
    }
  }

  // 如果一切正常，发送确认事件
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}

const handleClose = () => {
  emit('close')
}

const handleUpdateOpen = value => {
  emit('update:open', value)
}

// selectAll（不包含扩展名）
const getValueWithoutExtension = value => {
  const lastDotIndex = value.lastIndexOf('.')
  return lastDotIndex === -1 ? value : value.slice(0, lastDotIndex)
}

// 自动聚焦功能
const focusInput = () => {
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus()
      if (props.selectAll) {
        const value = getValueWithoutExtension(props.modelValue)
        console.log('111111', value)
        inputRef.value.setRangeText(value, 0, value.length, 'select')
      }
    }
  })
}

// 监听 open 状态变化，自动聚焦
watch(
  () => props.open,
  newValue => {
    if (newValue) {
      setTimeout(focusInput, 100)
    }
  },
  { immediate: true }
)

// 组件挂载后自动聚焦
onMounted(() => {
  if (props.open) {
    focusInput()
  }
})

// 暴露方法
defineExpose({
  focus: focusInput,
  inputRef,
})
</script>

<style scoped>
/* 错误信息容器 - 方案1: 平滑过渡 */
.error-message-container {
  min-height: 0;
  max-height: 0;
  margin-top: 8px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.error-message-container.has-error {
  min-height: 20px;
  max-height: 60px; /* 足够显示多行错误信息 */
}

/* 方案2: 固定高度 - 始终预留空间 */
.error-message-container.fixed-height {
  min-height: 24px;
  max-height: 24px; /* 固定高度，始终占位 */
  transition: none; /* 不需要过渡动画 */
}

.error-message-content {
  padding: 2px 0;
  transform: translateY(-8px);
  color: #dc3545;
  font-size: 12px;
  line-height: 1.4;
  opacity: 0;
  transition: all 0.25s ease;
}

/* 错误信息显示时的动画 */
.has-error .error-message-content {
  transform: translateY(0);
  opacity: 1;
}

/* 占位符文本，在固定高度模式下显示 */
.error-message-container.fixed-height:not(.has-error)::after {
  padding: 2px 0;
  content: '\00A0'; /* 不间断空格占位 */
  color: transparent;
  font-size: 12px;
  line-height: 1.4;
}

/* 优化输入框的焦点状态过渡 */
input,
textarea {
  transition: all 0.25s ease !important;
}

/* 输入框错误状态的动画 */
input.error,
textarea.error {
  animation: shake 0.4s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(4px);
  }
}
</style>
