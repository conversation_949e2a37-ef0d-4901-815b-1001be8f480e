import * as path from 'path'

export namespace FileExtNS {
  const allExt: string[] = [
    '.ape',
    '.au',
    '.cda',
    '.dts',
    '.flac',
    '.m1a',
    '.m2a',
    '.m4a',
    '.mka',
    '.mp2',
    '.mp3',
    '.mpc',
    '.mpa',
    '.ra',
    '.tta',
    '.wav',
    '.wma',
    '.wv',
    '.midi',
    '.mid',
    '.ogg',
    '.oga',
    '.xlmv',
    '.wmv',
    '.wmp',
    '.wm',
    '.vp6',
    '.vob',
    '.ts',
    '.tpr',
    '.tp',
    '.swf',
    '.smil',
    '.scm',
    '.rt',
    '.rpm',
    '.rmvb',
    '.rm',
    '.ram',
    '.qt',
    '.pva',
    '.pmp',
    '.ogx',
    '.ogv',
    '.ogm',
    '.mts',
    '.mpv2',
    '.mpg',
    '.mpeg4',
    '.mpeg2',
    '.mpeg1',
    '.mpeg',
    '.mpe',
    '.mp5',
    '.mp4',
    '.mp2v',
    '.mov',
    '.mod',
    '.mkv',
    '.mk5',
    '.m4v',
    '.m4r',
    '.m4p',
    '.m4b',
    '.m2v',
    '.m2ts',
    '.m2p',
    '.m1v',
    '.letv',
    '.hmp4',
    '.hmkv',
    '.hlv',
    '.hflv',
    '.flv',
    '.f5v',
    '.f4v',
    '.evo',
    '.dvd',
    '.divx',
    '.csf',
    '.bik',
    '.avi',
    '.asf',
    '.amv',
    '.amr',
    '.aiff',
    '.acc',
    '.ac3',
    '.aac',
    '.3gpp',
    '.3gp2',
    '.3gp',
    '.3g2',
    '.m3u8'
  ];

  const audioExt: string[] = [
    '.ape',
    '.au',
    '.cda',
    '.dts',
    '.flac',
    '.m1a',
    '.m2a',
    '.m4a',
    '.mka',
    '.mp2',
    '.mp3',
    '.mpc',
    '.mpa',
    '.ra',
    '.tta',
    '.wav',
    '.wma',
    '.wv',
    '.midi',
    '.mid',
    '.ogg',
    '.oga',
  ];
  let setAudioExt: Set<string> = new Set();

  const videoExt: string[] = [
    '.xlmv',
    '.wmv',
    '.wmp',
    '.wm',
    '.vp6',
    '.vob',
    '.ts',
    '.tpr',
    '.tp',
    '.swf',
    '.smil',
    '.scm',
    '.rt',
    '.rpm',
    '.rmvb',
    '.rm',
    '.ram',
    '.qt',
    '.pva',
    '.pmp',
    '.ogx',
    '.ogv',
    '.ogm',
    '.mts',
    '.mpv2',
    '.mpg',
    '.mpeg4',
    '.mpeg2',
    '.mpeg1',
    '.mpeg',
    '.mpe',
    '.mp5',
    '.mp4',
    '.mp2v',
    '.mov',
    '.mod',
    '.mkv',
    '.mk5',
    '.m4v',
    '.m4r',
    '.m4p',
    '.m4b',
    '.m2v',
    '.m2ts',
    '.m2p',
    '.m1v',
    '.letv',
    '.hmp4',
    '.hmkv',
    '.hlv',
    '.hflv',
    '.flv',
    '.f5v',
    '.f4v',
    '.evo',
    '.dvd',
    '.divx',
    '.csf',
    '.bik',
    '.avi',
    '.asf',
    '.amv',
    '.amr',
    '.aiff',
    '.acc',
    '.ac3',
    '.aac',
    '.3gpp',
    '.3gp2',
    '.3gp',
    '.3g2',
    '.m3u8'
  ];
  let setVideoExt: Set<string> = new Set();

  const defaultAllExt: string[] = [
    '.3g2',
    '.3gp',
    '.3gp2',
    '.3gpp',
    '.aac',
    '.ac3',
    '.acc',
    '.aiff',
    '.amr',
    '.amv',
    '.ape',
    '.asf',
    '.ass',
    '.au',
    '.avi',
    '.bik',
    '.cda',
    '.csf',
    '.divx',
    '.dts',
    '.dvd',
    '.evo',
    '.f4v',
    '.flac',
    '.flv',
    '.hlv',
    '.m1a',
    '.m1v',
    '.m2a',
    '.m2p',
    '.m2ts',
    '.m2v',
    '.m4a',
    '.m4b',
    '.m4p',
    '.m4v',
    '.mid',
    '.midi',
    '.mka',
    '.mkv',
    '.mod',
    '.mov',
    '.mp2',
    '.mp2v',
    '.mp3',
    '.mp4',
    '.mpa',
    '.mpc',
    '.mpe',
    '.mpeg',
    '.mpeg1',
    '.mpeg2',
    '.mpeg4',
    '.mpg',
    '.mpv2',
    '.mts',
    '.oga',
    '.ogg',
    '.ogm',
    '.ogv',
    '.ogx',
    '.psb',
    '.pmp',
    '.pva',
    '.qt',
    '.ra',
    '.ram',
    '.rm',
    '.rmvb',
    '.rpm',
    '.rt',
    '.scm',
    '.smi',
    '.smil',
    '.srt',
    '.ssa',
    '.sub',
    '.swf',
    '.tp',
    '.tpr',
    '.ts',
    '.tta',
    '.usf',
    '.vob',
    '.vp6',
    '.wav',
    '.wm',
    '.wma',
    '.wmp',
    '.wmv',
    '.wv'
  ];

  const defaultAudioExt: string[] = [
    '.aac',
    '.ac3',
    '.acc',
    '.aiff',
    '.amr',
    '.ape',
    '.au',
    '.cda',
    '.dts',
    '.flac',
    '.m1a',
    '.m2a',
    '.m4a',
    '.mid',
    '.midi',
    '.mka',
    '.mod',
    '.mp2',
    '.mp3',
    '.mpa',
    '.mpc',
    '.oga',
    '.ogg',
    '.ra',
    '.tta',
    '.wav',
    '.wma',
    '.wv'
  ];

  const defaultVideoExt: string[] = [
    '.3g2',
    '.3gp',
    '.3gp2',
    '.3gpp',
    '.amv',
    '.asf',
    '.ass',
    '.avi',
    '.bik',
    '.csf',
    '.divx',
    '.dvd',
    '.evo',
    '.f4v',
    '.flv',
    '.hlv',
    '.m1v',
    '.m2p',
    '.m2ts',
    '.m2v',
    '.m4b',
    '.m4p',
    '.m4v',
    '.mkv',
    '.mov',
    '.mp4',
    '.mpe',
    '.mpeg',
    '.mpeg1',
    '.mpeg2',
    '.mpeg4',
    '.mpg',
    '.mpv2',
    '.mts',
    '.ogm',
    '.ogv',
    '.ogx',
    '.psb',
    '.pmp',
    '.pva',
    '.qt',
    '.ram',
    '.rm',
    '.rmvb',
    '.rpm',
    '.rt',
    '.scm',
    '.smi',
    '.smil',
    '.srt',
    '.ssa',
    '.sub',
    '.swf',
    '.tp',
    '.tpr',
    '.ts',
    '.usf',
    '.vob',
    '.vp6',
    '.wm',
    '.wmp',
    '.wmv'
  ];

  // 真正支持的视频文件，去除字幕等格式
  const trueVideoExt: string[] = [
    '.3g2',
    '.3gp',
    '.3gp2',
    '.3gpp',
    '.amv',
    '.asf',
    '.avi',
    '.bik',
    '.csf',
    '.divx',
    '.dvd',
    '.evo',
    '.f4v',
    '.flv',
    '.hlv',
    '.m1v',
    '.m2p',
    '.m2ts',
    '.m2v',
    '.m4b',
    '.m4p',
    '.m4v',
    '.mkv',
    '.mov',
    '.mp4',
    '.mpe',
    '.mpeg',
    '.mpeg1',
    '.mpeg2',
    '.mpeg4',
    '.mpg',
    '.mpv2',
    '.mts',
    '.ogm',
    '.ogv',
    '.ogx',
    '.pmp',
    '.pva',
    '.qt',
    '.ram',
    '.rm',
    '.rmvb',
    '.rpm',
    '.rt',
    '.scm',
    '.smil',
    '.swf',
    '.tp',
    '.tpr',
    '.ts',
    '.vob',
    '.vp6',
    '.wm',
    '.wmp',
    '.wmv'
  ];

  const popularVideoExt: string[] = [
    '.mp4',
    '.rmvb',
    '.mkv',
    '.avi',
    '.wmv',
    '.mpg',
    '.rm',
    '.xv',
    '.flv',
    '.mov',
    '.ts',
    '.3gp',
    '.m4v',
    '.asf',
    '.mpeg'
  ];

  export function getAllFileExt(): string[] {
    return allExt;
  }

  export function getAudioExt(): string[] {
    return audioExt;
  }

  export function getVideoExt(): string[] {
    return videoExt;
  }

  export function getAllDefaultExt(): string[] {
    return defaultAllExt;
  }

  export function getDefaultAudioExt(): string[] {
    return defaultAudioExt;
  }

  export function getDefaultVideoExt(): string[] {
    return defaultVideoExt;
  }

  export function getTrueVideoExt(): string[] {
    return trueVideoExt;
  }

  export function getPopularVideoExt(): string[] {
    return popularVideoExt;
  }

  export function isVideoExt(ext: string): boolean {
    if (setVideoExt.size === 0) {
      setVideoExt = new Set(videoExt);
    }

    return setVideoExt.has(ext);
  }

  export function isAudioExt(ext: string): boolean {
    if (setAudioExt.size === 0) {
      setAudioExt = new Set(audioExt);
    }
    return setAudioExt.has(ext);
  }

  export function isVideoFile(fileName: string): boolean {
    if (fileName === null || fileName === undefined || fileName === '') {
      return false;
    }
    // 迅雷下载的视频未完成文件
    let ext: string = path.extname(fileName);
    if (ext === '.xltd') {
      fileName = path.basename(fileName, ext);
      ext = path.extname(fileName);
      if (ext === '.bt' || ext === '.emule') {
        fileName = path.basename(fileName, ext);
        ext = path.extname(fileName);
      }
    }

    return isVideoExt(ext.toLowerCase()) || isAudioExt(ext.toLowerCase());
  }
}
