/**
 * 非模态窗口管理类
 * @description: 非模态窗口可复用窗口管理类，
 */

import { EventEmitter } from 'events';
import { is } from '@electron-toolkit/utils';
import { isShowLogger } from '@root/common/env';
import * as PopUpTypes from '@root/common/pop-up/types';
import { GetIcoPath } from '@root/common/xxx-node-path';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { WindowPosNS } from '@root/main/reuse-browser-window/window-pos';
import { attachShadowWindow, detachShadowWindow } from '@root/main/shadow-window';
import { BrowserWindow, WebContents, BrowserWindowConstructorOptions, OpenDevToolsOptions, screen, Rectangle } from 'electron';

const logger = (...args) => {
  const now = new Date();
  if (isShowLogger) {
    console.log(`[${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}：${now.getMilliseconds()}][ThunderPanServerSDK]`, ...args);
  }
}

function camelToKebab(str: string): string {
  return str
    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
    .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2')
    .toLowerCase()
}

export type ReuseWndOptions = {
  width: number;
  height: number;
  x?: number;
  y?: number;
  resizable?: boolean; // default true
  alwaysOnTop?: boolean; // default false
  show?: boolean; // default true
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
  backgroundColor?: string;
  transparent?: boolean;
};

function generateUuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c: any) => {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

const defaultWndOptions: BrowserWindowConstructorOptions = {
  frame: false,
  show: false,
  modal: false,
  backgroundColor: '#FFF',
  minWidth: 0,
  minHeight: 0,
  resizable: true,
  icon: GetIcoPath(),
  transparent: true,
  webPreferences: {
    // devTools: process.env.NODE_ENV !== undefined && process.env.NODE_ENV !== 'production',
    devTools: true,
    spellcheck: false,
    nodeIntegration: true,
    webSecurity: process.env.RUN_ENV !== 'development',
    webviewTag: true,
    contextIsolation: false,
    nodeIntegrationInSubFrames: true,
    backgroundThrottling: false,
  }
};

function getURL(): string {
  if (is.dev) {
    return `http://localhost:9529`;
  } else {
    return `file:///${__dirname}/modal-renderer/index.html`;
  }
}

class ReuseBrowserWindow extends EventEmitter {
  private _window: BrowserWindow; // tslint:disable-line
  private _readyToShow: boolean = false; // tslint:disable-line
  private _inUse: boolean; // tslint:disable-line
  private businessId: string = ''; // tslint:disable-line

  constructor(modal: boolean, parent?: BrowserWindow, transparent?: boolean) {
    super();
    logger('ReuseBrowserWindow constructor', modal);
    try {
      this._window = new BrowserWindow({ ...defaultWndOptions, modal, parent, transparent });
    } catch (error) {
      delete defaultWndOptions['icon'];
      this._window = new BrowserWindow({ ...defaultWndOptions, modal, parent, transparent });
    }
    // this._window.webContents.openDevTools();
    this._inUse = true;

    this._window.once('ready-to-show', () => {
      this._readyToShow = true;
    });

    let onClosed: any = (): void => {
      // this.emit('closed');
      // 这里先不触发closed事件，由窗口自己触发
      this.emit('abnormal-closed');
      this.removeAllListeners();
      this._window.removeListener('closed', onClosed);
    };

    // 窗口被异常关闭，也就是没有走里面的关闭流程
    this._window.on('closed', onClosed);
  }

  get id(): number | undefined {
    if (this._window && !this._window.isDestroyed()) {
      return this._window?.id;
    }
    return undefined;
  }

  get inUse(): boolean {
    return this._inUse;
  }

  set inUse(use: boolean) {
    this._inUse = use;
  }

  get readyToShow(): boolean {
    return this._readyToShow;
  }

  get window(): BrowserWindow {
    return this._window;
  }

  get webContents(): WebContents {
    return this._window?.webContents;
  }

  // 该方法不暴露给外部使用
  protected async loadURL(url: string): Promise<void> {
    const wndId: number = this._window.id;
    const urlObj: URL = new URL(url);
    urlObj.searchParams.set('boxId', String(wndId));
    await this._window.loadURL(urlObj.href);
  }

  public getBussinessId(): string {
    return this.businessId;
  }

  public updateBussinessId(id: string): void {
    this.businessId = id;
  }

  public openDevTools(options?: OpenDevToolsOptions): void {
    this._window.webContents.openDevTools(options);
  }

  isDestroyed(): boolean {
    let destroyed: boolean = true;
    do {
      if (!this._window) {
        break;
      }

      if (!this._inUse) {
        // 已经没有在使用，表示不再持有使用权
        break;
      }

      if (this._window.isDestroyed()) {
        break;
      }
      destroyed = false;
    } while (0);
    return destroyed;
  }

  isVisible(): boolean {
    return this._window?.isVisible();
  }

  isMinimized(): boolean {
    return this._window?.isMinimized();
  }

  isMaximized(): boolean {
    if (this._window && !this._window.isDestroyed()) {
      return this._window?.isMaximized();
    } else {
      return false;
    }
  }

  isFullScreen(): boolean {
    if (this._window && !this._window.isDestroyed()) {
      return this._window?.isFullScreen();
    } else {
      return false;
    }
  }

  isModal(): boolean {
    if (this._window && !this._window.isDestroyed()) {
      return this._window?.isModal();
    } else {
      return false;
    }
  }

  show(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window?.show();
    }
  }

  minimize(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window?.minimize();
    }
  }

  maximize(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window?.maximize();
    }
  }

  unmaximize(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window?.unmaximize();
    }
  }

  restore(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window?.restore();
    }
  }

  focus(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window?.focus();
    }
  }

  setPosition(x: number, y: number, animate?: boolean): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window.setPosition(x, y, animate);
    }
  }

  setTitle(title: string): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window.setTitle(title);
    }
  }

  center(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window.center();
    }
  }

  showInactive(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window.showInactive();
    }
  }

  getNativeWindowHandle(): Buffer {
    return this._window.getNativeWindowHandle();
  }

  isFocused(): boolean {
    return this._window.isFocused();
  }

  hide(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window?.hide();
    }
  }

  close(): void {
    detachShadowWindow(this.id!);
    this.webContents.send(`popup-wnd-destroy-${this.id}`);
    if (this.isMaximized()) {
      this.unmaximize();
    }
    if (this.isFullScreen()) {
      this.setFullScreen(false);
    }
    this.hide();
    this.emit('close', { sender: { id: this.id } });
    this.emit('closed');
    // 这里必须保证free是最后，因为要进行清理了
    this.emit('free');
  }

  destroy(): void {
    if (this._window && !this._window.isDestroyed()) {
      this._window?.destroy();
    }
  }

  getParentWindow(): BrowserWindow | null {
    return this._window?.getParentWindow();
  }

  setParentWindow(parent: (BrowserWindow) | (null)): void {
    this._window?.setParentWindow(parent);
  }

  setFullScreen(flag: boolean): void {
    this._window?.setFullScreen(flag);
  }

  setFullScreenable(fullscreenable: boolean): void {
    this._window?.setFullScreenable(fullscreenable);
  }

  moveTop(): void {
    this._window?.moveTop();
  }

  updateView(componentsName: string, title: string, options: any, dialogConf?: BrowserWindowConstructorOptions): void {
    do {
      if (!this._window || this._window.isDestroyed()) {
        break;
      }

      this.webContents.send(`popup-wnd-notify-${this.id}`, {
        componentsName,
        title,
        options,
        dialogConf
      });
    } while (0);
  }

  updateOptions(options: BrowserWindowConstructorOptions, relatePos: PopUpTypes.RelatePosType, relativeParent: BrowserWindow, autoSize: boolean): void {
    let bound = this._window.getBounds();
    if (options.x) {
      bound.x = options.x;
    }
    if (options.y) {
      bound.y = options.y;
    }
    bound.width = options.width ? options.width : 500;
    bound.height = options.height ? options.height : 500;
    this._window.setResizable(true);
    const minWidth = options.minWidth ? options.minWidth : 1;
    const minHeight = options.minHeight ? options.minHeight : 1;
    this._window.setMinimumSize(minWidth, minHeight);
    if (!options.maximizable) {
      let maxWidth: number = options.maxWidth ?? 0;
      let maxHeight: number = options.maxHeight ?? 0;
      if (!options.maxWidth || !options.maxHeight) {
        const display = screen.getPrimaryDisplay();
        const size = display.workAreaSize;
        maxWidth = maxWidth || size.width;
        maxHeight = maxHeight || size.height;
      }
      this._window.setMaximumSize(maxWidth || bound.width, maxHeight || bound.height);
    } else {
      this._window.setMaximumSize(9600, 5400);
    }
    // 如果指定了x和y，则窗口相对于屏幕偏移，默认屏幕居中
    if (relatePos === undefined || relatePos === null) {
      if (options.x && options.y) {
        relatePos = PopUpTypes.RelatePosType.Custom;
      } else {
        relatePos = PopUpTypes.RelatePosType.CenterScreen;
      }
    }
    if (!autoSize) {
      switch (relatePos) {
        case PopUpTypes.RelatePosType.CenterParent:
          {
            const parent = options.parent ?? relativeParent;
            if (parent) {
              const leftTop = WindowPosNS.getRelateCenterPos(parent, [bound.width, bound.height]);
              bound.x = leftTop.x;
              bound.y = leftTop.y;
            }
          }
          break;
        case PopUpTypes.RelatePosType.RightBottom:
          {
            // 弹出在右下角
            const leftTop = WindowPosNS.getRelateRightBottomPos([bound.width, bound.height]);
            bound.x = leftTop.x;
            bound.y = leftTop.y;
          }
          break;
        case PopUpTypes.RelatePosType.Custom:
          {
            // 自定义无需计算位置
          }
          break;
        case PopUpTypes.RelatePosType.CenterScreen:
        default:
          {
            // 默认屏幕居中
            this._window.center();
          }
          break;
      }
      this._window.setBounds(bound);
      // https://github.com/electron/electron/issues/27651 由于调用setBounds会使browserwindow区域多1-2像素的问题，从而暗黑模式出现白框的问题
      // 所以在此调用setSize调整窗口矩形区域
      this._window.setSize(bound.width, bound.height);
  
      let b = this._window.getBounds();
      logger('in bound =', JSON.stringify(bound),',out bound=',JSON.stringify(b), ',minWidth=', minWidth, ',minHeight=', minHeight);
    }

    if (options.resizable === false) {
      this._window.setResizable(false);
    }

    if (options.alwaysOnTop) {
      this._window.setAlwaysOnTop(true);
    } else {
      this._window.setAlwaysOnTop(false);
    }

    this._window.setMaximizable(options.maximizable ?? false);
    this._window.setMinimizable(options.minimizable ?? false);
    if (!options.modal || !options.parent) {
      // Can not be called for modal window
      this._window.setParentWindow(options.parent ?? null);
    }
    this._window.setSkipTaskbar(options.skipTaskbar ?? false);
    if (options.parent?.isAlwaysOnTop()) {
      this._window.setAlwaysOnTop(true);
    }

    this._window.setBackgroundColor(options.backgroundColor ?? '#FFF'); // default
    this.window.setHasShadow(options.hasShadow ?? true);
    // this._window.show();
  }

  on(event: 'ready-to-show' | 'close' | 'closed' | string, listener: Function): this {
    if (event === 'free' || event === 'abnormal-closed') {
      super.on(event, listener as (...args: any[]) => void);
      return this;
    }
    if (event === 'ready-to-show') {
      if (this._readyToShow) {
        listener();
      } else {
        this._window.on('ready-to-show', listener);
      }
    } else if (event === 'close' || event === 'closed') {
      // this.on('mock-closed', listener);
      super.on(event, listener as (...args: any[]) => void);
      this._window.on(event as any, listener);
    } else {
      this._window.on(event as any, listener);
    }
    return this;
  }

  once(event: 'ready-to-show' | 'close' | 'closed' | string, listener: Function): this {
    if (event === 'free' || event === 'abnormal-closed') {
      super.once(event, listener as (...args: any[]) => void);
      return this;
    }
    if (event === 'ready-to-show') {
      if (this._readyToShow) {
        listener(); // TODO 是否需要settimeout(0)
      } else {
        this._window.once('ready-to-show', listener);
      }
    } else if (event === 'close' || event === 'closed') {
      // Promise.race([new Promise<void>(
      //   (resolve: () => void, reject: (err: Error) => void): void => {
      //     this.once('mock-closed', () => {
      //       resolve();
      //     });
      //   }), new Promise<void>(
      //     (resolve: () => void, reject: (reason?: any) => void): void => {
      //       this._window.once(event as any, () => {
      //         resolve();
      //       });
      //     }
      //   )]).then(() => {
      //     listener && listener();
      //   }).catch();
      super.once(event, listener as (...args: any[]) => void);
      this._window.once(event as any, listener);
    } else {
      this._window.once(event as any, listener);
    }
    return this;
  }

  removeListener(event: string | symbol, listener: (...args: any[]) => void): this {
    if (event === 'free' || event === 'abnormal-closed') {
      super.removeListener(event, listener);
      return this;
    }
    if (event === 'close' || event === 'closed') {
      super.removeListener(event, listener);
      this._window.removeListener(event as any, listener);
      return this;
    }
    this._window.removeListener(event as any, listener);
    return this;
  }
  off(event: string | symbol, listener: (...args: any[]) => void): this {
    if (event === 'free' || event === 'abnormal-closed') {
      super.off(event, listener);
      return this;
    }
    if (event === 'close' || event === 'closed') {
      super.off(event, listener);
      this._window.off(event as any, listener);
      return this;
    }
    this._window.off(event as any, listener);
    return this;
  }
  removeAllListeners(event?: string | symbol): this {
    if (event === 'free' || event === 'abnormal-closed' || event === 'close' || event === 'closed') {
      super.removeAllListeners(event);
    }
    return this;
  }
}

// 每给ReuseBrowserWindow实现一个接口都需要在ReuseBrowserWindowGuard里面实现一个
// 在回收窗口的时候，方便清理事件
export class ReuseBrowserWindowGuard extends EventEmitter {
  private win: ReuseBrowserWindow | undefined = undefined;
  private handlers: Map<string, (...args: any[]) => void> = new Map();
  private bussinessId: string;
  private _useNativeShadow: boolean = false;

  constructor(win: ReuseBrowserWindow) {
    super();
    this.bussinessId = generateUuid();
    this.win = win;
    this.win.updateBussinessId(this.bussinessId);
  }

  get readyToShow(): boolean {
    return this.win?.readyToShow ?? false;
  }

  get useNativeShadow(): boolean {
    return this._useNativeShadow;
  }

  set useNativeShadow(use: boolean) {
    this._useNativeShadow = use;
  }

  on(event: string, listener: Function): this {
    if (event === 'free' || event === 'abnormal-closed') {
      // 外部不需要关注这两个事件
      return this;
    }

    super.on(event, listener as (...args: any[]) => void);
    if (!this.handlers.has(event)) {
      let handler: Function = (...args: any[]): void => {
        this.emit(event, ...args);
      };
      this.handlers.set(event, handler as any);
      this.win?.on(event, handler);
    }
    return this;
  }
  once(event: string, listener: Function): this {
    if (event === 'free' || event === 'abnormal-closed') {
      return this;
    }

    super.once(event, listener as (...args: any[]) => void);
    if (!this.handlers.has(event)) {
      let handler: Function = (...args: any[]): void => {
        this.emit(event, ...args);
      };
      this.handlers.set(event, handler as any);
      // 这里一定要用on
      this.win?.on(event, handler);
    }
    return this;
  }

  removeListener(event: string | symbol, listener: (...args: any[]) => void): this {
    if (event !== 'free' && event !== 'abnormal-closed') {
      super.removeListener(event, listener);
    }
    return this;
  }
  off(event: string | symbol, listener: (...args: any[]) => void): this {
    if (event !== 'free' && event !== 'abnormal-closed') {
      super.off(event, listener);
    }
    return this;
  }
  removeAllListeners(event?: string | symbol): this {
    if (event !== 'free' && event !== 'abnormal-closed') {
      super.removeAllListeners(event);
      this.handlers.forEach((handler: (...args: any[]) => void, event: string) => {
        this.win?.removeListener(event, handler);
      });
      this.handlers.clear();
    }
    return this;
  }

  get id(): number | undefined {
    return this.win?.id;
  }
  get window(): BrowserWindow | null {
    return this.win?.window ?? null;
  }

  get webContents(): WebContents | null {
    if (this.bussinessId === this.win?.getBussinessId()) {
      return this.win.webContents;
    }

    return null;
  }

  isDestroyed(): boolean {
    if (this.bussinessId === this.win?.getBussinessId()) {
      return this.win.isDestroyed();
    }

    return true;
  }

  isVisible(): boolean {
    if (this.bussinessId === this.win?.getBussinessId()) {
      return this.win.isVisible();
    }

    return false;
  }

  isModal(): boolean {
    if (this.bussinessId === this.win?.getBussinessId()) {
      return this.win.isModal();
    }

    return false;
  }

  isMinimized(): boolean {
    if (this.bussinessId === this.win?.getBussinessId()) {
      return this.win.isMinimized();
    }

    return false;
  }

  show(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.show();
    }
  }

  minimize(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.minimize();
    }
  }

  maximize(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.maximize();
    }
  }

  restore(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.restore();
    }
  }

  focus(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.focus();
    }
  }

  setPosition(x: number, y: number, animate?: boolean): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.setPosition(x, y, animate);
    }
  }

  setTitle(title: string): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.setTitle(title);
    }
  }

  center(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.center();
    }
  }

  showInactive(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.showInactive();
    }
  }

  getNativeWindowHandle(): Buffer {
    if (this.bussinessId === this.win?.getBussinessId()) {
      return this.win.getNativeWindowHandle();
    }
    return Buffer.from('');
  }

  isFocused(): boolean {
    if (this.bussinessId === this.win?.getBussinessId()) {
      return this.win.isFocused();
    }
    return false;
  }

  hide(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.hide();
    }
  }

  close(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.close();
    }
  }

  destroy(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.destroy();
    }
  }

  openDevTools(options?: OpenDevToolsOptions): void {
    this.win?.openDevTools(options);
  }

  getParentWindow(): BrowserWindow | null {
    if (this.bussinessId === this.win?.getBussinessId()) {
      return this.win.getParentWindow();
    }

    return null;
  }

  setParentWindow(parent: (BrowserWindow) | (null)): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      const old = this.win.getParentWindow();
      if (old?.id !== parent?.id) {
        if (this.useNativeShadow) {
          detachShadowWindow(this.id!);
          attachShadowWindow(this.win.window, parent);
        }
        this.win.setParentWindow(parent);
      }
    }
  }

  setFullScreen(flag: boolean): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.setFullScreen(flag);
    }
  }

  setFullScreenable(fullscreenable: boolean): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.setFullScreenable(fullscreenable);
    }
  }

  moveTop(): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.moveTop();
    }
  }

  updateView(componentsName: string, title: string, options: any, dialogConf?: BrowserWindowConstructorOptions): void {
    if (this.bussinessId === this.win?.getBussinessId()) {
      this.win.updateView(componentsName, title, options, dialogConf);
    }
  }

  // 内部使用,调用后对Gurad的引用将为空壳
  public clean(): void {
    this.removeAllListeners();
    this.win?.updateBussinessId('');
    this.win = undefined;
  }
}

class ReuseWindowManager {
  private freeWnds: ReuseBrowserWindow[] = []; // tslint:disable-line
  private busyWnds: Map<number, ReuseBrowserWindowGuard> = new Map(); // tslint:disable-line
  private isRelease: boolean = false;
  private mainWindow: BrowserWindow | null = null;
  private singletonMap: { [popType: string]: ReuseBrowserWindowGuard } = {};

  // 模态对话框，不预留新窗口，如果当前正在使用，则临时新创建即可
  private freeModalWnds: ReuseBrowserWindow[] = [];
  private busyModalWnds: Map<number, ReuseBrowserWindowGuard> = new Map(); // tslint:disable-line

  constructor() {
  }

  public async init(mainWindow: BrowserWindow): Promise<void> {
    this.mainWindow = mainWindow;
    this.freeWnds.push(this.newReuseWnd(false));
    if (mainWindow) {
      this.freeModalWnds.push(this.newReuseWnd(true, mainWindow));
    }

    client.registerFunctions({
      CreateReuseWindow: (remoteId: string, dialogConf: BrowserWindowConstructorOptions, extra: { parentId: number, relatePos: PopUpTypes.RelatePosType }): number | undefined => {
        if (typeof extra?.parentId === 'number') {
          dialogConf = dialogConf ?? {};
          dialogConf.parent = BrowserWindow.fromId(extra.parentId)!;
        }
        const wnd: ReuseBrowserWindowGuard = this.create(dialogConf, extra?.relatePos, !(dialogConf?.width && dialogConf?.height));
        return wnd.id;
      },
      CustomReuseWindow: async (remoteId: string,
        componentsName: string,
        options: { parentId?: number; singleton?: boolean; windowWidth?: number; windowHeight?: number; replaceView?: boolean; relatePos?: PopUpTypes.RelatePosType; [prop: string]: any } = {},
        dialogConf: BrowserWindowConstructorOptions = {}): Promise<PopUpTypes.ResolvePayload> => {
        if (typeof options?.parentId === 'number') {
          dialogConf = dialogConf ?? {};
          if (options.parentId !== -1) {
            dialogConf.parent = BrowserWindow.fromId(options.parentId)!;
          }          
        } else {
          dialogConf = dialogConf ?? {};
          dialogConf.parent = mainWindow;
        }
        return this.custom(componentsName, options, dialogConf, options?.relatePos, options?.replaceView);
      },
      ReuseUpdateView: (remoteId: string, wndId: number, componentsName: string, title: string, options: any): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.updateView(componentsName, title, options);
        } while (0);
      },
      AttachReuseEvent: (remoteId: string, wndId: number, event: string, listener: Function): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.on(event, listener);
        } while (0);
      },
      DetachReuseEvent: (remoteId: string, wndId: number, event: string, listener: (...args: any[]) => void): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.off(event, listener);
        } while (0);
      },
      ReuseIsDestroyed: (remoteId: string, wndId: number): boolean => {
        let ret: boolean = false;
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (wnd) {
            break;
          }

          ret = true;
        } while (0);
        return ret;
      },
      ReuseClose: (remoteId: string, wndId: number, action: PopUpTypes.Action, payloadArgs: any): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.emit('before-quit', action, payloadArgs);
          wnd.close();
        } while (0);
      },
      ReuseCheckExist: (remoteId: string, wndId: number): boolean => {
        let ret: boolean = false;
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          ret = true;
        } while (0);

        return ret;
      },
      ReuseIsVisible: (remoteId: string, wndId: number): boolean => {
        let ret: boolean = true;
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          ret = wnd.isVisible();
        } while (0);
        return ret;
      },
      ReuseIsMinimized: (remoteId: string, wndId: number): boolean => {
        let ret: boolean = true;
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          ret = wnd.isMinimized();
        } while (0);
        return ret;
      },
      ReuseMinimize: (remoteId: string, wndId: number): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.minimize();
        } while (0);
      },
      ReuseMaximize: (remoteId: string, wndId: number): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.maximize();
        } while (0);
      },
      ReuseRestore: (remoteId: string, wndId: number): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.restore();
        } while (0);
      },
      ReuseShow: (remoteId: string, wndId: number): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.show();
        } while (0);
      },
      ReuseHide: (remoteId: string, wndId: number): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.hide();
        } while (0);
      },
      ReuseFocus: (remoteId: string, wndId: number): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.focus();
        } while (0);
      },
      ReuseSetPosition: (remoteId: string, wndId: number, ...args: any[]): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.setPosition(args[0], args[1], args[2]);
        } while (0);
      },
      ReuseSetTitle: (remoteId: string, wndId: number, title: string): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.setTitle(title);
        } while (0);
      },
      ReuseSetParentWindow: (remoteId: string, wndId: number, parentId: number): void => {
        // 仅非模态窗口支持
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            break;
          }
          const parent = BrowserWindow.fromId(parentId);
          wnd.setParentWindow(parent);
        } while (0);
      },
      ReuseCenter: (remoteId: string, wndId: number): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.center();
        } while (0);
      },
      ReuseOpenDevTools: (remoteId: string, wndId: number, options: OpenDevToolsOptions): void => {
        do {
          let wnd = this.busyWnds.get(wndId);
          if (!wnd) {
            wnd = this.busyModalWnds.get(wndId);
          }
          if (!wnd) {
            break;
          }
          wnd.openDevTools(options);
        } while (0);
      },
      GetExistReuseWndId: (remoteId: string, popType: string, ownerId: number): number => {
        return this.getExistPopTypeId(popType, ownerId);
      },
      // 提供一些调整窗口位置的IPC接口
      FitWindowPosInParent: (remoteId: string, wndId: number, parentId: number, width: number, height: number): boolean => {
        let result: boolean = false;
        do {
          if (!wndId) {
            break;
          }
          const child = BrowserWindow.fromId(wndId);
          if (!child) {
            break;
          }

          let parentWindow: BrowserWindow | null = null;
          if (parentId) {
            parentWindow = BrowserWindow.fromId(parentId);
          } else {
            parentWindow = child.getParentWindow();
            if (!parentWindow) {
              parentWindow = mainWindow;
            }
          }

          if (!parentWindow) {
            break;
          }

          WindowPosNS.centerWnd(child, parentWindow, [width, height]);
          result = true;
        } while (0);
        return result;
      },
      ResizeWindowPos: (remoteId: string, wndId: number, rect: Rectangle, relatePos: PopUpTypes.RelatePosType): boolean => {
        let result: boolean = false;
        do {
          if (!wndId) {
            break;
          }
          const wnd: BrowserWindow | null = BrowserWindow.fromId(wndId);
          if (!wnd) {
            break;
          }
          const resizable = wnd.isResizable();
          if (relatePos === PopUpTypes.RelatePosType.RightBottom) {
            const leftTop = WindowPosNS.getRelateRightBottomPos([rect.width, rect.height]);
            if (!resizable) {
              wnd.setResizable(true);
            }
            wnd.setBounds({ x: leftTop.x, y: leftTop.y, width: Math.round(rect.width), height: Math.round(rect.height) });
            if (!resizable) {
              wnd.setResizable(false);
            }
            break;
          }

          if (rect.x === undefined || rect.y === undefined) {
            const r = wnd.getBounds();
            rect.x = rect.x === undefined ? r.x : rect.x;
            rect.y = rect.y === undefined ? r.y : rect.y;
          }
          if (!resizable) {
            wnd.setResizable(true);
          }
          wnd.setBounds({ x: Math.round(rect.x), y: Math.round(rect.y), width: Math.round(rect.width), height: Math.round(rect.height) });
          if (!resizable) {
            wnd.setResizable(false);
          }
        } while (0);
        return result;
      }
    });
  }

  public uninit(): void {
    this.isRelease = true;
    this.busyWnds.forEach((guard: ReuseBrowserWindowGuard) => {
      guard.destroy();
    });
    for (let i = 0; i < this.freeWnds.length; i++) {
      this.freeWnds[i].destroy();
    }

    this.busyModalWnds.forEach((guard: ReuseBrowserWindowGuard) => {
      guard.destroy();
    });
    for (let i = 0; i < this.freeModalWnds.length; i++) {
      this.freeModalWnds[i].destroy();
    }
  }

  // 由于新建面板可能先于主渲染进程被创建->新建面板创建后先立马预创建一个窗口，在main.ts中无法创建ipc连接
  create(options: BrowserWindowConstructorOptions, relatePos: PopUpTypes.RelatePosType, autoSize: boolean, nativeShadow: boolean = true): ReuseBrowserWindowGuard {
    if (options.modal) {
      return this.createModal(options, relatePos, autoSize);
    }
    if (this.freeWnds.length === 0) {
      this.freeWnds.push(this.newReuseWnd(false));
    }
    const wnd: ReuseBrowserWindow = this.freeWnds.splice(0, 1)[0];
    logger('wnd create, free count=', this.freeWnds.length + 1, ' ,id=', wnd.id);
    const guard: ReuseBrowserWindowGuard = new ReuseBrowserWindowGuard(wnd);
    this.busyWnds.set(wnd!.id as number, guard);
    if (this.freeWnds.length === 0) {
      // 预加载一个
      this.freeWnds.push(this.newReuseWnd(false, undefined, false));
    }
    wnd.updateOptions(options, relatePos, this.mainWindow as BrowserWindow, autoSize);
    guard.once('show', () => {
      if (nativeShadow !== false) {
        attachShadowWindow(guard.window!, options.parent);
        guard.useNativeShadow = true;
      }
    });
    return guard;
  }

  createModal(options: BrowserWindowConstructorOptions, relatePos: PopUpTypes.RelatePosType, autoSize: boolean, nativeShadow: boolean = true): ReuseBrowserWindowGuard {
    let wnd: ReuseBrowserWindow | null = null;
    for (let index: number = 0; index < this.freeModalWnds.length; index++) {
      if (this.freeModalWnds[index]?.getParentWindow()?.id === options.parent?.id && !options.transparent) {
        // 从空闲的列表排除 splice
        wnd = this.freeModalWnds.splice(index, 1)[0];
        break;
      }
    }

    if (!wnd) {
      this.freeModalWnds.push(this.newReuseWnd(true, options.parent, options.transparent ??  false));
      wnd = this.freeModalWnds.splice(this.freeModalWnds.length - 1, 1)[0];
    }

    const guard: ReuseBrowserWindowGuard = new ReuseBrowserWindowGuard(wnd);
    this.busyModalWnds.set(wnd!.id as number, guard);
    // 无需多预加载一个窗口
    wnd.updateOptions(options, relatePos, this.mainWindow!, autoSize);
    guard.once('show', () => {
      if (nativeShadow !== false) {
        attachShadowWindow(guard.window!, options.parent, false, false);
        guard.useNativeShadow = true;
      }
    });
    return guard;
  }

  public fromId(wndId: number): ReuseBrowserWindowGuard | undefined {
    let wnd = this.busyWnds.get(wndId);
    if (!wnd) {
      wnd = this.busyModalWnds.get(wndId);
    }
    return wnd;
  }

  /**
   * @description 判断是否在可复用窗口，包括在用&备用列表
   * @return
   * @param wndId
   * @example
   * const exist = has(id);
   * if (exist) {
   *   const wnd = fromId(id);
   *   if (wnd) {
   *     // todo
   *   }
   * }
   */
  public has(wndId: number): boolean {
    let exist: boolean = false;
    do {
      if (this.busyWnds.get(wndId)) {
        exist = true;
        break;
      }

      if (this.busyModalWnds.get(wndId)) {
        exist = true;
        break;
      }

      for (let v of this.freeWnds) {
        if (v.id === wndId) {
          exist = true;
          break;
        }
      }

      if (exist) {
        break;
      }

      for (let w of this.freeModalWnds) {
        if (w.id === wndId) {
          exist = true;
          break;
        }
      }
    } while (0);
    return exist;
  }

  public getExistPopTypeId(popType: string, id?: number): number {
    const singletonId = camelToKebab(popType) + (id ?? 0);
    return this.singletonMap[singletonId]?.id!;
  }

  public async custom(
    popType: string,
    options: { singleton?: boolean; windowWidth?: number; windowHeight?: number; [prop: string]: any } = {},
    dialogConf: BrowserWindowConstructorOptions = {},
    relatePos: PopUpTypes.RelatePosType = PopUpTypes.RelatePosType.CenterParent,
    replaceView: boolean = false): Promise<PopUpTypes.ResolvePayload> {
    dialogConf = dialogConf ?? {};
    if (options?.windowWidth) {
      dialogConf.width = options.windowWidth;
    }
    if (options?.windowHeight) {
      dialogConf.height = options.windowHeight;
    }

    let wnd: ReuseBrowserWindowGuard | undefined = undefined;
    let singletonId: string = '';
    if (options.singleton) {
      singletonId = camelToKebab(popType) + (dialogConf?.parent?.id ?? 0);
      if (this.singletonMap[singletonId]) {
        wnd = this.singletonMap[singletonId];
      }

      if (replaceView && wnd?.readyToShow) {
        const title = dialogConf.title ?? '';
        wnd.updateView(popType, title, options);
      }
    }

    const autoSize = !(options?.windowWidth && options?.windowHeight);
    if (options) {
      options.autoSize = autoSize;
      options.show = dialogConf.show ?? true;
    } else {
      options = { autoSize, show: dialogConf.show ?? true };
    }
    if (!wnd) {
      wnd = this.create(dialogConf, relatePos, autoSize);
      if (options.singleton) {
        this.singletonMap[singletonId] = wnd;
      }
    }

    wnd.once('ready-to-show', async () => {
      const title = dialogConf.title ?? '';
      wnd.updateView(popType, title, options);
      if (dialogConf.show !== false && !options.autoSize) {
        wnd.show();
      }
    });

    wnd.on('close', () => {
      if (options.singleton) {
        delete this.singletonMap[singletonId];
      }
    });

    return new Promise<PopUpTypes.ResolvePayload>(
      (resolve: (payload: PopUpTypes.ResolvePayload) => void): void => {
        wnd.once('before-quit', (action: PopUpTypes.Action, args: any) => {
          resolve({ action, args });
        });
      }
    );
  }

  private newReuseWnd(modal: boolean, parent?: BrowserWindow, transparent: boolean = false): ReuseBrowserWindow {
    let wnd: ReuseBrowserWindow = new ReuseBrowserWindow(modal, parent, transparent);
    logger('new reuse wnd, free count=', this.freeWnds.length, ' ,id=', wnd.id);
    const wndId: number = wnd.id!;
    wnd.on('free', () => {
      if (this.busyWnds.has(wndId)) {
        let gurad: ReuseBrowserWindowGuard = this.busyWnds.get(wndId)!;
        const freeWndId: number = gurad.id!;
        this.busyWnds.delete(wndId);
        logger('begin to clean=============');
        gurad.clean();
        if (this.freeWnds.length > 0) {
          let tempWnd = this.freeWnds.splice(0, 1)[0];
          logger('wnd recyle, id=', wndId, ' ,free count=', this.freeWnds.length + 1, ' , free wnd id=', tempWnd.id);
          // 一定要先放进去，在去关闭tempWnd，否则会以为没有free了，出现了异常关闭wnd，从而重新创建一个wnd
          this.freeWnds.push(wnd);
          // 这里一定要先清理事件，再调用destroy，否则会触发abnormal-closed
          // 这里预加载的窗口还没有给外部使用，所以外部不会挂任何有用事件，所以可以先清理
          tempWnd.removeAllListeners();
          tempWnd.destroy();
        } else {
          logger('wnd recyle, free count=0');
          this.freeWnds.push(wnd);
        }

        // 如果关闭的是父窗口，则也需要关闭父窗口关联的子窗口（窗口被异常关闭 无需处理:异常关闭子窗口会主动关闭)
        this.busyWnds.forEach((tmp: ReuseBrowserWindowGuard) => {
          if (tmp?.getParentWindow()?.id === freeWndId) {
            tmp.close();
          }
        });
      } else if (this.busyModalWnds.has(wndId)) {
        let gurad: ReuseBrowserWindowGuard = this.busyModalWnds.get(wndId)!;
        const freeWndId: number = gurad.id!;
        this.busyModalWnds.delete(wndId);
        logger('begin to clean=============');
        gurad.clean();

        do {
          if (this.freeModalWnds.length === 0) {
            // 只保留以主窗口为父窗口的模态非透明窗口
            if (wnd.getParentWindow()?.id === this.mainWindow?.id && !transparent) {
              this.freeModalWnds.push(wnd);
              break;
            }
          }

          // 始终最多预留一个
          wnd.removeAllListeners();
          wnd.destroy();
        } while (0);

        // 如果关闭的是父窗口，则也需要关闭父窗口关联的子窗口（窗口被异常关闭 无需处理:异常关闭子窗口会主动关闭)
        this.busyModalWnds.forEach((tmp: ReuseBrowserWindowGuard) => {
          if (tmp?.getParentWindow()?.id === freeWndId) {
            tmp.close();
          }
        });
      } else {
        logger('free event should from busywnd');
        throw Error('free event should from busywnd');
      }
    });
    wnd.on('abnormal-closed', () => {
      logger('wnd abnormal closed, id=', wndId, ' free count=',this.freeWnds.length);
      wnd.removeAllListeners();
      do {
        if (this.busyWnds.has(wndId)) {
          let gurad: ReuseBrowserWindowGuard = this.busyWnds.get(wndId)!;
          this.busyWnds.delete(wndId);
          gurad.emit('before-quit', { action: PopUpTypes.Action.Unknown });
          gurad.clean();
          break;
        }

        // 那可能是已经预加载但是未使用的窗口
        {
          let find: boolean = false;
          for (let i = 0; i < this.freeWnds.length; i++) {
            if (this.freeWnds[i] === wnd) {
              this.freeWnds.splice(i, 1);
              find = true;
              break;
            }
          }
          if (this.freeWnds.length === 0 && !this.isRelease) {
            this.freeWnds.push(this.newReuseWnd(false));
          }

          if (find) {
            break;
          }
        }

        if (this.busyModalWnds.has(wndId)) {
          let gurad: ReuseBrowserWindowGuard = this.busyModalWnds.get(wndId)!;
          this.busyModalWnds.delete(wndId);
          gurad.emit('before-quit', { action: PopUpTypes.Action.Unknown });
          gurad.clean();
          break;
        }

        {
          for (let i = 0; i < this.freeModalWnds.length; i++) {
            if (this.freeModalWnds[i] === wnd) {
              this.freeModalWnds.splice(i, 1);
            }
          }

          if (this.freeModalWnds.length === 0 && !this.isRelease && this.busyModalWnds.size === 0) {
            this.freeModalWnds.push(this.newReuseWnd(true, this.mainWindow!));
          }
        }

      } while (0);
    });
    const urlObj: URL = new URL(getURL());
    urlObj.searchParams.set('boxId', String(wndId));
    wnd.window.loadURL(urlObj.href).catch();
    return wnd;
  }
}

export const reuserWindowManager: ReuseWindowManager = new ReuseWindowManager();
