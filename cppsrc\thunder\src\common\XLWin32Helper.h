#pragma once

#include <Lmcons.h>
#include <tlhelp32.h>
#include <atlsecurity.h>
#include <atlsync.h>


namespace XLWin32Helper
{

namespace Synchronization
{

inline void SetNamedEvent(PCTSTR pszName)
{
	CEvent event;
	if (event.Open(EVENT_ALL_ACCESS, FALSE, pszName))
	{
		event.Set();
	}
}

}	// Synchronization

namespace Process
{

// When you are finished with the handle, be sure to close it using the CloseHandle function.
inline HANDLE OpenProcess(PCTSTR pProcessName, DWORD dwDesiredAccess = PROCESS_ALL_ACCESS)
{
	HANDLE hProcessSnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	if (hProcessSnap == INVALID_HANDLE_VALUE)
	{
		return FALSE;
	}

	HANDLE hProcess = NULL;

	PROCESSENTRY32 pe32 = { sizeof(PROCESSENTRY32) };
	if (!Process32First(hProcessSnap, &pe32))
	{
		CloseHandle(hProcessSnap);
		return FALSE;
	}

	do
	{
		if (::lstrcmpi(pe32.szExeFile, pProcessName) == 0)
		{
			hProcess = ::OpenProcess(dwDesiredAccess, FALSE, pe32.th32ProcessID);
			if (hProcess != NULL)
			{
				break;
			}
		}
	} while (Process32Next(hProcessSnap, &pe32));

	CloseHandle(hProcessSnap);

	return hProcess;
}

typedef struct _TOKEN_MANDATORY_LABEL {
    SID_AND_ATTRIBUTES Label;
} TOKEN_MANDATORY_LABEL, *PTOKEN_MANDATORY_LABEL;

#define SE_GROUP_INTEGRITY                 (0x00000020L)

#define TokenIntegrityLevel					25

inline BOOL CreateMediumIntegrityProcess(PCTSTR pszApplicationName, PTSTR pszCommandLine, PPROCESS_INFORMATION pPI)
{
	BOOL bRet = FALSE;

    CAccessToken ProcToken;
	CAccessToken PrimaryToken;

	PSID pSid = NULL;

	STARTUPINFO si = { sizeof(si) };

	do
	{
		if (!ProcToken.GetEffectiveToken(TOKEN_DUPLICATE | TOKEN_ADJUST_DEFAULT | TOKEN_QUERY | TOKEN_ASSIGN_PRIMARY))
		{
			break;
		}

		if (!ProcToken.CreatePrimaryToken(&PrimaryToken))
		{
			break;
		}

		TCHAR szIntegritySid[20] = _T("S-1-16-8192");
		ConvertStringSidToSid(szIntegritySid, &pSid);

		TOKEN_MANDATORY_LABEL TIL;
		TIL.Label.Attributes = SE_GROUP_INTEGRITY;
		TIL.Label.Sid = pSid;
		if (!SetTokenInformation(PrimaryToken.GetHandle(), (TOKEN_INFORMATION_CLASS)TokenIntegrityLevel, &TIL, sizeof(TOKEN_MANDATORY_LABEL) + GetLengthSid(pSid)))
		{
			break;
		}

		GetStartupInfo(&si);
		bRet = CreateProcessAsUser(PrimaryToken.GetHandle(), pszApplicationName, pszCommandLine, NULL, NULL, FALSE, NORMAL_PRIORITY_CLASS, NULL, NULL, &si, pPI);
	} while (false);

	if (pSid != NULL)
	{
		LocalFree(pSid);
	}

	if (!bRet)
	{
		bRet = CreateProcess(pszApplicationName, pszCommandLine, NULL, NULL, FALSE, NORMAL_PRIORITY_CLASS, NULL, NULL, &si, pPI);
	}

	return bRet;
}

}

namespace Integrity
{

inline void ChangeWindowMessageFilter(UINT message, DWORD dwFlag)
{
	HMODULE hUser32 = GetModuleHandle("user32.dll");
	if (hUser32 != NULL)
	{
		typedef BOOL (WINAPI* PFNChangeWindowMessageFilter)(UINT message, DWORD dwFlag);
		PFNChangeWindowMessageFilter pfnChangeWindowMessageFilter = (PFNChangeWindowMessageFilter)GetProcAddress(hUser32, "ChangeWindowMessageFilter");
		if (pfnChangeWindowMessageFilter != NULL)
		{
			pfnChangeWindowMessageFilter(message, dwFlag);
		}
	}
}

}	// Integrity


//namespace Timer
//{
//
//__interface ITimerProcThunkCallback
//{
//	VOID CALLBACK TimerProc(UINT_PTR idEvent, DWORD dwTime);
//};
//
//class TimerProcThunk
//{
//public:
//	TimerProcThunk() : m_pCallback(NULL), m_nIDEvent(0xFFFFFFFF)
//	{
//	}
//
//public:
//	void SetTimer(UINT uElapse, ITimerProcThunkCallback *pCallback)
//	{
//		ATLASSERT(pCallback != NULL);
//		m_pCallback = pCallback;
//		m_thunk.Init((DWORD_PTR)TimerProc, this);
//		m_nIDEvent = ::SetTimer(NULL, 0, uElapse, (TIMERPROC)m_thunk.GetCodeAddress());
//	}
//	void KillTimer()
//	{
//		if (m_nIDEvent != 0xFFFFFFFF)
//		{
//			::KillTimer(NULL, m_nIDEvent);
//			m_pCallback = NULL;
//			m_nIDEvent = 0xFFFFFFFF;
//		}
//	}
//
//	UINT_PTR GetId()
//	{
//		return m_nIDEvent;
//	}
//
//private:
//	static VOID CALLBACK TimerProc(HWND hwnd, UINT /*uMsg*/, UINT_PTR idEvent, DWORD dwTime)
//	{
//		TimerProcThunk *pThis = (TimerProcThunk *)hwnd;
//		if (pThis->m_pCallback != NULL)
//		{
//			__try
//			{
//				pThis->m_pCallback->TimerProc(idEvent, dwTime);
//			}
//			__except(UnhandledExceptionFilter(GetExceptionInformation()))
//			{
//			}
//		}
//	}
//
//private:
//	CStdCallThunk m_thunk;
//	UINT_PTR m_nIDEvent;
//	ITimerProcThunkCallback *m_pCallback;
//};
//
//}	// Timer


namespace Random
{
	static int RangedRand(int range_min, int range_max)
	{
		srand(GetTickCount());
		return (int)((double)rand() / (RAND_MAX + 1) * (range_max - range_min) + range_min);
	}
}


}	// XLWin32Helper
