/**
 * 封装给业务调用的配置模块
 */

import { EventEmitter } from 'events';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { ConfigValueType } from '@root/common/config/types';

export class Config extends EventEmitter {
  private attached: boolean = false;
  constructor() {
    super();
    this.setMaxListeners(0);
  }

  private init(): void {
    if (!this.attached) {
      this.attached = true;
      client.attachServerEvent('OnConfigValueChanged', (
        context: unknown,
        section: string,
        key: string,
        preValue: ConfigValueType | undefined,
        newValue: ConfigValueType | undefined,
        isClick: boolean) => {
        this.emit('OnConfigValueChanged', section, key, preValue, newValue, isClick);
      });
    }
  }

  public on(event: 'OnConfigValueChanged', listener: (...args: any[]) => void): this {
    this.init();
    return super.on(event, listener);
  }

  public off(event: 'OnConfigValueChanged', listener: (...args: any[]) => void) {
    this.init();
    return super.off(event, listener);
  }

  public async has(section: string, key: string): Promise<boolean> {
    this.init();
    return client.callServerFunction('HasConfigValue', section, key);
  }

  public async getValue(section: string, key: string, defValue: ConfigValueType): Promise<ConfigValueType> {
    this.init();
    return client.callServerFunction('GetConfigValue', section, key, defValue);
  }

  public async setValue(section: string, key: string, value: ConfigValueType, isClick: boolean = false): Promise<void> {
    this.init();
    await client.callServerFunction('SetConfigValue', section, key, value, isClick);
  }

  public async removeValue(section: string, key: string): Promise<void> {
    this.init();
    await client.callServerFunction('RemoveConfigValue', section, key);
  }

  public async getRemoteGlobalConfigValue(groupName: string, keyName?: string, defaultValue?: any): Promise<any> {
    return client.callServerFunction('GetRemoteGlobalConfigValue', groupName, keyName, defaultValue);
  }

  public async getStaticConfigValue(groupName?: string, keyName?: string): Promise<any> {
    return client.callServerFunction('GetConfigModules', groupName, keyName);
  }

  public async getAbTestConfigValue(groupName?: string, keyName?: string): Promise<any> {
    return client.callServerFunction('GetAbTestConfigValue', groupName, keyName);
  }

  public async getLinkFileIconConfigValue(fileType: string): Promise<any> {
    return client.callServerFunction('GetLinkFileIconConfigValue', fileType);
  }
}

export const config: Config = new Config();