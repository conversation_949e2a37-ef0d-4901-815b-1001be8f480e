import { defineConfig } from '@rsbuild/core'
import { pluginSass } from '@rsbuild/plugin-sass'
import { pluginVue } from '@rsbuild/plugin-vue'
import path from 'node:path'
import { port } from '../common/env'

const htmlTemplate = path.join(__dirname, 'template.html')

// ? 多页面入口配置
// const entryDir = path.join(__dirname, "src/entry");
// const files = fse.readdirSync(entryDir);
// const entry = keyBy(
//   files.map((f) => path.join(entryDir, f)),
//   (filePath) => removeFileExtname(filePath)
// );

export default defineConfig({
  html: {
    template: htmlTemplate,
  },
  source: {
    // entry,
    entry: {
      index: './entrys/plugin/index',
      renderer: './entrys/renderer/index',
    },
    tsconfigPath: path.resolve(process.cwd(), '../../tsconfig.json'),
  },
  output: {
    sourceMap: {
      js: 'source-map',
      css: true,
    },
    cleanDistPath: false,
    /**
     * Important: If set as an absolute path string,
     * it might be escaped in the browser,
     * causing resource request failures.
     * Therefore, it's best to use "auto".
     *
     * 重要：如果设置为绝对路径字符串，可能会在浏览器下被转义导致资源请求失败
     * 所以最好用auto
     */
    assetPrefix: 'auto',
    distPath: {
      root: path.join(process.cwd(), './build/player-plugin'),
      js: '',
    },
    injectStyles: true,
    filenameHash: false,
  },
  resolve: {
    alias: {
      '@root': path.join(process.cwd(), '../../src'),
      '@': path.join(process.cwd(), 'src'),
      "@xbase/electron_base_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_base_kit/dist/cjs/development/index.js"),
      "@xbase/electron_common_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_common_kit/dist/cjs/development/index.js"),
      "@xbase/electron_auth_types_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_auth_types_kit/dist/cjs/development/index.js"),
      "@xbase/electron_captcha_types_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_captcha_types_kit/dist/cjs/development/index.js"),
      "@xbase/electron_auth_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_auth_kit/dist/cjs/development/index.js"),
      "@xbase/electron_captcha_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_captcha_kit/dist/cjs/development/index.js"),
      "@xbase/electron_sync_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_sync_kit/dist/cjs/development/index.js"),
      "@xbase/electron_account_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_account_kit/dist/cjs/development/index.js"),
      "@xbase/electron_default_plugins_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_default_plugins_kit/dist/cjs/development/index.js"),
    },
  },
  server: {
    // 强制使用特定启动的 host
    // host: 'localhost',
    port: port,
  },
  // performance: {
  //   chunkSplit: {
  //     strategy: 'all-in-one',
  //   },
  // },
  tools: {
    rspack: {
      target: 'electron-renderer',
      output: {
        asyncChunks: false,
      },
    },
  },

  plugins: [
    pluginVue(),
    pluginSass({
      sassLoaderOptions: {
        // Force use of regular sass instead of sass-embedded to avoid Windows spawn issues
        implementation: require('sass'),
        additionalData: `
@import "~@root/common/assets/css/mixins.scss";`,
        sourceMap: true,
      },
    }),
  ],
})
