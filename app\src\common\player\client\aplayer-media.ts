import { CallApiProxy } from '@root/common/call-api';
import { SimpleObjectRef } from '@root/common/simple-object-ref';
import { EventEmitter } from 'events';
import * as BaseType from '../base';
import { AplayerSubTitleManager } from './subtitle-manager';

export class AplayerMedia extends SimpleObjectRef {
  private subTitleManager: AplayerSubTitleManager;
  private eventContainor: EventEmitter = new EventEmitter();
  private audioTrackPreparedCookie: number = 0;
  private audioTrackSelectChangeCookie: number = 0;
  private ratioPreparedCookie: number = 0;
  private progressChangeCookie: number = 0;
  private progressSetToLatestCookie: number = 0;
  private playStateChangeCookie: number = 0;
  private playBufferCookie: number = 0;
  constructor(apiProxy: CallApiProxy, id: string) {
    super(apiProxy, id);
    this.subTitleManager = new AplayerSubTitleManager(apiProxy, id);
  }

  public init() {
    console.log('client AplayerMedia init');
    this.subTitleManager.init();
    this.audioTrackPreparedCookie = this.apiProxy!.AttachServerEvent('AplayerMeidaAudioTrackPrepared', (id: string) => {
      if (id !== this.id) {
        return;
      }

      this.eventContainor.emit('AudioTrackPrepared');
    });
    this.audioTrackSelectChangeCookie = this.apiProxy!.AttachServerEvent('AplayerMeidaAudioTrackSelectChange', (id: string, index: number) => {
      if (id !== this.id) {
        return;
      }

      this.eventContainor.emit('AudioTrackSelectChange', index);
    });
    this.ratioPreparedCookie = this.apiProxy!.AttachServerEvent('AplayerMeidaRatioPrepared', (id: string) => {
      if (id !== this.id) {
        return;
      }

      this.eventContainor.emit('RatioPrepared');
    });
    this.progressChangeCookie = this.apiProxy!.AttachServerEvent('AplayerMeidaProgressChange', (id: string, pos: number) => {
      if (id !== this.id) {
        return;
      }

      this.eventContainor.emit('ProgressChange', pos);
    });
    this.progressSetToLatestCookie = this.apiProxy!.AttachServerEvent('AplayerMeidaProgressSetToLatest', (id: string, pos: number) => {
      if (id !== this.id) {
        return;
      }

      this.eventContainor.emit('ProgressSetToLatest', pos);
    });
    this.playStateChangeCookie = this.apiProxy!.AttachServerEvent('AplayerMeidaPlayStateChange', (id: string, s: BaseType.MediaState) => {
      if (id !== this.id) {
        return;
      }

      this.eventContainor.emit('PlayStateChange', s);
    });
    this.playBufferCookie = this.apiProxy!.AttachServerEvent('AplayerMeidaPlayBuffer', (id: string, buffer: boolean, local: boolean, t: any, speed: number) => {
      if (id !== this.id) {
        return;
      }

      this.eventContainor.emit('PlayBuffer', buffer, local, t, speed);
    });
  }

  public unInit() {
    this.subTitleManager.unInit();
    if (this.audioTrackPreparedCookie > 0) {
      this.apiProxy!.DetachServerEvent('AplayerMeidaAudioTrackPrepared', this.audioTrackPreparedCookie);
      this.audioTrackPreparedCookie = 0;
    }
    if (this.audioTrackSelectChangeCookie > 0) {
      this.apiProxy!.DetachServerEvent('AplayerMeidaAudioTrackSelectChange', this.audioTrackPreparedCookie);
      this.audioTrackSelectChangeCookie = 0;
    }
    if (this.ratioPreparedCookie > 0) {
      this.apiProxy!.DetachServerEvent('AplayerMeidaRatioPrepared', this.ratioPreparedCookie);
      this.ratioPreparedCookie = 0;
    }
    if (this.progressChangeCookie > 0) {
      this.apiProxy!.DetachServerEvent('AplayerMeidaProgressChange', this.progressChangeCookie);
      this.progressChangeCookie = 0;
    }
    if (this.progressSetToLatestCookie > 0) {
      this.apiProxy!.DetachServerEvent('AplayerMeidaProgressSetToLatest', this.progressSetToLatestCookie);
      this.progressSetToLatestCookie = 0;
    }
    if (this.playStateChangeCookie > 0) {
      this.apiProxy!.DetachServerEvent('AplayerMeidaPlayStateChange', this.playStateChangeCookie);
      this.playStateChangeCookie = 0;
    }
    if (this.playBufferCookie > 0) {
      this.apiProxy!.DetachServerEvent('AplayerMeidaPlayBuffer', this.playBufferCookie);
      this.playBufferCookie = 0;
    }
  }

  public getSubtitleManager(): AplayerSubTitleManager {
    return this.subTitleManager;
  }

  public async getName(): Promise<string> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetName', this.id);
    if (info.bSucc) {
      return info.result as string;
    }

    return '';
  }

  public async getAttribute(): Promise<BaseType.MediaAttribute> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetAttribute', this.id);
    if (info.bSucc) {
      return info.result as BaseType.MediaAttribute;
    }

    return {} as any;
  }

  public async getMediaWidth(): Promise<number> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetMediaWidth', this.id);
    if (info.bSucc) {
      return info.result as number;
    }

    return 0;

  }
  public async getMediaHeight(): Promise<number> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetMediaHeight', this.id);
    if (info.bSucc) {
      return info.result as number;
    }

    return 0;
  }
  public async isTaskLocalPlay(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerMediaIsTaskLocalPlay', this.id);
    if (info.bSucc) {
      return info.result as boolean;
    }

    return false;
  }
  public async isPanPlay(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerMediaIsPanPlay', this.id);
    if (info.bSucc) {
      return info.result as boolean;
    }

    return false;
  }
  public async isTaskPlay(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerMediaIsTaskPlay', this.id);
    if (info.bSucc) {
      return info.result as boolean;
    }

    return false;
  }
  public async getType(): Promise<BaseType.MediaType> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetType', this.id);
    if (info.bSucc) {
      return info.result as BaseType.MediaType;
    }

    return BaseType.MediaType.MtPan;
  }
  public attachAudioTrackPreparedEvent(cb: () => void): void {
    this.eventContainor.on('AudioTrackPrepared', cb);
  }
  public detachAudioTrackPreparedEvent(cb: () => void): void {
    this.eventContainor.off('AudioTrackPrepared', cb);
  }
  public async getAudioTrackList(): Promise<string[]> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetAudioTrackList', this.id);
    if (info.bSucc) {
      return info.result as string[];
    }

    return [];
  }
  public attachAudioTrackSelectChangeEvent(cb: (index: number) => void): void {
    this.eventContainor.on('AudioTrackSelectChange', cb);
  }

  public detachAudioTrackSelectChangeEvent(cb: (index: number) => void): void {
    this.eventContainor.off('AudioTrackSelectChange', cb);
  }


  public async getAudioTrackSelectedIndex(): Promise<number> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetAudioTrackSelectedIndex', this.id);
    if (info.bSucc) {
      return info.result as number;
    }

    return 0;
  }

  public async switchAudioTrack(index: number): Promise<void> {
    await this.apiProxy!.CallApi('AplayerMediaSwitchAudioTrack', this.id, index);
  }

  public async isShowRatio(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerMediaIsShowRatio', this.id);
    if (info.bSucc) {
      return info.result as boolean;
    }

    return false;
  }

  public attachRatioPreparedEvent(cb: () => void): void {
    this.eventContainor.on('RatioPrepared', cb);
  }

  public detachRatioPreparedEvent(cb: () => void): void {
    this.eventContainor.off('RatioPrepared', cb);
  }

  public async getRatioList(): Promise<BaseType.RatioItem[]> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetRatioList', this.id);
    if (info.bSucc) {
      return info.result as BaseType.RatioItem[];
    }

    return [];
  }

  public async getRatioSelectedId(): Promise<string> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetRatioSelectedId', this.id);
    if (info.bSucc) {
      return info.result as string;
    }

    return '';
  }

  public async switchRatio(id: string): Promise<void> {
    await this.apiProxy!.CallApi('AplayerMediaSwitchRatio', this.id, id);
  }

  public async isChangeRatio(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerMediaIsChangeRatio', this.id);
    if (info.bSucc) {
      return info.result as boolean;
    }

    return false;
  }

  public attachProgressChangedEvent(cb: (progress: number) => void): void {
    this.eventContainor.on('ProgressChange', cb);
  }

  public detachProgressChangedEvent(cb: (progress: number) => void): void {
    this.eventContainor.off('ProgressChange', cb);
  }

  public attachProgressSetToLatestEvent(cb: (nPos: number) => void): void {
    this.eventContainor.on('ProgressSetToLatest', cb);
  }

  public detachProgressSetToLatestEvent(cb: (nPos: number) => void): void {
    this.eventContainor.off('ProgressSetToLatest', cb);
  }

  public async getPlayProgress(): Promise<number> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetPlayProgress', this.id);
    if (info.bSucc) {
      return info.result as number;
    }

    return 0;
  }

  public async getDuration(): Promise<number> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetDuration', this.id);
    if (info.bSucc) {
      return info.result as number;
    }

    return 0;
  }

  public async progressMoveTo(pos: number): Promise<void> {
    await this.apiProxy!.CallApi('AplayerMediaProgressMoveTo', this.id, pos);
  }

  public attachPlayStateChangeEvent(cb: (state: BaseType.MediaState) => void): void {
    this.eventContainor.on('PlayStateChange', cb);
  }

  public detachPlayStateChangeEvent(cb: (state: BaseType.MediaState) => void): void {
    this.eventContainor.off('PlayStateChange', cb);
  }

  public async getMediaState(): Promise<BaseType.MediaState> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetMediaState', this.id);
    if (info.bSucc) {
      return info.result as BaseType.MediaState;
    }

    return BaseType.MediaState.MsUnKnown;
  }

  public async getMediaErrorInfo(): Promise<BaseType.PlayErrInfo> {
    let info = await this.apiProxy!.CallApi('AplayerMediaGetMediaErrorInfo', this.id);
    if (info.bSucc) {
      return info.result as BaseType.PlayErrInfo;
    }

    return { errCode: 0, errMsg: '' };
  }

  public attachPlayBufferEvent(cb: (buffer: boolean, local: boolean, type: BaseType.MediaType, speed: number) => void): void {
    this.eventContainor.on('PlayBuffer', cb);
  }

  public detachPlayBufferEvent(cb: (buffer: boolean, local: boolean, type: BaseType.MediaType, speed: number) => void): void {
    this.eventContainor.off('PlayBuffer', cb);
  }
}