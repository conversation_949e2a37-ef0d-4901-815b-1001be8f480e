import * as BaseType from '../base';
import { BtFile } from './bt-file';

export class BtTask {
  private nativeTask: any;
  private infoHash: string = '';
  private bLoadFinish: boolean = false;

  constructor(nativeTask: any) {
    this.nativeTask = nativeTask;
  }

  public getInfoHash(): string {
    if (this.infoHash.length > 0) {
      return this.infoHash;
    }

    this.infoHash = this.nativeTask.getInfoHash();
    return this.infoHash;
  }

  public async updateDownloadIndex(downloadIndexs: number[]): Promise<boolean> {
    return await new Promise((resolve) => {
      this.nativeTask.updateDownloadIndex(downloadIndexs, (b: boolean) => {
        resolve(b);
      });
    });
  }

  public async cancelSubTask(indexs: number[]): Promise<boolean> {
    return await new Promise((resolve) => {
      this.nativeTask.cancelSubTask(indexs, (b: boolean) => {
        resolve(b);
      });
    });
  }

  public async downloadSubTask(indexs: number[]): Promise<boolean> {
    return await new Promise((resolve) => {
      this.nativeTask.downloadSubTask(indexs, (b: boolean) => {
        resolve(b);
      });
    });
  }

  public async deleteSubTask(indexs: number[]): Promise<boolean> {
    return await new Promise((resolve) => {
      this.nativeTask.deleteSubTask(indexs, (b: boolean) => {
        resolve(b);
      });
    });
  }

  public async getBtFileByIndex(index: number): Promise<BtFile | null> {
    return await new Promise((resolve) => {
      this.nativeTask.getBtFileByIndex(index, (f: any | null) => {
        resolve(new BtFile(f));
      });
    });
  }

  public async getBtFileInfoByIndex(index: number): Promise<BaseType.BtFileDownloadInfo | null> {
    return await new Promise((resolve) => {
      this.nativeTask.getBtFileInfoByIndex(index, (info: any | null) => {
        resolve(info);
      });
    });
  }

  public async isOnlyOneFile(): Promise<boolean> {
    return await new Promise((resolve) => {
      this.nativeTask.isOnlyOneFile((n: number) => {
        resolve(n === 1);
      });
    });
  }

  public async getBtFiles(): Promise<BtFile[]> {
    return await new Promise((resolve) => {
      this.nativeTask.getBtFiles((a: any[]) => {
        let btFiles: BtFile[] = [];
        a.forEach((v) => {
          btFiles.push(new BtFile(v));
        })
        resolve(btFiles);
      });
    });
  }

  public async getBtFileInfos(): Promise<BaseType.BtFileDownloadInfo[]> {
    return await new Promise((resolve) => {
      this.nativeTask.getBtFileInfos((a: any[]) => {
        resolve(a);
      });
    });
  }

  public async getDownloadCount(): Promise<number> {
    return await new Promise((resolve) => {
      this.nativeTask.getDownloadCount((n: number) => {
        resolve(n);
      });
    });
  }

  public async getCompleteCount(): Promise<number> {
    return await new Promise((resolve) => {
      this.nativeTask.getCompleteCount((n: number) => {
        resolve(n);
      });
    });
  }

  public async getTotalCount(): Promise<number> {
    return await new Promise((resolve) => {
      this.nativeTask.getTotalCount((n: number) => {
        resolve(n);
      });
    });
  }

  public async updateBtSubFileScheduler(s: BaseType.XLBTTaskSubFileSchedulerType) {
    this.nativeTask.updateBtSubFileScheduler(s);
  }

  public async getBtSubFileScheduler(): Promise<BaseType.XLBTTaskSubFileSchedulerType> {
    return await new Promise((resolve) => {
      this.nativeTask.getBtSubFileScheduler((s: BaseType.XLBTTaskSubFileSchedulerType) => {
        resolve(s);
      })
    });
  }

  public async waitSubLoadFinish(): Promise<void> {
    if (this.bLoadFinish) {
      return;
    }
    return await new Promise((resolve) => {
      this.nativeTask.waitLoadBtFileFinish(() => {
        this.bLoadFinish = true;
        resolve();
      });
    });
  }
}