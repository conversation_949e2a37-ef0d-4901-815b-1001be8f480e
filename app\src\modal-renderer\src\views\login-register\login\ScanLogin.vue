<script setup lang="ts">
import { onMounted, ref, onUnmounted } from 'vue';
import { AccountHelper } from '@root/common/account/client/accountHelper';
import QRCode from 'qrcode'
import { AccountHelperEventKey } from '@root/common/account/account-type';
import {
  ResponseError,
  ITraceInfoOptionLike,
  ErrorType
} from '@xbase/electron_common_kit'
import { PopUpNS } from '@root/common/pop-up';

const qrCodeImg = ref('')
const sanLoginStatus = ref('')
const deviceAuthorizationTaskId = ref<string | undefined>(undefined)

const resetDeviceAuthorization = () => {
  sanLoginStatus.value = ''
  qrCodeImg.value = ''
  deviceAuthorizationTaskId.value = undefined
};

const generateQrCode = async () => {
  try {
    const qrcode = await AccountHelper.getInstance().getSignInQrcode()
    console.log('获取到的二维码', qrcode)

    QRCode.toDataURL(qrcode.url, { margin: 0 }, (error: any, imgCode: string) => {
      if (error) {
        sanLoginStatus.value = 'generate_qr_code_fail'
        console.log('生成二维码失败', error)
      } else {
        qrCodeImg.value = imgCode
        deviceAuthorizationTaskId.value = qrcode.taskId
        sanLoginStatus.value = 'generate_qr_code_succ'
      }
    })

  } catch (error) {
    console.log('获取二维码 Url 失败', error)
    sanLoginStatus.value = 'generate_qr_code_fail'
  }
}

const deviceAuthorizationHandler = async (traceInfoOptionLike: ITraceInfoOptionLike, taskId: string, responseError: ResponseError) => {
  console.log('设备授权监听结果 traceInfoOptionLike', traceInfoOptionLike)
  console.log('设备授权监听结果 taskId', taskId)
  console.log('设备授权监听结果 responseErr', responseError)

  if (deviceAuthorizationTaskId.value === taskId) {
    if (responseError) {
      switch (responseError.error) {
        case ErrorType.EXPIRED_TOKEN:
          sanLoginStatus.value = 'qr_code_expired'
          break;
        case ErrorType.ACCESS_DENIED:
          sanLoginStatus.value = 'qr_code_cancel'
          break;
        case ErrorType.AUTHORIZATION_PENDING:
          let status = 'WAITING_CONNECT';
          console.log('responseError.details', responseError.details)
          if (responseError.details && responseError.details.length > 0) {
            const detail = responseError.details[0];
            status = (detail as any).state;
          }
          if (status === 'WAITING_CONSENT') {
            sanLoginStatus.value = 'scan_succ'
          }
          break;
        default:
          break;
      }
    } else {
      // 登录成功，关掉窗口
      console.log('扫码登录成功')
      const currentWindow = PopUpNS.getCurrentWindow();
      await currentWindow.close();
    }
  }
}

onMounted(() => {
  generateQrCode()
  AccountHelper.getInstance().attachEvent(AccountHelperEventKey.DEVICE_AUTHORIZATION, deviceAuthorizationHandler)
})

// 组件卸载时清理定时器
onUnmounted(() => {
  AccountHelper.getInstance().detachEvent(AccountHelperEventKey.DEVICE_AUTHORIZATION, deviceAuthorizationHandler)
})

const handleRetry = () => {
  resetDeviceAuthorization()
  generateQrCode()
}

</script>

<template>

  <div class="scan-login-container">
    <span class="scan-login-header-title">{{ sanLoginStatus === 'scan_succ' ? "扫码成功" : "扫码登录" }}</span>
    <div class="scan-login-header-content">
      <span v-if="sanLoginStatus === 'scan_succ'">请在移动端确认登录</span>
      <template v-else>
        <span>打开</span> <span class="scan-login-header-content-link">手机迅雷</span> <span> ，扫码登录</span>
      </template>
    </div>

    <div class="scan-login-qrcode">
      <img :src="qrCodeImg" alt="登录二维码">
      <inline-svg class="scan-login-qrcode-xunlei" :src="require('@root/common/assets/img/ic_xunlei_qr.svg')" />
      <div class="scan-login-qrcode-retry-container"
        v-if="['qr_code_expired', 'generate_qr_code_fail', 'scan_succ', 'qr_code_cancel'].includes(sanLoginStatus)">
        <div
          :class="['scan-login-qrcode-retry-mask', 'generate_qr_code_fail' === sanLoginStatus && 'scan-login-qrcode-fail-mask']" />
        <div class="scan-login-qrcode-retry-icon" @click="handleRetry">
          <i class="xl-icon-general-retry-l"></i>
        </div>
      </div>
    </div>
  </div>

</template>

<style lang="scss" scoped>
.scan-login {
  &-container {
    width: 100%;
    margin-top: 64px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &-header-title {
    color: var(--font-font-1, #272E3B);
    text-align: center;
    font-size: 24px;
    line-height: 32px;
  }

  &-header-content {
    margin-top: 12px;
    color: var(--font-font-3, #86909C);
    text-align: center;
    font-size: 14px;
    line-height: 22px;

    &-link {
      color: var(--primary-primary-default, #226DF5);
    }
  }

  &-qrcode {
    margin-top: 36px;
    width: 182px;
    height: 182px;
    border-radius: var(--border-radius-M, 8px);
    border: 0.5px solid var(--border-border-2, #E5E6EB);
    padding: 4px;
    position: relative;

    img {
      height: 100%;
      width: 100%;
    }

    &-xunlei {
      position: absolute;
      height: 24px;
      width: 24px;
      z-index: 10;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    &-retry {
      &-container {
        position: absolute;
        height: 100%;
        width: 100%;
        left: 0;
        top: 0;
      }

      &-mask {
        position: absolute;
        height: 100%;
        width: 100%;
        background: var(--background-background-elevated, #FFF);
        opacity: 0.9;
        z-index: 10;
        border-radius: var(--border-radius-M, 8px);
      }

      &-icon {
        height: 48px;
        width: 48px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 20;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
        background: var(--background-background-elevated, #FFF);

        &:hover {
          cursor: pointer;
        }

        i {
          font-size: 24px;
          color: var(--font-font-3, #86909C);
        }

      }

      &-fail-mask {
        background: var(--button-button2-default, #F2F3F5);
        opacity: 1;
      }
    }
  }
}
</style>