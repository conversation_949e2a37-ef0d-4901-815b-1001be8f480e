import { Logger } from '@root/common/logger';
import fs from 'node:fs'
import path from 'node:path'
import { GetPluginPath } from '@root/common/xxx-node-path';

const logger = new Logger({ tag: 'PluginLoader' })

export class PluginLoader {

  private static _instance: PluginLoader;

  static getInstance () {
    if (PluginLoader._instance) return PluginLoader._instance;
    PluginLoader._instance = new PluginLoader();
    return PluginLoader._instance;
  }

  private currentDir: string = GetPluginPath();
  private configName: string = path.join(this.currentDir, 'config.json');
  private loadedPlugins: Set<string> = new Set();

  constructor () {}

  /**
   * 从配置中加载插件
   */
  loadConfig () {
    try {
      const data = fs.readFileSync(this.configName)
      const pluginConfig = JSON.parse(data.toString());

      logger.log('pluginConfig', pluginConfig)

      Object.keys(pluginConfig).forEach(name => {
        if (process.platform === 'darwin') {
          this.loadPluginMac(name);
        } else {
          this.loadPluginWin(name)
        }
      })
    } catch (err) {
      logger.log('loadConfig error', err)
    }
  }

  /**
   * 按插件名称加载插件
   * @param pluginName 插件名称
   */
  loadPluginWin (pluginName: string) {
    const tempName: string = pluginName.replace('.asar', '');

    let moduleName: string = path.join(this.currentDir, pluginName, 'index');
    let exist: boolean = fs.existsSync(`${moduleName}.js`);

    if (!exist) {
      moduleName = path.join(this.currentDir, `${tempName}.asar/index`);
      exist = fs.existsSync(`${moduleName}.js`);
    }

    if (this.loadedPlugins.has(pluginName)) {
      // 已加载
    } else {
      if (exist) {
        try {
          __non_webpack_require__(moduleName)
          this.loadedPlugins.add(pluginName)
        } catch (err) {
          console.warn('load plugin error', err)
        }
      }
    }
  }

  loadPluginMac (pluginName: string) {
    const tempName: string = pluginName.replace('.asar', '');

    let moduleName: string = path.join(this.currentDir, pluginName, 'index');
    let exist: boolean = fs.existsSync(`${moduleName}.js`);

    if (!exist) {
      moduleName = path.join(this.currentDir, `${tempName}/index`);
      exist = fs.existsSync(`${moduleName}.js`);
    }

    if (this.loadedPlugins.has(pluginName)) {
      // 已加载
    } else {
      if (exist) {
        try {
          __non_webpack_require__(moduleName)
          this.loadedPlugins.add(pluginName)
        } catch (err) {
          console.warn('load plugin error', err)
        }
      }
    }
  }
}
