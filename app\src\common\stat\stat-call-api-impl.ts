import { client } from '@xunlei/node-net-ipc/dist/ipc-client';

export class StatCallApiProxyImplWithIpcClient {
    public async CallApi(name: string, ...args: any[]): Promise<{bSucc: boolean, result?: any}> {
        try {
            const result = await client.callServerFunction(name, ...args);
            return {bSucc: true, result};
        } catch(e) {
            return {bSucc: false};
        }
    }

    public AttachServerEvent(name: string, callback: (...args: any[]) => void): number {

        return client.attachServerEvent(name, (c: any, ...args: any[]) => {
            callback(...args);
        });
    }

    public DetachServerEvent(name: string, cookie: number): void {
        client.detachServerEvent(name, cookie);
    }
    public async DestoryObject(id: string): Promise<void> {
        client.callServerFunction('DestoryObject', id);
    }
}