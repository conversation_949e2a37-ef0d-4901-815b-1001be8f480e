import { platform } from '@root/common/env'
import { useEventListener } from '@vueuse/core'
import { addClass, hasClass, removeClass } from '../utils/dom'
import { onBeforeUnmount, onMounted, type MaybeRefOrGetter, type ShallowRef } from 'vue'
import { Logger } from '@root/common/logger'
import { useWindowControlGlobal } from '../store/windowGlobal'

const logger = new Logger({ tag: 'Drag' })

type TMouseMoveAndClickProps = {
  isSetWindowDraggable?: boolean

  onClick?: (e?: MouseEvent) => void
  onDbClick?: (e?: MouseEvent) => void
  onMouseDown?: (e?: MouseEvent) => void
  onMouseUp?: (e?: MouseEvent) => void
  // onDrag?: (e?: MouseEvent) => void
}

export function useMouseDragAndClick(
  target: Readonly<ShallowRef<HTMLElement | null>>,
  props?: TMouseMoveAndClickProps,
  eventOptions?: MaybeRefOrGetter<boolean | AddEventListenerOptions>,
) {
  let isDown = false
  let isDraged = false
  let isClick = false
  let isSingleClickTrigger = false
  let clickTimer: NodeJS.Timeout | undefined
  // 窗口blur后，首次在窗口内click，会触发1次mousedown, 用这个变量来解决这个问题
  let isMoveCountInMouseDownLagerThenOne: boolean = false

  let clickCountOffset = 0 // 在其他地方click, 然后在target内click, e.detail会累加, 需要处理
  function getClickCount(event: MouseEvent) {
    const clickCount = event.detail - clickCountOffset
    logger.log('clickCount', clickCount)
    return clickCount
  }

  const { windowControlGlobalAction } = useWindowControlGlobal()

  onMounted(() => {
    if (props?.isSetWindowDraggable) {
      if (platform.isMacOS) {
        if (target.value) {
          addClass(target.value, 'draggable')
        }
      }
    }
  })

  onBeforeUnmount(() => {
    if (props?.isSetWindowDraggable) {
      if (platform.isMacOS) {
        if (target.value) {
          removeClass(target.value, 'draggable')
        }
      }
    }
  })

  function handleClick(e: MouseEvent) {
    if (getClickCount(e) === 1) {
      isSingleClickTrigger = false
    }
    clickTimer && clearTimeout(clickTimer)

    switch (getClickCount(e)) {
      case 1: {
        clickTimer = setTimeout(() => {
          isSingleClickTrigger = true
          props?.onClick?.(e)
        }, 200)
        break
      }
      case 2: {
        // ? 鼠标双击全屏
        props?.onDbClick?.(e)
        if (isSingleClickTrigger) {
          props?.onClick?.(e)
        }
        break
      }
    }
  }

  useEventListener(
    target,
    'mousemove',
    (e) => {
      // logger.log('mousemove')
      if (isDown && isMoveCountInMouseDownLagerThenOne) {
        isDraged = true
      }
      if (isDown) {
        isMoveCountInMouseDownLagerThenOne = true
      }
    },
    eventOptions,
  )
  useEventListener(target, 'mouseleave', (e) => {
    // logger.log('mouseleave')
  },
    eventOptions
  )
  useEventListener(
    target,
    'mousedown',
    (e) => {
      // 判断是否为鼠标左键点击
      if (e.buttons !== 1) {
        return
      }
      logger.log('mousedown', e.pageX, e.pageY)

      isDown = true
      isMoveCountInMouseDownLagerThenOne = false

      if (props?.isSetWindowDraggable) {
        if (platform.isWindows) {
          if (
            !e
              .composedPath()
              .find((el) => hasClass(el as HTMLElement, 'none-draggable'))
          ) {
            windowControlGlobalAction.fullyDraggable(true)
          }
        }
      }
      props?.onMouseDown?.(e)
    },
    eventOptions,
  )

  let clearClickTimer: NodeJS.Timeout | undefined
  useEventListener(
    target,
    'mouseup',
    (e) => {
      logger.log('mouseup', e.pageX, e.pageY, e.detail, e.buttons, isClick)

      // ? 在其他地方mousedown，在当前触发mouseup，忽略
      if (!isDown) return

      clearClickTimer && clearTimeout(clearClickTimer)

      // ? 在其他地方click, 然后在target内click, e.detail会累加, 需要处理
      if (!isClick && e.detail > 1) {
        clickCountOffset = e.detail - 1
      } else if (e.detail === 1) {
        clickCountOffset = 0
      }

      isDown = false
      if (getClickCount(e) === 1) {
        isClick = false
      }

      if (props?.isSetWindowDraggable) {
        if (platform.isWindows) {
          windowControlGlobalAction.fullyDraggable(false)
        }
      }

      props?.onMouseUp?.(e)
      if (isClick || !isDraged) {
        // logger.log('handle click')
        handleClick(e)
        isClick = true
        if (getClickCount(e) % 2 === 0) {
          isClick = false
        }
      } else {
        // logger.log('handle drag')
        // props?.onDrag?.(e)
      }
      isDraged = false
      clearClickTimer = setTimeout(() => {
        // ? 清除点击状态
        logger.log('clear isClick')
        isClick = false
      }, 200) // 200ms内算连续点击
    },
    eventOptions,
  )
}
