<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import HistoryList from './components/HistoryList.vue'
import { downloadHistory } from './index'
import { IHistoryTask, IResponseUpdateHistory } from './type'
import XMPMessage from '@root/common/components/ui/message/index'
import { useUserStore } from '@/stores/user'
import { clipboard } from 'electron'
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'
import { usePromptDialog } from '@root/common/components/ui/Dialog/usePromptDialog'

const userStore = useUserStore()
const promptDialog = usePromptDialog()

defineOptions({
  name: 'download-history'
})

const {
  initData,
  getCollection,
  getHistory,
  collectedFiles,
  unCollectedFiles,
  clearHistory,
  delHistoryTask,
  collectionList,
  historyList,
  loading,
  historyTotal,
  collectionTotal,
  initCollectionList,
  unInitData,
  renameHistory
} = downloadHistory()

const filterMenuList = ref([
  {
    key: 'all',
    label: '全部',
    checkbox: true,
    checked: true,
  },
  {
    key: 'collect',
    label: '我喜欢的资源',
    checkbox: true,
    checked: false,
  }
])
const filterData = ref<{key:string, label:string}>({key: 'all', label: '全部',})

const expiresTime = ref(-1)

const currentOperationTasks = ref<IHistoryTask[]>([])
const realRename = ref('')
const isShowDialog = ref(false)

const dialogInfo = ref({
  status: 'delete',
  title: '确定删除该该记录',
  tip: '删除后将无法恢复',
  confirmText: '删除记录',
  icon: ''
})

const collectionRef = ref<Element|null>(null)
const historyRef = ref<Element|null>(null)

const dropdownMenuList = computed(() => {
  return [{
    icon: 'xl-icon-general-clearout-m',
    key: 'delete',
    label: '清空记录',
    disabled: !isShowAll.value || !historyList.value.length
  }]
})

const isShowAll = computed(() => {
  return filterData.value.key === 'all'
})

const handleCheckboxSelect = (key: string) => {
  if (key === filterData.value.key) { return }
  if (key === 'collect') {
    collectionRef.value && collectionRef.value.scrollToItem(1)
    console.log('>>>>>>>>>>> 滚动到顶部')
    initCollectionList()
  }
  filterMenuList.value.map(item => {
    console.log('>>>>>>>>>>> item', item)
    if (item.key === key) {
      filterData.value.label = item.label
      filterData.value.key = key
      item.checked = true
    } else {
      item.checked = false
    }
    return item
  })
}

const handleFilterReset = () => {
  handleCheckboxSelect('all')
} 

const handleClearHistory = () => {
  console.log('> clear history')
  // clearHistory()
  dialogInfo.value = {
    status: 'deleteAll',
    title: '确定清空所有下载记录吗?',
    tip: '清数据空后将无法恢复',
    confirmText: '确认删除',
    icon: ''
  }
  isShowDialog.value = true
}

const handleCollected = async (tasks: IHistoryTask[]) => {
  const res = await collectedFiles({ taskIds: tasks.map(item => item.task_id) })
  XMPMessage({
    message: res.message,
    type: res.result === 0 ? 'success' : 'error'
  })
}

const handleUncollected = async (tasks: IHistoryTask[]) => {
  const res = await unCollectedFiles(tasks.map(item => item.collection_id), isShowAll.value)
  XMPMessage({
    message: res.message,
    type: res.result === 0 ? 'success' : 'error'
  })
}

/** 下载 */
const handleDownloadTask = (tasks: IHistoryTask[]) => {
  console.log('handleDownloadTask', tasks)
  const params = tasks.map(item => {
    return { url: item.url}
  })
  if (!params.length) { return }
  ThunderNewTaskHelperNS.showPreLinkCreateTaskWindow(params, {
    selectedPathType: ThunderNewTaskHelperNS.DownloadPathType.Local,
    autoExpandAll: false,
  });
}

/** 重命名确认 */
const handleRenameConfirm = async (newName: string) => {
  console.log('newName', newName)
  const changeName = newName.trim()
  if (currentOperationTasks.value[0].filename === changeName) {
    XMPMessage({
      message: '请修改后再确认',
      type: 'warning'
    })
    return
  }
  const res = await renameHistory(currentOperationTasks.value[0], changeName)
  if (res.result === 0) {
    XMPMessage({
      message: '重命名成功',
      type: 'success'
    })
  } else {
    XMPMessage({
      message: '重命名失败',
      type: 'error'
    })
  }
  clearSelectedFiles()
  realRename.value = ''
}

const clearSelectedFiles = () => {
  historyRef.value && historyRef.value.handleCleanPicked()
  collectionRef.value && collectionRef.value.handleCleanPicked()
}

const handleUpdateRename = async(task: IHistoryTask) => {
  console.log('handleUpdateRename', task)
  currentOperationTasks.value = [task]
  realRename.value = task.filename || ''
  const result = await promptDialog.prompt({
    title: '重命名',
    defaultValue: realRename.value,
    validateOnInput: true,
    fixedHeight: true, // 启用固定高度模式
    inputType: 'textarea', // 使用文本区域
    selectAll: true,
    inputStyle: {
      height: '82px',
      minHeight: '82px',
      lineHeight: '22px',
      fontSize: '13px',
      resize: 'none',
      color: 'var(--font-font-1, #272E3B)',
      border: '1px solid var(--border-border-primary, #226DF5)',
      background: 'var(--background-background-elevated, #FFF)',
    },
    validator: (value) => {
      if (value.length > 255) {
        return { valid: false, message: `名称最多允许255个字符` };
      }

      if (/[\\/:*?"<>|]/.test(value)) {
        return { valid: false, message: `名称不能包含以下字符: \ / : * ? " <> |` };
      }
      return { valid: true, message: '' };
    },
    placeholder: '',
    hint: '',
    variant: '',
    inputClass: '',
    containerStyle: undefined,
    containerClass: '',
    inputProps: undefined,
    onChange: () => { },
    onConfirm: () => { }
  })

  if (result === false) {
    return
  }
  console.log('>>>>>>>>>> result', result)
  handleRenameConfirm(result)
}

/** 设置过期时间 */
const setExpireTime = () => {
  if (userStore.getIsSVip) {
    expiresTime.value = -1
  } else if (userStore.getIsPlatina) {
    expiresTime.value = 1000 * 60 * 60 * 24 * 365 // 1年过期
  } else {
    expiresTime.value = 1000 * 60 * 60 * 24 * 30 // 30天过期
  }
}

/** 复制链接 */
const copyLink = (tasks: IHistoryTask[]) => {
  if (tasks.length === 1 && tasks[0].url) {
    clipboard.writeText(tasks[0].url)
    XMPMessage({
      message: '复制成功',
      type: 'success'
    })
  } else if (tasks.length > 1) {
    const urls = tasks.filter(item => item.url).map(item => item.url).join('\n')
    if (urls) {
      clipboard.writeText(urls)
      XMPMessage({
        message: '复制成功',
        type: 'success'
      })
    }
  }
}


const handleDelete = (tasks: IHistoryTask[]) => {
  // 弹窗确认删除
  currentOperationTasks.value = tasks
  dialogInfo.value = {
    status: 'delete',
    title: '确定删除下载记录',
    tip: '删除后将无法恢复',
    confirmText: '删除记录',
    icon: ''
  }
  isShowDialog.value = true
}


const handleCloseDialog = () => {
  isShowDialog.value = false
}

const handleDialogConfirm = async () => {
  isShowDialog.value = false
  let res = null as null | { result: number, message: string }
  if (dialogInfo.value.status === 'deleteAll') {
    res = await clearHistory()
  } else if (currentOperationTasks.value.length > 0) {
    res = await delHistoryTask(currentOperationTasks.value.map(item => item.task_id))
  }
  if (res && res.result === 0) {
    XMPMessage({
        message: res.message || '操作失败',
        type: 'success'
      })
  } else {
    XMPMessage({
      message: res?.message ?? '操作失败',
      type: 'success'
    })
  }
  clearSelectedFiles()
}

watch(() => userStore.uid, (newVal) => {
  console.log('> userStore.uid', newVal)
  if (newVal) {
    setExpireTime()
    console.log('>>>>>>>>>>>>>>>>> 登录', newVal)
  } else {
    console.log('>>>>>>>>>>>>>>>>> 未登录')
  }
  clearSelectedFiles()
}, { deep: true })

onMounted(async () => {
  await initData()
  console.log('>>>>>>>>>>>>>>> initData', collectionList.value, historyList.value)
  setExpireTime()
  if (!isShowAll.value) {
    await getCollection()
  }
})

onUnmounted(() => {
  console.log('destroyed')
  unInitData()
})

</script>


<template>
  <div class="download-history">
    <div class="download-history-header">
      <div class="download-history-title">下载记录</div>
      <div class="header-tab-btn">
        <div class="down-history-filter">
          <DropdownMenu
            :items="filterMenuList"
            @select="handleCheckboxSelect"
            side="bottom"
            align="end"
          >
            <Button
              size="sm"
              variant="outline"
              right-icon="xl-icon-general-close-m"
              :has-right-icon="!isShowAll"
              @right-icon-click="handleFilterReset"
            >
              <div
                class="filter-content"
                :class="!isShowAll && 'is-active'"
              >
                <i class="xl-icon-filter"></i>
                <span v-if="!isShowAll">{{ filterData.label }}</span>
              </div>
            </Button>
          </DropdownMenu>
        </div>
        <div class="more-btn-wrapper">
          <DropdownMenu
            :items="dropdownMenuList"
            @select="handleClearHistory" 
            side="bottom"
            align="end"
          >
            <Button variant="outline" is-icon size="sm">
              <i class="xl-icon-more"></i>
            </Button>
          </DropdownMenu>
        </div>
      </div>
    </div>
    <div class="download-history-body">
      <HistoryList
        ref="historyRef"
        v-show="isShowAll"
        :fileList="historyList"
        :total="historyTotal"
        :loading="loading"
        :expiresTime="expiresTime"
        @delete="handleDelete"
        @collection="handleCollected"
        @unCollection="handleUncollected"
        @rename="handleUpdateRename"
        @loadingMore="getHistory"
        @downloadTask="handleDownloadTask"
        @copyLink="copyLink"
      />
      <HistoryList
        ref="collectionRef"
        v-show="!isShowAll"
        :fileList="collectionList"
        :total="collectionTotal"
        :loading="loading"
        :expiresTime="expiresTime"
        @rename="handleUpdateRename"
        @unCollection="handleUncollected"
        @loadingMore="getCollection"
        @downloadTask="handleDownloadTask"
        @copyLink="copyLink"
        isCollection
      />
    </div>
    <Dialog
      :title="dialogInfo.title"
      variant="error"
      :show-trigger="false"
      v-model:open="isShowDialog"
      @confirm="handleDialogConfirm"
      @cancel="handleCloseDialog"
      :confirm-text="dialogInfo.confirmText"
    >
      <div>
        <p v-if="dialogInfo.tip" class="dialog-task-tips">
          {{ dialogInfo.tip }}
        </p>
      </div>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.download-history {
  --padding-left-right: 40px;
  display: flex;
  flex-direction: column;
  .download-history-header {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--padding-left-right);
    background-color: var(--color-bg-1);
  }
  .download-history-title {
    color: var(--font-font-1);
    font-size: 26px;
    line-height: 100%;
    font-weight: 700;
  }
  .header-tab-btn {
    display: flex;
    align-items: center;
  }
  .more-btn-wrapper {
    margin-left: 12px;
  }
  .download-history-body {
    position: relative;
    flex: 1;
    overflow: hidden;
  }
  .filter-content {
    display: flex;
    align-items: center;
    gap: 6px;

    &.is-active {
      color: var(--primary-primary-default);
    }
  }
  .dialog-task-tips {
    color: var(--font-font-2);
    font-size: 13px;
    line-height: 38px;
    height: 38px;
    // margin-top: 20px;
  }
}
</style>