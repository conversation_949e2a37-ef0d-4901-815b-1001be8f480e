<script setup lang="ts">
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import type { DropdownMenuCheckboxItemProps } from 'reka-ui'
import { ref } from 'vue'

type Checked = DropdownMenuCheckboxItemProps['modelValue']

const showStatusBar = ref<Checked>(true)
const showActivityBar = ref<Checked>(false)
const showPanel = ref<Checked>(false)

const dropdownMenuList = [
  {
    key: 'play',
    label: '播放',
    icon: 'xl-icon-general-play-m1',
  },
  {
    key: 'download',
    label: '下载',
    icon: 'xl-icon-download-l',
  },
  {
    key: 'local',
    label: '前往下载位置',
    children: [
      {
        key: 'local',
        label: '本地目录',
      },
      {
        key: 'cloud',
        label: '云盘目录',
      },
    ],
  },
  {
    key: 'rename',
    label: '重命名(R)',
  },
  {
    key: 'copy',
    label: '复制链接(C)',
  },
  {
    key: 'delete',
    label: '删除链接(D)',
    icon: 'xl-icon-delete',
    hasSeparator: true,
  },
]

const dropdownMenuList2 = [
  {
    key: 'play',
    label: '播放',
    checkbox: true,
    checked: true,
  },
  {
    key: 'download',
    label: '下载',
    checkbox: true,
    checked: false,
  },
]

const handleSelect = (key: string) => {
  console.log(key)
}

const handleCheckboxSelect = (key: string) => {
  console.log(key)
  dropdownMenuList2.find(item => item.key === key)!.checked = !dropdownMenuList2.find(item => item.key === key)!.checked
}


</script>

<template>
  <h1>DropdownMenu</h1>

  <div style="display: flex; flex-direction: column; gap: 10px; align-items: center; margin-bottom: 10px;">
    <DropdownMenu :items="dropdownMenuList">
      <Button variant="outline">
        打开默认的 dropdown-menu
      </Button>
    </DropdownMenu>

    <DropdownMenu :items="dropdownMenuList" side="left" align="start">
      <Button variant="outline">
        打开默认的left start dropdown-menu
      </Button>
    </DropdownMenu>

    <DropdownMenu :items="dropdownMenuList" side="left" align="center">
      <Button variant="outline">
        打开默认的left center dropdown-menu
      </Button>
    </DropdownMenu>

    <DropdownMenu :items="dropdownMenuList" side="left" align="end">
      <Button variant="outline">
        打开默认的left end dropdown-menu
      </Button>
    </DropdownMenu>

    <DropdownMenu :items="dropdownMenuList2" @select="handleCheckboxSelect">
      <Button variant="outline">
        打开带有复选框的dropdown-menu
      </Button>
    </DropdownMenu>
  </div>
</template>