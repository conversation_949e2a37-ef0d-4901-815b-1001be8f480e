// @ts-nocheck
import { API_FILE, IExtendDriveFile } from '../types';
import { getMediaType, getPreviewType, isFolder, isReadOnlyFile, isSensitiveFile, Phase } from './drive';

interface SortFile extends API_FILE.DriveFile {
  __order__?: number;
}
export type SortFunctionMapType = keyof typeof sortFunctionMap;
export enum SortOrderType {
  ASC = 1,
  DESC = -1
}
export const sortFunctionGen = <T extends (a: SortFile, b: SortFile, order: number) => number>(
  fn: T,
  order: SortOrderType,
  independentSort = true
) => {
  return (a: SortFile, b: SortFile) => {
    if (a.__order__ === b.__order__) {
      return fn(a, b, order)
    } else {
      return a.__order__! - b.__order__!
    }
  }
}

const modifiedTimeSortFunction = (sortType: SortOrderType) =>
  sortFunctionGen(
    (a: SortFile, b: SortFile, order: number) => {
      if (a.modified_time && b.modified_time && a.modified_time !== b.modified_time) {
        return a.modified_time.localeCompare(b.modified_time) * order
      } else {
        return sortFunctionMap.name(sortType, false)(a, b)
      }
    },
    sortType,
    true
  )

/**
 * 优先看数据中是否有 user_modified_time 的值，如果存在则使用 user_modified_time 进行排序，
 * 否则看数据中是否有 modified_time，如果有则使用 modified_time 进行排序，
 * 最后都没有的情况下使用默认排序
 * @returns
 */
const userModifiedTimeSortFunction = (sortType: SortOrderType) =>
  sortFunctionGen(
    (a: SortFile, b: SortFile, order: number) => {
      if (a.user_modified_time && b.user_modified_time && a.user_modified_time !== b.user_modified_time) {
        return a.user_modified_time.localeCompare(b.user_modified_time) * order
      } else if (a.modified_time && b.modified_time && a.modified_time !== b.modified_time) {
        return a.modified_time.localeCompare(b.modified_time) * order
      } else {
        return sortFunctionMap.name(sortType, false)(a, b)
      }
    },
    sortType,
    true
  )

export function compareStr (str1: string, str2: string): number {
  return str1.localeCompare(str2)
  // ? 应该根据文件名从头开始比较排序，不应该先判断长度
  // if (str1.length === str2.length) {
  //   return str1.localeCompare(str2)
  // }
  // const compareLength = str1.length - str2.length
  // if (compareLength > 0) return 1
  // return -1
}

export const sortFunctionMap = {
  name: (sortType: SortOrderType, independentSort = true, bIsTrash = false ) =>
    sortFunctionGen(
      (a: SortFile, b: SortFile, order: SortOrderType) => {
        const aName = a.name ?? ''
        const bName = b.name ?? ''
        const aSortName = a.sort_name ?? ''
        const bSortName = b.sort_name ?? ''
        let aTime = a.modified_time ?? ''
        let bTime = b.modified_time ?? ''

        // 回收站用刪除時时间
        if(bIsTrash){
          aTime = a.delete_time?? ''
          bTime = b.delete_time?? ''
        }

        // const aFirstCharacterOrder = orderOfCharacter(aName[0])
        // const bFirstCharacterOrder = orderOfCharacter(bName[0])
        // if (aFirstCharacterOrder !== bFirstCharacterOrder) {
        //   return (aFirstCharacterOrder - bFirstCharacterOrder) * order
        // } else {
        //   return aName.localeCompare(bName, 'zh') * order

        // }
        let retNumber : number = 0;

        // 有排序名称，优先按照排序名称比较
        if((aSortName.length > 0) && (bSortName.length > 0)){
          // 排序名称比较
          retNumber = compareStr(aSortName, bSortName) * order;

          // 排序名称相同按照文件名长短比较
          if(retNumber === 0){
            if(aName.length > bName.length){
              retNumber = 1 * order;
            } else if(aName.length < bName.length){
              retNumber = -1 * order;

            } else {
              retNumber = 0;
            }
          }

          if(retNumber === 0){
            // 文件名相同，按创建时间比较
            retNumber = compareStr(aTime, bTime) * order;
          }

          return retNumber;
        } else {
          // 无排序名称按照文件名比较
          retNumber = compareStr(aName, bName) * order;
          if(retNumber === 0) {
            // 文件名相同，按创建时间比较
            retNumber = compareStr(aTime, bTime) * order;
          }
          return retNumber;
        }
      },
      sortType,
      independentSort
    ),
  size: (sortType: SortOrderType) =>
    sortFunctionGen(
      (a: SortFile, b: SortFile, order: number) => {
        const aSize = Number(a.size ?? 0)
        const bSize = Number(b.size ?? 0)
        if (bSize === aSize || (a.kind === 'drive#folder' && b.kind === 'drive#folder')) { // 同大小，或同为文件夹，按名字排序
          return sortFunctionMap.name(sortType)(b, a)
        }
        return (aSize - bSize) * order
      },
      sortType
    ),
  created_time: (sortType: SortOrderType) =>
    sortFunctionGen(
      (a: SortFile, b: SortFile, order: number) => {
        const aCreateTime = a.created_time ?? ''
        const bCreateTime = b.created_time ?? ''
        if (aCreateTime !== bCreateTime) {
          return aCreateTime.localeCompare(bCreateTime) * order
        } else {
          return sortFunctionMap.name(sortType)(a, b)
        }
      },
      sortType,
      true
    ),
  modified_time: modifiedTimeSortFunction,
  user_modified_time: userModifiedTimeSortFunction,
  delete_time: (sortType: SortOrderType) =>
    sortFunctionGen(
      (a: SortFile, b: SortFile, order: number) => {
        const aDeleteTime = Number(a.delete_time ?? 0)
        const bDeleteTime = Number(b.delete_time ?? 0)
        if (aDeleteTime === bDeleteTime) {
          return sortFunctionMap.name(SortOrderType.ASC)(a, b)
        }
        return (aDeleteTime - bDeleteTime) * order
      },
      sortType,
      true
    ),
  effective_time: modifiedTimeSortFunction // 回收站
} as const

// 系统类型文件（放在最前面的文件、文件夹）
export const SYSTEM_FOLDER_TYPE_LIST: string[] = ['RESTORE', 'DOWNLOAD', 'SAFE', 'DECOMPRESS', 'ZHAN', 'FAVORITE', 'KOC_RES']

export function isSystemFolderType (folder_type: string) {
  return SYSTEM_FOLDER_TYPE_LIST.includes(folder_type)
}

/**
 * 获取文件的排序
 * @param item 文件信息
 * @param systemFolderTypeList 系统类型文件的声明列表
 * @returns
 */
export function getOrderOfDriveFile (item: API_FILE.DriveFile, systemFolderTypeList = SYSTEM_FOLDER_TYPE_LIST): number {
  const index = systemFolderTypeList.indexOf(item.folder_type ?? '')
  if (index > -1) {
    return index + Number.MIN_SAFE_INTEGER
  } else {
    if (item.kind === 'drive#file') {
      if (item.phase === Phase.PENDING || item.phase === Phase.RUNNING) {
        return 2
      }
      return 3
    } else {
      if (item.phase === Phase.PENDING || item.phase === Phase.RUNNING) {
        return 0
      } else {
        return 1
      }
    }
  }
}

/**
 * 获取文件的排序（忽略文件类型，文件夹与文件混排）
 * @param item 文件信息
 * @param systemFolderTypeList 系统类型文件的声明列表
 * @returns
 */
export function getOrderOfDriveFileIgnoreType (item: API_FILE.DriveFile, systemFolderTypeList = SYSTEM_FOLDER_TYPE_LIST): number {
  const index = systemFolderTypeList.indexOf(item.folder_type ?? '')
  if (index > -1) {
    return index + Number.MIN_SAFE_INTEGER
  } else {
    if (item.phase === Phase.PENDING || item.phase === Phase.RUNNING) {
      return 2
    }
    return 3
  }
}

/**
 * 格式化时间
 * @param date 时间
 * @param format 格式
 * @returns
 */
export function formatDate (date: string | Date | number, format = 'YYYY-MM-DD HH:mm:ss') {
  const _date = new Date(date);
  const fill = (val) => val < 10 ? `0${val}` : val;

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, (reg) => {
    switch (reg) {
      case 'YYYY':
        return fill(_date.getFullYear());
      case 'MM':
        return fill(_date.getMonth() + 1);
      case 'DD':
        return fill(_date.getDate());
      case 'HH':
        return fill(_date.getHours());
      case 'mm':
        return fill(_date.getMinutes());
      case 'ss':
        return fill(_date.getSeconds());
    }
  });
}

export enum EDriveFileOperation {
  OPEN = 'open',
  OPEN_FOLDER = 'open-folder',
  DOWNLOAD = 'download',
  DELETE = 'delete',
  PLAY = 'play',
  DLNA_PLAY = 'dlna-play',
  MOVE = 'move',
  COPY = 'copy',
  SHARE = 'share',
  RENAME = 'rename',
  BATCH_RENAME = 'batch-rename',
  MOVE_TO_SAFE = 'move-to-safe',
  MOVE_OUT_SAFE = 'move-out-safe',
  DECOMPRESS = 'decompress',
  REPORT = 'report',
  APPEAL = 'appeal',
  ANALYZE_TORRENT = 'analyze-torrent',
  REFRESH = 'refresh',
}

export enum ECloudAddTaskOperation {
  OPEN = 'open',
  OPEN_FOLDER = 'open-folder',
  DELETE = 'delete',
  PLAY = 'play',
  RETRY = 'retry',
  DLNA_PLAY = 'dlna-play',
  REFRESH = 'refresh',
  SELECT_ALL = 'select-all',
}

export enum EShareRecordOperation {
  COPY_LINK = 'copy-link',
  DELETE = 'delete',
}

export interface IGenerateFileContextMenuParams {
  currentParentFile: IExtendDriveFile
  selectedFiles: IExtendDriveFile[]
  isKol?: boolean
  shouldSafeShow?: boolean
  isInShareFolder?: boolean
  isInSafeBoxFolder?: boolean
  isInFavoriteFolder?: boolean
  unzipEnable?: boolean
  showBelongs?: boolean
  disabledMove?: boolean
}
export interface IContextMenuItem {
  key: string
  name: string
  disabled?: boolean
  iconLeft?: string
  iconRight?: string
  iconLeftEmpty?: boolean
  iconRightEmpty?: boolean
  toolbarIcon?: string
  children?: IContextMenuItem[][]
}
export function generateFileContextMenu (params: IGenerateFileContextMenuParams): IContextMenuItem[][] {
  // 父文件夹是否只读
  const isParentReadonly = isReadOnlyFile(params.currentParentFile)
  // 单选文件或选中的文件全部是流畅播相关的文件时，disable 一些右键操作
  const isAllFavorite = params.selectedFiles.every(item => item.space === 'SPACE_FAVORITE')
  // 选中的文件是否全部都是敏感文件
  const isAllSensitive = params.selectedFiles.every(item => isSensitiveFile(item))
  // 选中的文件是否包含敏感或失效文件
  const isSomeSensitive = params.selectedFiles.some(item => isSensitiveFile(item))
  // 选中的文件是否包含 KOC 分享文件
  const isSomeKocRes = params.selectedFiles.some(item => item.space === 'SPACE_KOC_RES')
  // 选中的文件中没有可以操作的文件
  const canOpsFiles = params.selectedFiles.filter(item => !(isSensitiveFile(item) || item.space === 'SPACE_FAVORITE'))
  // 置灰（下载、移动、分享、复制等）操作
  const isDisableOp = isAllFavorite || isAllSensitive || params.isInFavoriteFolder || canOpsFiles.length === 0
  // 保险箱操作
  const shouldSafeShow = params.shouldSafeShow
  const safeContextItem = shouldSafeShow ? [
    {
      key: params.isInSafeBoxFolder ? EDriveFileOperation.MOVE_OUT_SAFE : EDriveFileOperation.MOVE_TO_SAFE,
      name: params.isInSafeBoxFolder ? '移出保险箱' : '移入保险箱',
      disabled: isDisableOp || isSomeKocRes,
      iconLeft: params.isInSafeBoxFolder ? '' : 'xl-icon-general-supersafeBox-m'
    }
  ] : []
  // 如果用户是非 kol 或进入的文件夹是【共享中】的状态时，该文件夹下的文件右键不出现订阅分享相关的操作；
  // 反之，用户所选的文件全部都处于【共享中】的状态时，则显示【取消共享】；否则显示【与订阅者共享】
  const hasSubscribeItem = params.selectedFiles.every((item: any) => item.__hasShareTag__)
  const shouldShowSubscribe = params.isKol && !params.isInShareFolder && !params.isInSafeBoxFolder && !params.isInFavoriteFolder
  const subscribeText = shouldShowSubscribe ? (hasSubscribeItem ? '取消共享' : '与订阅者共享') : ''
  const subscribeContextItem = subscribeText ? [ { name: subscribeText, showNewTag: true } ] : []
  // 保险箱内不显示分享
  const shareContextItem = !params.isInSafeBoxFolder ? [ { key: EDriveFileOperation.SHARE, name: '分享', iconLeft: 'xl-icon-general-share-m', disabled: isDisableOp } ] : []

  // 单文件处理
  let consumeContextItem: any = [],
    consumeMoreContextItem: any = [],
    reportContextItem: any = [],
    mobileContextItem: any = [],
    appealContextItem: any = [],
    belongsContextItem: any = [],
    renameContextItem: any = []
  if (params.selectedFiles.length === 1) {
    const item = params.selectedFiles[0]
    const previewType = getPreviewType({
      extString: item.file_extension,
      mimeType: item.mime_type,
      category: item.file_category
    })
    // 不同类型文件消费相关处理
    if (previewType === 'media') {
      consumeContextItem = [ { key: EDriveFileOperation.PLAY, name: '在线播放', iconLeft: 'xl-icon-general-play-m' } ]
      // 视频才有【投屏播放】
      const mediaType = getMediaType(item.file_extension, item.mime_type, item.file_category)
      if (mediaType === 'video') {
        // consumeMoreContextItem = [ { key: 'dlna-play', name: '投屏播放' } ]
      }
    } else if (['picture'].includes(previewType) || item.kind === 'drive#folder') {
      // consumeContextItem = [ { key: 'open', name: '打开' } ]
    } else if (previewType === 'torrent') {
      // consumeContextItem = [ { key: 'analyze-torrent', name: '解析种子' } ]
    } else if (params.unzipEnable && previewType === 'unzip' && item.phase === Phase.COMPLETE) {
      // consumeContextItem = [ { key: 'compress', name: '在线解压' } ]
    }
    // 敏感资源处理
    if (isSensitiveFile(item) && !isParentReadonly) {
      appealContextItem = [
        [
          { key: EDriveFileOperation.DELETE, name: '删除', iconLeft: 'xl-icon-general-delete-m' },
          // { key: 'appeal',  name: '申诉' }
        ]
      ]
    }
    // 非只读文件夹的单选文件有重命名操作
    if (!isParentReadonly) {
      renameContextItem = [ { key: EDriveFileOperation.RENAME, name: '重命名' } ]
    }
    // 视图、图片视图处理
    if (params.showBelongs) {
      belongsContextItem = [ { key: EDriveFileOperation.OPEN_FOLDER, name: '打开所属文件夹', iconLeft: 'xl-icon-general-openfolder-m' } ]
    }

    // mobileContextItem = [ { key: 'open-mobile', name: '手机端查看' } ]
    // reportContextItem = [ [ { key: 'report', name: '举报' } ] ]
  }
  // 多选文件
  // if (params.selectedFiles.length > 1) {
  //   if (!isParentReadonly) {
  //     renameContextItem = [ { key: 'batch-rename', name: '批量重命名', disabled: isSomeSensitive } ]
  //   }
  // }

  const menuItemList = [
    [
      ...consumeContextItem,
      ...belongsContextItem,
      { key: EDriveFileOperation.DOWNLOAD, name: '下载到本地', iconLeft: 'xl-icon-download', disabled: isDisableOp },
      ...safeContextItem,
    ],
    [
      { key: EDriveFileOperation.DELETE, name: '删除', iconLeft: 'xl-icon-general-delete-m' },
      ...renameContextItem,
    ],
    [
      ...shareContextItem,
      // ...subscribeContextItem,
      { key: EDriveFileOperation.MOVE, name: '移动到', disabled: isDisableOp || isSomeKocRes || params.disabledMove },
      { key: EDriveFileOperation.COPY, name: '复制到', disabled: isDisableOp || isSomeKocRes },
    ],
    // [
    //   {
    //     key: 'more',
    //     name: '更多操作',
    //     iconRight: 'xl-icon-general-direction-right-s',
    //     children: [
    //       [
    //         ...consumeMoreContextItem,
    //         ...mobileContextItem,
    //         // { key: 'refresh', name: '刷新' },
    //       ]
    //     ]
    //   },
    // ],
    ...reportContextItem,
  ]

  if (appealContextItem.length) {
    return appealContextItem
  } else {
    return menuItemList
  }
}

/**
 * 移动端底部菜单栏
 * @param params
 * @returns
 */
export function generateFileToolbarMenu (params: IGenerateFileContextMenuParams): IContextMenuItem[][] {
  // 父文件夹是否只读
  const isParentReadonly = isReadOnlyFile(params.currentParentFile)
  // 单选文件或选中的文件全部是流畅播相关的文件时，disable 一些右键操作
  const isAllFavorite = params.selectedFiles.every(item => item.space === 'SPACE_FAVORITE')
  // 选中的文件是否全部都是敏感文件
  const isAllSensitive = params.selectedFiles.every(item => isSensitiveFile(item))
  // 选中的文件是否包含敏感或失效文件
  const isSomeSensitive = params.selectedFiles.some(item => isSensitiveFile(item))
  // 选中的文件中没有可以操作的文件
  const canOpsFiles = params.selectedFiles.filter(item => !(isSensitiveFile(item) || item.space === 'SPACE_FAVORITE'))
  // 置灰（下载、移动、分享、复制等）操作
  const isDisableOp = isAllFavorite || isAllSensitive || params.isInFavoriteFolder || canOpsFiles.length === 0
  // 保险箱操作
  const shouldSafeShow = params.shouldSafeShow
  const safeContextItem = shouldSafeShow ? [ {
    name: params.isInSafeBoxFolder ? '移出保险箱' : '移入保险箱',
    toolbarIcon: 'xly-icon-safebox',
    disabled: isDisableOp
  } ] : []
  // 如果用户是非 kol 或进入的文件夹是【共享中】的状态时，该文件夹下的文件右键不出现订阅分享相关的操作；
  // 反之，用户所选的文件全部都处于【共享中】的状态时，则显示【取消共享】；否则显示【与订阅者共享】
  const hasSubscribeItem = params.selectedFiles.every((item: any) => item.__hasShareTag__)
  const shouldShowSubscribe = params.isKol && !params.isInShareFolder && !params.isInSafeBoxFolder && !params.isInFavoriteFolder
  const subscribeText = shouldShowSubscribe ? (hasSubscribeItem ? '取消共享' : '与订阅者共享') : ''
  const subscribeContextItem = subscribeText ? [ { name: subscribeText, showNewTag: true } ] : []
  // 保险箱内不显示分享
  const shareContextItem = !params.isInSafeBoxFolder ? [ { name: '分享', toolbarIcon: 'xly-icon-share', disabled: isDisableOp } ] : []

  // 单文件处理
  let consumeContextItem: any = [],
    consumeMoreContextItem: any = [],
    reportContextItem: any = [],
    mobileContextItem: any = [],
    appealContextItem: any = [],
    belongsContextItem: any = [],
    renameContextItem: any = []
  if (params.selectedFiles.length === 1) {
    const item = params.selectedFiles[0]
    const previewType = getPreviewType({
      extString: item.file_extension,
      mimeType: item.mime_type,
      category: item.file_category
    })
    // 不同类型文件消费相关处理
    if (previewType === 'media') {
      consumeContextItem = [ { name: '在线播放', toolbarIcon: 'xly-icon-player' } ]
      // 视频才有【投屏播放】
      const mediaType = getMediaType(item.file_extension, item.mime_type, item.file_category)
      if (mediaType === 'video') {
        consumeMoreContextItem = [ { name: '投屏播放' } ]
      }
    } else if (['picture'].includes(previewType) || item.kind === 'drive#folder') {
      consumeContextItem = [ { name: '打开', toolbarIcon: 'xly-icon-file' } ]
    } else if (previewType === 'torrent') {
      consumeContextItem = [ { name: '解析种子' } ]
    } else if (params.unzipEnable && previewType === 'unzip' && item.phase === Phase.COMPLETE) {
      consumeContextItem = [ { name: '在线解压' } ]
    }
    // 敏感资源处理
    if (isSensitiveFile(item) && !isParentReadonly) {
      appealContextItem = [
        { name: '删除', toolbarIcon: 'xly-icon-delete' },
        // { name: '申诉', toolbarIcon: 'xly-icon-file' }
      ]
    }
    // 非只读文件夹的单选文件有重命名操作
    if (!isParentReadonly) {
      renameContextItem = [ { name: '重命名', toolbarIcon: 'xly-icon-edit' } ]
    }
    // 视图、图片视图处理
    if (params.showBelongs) {
      belongsContextItem = [ { name: '打开所属文件夹' } ]
    }

    mobileContextItem = [ { name: '手机端查看' } ]

    // 文件夹不支持举报
    if (!isFolder(item)) {
      reportContextItem = [{ name: '举报' }]
    }
  }
  // 多选文件
  if (params.selectedFiles.length > 1) {
    if (!isParentReadonly) {
      // renameContextItem = [ { name: '批量重命名', toolbarIcon: 'xly-icon-edit', disabled: isSomeSensitive } ]
    }
  }

  const menuItemList = [
    [
      { name: '下载', toolbarIcon: 'xly-icon-download', disabled: isDisableOp },
      ...shareContextItem,
      { name: '删除', toolbarIcon: 'xly-icon-delete' },
      ...safeContextItem,
      { name: '移动', toolbarIcon: 'xly-icon-move', disabled: isDisableOp },
      { name: '复制', toolbarIcon: 'xly-icon-copy', disabled: isDisableOp },
      ...renameContextItem,
      ...reportContextItem,
      // ...consumeContextItem,
      // ...belongsContextItem,
      // ...subscribeContextItem,
      // ...consumeMoreContextItem,
      // ...mobileContextItem,
      // { name: '刷新' },
    ]
  ]

  if (appealContextItem.length || isSomeSensitive) {
    return [ [ { name: '删除', toolbarIcon: 'xly-icon-delete' } ] ]
  } else {
    return menuItemList
  }
}

// 非法文件名正则匹配
export const FileNameForbidReg = /[\\/:*?"<>|]/;
// 非法文件名正则匹配字符串
export const FileNameForbidRegString = '\\/:*?"<>|';

export function getHighlightFileName (
  type: 'search' | 'replace',
  fileName: string,
  highlightKeyword: string,
  highlightStartTag: string,
  highlightEndTag: string,
) {
  // 用于匹配高亮的元素（避免有某些关键字命中）
  const StartTag = '|?* :^s:|', StartReg = /\|\?\*\s:\^s:\|/g
  const EndTag = '|?* :^e:|', EndReg = /\|\?\*\s:\^e:\|/g

  let result
  if (type === 'replace') {
    result = getReplaceFileName(fileName, highlightKeyword, key => (StartTag + key + EndTag ))
  } else {
    // 默认是搜索的匹配处理
    result = getSearchFileName(fileName, highlightKeyword, key => (StartTag + key + EndTag ))
  }
  return result.replace(StartReg, highlightStartTag).replace(EndReg, highlightEndTag)
}

type replacerFn = (substring: string, ...args: any[]) => string
// 搜索的处理函数
export function getSearchFileName (fileName: string, keyword: string, replacer: replacerFn) {
  if (!fileName || !keyword) return fileName

  let result = fileName
  const keys: string[] = keyword.trim().split(' ')

  keys.forEach((key: string) => {
    if (key.length === 0) return

    key = key.replace(new RegExp('[\\(|\\)|\\[|\\]|\\+|\\^|\\*|\\?|\\{|\\}|\\$]', 'ig'), key => ('\\' + key))
    result = result?.replace(new RegExp(key, 'ig'), replacer)
  })
  return result
}

// 批量重命名的文件名匹配处理函数
export function getReplaceFileName (fileName: string, keyword: string, replacer: replacerFn) {
  if (!fileName || !keyword) return fileName

  let result = fileName
  // 转换特殊字符
  const key = keyword.replace(/[\\/:*?"<>|\.\[\]\^\*\?\*\+\(\)\{\}\$]/g, key => ('\\' + key))
  // 全局匹配，区分大小写
  result = result?.replace(new RegExp(key, 'g'), replacer)
  return result
}
