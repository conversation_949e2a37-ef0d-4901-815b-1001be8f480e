import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { Dialog_Channels } from '@root/common/constant';

export namespace FolderDialogHelper {
  
  /**
   * 对话框选项接口
   */
  export interface IDialogOptions {
    title?: string;
    defaultPath?: string;
    buttonLabel?: string;
    filters?: Array<{
      name: string;
      extensions: string[];
    }>;
    properties?: string[];
  }

  /**
   * 对话框结果接口
   */
  export interface IDialogResult {
    canceled: boolean;
    filePaths: string[];
    filePath?: string;
  }

  /**
   * 打开文件夹选择对话框
   * @param options 对话框选项
   * @returns Promise<IDialogResult>
   */
  export async function openDirectory(options: IDialogOptions = {}): Promise<IDialogResult> {
    try {
      const dialogOptions = {
        title: options.title || '选择文件夹',
        defaultPath: options.defaultPath,
        buttonLabel: options.buttonLabel,
        properties: ['openDirectory', ...(options.properties || [])],
        ...options
      };

      const result = await ipc<PERSON>enderer.invoke(Dialog_Channels.openDirectory, dialogOptions);
      
      return {
        canceled: result.canceled || false,
        filePaths: result.filePaths || [],
        filePath: result.filePaths?.[0]
      };
    } catch (error) {
      console.error('FolderDialogHelper.openDirectory error:', error);
      return {
        canceled: true,
        filePaths: [],
        filePath: undefined
      };
    }
  }

  /**
   * 打开文件选择对话框
   * @param options 对话框选项
   * @returns Promise<IDialogResult>
   */
  export async function openFile(options: IDialogOptions = {}): Promise<IDialogResult> {
    try {
      const dialogOptions = {
        title: options.title || '选择文件',
        defaultPath: options.defaultPath,
        buttonLabel: options.buttonLabel,
        filters: options.filters,
        properties: ['openFile', ...(options.properties || [])],
        ...options
      };

      const result = await ipcRenderer.invoke(Dialog_Channels.openFile, dialogOptions);
      
      return {
        canceled: result.canceled || false,
        filePaths: result.filePaths || [],
        filePath: result.filePaths?.[0]
      };
    } catch (error) {
      console.error('FolderDialogHelper.openFile error:', error);
      return {
        canceled: true,
        filePaths: [],
        filePath: undefined
      };
    }
  }

  /**
   * 打开多文件选择对话框
   * @param options 对话框选项
   * @returns Promise<IDialogResult>
   */
  export async function openFiles(options: IDialogOptions = {}): Promise<IDialogResult> {
    try {
      const dialogOptions = {
        title: options.title || '选择文件',
        defaultPath: options.defaultPath,
        buttonLabel: options.buttonLabel,
        filters: options.filters,
        properties: ['openFile', 'multiSelections', ...(options.properties || [])],
        ...options
      };

      const result = await ipcRenderer.invoke(Dialog_Channels.openFile, dialogOptions);
      
      return {
        canceled: result.canceled || false,
        filePaths: result.filePaths || [],
        filePath: result.filePaths?.[0]
      };
    } catch (error) {
      console.error('FolderDialogHelper.openFiles error:', error);
      return {
        canceled: true,
        filePaths: [],
        filePath: undefined
      };
    }
  }

  /**
   * 打开保存文件对话框
   * @param options 对话框选项
   * @returns Promise<IDialogResult>
   */
  export async function saveFile(options: IDialogOptions = {}): Promise<IDialogResult> {
    try {
      const dialogOptions = {
        title: options.title || '保存文件',
        defaultPath: options.defaultPath,
        buttonLabel: options.buttonLabel,
        filters: options.filters,
        ...options
      };

      const result = await ipcRenderer.invoke(Dialog_Channels.saveFile, dialogOptions);
      
      return {
        canceled: result.canceled || false,
        filePaths: result.filePath ? [result.filePath] : [],
        filePath: result.filePath
      };
    } catch (error) {
      console.error('FolderDialogHelper.saveFile error:', error);
      return {
        canceled: true,
        filePaths: [],
        filePath: undefined
      };
    }
  }

  /**
   * 便捷方法：选择指定默认路径的文件夹
   * @param defaultPath 默认路径
   * @param title 对话框标题
   * @returns Promise<string | null> 返回选择的路径，如果取消则返回null
   */
  export async function selectDirectory(defaultPath?: string, title?: string): Promise<string | null> {
    const result = await openDirectory({
      defaultPath,
      title: title || '选择文件夹'
    });
    
    return result.canceled ? null : (result.filePath || null);
  }

  /**
   * 便捷方法：选择指定默认路径的单个文件
   * @param defaultPath 默认路径
   * @param title 对话框标题
   * @param filters 文件过滤器
   * @returns Promise<string | null> 返回选择的文件路径，如果取消则返回null
   */
  export async function selectFile(
    defaultPath?: string, 
    title?: string, 
    filters?: Array<{name: string; extensions: string[]}>
  ): Promise<string | null> {
    const result = await openFile({
      defaultPath,
      title: title || '选择文件',
      filters
    });
    
    return result.canceled ? null : (result.filePath || null);
  }

  /**
   * 便捷方法：选择多个文件
   * @param defaultPath 默认路径
   * @param title 对话框标题
   * @param filters 文件过滤器
   * @returns Promise<string[]> 返回选择的文件路径数组，如果取消则返回空数组
   */
  export async function selectFiles(
    defaultPath?: string, 
    title?: string, 
    filters?: Array<{name: string; extensions: string[]}>
  ): Promise<string[]> {
    const result = await openFiles({
      defaultPath,
      title: title || '选择文件',
      filters
    });
    
    return result.canceled ? [] : result.filePaths;
  }
} 