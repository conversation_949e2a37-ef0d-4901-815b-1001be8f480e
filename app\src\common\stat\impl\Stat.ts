import * as crypto from 'crypto';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { GetXxxNodePath, GetResourceAppPath } from '@root/common/xxx-node-path'
import requireNodeFile from '../common/require-node-file'
import path from 'path'
import { isDev, platform } from './env';
import os from 'node:os'
import { app_name } from './constant';
import type { UserInfo } from '../../account/impl/accountHelper';
import { WaitSomeThing } from './wait-some-thing';
import dayjs from 'dayjs';

// 协议文档： https://xldc.xunlei.com/manual/installation/restful_api_client.html

//const statUrl: string = 'http://xldc-test.xunlei.com/sync_data';  // 测试环境url
//const appId: string = '13';        // 测试环境appId
//const appKey: string = '15114e6cf0b9411b9f453fae628543e3';      // 测试环境appKey

const statUrl: string = 'https://rcv-xldc.xunlei.com/sync_data';    // 正式环境url
const appId: string = '9';        // 正式环境appId
const appKey: string = '77da6b8584605c459f04a08bf79bab3a';    // 正式环境appKey
export interface CommonBaseInfo {
  userInfo: UserInfo | null;
  device_id?: string;
  platform: 'mac' | 'pc' | 'other';
  app_version?: string;
  app_name: string;
  installChannel: string;
  startChanenl: string;
  aplayerVersion: string;
}

export class XLStat {
  private commonInfo: CommonBaseInfo = {
    userInfo: null,
    device_id: '',
    platform: platform.isWindows ? 'pc' : platform.isMacOS ? 'mac' : 'other',
    app_version: '',
    app_name: app_name,
    installChannel: '',
    startChanenl: '',
    aplayerVersion: '',
  };
  private static stat: XLStat | null = null;
  private waitUpdateUser: WaitSomeThing | null = null

  // private cbs: (()=>void)[] = [];
  // private bInit: boolean = false;
  constructor() {
    const xmpHelper = requireNodeFile(path.join(GetXxxNodePath(), 'thunder_helper.node'));
    this.commonInfo.device_id = xmpHelper.getPeerId();
    try {
      this.commonInfo.app_version = xmpHelper.getFileVersion(process.execPath);
    } catch (e) {
    }
    if (!this.commonInfo.app_version) {
      try {
        const packageJson = requireNodeFile(path.join(GetResourceAppPath(), 'package.json'));
        this.commonInfo.app_version = packageJson.version
      } catch (e) {
      }
    }
    if (!this.commonInfo.app_version) {
      this.commonInfo.app_version = '7.0.0.0' // 兜底
    }

    setTimeout(() => {
      let xmp_helper_version = ''
      try {
        xmp_helper_version = xmpHelper.getFileVersion(process.execPath)
      } catch (e) {
      }

      let package_json_version = ''
      try {
        package_json_version = requireNodeFile(path.join(GetResourceAppPath(), 'package.json')).version
      } catch (e) {
      }

      XLStat.GetInstance().trackEvent('get_app_version', {
        xmp_helper_version,
        package_json_version,
      })
    }, 200);

    this.commonInfo.installChannel = xmpHelper.getInstallChannel();
  }

  public setInfo(aplayerVersion: string, startChanenl: string) {
    this.commonInfo.aplayerVersion = aplayerVersion;
    this.commonInfo.startChanenl = startChanenl;
  }

  public updateUser(userInfo: UserInfo | null) {
    this.commonInfo.userInfo = userInfo;
    this.waitUpdateUser?.done()
  }

  public async waitUpdateUserDone() {
    if (!this.waitUpdateUser) {
      this.waitUpdateUser = new WaitSomeThing()
    } else if (this.waitUpdateUser.isDone()) {
      this.waitUpdateUser.reset()
    }

    await this.waitUpdateUser.waitDone()
  }

  public static GetInstance(): XLStat {
    if (!XLStat.stat) {
      if (global.StatInstance) {
        XLStat.stat = global.StatInstance;
      } else {
        XLStat.stat = new XLStat();
        global.StatInstance = XLStat.stat;
      }
    }
    return XLStat.stat!;
  }

  private async waitInitFinish(): Promise<void> {
    return;
    // if (this.bInit) {
    //   return;
    // }
    // await new Promise<boolean>((resolve) =>{
    //     this.cbs.push(()=>{
    //       resolve(true);
    //     });
    // });
  }

  private async GenSign(data: Record<string, string>, key: string): Promise<string> {
    let signKey: string = '';
    let keys: string[] = Object.keys(data);
    // 键排序
    keys = keys.sort();
    keys.forEach((value, index) => {
      signKey += `${value}=${data[value]}`;
    });
    signKey += key;

    const hash = crypto.createHash('sha1');
    hash.update(signKey);
    const sha1Hash = hash.digest('hex');
    return sha1Hash;
  }

  private getVasType(): string {
    if (this.commonInfo.userInfo && !!this.commonInfo.userInfo.vip_info?.[0] &&
      this.commonInfo.userInfo.vip_info[0].is_vip === '1') {
      return this.commonInfo.userInfo.vip_info[0].vas_type || '';
    }
    return '';
  }

  public async trackEvent(eventName: string, data: Record<string, Object>, axiosConfig?: AxiosRequestConfig<any>): Promise<boolean> {
    await this.waitInitFinish();
    const curTimeStamp = Date.now();
    const currentTime = new Date(curTimeStamp);
    const surTimeString = dayjs(curTimeStamp).format('YYYY-MM-DD[T]HH:mm:ss.SSS[+]08:00');
    let strSign: string = '';
    let statData: Record<string, Object> = {};
    statData['@user_id'] = this.commonInfo.userInfo?.sub || ''
    statData['viptype'] = this.getVasType();
    if (this.commonInfo?.device_id) {
      statData['@distinct_id'] = this.commonInfo?.device_id || '';
    }
    statData['@platform'] = this.commonInfo.platform;
    statData['@event_name'] = eventName;
    statData['@event_time'] = surTimeString;
    statData['app_name'] = this.commonInfo.app_name;
    statData['app_version'] = this.commonInfo.app_version!;
    statData['os_version'] = os.release();
    statData['player_version'] = this.commonInfo.aplayerVersion;
    statData['channel'] = this.commonInfo.installChannel;
    statData['launch_from'] = this.commonInfo.startChanenl;
    // TODO 播放库版本 player_version
    // TODO 渠道 channel
    // TODO 启动来源 launch_from 关联文件/迅雷/桌面图标/任务栏/开始菜单/开机自启/文件右键/其它

    console.log(surTimeString, 'XLStat eventName:', eventName, ',data:', JSON.stringify(data));
    console.log(surTimeString, 'XLStat commonData:', JSON.stringify(statData));
    if (Object.keys(data).length > 0) {
      statData['properties'] = data;
    }

    let strStat: string = JSON.stringify([statData]);
    // console.log('XLStat:', strStat);
    // base 编码
    strStat = Buffer.from(strStat).toString('base64');
    // uri编码
    strStat = encodeURIComponent(strStat);

    let sigData: Record<string, string> = {};
    sigData['appid'] = appId;
    sigData['data_list'] = strStat;
    sigData['gzip'] = '0';
    sigData['t'] = curTimeStamp.toFixed(0);
    strSign = await this.GenSign(sigData, appKey);
    sigData['sign'] = strSign;

    let sendData: string = '';
    Object.keys(sigData).forEach((key, index) => {
      if (index > 0) {
        sendData += '&';
      }
      sendData += `${encodeURIComponent(key)}=${encodeURIComponent(sigData[key])}`;
    });

    const header: any = { 'Content-Type': 'application/x-www-form-urlencoded' };
    //const res = await XLHttps.Post(statUrl, sendData, header);
    let bSuccess: boolean = true;


    try {
      if (!isDev) {
        const res: AxiosResponse = await axios.post(statUrl, sendData, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          timeout: 10000,
          ...axiosConfig,
        });
        // console.log('stat=========,res', res);
        if (
          res !== null &&
          res.status !== undefined &&
          res.status === 200
        ) {
          return true;
        }
      }

      return false;
    } catch (e) {
      console.log('trackEvent, e=', e);
      return false;
    }
  }
}