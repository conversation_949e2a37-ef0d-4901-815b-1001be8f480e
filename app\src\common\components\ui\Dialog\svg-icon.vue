<template>
  <svg
    v-if="type === 'thunder'"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.50958 0H16.4921C18.4304 0 20.0017 1.57116 20.0017 3.50929V16.4907C20.0017 18.4288 18.4304 20 16.4921 20H3.50958C1.57129 20 0 18.4288 0 16.4907V3.50929C0 1.57116 1.57129 0 3.50958 0Z"
      fill="#226DF5"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8.47424 6.96518C8.8364 6.65302 12.0755 3.94587 17.2831 2.33169C17.2831 2.33169 17.3056 2.32422 17.3298 2.32422C17.3689 2.32422 17.4126 2.34375 17.374 2.44602C17.374 2.44602 16.0175 4.33601 15.2596 5.87553C15.2596 5.87553 15.2275 5.95736 15.3279 5.92116C15.3279 5.92116 15.5199 5.86633 15.7144 5.82968C15.7144 5.82968 15.8546 5.80298 15.7598 5.94401C15.7598 5.94401 14.485 7.59621 13.4628 9.4492C13.4856 9.44675 13.5086 9.44433 13.5318 9.44196C13.5318 9.44196 16.3585 9.85345 18.3289 11.1109C18.3289 11.1109 18.4425 11.2252 18.2606 11.2481C18.2606 11.2481 14.0986 11.7828 11.378 12.7231C11.7138 13.4256 12.07 14.1976 12.4476 15.0468C12.9493 16.0177 13.5189 17.208 13.9638 18.3813C13.9638 18.3813 13.9911 18.587 13.8046 18.427C13.8046 18.427 11.9177 16.0835 9.23495 14.4945C9.16904 14.4555 9.04707 14.4014 8.88456 14.3294C7.79293 13.8455 4.87193 12.5508 4.8243 9.55634C4.82066 9.32519 4.86087 9.03777 4.90299 8.73677C4.98382 8.15912 5.07164 7.53148 4.86982 7.15569C4.86982 7.15569 4.68798 6.57277 3.80122 5.82968C3.80122 5.82968 2.94495 5.25815 1.55057 4.618C1.55057 4.618 1.40277 4.54567 1.30049 4.50374C1.30049 4.50374 1.24781 4.3922 1.36865 4.41226C1.36865 4.41226 3.53695 5.04668 4.32423 5.32671C4.32423 5.32671 4.49248 5.38154 4.66526 5.30386C4.66526 5.30386 5.86256 4.81235 6.91599 5.69256L6.91674 5.69347C7.0351 5.75494 7.46338 6.03398 8.25021 7.13178C8.25612 7.12688 8.26234 7.12235 8.26855 7.11783C8.27391 7.11393 8.27926 7.11003 8.2844 7.10591C8.31052 7.08426 8.33807 7.06352 8.36668 7.04354C8.37283 7.03896 8.37896 7.03436 8.3851 7.02977C8.4145 7.00778 8.44383 6.98583 8.47424 6.96518Z"
      fill="white"
    />
  </svg>

  <svg
    v-else-if="type === 'thunderIcon'"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="#1e90ff"
    stroke="#1e90ff"
    stroke-width="0.5"
    stroke-linecap="round"
    stroke-linejoin="round"
  >
    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
  </svg>

  <svg
    v-else-if="type === 'close'"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M3 3L13 13M3 13L13 3"
      stroke="#86909C"
      stroke-width="1.2"
      stroke-linecap="round"
    />
  </svg>

  <svg
    v-else-if="type === 'info'"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    width="24"
    height="24"
  >
    <path
      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"
      fill="#1976d2"
    />
  </svg>

  <svg
    v-else-if="type === 'success'"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    width="24"
    height="24"
  >
    <path
      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
      fill="#4caf50"
    />
  </svg>

  <svg
    v-else-if="type === 'warning'"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    width="24"
    height="24"
  >
    <path
      d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"
      fill="#ff9800"
    />
  </svg>

  <svg
    v-else-if="type === 'error'"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    width="24"
    height="24"
  >
    <path
      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"
      fill="#f44336"
    />
  </svg>
  <svg
    v-else-if="type === 'warning2'"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 1C11.866 1 15 4.13401 15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1ZM8 6.5C7.72386 6.5 7.5 6.72386 7.5 7L7.50781 11.5898C7.55016 11.8231 7.75455 12 8 12C8.27611 12 8.49995 11.7761 8.5 11.5L8.49219 6.91016C8.44985 6.67691 8.24546 6.5 8 6.5ZM8 4C7.58579 4 7.25 4.33579 7.25 4.75C7.25 5.16421 7.58579 5.5 8 5.5C8.41421 5.5 8.75 5.16421 8.75 4.75C8.75 4.33579 8.41421 4 8 4Z"
      fill="#FF4D4F"
    />
  </svg>

  <div
    class="svg-placeholder"
    v-else
  ></div>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    type: {
      type: String,
      required: true,
      validator: value => {
        // List of supported icon types
        return [
          'thunder',
          'thunderIcon',
          'logo',
          'close',
          'info',
          'success',
          'warning',
          'error',
          'warning2',
        ].includes(value)
      },
    },
  },
}
</script>

<style scoped>
.svg-placeholder {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: #f0f0f0;
}
</style>
