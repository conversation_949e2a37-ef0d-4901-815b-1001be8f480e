import { defineConfig } from '@rsbuild/core'
import { pluginSass } from '@rsbuild/plugin-sass'
import { pluginVue } from '@rsbuild/plugin-vue'

import path from 'node:path'

const htmlTemplate = path.join(__dirname, 'template.html')

// ? 多页面入口配置
// const entryDir = path.join(__dirname, "src/entry");
// const files = fse.readdirSync(entryDir);
// const entry = keyBy(
//   files.map((f) => path.join(entryDir, f)),
//   (filePath) => removeFileExtname(filePath)
// );

export default defineConfig({
  html: {
    template: htmlTemplate,
  },
  source: {
    // entry,
    tsconfigPath: path.resolve(process.cwd(), '../../tsconfig.json'),
  },
  output: {
    sourceMap: {
      js: 'source-map',
      css: true,
    },
    cleanDistPath: false,
    /**
     * Important: If set as an absolute path string,
     * it might be escaped in the browser,
     * causing resource request failures.
     * Therefore, it's best to use "auto".
     *
     * 重要：如果设置为绝对路径字符串，可能会在浏览器下被转义导致资源请求失败
     * 所以最好用auto
     */
    assetPrefix: 'auto',
    distPath: {
      root: path.join(process.cwd(), '../../dist/modal-renderer'),
      js: '',
    },
    injectStyles: true,
    filenameHash: false,
  },
  resolve: {
    alias: {
      '@root': path.join(process.cwd(), '../../src'),
      '@': path.join(process.cwd(), 'src'),
      "@xbase/electron_common_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_common_kit/dist/cjs/development/index.js"),
    },
  },
  server: {
    // 强制使用特定启动的 host
    // host: 'localhost',
    port: 9529,
  },
  // performance: {
  //   chunkSplit: {
  //     strategy: 'all-in-one',
  //   },
  // },
  tools: {
    rspack: {
      target: 'electron-renderer',
      output: {
        asyncChunks: false,
      },
    },
    bundlerChain: (chain) => {
      // 配置TypeScript处理，忽略错误
      chain.module
        .rule('typescript')
        .test(/\.(ts|tsx)$/)
        .use('builtin:swc-loader')
        .loader('builtin:swc-loader')
        .options({
          jsc: {
            parser: {
              syntax: 'typescript',
              tsx: true,
            },
            transform: {
              react: {
                runtime: 'automatic',
              },
            },
          },
          // 忽略TypeScript错误
          minify: false,
          sourceMaps: false,
        });
    },
  },

  plugins: [
    pluginVue(),
    pluginSass({
      sassLoaderOptions: {
        sourceMap: true,
        implementation: require('sass'),
        additionalData: `
@import "~@root/common/assets/css/mixins.scss";`,
      },
    }),
  ],
})
