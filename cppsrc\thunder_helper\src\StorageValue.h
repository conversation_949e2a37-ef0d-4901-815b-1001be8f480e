#ifndef XL_A038ABB1_9A00_46DE_95F8_CC9C61AC4B87
#define XL_A038ABB1_9A00_46DE_95F8_CC9C61AC4B87


#include <string>
#include <vector>

typedef unsigned char byte;

enum class ValueType
{
    TYPE_LOWER_BOUND = -1,

    TYPE_NULL,
    TYPE_INT,
    TYPE_BIGINT,
    TYPE_DOUBLE,
    TYPE_STRING,
    TYPE_BLOB,

    TYPE_UPPER_BOUND,
};

class StorageValue
{
public:
    StorageValue(void)
    {
        value_type_ = ValueType::TYPE_NULL;
        index_ = 0;
        memset(&value_, 0, sizeof(ValueUnion));
    }

    StorageValue(int index, int value)
    {
        index_ = index;
        value_type_ = ValueType::TYPE_INT;
        value_.value_int = value;
    }

    StorageValue(int index, int64_t value)
    {
        index_ = index;
        value_type_ = ValueType::TYPE_BIGINT;
        value_.value_int64 = value;
    }

    StorageValue(int index, double value)
    {
        index_ = index;
        value_type_ = ValueType::TYPE_DOUBLE;
        value_.value_double = value;
    }

    StorageValue(int index, const std::string &value)
    {
        index_ = index;
        value_type_ = ValueType::TYPE_STRING;
        value_.value_str = new std::string(value);
    }

    StorageValue(int index, const std::vector< byte > &value)
    {
        index_ = index;
        value_type_ = ValueType::TYPE_BLOB;
        value_.value_blob = new std::string();
        value_.value_blob->assign(value.begin(), value.end());
    }

    StorageValue(int index, const char* value, size_t len)
    {
        index_ = index;
        value_type_ = ValueType::TYPE_BLOB;
        value_.value_blob = new std::string();
        value_.value_blob->assign(value, len);
    }

    ~StorageValue(void)
    {
        if (value_type_ == ValueType::TYPE_STRING)
        {
            if (value_.value_str != NULL)
            {
                delete value_.value_str;
            }
        }

        if (value_type_ == ValueType::TYPE_BLOB)
        {
            if (value_.value_blob != NULL)
            {
                delete value_.value_blob;
            }
        }
    }

public:
    ValueType GetType()
    {
        return value_type_;
    }

    int GetIndex()
    {
        return index_;
    }

    int GetInt()
    {
        return value_.value_int;
    }

    int64_t GetInt64()
    {
        return value_.value_int64;
    }

    double GetDouble()
    {
        return value_.value_double;
    }

    const char* GetString(int& length)
    {
        size_t size = value_.value_str->length();
        length = (size + 1) * sizeof(wchar_t);
        return value_.value_str->c_str();
    }

    const byte* GetBlob(int& size)
    {
        size = value_.value_blob->size();
        return (const byte*)value_.value_blob->c_str();
    }

    bool IsNull()
    {
        return value_type_ == ValueType::TYPE_NULL;
    }

private:
    int index_;
    ValueType value_type_;

    union ValueUnion
    {
        int value_int;
        int64_t value_int64;
        double value_double;
        std::string* value_str;
        std::string* value_blob;
    } value_;
};

#endif // XL_A038ABB1_9A00_46DE_95F8_CC9C61AC4B87
