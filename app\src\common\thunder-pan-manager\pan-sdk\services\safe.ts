import { IRequestCommonConfig, IRequestClassImplement, IRequestCommonResponseT, IRequestHeader } from '..'
import { API_PASSWORD, Dictionary } from '../types'

export interface ISafeApisCommonOptions<T> {
  params?: T
  headers?: IRequestHeader
}

export interface ICheckSafeHasInitParams {
  scene?: string
}

export interface IPasswordParams extends ICheckSafeHasInitParams {
  password: string
}

export interface IResetPasswordParams extends IPasswordParams {
  verification_token: string
}

export interface IVerificationCodeParams {
  client_id: string
}

export interface ISendVerificationCodeParams extends IVerificationCodeParams {
  target?: string
  captcha_token?: string
}

export interface ICheckVerificationCodeParams extends IVerificationCodeParams {
  verification_id: string
  verification_code: string
}

class SafeApis {
  private host: string
  private headers: IRequestHeader
  private config: IRequestCommonConfig
  private requestFn: IRequestClassImplement

  constructor(requestFn: IRequestClassImplement, config: IRequestCommonConfig) {
    this.requestFn = requestFn
    this.config = config
    this.initHost()
    this.initHeaders()
  }

  private initHost () {
    const DRIVE_API: Dictionary = {
      test: 'http://password.office.k8s.xunlei.cn/v1/password',
      prod: 'https://xluser-ssl.xunlei.com/v1/password'
    }
    this.host = DRIVE_API[this.config.env || 'prod']
  }

  private  initHeaders () {
    this.headers = this.config.headers || {}
  }

  /**
   * 检测当前用户是否初始化保险箱
   * @param {ICheckSafeHasInitParam} options
   * @returns API_PASSWORD.IPasswordQueryResponse
   */
  async checkSafeHasInit (options: ISafeApisCommonOptions<ICheckSafeHasInitParams> = {}): Promise<IRequestCommonResponseT<API_PASSWORD.IPasswordQueryResponse>>{
    const url = `${this.host}/query`;
    const data = {
      space: "",
      scene: 'box',
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_PASSWORD.IPasswordQueryResponse>
  }

  /**
   * 初始化保险箱密码
   * @param {IPasswordParams} options password 必须使用 MD5 进行加密后再传入
   * @returns API_PASSWORD.IPasswordInitResponse
   */
  async initPassword (options: ISafeApisCommonOptions<IPasswordParams> = {}): Promise<IRequestCommonResponseT<API_PASSWORD.IPasswordInitResponse>>{
    const url = `${this.host}/init`;
    const data = {
      space: "",
      password: '',
      scene: 'box',
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_PASSWORD.IPasswordInitResponse>
  }

  /**
   * 检验保险箱密码是否正确
   * @param {IPasswordParams} options password 必须使用 MD5 进行加密后再传入
   * @returns API_PASSWORD.IPasswordCheckResponse
   */
  async checkPassword (options: ISafeApisCommonOptions<IPasswordParams> = {}): Promise<IRequestCommonResponseT<API_PASSWORD.IPasswordCheckResponse>>{
    const url = `${this.host}/check`;
    const data = {
      space: "",
      password: '',
      scene: 'box',
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_PASSWORD.IPasswordCheckResponse>
  }

  /**
   * 重置保险箱密码
   * @param {IResetPasswordParams} options password 必须使用 MD5 进行加密后再传入
   * @returns API_PASSWORD.IPasswordResetResponse
   */
  async resetPassword (options: ISafeApisCommonOptions<IResetPasswordParams> = {}): Promise<IRequestCommonResponseT<API_PASSWORD.IPasswordResetResponse>>{
    const url = `${this.host}/reset`;
    const data = {
      space: "",
      password: '',
      verification_token: '',
      scene: 'box',
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_PASSWORD.IPasswordResetResponse>
  }

  /**
   * 发送验证码
   * @param {ISendVerificationCodeParams} options
   * @returns API_PASSWORD.IPasswordSendVerificationCodeResponse
   */
  async sendVerificationCode (options: ISafeApisCommonOptions<ISendVerificationCodeParams> = {}): Promise<IRequestCommonResponseT<API_PASSWORD.IPasswordSendVerificationCodeResponse>>{
    const host = {
      test: 'https://dev-xluser-ssl.xunlei.com',
      prod: 'https://xluser-ssl.xunlei.com'
    }[this.config.env || 'prod'];

    const url = `${host}/v1/auth/verification`;
    const data = {
      client_id: '',
      target: 'CUR_USER',
      captcha_token: '',
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_PASSWORD.IPasswordSendVerificationCodeResponse>
  }

  /**
   * 校验验证码是否正确
   * @param {ICheckVerificationCodeParams} options
   * @returns API_PASSWORD.IPasswordVerifyVerificationCodeResponse
   */
  async checkVerificationCode (options: ISafeApisCommonOptions<ICheckVerificationCodeParams> = {}): Promise<IRequestCommonResponseT<API_PASSWORD.IPasswordVerifyVerificationCodeResponse>>{
    const host = {
      test: 'https://dev-xluser-ssl.xunlei.com',
      prod: 'https://xluser-ssl.xunlei.com'
    }[this.config.env || 'prod'];

    const url = `${host}/v1/auth/verification/verify`;
    const data = {
      client_id: '',
      verification_id: '',
      verification_code: '',
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_PASSWORD.IPasswordVerifyVerificationCodeResponse>
  }
}

export default SafeApis