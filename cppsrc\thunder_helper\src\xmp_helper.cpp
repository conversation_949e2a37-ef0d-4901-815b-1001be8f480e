﻿
#ifdef WIN32
#define WIN32_LEAN_AND_MEAN             // 从 Windows 头中排除极少使用的资料
#include <windows.h>
#include "./win/init.h"
#elif defined(__APPLE__) && defined(__MACH__)
#include "./mac/init.h"
#endif // WIN32

#include <node_api.h>
#include <node.h>
#include <v8.h>

#include <AddonOpt.h>
#include "./WeakReferences/object_life_monitor.h"
#include "./SimpleObjectRef.h"
#include "./FileMonitor.h"
#include "./Storage.h"
#include "./ClipboardWindow.h"
#include "./ShadowWindowAddon.h"
#include "./WorkerThread.h"
#include "./FileRecord.h"


#ifndef CLOSE_LOG
std::shared_ptr<spdlog::logger> g_xl_logger = nullptr;
#endif


void RequestGarbageCollectionForTesting(const v8::FunctionCallbackInfo<v8::Value>& args)
{
    v8::V8::SetFlagsFromString("--expose-gc", 11);
    v8::Isolate* isolate = args.GetIsolate();
    isolate->RequestGarbageCollectionForTesting(v8::Isolate::kFullGarbageCollection);
}

EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports) {
//#ifndef CLOSE_LOG
//    if (SPDLOG_ACTIVE_LEVEL < SPDLOG_LEVEL_OFF) {
//        std::string strLogDir = "D:\\work\\xl\\xmp_xdas_2\\bin\\logs";
//        if (!xl::path::IsFileExist(strLogDir)) {
//            xl::path::CreateDir(strLogDir);
//        }
//        std::string strFile = strLogDir;
//        strFile += std::string("/xmphelper_") +
//            std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
//                std::chrono::system_clock::now().time_since_epoch())
//                .count()) +
//            ".log";
//        std::wstring wstrTemp;
//        xl::text::transcode::UTF8_to_Unicode(strFile.c_str(), strFile.length(), wstrTemp);
//        xl::text::transcode::Unicode_to_ANSI(wstrTemp.c_str(), wstrTemp.length(), strFile);
//        g_xl_logger = spdlog::basic_logger_mt("basic_logger", strFile);
//        g_xl_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e][%P:%t][%l][%!]: %v (%@)");
//        g_xl_logger->set_level(spdlog::level::level_enum::debug);
//        spdlog::flush_every(std::chrono::seconds(2));
//    }
//#endif

    napi_property_descriptor desc[] = {
        {"setObjectFreer", nullptr, ObjectLifeMonitor::BindTo1, nullptr, nullptr, nullptr, napi_default, nullptr},
    };
    InitPlatformApi(env, exports);
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);

    SimpleObjectRefAddon::Init(env, exports);
    FileMonitorAddon::Init(env, exports);
    SqliteStorageAddon::Init(env, exports);
    ClipboardWindowAddon::Init(env, exports);
    ShadowWindowAddon::Init(env, exports);
    DownloadFileRecordAddon::Init(env, exports);

    uv_loop_t* pLoop{ nullptr };
    napi_get_uv_event_loop(env, &pLoop);
    auto spThreadMessage = xl::thread::LibuvThreadMessage::NewObject(pLoop);
    auto threadAffinity = xl::thread::ThreadAffinity::NewObject(spThreadMessage);
    WorkerThread::GetInstance()->Init(threadAffinity);

    return exports;
}
EXTERN_C_END

static napi_module demoModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "xmp_helper",
    .nm_priv = ((void*)0),
    .reserved = {0},
};

NAPI_MODULE(NODE_GYP_MODULE_NAME, Init)