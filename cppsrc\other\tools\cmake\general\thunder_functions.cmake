# function: _tree_source_group
# parameter: root
# parameter: files
# description: Keep structure of source groups
# usage: _tree_source_group(${root} "${files}")
function (_tree_source_group root files)
	foreach(source IN LISTS files)
		file(RELATIVE_PATH _source_rel ${root} ${source})
		get_filename_component(source_path_msvc ${_source_rel} PATH)
		string(REPLACE "/" "\\" source_path_msvc ${source_path_msvc})	
		source_group(${source_path_msvc} FILES ${source})
	endforeach()
endfunction()

function (generate_version_file target_name template_file dest_path)
	if (NOT EXISTS ${template_file})
		return()
	endif()
	
	if (EXISTS ${dest_path})
		return()
	endif()

	file(READ ${template_file} contents)
	string(COMPARE EQUAL "${contents}" "" result)	
	if (result)
		return()
	endif()
	
	string(REGEX REPLACE "%Target%" "${target_name}" correct_contents ${contents})
	file(WRITE ${dest_path} ${correct_contents})
endfunction ()

function (generate_resource_file target_name template_file dest_path)
	generate_version_file(${target_name} ${template_file} ${dest_path})
endfunction ()

# generate the general vbl target
MACRO (generate_thunder_target)
	if (NOT PROJECT_VERSION_BUILD)
		set (PROJECT_VERSION_BUILD ${THUNDER_VERSION_BUILD})
	endif ()

	# add project files
	set (prj_root ${CMAKE_CURRENT_LIST_DIR})

	FILE(GLOB_RECURSE prj_headersFiles ${prj_root}/*.h ${prj_root}/*.hpp)
	FILE(GLOB_RECURSE prj_srcFiles ${prj_root}/*.c ${prj_root}/*.cpp ${prj_root}/*.mm)

	# add msvc resource file 
	set(prj_rcFile)
	set(prj_resource_in_File)
	if(MSVC)	
		if (EXISTS ${prj_root}/${PROJECT_NAME}.rc.in)
			set (prj_resource_in_File ${prj_root}/${PROJECT_NAME}.rc.in)
			THUNDER_LOG_INFO ("Configure ${PROJECT_NAME} ${PROJECT_NAME}.rc file")
			string(TIMESTAMP _YEAR "%Y")
			configure_file(
			  ${prj_resource_in_File}
			  ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.rc
			  @ONLY)
			unset (_YEAR)
		endif ()
	endif ()

	if (EXISTS ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.rc)
		set(prj_rcFile ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.rc)
	endif ()

	add_library ( ${PROJECT_NAME} ${THUNDER_LIBRARY_MODE}
		${prj_headersFiles} 
		${prj_srcFiles}
		${prj_rcFile}
		${prj_resource_in_File})

	# target include directory 
	FOREACH(child ${prj_headersFiles})
		get_filename_component(parent ${child} DIRECTORY)
		LIST(APPEND prj_include_paths ${parent})
	ENDFOREACH()
	LIST(REMOVE_DUPLICATES prj_include_paths)

	target_include_directories (${PROJECT_NAME} PUBLIC
		${prj_include_paths}
	)
	unset(prj_include_paths)

	# 添加源代码可保持文件系统目录结构方式 
	option(THUNDER_launch_source_group "Keep launch structure of source groups" ON)

	if (THUNDER_use_structure_source_group STREQUAL "ON" OR THUNDER_launch_source_group STREQUAL "ON")
		_tree_source_group(${prj_root}/.. "${prj_srcFiles}")
		_tree_source_group(${prj_root}/.. "${prj_headersFiles}")
		_tree_source_group(${prj_root}/.. "${prj_resource_in_File}")
	else ()
		source_group("Source Files" FILES ${prj_srcFiles})
		source_group("Header Files" FILES ${prj_headersFiles})
		source_group("" FILES ${prj_resource_in_File})
	endif ()

	# install 
	if (THUNDER_ENABLE_install)
		set_target_properties(${PROJECT_NAME} PROPERTIES PUBLIC_HEADER "${prj_headersFiles}")
		install(TARGETS ${PROJECT_NAME}
				EXPORT ${PROJECT_NAME}
				RUNTIME DESTINATION ${prj_binary_install_dir}
				PUBLIC_HEADER DESTINATION ${prj_include_install_dir}/${PROJECT_NAME})

		# install window's pdb file
		IF (MSVC)
			INSTALL ( FILES ${CMAKE_PDB_OUTPUT_DIRECTORY}/Debug/${PROJECT_NAME}.pdb DESTINATION ${prj_pdb_install_dir} CONFIGURATIONS Debug )
			INSTALL ( FILES ${CMAKE_PDB_OUTPUT_DIRECTORY}/Release/${PROJECT_NAME}.pdb DESTINATION ${prj_pdb_install_dir} CONFIGURATIONS Release )
		ENDIF ()
	endif ()
ENDMACRO (generate_thunder_target)

MACRO(add_subdirectory_split)
	if(CICD)
		string(SUBSTRING ${subdir} 0 1 SubModule)
		add_subdirectory(${subdir} ${CMAKE_CURRENT_BINARY_DIR}/${SubModule}${ModuleNum})
		math(EXPR ModuleNum "${ModuleNum}+1")
		message(${ModuleNum})
	else()
		add_subdirectory(${subdir})
	endif(CICD)
ENDMACRO(add_subdirectory_split)

MACRO(add_manifest_file MANIFEST_FILE)
	# 嵌入清单文件,不生效，未找到原因
	if (MSVC)
		#set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /MANIFEST:EMBED")
		#set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /ManifestFile:${CMAKE_CURRENT_SOURCE_DIR}/${MANIFEST_FILE}")
		#set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /MANIFEST:EMBED")
		#target_link_options(${PROJECT_NAME} PRIVATE "/MANIFESTINPUT:${CMAKE_CURRENT_SOURCE_DIR}/${MANIFEST_FILE}")
	endif()
ENDMACRO(add_manifest_file)
