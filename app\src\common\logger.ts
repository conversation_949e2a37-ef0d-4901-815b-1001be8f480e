import { isShow<PERSON>ogger } from "./env"

function timeFormat(fmt: string, date: Date) {
  let fmtRes: string = fmt

  let ret
  const opt: { [prop: string]: string } = {
    'Y+': date.getFullYear().toString(), // 年
    'm+': (date.getMonth() + 1).toString(), // 月
    'd+': date.getDate().toString(), // 日
    'H+': date.getHours().toString(), // 时
    'M+': date.getMinutes().toString(), // 分
    'S+': date.getSeconds().toString(), // 秒
    's+': date.getMilliseconds().toString(), // 毫秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  }
  for (const k in opt) {
    ret = new RegExp('(' + k + ')').exec(fmtRes)
    if (ret) {
      fmtRes = fmtRes.replace(
        ret[1],
        ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'),
      )
    }
  }
  return fmtRes
}

type FunctionVariadic = (...args: any[]) => void

export enum TAG_COLOR {
  green = '#34C724',
  blue = '#0969EB',
  red = '#F56C6C',
}

interface LogOptions {
  tag: string
  tagColor?: string
  timeFmt?: string
  saveToIndexDB?: boolean
}

export class Logger {
  debug: FunctionVariadic
  log: FunctionVariadic
  trace: FunctionVariadic
  warn: FunctionVariadic
  error: FunctionVariadic

  tagColor: string
  tag: string
  timeFmt?: string

  constructor(options: LogOptions) {
    this.debug = this._log.bind(this, 'debug')
    this.log = this._log.bind(this, 'info')
    this.trace = this._log.bind(this, 'trace')
    this.warn = this._log.bind(this, 'warn')
    this.error = this._log.bind(this, 'error')

    this.tag = options.tag
    this.tagColor = options.tagColor || TAG_COLOR.blue
    this.timeFmt = options.timeFmt || 'YYYY-mm-dd HH:MM:SS.sss'
  }

  private _log(
    method: 'log' | 'debug' | 'info' | 'trace' | 'warn' | 'error',
    ...data: any[]
  ) {
    if (isShowLogger) {
      const now = new Date()
      const timeStr = this.timeFmt ? timeFormat(this.timeFmt, now) : ''
      console[method](
        ...([] as any[])
          .concat(`[${timeStr}] %c[${this.tag}]`, `color: ${this.tagColor}`)
          .concat(data),
      )
    }
  }
}

export const debuggerLogger = new Logger({
  tag: 'DEBUGGER',
  tagColor: TAG_COLOR.green,
})

export const win7Logger = new Logger({ tag: 'win7' })