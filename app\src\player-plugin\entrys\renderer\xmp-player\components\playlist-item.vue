<script lang="ts" setup>
import type { PlayListItem, PlayListItemMediaInfo } from '@root/common/player/base';
import { useElementVisibility, useIntersectionObserver } from '@vueuse/core';
import { sleep } from '../../utils/tools';
import { computed, nextTick, onMounted, ref, useTemplateRef, watch } from 'vue';
import { LottieAnimation } from "lottie-web-vue"

const props = defineProps<{
  idx: number
  item: PlayListItem
  activeId?: PlayListItem['id'] | null
  playMediaInfoMap: Map<PlayListItem['id'], PlayListItemMediaInfo>
  getMediaInfo: (item: PlayListItem) => void
}>()

const isActive_computed = computed(() => props.activeId === props.item.id)

const $componentVm = useTemplateRef('$componentVm')
const visible_ref = useElementVisibility($componentVm)

async function scrollIntoView() {
  await sleep(20)
  if (isActive_computed.value && !visible_ref.value) {
    $componentVm.value?.scrollIntoView()
  }
}

onMounted(() => {
  scrollIntoView()
})

defineExpose({
  scrollIntoView
})

const formatTime = (time: number) => {
  if (time < 0) {
    return ''
  }

  const hours = Math.floor(time / 3600)
  const minutes = Math.floor((time % 3600) / 60)
  const seconds = Math.floor(time % 60)

  const pad = (num: number) => String(num).padStart(2, '0')

  let str = ''
  if (hours > 0) {
    str += `${pad(hours)}:`
  }
  return str + `${pad(minutes)}:${pad(seconds)}`
}


const targetIsVisible = ref(false)
const isVisibleRendered = ref(false) // 用作懒加载
useIntersectionObserver($componentVm, ([entry], observerElement) => {
  targetIsVisible.value = entry?.isIntersecting || false
})

watch(targetIsVisible, () => {
  if (targetIsVisible.value) {
    if (!isVisibleRendered.value) {
      isVisibleRendered.value = true
      props.getMediaInfo?.(props.item)
    }
  }
})

const forceDefaultCover = ref(false)
const handleCoverLoadError = async (e) => {
  forceDefaultCover.value = true
}

const renderCover = computed(() => props.playMediaInfoMap.get(props.item.id)?.snapshot)
</script>


<template>
  <div v-bind="$attrs" ref="$componentVm" class="playlist-item" :class="{ 'playlist-item-active': isActive_computed }">
    <!-- 左侧 playItem 图片 -->
    <div class="playlist-item-pic">
      <!-- <img class="playlist-item-cover-img" :src="playMediaInfoMap.get(item.id)?.snapshot"
        @error="(e) => (e.target as HTMLImageElement).src = DefaultImg" /> -->
      <div class="playlist-item-cover">
        <img v-if="isVisibleRendered && !forceDefaultCover && renderCover" :src="renderCover"
          @error="handleCoverLoadError" />
        <!-- <img v-else-if="item.type === 'music'" src="@root/common/assets/img/player/im_default_music.png" />
        <img v-else-if="item.type === 'image'" src="@root/common/assets/img/player/im_default_pic.png" /> -->
        <img v-else src="@root/common/assets/img/player/im_default_video.png" />
      </div>
      <LottieAnimation v-if="activeId === item.id" class="playlist-item-playing-icon"
        :animation-data="require('@root/common/assets/lottie/ic-playing.json')" :auto-play="true" :loop="true" />
      <img class="playlist-item-play-icon" v-if="activeId !== item.id"
        src="@root/common/assets/img/player/ic_player_24.svg" />
    </div>
    <!-- 右侧 playItem 信息 -->
    <div class="playlist-item-info">
      <div class="playlist-item-name" :title="item.name">{{ item.name }}</div>
      <div class="playlist-item-duration">
        {{ formatTime((playMediaInfoMap.get(item.id)?.duration || -1) / 1000) }}
      </div>
    </div>
    <div class="playlist-item-data dev-hide">{{ item }}</div>
    <div class="playlist-item-snapshot dev-hide">{{ playMediaInfoMap.get(item.id) }}</div>
  </div>
</template>

<style lang="scss" scoped>
.playlist-item {
  display: flex;
  align-items: center;
  height: 106px;
  cursor: pointer;
  padding: 0 20px;

  &.playlist-item-active {
    background: var(--font-white-5, rgba(255, 255, 255, 0.05));

    .playlist-item-name {
      color: var(--secondary-blue-def, rgba(60, 155, 255, 1));
    }

    .playlist-item-duration {
      color: var(--secondary-blue-def, rgba(60, 155, 255, 1));
    }
  }

  &:not(.playlist-item-active):hover {
    background: var(--font-white-5, rgba(255, 255, 255, 0.05));

    .playlist-item-play-icon {
      display: block;
    }

    .playlist-item-name {
      color: var(--font-white-100);
    }

    .playlist-item-duration {
      color: var(--font-white-100);
    }
  }
}

.playlist-item-pic {
  position: relative;
  flex-shrink: 0;
  width: 160px;
  height: 90px;
}

.playlist-item-cover {
  border-radius: var(--radius-4);
  overflow: hidden;
  width: 100%;
  height: 100%;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.playlist-item-cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.playlist-item-playing-icon {
  position: absolute;
  left: 4px;
  bottom: 4px;

  width: 24px;
  height: 24px;
}

.playlist-item-play-icon {
  display: none;
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.playlist-item-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 12px;
  height: 81px;
}

.playlist-item-name {
  @include Body-L();
  color: var(--font-white-90);
  @include lineClamp(2);
  word-break: break-all;
}

.playlist-item-duration {
  @include Body-M();
  color: var(--font-white-50, rgba(255, 255, 255, 0.5));
}
</style>