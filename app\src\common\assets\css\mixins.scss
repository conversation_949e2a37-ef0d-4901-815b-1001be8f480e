@mixin resetSvgFill() {
  :deep(svg path) {
    fill: currentColor;
  }
}

@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin lineClamp($lineNum: 3) {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $lineNum;
}

@mixin resetInput() {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  outline: none;
  -moz-appearance: textfield;

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

@mixin resetCheckbox() {
  // ? checkbox 内容
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
  background: none;
  cursor: pointer;

  width: 16px;
  height: 16px;
  border: 1px solid var(--font-white-10);
  border-radius: 3px;

  position: relative;

  &:not(:checked):not(:indeterminate) {
    background: var(--background-neutral-1);
  }

  &:checked {
    background: var(--primary-def);
    border-color: var(--primary-def);

    &::after {
      content: "";
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 8px;
      height: 5px;
      border: 2px solid #fff;
      border-top: none;
      border-right: none;
      transform-origin: center;
      transform: translate(-50%, -65%) rotate(-45deg);
    }
  }

  &:indeterminate {
    background: var(--primary-def);
    border-color: var(--primary-def);

    &::after {
      content: "";
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 10px;
      height: 2px;
      background: var(--font-white-100);
      border-radius: 1px;
    }
  }

  &:hover {
    border-color: var(--primary-def);

    &:checked {
      background: var(--primary-hov);
      border-color: var(--primary-hov);
    }

    &:indeterminate {
      background: var(--primary-hov);
      border-color: var(--primary-hov);
    }

    &:focus {
      outline-color: var(--primary-hov);
    }
  }

  &:focus {
    outline: 1px solid var(--primary-def);
  }
}

@mixin resetRadio() {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
  background: none;
  cursor: pointer;

  width: 16px;
  height: 16px;
  border: 1px solid var(--font-white-20);
  border-radius: 50%;

  position: relative;

  &:not(:checked) {
    background: var(--background-neutral-1);
  }

  &:checked {
    background: var(--primary-def);
    border-color: var(--primary-def);

    &::after {
      content: "";
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 6px;
      height: 6px;
      background: #fff;
      border-radius: 50%;
    }
  }

  &:hover {
    border-color: var(--primary-def);
  }

  &:focus {
    outline: 1px solid var(--primary-def);
  }
}

@mixin absolute-full() {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

@mixin absolute-center() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

body {
  .xly-loading {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: var(--font-font-1);
    box-sizing: border-box;

    .xly-loading__img {
      display: block;
      width: 36px;
      height: 36px;
      background: url(../img/icons/loading.png) center/ 100% no-repeat;
    }
  }
}

body.is-svip {
  .xly-loading__img {
    background-image: url(../img/icons/loading-svip.png);
  }
}

@mixin Heading-H1 {
  font-size: var(--heading-h1-font-size);
  font-weight: var(--heading-h1-font-weight);
  line-height: var(--heading-h1-line-height);
}

@mixin Heading-H2 {
  font-size: var(--heading-h2-font-size);
  font-weight: var(--heading-h2-font-weight);
  line-height: var(--heading-h2-line-height);
}

@mixin Heading-H3 {
  font-size: var(--heading-h3-font-size);
  font-weight: var(--heading-h3-font-weight);
  line-height: var(--heading-h3-line-height);
}

@mixin Heading-H4 {
  font-size: var(--heading-h4-font-size);
  font-weight: var(--heading-h4-font-weight);
  line-height: var(--heading-h4-line-height);
}

@mixin Heading-H5 {
  font-size: var(--heading-h5-font-size);
  font-weight: var(--heading-h5-font-weight);
  line-height: var(--heading-h5-line-height);
}

@mixin Heading-H6 {
  font-size: var(--heading-h6-font-size);
  font-weight: var(--heading-h6-font-weight);
  line-height: var(--heading-h6-line-height);
}

@mixin Heading-H7 {
  font-size: var(--heading-h7-font-size);
  font-weight: var(--heading-h7-font-weight);
  line-height: var(--heading-h7-line-height);
}

@mixin Body-XXL {
  font-size: var(--body-xxl-font-size);
  font-weight: var(--body-xxl-font-weight);
  line-height: var(--body-xxl-line-height);
}

@mixin Body-XL {
  font-size: var(--body-xl-font-size);
  font-weight: var(--body-xl-font-weight);
  line-height: var(--body-xl-line-height);
}

@mixin Body-L {
  font-size: var(--body-l-font-size);
  font-weight: var(--body-l-font-weight);
  line-height: var(--body-l-line-height);
}

@mixin Body-M {
  font-size: var(--body-m-font-size);
  font-weight: var(--body-m-font-weight);
  line-height: var(--body-m-line-height);
}

@mixin Body-S {
  font-size: var(--body-s-font-size);
  font-weight: var(--body-s-font-weight);
  line-height: var(--body-s-line-height);
}

@mixin Body-XS {
  font-size: var(--body-xs-font-size);
  font-weight: var(--body-xs-font-weight);
  line-height: var(--body-xs-line-height);
}

@mixin Action-XL {
  font-size: var(--action-xl-font-size);
  font-weight: var(--action-xl-font-weight);
  line-height: var(--action-xl-line-height);
}

@mixin Action-L {
  font-size: var(--action-l-font-size);
  font-weight: var(--action-l-font-weight);
  line-height: var(--action-l-line-height);
}

@mixin Action-M {
  font-size: var(--action-m-font-size);
  font-weight: var(--action-m-font-weight);
  line-height: var(--action-m-line-height);
}

@mixin Action-S {
  font-size: var(--action-s-font-size);
  font-weight: var(--action-s-font-weight);
  line-height: var(--action-s-line-height);
}

@mixin Caption-M {
  font-size: var(--caption-m-font-size);
  font-weight: var(--caption-m-font-weight);
  line-height: var(--caption-m-line-height);
}

@mixin Caption-S {
  font-size: var(--caption-s-font-size);
  font-weight: var(--caption-s-font-weight);
  line-height: var(--caption-s-line-height);
}