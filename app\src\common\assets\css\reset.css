body,
dd,
dl,
form,
h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0
}

a:focus,
button:focus,
div:focus,
p:focus,
span:focus {
  outline: 0
}

button,
input,
ol,
ul {
  margin: 0;
  padding: 0
}

ol,
ul {
  list-style: none
}

body,
button,
input,
textarea {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  border-radius: 0;
  outline: none;
  background: transparent;
  color: inherit;
  font-size: inherit;
  line-height: inherit;
  font-family: inherit;
  resize: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  -webkit-font-smoothing: antialiased;
}

textarea:focus {
  border: none;
  outline: none;
  box-shadow: none;
}


body {
  word-wrap: break-word
}

a {
  color: var(--color-link);
  text-decoration: none
}

button {
  cursor: pointer
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400
}

h1 {
  font-size: 18px
}

h2 {
  font-size: 16px
}

h3 {
  font-size: 14px
}

h4,
h5,
h6,
p {
  font-size: inherit
}

small,
sub,
sup {
  font-size: 11px
}

hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eee
}

table {
  border-collapse: separate;
  border-spacing: 0
}

i {
  font-style: normal
}