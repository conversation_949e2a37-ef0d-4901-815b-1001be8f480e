/**
 * @description: 工具相关的 async/await 的封装, AW 后缀为 async/await 缩写
 * @author: chenguideng
 */
import crypto from 'crypto';
import { Logger } from '@root/common/logger';
const logger = new Logger({ tag: 'ToolsUtilitiesAWNS' })

export namespace ToolsUtilitiesAWNS {
  /**
   * 加密
   * @param input 原始buffer
   * @param aesKey 秘钥
   * @returns {Buffer}
   */
  export function encryptBuffer(input: Buffer, aesKey: string | Buffer): Buffer {
    let output: Buffer = null;
    try {
      const cipher: crypto.Cipher = crypto.createCipheriv('aes-128-ecb', aesKey, '');
      const update: Buffer = cipher.update(input);
      const final: Buffer = cipher.final();
      output = Buffer.concat([update, final]);
    } catch (err) {
      logger.warn('encryptBuffer', err);
    }
    return output;
  }

  /**
   * 解密
   * @param input 原始buffer
   * @param aesKey 秘钥
   * @returns {Buffer}
   */
  export function decryptBuffer(input: Buffer, aesKey: string | Buffer): Buffer {
    let output: Buffer = null;
    try {
      const decipher: crypto.Decipher = crypto.createDecipheriv('aes-128-ecb', aesKey, '');
      const update: Buffer = decipher.update(input);
      const final: Buffer = decipher.final();
      output = Buffer.concat([update, final]);
    } catch (err) {
      logger.warn('decryptBuffer', err);
    }
    return output;
  }

  /**
   * sha1字符串，返回hex
   * @param input 原始buffer
   * @returns {string}
   */
  export function encryptSha1Buffer(input: Buffer): string {
    let output: string = null;
    try {
      const hmac: crypto.Hash = crypto.createHash('sha1');
      output = hmac.update(input).digest('hex');
    } catch (err) {
      logger.warning('encryptSha1Buffer', err);
    }
    return output;
  }
  /**
   * 按算法Hmac加密字符串，返回hex
   * @param algorithm 算法
   * @param key key
   * @param input 原始buffer
   * @returns {string}
   */
  export function encryptHmacBuffer(
    algorithm: string,
    key: string | Buffer,
    input: string,
    encoding: crypto.BinaryToTextEncoding = 'hex'
  ): string {
    let output: string = null;
    try {
      const hmac = crypto.createHmac(algorithm, key);
      output = hmac.update(input, 'utf8').digest(encoding);
    } catch (err) {
      logger.warning('encryptSha1Buffer', err);
    }
    return output;
  }
}
