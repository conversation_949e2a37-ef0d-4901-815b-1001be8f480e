## 云盘 SDK

云盘基础数据模型：

- [x] 云盘数据请求接口
  - [x] 云盘基础数据
  - [x] 特权数据接口
  - [x] 文件列表
  - [x] 根据 parentId 获取全部文件
  - [x] 根据 parentId 获取全部文件夹
  - [x] 获取文件详情
  - [x] 全部视频文件列表
  - [x] 全部图片文件列表
  - [x] 新建文件夹
  - [x] 创建文件（上传文件）
  - [x] 删除文件
  - [x] 移动文件
  - [x] 复制文件
  - [x] 将文件放入回收站
  - [x] 最近播放列表
  - [x] 最近添加文件列表
  - [x] 批量删除最近记录
  - [x] 分享列表
  - [x] 创建分享
  - [x] 取消分享
  - [x] 回收站列表
  - [x] 回收站还原文件
  - [x] 回收站删除文件
  - [x] 获取保险箱初始化状态
  - [x] 初始化保险箱
  - [x] 校验保险箱密码
  - [x] 重置保险箱密码
  - [x] 发送验证码
  - [x] 校验验证码
  - [x] 判断文件是否处于【共享】文件夹中
  - [x] 获取云添加列表
  - [x] 获取单个云添加任务详情
  - [x] 获取单个云添加任务 status
  - [x] 批量获取云添加任务 status
  - [x] 批量删除云添加任务
- [x] 通用方法库
  - [x] 文件排序
  - [x] 文件大小数值转换
  - [x] 获取文件类型
  - [x] 获取文件扩展名
  - [x] 判断是否系统文件
  - [x] 判断是否敏感文件
  - [x] 判断是否只读文件
  - [x] 安全解析 JSON
  - [x] Object.assign
  - [x] Function.bind
  - [x] Function.call
  - [x] Function.apply
  - [x] 数组扁平化（flatten）
  - [x] 数组切割（chunk）
- [ ] 云盘数据库操作
- [ ] 云盘数据流转控制器

#### types/api

此文件夹下大部分的 API 类型声明可通过`npm run types:api`命令，从`swagger.json`生成。

不支持自动生成的API有：
- API_PASSWORD（[文档](https://md.office.k8s.xunlei.cn/8g0YgCaoSaGle_mZL0opbQ)）
- API_DECOMPRESS（[文档](http://wiki.xunlei.cn/pages/viewpage.action?pageId=55675309)）
