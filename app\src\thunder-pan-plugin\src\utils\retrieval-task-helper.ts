import { API_FILE } from "@root/common/thunder-pan-manager/pan-sdk/types"
import { objectAssign, sleep } from "@root/common/thunder-pan-manager/pan-sdk/utils/basic"
import { isFolder } from "@root/common/thunder-pan-manager/pan-sdk/utils/drive"
import * as DownloadKernel from '@root/common/task/base'
import { ThunderPanClientSDK } from "@root/common/thunder-pan-manager/client"
import { useUserStore } from "../store/user-store"
import { TaskManager } from "@root/common/task/impl/task-manager"
import { config } from "@root/common/config/config"
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { IPC_API_NAME } from "@root/common/thunder-pan-manager/common/ipc-define"
import { EventEmitter } from "events"
import { CreateSafeBoxDialog } from "@/components/safe-box-dialog"
import { BaseManager } from "@/manager/base-manager"
import { UserConfigTableManager } from "@/db/user-config"
import { PopUpNS } from '@root/common/pop-up'
import * as PopUpTypes from '@root/common/pop-up/types'
import hex_md5 from 'blueimp-md5'
import { Task } from "@root/common/task/impl/task"
import { Logger } from "@root/common/logger"

const logger = new Logger({ tag: 'PanRetrievalTaskHelper' })

interface IGetDownloadTaskInfosOptions {
  userId: string
  categoryId: number
  sandboxSavePath: string
}

interface IGroupTaskInfo extends DownloadKernel.NewTaskInfo {
  _subTaskInfos: DownloadKernel.NewGroupTaskInfo[]
  _subTaskFetchErrorCode?: EBatchCreateTaskErrorCode
}

interface IFolderSubFile extends API_FILE.DriveFile {
  _retrievalPath: string
}

export enum EBatchCreateTaskErrorCode {
  OVERLIMIT = 'overlimit',
  FETCH_ERROR = 'fetch_error'
}

export interface IBatchCreateDownloadTaskResponse {
  taskIds: number[]
  overlimitList: API_FILE.DriveFile[]
  errorList: API_FILE.DriveFile[]
  needToDownloadFileCount: number
  repeatTaskAction: 'continue' | 'ignore'
}

export interface IStartPreprocessResult {
  totalFileSize: number
  isAllOverlimit: boolean
  overlimitList: API_FILE.DriveFile[]
  needToDownloadFileCount: number
  isAllError: boolean
  errorList: API_FILE.DriveFile[]
}

export class RetrievalTaskHelper extends EventEmitter {
  private static _instance: RetrievalTaskHelper;

  static getInstance () {
    if (RetrievalTaskHelper._instance) return RetrievalTaskHelper._instance;
    RetrievalTaskHelper._instance = new RetrievalTaskHelper();
    return RetrievalTaskHelper._instance;
  }

  private preprocessReady: boolean = false
  private preprocessOverlimitList: API_FILE.DriveFile[] = []
  private preprocessErrorList: API_FILE.DriveFile[] = []
  private needToDownloadFiles: Map<string, { originFile: API_FILE.DriveFile; files: API_FILE.DriveFile[] }> = new Map()

  private _waitStartPreprocess () {
    if (this.preprocessReady) return true
    return new Promise((resolve) => {
      const handler = () => {
        resolve(true)
        this.off('RetrievalTaskHelper_EventKey_preprocessReady', handler)
      }
      this.on('RetrievalTaskHelper_EventKey_preprocessReady', handler)
    })
  }

  private _resetPreprocess () {
    this.preprocessReady = false
    this.preprocessOverlimitList = []
    this.preprocessErrorList = []
    this.needToDownloadFiles.clear()
  }

  private async _shouldCreateTasksAfterCheckRepeatTasks (taskInfoList: DownloadKernel.NewTaskInfo[]) {
    const preCheckTaskInfoList = taskInfoList.map(info => ({ taskInfo: info }))
    const repeatResponse = await TaskManager.GetInstance().findRepeatTask(preCheckTaskInfoList)

    logger.log('checkRepeatResponse', repeatResponse)
    let action = 'continue'
    let newTaskInfoList: DownloadKernel.NewTaskInfo[] = taskInfoList
    // 如果有重复任务，弹窗确认需要重新下载还是跳过
    if (repeatResponse.length) {
      let duplicateTaskList: { task: Task; index: number }[] = []
      let duplicateTaskDetails: any[] = []

      for (const repeatTask of repeatResponse) {
        const taskInfo = await TaskManager.GetInstance().findTaskById(repeatTask.taskId)

        if (taskInfo) {
          duplicateTaskList.push({
            task: taskInfo,
            index: repeatTask.index,
          })

          duplicateTaskDetails.push({
            taskId: repeatTask.taskId,
            taskType: taskInfo.getType(),
            taskBase: taskInfo.getTaskBase(),
          })
        }
      }

      if (duplicateTaskDetails.length) {
        action = 'ignore'
        // 确定弹窗位置相关配置
        let popupOptions: any = {
          parentId: -1, // 传递-1，表示不以主窗口为父窗口
          relatePos: PopUpTypes.RelatePosType.CenterParent,
          replaceView: false, // 该值设置为true，则如果已经存在窗口，则更新数据到当前窗口
          singleton: false, // 关键：确保只有一个创建任务窗口
          title: '',
          windowWidth: 460,
          windowHeight: 278,
          duplicateTaskDetails: duplicateTaskDetails,
        }

        const payload = await PopUpNS.popup('duplicate-task', popupOptions)

        // 处理用户操作结果
        if (payload.action === PopUpTypes.Action.OK) {
          const args = payload.args as any
          const picked = args.picked

          if (picked === 'skip') {
            // 过滤忽略掉的任务
            // const ignoreIndexList = duplicateTaskList.map(t => t.index)

            newTaskInfoList = []
            // taskInfoList.forEach((taskInfo, index) => {
            //   if (!ignoreIndexList.includes(index)) {
            //     newTaskInfoList.push(taskInfo)
            //   }
            // })
            // action = 'continue'
          } else if (picked === 'redownload') {
            // 重新下载需要删除原任务
            for (const taskInfo of duplicateTaskList) {
              await taskInfo.task.deleteTask(true)
            }
            action = 'continue'
          } else if (picked === 'cancel') {
            newTaskInfoList = []
          }
        } else {
          newTaskInfoList = []
        }
      }
    }

    logger.log('checkRepeat next', action, newTaskInfoList)
    return {
      action,
      newTaskInfoList
    }
  }

  async startPreprocess (files: API_FILE.DriveFile[]) {
    this._resetPreprocess()

    let totalFileSize = 0
    // 获取服务接口文件数量超限的任务
    const overlimitList: API_FILE.DriveFile[] = []
    // 获取文件数的服务接口报错数量
    const errorList: API_FILE.DriveFile[] = []
    // 倒序创建任务，在任务列表才会是正常顺序
    const preFiles = files.reverse()
    // 保险箱 token
    const safeBoxToken = BaseManager.getInstance().getSafeBoxInfo().token
    for (let file of preFiles) {
      // 文件夹
      if (isFolder(file)) {
        // 获取文件夹下所有文件列表
        const subFilesRes = await ThunderPanClientSDK.getInstance().getAllFlattenSubFilesByParentId(file.id!, file.space!, '', {
          headers: {
            'space-authorization': file.space === 'SPACE_SAFE' ? safeBoxToken : ''
          }
        })

        if (subFilesRes.success && subFilesRes.data && subFilesRes.data.files) {
          totalFileSize += subFilesRes.data.files.map(f => Number(f.size)).reduce((pre, current) => { return pre + current }, 0)
          // 如果有子文件
          if (subFilesRes.data.files.length) {
            this.needToDownloadFiles.set(file.id!, { originFile: file, files: subFilesRes.data.files })
          }
        }
        // 超过文件数量限制，根据服务端配置来（老方案存在性能问题限制更少，新方案“前缀”可支持更多）
        else if (subFilesRes.error && (subFilesRes.error.error_description.includes('count error'))) {
          overlimitList.push(file)
        } else {
          errorList.push(file)
        }
      }
      // 常规文件
      else {
        totalFileSize += Number(file.size ?? 0)
        this.needToDownloadFiles.set(file.id!,  { originFile: file, files: [ file ] })
      }
    }
    this.preprocessReady = true
    this.preprocessOverlimitList = overlimitList
    this.preprocessErrorList = errorList
    this.emit('RetrievalTaskHelper_EventKey_preprocessReady')

    return {
      isAllOverlimit: files.length === overlimitList.length,
      isAllError: files.length === errorList.length,
      needToDownloadFileCount: this.needToDownloadFiles.size,
      errorList,
      overlimitList,
      totalFileSize,
    }
  }

  async startDownloadByPreprocess (savePath?: string) {
    // 等待预处理完成
    await this._waitStartPreprocess()

    const { userStoreState } = useUserStore()
    const userId = userStoreState.curUser.userId
    const needToDownloadFileCount = this.needToDownloadFiles.size
    // 下载任务信息列表
    const taskInfoList: DownloadKernel.NewTaskInfo[] = []
    // 创建成功的任务 id 列表
    const taskIds: number[] = []

    if (userId && !!this.needToDownloadFiles.size) {
      const localSavePath = savePath || await config.getValue('ThunderPanPlugin', 'defaultDownloadPath', 'C:\\迅雷云盘') as string
      TaskManager.GetInstance().GetCategoryManager().setCurrentPanUserId(userId)
      const currentPanCategory = await TaskManager.GetInstance().GetCategoryManager().getCurrentPanCategory()
      // 下载任务配置
      const taskOptions = {
        userId: userId,
        categoryId: currentPanCategory?.getId()!,
        sandboxSavePath: localSavePath,
      }
      // 组装下载任务信息
      this.needToDownloadFiles.forEach(fileInfo => {
        if (isFolder(fileInfo.originFile)) {
          const subFilesRes = this.packageSubTaskInfos(fileInfo.files, fileInfo.originFile.name!)
          const subTaskInfos = this.getGroupSubTaskInfos(subFilesRes, taskOptions);

          taskInfoList.push({
            background: false,
            taskType: DownloadKernel.TaskType.Group,
            categoryId: taskOptions.categoryId,
            panFileId: fileInfo.originFile.id,
            panSpace: fileInfo.originFile.space,
            panUserId: taskOptions.userId,
            taskBaseInfo: {
              savePath: taskOptions.sandboxSavePath,
              taskName: fileInfo.originFile.name!,
            },
            _subTaskInfos: subTaskInfos,
          } as IGroupTaskInfo)
        } else {
          taskInfoList.push({
            background: false,
            taskType: DownloadKernel.TaskType.P2sp,
            categoryId: taskOptions.categoryId,
            panFileId: fileInfo.originFile.id,
            panSpace: fileInfo.originFile.space,
            panUserId: taskOptions.userId,
            taskBaseInfo: {
              savePath: taskOptions.sandboxSavePath,
              taskName: fileInfo.originFile.name!,
              fileSize: Number(fileInfo.originFile.size ?? 0),
            },
          } as DownloadKernel.NewTaskInfo)
        }
      })
    }
    // 检查重复任务
    const repeatResponse = await this._shouldCreateTasksAfterCheckRepeatTasks(taskInfoList)

    if (repeatResponse.newTaskInfoList.length) {
      // 创建下载任务
      for (let taskInfo of repeatResponse.newTaskInfoList) {
        // 普通文件下载
        if (taskInfo.taskType === DownloadKernel.TaskType.P2sp) {
          const task = await TaskManager.GetInstance().createP2spTask(taskInfo, { url: '' })
          if (task) {
            taskIds.push(task.getId())
          }
        }
        // 文件夹下载
        if (taskInfo.taskType === DownloadKernel.TaskType.Group) {
          const groupTaskInfo = taskInfo as IGroupTaskInfo

          if (groupTaskInfo._subTaskInfos.length) {
            const task = await TaskManager.GetInstance().createGroupTask(groupTaskInfo, groupTaskInfo._subTaskInfos)
            if (task) {
              taskIds.push(task.getId())
            }
          }
        }
      }
      // 批量开始任务
      TaskManager.GetInstance().batchStartTasks(taskIds)
      // 重置
      this._resetPreprocess()
    }

    return {
      taskIds,
      errorList: this.preprocessErrorList,
      overlimitList: this.preprocessOverlimitList,
      needToDownloadFileCount: needToDownloadFileCount,
      repeatTaskAction: repeatResponse.action,
    }
  }

  packageSubTaskInfos (files: API_FILE.DriveFile[], parentFolderName: string) {
    const fileInfoList: IFolderSubFile[] = []
    const subFiles = files || []
    const filesMap = new Map()

    subFiles.forEach(file => {
      filesMap.set(file.id, file)
    })

    for (let file of subFiles) {
      if (file.kind === 'drive#file') {
        const pathParts: string[] = []
        // 向上追溯父级
        let currentId = file.parent_id
        while (currentId) {
          const parent = filesMap.get(currentId)
          if (!parent) break
          pathParts.unshift(parent.name!)
          currentId = parent.parent_id
        }
        pathParts.unshift(parentFolderName)

        fileInfoList.push(objectAssign(file, {
          _retrievalPath: pathParts.join('\\'),
        }));
      }
    }

    return fileInfoList
  }

  /**
   * 深度获取文件夹下面的子文件
   * @param folderId 文件夹 id
   * @param folderName 文件夹名称
   * @param folderSpace 文件夹的空间标识
   * @returns
   */
  async getAllSubFilesByFolderId (folderId: string, folderName: string, folderSpace: string = '') {
    const isSafe = folderSpace === 'SPACE_SAFE';
    let safeToken = '';
    if (isSafe) {
      safeToken = BaseManager.getInstance().getSafeBoxInfo().token;
    }

    let fileInfoList: IFolderSubFile[] = [];
    const subFilesRes = await ThunderPanClientSDK.getInstance().getAllFlattenSubFilesByParentId(folderId, folderSpace, '', {
      headers: {
        'space-authorization': safeToken
      }
    });

    if (subFilesRes.success && subFilesRes.data && subFilesRes.data.files) {
      fileInfoList = this.packageSubTaskInfos(subFilesRes.data.files, folderName)
    //   for (let file of subFiles) {
    //     // 普通文件则直接加入
    //     if (file.kind === 'drive#file') {
    //       fileInfoList.push(objectAssign(file, {
    //         _retrievalPath: folderName,
    //       }));
    //     }
    //     // 文件夹则获取相应的子文件
    //     if (isFolder(file)) {
    //       const nextSubFiles = await RetrievalTaskHelper.getAllSubFilesByFolderId(file.id!, `${folderName}/${file.name}`, file.space);
    //       fileInfoList.push(...nextSubFiles);
    //     }
    //   }
    }
    // 超过文件数量限制，目前限制 10000 条
    else if (subFilesRes.error && subFilesRes.error.error_description.includes('count error')) {
      return {
        success: false,
        errorCode: EBatchCreateTaskErrorCode.OVERLIMIT,
      }
    } else {
      return {
        success: false,
        errorCode: EBatchCreateTaskErrorCode.FETCH_ERROR,
      }
    }

    return {
      success: true,
      fileInfoList,
    }
  }

  /**
   * 组装文件夹任务下面子文件的任务信息
   * @param files 文件列表
   * @param options 配置选项
   * @returns
   */
  getGroupSubTaskInfos (files: IFolderSubFile[], options: IGetDownloadTaskInfosOptions) {
    return files.map(file => {
      return {
        baseInfo: {
          background: false,
          taskType: DownloadKernel.TaskType.P2sp,
          categoryId: options.categoryId,
          panFileId: file.id,
          panSpace: file.space,
          panUserId: options.userId,
          taskBaseInfo: {
            savePath: options.sandboxSavePath + '\\' + file._retrievalPath,
            taskName: file.name!,
            fileSize: Number(file.size ?? 0),
          },
        },
        taskType: DownloadKernel.TaskType.P2sp,
      } as DownloadKernel.NewGroupTaskInfo;
    })
  }

  /**
   * 按文件类型组装相应的任务信息
   * @param file 文件数据
   * @param options 配置选项
   * @returns
   */
  async getDownloadTaskInfoByFile (file: API_FILE.DriveFile, options: IGetDownloadTaskInfosOptions) {
    if (isFolder(file)) {
      const subFilesRes = await this.getAllSubFilesByFolderId(file.id!, file.name!, file.space);
      let subTaskInfos: DownloadKernel.NewGroupTaskInfo[] = []
      if (subFilesRes.success) {
        subTaskInfos = this.getGroupSubTaskInfos(subFilesRes.fileInfoList!, options);
      }

      return {
        background: false,
        taskType: DownloadKernel.TaskType.Group,
        categoryId: options.categoryId,
        panFileId: file.id,
        panSpace: file.space,
        panUserId: options.userId,
        taskBaseInfo: {
          savePath: options.sandboxSavePath,
          taskName: file.name!,
        },
        _subTaskInfos: subTaskInfos,
        _subTaskFetchErrorCode: subFilesRes.errorCode,
      } as IGroupTaskInfo;
    }
    if (file.kind === 'drive#file') {
      return {
        background: false,
        taskType: DownloadKernel.TaskType.P2sp,
        categoryId: options.categoryId,
        panFileId: file.id,
        panSpace: file.space,
        panUserId: options.userId,
        taskBaseInfo: {
          savePath: options.sandboxSavePath,
          taskName: file.name!,
          fileSize: Number(file.size ?? 0),
        },
      } as DownloadKernel.NewTaskInfo;
    }
    return null;
  }

  /**
   * 批量创建下载任务
   * @param files 文件列表
   * @returns
   */
  async batchCreateDownloadTaskWithFiles (files: API_FILE.DriveFile[], savePath?: string) {
    const { userStoreState } = useUserStore()
    const userId = userStoreState.curUser.userId
    // 创建成功的任务 id 列表
    const taskIds: number[] = []
    // 待创建任务列表
    const taskInfoList: DownloadKernel.NewTaskInfo[] = []
    // 获取服务接口文件数量超限的任务
    const overlimitList: API_FILE.DriveFile[] = []
    // 获取文件数的服务接口报错数量
    const errorList: API_FILE.DriveFile[] = []

    if (userId) {
      const localSavePath = savePath || await config.getValue('ThunderPanPlugin', 'defaultDownloadPath', 'C:\\迅雷云盘') as string
      TaskManager.GetInstance().GetCategoryManager().setCurrentPanUserId(userId)
      const currentPanCategory = await TaskManager.GetInstance().GetCategoryManager().getCurrentPanCategory()
      // 倒序创建任务，在任务列表才会是正常顺序
      const preFiles = files.reverse()
      for (const file of preFiles) {
        // 获取文件相应的任务创建信息
        const taskInfo = await this.getDownloadTaskInfoByFile(file, {
          userId: userId,
          categoryId: currentPanCategory?.getId()!,
          sandboxSavePath: localSavePath,
        })

        if (taskInfo) {
          taskInfoList.push(taskInfo)

          if (taskInfo.taskType === DownloadKernel.TaskType.Group) {
            const groupTaskInfo = taskInfo as IGroupTaskInfo
            if (groupTaskInfo._subTaskFetchErrorCode === EBatchCreateTaskErrorCode.OVERLIMIT) {
              overlimitList.push(file)
            } else if (groupTaskInfo._subTaskFetchErrorCode === EBatchCreateTaskErrorCode.FETCH_ERROR) {
              errorList.push(file)
            }
          }
        }
      }
    }
    // 检查重复任务
    const repeatResponse = await this._shouldCreateTasksAfterCheckRepeatTasks(taskInfoList)

    if (repeatResponse.newTaskInfoList.length) {
      for (const taskInfo of repeatResponse.newTaskInfoList) {
        // 普通文件下载
        if (taskInfo.taskType === DownloadKernel.TaskType.P2sp) {
          const task = await TaskManager.GetInstance().createP2spTask(taskInfo, { url: '' })
          if (task) {
            taskIds.push(task.getId())
          }
        }
        // 文件夹下载
        if (taskInfo.taskType === DownloadKernel.TaskType.Group) {
          const groupTaskInfo = taskInfo as IGroupTaskInfo

          if (groupTaskInfo._subTaskInfos.length) {
            const task = await TaskManager.GetInstance().createGroupTask(groupTaskInfo, groupTaskInfo._subTaskInfos)
            if (task) {
              taskIds.push(task.getId())
            }
          }
        }
      }
      // 批量开始任务
      TaskManager.GetInstance().batchStartTasks(taskIds)
    }

    return {
      taskIds,
      errorList,
      overlimitList,
      repeatTaskAction: repeatResponse.action,
    }
  }

  /**
   * 云盘下载任务链接更新处理
   */
  panTaskTimes: Map<number, number> = new Map();
  monitorTimerId: NodeJS.Timeout|null = null;

  private async _autoGetSafeBoxToken (): Promise<string> {
    // 获取保险箱密码
    const password = await UserConfigTableManager.getInstance().getHash()
    // 云盘保险箱 token 在有效期内，直接返回
    if (!BaseManager.getInstance().getSafeBoxTokenIsExpires()) {
      return BaseManager.getInstance().getSafeBoxInfo().token
    }

    const res = await ThunderPanClientSDK.getInstance().checkPassword({
      params: {
        password: hex_md5(password)
      },
    })

    if (res.success && res.data && res.data.token) {
      return res.data.token
    } else {
      // 登录保险箱报错、密码错误弹出保险箱输入弹窗
      const { userStoreState } = useUserStore()
      return new Promise(resolve => {
        const dialog = CreateSafeBoxDialog({
          showType: 'reset',
          phoneNumber: userStoreState.curUser.mobile!,
          onResolve: async () => {
            await sleep(20)
            const token = BaseManager.getInstance().getSafeBoxInfo().token
            resolve(token)
            dialog.close()
          },
          onClose: () => {
            resolve('')
          }
        })
      })
    }
  }

  public async init() {
    let handleUpdateUrl: any;
    const addMonitor = (taskId: number, expireTime: number) => {
      if (this.panTaskTimes.has(taskId)) {
        this.panTaskTimes.set(taskId, expireTime);
      }
      if (!this.monitorTimerId) {
        this.monitorTimerId = setInterval(() => {
          let now = (new Date()).getTime();
          this.panTaskTimes.forEach((expireTime: number, taskId: number) => {
            if ((expireTime - now <= 20 * 60 * 1000) && handleUpdateUrl) {
              handleUpdateUrl(taskId);
            }
          });
        }, 10 * 60 * 1000);
      }
    };
    // 云盘取回任务的回调，更新url
    handleUpdateUrl = async (taskId: number) => {
      const task = await TaskManager.GetInstance().findTaskById(taskId);
      if (task) {
        const panInfo = task.getPanFileIdInfo();

        let headers: any = {};
        if (panInfo && (panInfo.space === 'SPACE_SAFE')) {
          headers['space-authorization'] = await this._autoGetSafeBoxToken()
        }

        const info = await ThunderPanClientSDK.getInstance().getFileInfo(panInfo.fileId, { params: { space: panInfo.space }, headers });
        if (info.success) {
          if (task.getUrl().length === 0) {
            task.setTaskUrl(info.data!.web_content_link ?? '');
          } else {
            task.redirectOriginalResource(info.data!.web_content_link ?? '');
          }
          // 加速token
          if (info.data!.links) {
            let keys = Object.keys(info.data!.links);
            if (info.data!.links[keys[0]].token) {
              task.enableDcdnWithVipCert(info.data!.links[keys[0]].token!, -1);
            }
            if (info.data!.links[keys[0]].expire) {
              let time = new Date(info.data!.links[keys[0]].expire!).getTime();
              addMonitor(taskId, time);
            }
          }
        }
      }
    };
    TaskManager.GetInstance().setBeforePanTaskCreateDkTaskCallback(async (taskId: number, _panFileId: string, done: () => void) => {
      await handleUpdateUrl(taskId);
      done();
    });

    const onTaskStatusChange = (taskId: number, eNew: DownloadKernel.TaskStatus) => {
      if (this.panTaskTimes.has(taskId) && [DownloadKernel.TaskStatus.Stopped, DownloadKernel.TaskStatus.Failed, DownloadKernel.TaskStatus.Succeeded].includes(eNew)) {
        this.panTaskTimes.delete(taskId);
        if (this.panTaskTimes.size === 0 && this.monitorTimerId) {
          clearInterval(this.monitorTimerId);
          this.monitorTimerId = null;
        }
      }
    };
    TaskManager.GetInstance().attachTaskStatusChangeEvent((taskId: number, _eOld: DownloadKernel.TaskStatus,
      eNew: DownloadKernel.TaskStatus) => {
      onTaskStatusChange(taskId, eNew);
    });
    TaskManager.GetInstance().attachGroupSubTaskStatusChangeEvent((taskId: number, _groupTaskId: number, _eOld: DownloadKernel.TaskStatus,
      eNew: DownloadKernel.TaskStatus) => {
      onTaskStatusChange(taskId, eNew);
    });

    this._registerIPC()
  }

  private _registerIPC () {
    // 与主进程通一个 ipc context，直接注册
    client.registerFunctions({
      [IPC_API_NAME.BATCH_CREATE_DOWNLOAD_TASK]: (_ctx: unknown, files: API_FILE.DriveFile[], savePath: string = '') => {
        return this.batchCreateDownloadTaskWithFiles(files, savePath)
      },
      [IPC_API_NAME.START_DOWNLOAD_PREPROCESS]: (_ctx: unknown, files: API_FILE.DriveFile[]) => {
        return this.startPreprocess(files)
      },
      [IPC_API_NAME.START_CREATE_DOWNLOAD_TASK_BY_PREPROCESS]: (_ctx: unknown, savePath: string = '') => {
        return this.startDownloadByPreprocess(savePath)
      },
    })
  }
}
