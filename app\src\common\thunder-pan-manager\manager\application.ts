export class ApplicationManager {
  // windows pc 的配置
  static appId_win_prod = '0';
  static appKey_win_prod = '4af30ad6c6b0c02c4d8df3ded2cb79b8';
  static clientId_win_prod = 'XW-G4v1H72tgfJym';
  static clientSecret_win_prod = 'Qbaferw2knfQKqxa25EYJGtZ2_6755CMwzXBN3ctW54';
  static signKey_win_prod = 'n&Ng)QE3EZ';
  static deviceId_win_prod = ''
  static bundleName_wind = 'com.xunlei.thunderx'

  // mac 的配置
  static appId_mac_prod = '22074';
  static appKey_mac_prod = 'cefd3efff22e3635bdc6df161ac0aef6';
  static clientId_mac_prod = 'Y_Rjlpdf8tXHrP6u';
  static clientSecret_mac_prod = 'YVbfjbGwEe2GUrZ_zl0NbQ';
  static signKey_mac_prod = 'ff22e3635b';
  static deviceId_mac_prod = '0e1f11a13129794efc4074304aeca101'

  static getCurrentDeviceClientId() {
    return process.platform === 'darwin' ?  ApplicationManager.clientId_mac_prod : ApplicationManager.clientId_win_prod;
  }

  static getCurrentDeviceClientSecret() {
    return process.platform === 'darwin' ? ApplicationManager.clientSecret_mac_prod : ApplicationManager.clientSecret_win_prod;
  }

  static getCurrentDeviceAppId() {
    return process.platform === 'darwin' ? ApplicationManager.appId_mac_prod : ApplicationManager.appId_win_prod;
  }

  static getCurrentDeviceAppKey() {
    return process.platform === 'darwin' ? ApplicationManager.appKey_mac_prod : ApplicationManager.appKey_win_prod;
  }

  static getCurrentDeviceSignKey() {
    return process.platform === 'darwin' ? ApplicationManager.signKey_mac_prod : ApplicationManager.signKey_win_prod;
  }

  static getCurrentDeviceDeviceId() {
    return process.platform === 'darwin' ? ApplicationManager.deviceId_mac_prod : ApplicationManager.deviceId_win_prod;
  }

  static getCurrentDeviceBundleName() {
    return ApplicationManager.bundleName_wind;
  }

  static getCurrentDeviceClientVersion() {
    return '1.2.3.4';
  }
}

export function getXbaseConfig () {
  return {
    algVersion: '1',
    clientId: ApplicationManager.getCurrentDeviceClientId(),
    packageName: 'ThunderPanPlugin',
    signKey: ApplicationManager.getCurrentDeviceSignKey(),
  }
}
