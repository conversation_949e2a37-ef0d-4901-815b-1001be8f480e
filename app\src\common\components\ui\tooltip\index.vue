<script setup lang="ts">
import { computed, ref, type HTMLAttributes } from 'vue'
import { TooltipArrow, TooltipContent, TooltipPortal, TooltipProvider, TooltipRoot, TooltipTrigger } from 'reka-ui'
import './style.css'

interface Props {
  showArrow?: boolean
  contentClass?: HTMLAttributes['class']
  arrowClass?: HTMLAttributes['class']
  triggerClass?: HTMLAttributes['class']
  sideOffset?: number
  align?: 'start' | 'center' | 'end'
  side?: 'top' | 'right' | 'bottom' | 'left'
  delayDuration?: number
  text?: string
  asChild?: boolean
  maxWidth?: number
  triggerByPointer?: boolean
  disabled?: boolean
  to?: string | HTMLElement
  defaultOpen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showArrow: false,
  contentClass: '',
  arrowClass: '',
  triggerClass: '',
  sideOffset: 4,
  align: 'center',
  side: 'bottom',
  delayDuration: 0,
  text: '',
  asChild: true,
  triggerByPointer: false,
  disabled: false,
  to: undefined,
  defaultOpen: false
})

const open = ref(props.defaultOpen)
const anchor = ref({
  x: 0,
  y: 0,
})
let timer: NodeJS.Timeout | undefined

const position = computed(() => ({
  getBoundingClientRect: () =>
  ({
    width: 0,
    height: 0,
    left: anchor.value.x,
    right: anchor.value.x,
    top: anchor.value.y,
    bottom: anchor.value.y,
    ...anchor.value,
  } as DOMRect),
}))

const handlePointerenter = () => {
  if (props.disabled) {
    return
  }

  // 如果延迟时间大于0，则设置延迟时间 采用防抖函数
  if (props.delayDuration > 0) {
    timer = setTimeout(() => {
      open.value = true
      if (timer) {
        clearTimeout(timer) // 清除定时器
        timer = undefined
      }
    }, props.delayDuration)
  } else {
    open.value = true
  }
}

const handlePointerLeave = () => {
  open.value = false
  if (timer) {
    clearTimeout(timer) // 清除定时器
    timer = undefined
  }
}

</script>

<template>
  <TooltipProvider :delay-duration="delayDuration">
    <div @pointerenter="handlePointerenter" @pointerleave="handlePointerLeave" @pointermove="(ev) => {
      anchor.x = ev.clientX
      anchor.y = ev.clientY
    }" v-if="triggerByPointer" :class="[props.triggerClass]">
      <slot name="trigger" />
    </div>
    <TooltipRoot :open="open">
      <TooltipTrigger :reference="position" v-if="triggerByPointer" />
      <TooltipTrigger v-else :as-child="props.asChild" @pointerenter="handlePointerenter"
        @pointerleave="handlePointerLeave">
        <slot name="trigger" />
      </TooltipTrigger>
      <TooltipPortal v-if="$slots.content || text" :to="to">
        <TooltipContent :class="['TooltipContent', props.contentClass]" :side-offset="sideOffset" :align="align"
          :side="side" :style="{ maxWidth: maxWidth ? `${maxWidth}px` : 'auto', zIndex: 999 }"
          :hide-when-detached="true">
          <slot name="content" v-if="$slots.content">
          </slot>
          <span v-else-if="text">{{ text }}</span>
          <TooltipArrow v-if="showArrow" :class="['TooltipArrow', props.arrowClass]" :width="8" />
        </TooltipContent>
      </TooltipPortal>
    </TooltipRoot>
  </TooltipProvider>
</template>