/**
 * 给所有的独立弹框组件提供基类，提供自适应窗口高度等接口
 */

import { onMounted, onBeforeUnmount } from 'vue';
import { PopUpNS } from '@root/common/pop-up';
import * as PopUpTypes from '@root/common/pop-up/types';

interface PositionOverriteOptions {
  /** (**required** if y is used) Window's left offset from screen. */
  x?: number;
  /** (**required** if x is used) Window's top offset from screen */
  y?: number;
  /** 窗口宽度，选填，0 表示动态获取弹框宽度 */
  windowWidth?: number; 
  /** 窗口高度，选填，0 表示动态获取窗口高度 */
  windowHeight?: number;
  /** 动态获取窗口宽高的样式选择器 */
  selector?: string;
  /** 窗口的显示位置方式：屏幕居中、窗口居中、右下角、自定义等 */
  relatePos?: PopUpTypes.RelatePosType;
  /** 窗口创建时是否自适应大小 */
  autoSize?: boolean;
  /** 窗口创建时是否展示 */
  show?: boolean;
}

export function usePositionMixinComponent() {

  /** 非响应式数据 ( 用于重置窗口大小的属性 ) */ 
  const positionOptions: PositionOverriteOptions = {
    windowWidth: 0,
    windowHeight: 0,
    selector: '.td-dialog',
    relatePos: PopUpTypes.RelatePosType.Custom,
    autoSize: false,
    show: false,
  };

  /** 基类方法：基于窗口当前左上自适应拉伸 */
  const resizeToFitContent: (width?: number, height?: number) => Promise<void> = async (width?: number, height?: number) => {
    width = (width ? width : positionOptions?.windowWidth) ?? 0;
    height = (height ? height : positionOptions?.windowHeight) ?? 0;
    do {
      if (width > 0 && height > 0) {
        window.resizeTo(Math.round(width), Math.round(height));
        break;
      }

      if (!positionOptions?.selector) {
        break;
      }
      
      const $dialog: HTMLElement | null = document.querySelector(positionOptions.selector)
      if (!$dialog) {
        console.log('基类选择器错误', positionOptions.selector);
        break;
      }
      // 优先使用外部传递的宽高
      width = width ? width : Math.round($dialog.offsetWidth);
      height = height ? height : Math.round($dialog.offsetHeight);
      console.log('调整窗口宽高', width, height);
      window.resizeTo(width, height);
      // await PopUpNS.resizeWindow({ width, height });
    } while (0);
  }

  /** 基类方法：基于父窗口居中自适应 */
  const fitWindowPosInParent = () => {

  }

  /** 根据预设的窗口位置方式自适应大小，一般在窗口展示之初调用 */
  const adaptiveSize = async () => {
    let width: number = 0;
    let height: number = 0;
    do {
      // 如果禁用了自动大小调整，直接退出
      if (!positionOptions.autoSize) {
        console.log('基类 自适应大小已禁用，跳过窗口调整');
        break;
      }

      if (positionOptions.windowWidth && positionOptions.windowHeight) {
        // 已经预设好窗口大小的创建之初已经调整过位置；此处无需处理
        width = Math.round(positionOptions.windowWidth);
        height = Math.round(positionOptions.windowHeight);
        break;
      }

      if (!positionOptions?.selector) {
        break;
      }

      // if (positionOptions.relatePos === PopUpTypes.RelatePosType.Custom) {
      //   console.log('自定义位置无需', positionOptions.selector);
      //   break;
      // }
      if (positionOptions.relatePos === undefined || positionOptions.relatePos === null) {
        break;
      }

      const $dialog: HTMLElement | null = document.querySelector(positionOptions.selector)
      if (!$dialog) {
        console.log('基类选择器错误', positionOptions.selector);
        break;
      }
      width = Math.round(positionOptions.windowWidth ? positionOptions.windowWidth : $dialog.offsetWidth);
      height = Math.round(positionOptions.windowHeight ? positionOptions.windowHeight : $dialog.offsetHeight);

      if (!width || !height) {
        console.log('获取选择器宽高异常', width, height);
        break;
      }

      console.log('自适应宽高为', width, height);
    } while (0);

    if (width && height) {
      switch (positionOptions.relatePos) {
        case PopUpTypes.RelatePosType.CenterParent:
          {
            await PopUpNS.fitWindowPosInParent(width, height);
          }
          break;
        case PopUpTypes.RelatePosType.RightBottom:
          {
            await PopUpNS.fitWindowPosRightBottom(width, height);
          }
          break;
        case PopUpTypes.RelatePosType.Custom:
          {
            await PopUpNS.resizeWindow({ width, height });
          }
          break;
        case PopUpTypes.RelatePosType.CenterScreen:
          {
            const currentWindow = PopUpNS.getCurrentWindow();
            await currentWindow.center();
          }
          break;
        default:
          break;
      }

      if (positionOptions.show) {
        const currentWindow = PopUpNS.getCurrentWindow();
        await currentWindow.show();
      }
    }
  }

  // 生命周期钩子
  onMounted(() => {
    console.log('基类挂载完成', positionOptions);;
    adaptiveSize();
  });

  onBeforeUnmount(() => {
    console.log('基类卸载前');
  });

  /** 提供重写非响应式数据的方法,修改位置相关的宽、高、样式选择器等 */
  const overridePosition = (newOptions: PositionOverriteOptions) => {
    Object.assign(positionOptions, newOptions);
  };

  return {
    overridePosition,
    resizeToFitContent,
    fitWindowPosInParent,
  };
}