# function: generate_module_test
# description: 用于自动构建interface的单元测试，目前只支持已I、i开头的接口头文件 
function(generate_module_test interface_dir test_root)
	file(GLOB_RECURSE file_list LIST_DIRECTORIES false RELATIVE ${interface_dir} 
		${interface_dir}/I*.h
		${interface_dir}/i*.h
		${interface_dir}/I*.hpp
		${interface_dir}/i*.hpp
		)
	
	set (main_file ${test_root}/main.cpp)
	if (EXISTS ${main_file})
		# nothing
	else ()
		generate_test_main(${main_file})
	endif ()
	
	foreach (rel_file ${file_list})
		set(abs_path ${interface_dir}/${rel_file})
		
		# 获取接口文件名(不含扩展名) 
		get_filename_component(file_name_we ${rel_file} NAME_WE)
				
		# file directory with '/' for suffix
		get_filename_component(rel_file_dir ${rel_file} DIRECTORY)
		# check ${rel_file_dir} suffix is '/'
		
		# 检测是否'I' or 'i'开头，有则删除 
		set (test_name ${file_name_we}_test.cpp)
		string(REGEX REPLACE "(^I|i)(.+)" "\\2" reg_test_name ${test_name})
		set(abs_test_path ${test_root}/${rel_file_dir}${test_name})
		set(abs_reg_test_path ${test_root}/${rel_file_dir}${reg_test_name})
		
		# 生成test文件并附加相应gtest环境  
		if (EXISTS ${abs_test_path} OR EXISTS ${abs_reg_test_path})
			# nothing
		else ()
			append_test_include("gtest/gtest.h" ${abs_reg_test_path})
			append_contens("\n" ${abs_reg_test_path})
			
			# 添加用户头文件 
			append_test_include(${rel_file} ${abs_reg_test_path})
			
			append_contens("\n" ${abs_reg_test_path})
			
			append_contens("// TODO, implement. \n" ${abs_reg_test_path})
			
			# app namespace THUNDER_NAMESPACE
			# append_contens("using namespace THUNDER_NAMESPACE;" ${abs_reg_test_path})
			# append_contens("\n\n" ${abs_reg_test_path})
			
			# 添加待测试接口(目前已TEST方式)
			fetch_test_interface(${abs_path} module_interface_list)
			foreach (module_interface_test ${module_interface_list})
				append_test(${module_interface_test} ${file_name_we} ${abs_reg_test_path})
				append_contens("\n" ${abs_reg_test_path})
			endforeach()
		endif ()
	endforeach ()
endfunction()

# 枚举接口文件的接口名 
MACRO (fetch_test_interface inf_file interface_list)
	set(${interface_list} "")
	# list (APPEND ${interface_list} "Release")
ENDMACRO ()

# 添加测试的头文件包含 
function (append_test_include header test_file)
	file(APPEND ${test_file} "#include \"${header}\"\n")
endfunction ()

function (append_contens content test_file)
	file(APPEND ${test_file} "${content}")
endfunction ()

# 添加gtest的单个TEST 
function (append_test gtest_name gtest_case test_file)
	file(APPEND ${test_file} "TEST(${gtest_case}, ${gtest_name})\n{\n\n}\n")
endfunction ()

# 创建main文件 
function (generate_test_main main_file)
	file(WRITE  ${main_file} "#include \"gtest/gtest.h\"\n")
    file(APPEND ${main_file} "#include \"../Common/Dumper.h\"\n\n")
	file(APPEND ${main_file} "int main(int argc, char **argv) {\n")
    file(APPEND ${main_file} "  INIT_DUMP();\n")
	file(APPEND ${main_file} "  testing::InitGoogleTest(&argc, argv);\n")
	file(APPEND ${main_file} "  return RUN_ALL_TESTS();\n")
	file(APPEND ${main_file} "}\n")
endfunction ()

MACRO (AppendCommonClass name)
    list(APPEND prj_test_common_header_src ${CMAKE_SOURCE_DIR}/tests/modules/CommonSource/${name}.h)   

    if(("${name}" STREQUAL "testPlayer"))
        If (APPLE)
            list(APPEND prj_test_common_header_src ${CMAKE_SOURCE_DIR}/tests/modules/CommonSource/${name}.mm)
        else()
            list(APPEND prj_test_common_header_src ${CMAKE_SOURCE_DIR}/tests/modules/CommonSource/${name}.cpp)
        endif ()
    else()
        list(APPEND prj_test_common_header_src ${CMAKE_SOURCE_DIR}/tests/modules/CommonSource/${name}.cpp)
    endif()
ENDMACRO ()


MACRO (AppendCommonSource name)
set(class_MAP
    "DataModel" "DataModelHelper"
    "AIManager" "DataModelHelper"
    "BsPluginManager" "DataModelHelper"
	"FrameData" "DataModelHelper"
    "VisualEditor" "WsidHelper#DataModelHelper"
    "TimelineEditor" "DataModelHelper#EditorlHelper"
    "TimelineUX" "DataModelHelper#EditorlHelper"
    "PlayManager" "testPlayer"
    "VisualEditor" "testPlayer"
)

list(LENGTH class_MAP map_length)
math(EXPR map_length "${map_length} - 1")

# 遍历键-值对.
foreach(index RANGE 0 ${map_length} 2) # 以步长为2遍历列表，考虑到有键和值
    list(GET class_MAP ${index} key)
    math(EXPR value_index "${index} + 1")
    list(GET class_MAP ${value_index} value)    
    if(("${name}" STREQUAL "${key}"))
            set(my_string "${value}")
            string(REPLACE "#" ";" lib_list "${my_string}")            
            foreach(item ${lib_list})
                AppendCommonClass(${item})
            endforeach()
    endif()    
endforeach()

if(("${name}" STREQUAL "PlayManager") OR ("${name}" STREQUAL "VisualEditor") )
    If (APPLE)
        list (APPEND COMMONSOURCE_LIBS "-framework Cocoa")
    endif ()
endif()

list (APPEND COMMONSOURCE_INCLUDE ${CMAKE_SOURCE_DIR}/tests/modules/CommonSource/)

if(NOT  prj_test_common_header_src STREQUAL "")
    message("prj_test_common_header_src: ${prj_test_common_header_src}")
endif()

ENDMACRO ()
























































