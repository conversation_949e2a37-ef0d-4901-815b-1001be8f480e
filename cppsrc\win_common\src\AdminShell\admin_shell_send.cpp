#include "admin_shell_send.h"

#include <ShlObj.h>
#include <shellapi.h>

AdminShellSend::AdminShellSend()
	: has_admin_privilege_(false)
{
	if (::IsUserAnAdmin())
	{
		has_admin_privilege_ = true;
	}
}

AdminShellSend::~AdminShellSend()
{
}

AdminShellSend *AdminShellSend::GetInstance()
{
	static AdminShellSend instance;
	return &instance;
}

bool AdminShellSend::HasAdminPrivilege()
{
	return has_admin_privilege_;
}

bool AdminShellSend::RunAdminShell()
{
	bool ret = true;
	do 
	{
		if (HasAdminPrivilege())
		{
			break;
		}

		if (IsAdminShellRunning())
		{
			break;
		}
		
		char shell_file[MAX_PATH] = { 0 };
		::GetModuleFileName(NULL, shell_file, MAX_PATH);

		char boot_param[4096] = { 0 };
		HANDLE sync_event = ::CreateEvent(NULL, FALSE, FALSE, NULL);
		sprintf(boot_param, "/bootshell\\%d\\%d", (DWORD)(ULONG_PTR)sync_event, ::GetCurrentProcessId());

		SHELLEXECUTEINFO se;
		memset(&se, 0, sizeof(SHELLEXECUTEINFO));
		se.cbSize = sizeof(SHELLEXECUTEINFO);
		se.lpVerb = "runas";
		se.lpFile = shell_file;
		se.lpParameters = boot_param;
		se.nShow = SW_HIDE;
		se.fMask = SEE_MASK_NOCLOSEPROCESS;

		if (::ShellExecuteEx(&se))
		{
			shell_process_ = se.hProcess;
			HANDLE event[] = { shell_process_, sync_event };
			if (WAIT_OBJECT_0 == ::WaitForMultipleObjects(2, event, FALSE, INFINITE))
			{
				ret = false;
			}
		}
		else
		{
			ret = false;
		}
	} while (0);

	return ret;
}

long AdminShellSend::Send(const std::string &data)
{
	long ret = -1;
	do 
	{
		if (!RunAdminShell())
		{
			ret = -2;
			break;
		}

		HWND h_wnd = ::FindWindow(XMP_ADMIN_SHELL_MESSAGE_CLASS_NAME, XMP_ADMIN_SHELL_MESSAGE_WINDOW_NAME);
		if (h_wnd)
		{
			COPYDATASTRUCT cs;
			cs.dwData = 0;
			cs.lpData = (PVOID)data.c_str();
			cs.cbData = (DWORD)(data.length() + 1) * 2;
			ret = ::SendMessage(h_wnd, WM_COPYDATA, NULL, (LPARAM)&cs);
		}
	} while (0);

	return ret;
}

bool AdminShellSend::IsAdminShellRunning()
{
	bool ret = false;
	do 
	{
		if (!shell_process_)
		{
			break;
		}

		HWND h_wnd = ::FindWindow(XMP_ADMIN_SHELL_MESSAGE_CLASS_NAME, XMP_ADMIN_SHELL_MESSAGE_WINDOW_NAME);
		if (!h_wnd)
		{
			shell_process_ = NULL;
			break;
		}

		ret = true;
	} while (0);

	return ret;
}
