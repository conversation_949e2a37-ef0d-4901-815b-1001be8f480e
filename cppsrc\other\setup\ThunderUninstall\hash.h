#ifndef ASSOCIATEHELPER_HASH_H
#define ASSOCIATEHELPER_HASH_H

#include <windows.h>
#include <string>

class Hash
{
public:
	Hash();
	~Hash();

	static Hash* GetInstance()
	{
		static Hash s_instance;
		return &s_instance;
	}

	std::wstring GetExtHash(const std::wstring &ext, const std::wstring &progid);


private:
	bool GetFileVersion(const std::wstring &file_path, unsigned int &ver3, unsigned int &ver4);
	bool CheckShell32();
	bool ArithmeticFunc(unsigned int *md5_digest, unsigned int *lpData, unsigned int size, int *ret1, unsigned int *ret2);
	bool ArithmeticFuncEx(unsigned int *md5_digest, unsigned int *lpData, unsigned int size, unsigned int *ret1, unsigned int *ret2);
	bool GetDigest(unsigned int lpData, int dwSize, unsigned int *md5_digest, unsigned int *digest);

	std::wstring GetSid();

private:
	bool check_shell_;
	std::wstring sid_;
};


#define theHash Hash::GetInstance()

#endif
