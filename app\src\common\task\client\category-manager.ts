import { CallApiProxyImplWithIpcClient } from '../call-api-impl';
export class CategoryManager {
    apiProxy: CallApiProxyImplWithIpcClient | null = null;
    public async init(apiProxy: CallApiProxyImplWithIpcClient) {
        this.apiProxy = apiProxy;
    }

    public async setCurrentPanUserId(id: string): Promise<void> {
        await this.apiProxy.CallApi('CategoryManagerSetCurrentPanUserId', id);
    }

    public async getCurrentPanCategoryId(): Promise<number> {
        let info = await this.apiProxy.CallApi('CategoryManagerGetCurrentPanCategoryId');
        if (info.bSucc) {
            return info.result as number;
        }

        return -1;
    }
}