project (xl_lib)
THUNDER_LOG_INFO("${PROJECT_NAME} is Generating...")
file(GLOB_RECURSE prj_headerFiles "${CMAKE_CURRENT_SOURCE_DIR}/*.h")
file(GLOB_RECURSE prj_srcFiles "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")
file(GLOB_RECURSE prj_cFiles "${CMAKE_CURRENT_SOURCE_DIR}/*.c")


add_library(${PROJECT_NAME} STATIC
	${prj_headerFiles} 
	${prj_srcFiles}
#	${prj_cFiles}
)
# 确保 .c 文件使用 C 编译器
set_source_files_properties(${prj_cFiles} PROPERTIES LANGUAGE C)

target_include_directories (${PROJECT_NAME} PRIVATE 
	${CMAKE_CURRENT_SOURCE_DIR}
	${THUNDER_3RDPARTY_PATH}/zlib/include
	${THUNDER_3RDPARTY_PATH}/openssl/include
	#${THUNDER_COMMON_PATH}/include
)

if (MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /MT)
endif()

# 设置目标属性，将目标分组到 "Common" 文件夹中
set_target_properties(${PROJECT_NAME} PROPERTIES FOLDER "Common")