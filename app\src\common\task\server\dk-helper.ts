import { server } from '@xunlei/node-net-ipc/dist/ipc-server';
import * as BaseType from '../base'
import {DkHelper} from '../impl/dk-helper'
export class TaskManagerDkHelperServer {
    static init() {
            server.registerFunctions({
                TaskManagerDkHelperIsThunderPrivateUrl: async (c: any, context: any, url: string) => {
                    return DkHelper.isThunderPrivateUrl(url);
                },
                TaskManagerDkHelperParseThunderPrivateUrl: async (c: any, context: any, url: string) => {
                    return DkHelper.parseThunderPrivateUrl(url);
                },
                TaskManagerDkHelperParserEd2kLink: async (c: any, context: any, url: string) => {
                    return DkHelper.parserEd2kLink(url);
                },
                TaskManagerDkHelperParseMagnetUrl: async (c: any, context: any, url: string) => {
                    return DkHelper.parseMagnetUrl(url);
                },
                TaskManagerDkHelperParseP2spUrl: async (c: any, context: any, url: string) => {
                    return DkHelper.parseP2spUrl(url);
                },
                TaskManagerDkHelperParseFileNameFromP2spUrlPath: async (c: any, context: any, url: string) => {
                    return DkHelper.parseFileNameFromP2spUrlPath(url);
                },
                TaskManagerDkHelperGetTaskTypeFromUrl: async (c: any, context: any, url: string) => {
                    return DkHelper.getTaskTypeFromUrl(url);
                },
                TaskManagerDkHelperParseBtTaskInfo: async (c: any, context: any, url: string) => {
                    return DkHelper.parseBtTaskInfo(url);
                },
                TaskManagerDkHelperProxyVerify: async (c: any, context: any, host: string,
                    port: number,
                    userName: string,
                    passWord: string,
                    proxyType: BaseType.ProxyType) => {
                    return DkHelper.proxyVerify(host, port, userName, passWord, proxyType);
                },
            });
        }
}