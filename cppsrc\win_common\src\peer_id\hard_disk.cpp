#include <windows.h>
#include <winioctl.h>
#include "hard_disk.h"
#include <algorithm>

//  Required to ensure correct PhysicalDrive IOCTL structure setup

#define  PhysicalDrive0   "\\\\.\\PhysicalDrive0"
#define  Scsi0            "\\\\.\\Scsi0:"

#define  IDENTIFY_BUFFER_SIZE  512

//  IOCTL commands
#define  DFP_GET_VERSION          0x00074080
#define  DFP_SEND_DRIVE_COMMAND   0x0007c084
#define  DFP_RECEIVE_DRIVE_DATA   0x0007c088

#define  FILE_DEVICE_SCSI              0x0000001b
#define  IOCTL_SCSI_MINIPORT_IDENTIFY  ((FILE_DEVICE_SCSI << 16) + 0x0501)
#define  IOCTL_SCSI_MINIPORT 0x0004D008  //  see NTDDSCSI.H for definition

#define SMART_GET_VERSION               CTL_CODE(IOCTL_DISK_BASE, 0x0020, METHOD_BUFFERED, FILE_READ_ACCESS)
#define SMART_SEND_DRIVE_COMMAND        CTL_CODE(IOCTL_DISK_BASE, 0x0021, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define SMART_RCV_DRIVE_DATA            CTL_CODE(IOCTL_DISK_BASE, 0x0022, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define  SENDIDLENGTH  sizeof (SENDCMDOUTPARAMS) + IDENTIFY_BUFFER_SIZE

//  GETVERSIONOUTPARAMS contains the data returned from the 
//  Get Driver Version function.
typedef struct _GETVERSIONOUTPARAMS
{
	BYTE bVersion;      // Binary driver version.
	BYTE bRevision;     // Binary driver revision.
	BYTE bReserved;     // Not used.
	BYTE bIDEDeviceMap; // Bit map of IDE devices.
	DWORD fCapabilities; // Bit mask of driver capabilities.
	DWORD dwReserved[4]; // For future use.
} GETVERSIONOUTPARAMS, *PGETVERSIONOUTPARAMS, *LPGETVERSIONOUTPARAMS;

//  Valid values for the bCommandReg member of IDEREGS.
#define  IDE_ATAPI_IDENTIFY  0xA1  //  Returns ID sector for ATAPI.
#define  IDE_ATA_IDENTIFY    0xEC  //  Returns ID sector for ATA.


// The following struct defines the interesting part of the IDENTIFY
// buffer:
typedef struct _IDSECTOR
{
	USHORT  wGenConfig;
	USHORT  wNumCyls;
	USHORT  wReserved;
	USHORT  wNumHeads;
	USHORT  wBytesPerTrack;
	USHORT  wBytesPerSector;
	USHORT  wSectorsPerTrack;
	USHORT  wVendorUnique[3];
	CHAR    sSerialNumber[20];
	USHORT  wBufferType;
	USHORT  wBufferSize;
	USHORT  wECCSize;
	CHAR    sFirmwareRev[8];
	CHAR    sModelNumber[40];
	USHORT  wMoreVendorUnique;
	USHORT  wDoubleWordIO;
	USHORT  wCapabilities;
	USHORT  wReserved1;
	USHORT  wPIOTiming;
	USHORT  wDMATiming;
	USHORT  wBS;
	USHORT  wNumCurrentCyls;
	USHORT  wNumCurrentHeads;
	USHORT  wNumCurrentSectorsPerTrack;
	ULONG   ulCurrentSectorCapacity;
	USHORT  wMultSectorStuff;
	ULONG   ulTotalAddressableSectors;
	USHORT  wSingleWordDMA;
	USHORT  wMultiWordDMA;
	BYTE    bReserved[128];
} IDSECTOR, *PIDSECTOR;

typedef struct _SRB_IO_CONTROL
{
	ULONG HeaderLength;
	UCHAR Signature[8];
	ULONG Timeout;
	ULONG ControlCode;
	ULONG ReturnCode;
	ULONG Length;
} SRB_IO_CONTROL, *PSRB_IO_CONTROL;

#pragma pack(1)
typedef struct _IDENTIFY_DATA {
	USHORT GeneralConfiguration;            // 00 00
	USHORT NumberOfCylinders;               // 02  1
	USHORT Reserved1;                       // 04  2
	USHORT NumberOfHeads;                   // 06  3
	USHORT UnformattedBytesPerTrack;        // 08  4
	USHORT UnformattedBytesPerSector;       // 0A  5
	USHORT SectorsPerTrack;                 // 0C  6
	USHORT VendorUnique1[3];                // 0E  7-9
	USHORT SerialNumber[10];                // 14  10-19
	USHORT BufferType;                      // 28  20
	USHORT BufferSectorSize;                // 2A  21
	USHORT NumberOfEccBytes;                // 2C  22
	USHORT FirmwareRevision[4];             // 2E  23-26
	USHORT ModelNumber[20];                 // 36  27-46
	UCHAR  MaximumBlockTransfer;            // 5E  47
	UCHAR  VendorUnique2;                   // 5F
	USHORT DoubleWordIo;                    // 60  48
	USHORT Capabilities;                    // 62  49
	USHORT Reserved2;                       // 64  50
	UCHAR  VendorUnique3;                   // 66  51
	UCHAR  PioCycleTimingMode;              // 67
	UCHAR  VendorUnique4;                   // 68  52
	UCHAR  DmaCycleTimingMode;              // 69
	USHORT TranslationFieldsValid:1;        // 6A  53
	USHORT Reserved3:15;
	USHORT NumberOfCurrentCylinders;        // 6C  54
	USHORT NumberOfCurrentHeads;            // 6E  55
	USHORT CurrentSectorsPerTrack;          // 70  56
	ULONG  CurrentSectorCapacity;           // 72  57-58
	USHORT CurrentMultiSectorSetting;       //     59
	ULONG  UserAddressableSectors;          //     60-61
	USHORT SingleWordDMASupport : 8;        //     62
	USHORT SingleWordDMAActive : 8;
	USHORT MultiWordDMASupport : 8;         //     63
	USHORT MultiWordDMAActive : 8;
	USHORT AdvancedPIOModes : 8;            //     64
	USHORT Reserved4 : 8;
	USHORT MinimumMWXferCycleTime;          //     65
	USHORT RecommendedMWXferCycleTime;      //     66
	USHORT MinimumPIOCycleTime;             //     67
	USHORT MinimumPIOCycleTimeIORDY;        //     68
	USHORT Reserved5[2];                    //     69-70
	USHORT ReleaseTimeOverlapped;           //     71
	USHORT ReleaseTimeServiceCommand;       //     72
	USHORT MajorRevision;                   //     73
	USHORT MinorRevision;                   //     74
	USHORT Reserved6[50];                   //     75-126
	USHORT SpecialFunctionsEnabled;         //     127
	USHORT Reserved7[128];                  //     128-255
} IDENTIFY_DATA, *PIDENTIFY_DATA;
#pragma pack()

//  ---------------------------------------------------

// (* Output Bbuffer for the VxD (rt_IdeDinfo record) *)
typedef struct _rt_IdeDInfo_
{
	BYTE IDEExists[4];
	BYTE DiskExists[8];
	WORD DisksRawInfo[8*256];
} rt_IdeDInfo, *pt_IdeDInfo;

#define  m_cVxDFunctionIdesDInfo  1

bool hard_disk::get_serial_number( std::string& serial_number )
{
	bool done = false;

	OSVERSIONINFO version;
	memset( &version, 0, sizeof(version));
	version.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
	GetVersionEx( &version );
	
	if (version.dwPlatformId == VER_PLATFORM_WIN32_NT)
	{
		done = read_physical_drive_in_nt_with_admin_rights( serial_number );

		if (!done)
		{
			done = read_ide_drive_as_scsi_drive_in_nt( serial_number );
		}

		if (!done)
		{
			done = read_physical_drive_in_nt_with_zero_rights( serial_number );
		}

		if (!done)
		{
			done = read_physical_drive_in_nt_using_smart( serial_number );
		}
	}
	else
	{
		done = read_drive_ports_in_win_9x( serial_number );
	}

	return done;
}

bool hard_disk::get_volume_serial(std::string& result)
{
	result.clear();
	char volume_buffer[100] = "";
	DWORD volume_serial;

	BOOL ret = GetVolumeInformationA("c:\\", volume_buffer, 100, &volume_serial, NULL, NULL, NULL, 0);
	if (ret == FALSE)
	{
		return false;
	}
	char tmp[18];
	sprintf(tmp, "%02X%02X%02X%02X%02X%02X", volume_serial << 24 >> 24, volume_serial << 16 >> 24, volume_serial << 8 >> 24, volume_serial >> 24, 0, 0);
	result.assign(tmp);
	return true;
}

// DoIDENTIFY
// FUNCTION: Send an IDENTIFY command to the drive
// bDriveNum = 0-3
// bIDCmd = IDE_ATA_IDENTIFY or IDE_ATAPI_IDENTIFY
BOOL DoIDENTIFY (HANDLE hPhysicalDriveIOCTL, PSENDCMDINPARAMS pSCIP,
				 PSENDCMDOUTPARAMS pSCOP, BYTE bIDCmd, BYTE bDriveNum,
				 PDWORD lpcbBytesReturned)
{
	// Set up data structures for IDENTIFY command.
	pSCIP->cBufferSize = IDENTIFY_BUFFER_SIZE;
	pSCIP->irDriveRegs.bFeaturesReg = 0;
	pSCIP->irDriveRegs.bSectorCountReg = 1;
	//pSCIP->irDriveRegs.bSectorNumberReg = 1;
	pSCIP->irDriveRegs.bCylLowReg = 0;
	pSCIP->irDriveRegs.bCylHighReg = 0;

	// Compute the drive number.
	pSCIP->irDriveRegs.bDriveHeadReg = 0xA0 | ((bDriveNum & 1) << 4);

	// The command can either be IDE identify or ATAPI identify.
	pSCIP->irDriveRegs.bCommandReg = bIDCmd;
	pSCIP->bDriveNumber = bDriveNum;
	pSCIP->cBufferSize = IDENTIFY_BUFFER_SIZE;

	return ( DeviceIoControl (hPhysicalDriveIOCTL, DFP_RECEIVE_DRIVE_DATA,
		(LPVOID) pSCIP,
		sizeof(SENDCMDINPARAMS) - 1,
		(LPVOID) pSCOP,
		sizeof(SENDCMDOUTPARAMS) + IDENTIFY_BUFFER_SIZE - 1,
		lpcbBytesReturned, NULL) );
}

bool hard_disk::read_physical_drive_in_nt_with_admin_rights( std::string& serial_number )
{
	bool done = false;

	//  Windows NT, Windows 2000, must have admin rights
	HANDLE hPhysicalDriveIOCTL = CreateFile( PhysicalDrive0, GENERIC_READ|GENERIC_WRITE, FILE_SHARE_READ|FILE_SHARE_WRITE, NULL, OPEN_EXISTING, 0, NULL );

	if (hPhysicalDriveIOCTL != INVALID_HANDLE_VALUE)
	{
		GETVERSIONOUTPARAMS VersionParams;
		DWORD               cbBytesReturned = 0;

		memset( &VersionParams, 0, sizeof(VersionParams) );
		if (!DeviceIoControl( hPhysicalDriveIOCTL, DFP_GET_VERSION, NULL, 0, &VersionParams, sizeof(VersionParams), &cbBytesReturned, NULL ))
		{
		}

		// If there is a IDE device at number "i" issue commands to the device
		if (VersionParams.bIDEDeviceMap > 0)
		{
			BYTE             bIDCmd = 0;   // IDE or ATAPI IDENTIFY cmd
			SENDCMDINPARAMS  scip;

			// Now, get the ID sector for all IDE devices in the system.
			// If the device is ATAPI use the IDE_ATAPI_IDENTIFY command, otherwise use the IDE_ATA_IDENTIFY command
			bIDCmd = (VersionParams.bIDEDeviceMap >> 0 & 0x10) ? IDE_ATAPI_IDENTIFY : IDE_ATA_IDENTIFY;

			BYTE IdOutCmd[sizeof(SENDCMDOUTPARAMS)+IDENTIFY_BUFFER_SIZE-1];

			memset( &scip, 0, sizeof(scip) );
			memset( IdOutCmd, 0, sizeof(IdOutCmd) );

			if (DoIDENTIFY( hPhysicalDriveIOCTL, &scip, (PSENDCMDOUTPARAMS)&IdOutCmd, (BYTE)bIDCmd, (BYTE)0, &cbBytesReturned ))
			{
				USHORT *pIdSector = (USHORT *)((PSENDCMDOUTPARAMS)IdOutCmd)->bBuffer;

				int position = 0;
				char tmp[256];
				for (int i = 10; i < 20; i++)
				{
					tmp[position++] = (char)(pIdSector[i]/256);
					tmp[position++] = (char)(pIdSector[i]%256);
				}
				tmp[position] = '\0';

				serial_number = std::string(tmp, strlen(tmp));
				serial_number.erase( std::remove( serial_number.begin(), serial_number.end(), ' '), serial_number.end() );

				if (!serial_number.empty())
				{
					done = true;
				}
			}
		}
		else
		{
		}

		CloseHandle( hPhysicalDriveIOCTL );
	}
	else
	{
	}

	return done;
}

bool hard_disk::read_ide_drive_as_scsi_drive_in_nt( std::string& serial_number )
{
	bool done = false;

	//  Windows NT, Windows 2000, any rights should do
	HANDLE hScsiDriveIOCTL = CreateFile( Scsi0, GENERIC_READ|GENERIC_WRITE, FILE_SHARE_READ|FILE_SHARE_WRITE, NULL, OPEN_EXISTING, 0, NULL );

	if (hScsiDriveIOCTL != INVALID_HANDLE_VALUE)
	{
		int drive = 0;
		char buffer[sizeof(SRB_IO_CONTROL)+SENDIDLENGTH];
		SRB_IO_CONTROL *p = (SRB_IO_CONTROL*) buffer;
		SENDCMDINPARAMS *pin = (SENDCMDINPARAMS*)(buffer + sizeof (SRB_IO_CONTROL));
		DWORD dummy;

		memset (buffer, 0, sizeof (buffer));
		p->HeaderLength = sizeof (SRB_IO_CONTROL);
		p->Timeout = 10000;
		p->Length = SENDIDLENGTH;
		p->ControlCode = IOCTL_SCSI_MINIPORT_IDENTIFY;
		strncpy( (char*)p->Signature, "SCSIDISK", 8 );

		pin->irDriveRegs.bCommandReg = IDE_ATA_IDENTIFY;
		pin->bDriveNumber = drive;

		if (DeviceIoControl( hScsiDriveIOCTL, IOCTL_SCSI_MINIPORT, buffer, sizeof(SRB_IO_CONTROL)+sizeof(SENDCMDINPARAMS)-1, buffer, sizeof(SRB_IO_CONTROL) + SENDIDLENGTH, &dummy, NULL ))
		{
			SENDCMDOUTPARAMS *pOut = (SENDCMDOUTPARAMS *)(buffer + sizeof (SRB_IO_CONTROL));
			IDSECTOR *pId = (IDSECTOR *)(pOut->bBuffer);
			if (pId->sModelNumber[0])
			{
				USHORT *pIdSector = (USHORT *) pId;

				int position = 0;
				char tmp[256];
				for (int i = 10; i < 20; i++)
				{
					tmp[position++] = (char)(pIdSector[i]/256);
					tmp[position++] = (char)(pIdSector[i]%256);
				}
				tmp[position] = '\0';

				serial_number = std::string(tmp, strlen(tmp));
				serial_number.erase( std::remove( serial_number.begin(), serial_number.end(), ' '), serial_number.end() );

				if (!serial_number.empty())
				{
					done = true;
				}
			}
		}
		CloseHandle( hScsiDriveIOCTL );
	}
	else
	{
	}

	return done;
}

char* flipAndCodeBytes( const char* str, int pos, int flip, char* buf)
{
	int i;
	int j = 0;
	int k = 0;

	buf [0] = '\0';
	if (pos <= 0)
		return buf;

	if (!j)
	{
		char p = 0;

		// First try to gather all characters representing hex digits only.
		j = 1;
		k = 0;
		buf[k] = 0;
		for (i = pos; j && str[i] != '\0'; ++i)
		{
			char c = tolower(str[i]);

			if (isspace(c))
				c = '0';

			++p;
			buf[k] <<= 4;

			if (c >= '0' && c <= '9')
				buf[k] |= (unsigned char) (c - '0');
			else if (c >= 'a' && c <= 'f')
				buf[k] |= (unsigned char) (c - 'a' + 10);
			else
			{
				j = 0;
				break;
			}

			if (p == 2)
			{
				if (buf[k] != '\0' && ! isprint(buf[k]))
				{
					j = 0;
					break;
				}
				++k;
				p = 0;
				buf[k] = 0;
			}

		}
	}

	if (!j)
	{
		// There are non-digit characters, gather them as is.
		j = 1;
		k = 0;
		for (i = pos; j && str[i] != '\0'; ++i)
		{
			char c = str[i];

			if ( ! isprint(c))
			{
				j = 0;
				break;
			}

			buf[k++] = c;
		}
	}

	if (!j)
	{
		// The characters are not there or are not printable.
		k = 0;
	}

	buf[k] = '\0';

	if (flip)
		// Flip adjacent characters
		for (j = 0; j < k; j += 2)
		{
			char t = buf[j];
			buf[j] = buf[j + 1];
			buf[j + 1] = t;
		}

		// Trim any beginning and end space
		i = j = -1;
		for (k = 0; buf[k] != '\0'; ++k)
		{
			if (! isspace(buf[k]))
			{
				if (i < 0)
					i = k;
				j = k;
			}
		}

		if ((i >= 0) && (j >= 0))
		{
			for (k = i; (k <= j) && (buf[k] != '\0'); ++k)
				buf[k - i] = buf[k];
			buf[k - i] = '\0';
		}

		return buf;
}

bool hard_disk::read_physical_drive_in_nt_with_zero_rights( std::string& serial_number )
{
	bool done = false;
	//  Windows NT, Windows 2000, Windows XP - admin rights not required
	HANDLE hPhysicalDriveIOCTL = CreateFile( PhysicalDrive0, 0, FILE_SHARE_READ|FILE_SHARE_WRITE, NULL, OPEN_EXISTING, 0, NULL );
	if (hPhysicalDriveIOCTL != INVALID_HANDLE_VALUE)
	{
		STORAGE_PROPERTY_QUERY query;
		memset( &query, 0, sizeof(query) );
		query.PropertyId = StorageDeviceProperty;
		query.QueryType = PropertyStandardQuery;

		char buffer[10000];
		memset( buffer, 0, sizeof (buffer) );

		DWORD cbBytesReturned = 0;

		if (DeviceIoControl( hPhysicalDriveIOCTL, IOCTL_STORAGE_QUERY_PROPERTY, &query, sizeof(query), &buffer, sizeof(buffer), &cbBytesReturned, NULL ))
		{
			STORAGE_DEVICE_DESCRIPTOR* descrip = (STORAGE_DEVICE_DESCRIPTOR*)&buffer;
			char serialNumber[1000];
			flipAndCodeBytes( buffer, descrip->SerialNumberOffset, 1, serialNumber );

			// serial number must be alphanumeric (but there can be leading spaces on IBM drives)
			if (isalnum(serialNumber[0]) || isalnum(serialNumber[19]))
			{
				serial_number.assign( serialNumber );
				serial_number.erase( std::remove( serial_number.begin(), serial_number.end(), ' ' ), serial_number.end() );
				if (!serial_number.empty())
				{
					done = true;
				}
			}
		}
		else
		{
		}

		CloseHandle (hPhysicalDriveIOCTL);
	}
	else
	{
	}

	return done;
}

bool hard_disk::read_physical_drive_in_nt_using_smart( std::string& serial_number )
{
	bool done = false;

	//  Windows NT, Windows 2000, Windows Server 2003, Vista
	HANDLE hPhysicalDriveIOCTL = CreateFile( PhysicalDrive0, GENERIC_READ|GENERIC_WRITE, FILE_SHARE_DELETE|FILE_SHARE_READ|FILE_SHARE_WRITE, NULL, OPEN_EXISTING, 0, NULL );
	if (hPhysicalDriveIOCTL != INVALID_HANDLE_VALUE)
	{
		GETVERSIONINPARAMS GetVersionParams;
		DWORD cbBytesReturned = 0;

		// Get the version, etc of PhysicalDrive IOCTL
		memset ((void*) & GetVersionParams, 0, sizeof(GetVersionParams));

		if (DeviceIoControl (hPhysicalDriveIOCTL, SMART_GET_VERSION, NULL, 0, &GetVersionParams, sizeof(GETVERSIONINPARAMS), &cbBytesReturned, NULL ))
		{
			// Print the SMART version
			// PrintVersion (& GetVersionParams);
			// Allocate the command buffer
			ULONG CommandSize = sizeof(SENDCMDINPARAMS) + IDENTIFY_BUFFER_SIZE;
			PSENDCMDINPARAMS Command = (PSENDCMDINPARAMS)malloc(CommandSize);
			// Retrieve the IDENTIFY data
			// Prepare the command
			Command->irDriveRegs.bCommandReg = 0xEC;
			DWORD BytesReturned = 0;
			if (DeviceIoControl( hPhysicalDriveIOCTL, SMART_RCV_DRIVE_DATA, Command, sizeof(SENDCMDINPARAMS), Command, CommandSize, &BytesReturned, NULL ))
			{
				USHORT* pIdSector = (USHORT*)(PIDENTIFY_DATA)((PSENDCMDOUTPARAMS)Command)->bBuffer;

				int position = 0;
				char tmp[256];
				for (int i = 10; i < 20; i++)
				{
					tmp[position++] = (char)(pIdSector[i]/256);
					tmp[position++] = (char)(pIdSector[i]%256);
				}
				tmp[position] = '\0';

				serial_number = std::string(tmp, strlen(tmp));
				serial_number.erase( std::remove( serial_number.begin(), serial_number.end(), ' '), serial_number.end() );

				if (!serial_number.empty())
				{
					done = true;
				}
			}
			else
			{
			} 

			// Done
			CloseHandle( hPhysicalDriveIOCTL );
			free (Command);
		}
		else
		{
		}
	}
	else
	{
	}

	return done;
}

bool hard_disk::read_drive_ports_in_win_9x( std::string& serial_number )
{
	bool done = false;

	HANDLE VxDHandle = 0;
	pt_IdeDInfo pOutBufVxD = 0;
	DWORD lpBytesReturned = 0;

	//  set the thread priority high so that we get exclusive access to the disk
	// BOOL status = SetThreadPriority( GetCurrentThread(), THREAD_PRIORITY_TIME_CRITICAL );
	BOOL status = SetPriorityClass( GetCurrentProcess (), REALTIME_PRIORITY_CLASS );
	// BOOL status = SetPriorityClass( GetCurrentProcess (), HIGH_PRIORITY_CLASS );

	// 1. Make an output buffer for the VxD
	rt_IdeDInfo info;
	pOutBufVxD = &info;

	// *****************
	// KLUDGE WARNING!!!
	// HAVE to zero out the buffer space for the IDE information!
	// If this is NOT done then garbage could be in the memory
	// locations indicating if a disk exists or not.
	ZeroMemory (&info, sizeof(info));

	// 1. Try to load the VxD
	//  must use the short file name path to open a VXD file
	//char StartupDirectory[2048];
	//char shortFileNamePath[2048];
	//char *p = NULL;
	//char vxd[2048];
	//  get the directory that the exe was started from
	//GetModuleFileName (hInst, (LPSTR) StartupDirectory, sizeof (StartupDirectory));
	//  cut the exe name from string
	//p = &(StartupDirectory [strlen (StartupDirectory) - 1]);
	//while (p >= StartupDirectory && *p && '\\' != *p) p--;
	//*p = '\0';   
	//GetShortPathName (StartupDirectory, shortFileNamePath, 2048);
	//sprintf (vxd, "\\\\.\\%s\\IDE21201.VXD", shortFileNamePath);
	//VxDHandle = CreateFile( vxd, 0, 0, 0, 0, FILE_FLAG_DELETE_ON_CLOSE, 0 );   
	VxDHandle = CreateFile( "\\\\.\\IDE21201.VXD", 0, 0, 0, 0, FILE_FLAG_DELETE_ON_CLOSE, 0 );

	if (VxDHandle != INVALID_HANDLE_VALUE)
	{
		// 2. Run VxD function
		DeviceIoControl( VxDHandle, m_cVxDFunctionIdesDInfo, 0, 0, pOutBufVxD, sizeof(pt_IdeDInfo), &lpBytesReturned, 0 );

		// 3. Unload VxD
		CloseHandle( VxDHandle );
	}
	else
	{
	}

	// 4. Translate and store data
	for (size_t i=0; i<8; i++)
	{
		if((pOutBufVxD->DiskExists[i]) && (pOutBufVxD->IDEExists[i/2]))
		{
			USHORT diskinfo[256];
			for (int j = 0; j < 256; j++)
			{
				diskinfo[j] = pOutBufVxD->DisksRawInfo[i * 256 + j];
			}

			USHORT* pIdSector = diskinfo;
			int position = 0;
			char tmp[256];
			for (int i = 10; i < 20; i++)
			{
				tmp[position++] = (char)(pIdSector[i]/256);
				tmp[position++] = (char)(pIdSector[i]%256);
			}
			tmp[position] = '\0';

			serial_number = std::string(tmp, strlen(tmp));
			serial_number.erase( std::remove( serial_number.begin(), serial_number.end(), ' '), serial_number.end() );
			if (!serial_number.empty())
			{
				done = true;
			}
		}
	}

	// reset the thread priority back to normal
	// SetThreadPriority( GetCurrentThread(), THREAD_PRIORITY_NORMAL );
	SetPriorityClass( GetCurrentProcess (), NORMAL_PRIORITY_CLASS );

	return done;
}
