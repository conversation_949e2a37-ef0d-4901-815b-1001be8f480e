import type { createApp } from 'vue'
import './assets/css/main.css'
import './assets/css/color-variable.scss';

import VueTippy from 'vue-tippy'
import './assets/tippy/tippy.css' // optional for styling

import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

import InlineSvg from 'vue-inline-svg'
import { PerfectScrollbarPlugin } from 'vue3-perfect-scrollbar'
import 'vue3-perfect-scrollbar/style.css'
import Button from './components/ui/button/index.vue'
import Input from './components/ui/input/index.vue'

import XMPMessage from './components/ui/message/index'

export function initialVueApp(app: ReturnType<typeof createApp>) {
  app.use(
    VueTippy,
    // optional
    {
      directive: 'tooltip', // => v-tooltip
      component: 'tooltip', // => <tooltip/>
      componentSingleton: 'tooltip-singleton', // => <tooltip-singleton/>,
      defaultProps: {
        allowHTML: true,
        hideOnClick: true,
        placement: 'top',
        arrow: false,
        offset: [0, 8],
        // theme: 'tooltip',
        duration: 300,
        maxWidth: 398,
        delay: [200, 0],
      }, // => Global default options * see all props
    },
  )
  app.component('RecycleScroller', RecycleScroller)
  app.use(PerfectScrollbarPlugin)
  app.use(XMPMessage)
  app.component('inline-svg', InlineSvg)
  app.component('Button', Button)
  app.component('xl-input', Input)
  // v-focus 指令, 用于input自动聚焦
  app.directive('focus', {
    mounted: (el) => el.focus(),
  })

  // 为方便调用，将 vue 全局方法挂到 window __VueGlobalProperties__ 上
  window.__VueGlobalProperties__ = app.config.globalProperties
}
