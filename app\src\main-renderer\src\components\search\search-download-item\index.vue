<template>
  <div 
    class="search-download-item" 
    :class="{ 
      'search-download-item-focused': focused,
      'search-item-focused': focused,
      'search-download-item-failed': isError
    }"
    @click="handleClick"
  >
    <!-- 文件图标 -->
    <div class="search-download-item-left">
      <div
        class="search-download-item-icon file-icon-type"
        :class="{ 'is-error': isError, [fileIcon]: fileIcon }"
      >
      </div>
    </div>
    
    <!-- 文件信息 -->
    <div class="search-download-item-content">
      <!-- 文件名 -->
      <div :class="['search-download-item-name', { 'is-error': isError }]">
        <span v-html="highlightedName" v-tooltip="{ content: task.taskName, maxWidth: '340px', appendTo: 'parent' }"></span>
      </div>
      <!-- 文件信息：大小 + 下载时间 -->
      <div :class="['search-download-item-info', { 'is-error': isError }]">
        {{ infoText }}
      </div>
    </div>
    
    <!-- 定位按钮 -->
    <div class="search-download-item-right">
      <button class="search-download-item-locate-btn" @click.stop="handleLocate" v-tooltip="'定位文件'">
        <i class="xl-icon-general-location-l"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, createVNode } from 'vue'
import { message } from '@root/common/components/ui/message/index'
import MessageBox from '@root/common/components/ui/message-box/index'
import { TimeHelperNS } from '@root/common/helper/time-helper'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import { ThunderUtil } from '@root/common/utils'
import { taskExtraFunc } from '@root/main-renderer/src/common/task-extra'
import * as BaseType from '@root/common/task/base'
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'
import fs from 'fs'
import MessageContent from './MessageContent.vue'

const props = defineProps<{
  task: BaseType.TaskBase
  focused: boolean
  searchText?: string
}>()

const emit = defineEmits<{
  select: [task: BaseType.TaskBase]
  locate: [task: BaseType.TaskBase]
  mouseenter: []
}>()

const isSupportPlay = ref(false)

// 判断是否为错误状态
const isError = computed(() => {
  return props.task.taskStatus === BaseType.TaskStatus.Failed
})

// 判断是否完成（暂时通过错误状态和下载状态推断）
const completed = computed(() => {
  return props.task.taskStatus === BaseType.TaskStatus.Succeeded
})

// 高亮搜索关键词的文件名
const highlightedName = computed(() => {
  if (!props.searchText || !props.task.taskName) {
    return props.task.taskName
  }
  
  const searchRegex = new RegExp(`(${props.searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return props.task.taskName.replace(searchRegex, '<span class="highlight">$1</span>')
})

// 文件信息：大小 + 下载时间
const infoText = computed(() => {
  if (isError.value) {
    return '下载失败'
  }
  
  const size = ThunderUtil.bytesToSize(props.task.fileSize, 2)
  
  if (props.task.createTime) {
    // 已完成：显示完成大小+时间
    const time = TimeHelperNS.formatDate(new Date(props.task.createTime), 'yyyy-MM-dd  hh:mm')
    return `${size}  ${time}`
  } else {
    // 下载中：显示大小
    return size || '未知大小'
  }
})

// 文件图标
const fileIcon = computed(() => {
  return props.task.taskName ? TaskUtilHelper.getTaskIcon(props.task.taskName, props.task.taskType) : ''
})

// 文件类型
const fileType = computed(() => {
  if (props.task.taskType === BaseType.TaskType.Bt) return TaskUtilHelper.FileExtType.Bt
  return props.task.taskName ? TaskUtilHelper.getTaskFileType(props.task.taskName) : ''
})

// 是否为视频文件
const isVideo = computed(() => {
  return fileType.value === TaskUtilHelper.FileExtType.Video
})

function checkFileExistence(path: string) {
  return new Promise(async (resolve) => {
    try {
      await fs.promises.access(path, fs.constants.F_OK | fs.constants.R_OK);
      console.log('文件存在且可读');
      resolve(true)
    } catch (err: any) {
      if (err.code === 'ENOENT') {
        console.log('文件不存在');
        resolve(false)
      } else if (err.code === 'EACCES') {
        console.log('文件存在但没有访问权限');
        resolve(false) // 修改为false，因为没有权限也应该提示用户
      } else {
        console.error('检查文件时出错:', err);
        resolve(false)
      }
    }
  })
}

const handleClick = async () => {
    // 根据任务状态处理点击事件
    if (isError.value) {
      // 下载失败的任务 - 显示失败对话框
      emit('select', props.task)
      showFailedMessageBox()
      return
    }
  if (isVideo.value) {
    isSupportPlay.value = await taskExtraFunc.getIsSupportPlay(props.task.taskId)
    if (!isSupportPlay.value) {
      message({ message: '该文件不支持播放', type: 'info' })
      return
    }
    // 下载中的视频文件-拉起播放器播放
    emit('select', props.task)
    ConsumeManagerNs.consumeTask(props.task.taskId, -1)
    return
  }
  

  // 已完成的任务 - 检查文件是否存在
  const path = `${props.task.savePath}\\${props.task.taskName}`
  const fileExists = await checkFileExistence(path)
  
  if (fileExists) {
    // 文件存在 - 打开文件
    emit('select', props.task)
    ConsumeManagerNs.consumeTask(props.task.taskId, -1)
  } else {
    // 文件不存在或无权访问 - 显示提示
    message({ message: '文件不存在或无权访问，请检查文件路径', type: 'error' })
  }
  // 其他状态（下载中、暂停等）- 定位到下载路由并高亮当前文件
  
}

// 显示下载失败的 MessageBox
const showFailedMessageBox = () => {
  const taskName = props.task.taskName || '';
  const taskSize = ThunderUtil.bytesToSize(props.task.fileSize, 2);
  
  // 使用组件创建VNode
  const messageVNode = createVNode(MessageContent, {
    taskName,
    taskSize,
    fileIconClass: fileIcon.value,
    messageText: '该文件下载失败无法预览，请尝试重新下载'
  });
  
  MessageBox.alert(messageVNode, '下载失败无法预览', {
    confirmButtonText: '重新下载',
    type: 'warning',
    showCancelButton: false,
    closeOnClickModal: true,
    closeOnPressEscape: true,
  })
  .then(() => {
    // 点击确认按钮
    retryDownload();
    handleLocate()
  })
  .catch(() => {
    // 关闭弹窗
  });
}

const retryDownload = () => {
  if (props.task.taskId) {
    taskExtraFunc.reDownloadTaskById(props.task.taskId)
  }
}

const handleLocate = () => {
  emit('locate', props.task)
}
</script>

<style lang="scss" scoped>
.search-download-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: var(--border-radius-M2, 10px);
  cursor: pointer;
  transition: background-color 0.2s ease;
  height: 68px;

  &:hover,
  &-focused {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
  }

  &-left {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
  }

  &-icon {
    width: 40px;
    height: 40px;
    &.is-error {
      opacity: 0.5;
    }
  }

  &-content {
    position: relative;
    flex: 1;
    margin-left: 12px;
    min-width: 0;
  }

  &-name {
    color: var(--font-font-1);
    font-size: 13px;
    line-height: 22px;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    :deep(.highlight) {
      color: var(--primary-primary-default, #226DF5);
    }

    &.is-error {
      color: var(--font-font-4);

      :deep(.highlight) {
        color: var(--primary-primary-disabled, #97C4FB);
      }
    }
  }

  &-info {
    display: flex;
    align-items: center;
    color: var(--font-font-3, rgba(137, 142, 151, 1));
    font-size: 12px;
    line-height: 20px;

    &.is-error {
      color: var(--functional-error-default, #FF4D4F);
    }
  }

  &-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
  }

  &-locate-btn {
    width: 24px;
    height: 24px;
    display: none;
    align-items: center;
    justify-content: center;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;

    i {
      font-size: 20px;
      color: var(--font-font-2, #4e5769);
    }

    &:hover i {
      color: var(--primary-primary-default, #226DF5);
    }
  }

  &:hover,
  &-focused {
    .search-download-item-locate-btn {
      display: flex;
    }
  }
}

</style>

