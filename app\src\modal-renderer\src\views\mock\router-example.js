// 路由配置示例 - 如何添加mock组件的路由

// 在你的路由配置文件中添加以下路由
export const mockRoutes = [
  {
    path: '/mock-task-list',
    name: 'MockTaskList',
    component: () => import('./mock-task-list.vue'),
    meta: {
      title: '任务列表组件测试',
      description: '用于测试TaskList组件的各种功能',
    },
  },
]

// 如果使用Vue Router，可以这样注册：
/*
import { createRouter, createWebHistory } from 'vue-router'
import MockTaskList from '@/views/mock/mock-task-list.vue'

const routes = [
  // ... 其他路由
  {
    path: '/mock-task-list',
    name: 'MockTaskList',
    component: MockTaskList
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
*/

// 开发环境中访问：
// http://localhost:8080/mock-task-list
