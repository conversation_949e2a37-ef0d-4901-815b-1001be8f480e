#pragma once
#include "UIlib.h"
class LoopPicUI
{
public:
	Loop<PERSON><PERSON><PERSON>();
	~LoopPicUI();
public:
	static LoopPicUI & GetInstance();
	void Init();
	void BeginLoop(CControlUI * pctrl, CControlUI * pClickObj);
	void CALLBACK OnTimer(HWND hwnd, UINT id, UINT_PTR up, DWORD dw);
	std::string UrlEncode(IN std::string & str);
	LPCSTR  GetShowID() { return m_curid.c_str(); };
protected:
	void CreatePathDescByIndex(const vecLoopPicFile & vec, int index);
	void GenerateAlpha();
	void SetClickObjVisible(bool visible = true);
protected:
	CControlUI * m_pctrl;
	CControlUI * m_pClickObj;
	CDuiString	 m_curFile;
	int			 m_alphaValue;
	CDuiString	 m_alphaDesc;
	int			 m_curTimeDis;
	std::string	 m_curid;
	int			 m_nStartTime;
	CDuiString	 m_curURL;
	UINT_PTR	 m_timerid;
	UINT_PTR	 m_alphaTimerId;
};

#define theLoopPic LoopPicUI::GetInstance()

