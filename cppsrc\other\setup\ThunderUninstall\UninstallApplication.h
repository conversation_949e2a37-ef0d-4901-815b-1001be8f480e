#pragma once
#include <vector>
#include <atlbase.h>
#include <atlwin.h>
#include "UninstallManager.h"
#include "UninstallEnvironmentHelper.h"
//UninstallApplication

class CUninstallApplication
{

public:
	static CUninstallApplication& GetInstance();

	CUninstallApplication(void);
	~CUninstallApplication(void);

public:
	HRESULT Initialize(LPCTSTR lpszCommandLine);
	HRESULT Exit(BOOL bFinish);
	HRESULT BeginUninstall(HWND hWnd);
	HRESULT SetUninstallPathAndType(const wchar_t* lpszUninstallPath, const wchar_t* lpszUninstallType);
	//HRESULT OnThunderUninstallMessage(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);
	HRESULT InitAppIcon(HWND hMainWnd);

	HRESULT BeginUninstall(LPCTSTR lpszUninstallPath);
	HRESULT SendStat(int id, std::map<std::string, std::string> *extQuerys = NULL);
	HRESULT SendDetailStat(int id);
	HRESULT InitBindingConfigInfo();

	void SetIsSaveHistoryData(BOOL state);
	BOOL GetIsSaveHistoryData();
	BOOL CheckIsLast();
	int GetPreserveDays();

	uint64 GetTaskCount();
	uint64 GetLastXunleiStartTime();
	uint64 GetLastCreateTaskTime();

	xl::tstring CheckEnvironment();

	void ForceCloseProcess(int killType);
	std::wstring& GetWorkPath();
	std::wstring& GetUninstallPath();
	std::wstring& GetUserId();
	HRESULT CreateDirectory(LPCTSTR lpszDirpath);
	
private:
	HRESULT InitAllDll();
	HRESULT ExtractUninstallRuntime();
	HRESULT InitPreserveDay();
	std::wstring GetVersionBlockString(LPCTSTR lpszFilePath, LPCTSTR lpszBlock);

	BOOL CheckMutex();
	BOOL CloseMutex();
	BOOL CheckWorkPathSpace();
	HRESULT InitWorkPath();
public:
	CUninstallManager m_uninstallManager;
	std::wstring m_lpszUninstallPath;
	std::wstring m_lpszUninstallType;
	bool m_bKeepProfilesForce;
    std::string m_productVersion;

private:
	int m_iPreserveDays;
	HANDLE m_hAppMutext;
	std::wstring m_strWorkPath;
	std::wstring m_strBinPath;
	std::wstring m_strXarPath;
	BOOL m_isSaveHistoryData;
	BOOL m_isLast;
	std::wstring m_strChannelId;
	std::wstring m_strUserId;

	uint64 m_taskcount;
	uint64 m_lastXunleiStartTime;
	uint64 m_lastCreateTaskTime;

};

#define theApp CUninstallApplication::GetInstance()
