import { MainToRenderer_Window_Channel } from '@root/common/constant';
import { win7Logger } from '@root/common/logger';
import requireNodeFile from '@root/common/require-node-file';
import { GetXxxNodePath } from '@root/common/xxx-node-path';
import { AplayerStack } from './client/aplayer-stack'
import { useIpcRendererOn } from '@vueuse/electron';
import path from 'path'

let xmpHelper = requireNodeFile(path.join(GetXxxNodePath(), 'thunder_helper.node'))
export class DwmHelper {
    private static instance: DwmHelper;
    private objs: Map<string, any> = new Map();

    private isEnableDwm = xmpHelper.getEnableDwm ? !!xmpHelper.getEnableDwm() : true
    // private isEnableDwm = false //

    refreshDwm() {
        let rects: any[] = [];
        this.objs.forEach((obj, name) => {
            let elementRect = obj.value?.getBoundingClientRect();
            win7Logger.log('wsw===========', elementRect);
            if (elementRect) {
                let rectObj: any = {};
                rectObj.name = name;
                rectObj.x = elementRect.x;
                rectObj.y = elementRect.y;
                rectObj.w = elementRect.width;
                rectObj.h = elementRect.height;
                rects.push(rectObj);
            }
        });
        if (rects.length > 0) {
            AplayerStack.GetInstance().addFloatShowRect(rects);
        }
    }

    constructor() {
        // 窗口大小变化、最大化、全屏
        window.addEventListener('resize', () => {
            win7Logger.log('wsw=============resize')
            this.refreshDwm()
        });

        // ? 最小化后 恢复展示
        useIpcRendererOn(MainToRenderer_Window_Channel.onRestore, (_) => {
            win7Logger.log('wsw=============restore')
            this.refreshDwm()
        })
    }
    static getInstance() {
        if (!DwmHelper.instance) {
            if (global.DwmHelperInstance) {
                DwmHelper.instance = global.DwmHelperInstance;
            } else {
                DwmHelper.instance = new DwmHelper();
                global.DwmHelperInstance = DwmHelper.instance;
            }
        }
        return DwmHelper.instance;
    }

    public addShow(objs: { name: string, obj: any }[]) {
        let rects: any[] = [];
        objs.forEach((item) => {
            this.objs.set(item.name, item.obj);
            let elementRect = item.obj.value?.getBoundingClientRect();
            win7Logger.log('dwm', 'addShow', 'name=', item.name, 'elementRect=', elementRect)
            if (elementRect) {
                let rectObj: any = {};
                rectObj.name = item.name;
                rectObj.x = elementRect.x;
                rectObj.y = elementRect.y;
                rectObj.w = elementRect.width;
                rectObj.h = elementRect.height;
                rects.push(rectObj);
            }
        });
        if (rects.length > 0) {
            AplayerStack.GetInstance().addFloatShowRect(rects);
        }
    }

    public delShow(names: string[]) {
        win7Logger.log('dwm', 'delShow', 'name=', names)
        names.forEach((name) => {
            this.objs.delete(name)
        });
        AplayerStack.GetInstance().deleteFloatShowRect(names);
    }

    public getIsDwm() {
        return !this.isEnableDwm;
    }
}