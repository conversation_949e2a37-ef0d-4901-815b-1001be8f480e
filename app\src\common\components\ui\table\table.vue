<template>
  <!-- 表格主容器 - 支持边框、斑马纹、树形结构、复选框、悬停效果 -->

  <div
    class="td-table"
    :class="{
      'td-table--border': bordered,
      'td-table--stripe': striped,
      'td-table-tree': treeEnabled,
      'td-table--checkbox': checkable,
      'td-table--hover': hoverable,
    }"
    :data-auto-width="autoWidthColumn ? 'true' : null"
  >
    <!-- 顶部筛选区域 - 包含全选复选框、选中信息显示、自定义筛选插槽 -->

    <div
      class="td-table__filter-header-wrapper"
      v-if="headerEnabled"
    >
      <div class="td-table__filter-container">
        <div class="td-table__selection-info">
          <!-- 全选复选框 -->

          <td-checkbox
            :model-value="allChecked"
            :indeterminate="allIndeterminate"
            @update:model-value="checkAll"
          >
            <span class="selection-text">全选</span>
          </td-checkbox>

          <!-- 选中文件数量和大小信息显示 -->

          <span
            class="selection-count"
            v-if="selectLeafCount > 0 && showSelectionCount"
          >
            已选 {{ selectLeafCount }}/{{ totalLeafCount }} 个文件，共 {{ formatSize(totalSize) }}
          </span>

          <!-- 自定义头部筛选插槽 -->

          <div class="td-table__selection-filter">
            <slot name="header-filter"></slot>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格头部区域 - 包含列标题、排序功能、树形展开控制 -->

    <div class="td-table__header-wrapper">
      <table class="td-table__header">
        <!-- 列宽度控制 -->

        <colgroup>
          <col
            v-for="(column, index) in computedColumns"
            :width="column.computedWidth || column.width"
            :class="{ 'auto-width': autoWidthColumn && column.prop === autoWidthColumn }"
            :key="column.prop"
          />
        </colgroup>

        <thead>
          <tr>
            <!-- 表头列渲染 - 支持排序和树形展开控制 -->

            <th
              v-for="(column, index) in columns"
              :key="column.prop"
            >
              <!-- 可排序的列标题 -->

              <a
                class="td-table__text"
                v-if="column.sortable"
                href="javascript:;"
              >
                {{ column.label }}
                <!-- 排序图标 -->
                <div class="td-table__sort" @click="handleSort(column)">
                  <i class="xl-icon-general-direction-caret-up-m" :class="{ 'is-light': column === sorting.column && sorting.order === 'descending' }"></i>
                  <i class="xl-icon-general-direction-caret-down-m" :class="{ 'is-light': column === sorting.column && sorting.order === 'ascending' }"></i>
                </div>
                <!-- <td-icon
                  :class="{
                    'is-show': column === sorting.column,
                    [`is-${sorting.order}`]: column === sorting.column,
                  }"
                  type="sequence"
                ></td-icon> -->
              </a>

              <!-- 不可排序的列标题 -->

              <p
                class="td-table__text"
                v-else
              >
                <!-- 树形结构全部展开/收起控制图标 -->

                <td-icon
                  class="td-tree-node__expand-icon"
                  v-if="treeEnabled && index === 0"
                  :class="{
                    'is-expanded': allExpanded,
                    'is-hidden': !allExpandable,
                  }"
                  type="arrow-drop"
                  @click="expandAll(!allExpanded)"
                ></td-icon>
                {{ column.label }}
              </p>
            </th>
          </tr>
        </thead>
      </table>
    </div>

    <!-- 表格主体提示区域 - 可插入自定义提示内容 -->

    <div
      class="td-table__body-tip-wrapper"
      v-if="bodyTipEnabled"
    >
      <slot name="body-tip"></slot>
    </div>

    <!-- 表格主体区域 - 虚拟滚动容器，支持大数据量渲染 -->

    <div
      class="td-table__body-wrapper"
      :style="{ 'max-height': `${height}px` }"
      ref="bodyWrapper"
    >
      <!-- 虚拟滚动容器 -->

      <div
        class="td-table__panel"
        :style="{ height: `${containerHeight}px` }"
        ref="container"
        @scroll="calculateScroll"
      >
        <!-- 虚拟滚动占位元素 - 用于维持滚动条高度 -->

        <div
          class="td-table__phantom"
          :style="{ height: `${rowHeight * sortedRowsLength}px` }"
          ref="phantom-list"
        ></div>

        <!-- 实际渲染的表格内容 -->

        <table
          class="td-table__body"
          ref="content"
        >
          <!-- 列宽度控制 -->

          <colgroup>
            <col
              v-for="(column, index) in computedColumns"
              :width="column.computedWidth || column.width"
              :class="{ 'auto-width': autoWidthColumn && column.prop === autoWidthColumn }"
              :key="column.prop"
            />
          </colgroup>

          <tbody>
            <!-- 表格行渲染 - 只渲染可见区域的行 -->
            <!-- 
              关键优化点：
              1. 使用 row.key 作为 key，而不是 index
              2. 原因：当展开/折叠节点时，sortedRows 的长度会变化
              3. 如果使用 index 作为 key，会导致其他行的索引发生变化
              4. Vue 会认为组件发生了变化，重新渲染所有行组件
              5. 这会导致其他行的折叠按钮出现抖动
              6. 使用 row.key 作为 key，Vue 能正确识别和复用组件
            -->

            <tr
              v-for="(row, index) of visibleRow"
              :style="{
                display: status[row.key]?.visible ? 'table-row' : 'none',
              }"
              :class="{ 'is-checked': status[row.key]?.checked }"
              :key="row.key"
              :level="row._level"
              @mouseenter="handleRowMouseEnter(row)"
              @mouseleave="handleRowMouseLeave"
            >
              <!-- 表格单元格渲染 -->

              <td
                v-for="(column, index) in columns"
                :style="{
                  paddingLeft:
                    index === 0 && treeEnabled && row._level > 1
                      ? `${(row._level - 1) * 20}px`
                      : null,
                }"
                :key="column.prop"
              >
                <!-- 第一列：树形节点或复选框列 -->

                <td-tree-node
                  v-if="index === 0 && (checkable || treeEnabled)"
                  :label="row[column.prop]"
                  :level="row._level"
                  :checked="status[row.key]?.checked"
                  :disabled="status[row.key]?.disabled"
                  :expanded="status[row.key]?.expanded"
                  :indeterminate="status[row.key]?.indeterminate"
                  :checkable="checkable"
                  :tree-enabled="treeEnabled"
                  :is-show-hint="selectLeafs.includes(row.key)"
                  :expandable="sget(row._children, 'length') > 0"
                  :has-children="!!row._children"
                  :in-table="true"
                  @change="check(row.key)"
                  @click-label="$emit('click-label', row.key)"
                  @dbclick-label="$emit('dbclick-label', row)"
                  @update:expanded="expand(row.key)"
                >
                  <!-- 前缀图标插槽 -->

                  <template
                    v-if="$slots.prefix"
                    #prefix
                  >
                    <slot
                      name="prefix"
                      :prop="column.prop"
                      :value="row._row[column.prop]"
                      :row="row._row"
                    ></slot>
                  </template>

                  <!-- 节点图标插槽 -->

                  <template #icon>
                    <slot
                      name="icon"
                      :prop="column.prop"
                      :value="row._row[column.prop]"
                      :row="row._row"
                    ></slot>
                  </template>

                  <!-- 节点标签插槽 -->

                  <template #label>
                    <slot
                      name="label"
                      :prop="column.prop"
                      :value="row._row[column.prop]"
                      :row="row._row"
                    >
                      {{ row._row[column.prop] }}
                    </slot>
                  </template>
                </td-tree-node>

                <!-- 普通单元格内容 -->

                <p
                  class="td-table__text actions-wrapper"
                  v-else
                >
                  <!-- 悬停操作插槽 - 允许调用者自定义悬停时的内容 -->

                  <!-- <template v-if="hoverActions && hoveredRowKey === row.key"> -->

                  <template v-if="shouldShowHoverActions(row.key)">
                    <slot
                      name="hover-actions"
                      :row="row._row"
                      :rowKey="row.key"
                      :column="column"
                      :columnIndex="index"
                    >
                      <!-- 默认内容：如果没有提供插槽，显示原始内容 -->

                      <slot
                        :prop="column.prop"
                        :value="row._row[column.prop]"
                        :row="row._row"
                      >
                        {{ row._row[column.prop] }}
                      </slot>
                    </slot>
                  </template>

                  <!-- 正常内容显示 -->

                  <template v-else>
                    <slot
                      :prop="column.prop"
                      :value="row._row[column.prop]"
                      :row="row._row"
                    >
                      {{ row._row[column.prop] }}
                    </slot>
                  </template>
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 表格底部区域 - 包含底部全选和自定义内容 -->

    <div
      class="td-table__footer-wrapper"
      v-if="footerEnabled"
    >
      <table class="td-table__footer">
        <colgroup>
          <col />

          <col :width="footerColWidth" />
        </colgroup>

        <tbody>
          <tr>
            <!-- 底部全选复选框 -->

            <td class="td-table-tree__cell">
              {{ allChecked }}
              <td-checkbox
                :model-value="allChecked"
                :indeterminate="allIndeterminate"
                @update:model-value="checkAll"
                :disabled="allSelectedDisabled"
              >
                <slot
                  name="footer-checkbox"
                  :selectLeafCount="selectLeafCount"
                >
                  全选
                </slot>
              </td-checkbox>
            </td>

            <!-- 底部自定义内容插槽 -->

            <td class="td-table-tree__cell">
              <slot name="footer"></slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import sget from '@xunlei/sget'
import sortBy from '@xunlei/sort-by'
import Icon from '@root/common/components/ui/icon/icon.vue'
import TreeNode from '@root/common/components/ui/tree/treeNode.vue'
import CheckBox from '@root/common/components/ui/checkbox/index.vue'

/**
 * 判断值是否已定义（不为undefined和null）
 * @param {*} val - 要检查的值
 * @returns {boolean} 是否已定义
 */
function isDef(val) {
  return val !== undefined && val !== null
}

/**
 * 判断值是否未定义（为undefined或null）
 * @param {*} val - 要检查的值
 * @returns {boolean} 是否未定义
 */
function isUndef(val) {
  return val === undefined || val === null
}

export default {
  components: {
    TdIcon: Icon,
    TdTreeNode: TreeNode,
    TdCheckbox: CheckBox,
  },

  props: {
    columns: Array,
    data: Array,
    height: {
      type: Number,
      default: 1200, // 大幅增加到1200px，配合40px行高
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => [],
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => [],
    },
    disabledKeys: {
      type: Array,
      default: () => [],
    },
    bordered: Boolean,
    striped: Boolean,
    checkable: Boolean,
    footerEnabled: Boolean,
    // body之上插入tip
    bodyTipEnabled: Boolean,
    headerEnabled: Boolean,
    hoverable: Boolean,
    treeEnabled: Boolean,
    // 新增：是否启用行悬停操作按钮
    hoverActions: {
      type: Boolean,
      default: false,
    },
    // 新增：调试模式 - 是否启用hoverActions调试
    debugHoverActions: {
      type: Boolean,
      default: true,
    },
    // 新增：调试模式 - 指定显示hoverActions的行keys
    debugHoverRowKeys: {
      type: Array,
      default: () => [],
    },
    footerColWidth: {
      type: Number,
      default() {
        return 110
      },
    },
    allSelectedDisabled: {
      type: Boolean,
      default: false,
    },
    // table内单行高度, 固定为40px
    rowHeight: {
      type: Number,
      default: 40, // 固定为40px
    },
    selectLeafs: {
      // 展示前缀节点
      type: Array,
      default: () => [],
    },
    // 新增：自适应列宽配置
    autoWidthColumn: {
      type: String,
      default: null,
    },
    // 新增：是否显示选中文件数量和大小信息
    showSelectionCount: {
      type: Boolean,
      default: false,
    },
    // 新增：缓冲区大小配置，用于预渲染更多行
    bufferSize: {
      type: Number,
      default: 40, // 上下各预渲染10行
    },
  },

  data() {
    return {
      rows: [],
      checkedKeysCache: null,
      expandedKeysCache: null,
      disabledKeysCache: null,
      checkedRow: [],
      sorting: {
        column: null,
        order: '',
      },
      status: {},
      visibleRow: [],
      top: 0,
      firstElementIndex: 0,
      rowNum: 40, // 可见区域可以显示多少行, 20是随便定的,
      tableScrollTop: 0,
      allExpanded: false,
      sortedRowsLength: 0,
      selectLeafCount: 0,
      jumpLocationKey: '',
      fileTypeFilters: {
        video: true,
        image: true,
        compressed: true,
        other: true,
      },
      // 新增：当前悬停的行
      hoveredRowKey: null,
      // 新增：缓冲区大小，用于预渲染更多行
      bufferSize: this.bufferSize || 10, // 使用props中的值，默认10
    }
  },

  computed: {
    containerHeight() {
      return Math.min(this.rowHeight * this.sortedRowsLength, this.height)
    },
    // 简化：移除缓存机制，直接计算sortedRows
    sortedRows() {
      let rows = this.rows.filter(row => {
        const status = this.status[row.key]
        return status && status.visible
      })

      if (this.sorting.column) {
        rows.sort((a, b) => this.compare(a, b, this.rows))
      }

      return rows
    },
    allChecked() {
      return this.checkedRow.length === this.rows.length
    },
    allIndeterminate() {
      return (
        !this.allChecked &&
        Boolean(
          this.rows.find(row => {
            const status = this.status[row.key]
            return status && (status.checked || status.indeterminate)
          })
        )
      )
    },
    allExpandable() {
      return this.rows.find(row => row._children)
    },
    // 简化：移除详细的调试日志
    totalSize() {
      let total = 0

      this.checkedRow.forEach(row => {
        // 检查行的类型，如果是branch类型，跳过不计算
        const rowType = row._row?.type || row.type
        if (rowType === 'branch') {
          return // 跳过分支节点，不计算其大小
        }

        const filesize = row._row?.fileSize || 0
        const fileSizeType = typeof filesize
        const isNumber = fileSizeType === 'number'
        const actualSize = isNumber ? filesize : 0

        total += actualSize
      })

      // 新增：emit totalSize 给父组件
      this.$emit('total-size-change', total)

      return total
    },
    // 新增：计算总的叶子节点数量
    totalLeafCount() {
      return this.rows.filter(row => !row._children).length
    },
    // 新增：判断某行是否应该显示hoverActions
    shouldShowHoverActions() {
      return rowKey => {
        return this.hoverActions && this.hoveredRowKey === rowKey
      }
    },
    // 新增：计算列宽度，支持自适应列
    computedColumns() {
      if (!this.autoWidthColumn || !this.columns) {
        return this.columns
      }

      // 查找自适应列的索引
      const autoColumnIndex = this.columns.findIndex(col => col.prop === this.autoWidthColumn)
      if (autoColumnIndex === -1) {
        console.warn(`Auto width column "${this.autoWidthColumn}" not found in columns`)
        return this.columns
      }

      // 计算固定列的总宽度
      let fixedWidth = 0
      this.columns.forEach((col, index) => {
        if (index !== autoColumnIndex && col.width) {
          // 提取数字部分，支持 '100px' 或 100 格式
          const width =
            typeof col.width === 'string' ? parseInt(col.width.replace(/px|%/g, '')) : col.width
          fixedWidth += width
        }
      })

      // 返回带有计算宽度的列配置
      return this.columns.map((col, index) => {
        if (index === autoColumnIndex) {
          // 自适应列不设置固定宽度，让CSS自动计算
          return {
            ...col,
            computedWidth: 'auto',
          }
        } else {
          return {
            ...col,
            computedWidth: col.width,
          }
        }
      })
    },
  },

  watch: {
    data: {
      handler(val) {
        console.log('data', val)
        this.rows = Object.freeze(this.getRows(val))
        this.initStatus()
        this.onDataChange()
      },

      immediate: true,
    },
    // resize 时触发
    height: {
      handler() {
        this.rowNum = Math.floor(this.height / this.rowHeight) + 1
      },
      immediate: true,
    },
    containerHeight(newVal) {
      this.visibleCount = Math.ceil(newVal / this.rowHeight)
      this.recalculateVisibleRow()
    },
    sortedRows: {
      handler(newVal) {
        this.recalculateVisibleRow()
        this.sortedRowsLength = newVal.length
      },
      immediate: true,
    },
    defaultCheckedKeys: {
      handler() {
        this.initStatus()
      },
      immediate: false,
    },
    defaultExpandedKeys: {
      handler() {
        this.initStatus()
      },
      immediate: false,
    },
    disabledKeys: {
      handler() {
        this.initStatus()
      },
      immediate: false,
    },
    // 新增：监听bufferSize变化
    bufferSize: {
      handler(newVal) {
        this.bufferSize = newVal
      },
      immediate: true,
    },
  },

  methods: {
    sget,

    /**
     * 格式化文件大小显示
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小字符串（如：1.50 GB）
     */
    formatSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    /**
     * 跳转到指定行并展开路径
     * @param {string} key - 目标行的key值
     */
    jumpToRow(key) {
      const pathList = key.split('\\')
      this.jumpLocationKey = key
      let keyList = []
      // 构建路径列表，逐级展开父级节点
      for (let i = 1; i < pathList.length; i++) {
        const path = pathList.slice(0, i).join('\\')
        keyList.push(path)
      }
      // 展开所有父级节点
      for (let keyItem of keyList) {
        this.expand(keyItem, true)
      }
      // 滚动到具体位置
      this.$nextTick(() => {
        let distanceTopNum = 0
        // 计算目标行在可见行中的位置
        for (const keyItem in this.status) {
          const statusVal = this.status[keyItem]
          if (statusVal && (statusVal.expanded || statusVal.visible)) {
            distanceTopNum += 1
          }
          if (key === keyItem) {
            break
          }
        }
        // 滚动到目标位置
        this.$refs['container'].scrollTop = (distanceTopNum - 1) * this.rowHeight
      })
    },

    /**
     * 重新计算可见行范围
     * 根据当前滚动位置和容器高度计算需要渲染的行
     * 新增：使用缓冲区预渲染更多行
     */
    recalculateVisibleRow() {
      this.start = Math.min(this.start, this.sortedRows.length)
      this.end = this.start + this.visibleCount

      // 添加缓冲区，预渲染更多行
      const bufferStart = Math.max(0, this.start - this.bufferSize)
      const bufferEnd = Math.min(this.sortedRows.length, this.end + this.bufferSize)

      this.visibleRow = this.sortedRows.slice(bufferStart, bufferEnd)
    },

    /**
     * 比较两行数据用于排序
     * @param {Object} a - 第一行数据
     * @param {Object} b - 第二行数据
     * @param {Array} origin - 原始数据数组
     * @returns {number} 比较结果（-1, 0, 1）
     */
    compare(a, b, origin) {
      let i = 0
      let pathNodeA = a._path[i]
      let pathNodeB = b._path[i]
      let descending = this.sorting.order === 'descending'
      let column = this.sorting.column

      // 找到第一个不同的路径节点
      while (pathNodeA === pathNodeB) {
        i++
        pathNodeA = a._path[i]
        pathNodeB = b._path[i]
      }

      return sortBy(
        function (a, b) {
          if (!a) return -1
          if (!b) return 1
          return 0
        },
        function (a, b) {
          let result = 0

          if (column.sorter) {
            result = column.sorter(a._row, b._row)
          } else {
            let propA = a._row[column.prop]
            let propB = b._row[column.prop]

            if (propA < propB) result = -1
            else if (propA > propB) result = 1
          }

          return descending ? -result : result
        },
        function (a, b) {
          if (origin.indexOf(a) < origin.indexOf(b)) {
            return descending ? 1 : -1
          } else {
            return descending ? -1 : 1
          }
        }
      )(pathNodeA, pathNodeB)
    },

    /**
     * 将原始数据转换为表格行数据结构
     * @param {Array} data - 原始数据数组
     * @param {number} level - 当前层级，默认为0
     * @param {Object} parent - 父级节点，默认为null
     * @returns {Array} 转换后的行数据数组
     */
    getRows(data, level = 0, parent = null) {
      let rows = []

      data.forEach(row => {
        let newRow = {
          _level: level,
          _parent: parent,
          _row: row,
          key: row.key,
        }

        // 构建节点路径
        newRow._path = parent ? [...parent._path, newRow] : [newRow]
        rows.push(newRow)

        // 处理子节点
        if (row.children) {
          let childrenRows = this.getRows(row.children, level + 1, newRow).map(Object.freeze)

          rows = [...rows, ...childrenRows]
          newRow._children = childrenRows.filter(row => row._level === level + 1)
          newRow._childrenRows = childrenRows
        }
      })

      return rows
    },

    /**
     * 获取所有行的状态信息（选中、展开、可见等）
     * @param {Array} rows - 行数据数组
     * @param {Object} status - 状态对象，默认为空对象
     * @returns {Object} 包含所有行状态的对象
     */
    getStatus(rows, status = {}) {
      rows.forEach(row => {
        let parent = status[row._parent && row._parent.key] || {}

        status[row.key] = {
          checked: Boolean(this.checkedKeysCache[row.key]),
          disabled: parent.disabled || this.disabledKeysCache[row.key],
          expanded: Boolean(this.expandedKeysCache[row.key]),
          indeterminate: false,
          visible: row._level === 0 || (parent.expanded && parent.visible),
          row,
        }

        if (row._children) {
          // 递归处理子节点
          this.getStatus(row._children, status)

          // 根据子节点状态更新父节点选中状态
          status[row.key].checked = row._children.every(child => {
            const childStatus = status[child.key]
            return childStatus && childStatus.checked
          })

          // 设置半选状态
          if (
            !status[row.key].checked &&
            row._children.find(child => {
              const childStatus = status[child.key]
              return childStatus && (childStatus.checked || childStatus.indeterminate)
            })
          ) {
            status[row.key].indeterminate = true
          }
        } else {
          // 叶子节点：统计选中文件数
          status[row.key].checked && ++this.selectLeafCount
        }
      })

      return status
    },

    /**
     * 初始化所有行的状态
     * 根据默认选中、展开、禁用的key数组初始化状态
     */
    initStatus() {
      this.checkedKeysCache = {}
      this.expandedKeysCache = {}
      this.disabledKeysCache = {}

      // 初始化默认选中
      this.defaultCheckedKeys.forEach(key => {
        this.checkedKeysCache[key] = true
      })

      // 初始化选中文件数目
      this.selectLeafCount = 0

      // 初始化默认展开
      this.defaultExpandedKeys.forEach(key => {
        this.expandedKeysCache[key] = true
      })

      // 初始化禁用状态
      this.disabledKeys.forEach(key => {
        this.disabledKeysCache[key] = true
      })

      this.status = this.getStatus(this.rows.filter(row => row._level === 0))
      let [_, checkedRow] = this.getCheckedRowAndCheckedKey()
      this.checkedRow = checkedRow

      // 初始化时更新全展开状态
      this.allExpanded = this.rows.every(row => {
        const status = this.status[row.key]
        return status && status.visible
      })

      // 初始化完成后emit selectLeafCount事件
      this.$emit('select-leaf-count-change', this.selectLeafCount)
    },

    /**
     * 切换行的选中状态
     * @param {string} key - 行的唯一标识
     * @param {boolean} checked - 是否选中，可选参数
     * @param {boolean} checkAll - 是否为全选操作，可选参数
     */
    check(key, checked, checkAll) {
      console.log('🔄 [check] 执行选中操作', 'key', key, 'checked', checked, 'checkAll', checkAll)
      let row = this.rows.find(row => row.key === key)
      const status = this.status[row.key]
      if (!status) return // 添加安全检查

      // 确定新的选中状态
      checked = isDef(checked)
        ? checked
        : row._children
          ? !row._childrenRows
              .filter(row => {
                const childStatus = this.status[row.key]
                return !row._children && childStatus && !childStatus.disabled
              })
              .every(row => {
                const childStatus = this.status[row.key]
                return childStatus && childStatus.checked
              })
          : !status.checked

      /**
       * 递归更新节点及其子节点的选中状态
       * @param {Object} row - 要更新的行对象
       * @param {boolean} checked - 选中状态
       */
      let updateChecked = (row, checked) => {
        const status = this.status[row.key]
        if (!status) return // 添加安全检查

        if (row._children) {
          // 更新所有子节点
          row._children.forEach(row => {
            const childStatus = this.status[row.key]
            if (childStatus && childStatus.checked !== checked) {
              updateChecked(row, checked)
            }
          })

          // 更新父节点状态
          status.checked = row._children.every(row => {
            const childStatus = this.status[row.key]
            return childStatus && childStatus.checked
          })

          // 设置半选状态
          if (status.checked) {
            status.indeterminate = false
          } else {
            status.indeterminate = Boolean(
              row._children.find(row => {
                const childStatus = this.status[row.key]
                return childStatus && (childStatus.checked || childStatus.indeterminate)
              })
            )
          }
        } else {
          // 叶子节点：更新选中状态和计数
          if (!status.disabled) {
            status.checked = checked
          }

          status.checked ? ++this.selectLeafCount : --this.selectLeafCount
        }
      }

      updateChecked(row, checked)
      this.updateCheckedUpward(row._parent)
      checkAll || this.emitCheckedChange(key, checked)

      // 新增：emit totalSize 变化
      this.$emit('total-size-change', this.totalSize)
    },

    /**
     * 全选/取消全选操作
     * @param {boolean} checked - 是否全选，可选参数
     */
    checkAll(checked) {
      console.log('🔄 [checkAll] 执行全选操作', checked)
      
      // 确定新的选中状态
      const newCheckedState = isDef(checked)
        ? checked
        : !this.rows
            .filter(row => {
              const status = this.status[row.key]
              return !row._children && status && !status.disabled
            })
            .every(row => {
              const status = this.status[row.key]
              return status && status.checked
            })

      // 对所有顶级节点执行选中操作
      this.rows
        .filter(row => row._level === 0)
        .forEach(row => {
          const status = this.status[row.key]
          if (status && status.checked !== newCheckedState) {
            this.check(row.key, newCheckedState, true)
          }
        })

      // 向父组件传递全选/全不选状态
      this.$emit('checkAll', newCheckedState)
      this.emitCheckedChange()

      // 新增：emit totalSize 变化
      this.$emit('total-size-change', this.totalSize)
      
      console.log('✅ [checkAll] 全选操作完成，状态:', newCheckedState ? '全选' : '全不选')
    },

    /**
     * 展开/收起节点
     * @param {string} key - 节点的唯一标识
     * @param {boolean} expanded - 是否展开，可选参数
     */
    expand(key, expanded) {
      let row = this.rows.find(row => row.key === key)
      const status = this.status[key]
      if (!status) return // 添加安全检查

      expanded = isDef(expanded) ? expanded : !status.expanded

      /**
       * 递归更新子节点的可见状态
       * @param {Object} row - 要更新的行对象
       * @param {boolean} visible - 可见状态
       */
      let updateVisible = (row, visible) => {
        row._children.forEach(innerRow => {
          const innerStatus = this.status[innerRow.key]
          const parentStatus = this.status[innerRow._parent.key]
          if (innerStatus && parentStatus) {
            innerStatus.visible = visible && parentStatus.expanded
          }
          innerRow._children && updateVisible(innerRow, visible)
        })
      }

      status.expanded = expanded
      status.visible && updateVisible(row, expanded)

      // 直接计算 allExpanded
      this.allExpanded = this.rows.every(row => {
        const status = this.status[row.key]
        return status && status.visible
      })

      this.emitExpandedChange(key, expanded)
    },

    /**
     * 展开/收起所有节点
     * @param {boolean} expanded - 是否展开，可选参数
     */
    expandAll(expanded) {
      expanded = isDef(expanded) ? expanded : !this.allExpanded
      let status = this.status
      let allExpanded = true
      let rows = this.rows

      // 批量更新所有节点的展开和可见状态
      for (let i = 0, len = rows.length; i < len; i++) {
        let row = rows[i]
        let statusOfRow = status[row.key]
        if (!statusOfRow) continue // 添加安全检查

        if (row._level > 0) {
          statusOfRow.visible = expanded
        }
        if (row._children) {
          statusOfRow.expanded = expanded
        }
        allExpanded && (allExpanded = allExpanded && statusOfRow.visible)
      }

      this.allExpanded = allExpanded
      this.emitExpandedChange()
    },

    /**
     * 获取当前选中的行数据和key数组
     * @returns {Array} [选中的key数组, 选中的行数据数组]
     */
    getCheckedRowAndCheckedKey() {
      let checkedKey = []
      const status = this.status
      let checkedRow = []

      for (let key in status) {
        const statusItem = status[key]
        if (statusItem && statusItem.checked) {
          checkedKey.push(key)
          checkedRow.push(statusItem.row)
        }
      }

      return [checkedKey, checkedRow]
    },

    /**
     * 触发选中状态变化事件
     * @param {string} key - 变化的行key
     * @param {boolean} checked - 选中状态
     */
    emitCheckedChange(key, checked) {
      let [checkedKey, checkedRow] = this.getCheckedRowAndCheckedKey()
      this.checkedRow = checkedRow
      this.$emit('checked-change', checkedKey, key, checked)
      // 新增：emit selectLeafCount 变化事件
      this.$emit('select-leaf-count-change', this.selectLeafCount)

      // 新增：emit totalSize 变化
      this.$emit('total-size-change', this.totalSize)
    },

    /**
     * 触发展开状态变化事件
     * @param {string} key - 变化的行key
     * @param {boolean} expanded - 展开状态
     */
    emitExpandedChange(key, expanded) {
      let expandKey = []
      const status = this.status

      for (let key in status) {
        const statusItem = status[key]
        if (statusItem && statusItem.checked) {
          expandKey.push(key)
        }
      }

      this.$emit('expanded-change', expandKey, key, expanded)
    },

    /**
     * 处理列排序
     * @param {Object} column - 要排序的列对象
     */
    handleSort(column) {
      if (this.sorting.column === column) {
        // 切换排序方向
        this.sorting.order = this.sorting.order === 'ascending' ? 'descending' : 'ascending'
      } else {
        // 设置新的排序列
        this.sorting = {
          column,
          order: 'ascending',
        }
      }
      this.recalculateVisibleRow()
    },

    /**
     * 计算滚动位置并更新可见行
     * 简化虚拟滚动逻辑
     * @param {Event} event - 滚动事件对象
     */
    calculateScroll(event) {
      let cb = () => {
        const scrollTop = this.$refs['container'].scrollTop
        const fixedScrollTop = scrollTop - (scrollTop % this.rowHeight)
        this.tableScrollTop = fixedScrollTop

        // 使用transform移动内容区域，实现虚拟滚动
        this.$refs.content.style.webkitTransform = `translate3d(0, ${fixedScrollTop}px, 0)`

        // 计算可见区域的开始和结束索引
        this.start = Math.floor(scrollTop / this.rowHeight)
        this.end = this.start + this.visibleCount
        this.visibleRow = this.sortedRows.slice(this.start, this.end)
      }

      cb()

      // 使用requestAnimationFrame优化性能
      // if (window.requestAnimationFrame) {
      //   window.requestAnimationFrame(cb)
      // } else {
      //   cb()
      // }
    },

    /**
     * 数据变化时的处理方法
     * 重新计算可见行数和初始化显示
     */
    onDataChange() {
      this.$nextTick(() => {
        this.visibleCount = Math.ceil(this.containerHeight / this.rowHeight)
        this.start = 0
        this.end = this.start + this.visibleCount
        this.visibleRow = this.sortedRows.slice(this.start, this.end)
        this.$forceUpdate()
      })
    },

    /**
     * 处理鼠标进入行事件
     * @param {string} key - 行的唯一标识
     */
    handleRowMouseEnter(row) {
      if (this.hoverActions) {
        this.hoveredRowKey = row.key
      }
    },

    /**
     * 处理鼠标离开行事件
     */
    handleRowMouseLeave() {
      if (this.hoverActions) {
        this.hoveredRowKey = null
      }
    },

    /**
     * 仅选此项操作 - 取消所有选中项，然后只选中指定项
     * @param {string} key - 要选中的行的唯一标识
     */
    selectOnly(key) {
      console.log('🎯 [selectOnly] 执行仅选此项操作', 'key', key)

      // 先清空所有选中状态
      this.selectLeafCount = 0

      // 遍历所有行，取消选中状态
      for (let key in this.status) {
        const statusItem = this.status[key]
        if (statusItem) {
          statusItem.checked = false
          statusItem.indeterminate = false
        }
      }

      // 然后只选中目标项
      const targetRow = this.rows.find(row => row.key === key)
      if (targetRow) {
        const targetStatus = this.status[key]
        if (targetStatus && !targetStatus.disabled) {
          targetStatus.checked = true

          // 如果是叶子节点，更新计数
          if (!targetRow._children) {
            this.selectLeafCount = 1
          }

          // 向上更新父节点状态
          this.updateCheckedUpward(targetRow._parent)
        }
      }

      // 触发状态变化事件
      this.emitCheckedChange(key, true)

      console.log('✅ [selectOnly] 仅选此项操作完成', {
        目标key: key,
        最终选中项数量: this.selectLeafCount,
      })
    },

    /**
     * 向上更新父节点的选中状态
     * @param {Object} row - 要更新的行对象
     */
    updateCheckedUpward(row) {
      if (isUndef(row)) return

      const status = this.status[row.key]
      if (!status) return // 添加安全检查

      // 根据子节点状态更新父节点
      status.checked = row._children.every(row => {
        const childStatus = this.status[row.key]
        return childStatus && childStatus.checked
      })

      // 设置半选状态
      if (status.checked) {
        status.indeterminate = false
      } else {
        status.indeterminate = Boolean(
          row._children.find(row => {
            const childStatus = this.status[row.key]
            return childStatus && (childStatus.checked || childStatus.indeterminate)
          })
        )
      }

      this.updateCheckedUpward(row._parent)
    },

    /**
     * 批量更新选中状态
     * 参考 table2.vue 的高性能实现，直接操作状态对象
     * @param {Array} updates - 更新数组，每个元素包含 { key: string; checked: boolean }
     */
    batchCheck(updates) {
      const startTime = performance.now()
      console.log('🚀 [batchCheck] 开始执行批量选中操作', {
        更新项数量: updates.length,
        总行数: this.rows.length,
        当前选中数: this.selectLeafCount,
      })

      if (!Array.isArray(updates) || updates.length === 0) {
        console.warn('[batchCheck] 更新数组为空或格式不正确')
        return
      }

      // 1. 批量更新内部状态，避免逐个DOM操作
      const updateStartTime = performance.now()
      let actualUpdates = 0
      let skippedUpdates = 0

      updates.forEach(({ key, checked }) => {
        if (this.status[key] && !this.status[key].disabled) {
          // 直接更新状态，不触发级联更新
          this.status[key].checked = checked
          actualUpdates++

          // 更新选中叶子节点计数
          const row = this.rows.find(r => r.key === key)
          if (row && !row._children) {
            // 只对叶子节点更新计数
            if (checked) {
              this.selectLeafCount++
            } else {
              this.selectLeafCount--
            }
          }
        } else {
          skippedUpdates++
        }
      })

      const updateEndTime = performance.now()
      const updateTime = updateEndTime - updateStartTime

      // 2. 批量处理父子关系更新
      const parentUpdateStartTime = performance.now()
      const affectedParents = new Set()

      updates.forEach(({ key }) => {
        const row = this.rows.find(r => r.key === key)
        if (row && row._parent) {
          affectedParents.add(row._parent.key)
        }
      })

      // 更新所有受影响的父节点状态
      affectedParents.forEach(parentKey => {
        const parentRow = this.rows.find(r => r.key === parentKey)
        if (parentRow && parentRow._children) {
          // 检查所有子节点是否都被选中
          const allChildrenChecked = parentRow._children.every(
            child => this.status[child.key].checked || this.status[child.key].disabled
          )

          // 检查是否有子节点被选中
          const someChildrenChecked = parentRow._children.some(
            child => this.status[child.key].checked || this.status[child.key].indeterminate
          )

          // 更新父节点状态
          this.status[parentKey].checked = allChildrenChecked
          this.status[parentKey].indeterminate = !allChildrenChecked && someChildrenChecked
        }
      })

      const parentUpdateEndTime = performance.now()
      const parentUpdateTime = parentUpdateEndTime - parentUpdateStartTime

      // 3. 一次性更新UI和状态
      const stateUpdateStartTime = performance.now()
      let [checkedKey, checkedRow] = this.getCheckedRowAndCheckedKey()
      this.checkedRow = checkedRow
      const stateUpdateEndTime = performance.now()
      const stateUpdateTime = stateUpdateEndTime - stateUpdateStartTime

      // 4. 触发事件通知
      const eventStartTime = performance.now()
      this.$emit('checked-change', checkedKey)
      this.$emit('select-leaf-count-change', this.selectLeafCount)
      this.$emit('total-size-change', this.totalSize)
      const eventEndTime = performance.now()
      const eventTime = eventEndTime - eventStartTime

      const totalTime = performance.now() - startTime

      console.log('✅ [batchCheck] 批量选中操作完成', {
        总执行时间: `${totalTime.toFixed(2)}ms`,
        性能分析: {
          状态更新时间: `${updateTime.toFixed(2)}ms`,
          父节点更新时间: `${parentUpdateTime.toFixed(2)}ms`,
          状态计算时间: `${stateUpdateTime.toFixed(2)}ms`,
          触发事件时间: `${eventTime.toFixed(2)}ms`,
        },
        更新统计: {
          请求更新数: updates.length,
          实际更新数: actualUpdates,
          跳过更新数: skippedUpdates,
          更新效率: `${((actualUpdates / updates.length) * 100).toFixed(1)}%`,
          受影响父节点数: affectedParents.size,
        },
        最终状态: {
          选中文件数: this.selectLeafCount,
          总文件数: this.totalLeafCount,
        },
      })

      // 性能警告：如果执行时间过长，给出提示
      if (totalTime > 100) {
        console.warn('⚠️ [batchCheck] 执行时间较长，建议优化:', {
          执行时间: `${totalTime.toFixed(2)}ms`,
          建议: '考虑减少批量更新数量或优化数据结构',
        })
      }

      return totalTime
    },
  },

  mounted() {
    this.onDataChange()
    console.log('sortedRows', this.sortedRows, 'props', this.props)
  },

  beforeUnmount() {
    // 组件卸载时的清理工作
  },
}
</script>

<style lang="scss" scoped>
@import '@root/common/assets/css/mixins.scss';
@import './table.scss';
</style>
