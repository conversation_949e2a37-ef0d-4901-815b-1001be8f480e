<script setup lang="ts">
import { ref, defineExpose } from 'vue'
import BasicSettings from './basicSettings.vue'
import CloudSettings from './cloudSettings.vue'
import ConnectSetting from './connectSettings.vue'
import DownloadSetting from './downloadSettings.vue'
import TaskSetting from './taskSettings.vue'
import RemindSetting from './remindSettings.vue'
import AdvancedSetting from './advancedSettings.vue'

const contentRef = ref<HTMLElement | null>(null)
defineExpose({ contentRef })
</script>

<template>
  <div class="settings-content" ref="contentRef">
    <section id="basic-settings">
      <div class="settings-content-header">基本设置</div>
      <div class="settings-content-body">
        <BasicSettings />
      </div>
    </section>
    <section id="cloud-settings">
      <div class="settings-content-header">云盘设置</div>
      <div class="settings-content-body">
        <CloudSettings />
      </div>
    </section>
    <section id="connect-settings">
      <div class="settings-content-header">接管设置</div>
      <div class="settings-content-body">
        <ConnectSetting />
      </div>
    </section>
    <section id="download-settings">
      <div class="settings-content-header">下载设置</div>
      <div class="settings-content-body">
        <DownloadSetting />
      </div>
    </section>
    <section id="task-settings">
      <div class="settings-content-header">任务管理</div>
      <div class="settings-content-body">
        <TaskSetting />
      </div>
    </section>
    <section id="remind-settings">
      <div class="settings-content-header">提醒</div>
      <div class="settings-content-body">
        <RemindSetting />
      </div>
    </section>
    <section id="advanced-settings">
      <div class="settings-content-header">高级设置</div>
      <div class="settings-content-body">
        <AdvancedSetting />
      </div>
    </section>

  </div>
</template>

<style scoped lang="scss">
.settings-content {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  user-select: none;

  &-header {
    color: var(--font-font-1, #272E3B);
    font-size: 18px;
    font-weight: 700;
    line-height: 24px;
    margin: 8px 0;
  }

  &-body {
    padding: 8px 0;
  }
}
</style>

<style lang="scss">
.settings-content-divider {
  width: 100%;
  height: 1px;
  background-color: var(--border-border-3, #F2F3F5);
  margin: 16px 0;
}

.settings-content-title {
  display: flex;
  height: 32px;
  align-items: center;
  color: var(--font-font-3, #86909C);
  font-size: 13px;
  line-height: 22px;
}
</style>
