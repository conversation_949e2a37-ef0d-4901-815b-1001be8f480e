{"version": 3, "file": "index.js", "sources": ["webpack://player-plugin/../common/env.ts", "webpack://player-plugin/../common/logger.ts", "webpack://player-plugin/../common/renderer-async-remote-call.ts", "webpack://player-plugin/./entrys/plugin/index.ts"], "sourcesContent": ["import dotenv from 'dotenv';\r\nimport path from 'path';\r\n\r\ndotenv.config({ path: path.resolve(__dirname, '../../.env') });\r\n\r\nexport const isDev = process.env.NODE_ENV === 'development';\r\n\r\nexport const port = Number(process.env.PORT) || 9527;\r\n\r\nexport const platform = {\r\n  isWindows: process.platform === \"win32\",\r\n  isMacOS: process.platform === \"darwin\",\r\n  isLinux: process.platform === \"linux\"\r\n};\r\n\r\nexport const isShowLogger = true // !!process.env.PUBLIC_LOGGER\r\n\r\nexport type TEnv = 'prod' | 'test'\r\nconst {THUNDER_ENV} = process.env;\r\nexport const env: TEnv = !!THUNDER_ENV ? THUNDER_ENV as TEnv : 'prod'\r\nconsole.log('======================', env, THUNDER_ENV, process.env )\r\n// 内网环境一键配置\r\n// export const env: TEnv = 'test'\r\n\r\nexport const isProcess = typeof window === 'undefined'\r\nexport const isRenderer = !isProcess ", "import { isShow<PERSON>ogger } from \"./env\"\r\n\r\nfunction timeFormat(fmt: string, date: Date) {\r\n  let fmtRes: string = fmt\r\n\r\n  let ret\r\n  const opt: { [prop: string]: string } = {\r\n    'Y+': date.getFullYear().toString(), // 年\r\n    'm+': (date.getMonth() + 1).toString(), // 月\r\n    'd+': date.getDate().toString(), // 日\r\n    'H+': date.getHours().toString(), // 时\r\n    'M+': date.getMinutes().toString(), // 分\r\n    'S+': date.getSeconds().toString(), // 秒\r\n    's+': date.getMilliseconds().toString(), // 毫秒\r\n    // 有其他格式化字符需求可以继续添加，必须转化成字符串\r\n  }\r\n  for (const k in opt) {\r\n    ret = new RegExp('(' + k + ')').exec(fmtRes)\r\n    if (ret) {\r\n      fmtRes = fmtRes.replace(\r\n        ret[1],\r\n        ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'),\r\n      )\r\n    }\r\n  }\r\n  return fmtRes\r\n}\r\n\r\ntype FunctionVariadic = (...args: any[]) => void\r\n\r\nexport enum TAG_COLOR {\r\n  green = '#34C724',\r\n  blue = '#0969EB',\r\n  red = '#F56C6C',\r\n}\r\n\r\ninterface LogOptions {\r\n  tag: string\r\n  tagColor?: string\r\n  timeFmt?: string\r\n  saveToIndexDB?: boolean\r\n}\r\n\r\nexport class Logger {\r\n  debug: FunctionVariadic\r\n  log: FunctionVariadic\r\n  trace: FunctionVariadic\r\n  warn: FunctionVariadic\r\n  error: FunctionVariadic\r\n\r\n  tagColor: string\r\n  tag: string\r\n  timeFmt?: string\r\n\r\n  constructor(options: LogOptions) {\r\n    this.debug = this._log.bind(this, 'debug')\r\n    this.log = this._log.bind(this, 'info')\r\n    this.trace = this._log.bind(this, 'trace')\r\n    this.warn = this._log.bind(this, 'warn')\r\n    this.error = this._log.bind(this, 'error')\r\n\r\n    this.tag = options.tag\r\n    this.tagColor = options.tagColor || TAG_COLOR.blue\r\n    this.timeFmt = options.timeFmt || 'YYYY-mm-dd HH:MM:SS.sss'\r\n  }\r\n\r\n  private _log(\r\n    method: 'log' | 'debug' | 'info' | 'trace' | 'warn' | 'error',\r\n    ...data: any[]\r\n  ) {\r\n    if (isShowLogger) {\r\n      const now = new Date()\r\n      const timeStr = this.timeFmt ? timeFormat(this.timeFmt, now) : ''\r\n      console[method](\r\n        ...([] as any[])\r\n          .concat(`[${timeStr}] %c[${this.tag}]`, `color: ${this.tagColor}`)\r\n          .concat(data),\r\n      )\r\n    }\r\n  }\r\n}\r\n\r\nexport const debuggerLogger = new Logger({\r\n  tag: 'DEBUGGER',\r\n  tagColor: TAG_COLOR.green,\r\n})\r\n\r\nexport const win7Logger = new Logger({ tag: 'win7' })", "/**\r\n * @description 用于渲染进程调用后台进程相关信息的接口\r\n */\r\n\r\nimport { EventEmitter } from 'events';\r\nimport { Logger } from '@root/common/logger';\r\nimport * as asyncRemote from '@xunlei/async-remote';\r\n\r\nconst logger = new Logger({ tag: 'async-remote-call' })\r\n\r\nexport type RPCShell = { openItem: (path: string) => void };\r\nexport type RPCMenu = { buildFromTemplate: (...args: any[]) => Promise<any>; setStyle: (...args: any[]) => void };\r\nexport type RPCDialog = { showOpenDialog: (...args: any[]) => Promise<any> };\r\nexport type RPCApp = { getName: () => Promise<string>; getVersion: () => Promise<string> };\r\nexport type RPCProcess = { argv: string[] };\r\nexport class AsyncRemoteCall extends EventEmitter {\r\n  private static instance: AsyncRemoteCall;\r\n  /**\r\n   * @description 渲染进程用到的\r\n   */\r\n  constructor() {\r\n    super();\r\n    this.appName = undefined;\r\n    this.appVersion = undefined;\r\n\r\n    if (process.type !== 'renderer') {\r\n      logger.warn('can not import \"renderer-process-call\" module in non-renderer process', process.type);\r\n    }\r\n  }\r\n\r\n  public static GetInstance(): AsyncRemoteCall {\r\n    if (!AsyncRemoteCall.instance) {\r\n      if (global.RendererAsyncRemoteCallInstance) {\r\n        AsyncRemoteCall.instance = global.RendererAsyncRemoteCallInstance;\r\n      } else {\r\n        AsyncRemoteCall.instance = new AsyncRemoteCall();\r\n        global.RendererAsyncRemoteCallInstance = AsyncRemoteCall.instance;\r\n      }\r\n    }\r\n    return AsyncRemoteCall.instance;\r\n  }\r\n  public async getAppName(): Promise<string> {\r\n    if (undefined === this.appName) {\r\n      const appObj: RPCApp = await this.getApp();\r\n      this.appName = await appObj.getName();\r\n    }\r\n    return this.appName;\r\n  }\r\n\r\n  public async getAppVersion(): Promise<string> {\r\n    if (undefined === this.appVersion) {\r\n      const appObj: RPCApp = await this.getApp();\r\n      this.appVersion = await appObj.getVersion();\r\n    }\r\n    return this.appVersion;\r\n  }\r\n\r\n  /**\r\n   * @description Need Update Everytime\r\n   */\r\n\r\n  public async getProcess(): Promise<RPCProcess> {\r\n    return asyncRemote.global.process.__resolve();\r\n  }\r\n\r\n  public async getIpcMain(): Promise<any> {\r\n    return this.getCurrentObject('ipcMain');\r\n  }\r\n\r\n  public async getDialog(): Promise<RPCDialog> {\r\n    return this.getCurrentObject('dialog');\r\n  }\r\n\r\n  public async getApp(): Promise<RPCApp> {\r\n    return this.getCurrentObject('app');\r\n  }\r\n\r\n  public async getShell(): Promise<RPCShell> {\r\n    return this.getCurrentObject('shell');\r\n  }\r\n\r\n  public async getMenu(): Promise<RPCMenu> {\r\n    return this.getCurrentObject('Menu');\r\n  }\r\n\r\n  public async getScreen(): Promise<any> {\r\n    return this.getCurrentObject('screen');\r\n  }\r\n\r\n  public async getBrowserWindow(): Promise<any> {\r\n    return this.getCurrentObject('BrowserWindow');\r\n  }\r\n\r\n  public async getWebContents(): Promise<any> {\r\n    return this.getCurrentObject('webContents');\r\n  }\r\n\r\n  public async getGlobalShortcut(): Promise<any> {\r\n    return this.getCurrentObject('globalShortcut');\r\n  }\r\n\r\n  public async getCurrentWebContents(): Promise<any> {\r\n    let currentWebContents: any = this.mapObj.get('currentWebContents');\r\n    if (undefined === currentWebContents) {\r\n      if (this.mapObjIniting.get('currentWebContents')) {\r\n        currentWebContents = await new Promise<void>(\r\n          async (resolve: (currentWebContents: any) => void): Promise<void> => {\r\n            this.on('OnInitCurrentWebContents', (currentWebContents: any) => {\r\n              resolve(currentWebContents);\r\n            });\r\n          }\r\n        );\r\n      } else {\r\n        this.mapObjIniting.set('currentWebContents', true);\r\n        currentWebContents = await asyncRemote.getCurrentWebContents().__resolve();\r\n        this.mapObjIniting.set('currentWebContents', false);\r\n        this.emit('OnInitCurrentWebContents', currentWebContents);\r\n        const callbacks: Function[] = this.listeners('OnInitCurrentWebContents');\r\n        callbacks.forEach((callback: Function) => {\r\n          this.removeListener('OnInitCurrentWebContents', callback as (...args: any[]) => void);\r\n        });\r\n      }\r\n      this.mapObj.set('currentWebContents', currentWebContents);\r\n    }\r\n\r\n    return currentWebContents;\r\n  }\r\n\r\n  public async getCurrentWindow(): Promise<any> {\r\n    let currentWindow: any = this.mapObj.get('currentWindow');\r\n    if (undefined === currentWindow) {\r\n      if (this.mapObjIniting.get('currentWindow')) {\r\n        currentWindow = await new Promise<void>(\r\n          async (resolve: (currentWindow: any) => void): Promise<void> => {\r\n            this.on('OnInitCurrentWindow', (currentWindow: any) => {\r\n              resolve(currentWindow);\r\n            });\r\n          }\r\n        );\r\n      } else {\r\n        this.mapObjIniting.set('currentWindow', true);\r\n        currentWindow = await asyncRemote.getCurrentWindow().__resolve();\r\n        this.mapObjIniting.set('currentWindow', false);\r\n        this.emit('OnInitCurrentWindow', currentWindow);\r\n        const callbacks: Function[] = this.listeners('OnInitCurrentWindow');\r\n        callbacks.forEach((callback: Function) => {\r\n          this.removeListener('OnInitCurrentWindow', callback as (...args: any[]) => void);\r\n        });\r\n      }\r\n      this.mapObj.set('currentWindow', currentWindow);\r\n    }\r\n\r\n    return currentWindow;\r\n  }\r\n\r\n  public async getCurrentObject(id: string): Promise<any> {\r\n    let obj: any = this.mapObj.get(id);\r\n    if (obj === null || obj === undefined) {\r\n      if (this.mapObjIniting.get(id)) {\r\n        obj = await new Promise<void>(\r\n          async (resolve: (currentWebContents: any) => void): Promise<void> => {\r\n            this.on(id, (currentWebContents: any) => {\r\n              resolve(currentWebContents);\r\n            });\r\n          }\r\n        );\r\n      } else {\r\n        this.mapObjIniting.set(id, true);\r\n        obj = await asyncRemote.electron[id].__resolve();\r\n        this.mapObjIniting.set(id, false);\r\n        this.emit(id, obj);\r\n        const callbacks: Function[] = this.listeners(id);\r\n        callbacks.forEach((callback: Function) => {\r\n          this.removeListener(id, callback as (...args: any[]) => void);\r\n        });\r\n      }\r\n      this.mapObj.set(id, obj);\r\n    }\r\n\r\n    return obj;\r\n  }\r\n\r\n  private mapObj: Map<string, any> = new Map();\r\n  private mapObjIniting: Map<string, boolean> = new Map();\r\n\r\n  private appName: string | undefined;\r\n  private appVersion: string | undefined;\r\n}\r\n\r\n// export const asyncRemoteCall: AsyncRemoteCall = new AsyncRemoteCall();\r\n\r\n\r\n// export class AsyncRemoteCall {\r\n//   private static instance: AsyncRemoteCall;\r\n\r\n//   public static GetInstance(): AsyncRemoteCall {\r\n//     if (!AsyncRemoteCall.instance) {\r\n//       if (global.RendererAsyncRemoteCallInstance) {\r\n//         AsyncRemoteCall.instance = global.RendererAsyncRemoteCallInstance;\r\n//       } else {\r\n//         AsyncRemoteCall.instance = new AsyncRemoteCall();\r\n//         global.RendererAsyncRemoteCallInstance = AsyncRemoteCall.instance;\r\n//       }\r\n//     }\r\n//     return AsyncRemoteCall.instance;\r\n//   }\r\n\r\n//   test() {\r\n//     console.log('AsyncRemoteCall test');\r\n//   }\r\n// }", "console.log('00000000')\r\nimport { AsyncRemoteCall } from '@root/common/renderer-async-remote-call';\r\n\r\nconsole.log('lllll')\r\n//import { GetLogsPath, GetPlayerControlAddonNodeName, GetProfilesPath, GetSdkPath, GetXxxNodePath } from '@root/common/xxx-node-path';\r\nconsole.log('222222222222')\r\nconst wndMinWidth: number = 960\r\nconst wndMinHeight: number = 640\r\nconst wndWidth: number = 1080\r\nconst wndHeight: number = 675\r\nasync function CreatePlayer() {\r\n  const BrowserWindow: any = await AsyncRemoteCall.GetInstance().getBrowserWindow(); // tslint:disable-line\r\n  // let playerParentWnd = await new BrowserWindow({\r\n  //   width: wndWidth,\r\n  //   height: wndHeight,\r\n  //   minWidth: wndMinWidth,\r\n  //   minHeight: wndMinHeight,\r\n  //   show: true,\r\n  //   frame: false,\r\n  //   autoHideMenuBar: true,\r\n  //   webPreferences: {\r\n  //     nodeIntegration: true,\r\n  //     devTools: true,\r\n  //     webviewTag: true,\r\n  //     contextIsolation: false,\r\n  //     nodeIntegrationInSubFrames: true,\r\n  //     backgroundThrottling: false,\r\n  //   },\r\n  // });\r\n  let playerControlWnd = await new BrowserWindow({\r\n    width: wndWidth,\r\n    height: wndHeight,\r\n    minWidth: wndMinWidth,\r\n    minHeight: wndMinHeight,\r\n    show: true,\r\n    frame: false,\r\n    closable: true,\r\n    //parent: playerParentWnd ? playerParentWnd : undefined,\r\n    transparent: true,\r\n    autoHideMenuBar: true,\r\n    webPreferences: {\r\n      nodeIntegration: true,\r\n      devTools: true,\r\n      webviewTag: true,\r\n      contextIsolation: false,\r\n      nodeIntegrationInSubFrames: true,\r\n      backgroundThrottling: false,\r\n    },\r\n  });\r\n\r\n  //console.log(`file://${path.resolve(__dirname, 'renderer.html')}`)\r\n  await playerControlWnd.loadURL('https://www.baidu.com')\r\n\r\n  // let hwndBuf: Buffer = playerParentWnd.getNativeWindowHandle()\r\n  // let handle = hwndBuf.readBigUInt64LE(0)\r\n\r\n  // let playercontrol = requireNodeFile(\r\n  //   path.join(GetXxxNodePath(), GetPlayerControlAddonNodeName()),\r\n  // )\r\n  // let playerInitParam: any = {\r\n  //   xmp: false,\r\n  //   openPlayerLog: false,\r\n  //   logDir: GetLogsPath(),\r\n  //   codecPath: '',\r\n  //   subtitleCachePath: path.join(GetProfilesPath(), 'subtitle'),\r\n  //   peerid: '1111111111',\r\n  //   version: '',\r\n  //   versionCode: 10,\r\n  //   configPath: path.join(GetProfilesPath(), 'player_config.json'),\r\n  //   dbDir: GetProfilesPath(),\r\n  //   dbName: 'player_record.db',\r\n  //   panPlayCache: path.join(GetProfilesPath(), 'yun_play'),\r\n  //   downloadServerDir: GetSdkPath()\r\n  // }\r\n  // playercontrol.initAddon(playerInitParam)\r\n  // setTimeout(() => {\r\n  //   playercontrol.setWnd(handle)\r\n  // }, 5000)\r\n}\r\n\r\nCreatePlayer();\r\n\r\n\r\n// AsyncRemoteCall.GetInstance().test();"], "names": ["dotenv", "path", "__dirname", "Number", "process", "THUNDER_ENV", "console", "<PERSON><PERSON>", "method", "_key", "data", "now", "Date", "timeStr", "timeFormat", "fmt", "date", "ret", "fmtRes", "opt", "k", "RegExp", "options", "logger", "AsyncRemoteCall", "EventEmitter", "global", "undefined", "appObj", "asyncRemote", "currentWebContents", "Promise", "resolve", "callbacks", "callback", "currentWindow", "id", "obj", "Map", "CreatePlayer", "BrowserWindow", "playerControlWnd"], "mappings": "oGAGAA,EAAAA,MAAa,CAAC,CAAE,KAAMC,IAAAA,OAAY,CAACC,UAAW,aAAc,GAIxCC,AAAOC,QAAQ,GAAG,CAAC,IAAK,CAG/BA,QAAQ,QAAQ,CAClBA,QAAQ,QAAQ,CAChBA,QAAQ,QAAQ,CAM3B,GAAM,CAACC,YAAAA,CAAW,CAAC,CAAGD,QAAQ,GAAG,CAEjCE,QAAQ,GAAG,CAAC,yBADa,AAAED,GAAoC,OACpBA,EAAaD,QAAQ,GAAG,CCuB5D,OAAMG,EAuBH,KACNC,CAA6D,CAE7D,CADAC,IAAAA,IAAAA,EAAAA,UAAAA,MAAAA,CAAGC,EAAHD,AAAAA,MAAAA,EAAAA,EAAAA,EAAAA,EAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAGC,CAAI,CAAPD,EAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAc,AAEI,EAChB,IAAME,EAAM,IAAIC,KACVC,EAAU,IAAI,CAAC,OAAO,CAAGC,AAtErC,SAAoBC,CAAW,CAAEC,CAAU,EACzC,IAEIC,EAFAC,EAAiBH,EAGfI,EAAkC,CACtC,KAAMH,EAAK,WAAW,GAAG,QAAQ,GACjC,KAAOA,AAAAA,CAAAA,EAAK,QAAQ,GAAK,GAAG,QAAQ,GACpC,KAAMA,EAAK,OAAO,GAAG,QAAQ,GAC7B,KAAMA,EAAK,QAAQ,GAAG,QAAQ,GAC9B,KAAMA,EAAK,UAAU,GAAG,QAAQ,GAChC,KAAMA,EAAK,UAAU,GAAG,QAAQ,GAChC,KAAMA,EAAK,eAAe,GAAG,QAAQ,EAEvC,EACA,IAAK,IAAMI,KAAKD,EACdF,CAAAA,EAAM,AAAII,OAAO,IAAMD,EAAI,KAAK,IAAI,CAACF,EAAM,GAEzCA,CAAAA,EAASA,EAAO,OAAO,CACrBD,CAAG,CAAC,EAAE,CACNA,AAAkB,IAAlBA,CAAG,CAAC,EAAE,CAAC,MAAM,CAASE,CAAG,CAACC,EAAE,CAAGD,CAAG,CAACC,EAAE,CAAC,QAAQ,CAACH,CAAG,CAAC,EAAE,CAAC,MAAM,CAAE,KAAI,EAIxE,OAAOC,CACT,EA8CgD,IAAI,CAAC,OAAO,CAAEP,GAAO,GAC/DL,OAAO,CAACE,EAAO,IACT,EAAE,CACH,MAAM,CAAC,CAAC,CAAC,EAAEK,EAAQ,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAChE,MAAM,CAACH,GAEd,CACF,CAzBA,YAAYY,CAAmB,CAAE,CAVjC,6BACA,2BACA,6BACA,4BACA,6BAEA,gCACA,2BACA,+BAGE,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAE,SAClC,IAAI,CAAC,GAAG,CAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAE,QAChC,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAE,SAClC,IAAI,CAAC,IAAI,CAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAE,QACjC,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAE,SAElC,IAAI,CAAC,GAAG,CAAGA,EAAQ,GAAG,CACtB,IAAI,CAAC,QAAQ,CAAGA,EAAQ,QAAQ,EAAI,UACpC,IAAI,CAAC,OAAO,CAAGA,EAAQ,OAAO,EAAI,yBACpC,CAgBF,CAE8B,IAAIf,EAAO,CACvC,IAAK,WACL,SAAU,SACZ,GAE0B,IAAIA,EAAO,CAAE,IAAK,MAAO,G,cC/EnD,IAAMgB,EAAS,IAAIhB,EAAO,CAAE,IAAK,mBAAoB,EAO9C,OAAMiB,UAAwBC,EAAAA,YAAYA,CAe/C,OAAc,aAA+B,CAS3C,OARKD,EAAgB,QAAQ,GACvBE,OAAO,+BAA+B,CACxCF,EAAgB,QAAQ,CAAGE,OAAO,+BAA+B,EAEjEF,EAAgB,QAAQ,CAAG,IAAIA,EAC/BE,OAAO,+BAA+B,CAAGF,EAAgB,QAAQ,GAG9DA,EAAgB,QAAQ,AACjC,CACA,MAAa,YAA8B,CACzC,GAAIG,KAAAA,IAAc,IAAI,CAAC,OAAO,CAAE,CAC9B,IAAMC,EAAiB,MAAM,IAAI,CAAC,MAAM,EACxC,KAAI,CAAC,OAAO,CAAG,MAAMA,EAAO,OAAO,EACrC,CACA,OAAO,IAAI,CAAC,OAAO,AACrB,CAEA,MAAa,eAAiC,CAC5C,GAAID,KAAAA,IAAc,IAAI,CAAC,UAAU,CAAE,CACjC,IAAMC,EAAiB,MAAM,IAAI,CAAC,MAAM,EACxC,KAAI,CAAC,UAAU,CAAG,MAAMA,EAAO,UAAU,EAC3C,CACA,OAAO,IAAI,CAAC,UAAU,AACxB,CAMA,MAAa,YAAkC,CAC7C,OAAOC,EAAAA,MAAAA,CAAAA,OAAAA,CAAAA,SAAoC,EAC7C,CAEA,MAAa,YAA2B,CACtC,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAC/B,CAEA,MAAa,WAAgC,CAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAC/B,CAEA,MAAa,QAA0B,CACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAC/B,CAEA,MAAa,UAA8B,CACzC,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAC/B,CAEA,MAAa,SAA4B,CACvC,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAC/B,CAEA,MAAa,WAA0B,CACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAC/B,CAEA,MAAa,kBAAiC,CAC5C,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAC/B,CAEA,MAAa,gBAA+B,CAC1C,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAC/B,CAEA,MAAa,mBAAkC,CAC7C,OAAO,IAAI,CAAC,gBAAgB,CAAC,iBAC/B,CAEA,MAAa,uBAAsC,CACjD,IAAIC,EAA0B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAuB9C,OAtBIH,KAAAA,IAAcG,IACZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,sBACzBA,EAAqB,MAAM,IAAIC,QAC7B,MAAOC,IACL,IAAI,CAAC,EAAE,CAAC,2BAA4B,AAACF,IACnCE,EAAQF,EACV,EACF,IAGF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,qBAAsB,IAC7CA,EAAqB,MAAMD,EAAAA,qBAAiC,GAAG,SAAS,GACxE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,qBAAsB,IAC7C,IAAI,CAAC,IAAI,CAAC,2BAA4BC,GAEtCG,AAD8B,IAAI,CAAC,SAAS,CAAC,4BACnC,OAAO,CAAC,AAACC,IACjB,IAAI,CAAC,cAAc,CAAC,2BAA4BA,EAClD,IAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAsBJ,IAGjCA,CACT,CAEA,MAAa,kBAAiC,CAC5C,IAAIK,EAAqB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAuBzC,OAtBIR,KAAAA,IAAcQ,IACZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,iBACzBA,EAAgB,MAAM,IAAIJ,QACxB,MAAOC,IACL,IAAI,CAAC,EAAE,CAAC,sBAAuB,AAACG,IAC9BH,EAAQG,EACV,EACF,IAGF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAiB,IACxCA,EAAgB,MAAMN,EAAAA,gBAA4B,GAAG,SAAS,GAC9D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAiB,IACxC,IAAI,CAAC,IAAI,CAAC,sBAAuBM,GAEjCF,AAD8B,IAAI,CAAC,SAAS,CAAC,uBACnC,OAAO,CAAC,AAACC,IACjB,IAAI,CAAC,cAAc,CAAC,sBAAuBA,EAC7C,IAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAiBC,IAG5BA,CACT,CAEA,MAAa,iBAAiBC,CAAU,CAAgB,CACtD,IAAIC,EAAW,IAAI,CAAC,MAAM,CAAC,GAAG,CAACD,GAuB/B,aAtBIC,IACE,IAAI,CAAC,aAAa,CAAC,GAAG,CAACD,GACzBC,EAAM,MAAM,IAAIN,QACd,MAAOC,IACL,IAAI,CAAC,EAAE,CAACI,EAAI,AAACN,IACXE,EAAQF,EACV,EACF,IAGF,IAAI,CAAC,aAAa,CAAC,GAAG,CAACM,EAAI,IAC3BC,EAAM,MAAMR,EAAAA,QAAoB,CAACO,EAAG,CAAC,SAAS,GAC9C,IAAI,CAAC,aAAa,CAAC,GAAG,CAACA,EAAI,IAC3B,IAAI,CAAC,IAAI,CAACA,EAAIC,GAEdJ,AAD8B,IAAI,CAAC,SAAS,CAACG,GACnC,OAAO,CAAC,AAACF,IACjB,IAAI,CAAC,cAAc,CAACE,EAAIF,EAC1B,IAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAACE,EAAIC,IAGfA,CACT,CAhKA,aAAc,CACZ,KAAK,GAiKP,aAAQ,SAA2B,IAAIC,KACvC,aAAQ,gBAAsC,IAAIA,KAElD,aAAQ,UAAR,QACA,aAAQ,aAAR,QApKE,IAAI,CAAC,OAAO,CAAGX,KAAAA,EACf,IAAI,CAAC,UAAU,CAAGA,KAAAA,EAEG,aAAjBvB,QAAQ,IAAI,EACdmB,EAAO,IAAI,CAAC,wEAAyEnB,QAAQ,IAAI,CAErG,CA+JF,CA3KE,QADWoB,EACI,WAAf,QChBFlB,QAAQ,GAAG,CAAC,YAGZA,QAAQ,GAAG,CAAC,SAEZA,QAAQ,GAAG,CAAC,iBA2EZiC,AAtEA,iBACE,IAAMC,EAAqB,MAAMhB,EAAgB,WAAW,GAAG,gBAAgB,GAkB3EiB,EAAmB,MAAM,IAAID,EAAc,CAC7C,MAtBqB,KAuBrB,OAtBsB,IAuBtB,SA1BwB,IA2BxB,UA1ByB,IA2BzB,KAAM,GACN,MAAO,GACP,SAAU,GAEV,YAAa,GACb,gBAAiB,GACjB,eAAgB,CACd,gBAAiB,GACjB,SAAU,GACV,WAAY,GACZ,iBAAkB,GAClB,2BAA4B,GAC5B,qBAAsB,EACxB,CACF,EAGA,OAAMC,EAAiB,OAAO,CAAC,wBA2BjC,G"}