<script lang="ts" setup>
defineOptions({
  name: 'PanTooltips'
})

const props = withDefaults(defineProps<{
  visible: boolean
  content: string
}>(), {
  visible: false,
  content: '',
})
</script>

<template>
  <div class="tooltips">
    <div v-show="visible" class="tooltips-content">
      <svg width="16" height="6" viewBox="0 0 16 6" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.47259 5.29219L3.68896 2.0043C2.5622 0.67435 1.33255 0.00937464 0 0.00937464C0.98699 -0.0117183 14.9975 0.00937464 16 0.00937464C14.6678 0.00937464 13.4371 0.675582 12.3079 2.008L9.52741 5.29219C8.81434 6.13444 7.55244 6.24006 6.70887 5.52811C6.62366 5.45619 6.54462 5.37728 6.47259 5.29219Z" fill="black" fill-opacity="0.8"/>
      </svg>
      <span>{{ content }}</span>
    </div>

    <slot />
  </div>
</template>

<style lang="scss" scoped>
.tooltips {
  position: relative;

  .tooltips-content {
    position: absolute;
    top: -40px;
    background-color: var(--background-background-tooltip-black);
    border-radius: var(--border-radius-S);
    color: #fff;
    font-size: 12px;
    padding: 6px 12px;

    svg {
      position: absolute;
      bottom: -6px;
    }
  }
}
</style>