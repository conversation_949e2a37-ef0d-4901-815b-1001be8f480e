<script setup lang="ts">
import HomeHeader from './views/home/<USER>'
import HomeContainer from './views/home/<USER>'
import SharePage from './views/share/index.vue'

import { onMounted, ref, useTemplateRef, watch } from 'vue'
import { EPanPage, useHistoryStore } from './store/history-store'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { EMainRenderUIHelperEventKey, MainRenderUIHelper } from '@root/common/main-renderer-ui-helper'
import { UserHelper } from './utils/user-helper'
import { RetrievalTaskHelper } from './utils/retrieval-task-helper'
import { GlobalEventHelper } from './utils/global-event-helper'
import { ETabId, TabsManager } from './manager/tabs-manager'
import { PanBusinessHelper } from './utils/business-helper'
import { RecentSaveFolderHelper } from './utils/recent-save-folder-helper'
import { BaseManager } from './manager/base-manager'
import { MessageCenterManager } from './manager/message-center-manager'
import { CloudAddManager } from './manager/cloud-add-manager'
import { SharePageManager } from './manager/share-page-manager'
import { DriveFileManager } from './manager/drive-file-manager'
import { TransferFileManager } from './manager/transfer-file-manager'
import { useCloudAddRouterStore } from './store/cloud-add-router-store'
import { useDriveRouterStore } from './store/drive-router-store'
import { useTransferRouterStore } from './store/transfer-router-store'
import { useUserStore } from './store/user-store'
import DragSelect from '@root/common/components/utils/drag-select'
import { sleep } from '@root/common/thunder-pan-manager/pan-sdk/utils/basic'
import { useElementVisibility } from '@vueuse/core'
import { FilterManager } from './manager/filter-manager'
import { closeAllContextmenu } from '@root/common/components/ui/contextmenu'
import { closeAllConfirmDialog } from './components/delete-confirm-dialog'
import { closeAllShareDialog } from './components/share-dialog'
import { closeAllSafeBoxDialog } from './components/safe-box-dialog'
import { ThunderPanDataBase } from './db'

const { isSignin } = useUserStore()
const { resetRouterList: resetDriveRouterList } = useDriveRouterStore()
const { resetRouterList: resetCloudAddRouterList } = useCloudAddRouterStore()
const { resetRouterList: resetTransferFileRouterList } = useTransferRouterStore()
const { currentHistoryPage, canGoBack, canGoForward, goBack, goForward, clear: clearHistory, } = useHistoryStore()

const stopClearPicked = ref(false)

const $rootElement = useTemplateRef<HTMLDivElement>('$rootElement')
const rootElementIsVisible = useElementVisibility($rootElement)

const dragSelectInstance = new DragSelect(
  '.drag-select__body',
  '.drag-select__item',
  '.drag-select__content',
  onDraggingSelect,
  onDragSelectEnd,
  {
    top: 40,        // 列表头部的高度，选择框的范围不包括该位置
  }
)

function onDraggingSelect () {
  stopClearPicked.value = true
}

async function onDragSelectEnd (selectArea: HTMLElement, picked?: HTMLElement[]) {
  if (!selectArea || !picked || !picked.length) return

  GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.DRAG_SELECT_END, selectArea, picked)
  await sleep(150)
  stopClearPicked.value = false
}

watch(canGoBack, () => {
  MainRenderUIHelper.getInstance().setHeaderStatusRemote({
    backward: {
      enable: canGoBack.value,
    }
  })
})

watch(canGoForward, () => {
  MainRenderUIHelper.getInstance().setHeaderStatusRemote({
    forward: {
      enable: canGoForward.value,
    }
  })
})

// 监听页面可见性，当页面变为可见时重新设置前进后退按钮状态
watch(rootElementIsVisible, (isVisible) => {
  if (isVisible) {
    MainRenderUIHelper.getInstance().setHeaderStatusRemote({
      backward: {
        enable: canGoBack.value,
      },
      forward: {
        enable: canGoForward.value,
      }
    })
  }
})

function handleInit () {
  BaseManager.getInstance().getDataInit()
  FilterManager.getInstance().init()
  MessageCenterManager.getInstance().init()
  RecentSaveFolderHelper.getInstance().reSyncUserPanSettingPath()
}

function handleSignout () {
  // 重置数据
  BaseManager.getInstance().reset()
  FilterManager.getInstance().reset()
  CloudAddManager.getInstance().reset()
  SharePageManager.getInstance().reset()
  DriveFileManager.getInstance().reset()
  TransferFileManager.getInstance().reset()
  MessageCenterManager.getInstance().reset()
  // 移除所有事件监听
  GlobalEventHelper.getInstance().removeAll()
  // 重置 tab 路由信息
  resetDriveRouterList()
  resetCloudAddRouterList()
  resetTransferFileRouterList()
  // 清空历史记录
  clearHistory()
  // 关闭弹窗
  closeAllShareDialog()
  closeAllContextmenu()
  closeAllConfirmDialog()
  closeAllSafeBoxDialog()
}

watch(isSignin, () => {
  if (isSignin.value) {
    handleInit()
  } else {
    handleSignout()
  }
})

onMounted(() => {
  client.attachServerEvent(EMainRenderUIHelperEventKey.BACKWARD, () => {
    if (rootElementIsVisible.value) {
      goBack()
    }
  })
  client.attachServerEvent(EMainRenderUIHelperEventKey.FORWARD, () => {
    if (rootElementIsVisible.value) {
      goForward()
    }
  })
  client.attachServerEvent(EMainRenderUIHelperEventKey.REFRESH, () => {
    if (!rootElementIsVisible.value || !isSignin.value) return

    // 当前打开了【我的分享】页面则仅刷新我的分享页面
    if (currentHistoryPage.value === EPanPage.SHARE) {
      GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.SHARE_LIST_REFRESH)
      return
    }

    BaseManager.getInstance().getBase()
    const currentTab = TabsManager.getInstance().getCurrentTab()
    switch (currentTab.id) {
      case ETabId.ALL: {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.DRIVE_LIST_REFRESH)
        break
      }
      case ETabId.CLOUD_ADD: {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.CLOUD_ADD_LIST_REFRESH)
        break
      }
      case ETabId.TRANSFER_FILE: {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.TRANSFER_LIST_REFRESH)
        break
      }
    }
  })

  UserHelper.initUserInfo()
  BaseManager.getInstance().init()
  PanBusinessHelper.getInstance().init()
  GlobalEventHelper.getInstance().init()
  RetrievalTaskHelper.getInstance().init()
  RecentSaveFolderHelper.getInstance().init()
  ThunderPanDataBase.getInstance().initLocalSql()
})
</script>

<template>
  <div ref="$rootElement" class="thunder-pan-app-container">
    <div class="home-wrapper" v-show="currentHistoryPage === EPanPage.HOME">
      <HomeHeader />
      <HomeContainer />
    </div>
    <SharePage v-show="currentHistoryPage === EPanPage.SHARE" />
  </div>
</template>

<style scoped lang="scss">
.thunder-pan-app-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .home-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
</style>

<style>
#thunder-pan {
  height: 100%;
}
</style>
