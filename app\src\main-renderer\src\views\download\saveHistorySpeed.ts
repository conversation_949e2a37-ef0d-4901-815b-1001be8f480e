import { ref } from 'vue'
import { TaskManager } from '@root/common/task/impl/task-manager';
import { IHistoryTaskSpeedInfo, IHistorySpeedInfo } from '@/types/taskDetails'
import { TaskStatus, TaskBase } from '@root/common/task/base'
import { calculateProgress, isNumber, isObject } from '@/utils'

import path from 'path';
import { GetProfilesPath } from '@root/common/xxx-node-path'
import { FileSystemAWNS } from '@root/common//fs-utilities';
import { Logger } from '@root/common/logger';
import { createGlobalState } from '@vueuse/core';

const logger = new Logger({ tag: 'config-modules' })

export const useSaveHistorySpeed = createGlobalState(() => {
  const taskSpeedMap = ref<Map<number, IHistoryTaskSpeedInfo>>(new Map())
  const speedPath = ref('')
  const taskDetailListenerId = ref(0)
  const taskStatusListenerId = ref(0)

  async function init () {
    unListenerTask()
    console.log('>>>>>>>>>>>>>>>>>>> 执行 init')
    let dataPath = GetProfilesPath();
    speedPath.value = path.join(dataPath, 'TaskSpeedInfo');
    const exist: boolean = await FileSystemAWNS.existsAW(speedPath.value);
    // console.log('>>>>>>>>>>>> speedPath.value', speedPath.value, exist)
    if (!exist) {
      await FileSystemAWNS.mkdirsAW(speedPath.value);
    }
    listenerTask()
  }

  function unListenerTask () {
    taskDetailListenerId.value && TaskManager.GetInstance().detachTaskDetailChangeEvent(taskDetailListenerId.value)
    taskStatusListenerId.value && TaskManager.GetInstance().detachTaskDetailChangeEvent(taskDetailListenerId.value)
  }

  function listenerTask () {
    taskDetailListenerId.value = TaskManager.GetInstance().attachTaskDetailChangeEvent(async (taskId: number, flags: number) => {
      // console.log('= 监听任务详情变化', taskId, flags)
      let task = await TaskManager.GetInstance().findTaskById(taskId);
      if (!task) return
      const taskBase = task.getTaskBase()
      onTaskDetailChanged(taskBase)
    });
    taskStatusListenerId.value = TaskManager.GetInstance().attachTaskStatusChangeEvent(async (taskId: number, eOld: TaskStatus, eNew: TaskStatus) => {
      // console.log('= 监听任务状态变化', taskId, eOld, eNew)
      onTaskStatusChanged(taskId)
    });
  }

  function newTaskSpeedInfo(taskId: number): IHistoryTaskSpeedInfo {
    const taskSpeedInfo: IHistoryTaskSpeedInfo = {};
    taskSpeedInfo.taskId = taskId;
    taskSpeedInfo.aveSpeed = 0;
    taskSpeedInfo.maxSpeed = 0;
    taskSpeedInfo.progress = 0;
    taskSpeedInfo.speedInfoMap = {};

    return taskSpeedInfo
  }

  async function saveTaskSpeedInfo(taskId: number): Promise<boolean> {
    let ret: boolean = false;
    const taskSpeedInfo: IHistoryTaskSpeedInfo | undefined = taskSpeedMap.value.get(taskId);
    // console.log('>>>>>>>>>>>>>>> taskSpeedInfo', taskSpeedInfo)
    if (taskSpeedInfo) {
      const jsonStr: string = JSON.stringify(taskSpeedInfo);
      const fileName: string = 'TaskInfoEx_' + taskId.toString() + '.txt';
      const filePath: string = path.join(speedPath.value, fileName);
      // console.log('>>>>>>>>>>>> filePath, jsonStr', {filePath, jsonStr})
      ret = await FileSystemAWNS.writeFileAW(filePath, jsonStr);
    }
    return ret;
  }

  async function updateProgress(
    taskId: number,
    aveSpeed: number,
    progress: number,
    downloadSpeed: number,
    addSpeed: number
  ): Promise<void> {
    let taskSpeedInfo: IHistoryTaskSpeedInfo|undefined = taskSpeedMap.value.get(taskId);
    if (!taskSpeedInfo) {
      await loadTaskSpeedInfo(taskId);
      taskSpeedInfo = taskSpeedMap.value.get(taskId) || newTaskSpeedInfo(taskId);
    }
    taskSpeedInfo.aveSpeed = aveSpeed;
    if (downloadSpeed > (taskSpeedInfo.maxSpeed || 0)) {
      taskSpeedInfo.maxSpeed = downloadSpeed;
    }
    taskSpeedInfo.progress = progress;

    // 获取会员加速曲线的颜色 0-默认颜色；1-白金颜色 2-超会颜色 undefined-不展示颜色
    const speedInfo: IHistorySpeedInfo = {
      progress: progress,
      downloadSpeed: downloadSpeed,
      addSpeed: addSpeed,
      vipSpeedColor: 0
    };

    const progressInt: number = Math.floor(progress);
    // 进度100%时只取第一次的数据
    if (progressInt === 100) {
      if (!(taskSpeedInfo.speedInfoMap && taskSpeedInfo.speedInfoMap[progressInt])) {
        taskSpeedInfo.speedInfoMap && (taskSpeedInfo.speedInfoMap[progressInt] = speedInfo)
        taskSpeedMap.value.set(taskId, taskSpeedInfo);
        await saveTaskSpeedInfo(taskId);
      }
    } else {
      taskSpeedInfo.speedInfoMap && (taskSpeedInfo.speedInfoMap[progressInt] = speedInfo)
      taskSpeedMap.value.set(taskId, taskSpeedInfo);
      await saveTaskSpeedInfo(taskId);
    }
  }

  async function loadTaskSpeedInfo(taskId: number): Promise<boolean> {
    let ret: boolean = false;
    const fileName: string = 'TaskInfoEx_' + taskId.toString() + '.txt';
    const filePath: string = path.join(speedPath.value, fileName);
    const exist: boolean = await FileSystemAWNS.existsAW(filePath);
    if (exist) {
      const buffer: Buffer = await FileSystemAWNS.readFileAW(filePath);
      if (buffer) {
        let taskSpeedInfo: IHistoryTaskSpeedInfo | null = null;
        try {
          taskSpeedInfo = JSON.parse(buffer.toString());
        } catch (e) {
          logger.log(e);
        }
        if (taskSpeedInfo !== null) {
          taskSpeedMap.value.set(taskId, taskSpeedInfo);
          ret = true;
        }
      }
    }
    return ret;
  }

  async function getTaskSpeedInfo(taskId: number): Promise<IHistoryTaskSpeedInfo | undefined> {
    let taskSpeedInfo: IHistoryTaskSpeedInfo | undefined = taskSpeedMap.value.get(taskId);
    if (!taskSpeedInfo) {
      // 加载历史数据
      await loadTaskSpeedInfo(taskId);
      taskSpeedInfo = taskSpeedMap.value.get(taskId);
    }
    if (taskSpeedInfo) {
      checkTaskSpeedInfo(taskId, taskSpeedInfo);
    }
    return taskSpeedInfo;
  }

  async function checkTaskSpeedInfo(taskId: number, taskSpeedInfo: IHistoryTaskSpeedInfo) {
    let needSave: boolean = false;
    let task = await TaskManager.GetInstance().findTaskById(taskId);
    if (!task) return
    const taskBase = task.getTaskBase()
    const taskStatus = taskBase.taskStatus;
    if (!isNumber(taskSpeedInfo.aveSpeed)) {
      taskSpeedInfo.aveSpeed = 0;
      needSave = true;
    }
    if (!isNumber(taskSpeedInfo.maxSpeed)) {
      taskSpeedInfo.maxSpeed = 0;
      needSave = true;
    }
    // 检查一下平均速度是否大于最大速度
    if ((taskSpeedInfo.aveSpeed || 0) > (taskSpeedInfo.maxSpeed || 0)) {
      logger.log('checkTaskSpeedInfo aveSpeed > maxSpeed', ', taskId: ', taskBase);
      taskSpeedInfo.aveSpeed = taskSpeedInfo.maxSpeed;
      needSave = true;
    }
    // 检查100%的点是否存在
    if (taskStatus === TaskStatus.Succeeded || taskStatus === TaskStatus.Seeding) {
      logger.log('checkTaskSpeedInfo check complete data');
      const completeSpeedInfo: IHistorySpeedInfo | undefined = taskSpeedInfo.speedInfoMap && taskSpeedInfo.speedInfoMap[100];
      const initSpeedInfo: IHistorySpeedInfo | undefined = taskSpeedInfo.speedInfoMap && taskSpeedInfo.speedInfoMap[0];
      if (!isObject(completeSpeedInfo) || !isObject(initSpeedInfo)) {
        // 下载完成了，但是缺少0%和100%的点，且平均速度为0
        if (taskSpeedInfo.aveSpeed === 0) {
          const downloadSize: number = taskBase.downloadSize || 0
          let downloadingPeriod: number = taskBase.openOnComplete || 0
          if (downloadingPeriod < 1) {
            downloadingPeriod = 1;
          }
          taskSpeedInfo.aveSpeed = Math.round(downloadSize / downloadingPeriod);
          needSave = true;
        }
      }
    }
    if (needSave) {
      saveTaskSpeedInfo(taskId).catch();
    }
  }

  function onTaskDetailChanged(taskBase: TaskBase) {
    do {
      let progress: number = 0;
      const addSpeed: number = 0;
      const downloadSize: number = taskBase.downloadSize || 0;
      let downloadingPeriod: number = taskBase.downloadPeriod || 1;
      if (downloadingPeriod < 1) {
        downloadingPeriod = 1;
      }

      let aveSpeed: number = Math.round(downloadSize / downloadingPeriod);

      const taskStatus: TaskStatus = taskBase.taskStatus || TaskStatus.Unkown;
      if (taskStatus === TaskStatus.Succeeded || taskStatus === TaskStatus.Seeding) {
        progress = 100;
      } else {
        const fileSize: number = taskBase.fileSize || 0;
        if (fileSize !== 0) {
          progress = (downloadSize / fileSize) * 100;
          progress = parseInt(progress.toString(), 10);
          if (progress > 100) {
            progress = 100;
          } else if (progress < 0) {
            progress = 0;
          }
        }
      }
      const downloadSpeed = taskBase.downloadSpeed || 0;
      // console.log('>>>>>>>>>>>>>>>>> 更新进度', taskBase.taskId, aveSpeed, progress, downloadSpeed, addSpeed)
      // 更新进度信息
      updateProgress(taskBase.taskId, aveSpeed, progress, downloadSpeed, addSpeed).catch((err: Error) => {
        logger.log(err);
      });
    } while (0);
  }

  async function onTaskStatusChanged(taskId: number) {
    do {
      let task = await TaskManager.GetInstance().findTaskById(taskId);
      if (!task) return
      const taskBase = task.getTaskBase()
      const taskStatus: TaskStatus = taskBase.taskStatus || TaskStatus.Unkown;
      if (
        (taskStatus === TaskStatus.StartWaiting ||
          taskStatus === TaskStatus.StartPending || 
          taskStatus === TaskStatus.Started
        )
      ) {
        // 重新下载, 其实不会进入这个分支
        const taskId: number = taskBase.taskId
        let taskSpeedInfo: IHistoryTaskSpeedInfo|undefined = taskSpeedMap.value.get(taskId)
        if (!taskSpeedInfo) {
          await loadTaskSpeedInfo(taskId);
          taskSpeedInfo = taskSpeedMap.value.get(taskId) || newTaskSpeedInfo(taskId);
        }
        if (taskSpeedInfo) {
          // taskSpeedInfo.speedInfoMap = {};
          taskSpeedMap.value.set(taskId, taskSpeedInfo);
          saveTaskSpeedInfo(taskId).catch((err: Error) => {
            logger.log(err);
          });
        }
      } else if ( taskStatus === TaskStatus.Succeeded || taskStatus === TaskStatus.Seeding) {
        const taskId: number = taskBase.taskId;
        const taskSpeedInfo: IHistoryTaskSpeedInfo|undefined = taskSpeedMap.value.get(taskId);
        if (taskSpeedInfo) {
          const downloadSize: number = taskBase.downloadSize || 0;
          let downloadingPeriod: number = taskBase.downloadPeriod || 1;
          if (downloadingPeriod < 1) {
            downloadingPeriod = 1;
          }
          taskSpeedInfo.aveSpeed = Math.round(downloadSize / downloadingPeriod);
          checkTaskSpeedInfo(taskId, taskSpeedInfo);
        }
      } else if (taskStatus === TaskStatus.DestroyPending) {
        const taskId: number = taskBase.taskId;
        const filePath: string = path.join(speedPath.value, 'TaskInfoEx_' + taskId + '.txt');
        logger.log('destroy task taskId:', taskId, ', taskinfo file:', filePath);
        FileSystemAWNS.unlinkAW(filePath).catch();
        taskSpeedMap.value.delete(taskId);
      }
    } while (0);
  }

  // 此处需要判断文件是否存在
  init()
  return {
    getTaskSpeedInfo,
    speedPath
  }
})