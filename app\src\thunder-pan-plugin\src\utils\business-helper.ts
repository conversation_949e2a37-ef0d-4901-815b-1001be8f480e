import { Logger } from "@root/common/logger"
import { PopUpNS } from "@root/common/pop-up"
import { ThunderPanClientSDK } from "@root/common/thunder-pan-manager/client"
import { API_FILE } from "@root/common/thunder-pan-manager/pan-sdk/types"
import { objectAssign } from "@root/common/thunder-pan-manager/pan-sdk/utils/basic"
import { AuditStatus, getMediaType } from "@root/common/thunder-pan-manager/pan-sdk/utils/drive"
import * as PopUpTypes from '@root/common/pop-up/types';
import { useUserStore } from "@/store/user-store"
import { BaseManager } from "@/manager/base-manager"
import { GlobalEventHelper } from "./global-event-helper"
import { TPanRoute, useDriveRouterStore } from "@/store/drive-router-store"
import { ETabId, TabsManager } from "@/manager/tabs-manager"
import { IPC_API_NAME } from "@root/common/thunder-pan-manager/common/ipc-define"
import { client } from "@xunlei/node-net-ipc/dist/ipc-client"
import { config } from '@root/common/config/config'
import { RecentSaveFolderHelper } from '@/utils/recent-save-folder-helper'
import { IBatchCreateDownloadTaskResponse } from "./retrieval-task-helper"
import { MainRenderUIHelper } from "@root/common/main-renderer-ui-helper"
import { ThunderHelper } from "@root/common/thunder-helper"
import { AsyncRemoteCall } from "@root/common/renderer-async-remote-call"
import { DriveFileManager } from "@/manager/drive-file-manager"
import { useTransferRouterStore } from "@/store/transfer-router-store"
import { TransferFileManager } from "@/manager/transfer-file-manager"
import { FileOperationHelper } from "./file-operation"

const logger = new Logger({ tag: 'PanBusinessHelper' })

export interface IGetAllFilesInDirectoryOptions {
  fileId: string
  space?: string
  filters?: Record<string, object>
}

export interface IGetSameFilesInDirectoryOptions {
  fileId: string
  space?: string
}

export interface IAddUrlToDriveTaskData {
  name?: string;
  url: string;
  path: string;
  parentId: string;
  files?: string[]; // bt 任务子文件下标
  play?: boolean;
  folder_type?: string;
  space?: string;
  webTitle?: string;
  cookie?: string;
  referer?: string;
}

export interface IAddUrlToDriveOptions {
  aidFrom?: string
  autoSelectTab?: boolean
}

export interface IBatchAddUrlsToDriveOptions extends IAddUrlToDriveOptions {
  savePath?: boolean
  groupName?: string  // 任务组名称
}

export interface IFileTreeRootFileSet {
  id?: string
  name?: string
  space?: string
}

export interface IPathSelectorPropsOptions {
  parentId?: number
  title?: string
  rootFile?: IFileTreeRootFileSet
  enableNewFolder?: boolean
  cancelButtonText?: string
  confirmButtonText?: string
}

export interface IPathSelectorItemData {
  id: string
  name: string
}

export interface IPathSelectorResponse {
  path: IPathSelectorItemData[]
  selectedItem: IPathSelectorItemData
}

export interface IOpenDirectoryOptions {
  fileSpace: string
  highlight?: boolean
}

export interface IGetCurrentUserQuotasResponse {
  limit: number
  usage: number
  surplus: number
}

export interface IOpenDirectoryResponse {
  success: boolean
  message: string
}

export class PanBusinessHelper {
  private static _instance: PanBusinessHelper;

  static getInstance () {
    if (PanBusinessHelper._instance) return PanBusinessHelper._instance;
    PanBusinessHelper._instance = new PanBusinessHelper();
    return PanBusinessHelper._instance;
  }

  // 云添加 toast 提示实例（实现 toast 单例）
  private _cloudAddMessageCtor

  init() {
    this._registerIPC()
  }

  async getAllFilesInDirectory (options: IGetAllFilesInDirectoryOptions) {
    const fileInfo = await this.getFileInfoById(options.fileId);
    if (!fileInfo) return { files: [] } as API_FILE.DriveListFilesResponse;

    const parentId = fileInfo.parent_id;
    const isInSafeBoxFolder = options.space === 'SPACE_SAFE';

    const allFilesRes = await ThunderPanClientSDK.getInstance().getAllFileList(
      parentId ?? '',
      options.space ?? '',
      '',
      {
        headers: {
          'space-authorization': isInSafeBoxFolder ? (await ThunderPanClientSDK.getInstance().getSafeBoxToken()).data : '',
        }
      }
    );

    if (allFilesRes.success && allFilesRes.data) {
      if (options.filters) {
        const filters = options.filters;
        const filtersKeys = Object.keys(filters);
        const files:  API_FILE.DriveFile[] = [];

        allFilesRes.data.files?.forEach(f => {
          filtersKeys.forEach(k => {
            const filter = filters[k];

            if (!['object', 'function'].includes(typeof filter)) {
              if (f[k as keyof API_FILE.DriveFile] === filter) {
                files.push(f);
              }
            } else if (Array.isArray(filter)) {
              if (filter.includes(f[k as keyof API_FILE.DriveFile])) {
                files.push(f);
              }
            }
          });
        });

        return objectAssign(allFilesRes.data, { files });
      } else {
        return allFilesRes.data;
      }
    }

    return { files: [] } as API_FILE.DriveListFilesResponse;
  }

  async getSameFilesInDirectory (options: IGetSameFilesInDirectoryOptions) {
    const fileInfo = await this.getFileInfoById(options.fileId, options.space);
    if (!fileInfo) return [];

    const parentId = fileInfo.parent_id!;
    const allFilesRes = await this.getFilesByParentId(parentId, options.space);

    // 匹配规则：如果是顶层目录，就按名称相似性进行匹配，否则就是当前目录
    let outFiles: API_FILE.DriveFile[] = [];
    allFilesRes.forEach((file: API_FILE.DriveFile) => {
      if (
        (file.audit && file.audit.status === AuditStatus.OK)
          && ['video', 'music'].includes(getMediaType(file.file_extension, file.mime_type, file.file_category))
      ) {
        outFiles.push(file);
      }
    });

    return outFiles;
  }

  async getFileInfoById (id: string, space: string = '') {
    let fileInfo: API_FILE.DriveFile | null = null;

    let headers = {}
    if (space === 'SPACE_SAFE') {
      headers['space-authorization'] = (await ThunderPanClientSDK.getInstance().getSafeBoxToken()).data;
    }
    const remoteFileInfo = await ThunderPanClientSDK.getInstance().getFileInfo(id, { params: { space }, headers })

    if (remoteFileInfo.success && remoteFileInfo.data) {
      fileInfo = remoteFileInfo.data
    }

    return fileInfo
  }

  private async getFilesByParentId (parentId: string, space: string = '') {
    let files: API_FILE.DriveFile[] = []

    let headers = {}
    if (space === 'SPACE_SAFE') {
      headers['space-authorization'] = (await ThunderPanClientSDK.getInstance().getSafeBoxToken()).data;
    }
    const remoteFileInfo = await ThunderPanClientSDK.getInstance().getAllFileList(parentId, space, '', { params: { space }, headers })

    if (remoteFileInfo.success && remoteFileInfo.data && remoteFileInfo.data.files) {
      files = remoteFileInfo.data.files
    }

    return files
  }

  async searchFileTreePathById(id: string): Promise<API_FILE.DriveFile[]> {
    // 优先从服务端取
    const fileListRes = await ThunderPanClientSDK.getInstance().serverSearchFileTreePathById(id)

    if (fileListRes.success && fileListRes.data?.length) {
      return fileListRes.data
    }
    return []
  }

  /**
   * 为指定 parent_id 创建文件夹，深度不能超过 15 层
    * @param parentId 在此目录下创建目录
    * @param paths 创建的目录
   * @returns
   */
  async createFolders (parentId: string, paths: string[]) {
    let message = '', error = ''
    const fileInfos: API_FILE.DriveFile[] = []

    if (paths.length > 15) {
      return {
        fileInfos,
        error: 'path_overlimit',
        message: '创建目录超出限制'
      }
    }
    // 逐层创建对应的目录文件夹
    for (const dirName of paths) {
      const res = await ThunderPanClientSDK.getInstance().createFolder(parentId, dirName, {
        params: {
          ignore_duplicated_name: true
        }
      })

      if (res.success && res.data && res.data.file) {
        fileInfos.push(res.data.file)
        // 下一层的父文件 id
        parentId = res.data.file?.id as string
      } else {
        error = 'partial_failure'
        message = '创建部分目录失败'
        break
      }
    }

    return {
      fileInfos,
      error,
      message,
    }
  }

  /**
   * 批量创建云添加任务
   */
  async batchAddUrlsToDrive (tasks: IAddUrlToDriveTaskData[], options: IBatchAddUrlsToDriveOptions = {}) {
    logger.log('batchAddUrlsToDrive', tasks, options)
    // 前置判断超限
    const filteredParams = this._checkURLTaskBatchLimit(tasks)
    if (filteredParams.length === 0) {
      return []
    }

    const originParentId = filteredParams[0].parentId === '' ? '' : filteredParams[0].parentId.split('\\').pop() as string
    let parentId = originParentId
    let savePathId = filteredParams[0].parentId
    let savePathName = filteredParams[0].path

    // 校验所选的文件路径是否还存在
    if (parentId) {
      const originParentResponse = await ThunderPanClientSDK.getInstance().getFileInfo(parentId)
      if (!originParentResponse.success) {
        // 文件已经被删除，则重新创建对应的文件夹
        if (['file_not_found', 'file_in_recycle_bin'].includes(originParentResponse.error.error)) {
          // 移除第一个“我的云盘”（根目录），创建后面的目录（path 的值例如：我的云盘\\文件夹1\\文件夹2）
          const res = await this.createFolders('', savePathName.split('\\').slice(1))

          if (res.fileInfos.length) {
            const lastFile = res.fileInfos[res.fileInfos.length - 1]

            parentId = lastFile.id!
            savePathId = lastFile.id!
            this.appendNewFilesToDrive(lastFile.parent_id!, [ lastFile ])
          } else {
            throw {
              error: res.error,
              message: res.message
            }
          }
        } else {
          throw originParentResponse.error
        }
      }
    }

    // 更新最近下载路径配置
    if (options.savePath) {
      const isUseLastPath = await config.getValue('ThunderPanPlugin', 'lastUsePath', true)
      if (isUseLastPath) {
        await RecentSaveFolderHelper.getInstance().setDefaultSaveFolder({ name: savePathName, id: savePathId })
      }
      await RecentSaveFolderHelper.getInstance().addRecentFolder({ name: savePathName, id: savePathId })
    }

    // 如果添加任务组, 创建文件夹
    // 服务端对于bt任务单文件只会添加文件,不会创建文件夹, 这时如果修改了名称, 需要为它创建目录, 如果是多文件, 服务端会创建文件, 只需要修改params[0]的name即可
    /* bt文件只有一个文件的情况 */ /* 常规多任务合并为任务组 */
    if (options.groupName && (filteredParams[0]?.files?.length === 1 || filteredParams.length > 1 )) {
      const newFolderRes = await ThunderPanClientSDK.getInstance().createFolder(parentId, options.groupName, {
        params: {
          ignore_duplicated_name: true,
        }
      })

      if (newFolderRes.success && newFolderRes.data && newFolderRes.data.file) {
        parentId = newFolderRes.data.file.id!
      }
    }

    // 替换待创建的云添加任务的 parentId
    if (originParentId !== parentId) {
      filteredParams.forEach(el => {
        el.parentId = parentId as string
      })
    }

    let result: API_FILE.DriveCreateFileResponse[] = []
    for (const task of filteredParams) {
      const res = await this.addUrlToDrive(task, options)

      if (res.success && res.data) {
        result.push(res.data)
      }
    }

    return result
  }

  /**
   * 通过 URL 创建云添加任务
   * @param task
   * @returns
   */
  async addUrlToDrive(task: IAddUrlToDriveTaskData, options: IAddUrlToDriveOptions = {}) {
    const createParams: API_FILE.DriveCreateFileRequest = {
      upload_type: 'UPLOAD_TYPE_URL',
      kind: 'drive#file',
      parent_id: task.parentId,
      name: task.name,
      hash: '',
      size: '0',
      path: task.path,
      space: task.space,
      folder_type: task.folder_type,
      url: {
        url: task.url,
        files: task.files,
      },
      params: {
        require_links: String(Boolean(task.play)),
        web_title: task.webTitle!,
        cookie: task.cookie!,
        referer: task.referer!,
      },
    };
    let message = ''
    const res = await ThunderPanClientSDK.getInstance().createFile({ params: createParams })

    if (res.success && res.data && res.data.task) {
      // 云添加任务创建成功，内部事件通知 UI 更新
      GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.CLOUD_ADD_TASK_ADD, res.data.task);

      if (options.autoSelectTab) {
        // 切换到云盘【云添加】tab
        this.openCloudAddTab()
      }
    } else if (res.error) {
      // 云添加次数不足
      if (res.error.error === 'task_create_count_limit') {
        const { isYearlySuper } = useUserStore();
        // 年费超会提示
        if (isYearlySuper) {
          message = '今日云添加次数已用完，请明日再试'
        } else {
          // 调起会员支付弹窗
          message = '今日云添加次数已用完，请明日再试'
        }
      }
      // 云盘空间不足
      else if (res.error.error === 'file_space_not_enough') {
        // 调起会员支付弹窗
        message ='云盘空间不足'
      }
    }

    if (message && !this._cloudAddMessageCtor) {
      this._cloudAddMessageCtor = window.__VueGlobalProperties__.$message({
        message: message,
        type: 'warning',
        onClose: () => {
          this._cloudAddMessageCtor = undefined
        }
      });
    }

    return res;
  }

  async openFetchBackDialog (files: API_FILE.DriveFile[]) {
    const res = await PopUpNS.popup('thunder-pan_fetch-back', {
      windowWidth: 680,
      windowHeight: 240,
      relatePos: PopUpTypes.RelatePosType.CenterParent,
      files: files,
    }, { resizable: false, modal: true })

    return {
      success: res.action === PopUpTypes.Action.OK,
      data: res.args as IBatchCreateDownloadTaskResponse
    }
  }

  async openPathSelector (options: IPathSelectorPropsOptions = {}) {
    const res = await PopUpNS.popup('thunder-pan_path-selector', {
      windowWidth: 680,
      windowHeight: 466,
      relatePos: PopUpTypes.RelatePosType.CenterParent,
      title: '选择云盘保存路径',
      enableNewFolder: true,
      rootFile: {
        id: '',
        name: '我的云盘',
        space: '',
      },
      ...options,
    }, { resizable: false, modal: true })

    if (res.action === PopUpTypes.Action.OK) {
      return res.args as IPathSelectorResponse
    }
    return undefined
  }

  async openSigninDialog () {
    PopUpNS.showLoginDlg()
  }

  async openDirectory (fileId: string, options: IOpenDirectoryOptions = { fileSpace: '' }) {
    logger.log('openDirectory', fileId, options)
    const ancestorsRes = await ThunderPanClientSDK.getInstance().getFileAncestors(fileId, { params: { space: options.fileSpace } })

    if (ancestorsRes.success && ancestorsRes.data && ancestorsRes.data.ancestors && ancestorsRes.data.ancestors.length) {
      const ancestors = ancestorsRes.data.ancestors
      const targetFile = ancestors.shift()
      // 组装面包屑
      const newRoutes: TPanRoute[] = []
      ancestors.reverse().forEach(file => {
        newRoutes.push({
          ...file,
          id: file.id!,
          title: file.name!,
        })
      })
      // 如果进入包含保险箱的路径，校验一下保险箱 token
      const isInToSafeBox = newRoutes.some(route => route.space === 'SPACE_SAFE')
      if (isInToSafeBox) {
        const isPass = await FileOperationHelper.getInstance().checkBeforeConsumeSafeBox()
        if (!isPass) {
          return {
            success: false,
            message: '超级保险箱凭证已过期'
          }
        }
      }
      // 更新面包屑
      const { setRouterList } = useDriveRouterStore()
      setRouterList(newRoutes, true)
      // 切到云盘 tab
      this._switchMainMenuToPan()
      // 切换到云盘【所有】tab
      TabsManager.getInstance().setCurrentTab(ETabId.ALL)
      // 通知 UI 选中、高亮文件
      GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.DRIVE_LIST_PICK_OR_HIGHLIGHT_FILE, targetFile, options.highlight)

      return {
        success: true,
        message: '',
      }
    }

    window.__VueGlobalProperties__.$message({
      message: '未找到相关文件',
      type: 'warning',
    });
    return {
      success: false,
      message: '未找到相关文件'
    }
  }

  getCurrentUserTrashPrivilegeDuration () {
    const { privilegeType } = useUserStore()
    const duration = BaseManager.getInstance().getPrivilege().TRASH_STORE_DURATION?.[privilegeType.value] || 10
    return Number(duration)
  }

  getCurrentUserCloudAddQuotas () {
    const about = BaseManager.getInstance().getAbout()
    const limit = Number(about.offlineTaskLimit)
    const usage = Number(about.offlineTaskUsage)
    const surplus = limit >= usage ? (limit - usage) : 0

    return {
      limit: limit,
      usage: usage,
      surplus: surplus,
    }
  }

  getCurrentUserDriveQuotas () {
    const about = BaseManager.getInstance().getAbout()
    const limit = Number(about.spaceLimit)
    const usage = Number(about.spaceUsage)
    const surplus = limit >= usage ? (limit - usage) : 0

    return {
      limit: limit,
      usage: usage,
      surplus: surplus,
    }
  }

  openCloudAddTab () {
    this.bringMainWndToTop()
    this._switchMainMenuToPan()
    // 切换到云盘【云添加】tab
    TabsManager.getInstance().setCurrentTab(ETabId.CLOUD_ADD)
  }

  appendNewFilesToDrive (parentId: string, files: API_FILE.DriveFile[]) {
    const { currentParentFile: driveParentFile }  = useDriveRouterStore()
    const { currentParentFile: transferParentFile } = useTransferRouterStore()
    // 如果当前所在的目录是需要添加的目录，则加进去
    if (parentId === driveParentFile.value.id) {
      DriveFileManager.getInstance().appendFiles(files)
    }
    if (parentId !== '' && parentId === transferParentFile.value.id) {
      TransferFileManager.getInstance().appendFiles(files)
    }
    // 更新基础信息
    BaseManager.getInstance().getBase()
  }

  consumeFileById (fileId: string, space: string = '') {
    FileOperationHelper.getInstance().consumeFile(fileId, space)
  }

  /**
   * 将主窗口置顶
   */
  async bringMainWndToTop () {
    const currentWindow = await AsyncRemoteCall.GetInstance().getCurrentWindow()
    if (currentWindow) {
      currentWindow.show()
      ThunderHelper.setForegroundWindow(currentWindow)
    }
  }

  private _switchMainMenuToPan () {
    MainRenderUIHelper.getInstance().navigateToPath('/cloud', {})
  }

  /**
   * 检查任务是否超过单任务文件数限制
   */
  private _checkURLTaskBatchLimit (tasks: IAddUrlToDriveTaskData[]) {
    const privilege = BaseManager.getInstance().getPrivilege()
    const { isSuperV, isYearlySuper, isVip } = useUserStore()
    const userLimit = Number(privilege.OFFLINE_SUB_FILE_COUNT_LIMIT?.user ?? 1000)
    const platinumLimit = Number(privilege.OFFLINE_SUB_FILE_COUNT_LIMIT?.platinum ?? 2000)
    const superLimit = Number(privilege.OFFLINE_SUB_FILE_COUNT_LIMIT?.superV ?? 5000)
    const yearlySuperLimit = Number(privilege.OFFLINE_SUB_FILE_COUNT_LIMIT?.superVYear ?? 5000)
    // 当前用户所在的会员的限制
    let limit = userLimit
    if (isSuperV) {
      if (isYearlySuper) {
        limit = yearlySuperLimit
      } else {
        limit = superLimit
      }
    } else if (isVip) {
      limit = platinumLimit
    } else {
      limit = userLimit
    }
    // 最大限制
    const maxLimit = yearlySuperLimit

    const underLimitParams = tasks.filter(taskParam => {
      // 安全检查：确保files字段存在且是数组
      const filesCount = taskParam.files && Array.isArray(taskParam.files) ? taskParam.files.length : 0
      return filesCount <= limit
    })
    if (underLimitParams.length !== tasks.length) {
      // 存在超限任务
      const overLimitParams = tasks.filter(taskParam => {
        // 安全检查：确保files字段存在且是数组
        const filesCount = taskParam.files && Array.isArray(taskParam.files) ? taskParam.files.length : 0
        return filesCount > limit
      }).map(taskParam => {
        // 安全检查：确保files字段存在且是数组
        const filesCount = taskParam.files && Array.isArray(taskParam.files) ? taskParam.files.length : 0
        return filesCount
      }).sort((a, b) => {
        // descending
        return b - a
      })
      if (overLimitParams.length > 0) {
        if (overLimitParams[0] > maxLimit) {
           // 超的是最大上限，直接toast
          window.__VueGlobalProperties__.$message({
            message: `本次云添加文件超过${maxLimit}个，暂不支持添加，请重新选择`,
            type: 'warning',
          });
        } else {
          // 否则弹特权弹窗
          window.__VueGlobalProperties__.$message({
            message: `本次云添加文件超过${limit}个，暂不支持添加，请重新选择`,
            type: 'warning',
          });
        }
      }
    }

    return underLimitParams
  }

  private _registerIPC () {
      // 与主进程通一个 ipc context，直接注册
    client.registerFunctions({
      [IPC_API_NAME.GET_ALL_FILES_IN_DIRECTORY]: (_ctx: unknown, options: IGetAllFilesInDirectoryOptions) => {
        return this.getAllFilesInDirectory(options)
      },
      [IPC_API_NAME.GET_SAME_FILES_IN_DIRECTORY]: (_ctx: unknown, options: IGetSameFilesInDirectoryOptions) => {
        return this.getSameFilesInDirectory(options)
      },
      [IPC_API_NAME.SEARCH_FILE_TREE_PATH_BY_ID]: (_ctx: unknown, id: string) => {
        return this.searchFileTreePathById(id)
      },
      [IPC_API_NAME.ADD_URL_TO_DRIVE]: async (_ctx: unknown, task: IAddUrlToDriveTaskData, options?: IAddUrlToDriveOptions) => {
        const res = await this.addUrlToDrive(task, options)
        // 云添加成功，返回 data（减少 ipc 封装多包一层）
        if (res.success) {
          return res.data
        }
        // 云添加失败，抛出对应错误
        throw res.error
      },
      [IPC_API_NAME.BATCH_ADD_URLS_TO_DRIVE]: (_ctx: unknown, tasks: IAddUrlToDriveTaskData[], options?: IBatchAddUrlsToDriveOptions) => {
        return this.batchAddUrlsToDrive(tasks, options)
      },
      [IPC_API_NAME.OPEN_PATH_SELECTOR_DIALOG]: (_ctx: unknown, options: IPathSelectorPropsOptions) => {
        return this.openPathSelector(options)
      },
      [IPC_API_NAME.OPEN_PAN_DIRECTORY]: (_ctx: unknown, fileId: string, options: IOpenDirectoryOptions) => {
        return this.openDirectory(fileId, options)
      },
      [IPC_API_NAME.GET_CURRENT_USER_PRIVILEGE_TRASH_DURATION]: (_ctx: unknown) => {
        return this.getCurrentUserTrashPrivilegeDuration()
      },
      [IPC_API_NAME.GET_CURRENT_USER_CLOUD_ADD_QUOTAS]: (_ctx: unknown) => {
        return this.getCurrentUserCloudAddQuotas()
      },
      [IPC_API_NAME.GET_CURRENT_USER_DRIVE_QUOTAS]: (_ctx: unknown) => {
        return this.getCurrentUserDriveQuotas()
      },
      [IPC_API_NAME.OPEN_PAN_CLOUD_ADD_TAB]: (_ctx: unknown) => {
        return this.openCloudAddTab()
      },
      [IPC_API_NAME.APPEND_NEW_FILES_TO_DRIVE]: (_ctx: unknown, parentId: string, files: API_FILE.DriveFile[]) => {
        return this.appendNewFilesToDrive(parentId, files)
      },
      [IPC_API_NAME.CONSUME_FILE_BY_ID]: (_ctx: unknown, fileId: string, space: string = '') => {
        return this.consumeFileById(fileId, space)
      },
    })
  }
}
