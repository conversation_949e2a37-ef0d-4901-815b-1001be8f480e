/**
 * 二级弹框在非browser进程的代理对象
 */

import { EventEmitter } from 'events';
import * as PopUpTypes from '@root/common/pop-up/types';
import { mainProcessContext } from '@xunlei/node-net-ipc/dist/ipc-base';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';

export class ReuseBrowserWindowProxy extends EventEmitter {
  private id_: number; // 窗口id
  private event_handlers_: { [eventName: string]: (...args: any[]) => void } = {};

  constructor(id: number) {
    super();
    this.id_ = id;
    this.event_handlers_ = {};
  }

  get id(): number {
    return this.id_;
  }

  // 重载事件
  public on(event: 'ready-to-show', listener: Function): this;
  public on(event: 'close', listener: Function): this;
  public on(event: 'closed', listener: Function): this;
  public on(event: 'ready-to-show' | 'close' | 'closed', listener: Function): this {
    super.on(event, listener as (...args: any[]) => void);
    if (!this.event_handlers_[event]) {
      const handler: (...args: any[]) => void = (...args: any[]): void => {
        this.emit(event, ...args);
      };
      this.event_handlers_[event] = handler;
      clientModule.callRemoteClientFunction(mainProcessContext, 'AttachReuseEvent', this.id_, event, handler).catch();
    }
    return this;
  }

  public once(event: 'ready-to-show', listener: Function): this
  public once(event: 'close', listener: Function): this
  public once(event: 'closed' | string, listener: Function): this
  public once(event: 'ready-to-show' | 'close' | 'closed' | string, listener: Function): this {
    if (event === 'free' || event === 'abnormal-closed') {
      return this;
    }
    super.once(event, listener as (...args: any[]) => void);
    if (!this.event_handlers_[event]) {
      const handler: (...args: any[]) => void = (...args: any[]): void => {
        this.emit(event, ...args);
      };
      this.event_handlers_[event] = handler;
      clientModule.callRemoteClientFunction(mainProcessContext, 'AttachReuseEvent', this.id_, event, handler).catch();
    }
    return this;
  }

  removeAllListeners(event?: string | symbol): this {
    super.removeAllListeners(event);
    this.event_handlers_.forEach((handler: (...args: any[]) => void, event: string) => {
      clientModule.callRemoteClientFunction(mainProcessContext, 'DetachReuseEvent', this.id_, event, handler).catch();
    });
    this.event_handlers_.clear();
    return this;
  }

  async setTitle(title: string): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseSetTitle', this.id_, title);
    } while (0);
  }

  /**
   * @description: 关闭窗口
   * @param payloadArgs: 关闭窗口时的附加信息，对应 ResolvePayload的非action字段
   */
  async close(action: PopUpTypes.Action = PopUpTypes.Action.Close, payloadArgs?: any): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseClose', this.id_, action, payloadArgs);
      super.removeAllListeners();
      this.event_handlers_ = {};
    } while (0);
  }

  async isDestroyed(): Promise<boolean> {
    let destroyed: boolean = false;
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      destroyed = (await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseIsDestroyed', this.id_))[0];
    } while (0);
    return destroyed;
  }

  async isVisible(): Promise<boolean> {
    let visible: boolean = false;
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      visible = (await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseIsVisible', this.id_))[0];
    } while (0);
    return visible;
  }

  async isMinimized(): Promise<boolean> {
    let minimized: boolean = false;
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      minimized = (await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseIsMinimized', this.id_))[0];
    } while (0);
    return minimized;
  }

  async minimize(): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseMinimize', this.id_);
    } while (0);
  }

  async maximize(): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseMaximize', this.id_);
    } while (0);
  }

  async restore(): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseRestore', this.id_);
    } while (0);
  }

  async show(): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseShow', this.id_);
    } while (0);
  }

  async hide(): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseHide', this.id_);
    } while (0);
  }

  async focus(): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseFocus', this.id_);
    } while (0);
  }

  async setPosition(x: number, y: number, animate?: boolean): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseSetPosition', this.id_, x, y, animate);
    } while (0);
  }

  async center(): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }

      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseCenter', this.id_);
    } while (0);
  }

  async openDevTools(options?: Electron.OpenDevToolsOptions): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseOpenDevTools', this.id_, options);
    } while (0);
  }

  async setParentWindow(parentId: number): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseSetParentWindow', this.id_, parentId);
    } while (0);
  }

  async updateView(componentsName: string, title: string, options: any): Promise<void> {
    do {
      if (this.id_ === undefined || this.id_ === null) {
        break;
      }
      await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseUpdateView', this.id_, componentsName, title, options);
    } while (0);
  }
}