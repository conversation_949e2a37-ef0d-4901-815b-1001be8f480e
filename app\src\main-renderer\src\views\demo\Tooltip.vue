<script setup lang="ts">
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
</script>

<template>
  <h1>Tooltip</h1>

  <div style="display: flex; flex-direction: column; gap: 10px; align-items: center; margin-bottom: 10px;">

    <Tooltip>
      <template #trigger>
        <Button variant="outline">
          <i class="xl-icon-add"></i>
        </Button>
      </template>
      <template #content>
        Add to library
      </template>
    </Tooltip>


    <Tooltip side="right">
      <template #trigger>
        <Button variant="outline">
          Right Tooltip
        </Button>
      </template>
      <template #content>
        show right tooltip
      </template>
    </Tooltip>

    <Tooltip side="left">
      <template #trigger>
        <Button variant="outline">
          Left Tooltip
        </Button>
      </template>
      <template #content>
        show left tooltip
      </template>
    </Tooltip>

    <Tooltip side="bottom">
      <template #trigger>
        <Button variant="outline">
          Bottom Tooltip
        </Button>
      </template>
      <template #content>
        show bottom tooltip
      </template>
    </Tooltip>


    <Tooltip side="bottom" showArrow>
      <template #trigger>
        <Button variant="outline">
          Has Arrow Bottom Tooltip
        </Button>
      </template>
      <template #content>
        show Arrow Bottom Tooltip
      </template>
    </Tooltip>

  </div>
</template>
