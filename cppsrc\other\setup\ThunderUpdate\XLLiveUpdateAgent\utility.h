#pragma once
#include <string>
#include <vector>
#include <Windows.h>

class utility
{
public:
    utility(void);
    ~utility(void);

    //static bool GetPeerId(std::string& peerid);

    static bool GetOSVer(std::string& osVer);

    static bool GetNowTime(std::string& szTime);

    static void MakeDirTree(const wchar_t* dir_path, bool bAuthorize = true);

    static void AuthorizeDirectory(PCTSTR pszPath, ACCESS_MASK accessmask=FILE_ALL_ACCESS, BYTE aceflags=OBJECT_INHERIT_ACE|CONTAINER_INHERIT_ACE);

    static bool IsHaveUAC();
    static bool IsAdministrator();

    static DWORD ExecuteExe(const wchar_t* exe_file, 
        const wchar_t* param, const wchar_t* work_dir, bool run_as = false, bool waitExecute = true);

    static void GetLiveUpdateDir(std::wstring& wsLiveUpdateDir, bool alwaysCreate = true);

    static void GetTempLiveUpdateDir(const wchar_t* productName, const wchar_t* productVer, std::wstring& wsTempLiveUpdateDir);

    static void GetStartupInfoFilePath(const wchar_t* name, const wchar_t* ver, const wchar_t* pPath, std::wstring& wsPath, bool alwaysCreate = true);
    static void GetPatchInfoFilePath(const wchar_t* name, const wchar_t* ver, const wchar_t* pPath, std::wstring& wsPath);
    static void GetDownloadPath(std::wstring& wsPath);

    static bool GetProcessPath(DWORD ProcessId, std::wstring& path);

    static void GetMD5FromString(const std::wstring str, std::wstring& md5);
    static BOOL GetPathFromDeviceName(LPCWSTR DevicePath, std::wstring& Path);

    static bool CheckProcessExistByName(const wchar_t* name);
    static void TerminateProcessByName(const wchar_t* name);
    static BOOL IsWindows7();
    static void OpenURL(const std::wstring strURL);
    static BOOL IsFullScreen();
    static BOOL IsFullScreen2();

    static bool ReadConfig(const wchar_t* file, const wchar_t* section, const wchar_t* key, std::wstring& value);
    static bool WriteConfig(const wchar_t* file, const wchar_t* section, const wchar_t* key, const wchar_t* value);

	static void GetLocalUpdateInfoJsonFilePath(const wchar_t* productName, const wchar_t* productVer, std::wstring& wsPath);
	static void GetPatchInfoJsonFilePath(const wchar_t* productName, const wchar_t* productVer, std::wstring& wsPath);
};
