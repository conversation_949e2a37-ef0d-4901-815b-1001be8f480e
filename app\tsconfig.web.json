{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/@types/global.d.ts", "src/**/*", "assets/**/*"], "compilerOptions": {"rootDir": "./", "composite": true, "incremental": true, "target": "es2021", "module": "commonjs", "lib": ["dom", "es2021"], "jsx": "react-jsx", "strict": false, "sourceMap": true, "baseUrl": "./", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "allowJs": true, "outDir": "output", "skipLibCheck": true, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "strictNullChecks": false}, "exclude": ["release/build", "release/app/dist"]}