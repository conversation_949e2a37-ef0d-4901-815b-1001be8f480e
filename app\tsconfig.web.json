{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/@types/global.d.ts", "src/**/*", "assets/**/*"], "compilerOptions": {"rootDir": "./", "composite": true, "incremental": true, "target": "es2021", "module": "commonjs", "lib": ["dom", "es2021"], "jsx": "react-jsx", "strict": true, "sourceMap": true, "baseUrl": "./", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "allowJs": true, "outDir": "output"}, "exclude": ["release/build", "release/app/dist"]}