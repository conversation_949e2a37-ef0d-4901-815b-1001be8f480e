<script lang="ts" setup>
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import Button from '@root/common/components/ui/button/index.vue'
import Input from '@root/common/components/ui/input/index.vue'
import PanTooltips from '@/components/tooltips/index.vue'

import { computed, reactive, ref } from 'vue';
import { ISafeBoxDialogOptions } from '.';
import { BaseManager } from '@/manager/base-manager'
import { IResponseError } from '@root/common/thunder-pan-manager/pan-sdk/types'
import { ApplicationManager } from '@root/common/thunder-pan-manager/manager/application';
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client';

enum ESafeBoxDialogStage {
  CHECK_PASSWORD = 0,
  FORGET_PASSWORD,
  INIT_PASSWORD,
}

defineOptions({
  name: 'SafeBoxDialog',
})

const props = defineProps<ISafeBoxDialogOptions>()

const visible = ref(true)
const currentStage = ref<ESafeBoxDialogStage>(
  props.showType === 'init' ? ESafeBoxDialogStage.INIT_PASSWORD : ESafeBoxDialogStage.CHECK_PASSWORD
)
// 校验密码
const checkState = reactive({
  password: '',
  toolTipContent: '',
  toolTipVisible: false,
})
// 忘记密码
const forgetState = reactive<{
  phoneNumber: string
  verificationId: string
  verificationCode: string
  errorTipsVisible: boolean
  errorTipsContent: string
  sendCodeTimer: NodeJS.Timeout | null
  sendCodeCountdown: number
}>({
  phoneNumber: props.phoneNumber,
  verificationId: '',
  verificationCode: '',
  errorTipsVisible: false,
  errorTipsContent: '',
  sendCodeTimer: null,
  sendCodeCountdown: -1,
})
// 初始化密码
const initState = reactive({
  showType: props.showType || 'init',
  password: '',
  toolTipContent: '',
  confirmPassword: '',
  passwordToolTipVisible: false,
  confirmPasswordToolTipVisible: false,
})

function resetForgetState () {
  forgetState.phoneNumber = ''
  forgetState.verificationId = ''
  forgetState.verificationCode = ''
  forgetState.errorTipsContent = ''
  forgetState.errorTipsVisible = false
  forgetState.sendCodeTimer = null
  forgetState.sendCodeCountdown = -1
}

function resetInitState () {
  initState.showType = 'init'
  initState.password = ''
  initState.confirmPassword = ''
  initState.toolTipContent = ''
  initState.passwordToolTipVisible = false
  initState.confirmPasswordToolTipVisible = false
}

const dialogTitle = computed(() => {
  if (currentStage.value === ESafeBoxDialogStage.FORGET_PASSWORD) {
    return '重置二级密码：验证绑定手机号（1/2）'
  } else if (currentStage.value === ESafeBoxDialogStage.INIT_PASSWORD) {
    return initState.showType === 'init' ? '设置二级密码：用于超级保险箱保障安全' : '重置二级密码：设置保险箱密码（2/2）'
  }
  return '请输入超级保险箱密码'
})
const dialogContentHeight = computed(() => currentStage.value === ESafeBoxDialogStage.FORGET_PASSWORD ? 286 : 242)
const textButtonText = computed(() => {
  if (currentStage.value === ESafeBoxDialogStage.FORGET_PASSWORD) {
    return '修改安全手机'
  } else if (currentStage.value === ESafeBoxDialogStage.CHECK_PASSWORD) {
    return '忘记密码'
  }
  return ''
})
const verificationCodeButtonText = computed(() => {
  if (forgetState.sendCodeCountdown > 0) {
    return `${forgetState.sendCodeCountdown}s后重新获取`
  }
  return '获取验证码'
})

function validateCheckPassword() {
  const maxLength = 16
  const minLength = 6
  if (!checkState.password) {
    checkState.toolTipVisible = true
    checkState.toolTipContent = '请输入密码'
    return false
  } else if (checkState.password.length < minLength || checkState.password.length > maxLength) {
    checkState.toolTipVisible = true
    checkState.toolTipContent = '请输入6-16位密码'
    return false
  }
  return true
}

async function onCheckConfirm() {
  const isPassed = validateCheckPassword()
  if (!isPassed) return

  checkState.toolTipVisible = false
  checkState.toolTipContent = ''

  const res = await BaseManager.getInstance().checkSafeBoxPassword(checkState.password)

  if (res.success && res.data?.token) {
    props.onResolve()
  } else {
    const err = res.error as IResponseError
    let msg: string = err?.['error_description']
    if (msg === 'repeated captcha_invalid') {
      msg = '尝试次数过多，请稍后重试'
    }

    checkState.toolTipVisible = true
    checkState.toolTipContent = msg || '登录保险箱失败，请稍后再试'
  }
}

function onForgetConfirm() {
  if (!forgetState.verificationId) {
    forgetState.errorTipsVisible = true;
    forgetState.errorTipsContent = '请先获取验证码';
    return;
  }
  if (!forgetState.verificationCode) {
    forgetState.errorTipsVisible = true;
    forgetState.errorTipsContent = '请输入验证码';
    return;
  }
  if (!forgetState.verificationId) {
    forgetState.errorTipsVisible = true;
    forgetState.errorTipsContent = '请先获取验证码';
    return;
  }

  handleCheckVerificationCode();
}

async function handleGetVerificationCode() {
  if (forgetState.sendCodeTimer) return;

  forgetState.errorTipsVisible = false;
  forgetState.errorTipsContent = '';

  const res = await ThunderPanClientSDK.getInstance().sendVerificationCode();

  if (res.success) {
    // 开启倒计时
    forgetState.verificationId = res.data!.verification_id!;

    if (forgetState.sendCodeTimer) clearInterval(forgetState.sendCodeTimer);
    forgetState.sendCodeCountdown = 60;
    forgetState.sendCodeTimer = setInterval(() => {
      if (forgetState.sendCodeCountdown < 1) {
        clearInterval(forgetState.sendCodeTimer!);
        forgetState.sendCodeTimer = null;
      }
      forgetState.sendCodeCountdown -= 1;
    }, 1000)
  } else {
    const err = res.error as IResponseError;

    if (forgetState.sendCodeTimer) {
      clearInterval(forgetState.sendCodeTimer);
      forgetState.sendCodeTimer = null;
    }
    forgetState.errorTipsVisible = true;
    forgetState.errorTipsContent = err?.['error_description'] || '获取验证码失败，请稍后再试';
  }
}

async function handleCheckVerificationCode() {
  forgetState.errorTipsVisible = false;
  forgetState.errorTipsContent = '';

  const res = await ThunderPanClientSDK.getInstance().checkVerificationCode({
    params: {
      client_id: ApplicationManager.getCurrentDeviceClientId(),
      verification_id: forgetState.verificationId,
      verification_code: forgetState.verificationCode,
    }
  })

  if (res.success && res.data?.verification_token) {
    BaseManager.getInstance().setSafeBoxVerificationToken(res.data!.verification_token!);
    enterResetPassword()
  } else {
    let msg: string = res.error?.['error_description'];
    if (/Verification code does not match the id/ig.test(msg)) {
      msg = '验证码错误，请重新输入';
    }

    forgetState.errorTipsVisible = true;
    forgetState.errorTipsContent = msg || '验证码校验失败，请稍后再试';
  }
}

function enterResetPassword() {
  resetForgetState()
  initState.showType = 'reset'
  currentStage.value = ESafeBoxDialogStage.INIT_PASSWORD
}

async function onInitConfirm() {
  const isPassed = validateInitPassword();
  if (!isPassed) return;

  initState.passwordToolTipVisible = false;
  initState.confirmPasswordToolTipVisible = false;

  // 初始化密码
  if (initState.showType === 'init') {
    const res = await BaseManager.getInstance().initSafeBoxPassword(initState.password);

    if (!res.success || !res.data?.msg) {
      const err = res.error as IResponseError;
      window.__VueGlobalProperties__.$message({
        message: err?.['error_description'] || '初始化密码失败，请稍后再试',
        type: 'error'
      })
      return;
    }
  }
  // 重置密码
  if (initState.showType === 'reset') {
    const res = await BaseManager.getInstance().resetSafeBoxPassword(initState.password);

    if (!res.success || !res.data?.msg) {
      const err = res.error as IResponseError;
      window.__VueGlobalProperties__.$message({
        message: err?.['error_description'] || '重置密码失败，请稍后再试',
        type: 'error'
      })
      return;
    }
    // 重置密码成功后，重置 token 为空
    BaseManager.getInstance().setSafeBoxVerificationToken('');
  }

  props.onResolve()
}

function validateInitPassword() {
  const maxLength = 16, minLength = 6;
  initState.toolTipContent = ''
  initState.passwordToolTipVisible = false
  initState.confirmPasswordToolTipVisible = false

  if (!initState.confirmPassword) {
    if (initState.password.length < minLength || initState.password.length > maxLength) {
      initState.passwordToolTipVisible = true
      initState.toolTipContent = '请输入6-16位密码'
      return false
    }
    initState.confirmPasswordToolTipVisible = true
    initState.toolTipContent = '请输入确认密码'
    return false
  }
  if (!initState.password) {
    initState.passwordToolTipVisible = true
    initState.toolTipContent = '请输入密码'
    return false
  }
  if (initState.password !== initState.confirmPassword) {
    initState.confirmPasswordToolTipVisible = true
    initState.toolTipContent = '两次密码不一致，请重新输入'
    return false
  }
  return true
}

function handleClose() {
  if (props.onClose) {
    props.onClose()
  }
}

function handleCancel() {
  if (props.showType === 'init') {
    handleClose()
  } else {
    if (currentStage.value === ESafeBoxDialogStage.FORGET_PASSWORD) {
      resetForgetState()
      currentStage.value = ESafeBoxDialogStage.CHECK_PASSWORD
    } else if (currentStage.value === ESafeBoxDialogStage.INIT_PASSWORD) {
      resetInitState()
      currentStage.value = ESafeBoxDialogStage.FORGET_PASSWORD
    } else {
      handleClose()
    }
  }
}

function handleConfirm() {
  if (currentStage.value === ESafeBoxDialogStage.CHECK_PASSWORD) {
    onCheckConfirm()
  } else if (currentStage.value === ESafeBoxDialogStage.FORGET_PASSWORD) {
    onForgetConfirm()
  } else if (currentStage.value === ESafeBoxDialogStage.INIT_PASSWORD) {
    onInitConfirm()
  }
}

function handleTextButtonClick() {
  if (currentStage.value === ESafeBoxDialogStage.CHECK_PASSWORD) {
    checkState.password = ''
    checkState.toolTipContent = ''
    checkState.toolTipVisible = false
    currentStage.value = ESafeBoxDialogStage.FORGET_PASSWORD
  } else if (currentStage.value === ESafeBoxDialogStage.FORGET_PASSWORD) {
    window.__VueGlobalProperties__.$message({
      message: '尚未支持该功能，敬请期待',
      type: 'info'
    })
  }
}

function handleDialogUpdateOpen(isOpen: boolean) {
  visible.value = isOpen
  if (!isOpen) {
    handleClose()
  }
}

defineExpose({
  visible,
})
</script>

<template>
  <Dialog
    :open="visible"
    :title="dialogTitle"
    :contentStyle="`width: 460px; height: ${dialogContentHeight}px;`"
    :modal="true"
    :draggable="false"
    :show-title-icon="false"
    :prevent-default-close="true"
    :disable-header-draggable="true"
    @close="handleClose"
    @update:open="handleDialogUpdateOpen"
  >
    <div class="dialog-content">
      <div v-if="currentStage === ESafeBoxDialogStage.CHECK_PASSWORD" class="check-password-wrapper">
        <PanTooltips :visible="checkState.toolTipVisible" :content="checkState.toolTipContent">
          <Input class="password-input" type="password" v-model="checkState.password" placeholder="请输入密码" @keydown.enter="handleConfirm" />
        </PanTooltips>

        <p class="tips">退出迅雷或2小时后进入超级保险箱，需要再次输入密码</p>
      </div>

      <div v-if="currentStage === ESafeBoxDialogStage.FORGET_PASSWORD" class="forget-password-wrapper">
        <p class="tips">为了确保信息安全，请验证您已绑定的安全手机，填写收到的验证码</p>
        <Input class="phone-input" type="text" :disabled="true" v-model="forgetState.phoneNumber" placeholder="请输入手机号">
          <!-- <template #left>
            <span>+86</span>
          </template> -->
        </Input>

        <PanTooltips :visible="forgetState.errorTipsVisible" :content="forgetState.errorTipsContent">
          <Input class="code-input" type="text" v-model="forgetState.verificationCode" placeholder="请输入验证码" @keydown.enter="handleConfirm">
            <template #right>
              <div
                class="code-text-btn"
                :class="{
                  'is-disabled': forgetState.sendCodeCountdown > 0
                }"
                @click="handleGetVerificationCode"
              >
                {{ verificationCodeButtonText }}
              </div>
            </template>
          </Input>
        </PanTooltips>
      </div>

      <div v-if="currentStage === ESafeBoxDialogStage.INIT_PASSWORD" class="init-password-wrapper">
        <PanTooltips :visible="initState.passwordToolTipVisible" :content="initState.toolTipContent">
          <Input class="password-input" type="password" v-model="initState.password" placeholder="请输入6-16位密码" @keydown.enter="handleConfirm" />
        </PanTooltips>

        <PanTooltips :visible="initState.confirmPasswordToolTipVisible" :content="initState.toolTipContent">
          <Input class="password-input" type="password" v-model="initState.confirmPassword" placeholder="再次输入密码" @keydown.enter="handleConfirm" />
        </PanTooltips>
      </div>
    </div>

    <template #actions>
      <div class="dialog-actions">
        <div
          class="text-btn"
          :class="{
            'is-visible': !!textButtonText
          }"
          @click="handleTextButtonClick"
        >
          {{ textButtonText }}
        </div>

        <div class="right-actions">
          <Button size="default" variant="secondary" @click="handleCancel">
            取消
          </Button>

          <Button size="default" variant="default" @click="handleConfirm">
            确定
          </Button>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  font-size: 13px;
  color: var(--font-font-1);

  .tips {
    color: var(--font-font-2);
    margin: 8px 0;
  }

  .check-password-wrapper {
    display: flex;
    flex-direction: column;

    .password-input {
      margin: 8px 0;
      width: 100%;
    }
  }

  .forget-password-wrapper {
    display: flex;
    flex-direction: column;

    .phone-input,
    .code-input {
      margin: 8px 0;
      width: 100%;
    }

    .code-text-btn {
      color: var(--font-font-2);
      cursor: pointer;

      &:hover {
        color: var(--primary-primary-default);
      }

      &.is-disabled {
        color: var(--font-font-4) !important;
        cursor: default;
      }
    }
  }

  .init-password-wrapper {
    display: flex;
    flex-direction: column;

    .password-input {
      margin: 8px 0;
      width: 100%;
    }
  }
}

.dialog-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .text-btn {
    color: var(--primary-primary-default);
    font-size: 13px;
    cursor: pointer;
    visibility: hidden;

    &.is-visible {
      visibility: visible;
    }
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    button {
      min-width: 84px;
    }
  }

  .is-warning {
    color: var(--functional-error-default);
  }
}
</style>
