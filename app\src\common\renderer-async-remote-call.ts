/**
 * @description 用于渲染进程调用后台进程相关信息的接口
 */

import { EventEmitter } from 'events';
import { Logger } from '@root/common/logger';
import * as asyncRemote from '@xunlei/async-remote';

const logger = new Logger({ tag: 'async-remote-call' })

export type RPCShell = { openItem: (path: string) => void };
export type RPCMenu = { buildFromTemplate: (...args: any[]) => Promise<any>; setStyle: (...args: any[]) => void };
export type RPCDialog = { showOpenDialog: (...args: any[]) => Promise<any> };
export type RPCApp = { getName: () => Promise<string>; getVersion: () => Promise<string> };
export type RPCProcess = { argv: string[] };
export class AsyncRemoteCall extends EventEmitter {
  private static instance: AsyncRemoteCall;
  /**
   * @description 渲染进程用到的
   */
  constructor() {
    super();
    this.appName = undefined;
    this.appVersion = undefined;

    if (process.type !== 'renderer') {
      logger.warn('can not import "renderer-process-call" module in non-renderer process', process.type);
    }
  }

  public static GetInstance(): AsyncRemoteCall {
    if (!AsyncRemoteCall.instance) {
      if (global.RendererAsyncRemoteCallInstance) {
        AsyncRemoteCall.instance = global.RendererAsyncRemoteCallInstance;
      } else {
        AsyncRemoteCall.instance = new AsyncRemoteCall();
        global.RendererAsyncRemoteCallInstance = AsyncRemoteCall.instance;
      }
    }
    return AsyncRemoteCall.instance;
  }
  public async getAppName(): Promise<string> {
    if (undefined === this.appName) {
      const appObj: RPCApp = await this.getApp();
      this.appName = await appObj.getName();
    }
    return this.appName;
  }

  public async getAppVersion(): Promise<string> {
    if (undefined === this.appVersion) {
      const appObj: RPCApp = await this.getApp();
      this.appVersion = await appObj.getVersion();
    }
    return this.appVersion;
  }

  /**
   * @description Need Update Everytime
   */

  public async getProcess(): Promise<RPCProcess> {
    return asyncRemote.global.process.__resolve();
  }

  public async getIpcMain(): Promise<any> {
    return this.getCurrentObject('ipcMain');
  }

  public async getDialog(): Promise<RPCDialog> {
    return this.getCurrentObject('dialog');
  }

  public async getApp(): Promise<RPCApp> {
    return this.getCurrentObject('app');
  }

  public async getShell(): Promise<RPCShell> {
    return this.getCurrentObject('shell');
  }

  public async getMenu(): Promise<RPCMenu> {
    return this.getCurrentObject('Menu');
  }

  public async getScreen(): Promise<any> {
    return this.getCurrentObject('screen');
  }

  public async getBrowserWindow(): Promise<any> {
    return this.getCurrentObject('BrowserWindow');
  }

  public async getWebContents(): Promise<any> {
    return this.getCurrentObject('webContents');
  }

  public async getGlobalShortcut(): Promise<any> {
    return this.getCurrentObject('globalShortcut');
  }

  public async getCurrentWebContents(): Promise<any> {
    let currentWebContents: any = this.mapObj.get('currentWebContents');
    if (undefined === currentWebContents) {
      if (this.mapObjIniting.get('currentWebContents')) {
        currentWebContents = await new Promise<void>(
          async (resolve: (currentWebContents: any) => void): Promise<void> => {
            this.on('OnInitCurrentWebContents', (currentWebContents: any) => {
              resolve(currentWebContents);
            });
          }
        );
      } else {
        this.mapObjIniting.set('currentWebContents', true);
        currentWebContents = await asyncRemote.getCurrentWebContents().__resolve();
        this.mapObjIniting.set('currentWebContents', false);
        this.emit('OnInitCurrentWebContents', currentWebContents);
        const callbacks: Function[] = this.listeners('OnInitCurrentWebContents');
        callbacks.forEach((callback: Function) => {
          this.removeListener('OnInitCurrentWebContents', callback as (...args: any[]) => void);
        });
      }
      this.mapObj.set('currentWebContents', currentWebContents);
    }

    return currentWebContents;
  }

  public async getCurrentWindow(): Promise<any> {
    let currentWindow: any = this.mapObj.get('currentWindow');
    if (undefined === currentWindow) {
      if (this.mapObjIniting.get('currentWindow')) {
        currentWindow = await new Promise<void>(
          async (resolve: (currentWindow: any) => void): Promise<void> => {
            this.on('OnInitCurrentWindow', (currentWindow: any) => {
              resolve(currentWindow);
            });
          }
        );
      } else {
        this.mapObjIniting.set('currentWindow', true);
        currentWindow = await asyncRemote.getCurrentWindow().__resolve();
        this.mapObjIniting.set('currentWindow', false);
        this.emit('OnInitCurrentWindow', currentWindow);
        const callbacks: Function[] = this.listeners('OnInitCurrentWindow');
        callbacks.forEach((callback: Function) => {
          this.removeListener('OnInitCurrentWindow', callback as (...args: any[]) => void);
        });
      }
      this.mapObj.set('currentWindow', currentWindow);
    }

    return currentWindow;
  }

  public async getCurrentObject(id: string): Promise<any> {
    let obj: any = this.mapObj.get(id);
    if (obj === null || obj === undefined) {
      if (this.mapObjIniting.get(id)) {
        obj = await new Promise<void>(
          async (resolve: (currentWebContents: any) => void): Promise<void> => {
            this.on(id, (currentWebContents: any) => {
              resolve(currentWebContents);
            });
          }
        );
      } else {
        this.mapObjIniting.set(id, true);
        obj = await asyncRemote.electron[id].__resolve();
        this.mapObjIniting.set(id, false);
        this.emit(id, obj);
        const callbacks: Function[] = this.listeners(id);
        callbacks.forEach((callback: Function) => {
          this.removeListener(id, callback as (...args: any[]) => void);
        });
      }
      this.mapObj.set(id, obj);
    }

    return obj;
  }

  private mapObj: Map<string, any> = new Map();
  private mapObjIniting: Map<string, boolean> = new Map();

  private appName: string | undefined;
  private appVersion: string | undefined;
}

// export const asyncRemoteCall: AsyncRemoteCall = new AsyncRemoteCall();


// export class AsyncRemoteCall {
//   private static instance: AsyncRemoteCall;

//   public static GetInstance(): AsyncRemoteCall {
//     if (!AsyncRemoteCall.instance) {
//       if (global.RendererAsyncRemoteCallInstance) {
//         AsyncRemoteCall.instance = global.RendererAsyncRemoteCallInstance;
//       } else {
//         AsyncRemoteCall.instance = new AsyncRemoteCall();
//         global.RendererAsyncRemoteCallInstance = AsyncRemoteCall.instance;
//       }
//     }
//     return AsyncRemoteCall.instance;
//   }

//   test() {
//     console.log('AsyncRemoteCall test');
//   }
// }