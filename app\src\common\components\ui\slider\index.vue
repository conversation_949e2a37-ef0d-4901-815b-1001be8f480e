<script setup lang="ts">
import { <PERSON>lider<PERSON><PERSON><PERSON>, SliderRoot, SliderThumb, SliderTrack } from 'reka-ui'
import { onMounted, ref, watch } from 'vue'
import Tooltip from '@root/common/components/ui/tooltip/index.vue'

export interface Segment {
  start: number
  end: number
  id: string
}

// 组件属性
interface Props {
  modelValue: number
  segments: Segment[]
  step?: number
  disabled?: boolean
  labels: string[]
  showInput?: boolean
  unit?: string
}

const props = withDefaults(defineProps<Props>(), {
  step: 1,
  disabled: false,
  showInput: false,
  unit: '',
})

const rightTooltipText = ref('')

// 事件
const emit = defineEmits<{
  'update:modelValue': [value: number]
}>()

// 内部值 - 使用0-100的标准化值
const normalizedValue = ref([0])

// 输入框的本地值
const inputValue = ref(props.modelValue.toString())

// 初始化标准化值
const initializeNormalizedValue = () => {
  normalizedValue.value = [valueToNormalized(props.modelValue)]
  inputValue.value = props.modelValue.toString()
}

onMounted(() => {
  initializeNormalizedValue()
})


// 将实际值转换为标准化值 (0-100)
const valueToNormalized = (value: number) => {
  const segment = props.segments.find(s => value >= s.start && value <= s.end)
  if (!segment) return 0

  const segmentIndex = props.segments.findIndex(s => s.id === segment.id)
  const segmentCount = props.segments.length
  const segmentVisualWidth = 100 / segmentCount
  const segmentVisualStart = (segmentIndex / segmentCount) * 100

  const segmentProgress = (value - segment.start) / (segment.end - segment.start)
  return segmentVisualStart + (segmentVisualWidth * segmentProgress)
}

// 将标准化值转换为实际值
const normalizedToValue = (normalized: number) => {
  const segmentCount = props.segments.length
  const segmentIndex = Math.floor((normalized / 100) * segmentCount)
  const segment = props.segments[segmentIndex]

  if (!segment) return props.segments[segmentCount - 1].end

  const segmentVisualWidth = 100 / segmentCount
  const segmentVisualStart = (segmentIndex / segmentCount) * 100
  const segmentVisualProgress = Math.max(0, Math.min(1, (normalized - segmentVisualStart) / segmentVisualWidth))

  return Math.round(segment.start + (segment.end - segment.start) * segmentVisualProgress)
}


// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  normalizedValue.value = [valueToNormalized(newValue)]
  inputValue.value = newValue.toString()
}, { immediate: true })

// 监听内部值变化
watch(normalizedValue, (newNormalized) => {
  const actualValue = normalizedToValue(newNormalized[0])
  emit('update:modelValue', actualValue)
  inputValue.value = actualValue.toString()
}, { deep: true })

watch(inputValue, (newInputValue) => {
  // 将字符串转换为数字
  const numValue = parseFloat(newInputValue)

  let clampedValue = numValue

  // 检查是否为有效数字
  if (isNaN(numValue)) {
    rightTooltipText.value = '输入值必须为数字'
    clampedValue = props.modelValue
  } else {
    // 确保值在有效范围内
    const allSegments = props.segments
    const minValue = Math.min(...allSegments.map(s => s.start))
    const maxValue = Math.max(...allSegments.map(s => s.end))

    if (numValue < minValue) {
      rightTooltipText.value = `输入值必须大于等于${minValue}`
      clampedValue = minValue
    } else if (numValue > maxValue) {
      rightTooltipText.value = `输入值必须小于等于${maxValue}`
      clampedValue = maxValue
    }
  }

  if (rightTooltipText.value) {
    setTimeout(() => {
      rightTooltipText.value = ''
    }, 1000)
  }

  // 更新标准化值
  normalizedValue.value = [valueToNormalized(clampedValue)]

  emit('update:modelValue', clampedValue)
}, { immediate: true })


</script>

<template>
  <div class="SliderContainer">
    <div class="SliderRootContainer">
      <SliderRoot v-model="normalizedValue" class="SliderRoot" :max="100" :step="1" :min="0" :disabled="disabled">
        <SliderTrack class="SliderTrack">
          <SliderRange class="SliderRange" />
        </SliderTrack>
        <SliderThumb class="SliderThumb" aria-label="Volume" v-if="!disabled" />
      </SliderRoot>
      <div v-if="labels.length > 0" class="SliderLabels">
        <div v-for="label in labels" :key="label">
          {{ label }}
        </div>
      </div>
    </div>
    <div v-if="showInput" class="SliderInputContainer">
      <Tooltip :default-open="!!rightTooltipText" :side="'left'" :show-arrow="true" contentClass="slider-tip-content"
        :auto-open="false" arrowClass="slider-tip-arrow">
        <template #trigger>
          <xl-input v-model="inputValue" :style="{ width: '72px', height: '32px' }" />
        </template>
        <template #content v-if="rightTooltipText">
          {{ rightTooltipText }}
        </template>
      </Tooltip>
      <span v-if="unit">{{ unit }}</span>
    </div>
  </div>

</template>

<style lang="css" scoped>
.SliderContainer {
  display: flex;
  gap: 40px;
  align-items: center;
}

.SliderRootContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 0 13px;
}

.SliderLabels {
  display: flex;
  justify-content: space-between;
  color: var(--font-font-3, #898E97);
  font-size: 12px;
  line-height: 20px;
  margin: 0 -13px;
}

.SliderInputContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--font-font-2, #4E5769);
  font-size: 13px;
  line-height: 22px;
}

.SliderRoot {
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
  touch-action: none;
  width: 402px;
  height: 6px;
  border-radius: var(--border-radius-circle, 100px);
}

.SliderTrack {
  background-color: var(--border-border-2, #E5E6EB);
  position: relative;
  flex-grow: 1;
  border-radius: var(--border-radius-circle, 100px);
  height: 6px;
}

.SliderRange {
  position: absolute;
  background-color: var(--primary-primary-hover, #488BF7);
  border-radius: 9999px;
  height: 100%;
}

.SliderRange[data-disabled] {
  background-color: var(--primary-primary-disabled, #97C4FB);
}

.SliderThumb {
  display: block;
  width: 13px;
  height: 13px;
  background-color: var(--font-font-light, #FFF);
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.14), 0px 0px 1px rgba(0, 0, 0, 0.16);
  border-radius: 100%;
}

.SliderThumb:hover {
  cursor: pointer;
}

.SliderThumb:focus {
  outline: none;
}
</style>

<style lang="scss">
.slider-tip-content {
  background: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
  height: 32px !important;
  padding: 0 12px !important;
  color: var(--white-white-900, #FFF) !important;
  line-height: 20px !important;
}

.slider-tip-arrow {
  fill: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
}
</style>