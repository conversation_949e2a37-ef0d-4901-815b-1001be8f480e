<script setup lang="ts">
import trashEmpty from '@root/common/assets/img/icons/trash-empty.png'

const props = defineProps<{
  type: 'download' | 'cloud'
}>()

</script>

<template>
  <div class="trash-empty-container">
    <img :src="trashEmpty" alt="trash-empty" />

    <span class="trash-empty-title">{{ type === 'download' ? '暂无下载任务' : '暂无云盘文件' }}</span>
  </div>
</template>

<style lang="scss" scoped>
.trash-empty {
  &-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    width: 100%;
    padding-top: 131px;

    img {
      width: 140px;
      height: 140px;
    }
  }

  &-title {
    margin-top: 18px;
    font-size: 13px;
    line-height: 22px;
    color: var(--font-font-1, #272E3B);
  }
}
</style>
