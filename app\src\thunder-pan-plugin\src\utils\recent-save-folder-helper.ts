// 插件和客户端设置的维护

/*
注意:===================
现在不依赖保存的id, 使用 searchFileTreePathByName 根据目录名称获取数据库最新的id

基本逻辑

下载本地目录 客户端初始化
云盘下载(云添加)目录 由云盘插件初始化,

下载转存目录，修改的时候需要调用方法 IpcSetDefaultSavePath，展示需要读取config，ThunderPanPlugin.defaultSavePath

下载转存目录清除, 调用方法 IpcClearRecentFolder

切换用户，读取配置，重新设置最近转存目录(ThunderPanPlugin.savePaths)和云盘下载(云添加)目录(ThunderPanPlugin.defaultSavePath)
 */
import { loadJsonFile } from 'load-json-file'
import { writeJsonFile } from 'write-json-file'
import { config } from '@root/common/config/config'
import { GetProfilesPath } from '@root/common/xxx-node-path';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { IPC_API_NAME } from '@root/common/thunder-pan-manager/common/ipc-define';
import { useUserStore } from '@/store/user-store';
import { UserHelper } from './user-helper';

const profilesDir = GetProfilesPath()

export interface IRecentDownloadPathData {
  id: string
  name: string
}

export class RecentSaveFolderHelper {
  private static _instance: RecentSaveFolderHelper;

  static getInstance () {
    if (RecentSaveFolderHelper._instance) return RecentSaveFolderHelper._instance;
    RecentSaveFolderHelper._instance = new RecentSaveFolderHelper();
    return RecentSaveFolderHelper._instance;
  }

  init() {
    this._registerIPC()
  }

  async getRecentSaveFolders (): Promise<IRecentDownloadPathData[]> {
    await UserHelper.waitUserSignin()

    const { userStoreState } = useUserStore()
    const userId = userStoreState.curUser.userId

    let userNetDiskPaths: IRecentDownloadPathData[] = []
    if (userId) {
      try {
        userNetDiskPaths = await loadJsonFile(`${profilesDir}/pan-plugin/config/recent-folder/${userId}.json`) || []
      } catch (error) {
        userNetDiskPaths = []
      }
    }
    return userNetDiskPaths
  }

  async getDefaultSaveFolder (): Promise<IRecentDownloadPathData> {
    await UserHelper.waitUserSignin()

    const { userStoreState } = useUserStore()
    const userId = userStoreState.curUser.userId

    if (userId) {
      try {
        const result = await loadJsonFile(`${profilesDir}/pan-plugin/config/default-save-folder/${userId}.json`) as IRecentDownloadPathData
        if (result.id === '') {
          return { name: '我的云盘', id: '' }
        } else {
          return result
        }
      } catch (error) {
        return { name: '我的云盘', id: '' }
      }
    }
    return { name: '我的云盘', id: '' }
  }

  async setDefaultSaveFolder (defaultPath: IRecentDownloadPathData): Promise<void> {
    await UserHelper.waitUserSignin()

    const { userStoreState } = useUserStore()
    const userId = userStoreState.curUser.userId

    if (userId) {
      writeJsonFile(`${profilesDir}/pan-plugin/config/default-save-folder/${userId}.json`, defaultPath).catch((error) => {
        console.error(error)
      })
      // 客户端设置中心保存路径居然只支持名字单个string值，故插件维护另一份包含id的值
      config.setValue('ThunderPanPlugin', 'defaultSavePath', defaultPath.name)
    }
  }

  async setRecentSaveFolders (folders: IRecentDownloadPathData[]) {
    await UserHelper.waitUserSignin()

    const { userStoreState } = useUserStore()
    const userId = userStoreState.curUser.userId

    if (userId) {
      await writeJsonFile(`${profilesDir}/pan-plugin/config/recent-folder/${userId}.json`, folders).catch((error) => {
        console.error(error)
      })
    }
    // 客户端设置中心转存路径居然只支持名字单个string值 [string, string]，故插件助其维护
    config.setValue('ThunderPanPlugin', 'savePaths', folders.map(path => path.name))
  }

  async addRecentFolder (defaultPath: IRecentDownloadPathData): Promise<void> {
    let userNetDiskPaths = await this.getRecentSaveFolders()
    const index = userNetDiskPaths.findIndex(el => el.name === defaultPath.name)
    if (index > -1) {
      userNetDiskPaths.splice(index, 1)
    }
    userNetDiskPaths.unshift(defaultPath)
    userNetDiskPaths = userNetDiskPaths.slice(0, 5)
    // 常用路径保存五个，区分用户
    this.setRecentSaveFolders(userNetDiskPaths)
  }

  async reSyncUserPanSettingPath () {
    // 登录之后初始化设置中心  ThunderPanPlugin.savePaths ThunderPanPlugin.defaultSavePath
    const folders = await this.getRecentSaveFolders()
    this.setRecentSaveFolders(folders)
    const defaultFolder = await this.getDefaultSaveFolder()
    this.setDefaultSaveFolder(defaultFolder)
  }

  private _registerIPC () {
    // 与主进程通一个 ipc context，直接注册
    client.registerFunctions({
      [IPC_API_NAME.RECENT_SAVE_GET_FOLDERS]: (_ctx: unknown) => {
        return this.getRecentSaveFolders()
      },
      [IPC_API_NAME.RECENT_SAVE_SET_FOLDERS]: (_ctx: unknown, folders: IRecentDownloadPathData[]) => {
        return this.setRecentSaveFolders(folders)
      },
      [IPC_API_NAME.RECENT_SAVE_DELETE_FOLDER]: async (_ctx: unknown, folderIds: string[]) => {
        const oldPaths = await this.getRecentSaveFolders()
        const newPaths = oldPaths.filter(path => !folderIds.includes(path.id))

        return this.setRecentSaveFolders(newPaths)
      },
      [IPC_API_NAME.RECENT_SAVE_GET_DEFAULT]: (_ctx: unknown) => {
        return this.getDefaultSaveFolder()
      },
      [IPC_API_NAME.RECENT_SAVE_SET_DEFAULT]: (_ctx: unknown, defaultPath: IRecentDownloadPathData) => {
        return this.setDefaultSaveFolder(defaultPath)
      },
      [IPC_API_NAME.RECENT_SAVE_CLEAR_FOLDERS]: (_ctx: unknown) => {
        return this.setRecentSaveFolders([])
      },
    })
  }
}

