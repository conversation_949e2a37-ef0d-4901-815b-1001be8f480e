#pragma once

class CFileHelper
{
public:
	static HRESULT WriteDataToFile(LPCTSTR lpszFilePath, void* vpData, DWORD dwDataSize, DWORD* pdwDataWirtten = NULL);

	static HRESULT GetFileSize(LPCTSTR lpszFilePath,  DWORD& dwFileSize);
	static HRESULT IsFileExists(LPCTSTR lpszFilePath, BOOL& bExists);
	static HRESULT IsDirectoryExists(LPCTSTR lpszDirpath, BOOL& bExists);
	static HRESULT CreateDirectory(LPCTSTR lpszDirpath);

	static HRESULT GetFileVersion(LPCTSTR lpszFilePath, int& nVer1, int& nVer2, int& nVer3, int& nVer4);
	static HRESULT GetFileVersion(LPCTSTR lpszFilePath, std::wstring& strVersion);

	static HRESULT CleanDir(LPCTSTR lpszDirPath, StringList* pListExcludeFileName = NULL, BOOL bDeleteOnReboot = FALSE, BOOL bRemoveDir =FALSE);
	static HRESULT DeleteFile(LPCTSTR lpszFilePath, BOOL bDeleteOnReboot = FALSE);

	static HRESULT CopyDir(LPCTSTR lpszSrcName, LPCTSTR lpszDesName, StringList* pListExcludeFileName = NULL, BOOL bOverWrite = TRUE);
	static HRESULT CopyFile(LPCTSTR lpszSrcName, LPCTSTR lpszDesName, BOOL bFailIfExists = FALSE);

	//static HRESULT MoveDir(LPCTSTR lpszSrcDir, LPCTSTR lpszDesDir, std::vector<std::wstring>& DirectCoverDir);
};
