import { IExtendDriveFile } from '@root/common/thunder-pan-manager/pan-sdk/types'
import { createGlobalState } from '@vueuse/core'
import { computed, readonly, ref } from 'vue'

export type TPanRoute = {
  id: string
  title: string
} & IExtendDriveFile

export const useTransferRouterStore = createGlobalState(() => {
  function getRootRouter () {
    return {
      id: '',
      title: '全部文件',
      writable: true,
    }
  }

  function resetRouterList () {
    routerList_ref.value = [
      getRootRouter(),
    ]
  }
  const routerList_ref = ref<TPanRoute[]>([])
  resetRouterList()

  const isEnterFolder = computed(() => {
    return routerList_ref.value.length > 1
  })

  const isInSafeBoxFolder = computed(() => {
    return routerList_ref.value.length > 1 && routerList_ref.value[1].folder_type === 'SAFE'
  })

  const isInFavoriteFolder = computed(() => {
    return routerList_ref.value.length > 1 && routerList_ref.value[1].folder_type === 'FAVORITE'
  })

  const isInPrivilegeFolder = computed(() => {
    return routerList_ref.value.length > 1 && routerList_ref.value[1].folder_type === 'KOC_RES'
  })

  const currentParentFile = computed(() => {
    return routerList_ref.value.slice(-1)[0]
  })

  function setRouterList (routeList: TPanRoute[], isConcatRoot = false) {
    if (isConcatRoot) {
      routerList_ref.value = [getRootRouter(), ...routeList]
    } else {
      routerList_ref.value = routeList
    }
  }

  return {
    routerList_computed: readonly(routerList_ref),
    isEnterFolder,
    isInSafeBoxFolder,
    isInFavoriteFolder,
    isInPrivilegeFolder,
    currentParentFile,
    setRouterList,
    resetRouterList,
  }
})
