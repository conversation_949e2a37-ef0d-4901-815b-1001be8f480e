#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 自动构建脚本已启动');
console.log('⏰ 每分钟执行一次 npm run build');
console.log('📁 工作目录:', process.cwd());
console.log('');

let buildCount = 0;

function runBuild() {
  buildCount++;
  const timestamp = new Date().toLocaleString('zh-CN');
  
  console.log(`\n🔄 [${timestamp}] 开始第 ${buildCount} 次构建...`);
  
  const buildProcess = spawn('npm', ['run', 'build'], {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd()
  });

  buildProcess.on('close', (code) => {
    const status = code === 0 ? '✅ 成功' : '❌ 失败';
    console.log(`\n${status} [${timestamp}] 第 ${buildCount} 次构建完成 (退出码: ${code})`);
    
    if (code !== 0) {
      console.log('⚠️  构建失败，将在下次定时时重试');
    }
  });

  buildProcess.on('error', (error) => {
    console.error(`\n💥 [${timestamp}] 构建进程出错:`, error.message);
  });
}

// 立即执行一次构建
runBuild();

// 设置定时器，每分钟执行一次
setInterval(() => {
  runBuild();
}, 60 * 2000); // 60秒 = 1分钟

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在停止自动构建...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在停止自动构建...');
  process.exit(0);
});

console.log('�� 按 Ctrl+C 停止自动构建'); 