# function for module basic configuration
# function name : AppendModulesBasic
# parameter: name, the module's name; group, module group; ${arg3} is group_link_libs
Function(AppendModulesBasic name group folders)

string(TOUPPER ${name} upper_name)
string(COMPARE EQUAL "${group}" "" result)
if (result)
    set (group ${name})
endif()

if (NOT PROJECT_VERSION)
	set (PROJECT_VERSION_MAJOR ${VBL_VERSION_MAJOR})
	set (PROJECT_VERSION_MINOR ${VBL_VERSION_MINOR})
	set (PROJECT_VERSION_PATCH ${VBL_VERSION_PATCH})
	set (PROJECT_VERSION_TWEAK ${VBL_VERSION_TWEAK})
endif ()
if (NOT PROJECT_VERSION_TWEAK)
	set (PROJECT_VERSION_TWEAK ${VBL_VERSION_TWEAK})
endif ()

string (REGEX REPLACE "(.+)/${name}" "\\1" prj_folder Modules/${group})
set(prj_root ${CMAKE_CURRENT_LIST_DIR})

# log current project revision(git rivision) for invoke ZERO_CHECK target 
VBL_LOG_PROJECT_REVISION_INFO(modules/${group})

# config windows resource file
set(prj_rcFile)
set (prj_resource_in_File)
IF (WIN32)
	if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${name}.rc)
		set(prj_rcFile ${CMAKE_CURRENT_SOURCE_DIR}/${name}.rc)
	else()
		if (NOT EXISTS ${prj_root}/${name}.rc.in)
			generate_resource_file(${name} ${CMAKE_SOURCE_DIR}/tools/cmake/general/MSVCResourceTemplate.rc.in ${prj_root}/${name}.rc.in)
		endif ()
		
		if (EXISTS ${prj_root}/${name}.rc.in)
			set (prj_resource_in_File ${prj_root}/${name}.rc.in)
			VBL_LOG_INFO ("Configure ${name} ${name}.rc file")
			string(TIMESTAMP _YEAR "%Y")
			configure_file(
			  ${prj_resource_in_File}
			  ${CMAKE_CURRENT_BINARY_DIR}/${name}.rc
			  @ONLY)
			unset (_YEAR)
		endif ()		
		if (EXISTS ${CMAKE_CURRENT_BINARY_DIR}/${name}.rc)
			set(prj_rcFile ${CMAKE_CURRENT_BINARY_DIR}/${name}.rc)
		endif ()
	endif ()	
endif ()

# generate version file
if (NOT EXISTS ${prj_root}/version.h.in)
	generate_version_file(${upper_name} ${CMAKE_SOURCE_DIR}/tools/cmake/general/VersionTemplate.h.in ${prj_root}/version.h.in)
endif ()

if (EXISTS ${prj_root}/version.h.in)
	set (prj_version_in_File ${prj_root}/version.h.in)
	VBL_LOG_INFO ("Configure ${name} version.h file")
	configure_file(
	  ${prj_version_in_File}
	  ${CMAKE_CURRENT_BINARY_DIR}/version.h
	  @ONLY)
endif ()

set(prj_version_File)
if (EXISTS ${CMAKE_CURRENT_BINARY_DIR}/version.h)
	set(prj_version_File ${CMAKE_CURRENT_BINARY_DIR}/version.h)
	include_directories(${CMAKE_CURRENT_BINARY_DIR})
endif ()

set(include_root ${CMAKE_SOURCE_DIR}/Include)
set(interface_root ${CMAKE_SOURCE_DIR}/Interface)
set(internal_root ${CMAKE_SOURCE_DIR}/Internal)

SET(prj_Include_Common ${CMAKE_SOURCE_DIR}/Include/Common)
if (${name}_alias)
	# for include/interface(e.g FMMeeTimelineUX's alias is TimelineUX)
	set (alias_group ${group}/../${${name}_alias})	
	VBL_LOG_INFO("${name}'s alias_group is ${alias_group}")
	
	get_filename_component(prj_Include ${include_root}/${alias_group} ABSOLUTE)
	get_filename_component(prj_Interface ${interface_root}/${alias_group} ABSOLUTE)	
	get_filename_component(prj_Internal ${internal_root}/${alias_group} ABSOLUTE)	
else ()
	SET(prj_Include ${include_root}/${group})
	SET(prj_Interface ${interface_root}/${group})
	SET(prj_Internal ${internal_root}/${group})
endif ()

set(prj_IncludeCommonFiles "")
set(prj_IncludeFiles "")
set(prj_InterfaceFiles "")
set(prj_InternalFiles "")
set(prj_headersFiles "")
set(prj_srcFiles "")
FILE(GLOB_RECURSE prj_IncludeCommonFiles ${prj_Include_Common}/*.h ${prj_Include_Common}/*.hpp)
FILE(GLOB_RECURSE prj_IncludeFiles ${prj_Include}/*.h ${prj_Include}/*.hpp)
FILE(GLOB_RECURSE prj_InterfaceFiles ${prj_Interface}/*.h ${prj_Interface}/*.hpp)
FILE(GLOB_RECURSE prj_InternalFiles ${prj_Internal}/*.h ${prj_Internal}/*.hpp)
if (NOT folders) 
	FILE(GLOB_RECURSE prj_headersFiles ${prj_root}/*.h ${prj_root}/*.hpp)
	FILE(GLOB_RECURSE prj_srcFiles ${prj_root}/*.c ${prj_root}/*.cpp)
else ()
	foreach(folder ${folders})
		FILE(GLOB_RECURSE prj_headersFiles_temp ${prj_root}/${folder}/*.h ${prj_root}/${folder}/*.hpp)
		list(APPEND prj_headersFiles ${prj_headersFiles_temp})
	endforeach()
	FILE(GLOB prj_headersFiles_temp ${prj_root}/*.h ${prj_root}/*.hpp)
	list(APPEND prj_headersFiles ${prj_headersFiles_temp})

	foreach(folder ${folders})
		FILE(GLOB_RECURSE prj_srcFiles_temp ${prj_root}/${folder}/*.c ${prj_root}/${folder}/*.cpp)
		list(APPEND prj_srcFiles ${prj_srcFiles_temp})
	endforeach()
		FILE(GLOB prj_srcFiles_temp ${prj_root}/*.c ${prj_root}/*.cpp)
		list(APPEND prj_srcFiles ${prj_srcFiles_temp})
endif()

include_directories(${prj_root})
    
add_library(${name} ${VBL_LIBRARY_MODE}
    ${prj_IncludeFiles} 
    ${prj_IncludeCommonFiles} 
    ${prj_InterfaceFiles} 
	${prj_InternalFiles} 
    ${prj_headersFiles} 
    ${prj_srcFiles} 
    ${prj_rcFile}
    ${prj_resource_in_File}
	${prj_version_File}
    ${prj_natvis_File}
)

add_library(VBL::${name} ALIAS ${name})

IF (WIN32)
    set(exports_def "")
    string(TOUPPER ${name}_EXPORTS exports_def)
    target_compile_definitions(${name} PRIVATE ${exports_def})
	target_compile_options(${name} PRIVATE "/MP")
ENDIF ()

if (VBL_ENABLE_COLLECT_PERFORMANCE)
	target_compile_definitions(${name} PRIVATE VBL_COLLECT_PERFORMANCE)
endif ()

string (REGEX REPLACE "(.+)/${name}" "\\1" group_ew_name ${group})

if (NOT "${name}" STREQUAL "Adapter")
	target_include_directories(${name} 
		PUBLIC  
		$<BUILD_INTERFACE:${prj_Include}>  
		$<INSTALL_INTERFACE:${prj_include_install_dir}/${group}>
		PUBLIC  
		$<BUILD_INTERFACE:${prj_Include_Common}>  
		$<INSTALL_INTERFACE:${prj_include_install_dir}/Common>
		PUBLIC  
		$<BUILD_INTERFACE:${prj_Interface}>  
		$<INSTALL_INTERFACE:${prj_include_install_dir}/${group}>
		PUBLIC  
		$<BUILD_INTERFACE:${prj_Internal}>  
		$<INSTALL_INTERFACE:${prj_include_install_dir}/${group}>
		PUBLIC  
		$<BUILD_INTERFACE:${include_root}> 
		$<INSTALL_INTERFACE:${prj_include_install_dir}>
		PUBLIC  
		$<BUILD_INTERFACE:${include_root}/${group_ew_name}> 
		$<INSTALL_INTERFACE:${prj_include_install_dir}/${group_ew_name}>
	)
else()
	target_include_directories(${name} 
		PUBLIC  
		$<BUILD_INTERFACE:${prj_Include}>  
		PUBLIC  
		$<BUILD_INTERFACE:${prj_Include_Common}>  
		$<INSTALL_INTERFACE:${prj_include_install_dir}/Common>
		PUBLIC  
		$<BUILD_INTERFACE:${prj_Interface}>  
		PUBLIC  
		$<BUILD_INTERFACE:${prj_Internal}>  
		PUBLIC  
		$<BUILD_INTERFACE:${include_root}> 
		$<INSTALL_INTERFACE:${prj_include_install_dir}>
		PUBLIC  
		$<BUILD_INTERFACE:${include_root}/${group_ew_name}> 
	)
endif()
unset(group_ew_name)

# remove duplicate items and self target
if (group_public_link_libs)
    list (REMOVE_DUPLICATES group_public_link_libs)
    list (REMOVE_ITEM group_public_link_libs VBL::${name} ${name})
endif ()

if (group_private_link_libs)
    list (REMOVE_DUPLICATES group_private_link_libs)
    list (REMOVE_ITEM group_private_link_libs VBL::${name} ${name})
endif ()

if (group_interface_link_libs)
    list (REMOVE_DUPLICATES group_interface_link_libs)
    list (REMOVE_ITEM group_interface_link_libs VBL::${name} ${name})
endif ()

target_link_libraries (${name}
    PUBLIC ${group_public_link_libs}
    PRIVATE ${group_private_link_libs}
    INTERFACE ${group_interface_link_libs}
)

IF (WIN32)
	# Resolve windows.h and algorithm min/max macro conflict
    target_compile_definitions(${name}
	PUBLIC NOMINMAX)
ENDIF ()


SET_PROPERTY(TARGET ${name} PROPERTY FOLDER ${prj_folder})

# filter 
if (VBL_use_structure_source_group STREQUAL "ON")
    _tree_source_group(${include_root}/.. "${prj_IncludeFiles}")
    _tree_source_group(${include_root}/.. "${prj_IncludeCommonFiles}")
    _tree_source_group(${interface_root}/.. "${prj_InterfaceFiles}")
	_tree_source_group(${internal_root}/.. "${prj_InternalFiles}")
    _tree_source_group(${prj_root}/.. "${prj_srcFiles}")
    _tree_source_group(${prj_root}/.. "${prj_headersFiles}")
    # _tree_source_group(${prj_root}/.. "${prj_rcFile}")
    _tree_source_group(${prj_root}/.. "${prj_resource_in_File}")
else ()
    source_group("Include\\${name}" FILES ${prj_IncludeFiles})
    source_group("Include\\Common" FILES ${prj_IncludeCommonFiles})
    source_group("Interface" FILES ${prj_InterfaceFiles})
	source_group("Internal" FILES ${prj_InternalFiles})
    source_group("Source Files" FILES ${prj_srcFiles})
    source_group("Header Files" FILES ${prj_headersFiles})
    # source_group("" FILES ${prj_rcFile})
    source_group("" FILES ${prj_resource_in_File})
endif ()
unset (prj_rcFile)

# install  
if (VBL_ENABLE_install)
	FILE(GLOB_RECURSE prj_export_headerFile ${prj_Include}/*Exports.h ${prj_Include}/*Exports.hpp)
	if (NOT "${group}" STREQUAL "Adapter")
		SET(prj_install_headerFile ${prj_export_headerFile} ${prj_InterfaceFiles})
	endif()
	set_target_properties(${name} PROPERTIES PUBLIC_HEADER "${prj_install_headerFile}")
	install(TARGETS ${name}
			OPTIONAL
			EXPORT ${name}
			LIBRARY DESTINATION ${prj_lib_install_dir}
			ARCHIVE DESTINATION ${prj_lib_install_dir}
			RUNTIME DESTINATION ${prj_binary_install_dir}
			PUBLIC_HEADER DESTINATION ${prj_include_install_dir}/${group}
			)

	# install window's pdb file
	IF (MSVC)
		INSTALL ( FILES ${CMAKE_PDB_OUTPUT_DIRECTORY}/Debug/${name}.pdb DESTINATION ${prj_pdb_install_dir} CONFIGURATIONS Debug OPTIONAL)
		INSTALL ( FILES ${CMAKE_PDB_OUTPUT_DIRECTORY}/Release/${name}.pdb DESTINATION ${prj_pdb_install_dir} CONFIGURATIONS Release OPTIONAL)
	ELSE ()
		set(symbolnamelib lib${name}.dylib.dSYM)
		INSTALL ( DIRECTORY ${CMAKE_PDB_OUTPUT_DIRECTORY}/Release/${symbolnamelib} DESTINATION ${prj_dsym_install_dir} CONFIGURATIONS Release OPTIONAL)
	ENDIF ()

	install(EXPORT ${name} FILE ${name}Config.cmake NAMESPACE VBL:: DESTINATION ${prj_lib_install_dir}/cmake/${name} OPTIONAL)
endif ()

# module_test
if (VBL_ENABLE_TESTS)
    set(module_test_root "${CMAKE_SOURCE_DIR}/tests/modules/${group}")
	
    # check and generate test
    # generate prj_Interface to module_test_root
    if (VBL_AUTO_GENERATE_TESTS)
        generate_module_test(${prj_Interface} ${module_test_root})
    endif ()
        
    if (EXISTS ${module_test_root})
        string(COMPARE EQUAL "${GTEST_INCLUDE}" "" result)
        if (result)
            LIST(APPEND CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR}/3rdparty/googletest)	
            INCLUDE(googletest)
        endif ()
        set (module_test_name ${name}_test)		
		VBL_LOG_INFO("Build ${module_test_name} from ${module_test_root}")
		
		project (${module_test_name} VERSION ${VBL_VERSION})
        VBL_LOG_PROJECT_REVISION_INFO(tests/modules/${group})
		
        FILE(GLOB_RECURSE prj_test_headers 
            ${module_test_root}/*.h 
            ${module_test_root}/*.hpp
        )
            
        FILE(GLOB_RECURSE prj_test_src 
            ${module_test_root}/*.c 
            ${module_test_root}/*.cpp
        )        

        AppendCommonSource(${name})
		# config windows resource file
		set(prj_rcFile)
		IF (WIN32)
			if (EXISTS ${module_test_root}/${module_test_name}.rc)
				set(prj_rcFile ${module_test_root}/${module_test_name}.rc)
			else()
				# generate version file
				if (NOT EXISTS  ${module_test_root}/${module_test_name}.rc.in)
					generate_resource_file(${module_test_name} ${CMAKE_SOURCE_DIR}/tools/cmake/general/MSVCResourceTemplate.rc.in ${module_test_root}/${module_test_name}.rc.in)
				endif ()
				set (prj_resource_in_File ${module_test_root}/${module_test_name}.rc.in)
				if (EXISTS ${prj_resource_in_File})
					VBL_LOG_INFO ("Configure ${module_test_name} ${module_test_name}.rc file")
					string(TIMESTAMP _YEAR "%Y")
					configure_file(
					  ${prj_resource_in_File}
					  ${CMAKE_CURRENT_BINARY_DIR}/${module_test_name}.rc
					  @ONLY)
					unset (_YEAR)
				endif ()
				
				if (EXISTS ${CMAKE_CURRENT_BINARY_DIR}/${module_test_name}.rc)
					set(prj_rcFile ${CMAKE_CURRENT_BINARY_DIR}/${module_test_name}.rc)
				endif ()
			endif ()	
		endif ()

        link_directories (${GTEST_LIB_DIR})        
        add_executable(${module_test_name} 
            ${prj_test_headers} 
            ${prj_test_src}
            ${prj_test_common_header_src} 
			${prj_rcFile}
        )           
        
        
        message("prj_test_common_header_src： ${prj_test_common_header_src}")
        target_include_directories (${module_test_name} 
            PRIVATE 
            ${GTEST_INCLUDE} 
            ${COMMONSOURCE_INCLUDE} 
        )
        message("COMMONSOURCE_INCLUDE： ${COMMONSOURCE_INCLUDE}")
        target_link_libraries (${module_test_name}
            PRIVATE
			${GTEST_LIBS}
            		${name}
			${group_private_link_libs}
			${COMMONSOURCE_LIBS}
        )
        
        # set Xcode property 
        IF (APPLE)
            set_xcode_property(${module_test_name} LD_RUNPATH_SEARCH_PATHS "@executable_path")
        ENDIF ()
        
        SET_PROPERTY(TARGET ${module_test_name} PROPERTY FOLDER "tests/${prj_folder}")
		# install 
		#if (VBL_ENABLE_install)
			#install(TARGETS ${module_test_name}
			# CONFIGURATIONS Debug # 默认只安装release版本 
				#RUNTIME DESTINATION ${prj_binary_install_dir})
		#endif ()
		
        add_test(NAME ${module_test_name} COMMAND ${module_test_name} --gtest_output=json:${CMAKE_BINARY_DIR}/vbl_gtest_output/${module_test_name}.json)
    endif()
endif()

endfunction(AppendModulesBasic)
