import { AplayerMedia } from "@root/common/player/client/aplayer-media"
import { ConfigStore } from "./ConfigStore"

export type TMediaControlStore = {
}

export type TPlayerControlStore = {
  mediaControlMap: Map<AplayerMedia['id'], TMediaControlStore>
  currVolume: number
  prevVolume: number
}

export const playerControlStore = new ConfigStore<TPlayerControlStore>({
  name: 'playerControlStore',
})