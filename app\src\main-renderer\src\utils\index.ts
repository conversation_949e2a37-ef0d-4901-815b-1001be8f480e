/** 计算进度 */
export function calculateProgress (size, download, decimalPlaces = 2) {
  if (download === 0 || size === 0) return 0
  const quotient = (size / download)*100
  return parseFloat(quotient.toFixed(decimalPlaces)) || 0
}
/** 实现isNumber方法 */
export function isNumber (num) {
  return!isNaN(num) && typeof num === 'number'
}
/** 实现isObject方法 */
export function isObject (obj) {
  return obj !== null && typeof obj === 'object'
}