#pragma once
#include "PatchDownload.h"
#include "SyncHttpsClient.h"
#include <vector>

class CUpdateConfig
{
public:
    CUpdateConfig();
    ~CUpdateConfig();
    static CUpdateConfig* GetInstance();

    void StartPost(CPatchDownload* pOwner);
    BOOL GetPatchData(PluginItem& data);
    void CleanUp();

private:
    bool ParseConfig(const std::string& strContent);

private:
    CPatchDownload* m_pOwner;
    std::vector<PluginItem> m_vecPluginList;
    CSyncHttpsClient m_syncHttpsClient;
};

#define theUpdateConfig CUpdateConfig::GetInstance()
