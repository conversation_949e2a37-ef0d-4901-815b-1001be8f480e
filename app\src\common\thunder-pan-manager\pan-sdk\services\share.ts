import { IRequestCommonConfig, IRequestClassImplement, IRequestCommonResponseT, IRequestHeader } from '..'
import { API_SHARE, Dictionary } from '../types'
import { convertEveryKeyToString } from '../utils/basic'

export interface IShareApisCommonOptions<T> {
  params?: T
  headers?: IRequestHeader
}

export interface IGetShareListParams {
  page_token?: string
  thumbnail_size?: API_SHARE.DriveImageSize
  limit?: number
}

export interface IGetRestoreListParams extends IGetShareListParams {
  filters?: any
  /**
   * 排序方式
  - ORDER_DEFAULT: 默认排序（时间倒序）
  - ORDER_REVIEW_TIME_DESC: 按浏览时间倒序
  - ORDER_UPDATE_TIME_DESC: 按更新时间倒序 更新标签 -> 转存时间
  - ORDER_UPDATE_TIME_ASC: 按更新时间正序 更新标签 -> 转存时间
   */
  order_by?: ERestoreOrder
  space?: string
  only_updated?: boolean
}

export enum ERestoreOrder {
  ORDER_DEFAULT = 'ORDER_DEFAULT',
  ORDER_REVIEW_TIME_DESC = 'ORDER_REVIEW_TIME_DESC',
  ORDER_UPDATE_TIME_DESC = 'ORDER_UPDATE_TIME_DESC',
  ORDER_UPDATE_TIME_ASC = 'ORDER_UPDATE_TIME_ASC',
}

/**
 * 获取订阅的基础信息，当前用户是否在 koc 白名单内
 */
export interface DriveGetKocInfoResponse {
  /**
   * 是否koc
   */
  is_kol: boolean
  /**
   * 订阅的 koc 数
   */
  subscribe_nums: number
}

class ShareApis {
  private host: string
  private headers: IRequestHeader
  private config: IRequestCommonConfig
  private requestFn: IRequestClassImplement

  constructor(requestFn: IRequestClassImplement, config: IRequestCommonConfig) {
    this.requestFn = requestFn
    this.config = config
    this.initHost()
    this.initHeaders()
  }

  private initHost () {
    const DRIVE_API: Dictionary = {
      test: 'http://api-alpha-drive.office.k8s.xunlei.cn/drive/v1',
      prod: 'https://api-pan.xunlei.com/drive/v1'
    }
    this.host = DRIVE_API[this.config.env || 'prod']
  }

  private  initHeaders () {
    this.headers = this.config.headers || {}
  }

  async getShareList (options: IShareApisCommonOptions<IGetShareListParams> = {}): Promise<IRequestCommonResponseT<API_SHARE.DriveListShareResponse>> {
    const url = `${this.host}/share/list`;
    const params = convertEveryKeyToString({
      space: "",
      page_token: "",
      thumbnail_size: 'SIZE_MEDIUM',
      limit: 999,
      ...options.params
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_SHARE.DriveListShareResponse>
  }

  async batchDeleteShare (options: IShareApisCommonOptions<API_SHARE.DriveBatchDeleteSharesRequest> = {}): Promise<IRequestCommonResponseT<API_SHARE.DriveBatchDeleteSharesRequest>> {
    const url = `${this.host}/share:batchDelete`;
    const data: any = {
      space: "",
      ids: [],
      ...options.params
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_SHARE.DriveBatchDeleteSharesRequest>
  }

  async createShare (options: IShareApisCommonOptions<API_SHARE.DriveCreateShareRequest> = {}): Promise<IRequestCommonResponseT<API_SHARE.DriveCreateShareResponse>> {
    const url = `${this.host}/share`;
    const data: any = {
      file_ids: [],
      share_to: 'copy',
      title: '',
      restore_limit: '-1',
      expiration_days: '-1',
      params: {
        subscribe_push: String(false)
      },
      ...options.params
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_SHARE.DriveCreateShareResponse>
  }

  async getKocInfo (options: IShareApisCommonOptions<null> = {}): Promise<IRequestCommonResponseT<DriveGetKocInfoResponse>> {
    const host = {
      test: 'http://test.api-shoulei-ssl.xunlei.com',
      prod: 'https://api-shoulei-ssl.xunlei.com'
    }[this.config.env || 'prod'];

    const url = `${host}/api/subscribe/own/info`;
    const params = {}
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<DriveGetKocInfoResponse>
  }

  /**
   * 获取转存列表
   * @param options
   * @returns
   */
  async getRestoreList (options: IShareApisCommonOptions<IGetRestoreListParams> = {}): Promise<IRequestCommonResponseT<API_SHARE.DriveRestoreListResponse>> {
    const url = `${this.host}/share/restore/list`;
    const params = convertEveryKeyToString({
      space: "",
      page_token: "",
      thumbnail_size: 'SIZE_MEDIUM',
      limit: 999,
      ...options.params
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_SHARE.DriveRestoreListResponse>
  }

  /**
   * 删除转存记录
   * @param id 转存记录 id
   * @param options
   * @returns
   */
  async deleteRestoreById (id: string, options: IShareApisCommonOptions<API_SHARE.DriveRestoreDeleteRequest> = {}): Promise<IRequestCommonResponseT<API_SHARE.DriveRestoreDeleteResponse>> {
    const url = `${this.host}/share/restore/delete`;
    const data: any = {
      space: "",
      restore_id: id,
      ...options.params
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_SHARE.DriveRestoreDeleteResponse>
  }
}

export default ShareApis