import { LifeObjectContainor } from '@root/common/life-object-containor';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import * as BaseType from '../base';
import { AplayerMedia } from '../impl/aplayer-media';

export class AplayerServerMedia {
  static init() {
    client.registerFunctions({
      AplayerMediaGetName: (context: any, id: string): string => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return '';
        }

        return media.getName();
      },
      AplayerMediaGetAttribute: (context: any, id: string): any => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return {} as any;
        }

        return media.getAttribute();
      },
      AplayerMediaGetMediaWidth: (context: any, id: string): number => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return 0;
        }

        return media.getMediaWidth();
      },
      AplayerMediaGetMediaHeight: (context: any, id: string): number => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return 0;
        }

        return media.getMediaHeight();
      },
      AplayerMediaIsTaskLocalPlay: (context: any, id: string): boolean => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return false;
        }

        return media.isTaskLocalPlay();
      },
      AplayerMediaIsPanPlay: (context: any, id: string): boolean => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return false;
        }

        return media.isPanPlay();
      },
      AplayerMediaIsTaskPlay: (context: any, id: string): boolean => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return false;
        }

        return media.isTaskPlay();
      },
      AplayerMediaGetType: (context: any, id: string): number => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return 0;
        }

        return media.getType();
      },
      AplayerMediaGetAudioTrackList: (context: any, id: string): string[] => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return [];
        }

        return media.getAudioTrackList();
      },
      AplayerMediaGetAudioTrackSelectedIndex: (context: any, id: string): number => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return 0;
        }

        return media.getAudioTrackSelectedIndex();
      },
      AplayerMediaSwitchAudioTrack: (context: any, id: string, index: number) => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return;
        }

        media.switchAudioTrack(index);
      },
      AplayerMediaIsShowRatio: (context: any, id: string): boolean => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return false;
        }

        return media.isShowRatio();
      },
      AplayerMediaGetRatioList: (context: any, id: string): any => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return [];
        }

        return media.getRatioList();
      },
      AplayerMediaGetRatioSelectedId: (context: any, id: string): string => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return '';
        }

        return media.getRatioSelectedId();
      },
      AplayerMediaSwitchRatio: (context: any, id: string, ratioId: string) => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return;
        }

        media.switchRatio(ratioId);
      },
      AplayerMediaIsChangeRatio: (context: any, id: string): boolean => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return false;
        }

        return media.isChangeRatio();
      },
      AplayerMediaGetPlayProgress: (context: any, id: string): number => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return 0;
        }

        return media.getPlayProgress();
      },
      AplayerMediaGetDuration: (context: any, id: string): number => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return 0;
        }

        return media.getDuration();
      },
      AplayerMediaProgressMoveTo: (context: any, id: string, pos: number) => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return;
        }

        media.progressMoveTo(pos);
      },
      AplayerMediaGetMediaState: (context: any, id: string): number => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return -1;
        }

        return media.getMediaState();
      },
      AplayerMediaGetMediaErrorInfo: (context: any, id: string): BaseType.PlayErrInfo => {
        let media: AplayerMedia = LifeObjectContainor.GetInstance().getObject(id);
        if (!media) {
          return { errCode: 0, errMsg: '' };
        }

        return media.getMediaErrorInfo();
      },
    });
  }
}