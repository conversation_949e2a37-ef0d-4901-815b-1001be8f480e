# Tree 树形控件

用于展示层级结构数据的树形控件，支持展开/折叠、复选框选择、自定义图标等功能。

## 功能特性

- ✅ 支持层级数据展示
- ✅ 支持展开/折叠操作（自动检测节点children并显示展开图标）
- ✅ 支持复选框选择（含半选状态）
- ✅ 支持禁用状态
- ✅ 支持自定义图标
- ✅ 支持自定义节点内容
- ✅ 支持点击和双击事件
- ✅ 支持键盘导航
- ✅ 自动缩进显示层级关系
- ✅ 响应式设计

## 基础用法

```vue
<template>
  <Tree
    :data="treeData"
    @node-click="handleNodeClick"
  />
</template>

<script setup>
import Tree from '@/components/ui/tree/tree.vue'

const treeData = [
  {
    id: '1',
    label: '一级节点1',
    children: [
      {
        id: '1-1',
        label: '二级节点1-1',
        children: [
          { id: '1-1-1', label: '三级节点1-1-1' },
          { id: '1-1-2', label: '三级节点1-1-2' },
        ],
      },
      { id: '1-2', label: '二级节点1-2' },
    ],
  },
  {
    id: '2',
    label: '一级节点2',
    children: [
      { id: '2-1', label: '二级节点2-1' },
      { id: '2-2', label: '二级节点2-2' },
    ],
  },
]

const handleNodeClick = node => {
  console.log('点击节点:', node)
}
</script>
```

## Tree Props

| 参数    | 说明               | 类型         | 默认值 |
| ------- | ------------------ | ------------ | ------ |
| data    | 树形数据源         | `TreeNode[]` | `[]`   |
| nodeKey | 节点唯一标识字段名 | `string`     | `'id'` |

## Tree Events

| 事件名         | 说明             | 回调参数           |
| -------------- | ---------------- | ------------------ |
| node-click     | 节点被点击时触发 | `(node: TreeNode)` |
| node-expanded  | 节点展开时触发   | `(node: TreeNode)` |
| node-collapsed | 节点折叠时触发   | `(node: TreeNode)` |

## Tree Slots

| 插槽名 | 说明               | 参数                 |
| ------ | ------------------ | -------------------- |
| node   | 自定义节点内容     | `{ node: TreeNode }` |
| label  | 自定义节点标签     | `{ node: TreeNode }` |
| icon   | 自定义节点图标     | `{ node: TreeNode }` |
| prefix | 自定义节点前缀内容 | `{ node: TreeNode }` |

## TreeNode Props

树节点组件的属性配置：

| 参数          | 说明                | 类型           | 默认值                        |
| ------------- | ------------------- | -------------- | ----------------------------- |
| node          | 节点数据对象        | `TreeNodeData` | `{ label: '', children: [] }` |
| label         | 节点显示文本        | `string`       | -                             |
| hasChildren   | 是否有子节点        | `boolean`      | `false`                       |
| level         | 节点层级（从0开始） | `number`       | `0`                           |
| checked       | 节点选中状态        | `boolean`      | `false`                       |
| disabled      | 节点禁用状态        | `boolean`      | `false`                       |
| expanded      | 节点展开状态        | `boolean`      | `false`                       |
| indeterminate | 节点半选状态        | `boolean`      | `false`                       |
| checkable     | 是否显示复选框      | `boolean`      | `false`                       |
| expandable    | 是否可展开          | `boolean`      | `false`                       |
| treeEnabled   | 是否启用树形结构    | `boolean`      | `true`                        |
| isShowHint    | 是否显示提示图标    | `boolean`      | `false`                       |
| inTable       | 是否在表格中使用    | `boolean`      | `false`                       |

## TreeNode Events

| 事件名          | 说明             | 回调参数                                  |
| --------------- | ---------------- | ----------------------------------------- |
| click-label     | 节点标签被点击   | `(node: TreeNodeData)`                    |
| dbclick-label   | 节点标签被双击   | `(node: TreeNodeData)`                    |
| update:expanded | 节点展开状态改变 | `(node: TreeNodeData, expanded: boolean)` |
| change          | 节点选中状态改变 | `(node: TreeNodeData, checked: boolean)`  |

## 数据结构

### TreeNodeData

```typescript
interface TreeNodeData {
  id?: string | number // 节点唯一标识
  label: string // 节点显示文本
  children?: TreeNodeData[] // 子节点数组
  expanded?: boolean // 是否展开
  checked?: boolean // 是否选中
  disabled?: boolean // 是否禁用
  indeterminate?: boolean // 是否半选状态
  checkable?: boolean // 是否显示复选框
  expandable?: boolean // 是否可展开
  [key: string]: any // 其他自定义字段
}
```

## 样式定制

组件提供了丰富的CSS类名用于样式定制：

```scss
.td-tree {
  // 树形控件容器样式
}

.td-tree-node {
  // 树节点容器样式

  &.is-expanded {
    // 展开状态样式
  }

  &.is-last-leaf-with-parent {
    // 最末端叶子节点样式
  }
}

.td-tree-node__content {
  // 节点内容容器样式

  &:hover {
    // 悬停状态样式
  }

  &.is-chosen {
    // 选中状态样式
  }
}

.td-tree-node__expand-icon {
  // 展开图标样式

  &.is-expanded {
    // 展开状态图标样式
  }

  &.is-hidden {
    // 隐藏状态图标样式
  }
}

.td-tree-node__checkbox {
  // 复选框容器样式
}

.td-tree-node__image-icon {
  // 自定义图标容器样式
}

.td-tree-node__label {
  // 节点文本标签样式
}

.td-tree-node__prefix {
  // 前缀内容容器样式
}

.td-tree-node__children {
  // 子节点容器样式
}
```

## 注意事项

1. **节点唯一标识**: 确保每个节点都有唯一的标识符，默认使用`id`字段，可通过`nodeKey`属性自定义
2. **数据响应性**: 组件支持响应式数据更新，可以动态修改`data`属性
3. **展开图标自动显示**: Tree组件会自动检测节点的`children`属性，如果存在且不为空，则显示展开图标
4. **层级缩进**: 每级缩进20px，可通过CSS变量或样式覆盖调整
5. **性能优化**: 对于大量数据，建议使用虚拟滚动或分页加载
6. **事件冒泡**: 点击事件会冒泡，需要时请使用`stopPropagation()`阻止
7. **插槽优先级**: 当同时提供`node`和`label`插槽时，`node`插槽优先级更高

## 常见问题

### Q: 如何实现节点的异步加载？

A: 可以在节点点击事件中动态更新节点的`children`属性：

```javascript
const handleNodeClick = async node => {
  if (!node.children && node.hasChildren) {
    const children = await loadChildren(node.id)
    node.children = children
    node.expanded = true
  }
}
```

### Q: 如何实现全选/取消全选功能？

A: 需要递归处理所有节点的选中状态：

```javascript
const setAllChecked = (nodes, checked) => {
  nodes.forEach(node => {
    node.checked = checked
    if (node.children) {
      setAllChecked(node.children, checked)
    }
  })
}
```

### Q: 如何获取所有选中的节点？

A: 递归遍历树形数据，收集选中的节点：

```javascript
const getCheckedNodes = nodes => {
  const result = []
  const traverse = nodes => {
    nodes.forEach(node => {
      if (node.checked) {
        result.push(node)
      }
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  traverse(nodes)
  return result
}
```
