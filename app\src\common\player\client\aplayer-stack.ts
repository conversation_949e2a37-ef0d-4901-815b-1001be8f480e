import { CallApiProxy } from '@root/common/call-api';
//import { StatHelper, type TOpenMediaFrom } from '@root/common/stat-helper';
import { WaitSomeThing } from '@root/common/wait-some-thing';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { EventEmitter } from 'events';
import * as BaseType from '../base';
import { AplayerPlayHistory } from './aplayer-history';
import { AplayerMedia } from './aplayer-media';
import { AplayerPlayList } from './play-list';
import { PlayerCallApiProxy } from '../call-api-impl';


export class AplayerStack {
  private static instance: AplayerStack;
  private currMedia: AplayerMedia | null = null;
  private playList: AplayerPlayList = new AplayerPlayList();
  private playHistory: AplayerPlayHistory = new AplayerPlayHistory();
  private apiProxy: CallApiProxy = new PlayerCallApiProxy();
  private waitSomeThing: WaitSomeThing = new WaitSomeThing();
  private eventContainor: EventEmitter = new EventEmitter();

  constructor() {
  }

  public static GetInstance(): AplayerStack {
    if (!AplayerStack.instance) {
      if (global.AplayerStackClientInstance) {
        AplayerStack.instance = global.AplayerStackClientInstance;
      } else {
        AplayerStack.instance = new AplayerStack();
        global.AplayerStackClientInstance = AplayerStack.instance;
      }
    }
    return AplayerStack.instance;
  }

  public async initApiProxy(): Promise<void> {
    await this.playList.initApiProxy(this.apiProxy);
    await this.playHistory.initApiProxy(this.apiProxy);

    this.apiProxy!.AttachServerEvent('AplayerStackMediaChangeEvent', ((id: string) => {
      console.log('client AplayerStackMediaChangeEvent fire')
      this.currMedia = new AplayerMedia(this.apiProxy!, id)
      this.currMedia.init();
      this.eventContainor.emit('MediaChangeEvent', this.currMedia);
    }));
    this.apiProxy!.AttachServerEvent('AplayerStackMediaCloseEvent', ((id: string) => {
      let currMedia = new AplayerMedia(this.apiProxy!, id);
      this.eventContainor.emit('MediaCloseEvent', currMedia);
      if (this.currMedia) {
        this.currMedia.unInit();
      }
      this.currMedia = null;
    }));
    this.apiProxy!.AttachServerEvent('AplayerStackInitFinishEvent', (() => {
      this.eventContainor.emit('InitFinishEvent');
    }));
    this.apiProxy!.AttachServerEvent('AplayerStackQuitPlayEvent', ((id: string) => {
      let currMedia = new AplayerMedia(this.apiProxy!, id);
      this.eventContainor.emit('QuitPlayEvent', currMedia);
    }));
    client.attachServerEvent('AplayerServerInitFinished', () => {
      this.waitSomeThing.done();
    });
    let info = await this.apiProxy!.CallApi('IsAplayerServerInitFinished')
    if (info.bSucc && info.result) {
      this.waitSomeThing.done();
    }
  }

  public async getCurrPlayMedia(): Promise<AplayerMedia | null> {
    await this.waitSomeThing.waitDone();
    if (!this.currMedia) {
      let info = await this.apiProxy!.CallApi('AplayerStackGetCurrPlayMedia');
      if (info.bSucc) {
        this.currMedia = new AplayerMedia(this.apiProxy!, info.result as string);
      }
    }
    return this.currMedia;
  }

  public async openMedia(attr: BaseType.MediaAttribute): Promise<void> {
    console.log('openMedia', attr)
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackOpenMedia', attr);
    //StatHelper.getInstance().openMediaFrom = openMediaFrom
  }

  public async reOpenMedia(r: BaseType.OpenMediaReason): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackReOpenMedia', r);
  }

  public async closeMedia(): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackCloseMedia');
    //StatHelper.getInstance().openMediaFrom = null
  }

  public async pauseMedia(): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackPauseMedia');
  }

  public async playMedia(): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackPlayMedia');
  }

  public async attachMediaChangeEvent(cb: (m: AplayerMedia) => void): Promise<void> {
    await this.waitSomeThing.waitDone();
    this.eventContainor.on('MediaChangeEvent', cb);
  }

  public async detachMediaChangeEvent(cb: (m: AplayerMedia) => void): Promise<void> {
    await this.waitSomeThing.waitDone();
    this.eventContainor.off('MediaChangeEvent', cb);
  }

  public async attachMediaClosedEvent(cb: (m: AplayerMedia) => void): Promise<void> {
    await this.waitSomeThing.waitDone();
    this.eventContainor.on('MediaCloseEvent', cb);
  }

  public async detachMediaCloseEvent(cb: (m: AplayerMedia) => void): Promise<void> {
    await this.waitSomeThing.waitDone();
    this.eventContainor.off('MediaCloseEvent', cb);
  }

  public async attachQuitPlayEvent(cb: (m: AplayerMedia) => void): Promise<void> {
    await this.waitSomeThing.waitDone();
    this.eventContainor.on('QuitPlayEvent', cb);
  }

  public async detachQuitPlayEvent(cb: (m: AplayerMedia) => void): Promise<void> {
    await this.waitSomeThing.waitDone();
    this.eventContainor.off('QuitPlayEvent', cb);
  }

  public async attachInitFinishEvent(cb: () => void): Promise<void> {
    await this.waitSomeThing.waitDone();
    this.eventContainor.on('InitFinishEvent', cb);
  }

  public async detachInitFinishEvent(cb: () => void): Promise<void> {
    await this.waitSomeThing.waitDone();
    this.eventContainor.off('QuitPlayEvent', cb);
  }

  public async getSupportedPlaySpeedList(): Promise<BaseType.PlaySpeedDisplayInfo[]> {
    await this.waitSomeThing.waitDone();
    let ret = await this.apiProxy!.CallApi('AplayerStackGetSupportedPlaySpeedList');
    if (ret.bSucc) {
      return ret.result as BaseType.PlaySpeedDisplayInfo[];
    }
    return [];
  }

  public async getCurrentPlaySpeedId(): Promise<string> {
    await this.waitSomeThing.waitDone();
    let ret = await this.apiProxy!.CallApi('AplayerStackGetCurrentPlaySpeedId');
    if (ret.bSucc) {
      return ret.result as string;
    }
    return '';
  }

  public async switchSpeed(id: string): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackSwitchSpeed', id);
  }

  public async isSilent(): Promise<boolean> {
    await this.waitSomeThing.waitDone();
    let ret = await this.apiProxy!.CallApi('AplayerStackIsSilent');
    if (ret.bSucc) {
      return ret.result as boolean;
    }
    return false;
  }

  public async setSilent(b: boolean): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackSetSilent', b);
  }

  public async getVolume(): Promise<number> {
    await this.waitSomeThing.waitDone();
    let ret = await this.apiProxy!.CallApi('AplayerStackGetVolume');
    console.log('getVolume', ret)
    if (ret.bSucc) {
      return ret.result as number;
    }
    return 0;
  };

  public async setVolume(n: number): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackSetVolume', n);
  }

  public async getCurrentPlaySequence(): Promise<BaseType.ListPlaySequence> {
    await this.waitSomeThing.waitDone();
    let ret = await this.apiProxy!.CallApi('AplayerStackGetCurrentPlaySequence');
    if (ret.bSucc) {
      return ret.result as BaseType.ListPlaySequence;
    }
    return 0;
  }

  public async setPlaySequence(seq: BaseType.ListPlaySequence): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackSetPlaySequence', seq);
  }

  // public async attachPlayViewSizeChangeEvent(cb: (viewId: string, x: number, y: number, w: number, h: number, angle: number) => void): Promise<number> {
  //     return this.nativeStack.attachPlayViewSizeChangeEvent(cb);
  // }

  // public async detachPlayViewSizeChangeEvent(cookie: number): Promise<void> {
  //     this.nativeStack.detachPlayViewSizeChangeEvent(cookie);
  // }

  // public async attachPlayViewVisibleChangeEvent(cb: (viewId: string, bVisible: boolean) => void): Promise<number> {
  //     return this.nativeStack.attachPlayViewVisibleChangeEvent(cb);
  // }

  // public async detachPlayViewVisibleChangeEvent(cookie: number): Promise<void> {
  //     this.nativeStack.detachPlayViewVisibleChangeEvent(cookie);
  // }

  public async closePlayWindow(): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackClosePlayWindow');
  }

  public getPlayList(): AplayerPlayList {
    return this.playList!;
  }

  public getPlayerHistory(): AplayerPlayHistory {
    return this.playHistory
  }

  public async isMediaLocalPlay(attr: BaseType.MediaAttribute): Promise<boolean> {
    await this.waitSomeThing.waitDone();
    let ret = await this.apiProxy!.CallApi('AplayerStackIsMediaLocalPlay', attr);

    if (ret.bSucc) {
      return ret.result as boolean;
    }
    return false;
  }

  public async createSnapshot(filePath: string, out: string, w: number, h: number): Promise<boolean> {
    await this.waitSomeThing.waitDone();
    let ret = await this.apiProxy!.CallApi('AplayerStackCreateSnapshot', filePath, out, w, h);

    if (ret.bSucc) {
      return ret.result as boolean;
    }
    return false;
  }

  public async getAplayerVersion(): Promise<string> {
    await this.waitSomeThing.waitDone();
    let ret = await this.apiProxy!.CallApi('AplayerStackGetAplayerVersion');

    if (ret.bSucc) {
      return ret.result as string;
    }
    return '';
  }

  public async imageRatio(id: BaseType.ImageRatioType): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackImageRatio', id);
  }

  public async getImageRatioItems(): Promise<BaseType.ImageRatioItem[]> {
    await this.waitSomeThing.waitDone();
    let ret = await this.apiProxy!.CallApi('AplayerStackGetImageRatioItems');

    if (ret.bSucc) {
      return ret.result as BaseType.ImageRatioItem[];
    }
    return [];
  }

  public async imageRotation(angle: number): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackImageRotation', angle);
  }

  public async imageFlip(bHorizontal: boolean, bRestore: boolean): Promise<void> {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackImageFlip', bHorizontal, bRestore);
  }

  public async addFloatShowRect(name: string, rects: any[]) {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackAddFloatShowRect', name, rects);
  }

  public async deleteFloatShowRect(name: string) {
    await this.waitSomeThing.waitDone();
    await this.apiProxy!.CallApi('AplayerStackDeleteFloatShowRect', name);
  }
}