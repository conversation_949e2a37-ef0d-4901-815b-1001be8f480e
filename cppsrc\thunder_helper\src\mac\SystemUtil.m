#import "SystemUtil.h"
#import <Cocoa/Cocoa.h>
#import <IOKit/IOKitLib.h>
#import <IOKit/network/IOEthernetController.h>
#import <fcntl.h>
#import <sys/sysctl.h>
#import <CommonCrypto/CommonCrypto.h>
#import <IOKit/pwr_mgt/IOPMLib.h>

static BOOL getPrimaryMACAddresses(NSArray **addresses) {
    BOOL result = NO;
    kern_return_t kernResult;
    CFMutableDictionaryRef matchingDict;
    CFMutableDictionaryRef propertyMatchDict;
    io_iterator_t matchingServices;
    
    matchingDict = IOServiceMatching("IOEthernetInterface");
    
    if (matchingDict == NULL)
    {
        return result;
    }
    
    propertyMatchDict = CFDictionaryCreateMutable(kCFAllocatorDefault, 0, &kCFTypeDictionaryKeyCallBacks, &kCFTypeDictionaryValueCallBacks);
    if (propertyMatchDict == NULL)
    {
        CFRelease(matchingDict);
        return result;
    }
    
    CFDictionarySetValue(propertyMatchDict, CFSTR("IOPrimaryInterface"), kCFBooleanTrue);
    CFDictionarySetValue(matchingDict, CFSTR("IOPropertyMatch"), propertyMatchDict);
    CFRelease(propertyMatchDict);
    
    kernResult = IOServiceGetMatchingServices(kIOMasterPortDefault, matchingDict, &matchingServices);
    if (kernResult != KERN_SUCCESS)
    {
        return result;
    }
    
    io_object_t interfaceService;
    io_object_t controllerService;
    NSMutableArray* addrs = [[NSMutableArray alloc] init];
    
    while ((interfaceService = IOIteratorNext(matchingServices)))
    {
        CFTypeRef MACAddressAsCFData;
        
        kernResult = IORegistryEntryGetParentEntry(interfaceService, kIOServicePlane, &controllerService);
        
        if (kernResult == KERN_SUCCESS)
        {
            MACAddressAsCFData = IORegistryEntryCreateCFProperty(controllerService, CFSTR(kIOMACAddress), kCFAllocatorDefault, 0);
            if (MACAddressAsCFData)
            {
                NSData* MACAddress = (__bridge NSData*)MACAddressAsCFData;
                [addrs addObject: MACAddress];
                
            }
            IOObjectRelease(controllerService);
        }
        IOObjectRelease(interfaceService);
    }
    
    IOObjectRelease(matchingServices);
    
    if ([addrs count] > 0)
    {
        if (addresses)
        {
            *addresses = [addrs copy];
        }
        
        result = YES;
    }
    
    return result;
}


static NSString * serialNumber() {
    io_service_t    platformExpert = IOServiceGetMatchingService(kIOMasterPortDefault,
                                                                 
                                                                 IOServiceMatching("IOPlatformExpertDevice"));
    CFStringRef serialNumberAsCFString = NULL;
    
    if (platformExpert) {
        serialNumberAsCFString = IORegistryEntryCreateCFProperty(platformExpert,
                                                                 CFSTR(kIOPlatformSerialNumberKey),
                                                                 kCFAllocatorDefault, 0);
        IOObjectRelease(platformExpert);
    }
    
    NSString *serialNumberAsNSString = nil;
    if (serialNumberAsCFString) {
        serialNumberAsNSString = [NSString stringWithString:(__bridge NSString *)serialNumberAsCFString];
        CFRelease(serialNumberAsCFString);
    }
    
    return serialNumberAsNSString;
}


@interface SystemUtil()


@end

static NSString* const kPeerIdSuffix = @"005V";
static NSString* const kDefaultPeerId = @"XXXXXXXXXXXX005V";
// 在接口声明中添加属性
static IOPMAssertionID _assertionID = 0;

@implementation SystemUtil

+ (NSString *)getPeerId {
    NSString *peerId = [[NSUserDefaults standardUserDefaults] objectForKey:@"PEERID"];
    if (peerId == nil && [peerId length] != 16) {
        NSArray* primaryMACAddresses = nil;
        BOOL result = getPrimaryMACAddresses(&primaryMACAddresses);
        NSData* primaryMacAddressHex = nil;
        if (result)
        {
            NSData* primaryMacAddress = [primaryMACAddresses objectAtIndex: 0];
            if (primaryMacAddress && [primaryMacAddress length] != 0) {
                
                NSUInteger capacity = [primaryMacAddress length] * 2;
                NSMutableString* hexString = [NSMutableString stringWithCapacity: capacity];
                const unsigned char* dataBytes = [primaryMacAddress bytes];
                for (int i = 0; i < [primaryMacAddress length]; ++i)
                {
                    [hexString appendFormat: @"%02x", dataBytes[i]];
                }
                
                primaryMacAddressHex = [NSData dataWithBytes: (const void *)[hexString UTF8String] length: [hexString length]];
            }
            NSString* primaryMacAddressString = [[NSString alloc] initWithData: primaryMacAddressHex encoding: NSUTF8StringEncoding];
            primaryMacAddressString = [primaryMacAddressString uppercaseString];
            peerId = [primaryMacAddressString stringByAppendingString: kPeerIdSuffix];
            [[NSUserDefaults standardUserDefaults] setObject:peerId forKey:@"PEERID"];
            [[NSUserDefaults standardUserDefaults] synchronize];
        } else {
            NSString *sn = serialNumber();
            if( sn.length == 12 ) {
                peerId = [[NSString alloc] initWithFormat:@"%@005V", sn];
                [[NSUserDefaults standardUserDefaults] setObject:peerId forKey:@"PEERID"];
                [[NSUserDefaults standardUserDefaults] synchronize];
            } else {
                peerId = kDefaultPeerId;
            }
        }
    }
    
    return peerId;
    
}

/// 防止系统进入睡眠
+ (void)preventSleep {
    if (_assertionID == 0) {
        CFStringRef reasonForActivity = CFSTR("Downloading files");
        IOReturn success = IOPMAssertionCreateWithName(
            kIOPMAssertionTypeNoDisplaySleep,  // 仅屏幕不休眠
            kIOPMAssertionLevelOn,            // 强制阻止系统进入睡眠
            reasonForActivity,
            &_assertionID
        );
        
        if (success == kIOReturnSuccess) {
            NSLog(@"✅ 成功防止系统进入休眠 (assertionID: %u)", _assertionID);
        } else {
            // NSLog(@"❌ 失败: 无法创建 assertion");
        }
    }
}

/// 允许系统进入睡眠
+ (void)allowSleep {
    if (_assertionID != 0) {
        IOPMAssertionRelease(_assertionID); // 释放 assertion
        _assertionID = 0;
    }
}

@end