.single-task-content {
  // 使用 transform 进行硬件加速
  transform: translateZ(0);
  // 避免重排重绘
  will-change: auto;
}

// 确保对话框内容稳定
:deep(.dialog-content) {
  // 硬件加速
  transform: translateZ(0);
  will-change: auto;
}

:deep(.playButton) {
  width: 154px;
  padding: 0;
}

.playButton {
  width: 154px;
  padding: 0;
}

// 优化任务信息区域
.task-info-section {
  transform: translateZ(0);
}

// pre-new-mixed-task 组件样式
.task-name {
  display: -webkit-box;
  margin-bottom: 0;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  &.isErrored {
    color: var(--functional-error-default, #ff4d4f);
  }
}

.parsing-complete {
  &.parsing-complete-single-magnet {
    padding: 0;

    .task-name {
      margin-bottom: 8px;
      color: var(--font-font-1, #272e3b);
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;

      &.isErrored {
        color: var(--functional-error-default, #ff4d4f);
      }
    }

    .tasks-list-container {
      margin-top: 12px;
    }

    .task-size {
      margin-left: 0;
      cursor: pointer;
    }

    .task-list-container {
      display: none;

      &.is-show {
        display: block;
      }
    }
  }
}

.bt-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 100%;
}

.parsing-loading {
  .parsing-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .retry-btn-container {
    margin-left: auto;
  }

  .task-name {
    margin-bottom: 8px;
  }
}

.parsing-failed {
  .task-name {
    margin-bottom: 8px;
  }
}

:global(.dialog-actions.pre-new-task-actions) {
  padding-top: 40px;
}

.file-selection-header {
  margin-bottom: 16px;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--font-font-2);
  font-size: 14px;
}

.selection-info i {
  color: var(--font-font-3);
  font-size: 16px;
}

// 全局样式，覆盖Dialog组件的默认样式
:deep(.pre-new-mixed-task-content) {
  width: 100%;
  min-width: 680px;
  height: 100%;
  min-height: 550px;
}

.pre-new-mixed-task-content {
  width: 100%;
  min-width: 681px;
  height: 100%;
  min-height: 550px;
}

:deep(.pre-new-mixed-task-description) {
  margin: 0;
  padding: 0;
}

:deep(.pre-new-mixed-task-actions) {
  display: none;
}

// single-p2sp-task 组件样式
.single-task-content {
  padding: 0;

  &.single-magnet {
    // padding-top: 12px;

    .loading-container {
      position: relative;
      flex-shrink: 0;
      width: 434px;
      height: 6px;
      height: 6px;
      border-radius: 60px;
      background-color: var(--button-button2-default, #f2f3f5);

      svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      :deep(svg) {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }

    .retry-btn-container {
      :deep(button) {
        width: 92px;
      }
    }
  }
}

.retry-btn-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.task-info-section {
  display: flex;
  align-items: flex-start;
  width: 100%;
  height: 80px;
  gap: 10px;

  &.video-task-info {
    width: 100%;
    height: 80px;

    .task-thumbnail {
      width: 132px;
    }
  }
}

.task-thumbnail {
  position: relative;
  flex-shrink: 0;
  width: 64px;
  height: 100%;
  overflow: hidden;
  border-radius: var(--border-radius-M);
}

.video-thumbnail {
  // background: linear-gradient(135deg, #4a9eff 0%, #1e88e5 100%);
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  .thumbnail-img {
    display: block;
    width: 132px;
    height: auto;
  }
}

.thumbnail-bg {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  align-items: center;
  width: 132px;
  height: 100%;
  opacity: 0.6;

  img {
    display: block;
    width: 100%;
    height: auto;
    object-fit: cover;
  }
}

.play-button {
  width: 154px;
}

.file-icon-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;

  .file-icon-type {
    zoom: 1.5;
  }
}

.task-details-section {
  flex: 1;
  min-width: 0;
  height: 100%;
}

.task-details {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  height: 100%;
}

.task-size {
  margin-left: 8px;
  color: var(--font-font-3, #86909c);
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}

.task-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.download-btn {
  min-width: 140px;
}

// 自定义Dialog样式
:deep(.single-task-dialog-description) {
  margin: 0;
  padding: 0;
}

.config-icon {
  display: flex;
  flex-shrink: 0;
  align-items: flex-start;
  margin-left: 28px;
  padding-top: 14px;
  cursor: pointer;

  i {
    color: var(--font-font-3);
    font-size: 16px;
    transition: color 0.2s ease;

    &:hover {
      color: var(--font-font-2);
    }
  }
}

// 全局样式，覆盖Dialog组件的默认样式
:deep(.pre-multi-p2sp-task-content) {
  width: 90vw;
  max-width: 800px;
}

:deep(.pre-multi-p2sp-task-description) {
  margin: 0;
  padding: 0;
}

:deep(.td-table td) {
  // display: none;
  height: 40px;
}

// 以下为公共css
// 公共文本溢出样式
.text-ellipsis-single {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本溢出 mixin
@mixin text-ellipsis($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 动态生成1-10行的省略号类
@for $i from 1 through 10 {
  .text-ellipsis-#{$i} {
    @include text-ellipsis($i);
  }
}

// 兼容旧的命名
.text-ellipsis-multi {
  @include text-ellipsis(2);
}
