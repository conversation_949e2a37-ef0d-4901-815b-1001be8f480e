import { isProcess, is<PERSON><PERSON><PERSON> } from "@root/common/env";
import { config } from "@root/common/config/config";
import { isPlainObject, isString } from "@root/common/checkTypes";
import { client } from "@xunlei/node-net-ipc/dist/ipc-client";
// import { server } from "@xunlei/node-net-ipc/dist/ipc-server";
import { mainRendererContext } from '@xunlei/node-net-ipc/dist/ipc-base';
import { EventEmitter } from 'events';

type TStoreOptions<T> = {
  name: string
}

export class ConfigStore<T extends Record<string, any>> {
  private _options: TStoreOptions<T>
  private _emitter: EventEmitter

  constructor(options: TStoreOptions<T>) {
    this._options = options
    this._emitter = new EventEmitter()
    this.init()
  }

  private genIpcName(key: string) {
    return `${this._options.name}:${key}`
  }

  init() {
    if (is<PERSON>ender<PERSON>) {
      client.registerFunctions({
        [this.genIpcName('get')]: (ctx: unknown, key: string, defaultValue: any) => {
          return this.get(key, defaultValue)
        },
        [this.genIpcName('set')]: (ctx: unknown, key: string, value: any) => {
          return this.set(key, value)
        },
      })
    }
    if (isProcess) {
      // server目前已经挪到main-renderer，暂时屏蔽此处
      // server.registerFunctions({
      //   [this.genIpcName('emit')]: (client: any, context: any, key: string, value: any) => {
      //     this._emitter.emit(key, value)
      //   },
      // })
    }
  }

  async get<Key extends keyof T>(key: Key, defaultValue?: Required<T>[Key]): Promise<T[Key] | undefined> {
    if (isRenderer) {
      let res: T[Key] | undefined

      const val = await config.getValue(this._options.name, String(key), '')
      if (val === '') {
        res = defaultValue
      } else if (isString(val)) {
        // string 或 json string
        try {
          const parseRes = JSON.parse(val)
          if (isPlainObject(parseRes)) {
            // json string
            res = parseRes
          }
        } catch (e) { }
        // @ts-ignore
        if (!res) {
          // string
          res = val as any
        }
      } else {
        // 非 string 和 json string 类型
        res = val as any
      }

      return res
    } else if (isProcess) {
      return (await client.callRemoteClientFunction(mainRendererContext, this.genIpcName('get'), String(key), defaultValue))?.[0]
    }
  }

  async set<Key extends keyof T>(key: Key, value: Required<T>[Key]) {
    if (isRenderer) {
      if (isPlainObject(value)) {
        await config.setValue(this._options.name, String(key), JSON.stringify(value))
      } else {
        await config.setValue(this._options.name, String(key), value)
      }
      // client emit
      this._emitter.emit(`change:${String(key)}`, value)
      this._emitter.emit('change', { key, value })
      // process emit
      client.callServerFunction(this.genIpcName('emit'), `change:${String(key)}`, value)
      client.callServerFunction(this.genIpcName('emit'), `change`, { key, value })
    }
    if (isProcess) {
      await client.callRemoteClientFunction(mainRendererContext, this.genIpcName('set'), String(key), value)
    }

  }

  on<Key extends keyof T>(key: Key, callback: (value: T[Key]) => void): void {
    this._emitter.on(`change:${String(key)}`, callback)
  }

  off<Key extends keyof T>(key: Key): void {
    this._emitter.removeAllListeners(`change:${String(key)}`)
  }

  onAny(callback: (data: { key: keyof T, value: any }) => void): void {
    this._emitter.on('change', callback)
  }

  offAny(): void {
    this._emitter.removeAllListeners('change')
  }
}