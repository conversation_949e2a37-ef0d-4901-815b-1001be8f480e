<script setup lang="ts">
import CloudAddTaskItem from './item.vue'
import EmptyHolder from '@/components/empty-holder/index.vue'
import FileListHeader from '@/components/file-header/index.vue'
import Loading from '@root/common/components/ui/loading/index.vue'
import Breadcrumb, { IBreadcrumbItemData } from '@root/common/components/ui/breadcrumb/index.vue'

import { CloudAddManager } from '@/manager/cloud-add-manager'
import { API_TASK } from '@root/common/thunder-pan-manager/pan-sdk/types'
import { computed, nextTick, onMounted, ref, useTemplateRef, watch } from 'vue'
import { TPanRoute, useCloudAddRouterStore } from '@/store/cloud-add-router-store'
import { CreateCommonContextmenu } from '@root/common/components/ui/contextmenu'
import { getFileExtension, getMediaType, getPreviewType, isFolder, isTaskAdding } from '@root/common/thunder-pan-manager/pan-sdk/utils/drive'
import { IContextMenuItem } from '@root/common/thunder-pan-manager/pan-sdk/utils/file'
import { TypeDriveSortInfo } from '@/manager/drive-file-manager'
import { FileOperationHelper } from '@/utils/file-operation'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { GlobalEventHelper } from '@/utils/global-event-helper'
import { FilterManager } from '@/manager/filter-manager'
import { PanBusinessHelper } from '@/utils/business-helper'
import { ECloudAddTaskOperation } from '@root/common/thunder-pan-manager/pan-sdk/utils/file'
import { EPanPage, useHistoryStore } from '@/store/history-store'
import { ETabId } from '@/manager/tabs-manager'
import DragSelect from '@root/common/components/utils/drag-select'
import { useElementVisibility } from '@vueuse/core'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'

const currenTaskData = CloudAddManager.getInstance().getCurrentData()
const { pushState, replaceState } = useHistoryStore()
const { routerList_computed, currentParentFile, setRouterList } = useCloudAddRouterStore()

let getAddingTaskListTimer

const isLoading = ref(false)
const isEnableTimeSorter = ref(true)
const scrollerVm = ref<any>(null)
const lastChosenIndex = ref(0)

const $rootElement = useTemplateRef<HTMLDivElement>('$rootElement')
const rootElementIsVisible = useElementVisibility($rootElement)

const pickedIds = computed(() => currenTaskData.pickedIds)
const renderList = computed(() => {
  if (currentParentFile.value.id) return currenTaskData.childList
  return currenTaskData.list
})
const canPickIds = computed(() => renderList.value.map(t => t.id!))
const selectedFiles = computed(() => {
  const list: API_TASK.DriveTask[] = []
  renderList.value.forEach(file => {
    if (pickedIds.value.includes(file.id!)) {
      list.push(file)
    }
  })
  return list
})
const contextmenuList = computed(() => {
  const list = selectedFiles.value
  let menuItemList: IContextMenuItem[] = [];

  if (list.length === 1) {
    let complete: IContextMenuItem[] = []
    const item = list[0]
    const prevType = getPreviewType({
      extString: getFileExtension(item.file_name!),
      mimeType: item.reference_resource?.mime_type,
      category: item.reference_resource?.file_category,
    })
    const media = getMediaType(
      getFileExtension(item.file_name!),
      item.reference_resource?.mime_type,
      item.reference_resource?.file_category,
    )

    if (prevType !== 'other') {
      if (prevType === 'media' && media === 'video') {
        complete.push({ key: ECloudAddTaskOperation.PLAY, name: '在线播放' })
        // complete.push({ name: '投屏播放' })
      } else {
        // complete.push({ name: '打开' })
      }

      complete.push({ key: ECloudAddTaskOperation.OPEN_FOLDER, name: '打开云盘目录' })
      complete.push({ key: ECloudAddTaskOperation.DELETE, name: '删除' })
    } else {
      complete = [
        { key: ECloudAddTaskOperation.OPEN_FOLDER, name: '打开云盘目录' },
        { key: ECloudAddTaskOperation.DELETE, name: '删除' }
      ]
    }
    const menuMap: any = {
      PHASE_TYPE_COMPLETE: complete,
      PHASE_TYPE_RUNNING: [{ key: ECloudAddTaskOperation.DELETE, name: '删除' }],
      PHASE_TYPE_ERROR: [{ key: ECloudAddTaskOperation.DELETE, name: '删除' }],
      PHASE_TYPE_UNKNOW: [],
      PHASE_TYPE_PENDING: [],
      PHASE_TYPE_PAUSED: []
    }
    menuItemList = menuMap[item.phase!]
  } else {
    menuItemList = [{ key: ECloudAddTaskOperation.DELETE, name: '删除' }]
  }

  // if (isHeaderSelectAll) {
  //   menuItemList.push({ key: ECloudAddTaskOperation.SELECT_ALL,  name: '全选' })
  // }
  // menuItemList.push({ key: 'refresh', name: '刷新' })

  return [ menuItemList ]
})
const isHeaderIndeterminate = computed(() => {
  return !!pickedIds.value.length && pickedIds.value.length !== canPickIds.value.length
})
const isHeaderSelectAll = computed(() => {
  return !!pickedIds.value.length && pickedIds.value.length === canPickIds.value.length
})

function handleCleanPicked() {
  CloudAddManager.getInstance().cleanPicked()
}

function handlePickedIdsChange(isSelectAll: boolean) {
  if (!isSelectAll) {
    handleCleanPicked()
  } else {
    CloudAddManager.getInstance().setPickedIds(canPickIds.value)
  }
}

async function handleHeaderSorterClick (sortInfo: TypeDriveSortInfo) {
  CloudAddManager.getInstance().setSortType(sortInfo)

  isLoading.value = true
  await CloudAddManager.getInstance().fetchList({ reset: true })
  isLoading.value = false

  handleUpdateScroller()
}

async function handleListScrollEnd() {
  if (!currentParentFile.value.id) {
    // 加载根目录文件列表
    if (CloudAddManager.getInstance().getCurrentPageToken()) {
      await CloudAddManager.getInstance().fetchList()
    }
  } else {
    // 加载子文件夹的文件列表
    const current = currentParentFile.value
    await CloudAddManager.getInstance().fetchChildList(current.id, current.space)
  }
  handleUpdateScroller()
}

function handleSetRouterList (newList: TPanRoute[]) {
  pushState({
    page: EPanPage.HOME,
    tab: ETabId.CLOUD_ADD,
    routes: newList
  })

  setRouterList(newList)
}

async function handleFileItemConsume(task: API_TASK.DriveTask) {
  // 为了判断，将云添加任务数据转成文件数据
  const fileData = CloudAddManager.convertToFileData(task)

  if (isFolder(fileData)) {
    handleSetRouterList(
      routerList_computed.value.concat({
        id: task.id,
        title: task.name!,
        ...task,
      } as any) as any,
    )
  } else {
    if (task.phase === 'PHASE_TYPE_COMPLETE') {
      FileOperationHelper.getInstance().consumeFile(task.file_id!, task.reference_resource?.space)
    } else if (task.phase === 'PHASE_TYPE_ERROR') {
      window.__VueGlobalProperties__.$message({
        message: task.message!,
        type: 'error'
      })
    }
  }
}

/**
 * 处理面包屑点击
 * @param item
 * @param index
 */
function handleBreadcrumbItemClick(item: IBreadcrumbItemData, index: number) {
  const newList = routerList_computed.value.slice(0, index + 1)
  handleSetRouterList(newList as any)
}

async function handleContextMenu(event: MouseEvent, task: API_TASK.DriveTask) {
  if (!pickedIds.value.includes(task.id!)) {
    CloudAddManager.getInstance().togglePickedId(task.id!, true)
  }

  await nextTick()
  CreateCommonContextmenu({
    menuList: contextmenuList.value,
    parentElement: $rootElement.value!,
    clickPosition: {
      x: event.clientX,
      y: event.clientY,
    },
    onMenuItemClick: (item) => {
      handleOperationWithType(item.key, selectedFiles.value)
    }
  })
}

function handleItemClick (event: MouseEvent, index: number, file: API_TASK.DriveTask) {
  if (event.shiftKey) {
    const start = Math.min(lastChosenIndex.value, index)
    const end = Math.max(lastChosenIndex.value, index)
    const pickedIds = renderList.value.slice(start, end + 1)
        .map(item => item.id!)

    CloudAddManager.getInstance().setPickedIds(pickedIds)
  } else {
    CloudAddManager.getInstance().togglePickedId(file.id!, !event.ctrlKey)
  }
}

function handleItemOperationClick (type: string, task: API_TASK.DriveTask) {
  handleOperationWithType(type, [ task ])
}

async function handleOperationWithType (type: string, tasks: API_TASK.DriveTask[]) {
  switch (type) {
    case ECloudAddTaskOperation.PLAY: {
      const file = tasks[0]
      FileOperationHelper.getInstance().playMedia({
        id: file.file_id,
        name: file.file_name,
        hash: file.reference_resource?.hash,
        space: file.reference_resource?.space,
      })
      return
    }
    case ECloudAddTaskOperation.DELETE: {
      const ids = tasks.map(t => t.id!)
      const res = await ThunderPanClientSDK.getInstance().batchDeleteUrlTasks(ids)

      if (res.success) {
        window.__VueGlobalProperties__.$message({
          message: '已删除',
          type: 'success',
        })
        clearTimeout(getAddingTaskListTimer)
        CloudAddManager.getInstance().cleanPicked()
        CloudAddManager.getInstance().UIDeleteItems(ids)
      } else {
        window.__VueGlobalProperties__.$message({
          message: '删除失败，请稍后再试',
          type: 'error',
        })
      }
      return
    }
    case ECloudAddTaskOperation.SELECT_ALL: {
      CloudAddManager.getInstance().setPickedIds(canPickIds.value)
      return
    }
    case ECloudAddTaskOperation.OPEN_FOLDER: {
      const file = tasks[0]
      PanBusinessHelper.getInstance().openDirectory(file.file_id!, { fileSpace: file.reference_resource?.space })
      return
    }
  }

  window.__VueGlobalProperties__.$message({
    message: '尚未支持该功能，敬请期待',
    type: 'info'
  })
}

function handleCheckChange(isCheck: boolean, file: API_TASK.DriveTask) {
  CloudAddManager.getInstance().togglePickedId(file.id!)
}

/**
 * 更新当前面包屑对应的文件列表数据
 * @param options
 */
async function updateCurrentList() {
  const current = currentParentFile.value

  // 子文件夹
  if (current.id !== '') {
    isLoading.value = true
    await CloudAddManager.getInstance().fetchChildList(current.id, current.space)
    isLoading.value = false
  } else {
    CloudAddManager.getInstance().resetChildFolder()
  }

  isEnableTimeSorter.value = current.id === ''
  FilterManager.getInstance().updateSetting('cloud-add_', { enable: current.id === '' })
}

const toolbarPrimaryOperation = [
  {
    key: ECloudAddTaskOperation.DELETE,
    icon: 'xl-icon-delete',
    text: '删除',
    disabled: false,
  }
]
const toolbarDropdownMenuList = []

function handleToolbarItemClick (itemKey: string, tasks: API_TASK.DriveTask[]) {
  handleOperationWithType(itemKey, tasks)
}

async function handleRefresh () {
  isLoading.value = true
  if (currentParentFile.value.id !== '') {
    await CloudAddManager.getInstance().fetchChildList(currentParentFile.value.id, currentParentFile.value.space, { reset: true })
  } else {
    await CloudAddManager.getInstance().fetchList({ reset: true })
  }
  isLoading.value = false
}

function handleCheckAddingTaskList () {
  const addingTaskIds: string[] = []
  renderList.value.forEach(task => {
    if (isTaskAdding(task)) {
      addingTaskIds.push(task.id!)
    }
  })

  if (addingTaskIds.length) {
    clearTimeout(getAddingTaskListTimer)
    handleFetchAddingTaskListTimer(addingTaskIds)
  }
}

async function handleFetchAddingTaskListTimer (taskIds: string[]) {
  const res = await ThunderPanClientSDK.getInstance().getUrlTaskList({
    params: {
      filters: {
        id: {
          in: taskIds.join(',')
        }
      },
    }
  })

  if (res.success && res.data && res.data.tasks) {
    const newAddingTaskIds: string[] = []
    const refreshStatusDuration = res.data.expires_in_ms

    res.data.tasks.forEach(task => {
      CloudAddManager.getInstance().updateTask(task)

      if (isTaskAdding(task)) {
        newAddingTaskIds.push(task.id!)
      }
    })

    if (newAddingTaskIds.length) {
      getAddingTaskListTimer = setTimeout(() => {
        handleFetchAddingTaskListTimer(newAddingTaskIds)
      }, refreshStatusDuration)
    }
  }
}

function handleUpdateScroller () {
  // RecycleScroller 有 bug ，更新列表里面的 DOM 不更新，这里需要手动调里面的方法来更新
  if (scrollerVm.value) {
    scrollerVm.value.updateVisibleItems(true)
  }
}

function handleOpenNewTaskDialog () {
  ThunderNewTaskHelperNS.showNewTaskWindow({
    selectedPathType: ThunderNewTaskHelperNS.DownloadPathType.Cloud
  })
}

watch(currentParentFile, () => {
  CloudAddManager.getInstance().cleanPicked()
  FilterManager.getInstance().resetByKey(`cloud-add_${currentParentFile.value.id}`, true)
  updateCurrentList()
  handleCheckAddingTaskList()
})

onMounted(async () => {
  isLoading.value = true
  await CloudAddManager.getInstance().fetchList({ reset: true })
  isLoading.value = false
  handleCheckAddingTaskList()

  // 全局事件监听
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.CLOUD_ADD_LIST_REFRESH, async () => {
    handleRefresh()
    handleCleanPicked()
    replaceState({
      page: EPanPage.HOME,
      tab: ETabId.CLOUD_ADD,
      routes: routerList_computed.value as any
    })
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.CLOUD_ADD_LIST_CLEAN_PICKED, () => {
    handleCleanPicked()
  })
  // 云添加通知
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.CLOUD_ADD_TASK_ADD, (task: API_TASK.DriveTask) => {
    CloudAddManager.getInstance().appendTasks([ task ])
    handleCheckAddingTaskList()
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.CLOUD_ADD_TASK_FAIL, (task: API_TASK.DriveTask) => {
    CloudAddManager.getInstance().updateTask(task)
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.CLOUD_ADD_TASK_SUCCESS, (task: API_TASK.DriveTask) => {
    CloudAddManager.getInstance().updateTask(task)
  })
  // 监听框选事件
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.DRAG_SELECT_END, (selectArea: HTMLElement, picked: HTMLElement[]) => {
    if (!rootElementIsVisible.value) return

    const { startIndex, lastIndex } = DragSelect.getDragSelectIndex(picked, selectArea, 56)
    // 从数据中遴选出相应被选中的数据
    const pickList = renderList.value.slice(startIndex!, lastIndex! + 1)
    const pickedIds = pickList.map(file => canPickIds.value.includes(file.id!) ? file.id! : '').filter(Boolean)

    if (pickedIds && pickedIds.length) {
      CloudAddManager.getInstance().setPickedIds(pickedIds)
    }
  })
})
</script>

<template>
  <div ref="$rootElement" class="cloud-add-list__wrapper">
    <div class="title-bar">
      <Breadcrumb :list="routerList_computed" @item-click="handleBreadcrumbItemClick" />
    </div>

    <Loading v-if="isLoading" />

    <EmptyHolder
      v-else-if="!renderList.length"
      button-text="新建任务"
      button-left-icon="xl-icon-add"
      @button-click="handleOpenNewTaskDialog"
    ></EmptyHolder>

    <div v-else class="file-list-container drag-select__body">
      <FileListHeader
        class="disable-drag-select"
        :sort-info="currenTaskData.sortInfo"
        :select-all="isHeaderSelectAll"
        :selected-files="selectedFiles"
        :indeterminate="isHeaderIndeterminate"
        :toolbar-primary-operation="toolbarPrimaryOperation"
        :toolbar-dropdown-menu-list="toolbarDropdownMenuList"
        :enable-name-sorter="false"
        :enable-size-sorter="false"
        :enable-time-sorter="isEnableTimeSorter"
        @clean-picked="handleCleanPicked"
        @check-change="handlePickedIdsChange"
        @toolbar-item-click="handleToolbarItemClick"
        @sorter-click="handleHeaderSorterClick"
      />

      <RecycleScroller
        v-slot="{ item, index }"
        ref="scrollerVm"
        class="file-list__wrapper drag-select__content"
        key-field="id"
        data-scroll-container
        :items="renderList"
        :item-size="56"
        @scroll-end="handleListScrollEnd"
      >
        <CloudAddTaskItem
          class="drag-select__item"
          :file="item"
          :index="index"
          :picked-ids="pickedIds"
          :data-index="index"
          :style="{
            'margin-top': '4px',
          }"
          @consume="handleFileItemConsume"
          @right-click="handleContextMenu"
          @item-click="handleItemClick"
          @checkbox-click="handleCheckChange"
          @operation-click="handleItemOperationClick"
        />
      </RecycleScroller>
    </div>
  </div>
</template>

<style scoped lang="scss">
.cloud-add-list__wrapper {
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  position: relative;

  .title-bar {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 8px 40px;
    font-size: 13px;
    flex-shrink: 0;
  }

  .file-list-container {
    flex-grow: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .file-list__wrapper {
      flex-grow: 1;
      min-height: 0;
      overflow: overlay;
    }
  }
}
</style>
