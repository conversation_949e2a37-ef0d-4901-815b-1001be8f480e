/// <reference types="@rsbuild/core/types" />

export type MessageType = 'info' | 'success' | 'warning' | 'error' | 'loading';

export interface IMessageOptions {
  message: string
  duration?: number
  type?: MessageType
  showTypeIcon?: boolean
  rightTextButton?: string
  onClose?: () => void
  onRightTextButtonClick?: () => void
}

export type MessageFn = {
  (options?: IMessageOptions, appContext?: null | AppContext): MessageHandler
  closeAll(type?: MessageType): void
}
export type MessageTypedFn = (
  options?: IMessageOptions,
  appContext?: null | AppContext
) => MessageHandler

export interface Message extends MessageFn {
  success: MessageTypedFn
  warning: MessageTypedFn
  info: MessageTypedFn
  error: MessageTypedFn
}

interface ThunderComponentCustomProperties  {
  $message: Message
}

declare global {
  interface Window {
    __ELECTRON__: {
      ipcRenderer: Electron.IpcRenderer
    }

    __profilesDir: string
    __popupWindowId__: number;
    __VueGlobalProperties__: ThunderComponentCustomProperties & Record<string, any>
  }

  interface PackageJson {
    name: string
    author: string
    version: string
    description: string
    homepage: string
    repository: {
      type: string
      url: string
    }
    license: string
  }

  // 给 util.promisify 库的 promisify 绑定定义一个别名类型
  type Promisify = (...args: any[]) => Promise<any>
}

export { }

