import { ReferenceResource } from "..";

export namespace API_EVENT {
    /**
     * ClearEventsRequest 清除事件
     */
    export interface DriveClearEventsRequest {
        /**
         * 文件空间
         */
        space?: string;
        /**
         * types 事件类型列表，为空表示清除所有
         */
        types?: DriveType[];
    }
    /**
     * ClearEventsResponse 清除事件响应
     */
    export interface DriveClearEventsResponse {
    }
    /**
     * DeleteEventsAdminRequest 删除事件admin
     */
    export interface DriveDeleteEventsAdminRequest {
        /**
         * userid 用户id
         */
        userid?: string;
        /**
         * file_ids 删除事件的文件id列表
         */
        file_ids?: string[];
        /**
         * types 删除事件的类型列表
         */
        types?: DriveType[];
        /**
         * space 事件所属空间
         */
        space?: string;
    }
    /**
     * DeleteEventsResponse 删除事件响应admin
     */
    export interface DriveDeleteEventsAdminResponse {
    }
    /**
     * DeleteEventsRequest 删除事件
     */
    export interface DriveDeleteEventsRequest {
        /**
         * ids 事件id列表
         */
        ids?: string[];
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * DeleteEventsResponse 删除事件响应
     */
    export interface DriveDeleteEventsResponse {
    }
    export interface DriveEvent {
        /**
         * kind 固定值：drive#event
         */
        kind?: string;
        /**
         * type 事件类型
         */
        type?: DriveType;
        /**
         * type_name 事件类型描述。前端直接使用该字段展现
         */
        type_name?: string;
        /**
         * source 事件源，比如是上传、离线添加
         */
        source?: string;
        /**
         * subject 事件主题
         */
        subject?: string;
        /**
         * device 发生该事件的设备
         */
        device?: string;
        /**
         * id 事件id
         */
        id?: string;
        /**
         * created_time 事件发生时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        created_time?: string;
        /**
         * mime_type 数据的格式，可能是视频、音频或者是pdf等
         */
        mime_type?: string;
        /**
         * file_id 事件对应的文件id。如果type=TYPE_DELETE，该值会为空。
         */
        file_id?: string;
        /**
         * file_name 事件对应的文件名。
         */
        file_name?: string;
        /**
         * icon_url 封面地址
         */
        icon_url?: string;
        /**
         * params 事件内容，json格式
         * play_seconds 客户端上报的播放时间，单位：秒
         * play_duration 客户端上报的视频时长，单位：秒
         * audio_track 客户端上报的音轨索引，索引下标从0开始，0代表第一个音轨，形如："audio_track":"0"
         * subtitle 客户端上报的字幕，内嵌字幕上报索引，索引下标从0开始，0代表第一个内嵌字幕，形如："subtitle":"2"。在线字幕报gcid，形如"subtitle":"30467D13ACA17FFD76086108B2635C88F9D400C0"
         * media_id 客户端上报的文件gcid，值的是取medias里面的media_id字段，形如："media_id":"3D136857102A551559EA90781DBCFC88BE2BC54A"
         * 如果要删除某个上报记录，则将其key对应的val置成delete，例如 删除音轨: "audio_track":"delete"，删除字幕"subtitle":"delete"
         * 注：上报什么字段就覆盖什么字段，没上报的字段旧值不会被覆盖
         */
        params?: {
            [name: string]: string;
        };
        /**
         * updated_time 事件更新时间
         */
        updated_time?: string;
        /**
         * folder_id 文件所在目录
         */
        folder_id?: string;
        /**
         * 事件标签
         */
        label?: string;
        /**
         * 进度
         */
        progress?: number; // int32
        /**
         * 引用的资源
         */
        reference_resource?: ReferenceResource;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * 图片尺寸
     * - SIZE_DEFAULT: 默认 具体行为由服务端定义
     *  - SIZE_SMALL: 小图
     *  - SIZE_MEDIUM: 中图
     *  - SIZE_LARGE: 大图
     *  - SIZE_BIG: 兼容TV端视频缩略图 width*height=720*406
     */
    export type DriveImageSize = "SIZE_DEFAULT" | "SIZE_SMALL" | "SIZE_MEDIUM" | "SIZE_LARGE" | "SIZE_BIG";
    /**
     * ListEventsAdminResponse 事件列表admin
     */
    export interface DriveListEventsAdminResponse {
        /**
         * events 返回的事件列表
         */
        events?: DriveEvent[];
        /**
         * next_page_token 事件偏移key，用以检索下一页
         */
        next_page_token?: string;
    }
    export interface DriveListEventsResponse {
        /**
         * next_page_token 事件偏移key，用以检索下一页
         */
        next_page_token?: string;
        /**
         * events 返回的事件列表
         */
        events?: DriveEvent[];
    }
    /**
     * PatchEventResponse 更新
     */
    export interface DrivePatchEventResponse {
    }
    /**
     * ReportEventRequest 上报事件
     */
    export interface DriveReportEventRequest {
        /**
         * event 事件内容
         */
        event?: DriveEvent;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * ReportEventResponse 上报事件响应
     */
    export interface DriveReportEventResponse {
    }
    /**
     * Type 事件类型
     * - TYPE_PLAY: TYPE_PLAY 播放视频或音频
     *  - TYPE_VIEW: TYPE_VIEW 查看文件内容
     *  - TYPE_CREATE: TYPE_UPDATE 创建文件。
     * 可能是离线下载或者解压等动作创建的文件。
     *  - TYPE_UPDATE: TYPE_UPDATE 更新文件
     *  - TYPE_DOWNLOAD: TYPE_DOWNLOAD 下载文件
     *  - TYPE_DELETE: TYPE_DELETE 删除文件
     *  - TYPE_UPLOAD: TYPE_UPLOAD 上传文件
     *  - TYPE_RESTORE: TYPE_RESTORE 转存文件(夹)
     */
    export type DriveType = "TYPE_UNKNOWN" | "TYPE_PLAY" | "TYPE_VIEW" | "TYPE_CREATE" | "TYPE_UPDATE" | "TYPE_DOWNLOAD" | "TYPE_DELETE" | "TYPE_UPLOAD" | "TYPE_RESTORE";
    export interface GooglerpcStatus {
        code?: number; // int32
        message?: string;
        details?: ProtobufAny[];
    }
    /**
     * `Any` contains an arbitrary serialized protocol buffer message along with a
     * URL that describes the type of the serialized message.
     * 
     * Protobuf library provides support to pack/unpack Any values in the form
     * of utility functions or additional generated methods of the Any type.
     * 
     * Example 1: Pack and unpack a message in C++.
     * 
     *     Foo foo = ...;
     *     Any any;
     *     any.PackFrom(foo);
     *     ...
     *     if (any.UnpackTo(&foo)) {
     *       ...
     *     }
     * 
     * Example 2: Pack and unpack a message in Java.
     * 
     *     Foo foo = ...;
     *     Any any = Any.pack(foo);
     *     ...
     *     if (any.is(Foo.class)) {
     *       foo = any.unpack(Foo.class);
     *     }
     * 
     * Example 3: Pack and unpack a message in Python.
     * 
     *     foo = Foo(...)
     *     any = Any()
     *     any.Pack(foo)
     *     ...
     *     if any.Is(Foo.DESCRIPTOR):
     *       any.Unpack(foo)
     *       ...
     * 
     * Example 4: Pack and unpack a message in Go
     * 
     *      foo := &pb.Foo{...}
     *      any, err := anypb.New(foo)
     *      if err != nil {
     *        ...
     *      }
     *      ...
     *      foo := &pb.Foo{}
     *      if err := any.UnmarshalTo(foo); err != nil {
     *        ...
     *      }
     * 
     * The pack methods provided by protobuf library will by default use
     * 'type.googleapis.com/full.type.name' as the type URL and the unpack
     * methods only use the fully qualified type name after the last '/'
     * in the type URL, for example "foo.bar.com/x/y.z" will yield type
     * name "y.z".
     * 
     * 
     * JSON
     * 
     * The JSON representation of an `Any` value uses the regular
     * representation of the deserialized, embedded message, with an
     * additional field `@type` which contains the type URL. Example:
     * 
     *     package google.profile;
     *     message Person {
     *       string first_name = 1;
     *       string last_name = 2;
     *     }
     * 
     *     {
     *       "@type": "type.googleapis.com/google.profile.Person",
     *       "firstName": <string>,
     *       "lastName": <string>
     *     }
     * 
     * If the embedded message type is well-known and has a custom JSON
     * representation, that representation will be embedded adding a field
     * `value` which holds the custom JSON in addition to the `@type`
     * field. Example (for message [google.protobuf.Duration][]):
     * 
     *     {
     *       "@type": "type.googleapis.com/google.protobuf.Duration",
     *       "value": "1.212s"
     *     }
     */
    export interface ProtobufAny {
        [name: string]: any;
        /**
         * A URL/resource name that uniquely identifies the type of the serialized
         * protocol buffer message. This string must contain at least
         * one "/" character. The last segment of the URL's path must represent
         * the fully qualified name of the type (as in
         * `path/google.protobuf.Duration`). The name should be in a canonical form
         * (e.g., leading "." is not accepted).
         * 
         * In practice, teams usually precompile into the binary all types that they
         * expect it to use in the context of Any. However, for URLs which use the
         * scheme `http`, `https`, or no scheme, one can optionally set up a type
         * server that maps type URLs to message definitions as follows:
         * 
         * * If no scheme is provided, `https` is assumed.
         * * An HTTP GET on the URL must yield a [google.protobuf.Type][]
         *   value in binary format, or produce an error.
         * * Applications are allowed to cache lookup results based on the
         *   URL, or have them precompiled into a binary to avoid any
         *   lookup. Therefore, binary compatibility needs to be preserved
         *   on changes to types. (Use versioned type names to manage
         *   breaking changes.)
         * 
         * Note: this functionality is not currently available in the official
         * protobuf release, and it is not used for type URLs beginning with
         * type.googleapis.com.
         * 
         * Schemes other than `http`, `https` (or the empty scheme) might be
         * used with implementation specific semantics.
         */
        "@type"?: string;
    }
}
