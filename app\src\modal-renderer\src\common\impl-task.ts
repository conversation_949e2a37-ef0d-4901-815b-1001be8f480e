  import { TaskManager } from '@root/common/task/client/task-manager';
  import { BtTask } from '@root/common/task/client/bt-task';
  import { GroupTask } from '@root/common/task/client/group-task';
  import { TaskType } from '@root/common/task/base';


  export namespace taskExtraClientFunc {
    export async function getTask (taskId: number) {
      const task = await TaskManager.GetInstance().findTaskById(taskId);
      if (!task) { return null}
      const taskBase = await task.getTaskBase()
      const taskType = taskBase && taskBase.taskType
      if (taskType === TaskType.Bt) {
        const btTask = task.toBtTask();
        return btTask;
      } else if (taskType === TaskType.Group) {
        const groupTask = task.toGroupTask();
        return groupTask;
      } else if (taskType === TaskType.P2sp) {
        const p2spTask = task.toP2spTask();
        return p2spTask;
      } else if (taskType === TaskType.Emule) {
        const emuleTask = task.toEmuleTask();
        return emuleTask;
      }
      return null
    }
    /** 获取task基本信息 */
    export async function getTaskBase (taskId: number) {
      const task = await TaskManager.GetInstance().findTaskById(taskId);
      return task?.getTaskBase() ?? null
    }
    /** 获取下载总数 */
    export async function getDownloadCount(taskId: number) {
      const task = await getTask(taskId)
      if (task && 'getDownloadCount' in task) {
        return task.getDownloadCount()
      }
      return 0
    }
    /** 获取总数 */
    export async function getDownloadTotal(taskId: number) {
      const task = await getTask(taskId)
      if (task && 'getDownloadCount' in task) {
        return task.getTotalCount()
      }
      return 0
    }
    /** 获取bt任务的子文件信息 */
    export async function getBtSubFileInfos(taskId: number) {
      const task = await getTask(taskId)
      if (task && 'getBtFileInfos' in task) {
        return task.getBtFileInfos()
      }
      return null
    }

    export async function updateDownloadIndex(taskId: number, indexList: number[]) {
      const task = await getTask(taskId)
      if (task && 'updateDownloadIndex' in task) {
        return task.updateDownloadIndex(indexList)
      }
      return null
    }

    // ====================任务组==============================

    /** 获取任务组详情 */
    export async function getGroupSubTasks(groupTaskId: number) {
      const groupTask = await getTask(groupTaskId)
      console.log('>>>>>>>>>>>>>>> groupTask', groupTask)
      if (groupTask && 'getSubTaskIds' in groupTask) {
        return groupTask.getSubTaskIds()
      }
      return null
    }
    /** 获取任务组下载总数 */
    export async function getGroupDownloadCount(groupTaskId: number) {
      const groupTask = await getTask(groupTaskId)
      if (groupTask && 'getDownloadCount' in groupTask) {
        return groupTask.getDownloadCount()
      }
      return 0
    }

    export async function getGroupTotalCount(groupTaskId: number) {
      const groupTask = await getTask(groupTaskId)
      if (groupTask && 'getDownloadCount' in groupTask) {
        return groupTask.getTotalCount()
      }
      return 0
    }

    /** 跟新任务组的bt子任务选中状态 */
    export async function updateGroupBtSubSelectTask(groupTaskId: number, info: {taskId: number, indexs: number[]}[]) {
      const groupTask = await getTask(groupTaskId)
      if (groupTask && 'updateSubBtSelectTask' in groupTask) {
        return groupTask.updateSubBtSelectTask(info)
      }
      return 0
    }

    /** 跟新任务组的子任务选中状态 */
    export async function updateGroupSubSelectTask(groupTaskId: number, taskId: number[]) {
      const groupTask = await getTask(groupTaskId)
      if (groupTask && 'updateSubSelectTask' in groupTask) {
        return groupTask.updateSubSelectTask(taskId)
      }
      return 0
    }
  }