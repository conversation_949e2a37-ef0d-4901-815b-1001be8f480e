import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { Logger } from '@root/common/logger';
import { Channels } from '@root/common/constant'
import { server } from '@xunlei/node-net-ipc/dist/ipc-server';
import { AsyncRemoteCall } from '@root/common/renderer-async-remote-call';

const logger = new Logger({ tag: 'quit-promises' })

type QuitPromiseCallback = () => Promise<boolean>;

interface IIpcCallbackItem {
  context: any;
  callback: QuitPromiseCallback;
}

class QuitPromisesServer {
  private promiseFuncArray: QuitPromiseCallback[];
  private promiseBeforeFuncArray: QuitPromiseCallback[];
  private promiseAfterFuncArray: QuitPromiseCallback[];
  private promiseIpcCallbackArray: IIpcCallbackItem[];

  constructor() {
    this.promiseFuncArray = [];
    this.promiseBeforeFuncArray = [];
    this.promiseAfterFuncArray = [];
    this.promiseIpcCallbackArray = [];

    ipcRenderer.once('OnQueryEndSession', async () => {
      ipcRenderer.send(Channels.Close)
      await this.runAllPromises();
    });

    const functions: { [method: string]: any } = {
      AddQuitPromise: this.addQuitPromise.bind(this),
      QuitApp: this.quitApp.bind(this),
    };
    server.registerFunctions(functions);
  }
  private addQuitPromise(client: unknown, context: any, callback: QuitPromiseCallback): void {
    logger.log('addQuitPromise context:', context);
    this.promiseIpcCallbackArray.push({ context, callback });
  }

  public add(promiseFunc: QuitPromiseCallback): void {
    this.promiseFuncArray.push(promiseFunc);
  }

  public addBefore(promiseFunc: QuitPromiseCallback): void {
    this.promiseBeforeFuncArray.push(promiseFunc);
  }

  public addAfter(promiseFunc: QuitPromiseCallback): void {
    this.promiseAfterFuncArray.push(promiseFunc);
  }

  private async runPromises(promiseFuncArray: QuitPromiseCallback[]): Promise<void> {
    const promises: (Promise<boolean>)[] = [];
    for (const promiseFunc of promiseFuncArray) {
      if (typeof promiseFunc === 'function') {
        promises.push(promiseFunc().catch());
      }
    }
    await Promise.all(promises);
    return ;
  }

  private async runAllPromises(): Promise<void> {
    let timeoutId: number | undefined = undefined;
    await Promise.race([new Promise<void>(
      async (resolve: () => void, reject: (err: Error) => void): Promise<void> => {
        logger.log('runPromises before');
        await this.runPromises(this.promiseBeforeFuncArray);
        logger.log('runIpcPromises & runPromises');
        const ipcCallbacks: QuitPromiseCallback[] = [];
        for (const value of this.promiseIpcCallbackArray) {
          ipcCallbacks.push(value.callback);
        }

        await Promise.all([this.runPromises(ipcCallbacks), this.runPromises(this.promiseFuncArray)]);
        logger.log('runPromises after');
        await this.runPromises(this.promiseAfterFuncArray);

        if (timeoutId !== undefined) {
          clearTimeout(timeoutId);
          timeoutId = undefined;
        }

        resolve();
      }
    ), new Promise<void>(
      (resolve: () => void, reject: (reason?: any) => void): void => {
          timeoutId = setTimeout(() => {
            timeoutId = undefined;
            // 超时返回
            logger.log('runAllPromises timeout reject');
            resolve();
          }, 5 * 1000) as any;
        }
    )
    ]);
  }

  public async quitApp(): Promise<void> {
    ipcRenderer.send(Channels.Close)
    await this.runAllPromises();

    logger.log('app.quit before');
    const appObj: any = await AsyncRemoteCall.GetInstance().getApp();
    await appObj.quit();
    // logger.information('app.quit end');
  }
}
export const quitPromisesServer: QuitPromisesServer = new QuitPromisesServer();
