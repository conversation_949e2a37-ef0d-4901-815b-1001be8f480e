import { CallApiProxyImplWithIpcClient } from '../call-api-impl';
import * as BaseType from '../base'
export class GroupTask {
    private apiProxy: CallApiProxyImplWithIpcClient | null = null;
    taskId: number = 0;
    constructor(apiProxy: CallApiProxyImplWithIpcClient, id: number) {
        this.apiProxy = apiProxy;
        this.taskId = id;
    }

    public async getSubTaskIds(): Promise<number[]> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskGetSubTaskIds', this.taskId);
        if (info.bSucc) {
            return info.result as number[];
        }

        return [];
    }

    // 彻底删除子任务的下载
    public async deleteSubTask(ids: number[]): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskDeleteSubTask', this.taskId, ids);
    }

    // 取消子任务下载，可以恢复
    public async cancelSubTask(ids: number[]): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskCancelSubTask', this.taskId, ids);
    }

    // 恢复取消下载的
    public async downloadSubTask(ids: number[]): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskDownloadSubTask', this.taskId, ids);
    }

    public async updateSubSelectTask(ids: number[]): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskUpdateSubSelectTask', this.taskId, ids);
    }

    // 获取总共下载的个数
    public async getDownloadCount(): Promise<number> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskGetDownloadCount', this.taskId);
        if (info.bSucc) {
            return info.result as number;
        }

        return 0;
    }

    // 获取已完成下载的个数
    public async getCompleteCount(): Promise<number> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskGetCompleteCount', this.taskId);
        if (info.bSucc) {
            return info.result as number;
        }

        return 0;
    }

    // 获取总共子文件的个数
    public async getTotalCount(): Promise<number> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskGetTotalCount', this.taskId);
        if (info.bSucc) {
            return info.result as number;
        }

        return 0;
    }

    // 彻底删除任务组里面的bt的子任务的下载
  public async deleteSubBtTask(info: {taskId: number, indexs: number[]}[]): Promise<void> {
    await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskDeleteSubBtTask', this.taskId, info);
  }

  // 取消子任务组里面的bt的任务下载
  public async cancelSubBtTask(info: {taskId: number, indexs: number[]}[]): Promise<void> {
    await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskCancelSubBtTask', this.taskId, info);
  }

  // 恢复任务组里面的bt的取消下载的
  public async downloadSubBtTask(info: {taskId: number, indexs: number[]}[]): Promise<void> {
    await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskDownloadSubBtTask', this.taskId, info);
  }

  public async updateSubBtSelectTask(info: {taskId: number, indexs: number[]}[]): Promise<void> {
    await this.apiProxy!.CallApi('TaskManagerTaskGroupTaskUpdateSubBtSelectTask', this.taskId, info);
  }
}