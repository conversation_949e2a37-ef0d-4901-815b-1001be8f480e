import { config } from "@root/common/config/config"
import { ConfigValueType } from '@root/common/config/types';

// 下载限速相关
export const DOWNLOAD_SPEED_SETTING_NAME_MAP = {
  'Type': 'ConfigNet-ConfigNet_Type',
  "UploadSpeedChk": "ConfigNet-ConfigNet_Custom_UploadSpeedChk",
  "DownloadSpeedChk": "ConfigNet-ConfigNet_Custom_DownloadSpeedChk",
  "TimeSwitch": "ConfigNet-ConfigNet_Custom_Time_Switch",
  "MaxDownloadSpeedTmp": "ConfigNet-ConfigNet_Custom_MaxDownloadSpeedTmp",
  "MaxUploadSpeedTmp": "ConfigNet-ConfigNet_Custom_MaxUploadSpeedTmp",
  "MaxDownloadSpeed": "ConfigNet-ConfigNet_Custom_MaxDownloadSpeed",
  "MaxUploadSpeed": "ConfigNet-ConfigNet_Custom_MaxUploadSpeed",
  "TimeBeginHour": "ConfigNet-ConfigNet_Custom_Time_Begin_Hour",
  "TimeBeginMinute": "ConfigNet-ConfigNet_Custom_Time_Begin_Minute",
  "TimeEndHour": "ConfigNet-ConfigNet_Custom_Time_End_Hour",
  "TimeEndMinute": "ConfigNet-ConfigNet_Custom_Time_End_Minute"
}

// 开机与启动相关
export const START_SETTING_NAME_MAP = {
  'NewTaskDlgWithoutMainWnd': 'TaskDefaultSettings-NewTaskDlgWithoutMainWnd',
  'BossKeySwitch': 'BossKey-BossKeySwitch',
  'BossKeyName': 'BossKey-BossKeyName',
  'AutoUpdate': 'ConfigNormalSession-AutoUpdate',
  'AutoRun': 'ConfigNormalSession-ConfigNormal_AutoRun',
  'AntiDisturb': 'Others-AntiDisturb',
}

// 提醒相关
export const REMIND_SETTING_NAME_MAP = {
  "UnzipTipSuccess": "ConfigMsg-UnzipTipSuccess",
  "UnzipTipFail": "ConfigMsg-UnzipTipFail",
  "AddNetDiskMessageSuccess": "ConfigMsg-addNetDiskMessageSuccess",
  "AddNetDiskMessageFail": "ConfigMsg-addNetDiskMessageFail",
  "FailSuggest": "ConfigMsg-ConfigMsg_FailSuggest",
  "NotifyAdvertisement": "ConfigMsg-NotifyAdvertisement",
  "OneKeyDownload": "ConfigMsg-ConfigMsg_OneKeyDownload",
  "Finish": "ConfigMsg-ConfigMsg_Finish",
  "PlayWaveWhileFinish": "ConfigMsg-ConfigMsg_PlayWaveWhileFinish"
}

// 云盘相关
export const THUNER_PATH_SETTING_NAME_MAP = {
  "VODCachePath": "ThunderPanPlugin-VODCachePath",
  "MaxDownloadTaskNum": "ThunderPanPlugin-MaxDownloadTaskNum",
  "DefaultDownloadPath": "ThunderPanPlugin-defaultDownloadPath",
  "DefaultSavePath": "ThunderPanPlugin-defaultSavePath",
  "LastUsePath": "ThunderPanPlugin-lastUsePath",
  "SetVODCachePath": "ThunderPanPlugin-SetVODCachePath",
  "UseDefault": "ThunderPanPlugin-useDefault",
  "StartCloudStopDownload": "ThunderPanPlugin-StartCloudStopDownload",
  "ShellContextmenu": "ThunderPanPlugin-ShellContextmenu",
  "MaxUploadTaskNum": "ThunderPanPlugin-MaxUploadTaskNum"
}

// 接管相关
export const CONNECT_SETTING_NAME_MAP = {
  "MotitorAll": "Motitor-MotitorAll",
  "MonitorClipBoard": "Monitor-MonitorClipBoard",
  "WatchTraditionLink": "Monitor-WatchTraditionLink",
  "ConfigWatch_Bt": "Monitor-ConfigWatch_Bt",
  "EMuleWatchLink": "EMuleGenericSettings-EMuleWatchLink",
  "MagnetWatchLink": "MagnetGenericSettings-MagnetWatchLink",
  "VideoExtendNames": "Monitor-VideoExtendNames",
  "WordExtendNames": "Monitor-WordExtendNames",
  "ImageExtendNames": "Monitor-ImageExtendNames",
  "MusicExtendNames": "Monitor-MusicExtendNames",
  "ZipExtendNames": "Monitor-ZipExtendNames",
  "ExeExtendNames": "Monitor-ExeExtendNames",
  "ExtraExtendNames": "Monitor-ExtraExtendNames",
  "ExtendNames": "Monitor-ExtendNames",
  "ExtendNamesAdded": "Monitor-ExtendNamesAdded",
  "ExtendNamesRemoved": "Monitor-ExtendNamesRemoved",
}

// 下载设置相关
export const DOWNLOAD_SETTING_NAME_MAP = {
  "DefaultPath": "TaskDefaultSettings-DefaultPath",
  "UseLastCatalog": "TaskDefaultSettings-UseLastCatalog",
  "OrignHostThreads": "TaskDefaultSettings-OrignHostThreads",
  "MaxResourceLimit": "TaskDefaultSettings-MaxResourceLimit",
  "MaxResourceCount": "TaskDefaultSettings-MaxResourceCount",
  "AwayModeEnabled": "TaskDefaultSettings-AwayModeEnabled",
}

// 任务相关
export const TASK_SETTING_NAME_MAP = {
  "TaskCountAutoAdd": "ConfigNoLimitDownload-TaskCountAutoAdd",
  "TaskCountAutoAddRate": "ConfigNoLimitDownload-TaskCountAutoAddRate",
  "OpenNoLimitDownload": "ConfigNoLimitDownload-OpenNoLimitDownload",
  "NoLimitDownloadFileSize": "ConfigNoLimitDownload-NoLimitDownloadFileSize",
  "MaxRunningTaskCount": "ConfigNormalSession-ConfigNormal_MaxRunningTaskCount",
  "AutoStartUnFinishedTask": "ConfigNormalSession-ConfigNormal_AutoStartUnFinishedTask",
  "AutoSlowTask2Tail": "ConfigNormalSession-ConfigNormal_AutoSlowTask2Tail",
  "AutoDeleteNotExistFile": "ConfigNormalSession-ConfigNormal_AutoDeleteNotExistFile"
}

// 高级设置相关
export const ADVANCED_SETTING_NAME_MAP = {
  "AutoBTNewTask": "BtGenericSettings-AutoBTNewTask",
  "AssocTorrent": "BtGenericSettings-AssocTorrent",
  "DiskCacheSelect": "DiskCache-DiskCacheSelect",
  "OpenMirrorImageIncreaseSpeed": "ConfigNet-ConfigNet_OpenMirrorImageIncreaseSpeed",
  "OpenXunleiP2PIncreaseSpeed": "ConfigNet-ConfigNet_OpenXunleiP2PIncreaseSpeed",
  "ProxyType": "ProxySetting-ConfigProxy_Type",
  "ConnectTypeHub": "ProxySetting-ConnectType_Hub",
  "ConnectTypeHttp": "ProxySetting-ConnectType_Http",
  "ConnectTypeFtp": "ProxySetting-ConnectType_Ftp",
  "ProxyList": "ProxyList-proxys",
  "ProxyName": "ConnectType-ProxyName",
}

export const videoDefaultExtendNames = '.3g2;.3gp;.asf;.asx;.avi;.divx;.dv;.flv;.f4v;.m2ts;.m4v;.mkv;.mov;.mp4;.mpe;.mpeg;.mpg;.qt;.rm;.rmvb;.ts;.vob;.webm;.wmv;.xv;.swf;.tp;.dat;.m3u8;.xvx;';
export const docDefaultExtendNames = '.csv;.html;.htm;.ini;.json;.tsv;.xml;.yaml;.yml;.md;.markdown;.cnf;.conf;.cfg;.log;.txt;.epub;.mobi;.chm;.prc;.doc;.docx;.ppt;.pptx;.xls;.xlsx;.pdf;.xmind;.bat;.bin;.odt;';
export const imageDefaultExtendNames = '.ai;.bmp;.cdr;.cr2;.dwg;.dxf;.eps;.exif;.fpx;.gif;.hdri;.heif;.ico;.jfif;.jif;.jpe;.jpeg;.jpeg2000;.jpg;.jxr;.pcd;.pcx;.png;.psd;.raw;.svg;.tga;.tif;.tiff;.ufo;.webp;.wmf;.webp;.apng;';
export const audioDefaultExtendNames = '.aac;.aiff;.amr;.cda;.flac;.m4a;.mid;.mp3;.ogg;.vqf;.wav;.wma;.ra;.ape;.mpga;';
export const archiveDefaultExtendNames = '.rar;.7z;.ar;.bz2;.cab;.crx;.dcm;.elf;.eot;.gz;.iso;.lz;.nes;.ps;.rar;.rtf;.sqlite;.tar;.xz;.z;.zip;.arj;.sit;.lzh;.gz;.tgz;';
export const softwareDefaultExtendNames = '.apk;.rpm;.deb;.ipa;.pxl;.pkg;.dmg;.msi;.exe;.appx;.msix;.iso;.bin;.jar;.sisx;';
export const otherDefaultExtendNames = '.dll;.hqx;.msu;.pth;.pt;.ckpt‌;.bin‌;.json‌;.onnx‌;.gguf‌;.dds;.otf;.safetensors;.srt;.web;.vtt;.stl;.sbv;.ass;.dfxp;.ttml;.ssa;.sub;.idx;.vtt;.mpl2;.smi;.otf;.ttf;.woff;.woff2;';

export const EXTEND_NAME_MAP = {
  [CONNECT_SETTING_NAME_MAP.VideoExtendNames]: videoDefaultExtendNames,
  [CONNECT_SETTING_NAME_MAP.WordExtendNames]: docDefaultExtendNames,
  [CONNECT_SETTING_NAME_MAP.ImageExtendNames]: imageDefaultExtendNames,
  [CONNECT_SETTING_NAME_MAP.MusicExtendNames]: audioDefaultExtendNames,
  [CONNECT_SETTING_NAME_MAP.ZipExtendNames]: archiveDefaultExtendNames,
  [CONNECT_SETTING_NAME_MAP.ExeExtendNames]: softwareDefaultExtendNames,
  [CONNECT_SETTING_NAME_MAP.ExtraExtendNames]: otherDefaultExtendNames,
}

export const setSettingConfig = async (key: string, value: ConfigValueType) => {
  console.log('setSettingConfig', key, value)

  if (key.indexOf('-') === -1) {
    console.error('setSettingConfig error: key is not valid')
  }

  const [section, keyName] = key.split('-')
  if (section && keyName) {
    await config.setValue(section, keyName, value, true)
  }
  
}

export const getSettingConfig = async (key: string, defaultValue?: ConfigValueType) => {
  if (key.indexOf('-') === -1) {
    console.error('getSettingConfig error: key is not valid')
    return defaultValue ?? ''
  }

  const [section, keyName] = key.split('-')
  if (section && keyName) {
    const configValue = await config.getValue(section, keyName, defaultValue ?? '')
    return configValue
  }

  return defaultValue ?? ''
}