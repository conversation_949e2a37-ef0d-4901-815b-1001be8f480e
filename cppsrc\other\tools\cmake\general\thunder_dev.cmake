MACRO(DEV_CONFIG_BEGIN log_file_prefix use_static use_log_force)
    if("${PROJECT_SOURCES}" STREQUAL "")
      message(FATAL_ERROR "The PROJECT_SOURCES is empty")
    endif()

    # if the glog is enabled, then add the compile option to the target module.
    add_compile_options(-DGLOG_ENABLED)

    list(APPEND PROJECT_SOURCES ${THUNDER_COMMON_PATH}/logger/log_utils.h)
    list(APPEND PROJECT_SOURCES ${THUNDER_COMMON_PATH}/logger/log_utils.cpp)

    if("${log_file_prefix}" STREQUAL "")
      add_compile_options(-DLOG_MODULE_NAME="${log_file_prefix}${THUNDER_ARCH}")
    else()
      add_compile_options(-DLOG_MODULE_NAME="${PROJECT_NAME}${THUNDER_ARCH}")
    endif()
    message("The current log_file_prefix is ${log_file_prefix}${THUNDER_ARCH}")

    if(${use_log_force})
      add_compile_options(-DUSE_LOG_FORCE)
    endif()

    if(${use_static})
      set(USE_STATIC_GLOG ON)
      add_compile_options( -DUSE_STATIC_GLOG )
    else()
      set(USE_STATIC_GLOG OFF)
    endif()
ENDMACRO()

MACRO(DEV_CONFIG_END)
    target_include_directories (${PROJECT_NAME} PRIVATE 
      ${THUNDER_COMMON_PATH}/logger
    )
    USE_3RD_PARTY_LIBRARY(GLOG)
    USE_3RD_PARTY_LIBRARY(GFLAGS)
    USE_3RD_PARTY_LIBRARY(NLOHMANN)
ENDMACRO()
