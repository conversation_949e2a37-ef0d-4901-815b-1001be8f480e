{"assets": [], "ddd": 0, "fr": 30, "h": 20, "ip": 0, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Group 1", "hd": false, "sr": 1, "ks": {"a": {"a": 0, "k": [0, 0]}, "o": {"a": 0, "k": 100}, "p": {"a": 0, "k": [10, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 61, "st": 0, "bm": 0, "shapes": [{"ty": "gr", "hd": false, "nm": "旋转 Group", "bm": 0, "it": [{"ty": "sh", "hd": false, "nm": "旋转", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.047, 0.023], [0, 0], [0, 0], [-0.09, 0.218], [-0.22, -0.05], [0, 0], [0, 0], [1.058, 0.003], [0.45, -0.43], [0.19, 0.2], [-0.2, 0.19], [-0.94, 0], [0, -1.93], [0, 0], [0.01, -0.03], [0, -0.01], [0, 0], [0, -0.01], [0.01, -0.01], [0, 0], [0.01, -0.01], [0.02, -0.01], [0.01, -0.01], [0.01, 0], [0.01, -0.01], [0.02, -0.01], [0, 0]], "o": [[-0.052, -0.003], [0, 0], [0, 0], [-0.19, -0.14], [0.1, -0.22], [0, 0], [0, 0], [-0.351, -0.998], [-0.67, 0], [-0.2, 0.19], [-0.19, -0.2], [0.63, -0.61], [1.93, 0], [0, 0], [0, 0.04], [0, 0], [0, 0], [0, 0], [-0.01, 0.01], [0, 0], [0, 0], [-0.01, 0], [-0.01, 0], [-0.01, 0], [-0.01, 0], [-0.02, 0], [0, 0], [0, 0]], "v": [[12.95, 10.5], [12.8, 10.46], [11.51, 9.89], [11.42, 9.84], [11.25, 9.23], [11.81, 8.94], [11.91, 8.97], [12.35, 9.16], [10, 7.49], [8.26, 8.19], [7.55, 8.18], [7.56, 7.47], [9.99, 6.49], [13.49, 9.99], [13.49, 10.08], [13.46, 10.19], [13.45, 10.22], [13.42, 10.28], [13.4, 10.31], [13.37, 10.35], [13.36, 10.35], [13.33, 10.39], [13.29, 10.42], [13.25, 10.44], [13.21, 10.46], [13.17, 10.47], [13.11, 10.49], [12.98, 10.49]]}}}, {"ty": "sh", "hd": false, "nm": "旋转", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.93], [-0.03, 0.07], [-0.14, 0.03], [0, 0], [-0.07, -0.01], [0, 0], [-0.03, -0.01], [0, 0], [0, 0], [0.12, -0.25], [0.25, 0.12], [0, 0], [-1.293, 1.33], [0, 0], [-0.172, -0.164], [0.19, -0.2], [0, 0], [0.93, 0]], "o": [[-1.93, 0], [0, -0.07], [0.07, -0.15], [0, 0], [0.06, -0.01], [0, 0], [0.02, 0], [0, 0], [0, 0], [0.25, 0.11], [-0.11, 0.25], [0, 0], [0.618, 1.749], [0, 0], [0.195, -0.135], [0.2, 0.19], [0, 0], [-0.63, 0.59], [0, 0]], "v": [[10, 13.5], [6.5, 10], [6.54, 9.79], [6.88, 9.51], [6.9, 9.51], [7.09, 9.51], [7.1, 9.51], [7.17, 9.53], [7.2, 9.53], [8.49, 10.1], [8.74, 10.76], [8.08, 11.01], [7.64, 10.82], [11.79, 11.73], [11.87, 11.66], [12.5, 11.71], [12.51, 12.42], [12.39, 12.54], [9.99, 13.49]]}}}, {"ty": "fl", "hd": false, "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "nm": "Transform", "a": {"a": 0, "k": [9.995, 9.995]}, "o": {"a": 0, "k": 100}, "p": {"a": 0, "k": [-0.275, 0.495]}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": 0.75, "y": 0.75}, "o": {"x": 0.25, "y": 0.25}}, {"t": 60, "s": [360], "i": {"x": 0, "y": 0}, "o": {"x": 1, "y": 1}}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}], "np": 0}, {"ty": "gr", "hd": false, "nm": "Path 1 Group", "bm": 0, "it": [{"ty": "sh", "hd": false, "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [-2.98, 0], [-0.39, -2.88], [0, -1.67], [2.29, 0], [0, 0], [0, 2.29], [-1.43, 0.65]], "o": [[0.39, -2.88], [2.98, 0], [1.43, 0.65], [0, 2.28], [0, 0], [-2.28, 0], [0, -1.67], [0, 0]], "v": [[4.42, 8.1], [10.27, 3], [16.12, 8.1], [18.54, 11.86], [14.4, 16], [6.14, 16], [2, 11.86], [4.42, 8.1]]}}}, {"ty": "gf", "hd": false, "bm": 0, "e": {"a": 0, "k": [-15.973333009276914, 40.23333308449587]}, "g": {"p": 2, "k": {"a": 0, "k": [0, 0.282, 0.545, 0.969, 1, 0.463, 0.671, 1, 0, 1, 1, 1]}}, "t": 1, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "r": 1, "s": {"a": 0, "k": [-7.389998940769672, 28.14999957755824]}}, {"ty": "tr", "nm": "Transform", "a": {"a": 0, "k": [0, 0]}, "o": {"a": 0, "k": 100}, "p": {"a": 0, "k": [-10.27, -9.5]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}], "np": 0}]}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Screen", "hd": false, "sr": 1, "ks": {"a": {"a": 0, "k": [10, 10]}, "o": {"a": 0, "k": 100}, "p": {"a": 0, "k": [10, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}, "ao": 0, "ip": 0, "op": 61, "st": 0, "bm": 0, "shapes": [{"ty": "gr", "hd": false, "nm": "Screen Group", "bm": 0, "it": [{"ty": "rc", "hd": false, "nm": "Screen", "d": 1, "p": {"a": 0, "k": [10, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [20, 20]}}, {"ty": "fl", "hd": false, "bm": 0, "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "nm": "Transform", "a": {"a": 0, "k": [0, 0]}, "o": {"a": 0, "k": 100}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}], "np": 0}]}], "meta": {"g": "@phase-software/lottie-exporter 0.7.0"}, "nm": "", "op": 60, "v": "5.6.0", "w": 20}