@import "./base.css";
@import "./reset.css";
@import "./unocss.css";
@import "./xly-icon-type.css";
@import "./file-icon.css";
/* @import "./font.css"; */
@import "./animate.css";
@import "./input.css";

body {
  color: var(--font-1);
  overflow: hidden;
  background-size: cover;
  user-select: none;
  width: 100%;
  padding: 0;
  margin: 0;
}

:root {
  --z-index-notice: 100005;
  --z-index-alert: 100000;
  --z-index-model: 10000;
  --z-index-panel: 2000;
  --z-index-area: 1000;
  --z-index-drawer: 99;
}

#app {
  height: 100vh;
}

.mt-0 {
  margin-top: 0 !important;
}

.td-drag-area {
  position: absolute;
  z-index: var(--z-index-area);
  border: solid 0.5px var(--border-border-primary);
  background: rgba(34, 109, 245, 0.1);
}
