
import { platform } from '@root/common/env'
import { type MaybeRefOrGetter, type ShallowRef } from 'vue'
import { Logger } from '@root/common/logger'
import { useMouseDragAndClick } from './useMouseDragMoveAndClick'
import { useMouseDragAndClickViaMain } from './useMouseDragMoveAndClickViaMain'

const logger = new Logger({ tag: 'Drag' })

type TMouseMoveAndClickProps = {
  isSetWindowDraggable?: boolean

  onClick?: (e?: MouseEvent) => void
  onDbClick?: (e?: MouseEvent) => void
  onMouseDown?: (e?: MouseEvent) => void
  onMouseUp?: (e?: MouseEvent) => void
  // onDrag?: (e?: MouseEvent) => void
  onMouseMove?: (e?: MouseEvent) => void
  onMouseLeave?: (e?: MouseEvent) => void
  onKeyDown?: (code: number) => void
  onMouseWheel?:(delta: number) =>void
}

export function useMouseDragMoveAndClickViaEventAndMain(
  target: Readonly<ShallowRef<HTMLElement | null>>,
  props?: TMouseMoveAndClickProps,
  eventOptions?: MaybeRefOrGetter<boolean | AddEventListenerOptions>,
) {
  if (!platform.isWindows) {
    // ? mac 走原来逻辑
    useMouseDragAndClick(target, props, eventOptions)
    return
  } else {
    // ? windows 两种都监听, 关掉GPU加速后透明背景的事件会透传
    useMouseDragAndClick(target, props, eventOptions)
    useMouseDragAndClickViaMain(target, props, eventOptions)
  }
}
