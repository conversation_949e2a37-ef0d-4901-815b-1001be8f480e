<template>
  <transition name="back-to-top-fade">
    <div 
      v-if="visible" 
      class="back-to-top-button"
      @click="scrollToTop"
      v-tooltip="'返回顶部'"
    >
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="24" cy="24" r="20" fill="#272E3B" fill-opacity="0.2"/>
        <path d="M24 17V31M24 17L17 25M24 17L31 25" stroke="white" stroke-width="1.2" stroke-linecap="round"/>
      </svg>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const visible = ref(false)
let observer: MutationObserver | null = null
let scrollContainers: Element[] = []

const checkScroll = () => {
  // 默认不显示按钮
  visible.value = false
  
  // 检查所有滚动容器
  for (const container of scrollContainers) {
    // 获取滚动位置和容器尺寸
    const scrollTop = container.scrollTop || (container as any).scrollY || 0
    const rect = container.getBoundingClientRect()
    
    // 如果滚动超过一屏高度，显示按钮
    if (scrollTop > rect.height) {
      visible.value = true
      return
    }
  }
}

const scrollToTop = () => {
  // 滚动到第一个容器的顶部
  if (scrollContainers.length > 0) {
    scrollContainers[0].scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }
}

const bindScrollListeners = () => {
  // 查找所有带有 data-scroll-container 属性的元素
  const containers = document.querySelectorAll('[data-scroll-container]')
  
  // 过滤出可见的容器
  scrollContainers = Array.from(containers).filter(container => {
    // 检查元素是否在DOM中
    if (!document.body.contains(container)) return false;
    
    // 检查元素是否可见（有宽高）
    const rect = container.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) return false;
    
    // 检查元素样式
    const computedStyle = window.getComputedStyle(container);
    if (computedStyle.display === 'none' || computedStyle.visibility === 'hidden') return false;
    
    return true;
  });
  console.log(scrollContainers, 'scrollContainers')
  
  // 为每个可见容器绑定滚动事件
  scrollContainers.forEach(container => {
    container.addEventListener('scroll', checkScroll)
  })
  
  // 初始检查
  checkScroll()
}

const unbindScrollListeners = () => {
  scrollContainers.forEach(container => {
    container.removeEventListener('scroll', checkScroll)
  })
  scrollContainers = []
}

onMounted(() => {
  // 初始绑定
  bindScrollListeners()
  
  // 监听 DOM 变化，自动绑定新的滚动容器
  // 监听带有data-scroll-container属性的元素的添加和删除
  observer = new MutationObserver((mutations) => {
    let shouldRebind = false;
    
    for (const mutation of mutations) {
      // 处理节点添加和删除
      if (mutation.type === 'childList') {
        // 检查是否添加了带有data-scroll-container属性的元素
        for (const node of Array.from(mutation.addedNodes)) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            if (element.hasAttribute('data-scroll-container') || 
                element.querySelector('[data-scroll-container]')) {
              shouldRebind = true;
              break;
            }
          }
        }
        
        // 检查是否删除了带有data-scroll-container属性的元素
        for (const node of Array.from(mutation.removedNodes)) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            if (element.hasAttribute('data-scroll-container') || 
                element.querySelector('[data-scroll-container]')) {
              shouldRebind = true;
              break;
            }
          }
        }
      }
    }
    
    // 如果有滚动容器的添加或删除，重新绑定事件
    if (shouldRebind) {
      unbindScrollListeners();
      bindScrollListeners();
    }
  })
  
    // 监听整个文档的子节点变化
  observer.observe(document.body, {
    childList: true,
    subtree: true
  })
})

onUnmounted(() => {
  if (observer) {
    observer.disconnect()
    observer = null
  }
  unbindScrollListeners()
})
</script>

<style lang="scss" scoped>
.back-to-top-button {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 48px;
  height: 48px;

  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 88;
  transition: all 0.3s ease;

  &:hover {
    svg circle {
      fill: black;
      fill-opacity: 0.8;
    }
  }

  svg {
    width: 48px;
    height: 48px;
    
    circle {
      transition: fill 0.3s ease, fill-opacity 0.3s ease;
    }
  }
}

.back-to-top-fade-enter-active,
.back-to-top-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.back-to-top-fade-enter-from,
.back-to-top-fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.back-to-top-fade-enter-to,
.back-to-top-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}
</style>