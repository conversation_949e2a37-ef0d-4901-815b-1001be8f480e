import { LocalStorage } from '@root/common/common-storage';

export interface ISearchSite {
    id: string;       // 唯一标识
    name: string;     // 搜索内容
    time: number;     // 时间戳
    type?: string;    // 类型，可选
  }
  
  /**
   * 搜索记录存储类
   */
  export class CommonSites {
    private static instance: CommonSites | null = null;
    private storage: LocalStorage<string>;
  
    private constructor() {
      // 使用通用存储类
      this.storage = LocalStorage.getInstance<string>({
        name: 'SearchSites',
        maxItems: 20
      });
    }
  
    public static getInstance(): CommonSites {
      if (!CommonSites.instance) {
        CommonSites.instance = new CommonSites();
      }
      return CommonSites.instance;
    }
  
    public async addSite(name: string, type?: string): Promise<boolean> {
      // 去重处理：先检查是否已存在相同内容的记录
      const existingSites = await this.getSites();
      const existingSite = existingSites.find(site => site.name === name);
      if (existingSite) {
        // 如果已存在相同内容的记录，先删除旧记录
        await this.deleteSite(existingSite.id);
      }
      
      // 添加新记录（使用当前时间戳作为ID）
      return await this.storage.setItem(Date.now().toString(), name, type);
    }
  
    public async deleteSite(id: string): Promise<boolean> {
      return await this.storage.removeItem(id);
    }
  
    public async clearSites(): Promise<boolean> {
      return await this.storage.clear();
    }
  
    public async getSites(): Promise<ISearchSite[]> {
      const items = await this.storage.getAllItems();
      return items.map(item => ({
        id: item.key,
        name: item.value,
        time: item.timestamp,
        type: item.type
      }));
    }
  
    public async getSitesByType(type: string): Promise<ISearchSite[]> {
      const items = await this.storage.getItemsByType(type);
      return items.map(item => ({
        id: item.key,
        name: item.value,
        time: item.timestamp,
        type: item.type
      }));
    }
  
    public async getSitesCount(): Promise<number> {
      return await this.storage.getItemCount();
    }
  }
  
  // 导出单例实例
  export const commonSites = CommonSites.getInstance();