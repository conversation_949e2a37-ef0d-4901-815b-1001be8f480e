import { ISourceFilter, SortType, ISortMap } from './types'

export const userDataPan = {
  key: 'userDataPanExtendInfo',
  field: 'panTaskInfo',
  defaultVal: '{}',
}

export const tabMap: { 'download': 'download', 'finish': 'finish' } = {
  download: 'download',
  finish: 'finish'
}

export const filterSource = [
  {
    title: '文件来源',
    checkAllTitle: '全部来源',
    filters: [
      { val: ISourceFilter.yunpan, label: '云盘' },
      // { val: ISourceFilter.nas, label: 'NAS' },
      // { val: ISourceFilter.wky, label: '玩客云' },
    ],
  },
  {
    title: '文件类型',
    checkAllTitle: '全部类型',
    filters: [
      { val: ISourceFilter.Video, label: '视频' },
      { val: ISourceFilter.Software, label: '安装包' },
      { val: ISourceFilter.Pic, label: '图片' },
      { val: ISourceFilter.Music, label: '音频' },
      { val: ISourceFilter.Zip, label: '压缩包' },
      // { val: ISourceFilter.image, label: '镜像文件' },
      { val: ISourceFilter.Bt, label: 'BT种子' },
      // { val: ISourceFilter.pdf, label: 'PDF' },
      { val: ISourceFilter.Doc, label: '文档' },
      { val: ISourceFilter.taskGroup, label: '任务组' },
      { val: ISourceFilter.Unkown, label: '其他' },
      // { val: ISourceFilter.loseFile, label: '失效文件' },
    ],
  },
]
/** 排序列表 */
export const sortList: ISortMap[] = [
  { title: '按创建时间', key: SortType.ByTime_Creation, list: [{ label: '从新到旧', val: 'downNewest' }, { label: '从旧到新', val: 'downOldest' }] },
  { title: '按完成时间', key: SortType.ByTime_Completion, list: [{ label: '从新到旧', val: 'completedNewest' }, { label: '从旧到新', val: 'completedOldest' }] },
  { title: '按当前进度', key: SortType.ByProgress, list: [{ label: '从慢到快', val: 'slowToFast' }, { label: '从快到慢', val: 'FastToSlow' }] },
  { title: '按文件名称', key: SortType.ByName, list: [{ label: '从A到Z', val: 'asc' }, { label: '从Z到A', val: 'desc' }] },
  { title: '按文件大小', key: SortType.BySize, list: [{ label: '从小到大', val: 'smallToBig' }, { label: '从大到小', val: 'BigToSmall' }] },
]

export const filterCategoryData = [
  {
    type: 'fileSource',
    isCheckAll: true,
    filters: [] as any[],
  },
  {
    type: 'fileType',
    isCheckAll: true,
    filters: [] as any[],
  },
]
// ASCE(升序) ? 0 : -1  DESC(降序): -1 ASCE(升序): 0
export const sortMap = [
  { key: 'downNewest', attr: SortType.ByTime_Creation, state: -1 },
  { key: 'downOldest', attr: SortType.ByTime_Creation, state: 0 },
  { key: 'completedNewest', attr: SortType.ByTime_Completion, state: -1 },
  { key: 'completedOldest', attr: SortType.ByTime_Completion, state: 0 },
  { key: 'slowToFast', attr: SortType.ByProgress, state: -1 },
  { key: 'FastToSlow', attr: SortType.ByProgress, state: 0 },
  { key: 'asc', attr: SortType.ByName, state: -1 },
  { key: 'desc', attr: SortType.ByName, state: 0 },
  { key: 'smallToBig', attr: SortType.BySize, state: -1 },
  { key: 'BigToSmall', attr: SortType.BySize, state: 0 }
]

export const dropdownMenuList = [
  {
    key: 'downHistory',
    label: '下载记录',
  },
  {
    key: 'downPlan',
    label: '下载计划',
    children: [
      {
        key: 'downSpeed',
        label: '限速下载',
      },
      {
        key: 'downIdle',
        label: '空闲下载',
      },
      {
        key: 'scheduleTask',
        label: '计划任务',
        rightText: '',  // 这里将在运行时动态设置
      },
    ],
  },
  // {
  //   key: 'downRemote',
  //   label: '远程下载',
  // },
  // {
  //   key: 'downImport',
  //   label: '导入未完成下载',
  // },
  {
    key: 'downFinish',
    label: '下载完成后',
    children: [
      {
        key: 'powerOff',
        label: '关机',
      },
      {
        key: 'sleep',
        label: '睡眠',
      },
      {
        key: 'exit',
        label: '退出',
      },
    ],
  },
  // {
  //   key: 'privateSpace',
  //   label: '私人空间',
  // },
  // {
  //   key: 'movieLibrary',
  //   label: '片库',
  //   hasSeparator: true,
  // },
  {
    key: 'delInvalidTasks',
    label: '删除失效任务',
  },
]

