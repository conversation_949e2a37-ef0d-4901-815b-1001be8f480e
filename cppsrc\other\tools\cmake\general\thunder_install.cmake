# install Common header files 
FILE(GLOB_RECURSE thunder_common_header_files ${CMAKE_SOURCE_DIR}/Include/Common/*.h)

# exclude C++ standard header files
list(FILTER thunder_common_header_files EXCLUDE REGEX ".+ThunderEventbusWrapper.h")
list(FILTER thunder_common_header_files EXCLUDE REGEX ".+ThunderLoggerWrapper.h")
list(FILTER thunder_common_header_files EXCLUDE REGEX ".+ThunderRefCnt.h")
list(FILTER thunder_common_header_files EXCLUDE REGEX ".+ThunderSafePtr.h")
list(FILTER thunder_common_header_files EXCLUDE REGEX ".+ThunderWeakPtr.h")
list(FILTER thunder_common_header_files EXCLUDE REGEX ".+ThunderControlBlock.h")
list(FILTER thunder_common_header_files EXCLUDE REGEX ".+ThunderUndoRedoWrapper.h")
list(FILTER thunder_common_header_files EXCLUDE REGEX ".+ThunderInternalComUtility.h")
list(FILTER thunder_common_header_files EXCLUDE REGEX ".+DmUserDataKeyDefInternal.h")
install(FILES ${thunder_common_header_files} DESTINATION ${prj_include_install_dir}/Common)

# install configs & sysconfigs
if (THUNDER_ENABLE_EXTERNTRANSFER_install)
  include(thunder_install_externtransfer)
else ()
  install(DIRECTORY ${CMAKE_SOURCE_DIR}/configs/ DESTINATION ${prj_config_install_dir})
  install(DIRECTORY ${CMAKE_SOURCE_DIR}/sysconfigs/ DESTINATION ${prj_sysconfig_install_dir})
  set(thunder_beatManager_dependencies)
  IF (MSVC)
    list(APPEND thunder_beatManager_dependencies ${CMAKE_SOURCE_DIR}/3rdparty/aliyun/lib/${thunder_platforms}/alibabacloud-oss-cpp-sdk.dll)
  ELSEIF (APPLE)
    list(APPEND thunder_beatManager_dependencies  ${CMAKE_SOURCE_DIR}/3rdparty/aliyun/lib/${thunder_platforms}/libalibabacloud-oss-cpp-sdk.dylib)	
  ELSE ()
    message(FATAL_ERROR "Now is '${CMAKE_SYSTEM_NAME}' OS. not supported yet.")
  ENDIF ()
  install(FILES ${thunder_beatManager_dependencies} DESTINATION ${prj_binary_install_dir})
  # install default effect
  install(DIRECTORY ${CMAKE_SOURCE_DIR}/Default\ Effects DESTINATION ${prj_binary_install_dir})
endif()
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/sysconfigs/VBLCommonSetting.json DESTINATION ${prj_sysconfig_install_dir})
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/sysconfigs/default_effect/DefaultEffectConfig.json DESTINATION ${prj_sysconfig_install_dir}/default_effect)
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/sysconfigs/default_effect/DC_DefaultEffectConfig.json DESTINATION ${prj_sysconfig_install_dir}/default_effect)

# instll test files
if (THUNDER_ENABLE_TESTS)
    #install(DIRECTORY ${CMAKE_SOURCE_DIR}/tests/testfiles/ DESTINATION #${prj_testfile_install_dir})
endif ()

# install THUNDER-Config
include(CMakePackageConfigHelpers)
configure_package_config_file(${CMAKE_SOURCE_DIR}/tools/cmake/general/Thunder_Config.cmake.in
  ${CMAKE_CURRENT_BINARY_DIR}/VBLConfig.cmake
  INSTALL_DESTINATION ${prj_lib_install_dir}/cmake/THUNDER
)
write_basic_package_version_file(
  ${CMAKE_CURRENT_BINARY_DIR}/VBLConfigVersion.cmake
  VERSION ${THUNDER_VERSION}
  COMPATIBILITY SameMajorVersion 
)
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/VBLConfig.cmake
              ${CMAKE_CURRENT_BINARY_DIR}/VBLConfigVersion.cmake
        DESTINATION ${prj_lib_install_dir}/cmake/THUNDER
		)
		
# install system runtime libraries
SET(CMAKE_INSTALL_SYSTEM_RUNTIME_DESTINATION ${prj_binary_install_dir})
INCLUDE(InstallRequiredSystemLibraries)
