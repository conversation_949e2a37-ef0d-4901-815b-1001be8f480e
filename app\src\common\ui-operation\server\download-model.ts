import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { GetDownloadModelManager } from '../impl/download-model';
import { IDownloadModelPositionParams } from '../download-model-type';

export class DownloadModelManagerServer {
  static init() {

    client.registerFunctions({
      DownloadModelManagerClientPositionTask: async (context: any ,params: IDownloadModelPositionParams) => {
        GetDownloadModelManager().positionDownloadTask(params);
      }
    })
  }
}