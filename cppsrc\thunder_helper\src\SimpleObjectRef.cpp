#include "./SimpleObjectRef.h"

void SimpleObjectRefAddon::Init(napi_env env, napi_value exports) {
    napi_property_descriptor desc[] = {
        { "getValue", nullptr, SimpleObjectRefAddon::GetValue, nullptr, nullptr, nullptr, napi_default, nullptr }
    };

    napi_value constructor;
    const char szClassName[] = "SimpleObjectRefAddon";
    napi_define_class(env, szClassName, sizeof(szClassName), SimpleObjectRefAddon::JSConstructor, nullptr,
        sizeof(desc) / sizeof(napi_property_descriptor), desc, &constructor);


    napi_set_named_property(env, exports, szClassName, constructor);
}

napi_value SimpleObjectRefAddon::GetValue(napi_env env, napi_callback_info info) {
    return nullptr;
}

napi_value SimpleObjectRefAddon::JSConstructor(napi_env env, napi_callback_info info) {
    napi_status status;
    size_t argc = 2;
    napi_value argv[2];
    napi_value _this = nullptr;
    status = napi_get_cb_info(env, info, &argc, argv, &_this, nullptr);
    if (argc < 2) {
        XL_SPDLOG_ERROR("failed, for argc < 2, count={:d}", argc);
        assert(false);
        return nullptr;
    }

    std::string strId = "";
	AddonBaseOpt::ParseString(env, argv[0], strId);
	auto pFunc = NapiFunctionWarp::NewObject(env, argv[1]);

	RefValue* obj = new RefValue(strId, pFunc);
	status = napi_wrap(env, _this,
		reinterpret_cast<void*>(obj),
		[](napi_env env, void* finalize_hint, void* data) {
			delete reinterpret_cast<RefValue*>(data);
		},
		nullptr,nullptr);

    if (status != napi_ok) return nullptr;

    return _this;
}