#include "./Storage.h"

SqliteStorage::SqliteStorage(const std::string& strDb, std::shared_ptr<xl::thread::ThreadAffinity> main) {
	m_mainAffinity = main;
	auto dir = xl::path::BaseDir(strDb);
	if (!xl::path::IsFileExist(dir)) {
		xl::path::CreateDir(dir);
	}

	m_strDb = strDb;
	m_t = std::thread([this]() {
		Load();
		});
	m_t.detach();
}


xl::coroutine::AsyncTask<bool> SqliteStorage::Execute(std::string&& strSql, bool bHigh) {
	SqliteStorageCommand command;
	bool bRet = false;
	command.run = std::move([this, strSql = std::move(strSql), bRet = std::ref(bRet)]() {
		auto nRet = sqlite3_exec(m_pDataBase, strSql.c_str(), nullptr, nullptr, nullptr);
		XL_SPDLOG_INFO("SqliteStorage, Execute, nRet={:d}, sql={:s}, msg={:s}", nRet, strSql, sqlite3_errmsg(m_pDataBase));
		bRet.get() = nRet == SQLITE_OK;
	});
	if (bHigh) {
		m_commands.push_front(std::move(command));
	}
	else {
		m_commands.push_back(std::move(command));
	}
	auto awaitCb = [this, bHigh](CommonAwaitable<void>::_CB cb) {
		if (bHigh) {
			auto ite = m_commands.begin();
			ite->cb = [cb]() {
				cb();
			};
		}
		else {
			auto ite = m_commands.rbegin();
			ite->cb = [cb]() {
				cb();
			};
		}
		if (!m_bRunLoop) {
			LoopRunTask();
		}
	};
	co_await CommonAwaitable<void>{awaitCb};
	co_return bRet;
}

xl::coroutine::AsyncTask<bool> SqliteStorage::Query(std::string&& strSql, std::vector<std::map<std::string, StorageValue*>>& values, bool bHigh) {
	SqliteStorageCommand command;
	bool bRet = false;
	command.run = std::move([this,strSql=std::move(strSql), values=std::ref(values), bRet=std::ref(bRet)]() {
		sqlite3_stmt* pStmt{ nullptr };
		do {
			auto nRet = sqlite3_prepare_v2(m_pDataBase, strSql.c_str(), -1, &pStmt, nullptr);
			XL_SPDLOG_INFO("SqliteStorage Query, code={:d}, sql={:s}, msg={:s}", nRet, strSql, sqlite3_errmsg(m_pDataBase));
			if (nRet != SQLITE_OK) {
				break;
			}
			while (sqlite3_step(pStmt) == SQLITE_ROW) {
				std::map<std::string, StorageValue*> record;
				int nCount = sqlite3_column_count(pStmt);
				for (int i = 0; i < nCount; i++) {
					std::string strName = sqlite3_column_name(pStmt, i);
					int nType = sqlite3_column_type(pStmt, i);
					switch (nType) {
					case SQLITE_INTEGER:
					{
						long long column_value = sqlite3_column_int64(pStmt, i);
						StorageValue* p = new StorageValue(i, column_value);
						record.insert(decltype(record)::value_type(strName, p));
					}
					break;
					case SQLITE_FLOAT:
					{
						double column_value = sqlite3_column_double(pStmt, i);
						StorageValue* p = new StorageValue(i, column_value);
						record.insert(decltype(record)::value_type(strName, p));
					}
					break;
					case SQLITE_TEXT:
					{
						auto column_value = (const char*)sqlite3_column_text(pStmt, i);
						StorageValue* p = new StorageValue(i, column_value);
						record.insert(decltype(record)::value_type(strName, p));
					}
					break;
					/*case SQLITE_BLOB:
					{
#pragma warning(disable: 4302)
						byte value = (byte)sqlite3_column_blob(pStmt, i);
#pragma warning(default: 4302)
						int size = sqlite3_column_bytes(pStmt, i);
						std::vector<byte> column_value(value, value + size);
						StorageValue* p = new StorageValue(i, column_value);
						record.insert(decltype(record)::value_type(strName, p));
					}
					break;*/
					case SQLITE_NULL:
						break;
					default:
						break;
					}
				}
				
				values.get().push_back(std::move(record));
			}
			bRet.get() = true;
		} while (false);
		if (pStmt) {
			sqlite3_finalize(pStmt);
		}
	});
	if (bHigh) {
		m_commands.push_front(std::move(command));
	}
	else {
		m_commands.push_back(std::move(command));
	}
	auto awaitCb = [this, bHigh](CommonAwaitable<void>::_CB cb) {
		if (bHigh) {
			auto ite = m_commands.begin();
			ite->cb = [cb]() {
				cb();
			};
		}
		else {
			auto ite = m_commands.rbegin();
			ite->cb = [cb]() {
				cb();
			};
		}
		if (!m_bRunLoop) {
			LoopRunTask();
		}
	};
	co_await CommonAwaitable<void>{awaitCb};
	co_return bRet;
}

void SqliteStorage::Load() {
	auto nRet = sqlite3_open(m_strDb.c_str(), &m_pDataBase);
	if (nRet != SQLITE_OK) {
		XL_SPDLOG_INFO("SqliteStorage load failed, code={:d}", nRet);
	}
	uv_loop_init(&m_loop);
	auto threadMessage = xl::thread::LibuvThreadMessage::NewObject(&m_loop);
	m_threadAffinity = xl::thread::ThreadAffinity::NewObject(threadMessage);


	auto func = [](SqliteStorage* pThis) -> xl::coroutine::AsyncTask<void> {
		co_await pThis->m_mainAffinity->SwitchToAffinityThread();
		pThis->m_loadLock.UnLock();
		XL_SPDLOG_INFO("finish");
		co_await pThis->m_threadAffinity->SwitchToAffinityThread();
	};
	func(this);
	uv_run(&m_loop, UV_RUN_DEFAULT);
}

AsyncTask<void> SqliteStorage::LoopRunTask() {
	m_bRunLoop = true;
	co_await m_loadLock.Lock();
	m_loadLock.UnLock();
	do
	{
		if (m_commands.empty()) {
			m_bRunLoop = false;
			break;
		}
		auto ite = m_commands.begin();
		SqliteStorageCommand command = std::move(*ite);
		m_commands.pop_front();
		co_await m_threadAffinity->SwitchToAffinityThread();
		command.run();
		co_await m_mainAffinity->SwitchToAffinityThread();
		command.cb();
	} while (true);
}

void SqliteStorageAddon::Init(napi_env env, napi_value exports) {
	napi_property_descriptor desc[] = {
		{ "execute", nullptr, SqliteStorageAddon::Execute, nullptr, nullptr, nullptr, napi_default, nullptr },
		{ "query", nullptr, SqliteStorageAddon::Query, nullptr, nullptr, nullptr, napi_default, nullptr }
	};

	napi_value constructor;
	const char szClassName[] = "NativeSqliteStorage";
	napi_define_class(env, szClassName, sizeof(szClassName), SqliteStorageAddon::JSConstructor, nullptr,
		sizeof(desc) / sizeof(napi_property_descriptor), desc, &constructor);


	napi_set_named_property(env, exports, szClassName, constructor);
}

napi_value SqliteStorageAddon::Execute(napi_env env, napi_callback_info info) {
	napi_status status;
	size_t argc = 3;
	napi_value argv[3];
	napi_value _this = nullptr;
	status = napi_get_cb_info(env, info, &argc, argv, &_this, nullptr);
	if (argc < 2) {
		XL_SPDLOG_ERROR("failed, for argc < 2, count={:d}", argc);
		assert(false);
		return nullptr;
	}
	SqliteStorage* pObj = nullptr;
	napi_unwrap(env, _this, (void**)&pObj);

	std::string strSql;
	AddonBaseOpt::ParseString(env, argv[0], strSql);
	auto pFunc = NapiFunctionWarp::NewObject(env, argv[1]);

	bool bHigh = false;
	AddonBaseOpt::ParseBool(env, argv[2], bHigh);

	auto f = [](SqliteStorage* pObj, std::string&& strSql, decltype(pFunc) pFunc, bool bHigh)->xl::coroutine::AsyncTask<void> {
		auto b = co_await pObj->Execute(std::forward<std::string>(strSql), bHigh);
		auto pArgv = pFunc->NewArgv(1);
		pArgv->PushBool(b);
		pFunc->Call(std::move(pArgv), nullptr);
	};
	f(pObj, std::move(strSql), pFunc, bHigh);

	return nullptr;
}

napi_value SqliteStorageAddon::Query(napi_env env, napi_callback_info info) {
	napi_status status;
	size_t argc = 3;
	napi_value argv[3];
	napi_value _this = nullptr;
	status = napi_get_cb_info(env, info, &argc, argv, &_this, nullptr);
	if (argc < 2) {
		XL_SPDLOG_ERROR("failed, for argc < 2, count={:d}", argc);
		assert(false);
		return nullptr;
	}
	SqliteStorage* pObj = nullptr;
	napi_unwrap(env, _this, (void**)&pObj);

	std::string strSql;
	AddonBaseOpt::ParseString(env, argv[0], strSql);
	auto pFunc = NapiFunctionWarp::NewObject(env, argv[1]);

	bool bHigh = false;
	AddonBaseOpt::ParseBool(env, argv[2], bHigh);

	auto f = [](SqliteStorage* pObj, std::string&& strSql, decltype(pFunc) pFunc, bool bHigh)->xl::coroutine::AsyncTask<void> {
		std::vector<std::map<std::string, StorageValue*>> records;
		auto b = co_await pObj->Query(std::forward<std::string>(strSql), records, bHigh);
		napi_value objRecords;
		auto env = pFunc->GetEnv();
		napi_create_array_with_length(env, records.size(), &objRecords);
		for (auto i = 0; i < records.size(); i++) {
			napi_value objItem;
			napi_create_object(env, &objItem);
			for (auto& pair : records[i]) {
				auto nType = pair.second->GetType();
				switch (nType)
				{
				case ValueType::TYPE_INT:
					AddonBaseOpt::PushObjectInt32(env, objItem, pair.first.c_str(), pair.second->GetInt());
					break;
				case ValueType::TYPE_BIGINT:
					AddonBaseOpt::PushObjectInt64(env, objItem, pair.first.c_str(), pair.second->GetInt64());
					break;
				case ValueType::TYPE_DOUBLE:
					AddonBaseOpt::PushObjectDouble(env, objItem, pair.first.c_str(), pair.second->GetDouble());
					break;
				case ValueType::TYPE_STRING:
				{
					int nSize = 0;
					AddonBaseOpt::PushObjectString(env, objItem, pair.first.c_str(), pair.second->GetString(nSize));
				}
					break;
				case ValueType::TYPE_BLOB:
				{
					int nSize = 0;
					AddonBaseOpt::PushObjectString(env, objItem, pair.first.c_str(), pair.second->GetString(nSize));
				}
					break;
				default:
					break;
				}
				delete pair.second;
			}
			napi_set_element(env, objRecords, i, objItem);
		}

		auto pArgv = pFunc->NewArgv(2);
		pArgv->PushBool(b);
		pArgv->PushValue(objRecords);
		pFunc->Call(std::move(pArgv), nullptr);
	};
	f(pObj, std::move(strSql), pFunc, bHigh);

	return nullptr;
}

napi_value SqliteStorageAddon::JSConstructor(napi_env env, napi_callback_info info) {
	napi_status status;
	size_t argc = 2;
	napi_value argv[2];
	napi_value _this = nullptr;
	status = napi_get_cb_info(env, info, &argc, argv, &_this, nullptr);
	if (argc < 1) {
		XL_SPDLOG_ERROR("failed, for argc < 1, count={:d}", argc);
		assert(false);
		return nullptr;
	}

	std::string strDb = "";
	AddonBaseOpt::ParseString(env, argv[0], strDb);

	uv_loop_t* pLoop{ nullptr };
	napi_get_uv_event_loop(env, &pLoop);
	auto spThreadMessage = xl::thread::LibuvThreadMessage::NewObject(pLoop);
	auto threadAffinity = xl::thread::ThreadAffinity::NewObject(spThreadMessage);

	SqliteStorage* pStorage = new SqliteStorage(strDb, threadAffinity);
	status = napi_wrap(env, _this,
		reinterpret_cast<void*>(pStorage),
		[](napi_env env, void* finalize_hint, void* data) {
			delete reinterpret_cast<SqliteStorage*>(data);
		},
		nullptr, nullptr);

	if (status != napi_ok) return nullptr;

	return _this;
}