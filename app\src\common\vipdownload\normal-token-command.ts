import { Command, SpeedBody, SpeedTaskInfos, TokenResponse } from './command';
import { VipTask } from './task';
import * as DownloadKernel from '@root/common/task/impl/task-manager'
import * as DownloadKernelType from '@root/common/task/base'
import { BtTask } from '@root/common/task/impl/bt-task';
import { ServerRequest } from './server-request';

export class NormalTokenCommand implements Command {
  private task: VipTask;
  private fileIndex: number;
  private isCancel: boolean = false;
  private timerId: NodeJS.Timeout|undefined = undefined;
  constructor(task: VipTask, fileIndex: number) {
    this.task = task;
    this.fileIndex = fileIndex;
  }

  public async execute(): Promise<void> {
    let tm = DownloadKernel.GetTaskManager();
    let dkTask = await tm.findTaskById(this.task.getTaskId());
    if (!dkTask || dkTask.isPanTask()) {
      return ;
    }
    if (this.isCancel) {
      return;
    }

    let globalData = this.task.getGlobalGetter().getData();
    let body: SpeedBody = {
      peer_id: globalData.peerid,
      task_infos: [],
    }
    if (dkTask.getType() === DownloadKernelType.TaskType.Bt) {
      let btTask = dkTask.toExtra<BtTask>();
      body.infohash = btTask.getInfoHash();
      body.bt_title = dkTask.getTaskName();
      let btFile = await btTask.getBtFileByIndex(this.fileIndex);
      let taskInfo: SpeedTaskInfos = {
        url: 'bt://' + body.infohash + '/' + this.fileIndex.toString(),
        filename: btFile!.getFileName(),
        gcid: btFile!.getGcid(),
        cid: btFile!.getCid(),
        filesize: btFile!.getFileSize(),
        file_index: this.fileIndex,
        refer_url: '', // TODO
      }
      body.task_infos.push(taskInfo);
    } else if (dkTask.getType() === DownloadKernelType.TaskType.Group) {
      let groupSubTask = await DownloadKernel.GetTaskManager().findTaskById(this.fileIndex);
      if (groupSubTask) {
        let taskInfo: SpeedTaskInfos = {
          url: groupSubTask.getUrl(),
          filename: groupSubTask.getTaskName(),
          gcid: groupSubTask.getGcid(),
          cid: groupSubTask.getCid(),
          filesize: groupSubTask.getFileSize(),
          refer_url: '', // TODO
        }
        body.task_infos.push(taskInfo);
      }
    } else {
      let taskInfo: SpeedTaskInfos = {
        url: dkTask.getUrl(),
        filename: dkTask.getTaskName(),
        gcid: dkTask.getGcid(),
        cid: dkTask.getCid(),
        filesize: dkTask.getFileSize(),
        refer_url: '', // TODO
      }
      body.task_infos.push(taskInfo);
    }

    let res = await ServerRequest.querySpeedToken(globalData, body);

    if (res.result === 0 && res.task_infos && res.task_infos.length > 0 && !this.isCancel) {
      if (res.task_infos[0].result == 0) {
        let expireTime: number = res.task_infos[0].time_interval;
        this.task.onNewToken(this.fileIndex, res.task_infos[0].token!);
        if (expireTime > 0) {
          this.timerId = setTimeout(() => {
            this.timerId = undefined;
            this.execute();
          }, (expireTime - 5 * 60) * 1000);
        }
      }
    }
  }

  public  async cancel(): Promise<void> {
    this.isCancel = true;
    if (this.timerId) {
      clearTimeout(this.timerId);
      this.timerId = undefined;
    }
  }
}