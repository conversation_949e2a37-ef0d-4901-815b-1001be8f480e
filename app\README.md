## Installation

Use pnpm in order to install all dependencies.

```bash
npm install
```

## 构建调试

```bash
npm run update-bin

npm run build:pan

npm run build

npm run start
```

## Usage

```bash
# 云盘调试或构建前需要先运行
npm run build:pan

# use `pnpm start:renderer` to start renderer process.
npm run dev:renderer

# and use `pnpm start:main` to start main process.
npm run dev:main
```

## Packaging

To generate the project package based on the OS you're running on, just run:

```bash
npm package
```
