@PACKAGE_INIT@

get_filename_component(_THUNDER_CMAKE_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)
get_filename_component(_THUNDER_CMAKE_DIR "${_THUNDER_CMAKE_DIR}" PATH)
FILE(GLOB components RELATIVE ${_THUNDER_CMAKE_DIR} ${_THUNDER_CMAKE_DIR}/*)
FOREACH(component ${components})
	IF ("${component}" MATCHES "THUNDER")
		continue()
	ENDIF()
	IF(IS_DIRECTORY ${_THUNDER_CMAKE_DIR}/${component})
		message (STATUS "Found THUNDER component ${component}")
		include(${_THUNDER_CMAKE_DIR}/${component}/${component}Config.cmake)
		check_required_components(${component})
	ENDIF()
ENDFOREACH()
unset(_THUNDER_CMAKE_DIR)

set(THUNDER_FOUND TRUE)