import { h, ref, render, watch } from 'vue'

import AlertDialog from './Dialog.vue'
import { usePromptDialog } from './usePromptDialog.js'

/**
 * AlertDialog 编程式API
 * 允许在JS代码中直接调用对话框，无需在模板中声明
 */
export function useAlertDialog() {
  // 当前打开的对话框DOM容器
  let container = null

  // 获取 prompt 功能
  const promptDialog = usePromptDialog()

  /**
   * 创建并显示一个对话框
   * @param {Object} options 对话框配置选项
   * @param {string} options.title 标题
   * @param {string} options.content 内容（纯文本）
   * @param {boolean} [options.allowHtml] 是否允许content包含HTML（默认false，注意XSS安全）
   * @param {boolean} options.alwaysOnTop 是否置顶显示（默认false）
   * @param {string} [options.confirmText] 确认按钮文本（可选）
   * @param {string} [options.cancelText] 取消按钮文本（可选）
   * @param {boolean} [options.showCancel] 是否显示取消按钮（可选）
   * @param {boolean} [options.showCloseButton] 是否显示关闭按钮（可选）
   * @param {string} [options.variant] 对话框变体：info, success, warning, error, thunder（可选）
   * @param {boolean} [options.showTitleIcon] 是否显示标题图标（可选）
   * @returns {Promise} 一个在对话框操作后resolve的Promise
   */
  function open(options) {
    return new Promise(resolve => {
      // 创建DOM容器
      if (!container) {
        container = document.createElement('div')
        // 如果设置了置顶，给容器添加更高的 z-index
        if (options.alwaysOnTop) {
          container.style.zIndex = '9999'
          container.style.position = 'fixed'
          container.style.top = '0'
          container.style.left = '0'
          container.style.width = '100%'
          container.style.height = '100%'
          container.style.pointerEvents = 'none'
        }
        document.body.appendChild(container)
      }

      // 用于控制对话框可见性的ref
      const visible = ref(true)

      // 确认回调
      const handleConfirm = () => {
        visible.value = false
        resolve(true)
        setTimeout(() => unmount(), 300) // 动画结束后卸载
      }

      // 取消回调
      const handleCancel = () => {
        visible.value = false
        resolve(false)
        setTimeout(() => unmount(), 300) // 动画结束后卸载
      }

      // 关闭回调
      const handleClose = () => {
        visible.value = false
        resolve(false)
        setTimeout(() => unmount(), 300) // 动画结束后卸载
      }

      // 处理open状态变化
      const handleUpdateOpen = value => {
        visible.value = value
        if (!value) {
          resolve(false)
          setTimeout(() => unmount(), 300)
        }
      }

      // 卸载组件
      const unmount = () => {
        if (container) {
          render(null, container)
          document.body.removeChild(container)
          container = null
        }
      }

      // 渲染AlertDialog组件
      const vnode = h(AlertDialog, {
        ...options,
        open: visible.value, // 确保对话框显示
        showTrigger: false, // 编程式调用不需要trigger
        showTitleIcon: options.showTitleIcon !== undefined ? options.showTitleIcon : true, // 默认显示图标
        preventDefaultClose: true, // 阻止默认关闭行为，由我们手动控制
        onConfirm: handleConfirm,
        onCancel: handleCancel,
        onClose: handleClose,
        'onUpdate:open': handleUpdateOpen,
      })

      // 将组件渲染到DOM
      render(vnode, container)
    })
  }

  /**
   * 打开信息对话框
   */
  function info(options) {
    return open({ ...options, variant: 'info' })
  }

  /**
   * 打开成功对话框
   */
  function success(options) {
    return open({ ...options, variant: 'success' })
  }

  /**
   * 打开警告对话框
   */
  function warning(options) {
    return open({ ...options, variant: 'warning' })
  }

  /**
   * 打开错误对话框
   */
  function error(options) {
    return open({ ...options, variant: 'error' })
  }

  /**
   * 打开确认对话框
   */
  function confirm(options) {
    return open({
      ...options,
      variant: options.variant || 'info',
      showCancel: true,
    })
  }

  // 返回可用方法
  return {
    open,
    info,
    success,
    warning,
    error,
    confirm,
    prompt: promptDialog.prompt,
  }
}

export default useAlertDialog
