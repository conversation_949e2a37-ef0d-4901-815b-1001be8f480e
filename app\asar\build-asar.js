const asar = require('./security-asar/lib/asar');

const fs = require('fs');
const path = require('path');
// const packageJSON = require('../package.json');
// const buildTarget = process.env.BIN_TARGET || 'Release';
// const binDir = path.resolve(packageJSON.build.binDir);
// const binName = packageJSON.build.binName;

const src = './dist';
//const outDir = path.join(binDir, '/resources/app/out');
const dest = '../bin/release/resources/app/out.asar';

/**
 *
 * @param {*} url
 */
function deleteFolderRecursive(url) {
  let files = [];
  console.log('delete folder: ', url);
  /**
   * 判断给定的路径是否存在
   */
  if (fs.existsSync(url)) {
    /**
     * 返回文件和子目录的数组
     */
    files = fs.readdirSync(url);
    files.forEach(function (file, index) {

      const curPath = path.join(url, file);
      /**
       * fs.statSync同步读取文件夹文件，如果是文件夹，在重复触发函数
       */
      if (fs.statSync(curPath).isDirectory()) { // recurse
        deleteFolderRecursive(curPath);
      } else {
        fs.unlinkSync(curPath);
      }
    });
    /**
     * 清除文件夹
     */
    fs.rmdirSync(url);

  } else {
    console.log('给定的路径不存在，请给出正确的路径');
  }
}


asar.createPackage(src, dest).then(() => {
  console.log('done.');
}).catch((error) => {
  console.error(error);
}).finally(() => {
  //deleteFolderRecursive(src);
});
