/// <reference path="../impl/thunder-client-api.d.ts" />
import {
    LinkHubCallApiProxyImplWithIpcServer,
    getLinkHubCallApiProxy
} from '../linkhub-call-api-impl'

export class EventDispatcher implements ThunderClientAPI.base.IEventDispatcher {
    private apiProxy: LinkHubCallApiProxyImplWithIpcServer;
    private static instance: EventDispatcher | null = null;
    private constructor() {
        this.apiProxy = getLinkHubCallApiProxy();
    }

    public static getInstance(): EventDispatcher {
        if (!EventDispatcher.instance) {
            if (global.EventDispatcherClientInstance) {
                EventDispatcher.instance = global.LinkHubHelperClientInstance;
            } else {
                EventDispatcher.instance = new EventDispatcher();
                global.EventDispatcherClientInstance = EventDispatcher.instance;
            }
        }
        return EventDispatcher.instance!;
    }
    public attachEvent<T extends keyof ThunderClientAPI.base.EventDetails>(event: T, listener: any): void {
        this.apiProxy.AttachServerEvent(event, listener);
    }

    public detachEvent<T extends keyof ThunderClientAPI.base.EventDetails>(event: T, listener: any): void {
        this.apiProxy.DetachServerEvent(event, listener);
    }

}
