  import path from 'path';
  import { TaskManager } from '@root/common/task/impl/task-manager';
  import { BtTask } from '@root/common/task/impl/bt-task';
  import { GroupTask } from '@root/common/task/impl/group-task';
  import { FileSystemAWNS } from '@root/common//fs-utilities';
  import { GetProfilesPath } from '@root/common/xxx-node-path'
  import * as BaseType from '@root/common/task/base';

  export namespace taskExtraFunc {

    // ====================== group =======================================
    export async function getGroupTask (groupTaskId: number) {
      const task = await TaskManager.GetInstance().findTaskById(groupTaskId);
      const groupTask = task!.toExtra<GroupTask>();
      return groupTask;
    }
    /** TODO:获取任务组下载总数 */
    export async function getGroupDownloadTotal(groupTaskId: number) {
      const groupTask = await getGroupTask(groupTaskId)
      return groupTask.getTotalCount();
    }
    /** 获取任务组添加总数 */
    export async function getGroupDownloadCount(groupTaskId: number) {
      const groupTask = await getGroupTask(groupTaskId)
      return groupTask.getDownloadCount();
    }

    /** 获取下载完成数 */
    export async function getGroupCompleteCount(groupTaskId: number) {
      const groupTask = await getGroupTask(groupTaskId)
      return groupTask.getCompleteCount();
    }

    /** 取消下載 */
    export async function groupCancelSubTask(groupTaskId: number, taskIds: number[]) {
      const groupTask = await getGroupTask(groupTaskId)
      return groupTask.cancelSubTask(taskIds)
    }

    /** 任務組删除下載 */
    export async function groupDeleteSubTask(groupTaskId: number, taskIds: number[]) {
      const groupTask = await getGroupTask(groupTaskId)
      return groupTask.deleteSubTask(taskIds)
    }

    /** 任務組bt删除下載 */
    export async function groupDeleteBtSubTask(groupTaskId: number, info: {taskId: number, indexs: number[]}[]) {
      const groupTask = await getGroupTask(groupTaskId)
      return groupTask.deleteSubBtTask(info)
    }

    /** 任務組bt取消下載 */
    export async function groupCancelBtSubTask(groupTaskId: number, info: {taskId: number, indexs: number[]}[]) {
      const groupTask = await getGroupTask(groupTaskId)
      return groupTask.cancelSubBtTask(info)
    }
    
    /** 获取任务组taskId */
    export async function getGroupSubTaskIds(taskId: number) {
      const groupTask = await getGroupTask(taskId)
      return groupTask.getSubTaskIds()
    }

    /** 恢复取消下载 */
    export async function groupDownloadSubTask(groupTaskId: number, taskIds: number[]) {
      const groupTask = await getGroupTask(groupTaskId)
      return groupTask.downloadSubTask(taskIds)
    }

    // ============================ bt =================================
    /** 获取bt任务 */
    export async function getBtTask (taskId: number) {
      const task = await TaskManager.GetInstance().findTaskById(taskId);
      const btTask = task!.toExtra<BtTask>();
      return btTask;
    }

    /** 取消下载bt子任务 */
    export async function cancelBtSubTask(taskId: number, ids: number[]) {
      const btTask = await getBtTask(taskId)
      return btTask.cancelSubTask(ids);
    }

    /** 下载bt子任务 */
    export async function downloadBtSubTask(taskId: number, ids: number[]) {
      const btTask = await getBtTask(taskId)
      return btTask.downloadSubTask(ids);
    }

    /** 删除bt任务 */
    export async function deleteBtSubTask(taskId: number, ids: number[]) {
      const btTask = await getBtTask(taskId)
      return btTask.deleteSubTask(ids);
    }

    /** 是否为bt单文件任务 */
    export async function isSingleBT(taskId: number) {
      const btTask = await getBtTask(taskId)
      /** 是否为bt单文件任务 */
      return btTask.isOnlyOneFile();
    }

    /** TODO:获取已添加未添加文件数 */
    export async function getDownloadTotal(taskId: number) {
      const btTask = await getBtTask(taskId)
      return btTask.getTotalCount()
    }
    /** 获取已添加文件数 */
    export async function getDownloadCount(taskId: number) {
      const btTask = await getBtTask(taskId)
      return btTask.getDownloadCount()
    }
    /** 获取下载完成文件数 */
    export async function getCompleteCount(id: number): Promise<number> {
      const btTask = await getBtTask(id)
      if (!btTask) return 0
      return btTask.getCompleteCount()
    }

    /** 获取bt文件详情 */
    export async function getBtFileInfos(taskId: number) {
      const btTask = await getBtTask(taskId)
      return btTask.getBtFileInfos()
    }


    /** 获取文件hash */
    export async function getInfoHash(taskId: number) {
      const btTask = await getBtTask(taskId)
      return btTask.getInfoHash()
    }

    /** 获取bt子任务详情 */
    export async function getBtSubInfoByIndex(taskId: number, index: number) {
      const btTask = await getBtTask(taskId)
      return btTask.getBtFileInfoByIndex(index)
    }

    /** 是否为单文件bt */
    export async function btIsOnlyOneFile(taskId: number) {
      const btTask = await getBtTask(taskId)
      return btTask.isOnlyOneFile()
    }
    /** 获取bt子任务下载顺序 */
    export async function getBtSubFileScheduler(taskId: number) {
      const btTask = await getBtTask(taskId)
      return btTask.getBtSubFileScheduler()
    }
    /** 设置顺便下载 */
    export async function updateBtSubFileScheduler(taskId: number, scheduler: BaseType.XLBTTaskSubFileSchedulerType) {
      const btTask = await getBtTask(taskId)
      return btTask.updateBtSubFileScheduler(scheduler)
    }

    // ===============================task========================================
    export async function getTaskBase(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) return
      return task.getTaskBase()
    }

    export async function getTaskUrl(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) return
      return task.getUrl()
    }

    export async function taskMove(id: number, newPath: string) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) return
      return task.move(newPath)
    }

    /** 暂停任务 */
    export async function stopTaskById(id: number, reason: BaseType.TaskStopReason = BaseType.TaskStopReason.Unknown) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      console.log('stopTaskById', task)
      if (!task) return
      return task.stop(reason)
    }
    export function batchStopTasks(ids: number[], reason: BaseType.TaskStopReason = BaseType.TaskStopReason.Unknown) {
      return TaskManager.GetInstance().batchStopTasks(ids, reason);
    }

    /** 开始任务 */
    export async function startTaskById(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      console.log('startTaskById', task)
      if (!task) return
      return task.start()
    }
    export function batchStartTasks(ids: number[]) {
      return TaskManager.GetInstance().batchStartTasks(ids);
    }

    /** 彻底删除任务 */
    export async function delTaskById(id: number, deleteFile=false) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      console.log('startTaskById', task)
      if (!task) return
      try {
        let dataPath = GetProfilesPath();
        const speedPath = path.join(dataPath, 'TaskSpeedInfo');
        const fileName: string = 'TaskInfoEx_' + id.toString() + '.txt';
        const filePath: string = path.join(speedPath, fileName);
        FileSystemAWNS.unlinkAW(filePath).catch(e => {console.log('> e', e)})
      } catch (error) {
        console.log('> error', error)
      }
      // 同时删除记录速度历史文件
      return task.deleteTask(deleteFile)
    }
    /** 批量彻底删除 */
    export function branchDelTaskById(ids: number[], deleteFile=false) {
      console.log('>>>>>> ids', ids)
      for (let id of ids) {
        try {
          let dataPath = GetProfilesPath();
          const speedPath = path.join(dataPath, 'TaskSpeedInfo');
          const fileName: string = 'TaskInfoEx_' + id.toString() + '.txt';
          const filePath: string = path.join(speedPath, fileName);
          FileSystemAWNS.unlinkAW(filePath).catch(e => {console.log('> e', e)})
        } catch (error) {
          console.log('> error', error)
        }
      }
      console.log('>>>>>>>>>>>>>>>>>> ids', ids)
      return TaskManager.GetInstance().batchDeleteTasks(ids, deleteFile);
    }

    /** 回收站还原下载 */
    export async function recoverFromRecycleTask(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      console.log('recycleTaskById', task)
      if (!task) return
      return task.recoverFromRecycle()
    }

    /** 放入回收站 */
    export async function recycleTaskById(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      console.log('recycleTaskById', task)
      if (!task) return
      return task.recycle()
    }
    /** 批量放入回收站 */
    export function batchRecycleTasks(ids: number[]) {
      return TaskManager.GetInstance().batchRecycleTasks(ids);
    }

    /** 重新下载 */
    export async function reDownloadTaskById(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      console.log('recycleTaskById', task)
      if (!task) return
      return task.reDownload()
    }

    /** 重命名任务 */
    export async function rename(id: number, newName: string) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      console.log('>>>>> rename', task, newName)
      if (!task) return
      return task.rename(newName)
    }

    /** 是否为云盘任务 */
    export async function isPanTask(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) return
      return task.isPanTask()
    }

    /** 获取云盘信息 */
    export async function getPanFileIdInfo(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) return
      return task.getPanFileIdInfo()
    }

    /** 是否支持播放 */
    export async function getIsSupportPlay(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) return false
      return task.isSupportPlay()
    }

    /** 回收站下载数据清空 */
    export async function recycleDownloadEmpty(deleteFile: boolean) {
      let categoryManager = TaskManager.GetInstance().GetCategoryManager();
      let category = await categoryManager.getCategoryById(-1);
      let recycleView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Recycle);
      recycleView && recycleView.deleteAllTask(deleteFile)
    }

    /** 设置额外参数 */
    export async function setUserData(id: number, key: string, field: string, value: string) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) return false
      return task.setUserData(key, field, value)
    }

    /** 获取额外参数 */
    export async function getUserData(id: number, key: string, field: string, defaultValue: string) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) return ''
      return task.getUserData(key, field, defaultValue)
    }


    /** 获取速度 */
    export async function getDownloadSpeed(id: number) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) return ''
      return task.getDownloadSpeed()
    }
}