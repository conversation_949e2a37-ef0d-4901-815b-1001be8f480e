/* ========== 动画定义 ========== */
@keyframes overlayShow {
  from {
    opacity: 0;
  }

  to {
    opacity: 0.8;
  }
}

@keyframes contentShow {
  from {
    transform: translate(-50%, -48%) scale(0.96);
    opacity: 0;
  }

  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

/* ========== 基础布局组件 ========== */
.dialog-overlay {
  position: fixed;
  inset: 0;
  background: var(--background-background-mask, rgba(39, 46, 59, 0.2));
  animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  opacity: 0.8;

  &.create-task-overlay, &.pre-new-task-overlay {
    background-color: transparent;
    box-shadow: none;
  }
}

.dialog-content {
  width: 460px;
  box-sizing: border-box;
  z-index: 100;
  position: fixed;
  // width: 630px;
  /* 宽度固定 */
  padding: 24px;
  border-radius: var(--border-radius-L);
  background-color: var(--background-background-container);
  box-shadow:
    0 10px 38px -10px rgba(22, 23, 24, 0.35),
    0 10px 20px -15px rgba(22, 23, 24, 0.2);
  color: var(--font-font-1);

  /* 创建任务弹窗宽度 */

  &.create-task-container {
    width: 680px;
    box-shadow: none;
  }

  /* 置顶对话框样式 */
  &.always-on-top {
    z-index: 9999;
  }

  &.position-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &.position-top {
    top: 20px;
    transform: translateX(0);
  }
  &.position-bottom {
    bottom: 20px;
    transform: translateX(0);
  }
  &.position-left {
    left: 20px;
    transform: translateY(0);
  }
  &.position-right {
    right: 20px;
    transform: translateY(0);
  }
}

/* 置顶对话框的遮罩层 */
.dialog-overlay.always-on-top {
  z-index: 9998;
}

/* ========== 内容组件 ========== */
.dialog-title {
  display: flex;
  align-items: center;
  margin: 0;
  margin-bottom: 8px;
  padding-right: 20px;
  gap: 8px;
  font-style: normal;
  font-weight: 600;
  font-weight: 700;
  font-size: 18px;
  font-size: 16px;

  /* Headline/H6Bold */
  font-family: 'Microsoft YaHei';

  &.task-dialog-title {
    font-size: 18px;
  }
}

.dialog-title :deep(svg) {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.dialog-description {
  margin-top: 10px;
  color: var(--font-font-3);
  font-size: 14px;
  line-height: 1.5;
}

/* 右上角关闭按钮样式 */
.dialog-close-button {
  display: flex;
  position: absolute;
  top: 15px;
  right: 15px;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  border-radius: 2px;
  background: transparent;
  color: var(--font-font-3);
  cursor: pointer;
  transition:
    background-color 0.2s,
    color 0.2s;
  -webkit-app-region: no-drag;
}

.dialog-close-button:hover {
  background-color: var(--button-button2-default);
  color: var(--font-font-2);
}

.dialog-close-button:focus {
  outline: none;
  // box-shadow: 0 0 0 2px var(--border-border-2);
}

.dialog-close-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

/* ========== 操作区域组件 ========== */
.dialog-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  gap: 12px;
}

.dialog-actions-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.dialog-actions-right {
  display: flex;
  justify-content: flex-end;
  margin-left: auto;
  gap: 12px;
}

/* ========== 基础按钮组件 ========== */
.dialog-btn {
  padding: 8px 16px;
  border: none;

  border-radius: var(--border-radius-M, 8px);
  color: var(--font-font-2, #4e5769);
  font-style: normal;
  font-weight: 500;
  font-size: 14px;

  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dialog-btn[disabled] {
  cursor: not-allowed;
  opacity: 0.6;
}

.dialog-cancel {
  background-color: var(--button-button2-default, #f2f3f5);
  color: var(--font-font-2, #4e5769);
}

.dialog-action {
  display: flex;
  /* 169.231% */

  border-radius: var(--border-radius-M, 8px);
  background: var(--button-button1-default, #272e3b);
  color: white;
  color: var(--button-button1-font-default, #fff);
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 22px;

  /* Footnote/description2 */
  font-family: 'Microsoft YaHei';
}

/* hover待定 */
/* .dialog-cancel:hover:not([disabled]) {
      background-color: var(--border-border-2);
  }

  .dialog-action:hover:not([disabled]) {
      background-color: var(--primary-primary-active);
  } */

/* ========== Trigger按钮样式 ========== */
.dialog-trigger {
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius-M, 8px);
  background-color: var(--button-button1-default, #272e3b);
  color: var(--button-button1-font-default, #fff);
  font-style: normal;
  font-weight: 500;
  font-weight: 400;
  font-size: 14px;
  font-size: 13px;
  line-height: 22px;
  font-family: 'Microsoft YaHei';
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dialog-trigger:hover:not([disabled]) {
  background-color: var(--button-button1-hover, #1b2329);
}

.dialog-trigger:focus {
  outline: none;
  // box-shadow: 0 0 0 2px var(--border-border-2);
}

.dialog-trigger[disabled] {
  cursor: not-allowed;
  opacity: 0.6;
}

/* ========== 变体样式 ========== */
/* 基础变体 */
.variant-success .dialog-title {
  color: var(--functional-success-default);
}

.variant-warning .dialog-title {
  color: var(--functional-warning-default);
}

.variant-thunder .dialog-title {
  color: var(--font-font-1, #272e3b);
}

.variant-info .dialog-title {
  color: var(--primary-primary-default);
}

/* ========== Thunder按钮样式 ========== */
.thunder-action {
  /* background-color: var(--primary-primary-default, #226DF5);
      color: white; */

  /* 和默认一致 */
}

.thunder-action:hover:not([disabled]) {
  /* background-color: var(--primary-primary-active, #1b5ac2); */
}

.thunder-cancel {
  background-color: var(--button-button2-default);
  color: var(--font-font-2);
}

/* ========== AlertDialog 组件样式 ========== */
/* 信息对话框 - 蓝色 */
.info-title {
  color: var(--primary-primary-default);
}

.info-action {
  background-color: var(--primary-primary-default);
}

.info-action:hover {
  background-color: var(--primary-primary-active);
}

.info-cancel {
  color: var(--font-font-2);
}

/* 成功对话框 - 绿色 */
.success-title {
  color: var(--functional-success-default);
}

.success-action {
  background-color: var(--functional-success-default);
}

.success-action:hover {
  background-color: var(--functional-success-active);
}

.success-cancel {
  color: var(--font-font-2);
}

/* 警告对话框 - 橙色 */
.warning-title {
  color: var(--functional-warning-default);
}

.warning-action {
  background-color: var(--functional-warning-default);
}

.warning-action:hover {
  background-color: var(--functional-warning-active);
}

.warning-cancel {
  color: var(--font-font-2);
}

.error-action {
  background: var(--button-button-warn-default, #ffece8);
  color: var(--button-button-warn-font-default, #ff4d4f);
}

.error-action:hover {
  background: var(--button-button-warn-hover, #ffdad4);
}

.error-cancel {
  color: var(--font-font-2);
}
