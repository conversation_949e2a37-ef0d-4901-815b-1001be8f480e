#include "stdafx.h"

class CSaveHistoryBox : public WindowImplBase
{
public:
	CSaveHistoryBox()
	{
	};
	virtual ~CSaveHistoryBox()
	{
		DestroyWindow(GetParent(this->GetHWND()));
	};

	LPCTSTR GetWindowClassName() const { return _T("SaveHistoryDialog"); };
	UINT GetClassStyle() const { return UI_CLASSSTYLE_DIALOG; };
	virtual CDuiString     GetSkinFolder() { return L""; };
	virtual CDuiString     GetSkinFile() { return L""; };
	void OnFinalMessage(HWND /*hWnd*/)
	{
		m_pm.RemovePreMessageFilter(this);
		delete this;
	};

	void Init() {
		int nOriginalDpi = CDPI::GetMainMonitorDPI();
		setDPI(nOriginalDpi);	
	}

	void Notify(TNotifyUI& msg)
	{
		if (msg.sType == _T("click"))
		{
			if (msg.pSender->GetName() == _T("closebtn"))
			{
				Close(IDOK);
			}
			else if (msg.pSender->GetName() == _T("reserve_btn"))
			{
				Close(IDOK);
			}
			else if (msg.pSender->GetName() == _T("removal_btn"))
			{
				Close(IDCANCEL);
			}
		}
	}

	void setDPI(int DPI)
	{
		m_pm.SetDPI(DPI);
	}
	LRESULT OnDestroy(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/)
	{
		return S_OK;
	}
	LRESULT OnClose(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/)
	{
		DestroyWindow(m_hWnd);
		return S_OK;
	}
	LRESULT OnCreate(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/)
	{
		LONG styleValue = ::GetWindowLong(*this, GWL_STYLE);
		styleValue &= ~WS_CAPTION;
		::SetWindowLong(*this, GWL_STYLE, styleValue | WS_CLIPSIBLINGS | WS_CLIPCHILDREN);

		m_pm.Init(m_hWnd);
		m_pm.AddPreMessageFilter(this);
		CDialogBuilder builder;
		CControlUI* pRoot = builder.Create(_T("SaveHistoryBox.xml"), (UINT)0, NULL, &m_pm);
		ASSERT(pRoot && "Failed to parse XML");
		m_pm.AttachDialog(pRoot);
		m_pm.AddNotifier(this);

		Init();
		return 0;
	}


	LRESULT OnSize(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& bHandled)
	{
		SIZE szRoundCorner = m_pm.GetRoundCorner();
		if (!::IsIconic(*this) && (szRoundCorner.cx != 0 || szRoundCorner.cy != 0)) {
			CDuiRect rcWnd;
			::GetWindowRect(*this, &rcWnd);
			rcWnd.Offset(-rcWnd.left, -rcWnd.top);
			rcWnd.right++; rcWnd.bottom++;
			HRGN hRgn = ::CreateRoundRectRgn(rcWnd.left, rcWnd.top, rcWnd.right, rcWnd.bottom, szRoundCorner.cx, szRoundCorner.cy);
			::SetWindowRgn(*this, hRgn, TRUE);
			::DeleteObject(hRgn);
		}

		bHandled = FALSE;
		return 0;
	}

	LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam)
	{
		LRESULT lRes = 0;
		BOOL bHandled = TRUE;
		switch (uMsg) {
		case WM_CREATE:        lRes = OnCreate(uMsg, wParam, lParam, bHandled); break;
		default:
			bHandled = FALSE;
		}
		if (bHandled) return lRes;
		if (m_pm.MessageHandler(uMsg, wParam, lParam, lRes)) return lRes;
		return WindowImplBase::HandleMessage(uMsg, wParam, lParam);
	}

	LRESULT HandleCustomMessage(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
	{
		if (uMsg == WM_DPICHANGED) {
			OnDPIChanged(uMsg, wParam, lParam, bHandled);
		}
	
		bHandled = FALSE;
		return 0;
	}

	LRESULT OnDPIChanged(UINT /*uMsg*/, WPARAM wParam, LPARAM lParam, BOOL& bHandled)
	{
		m_pm.SetDPI(LOWORD(wParam));  // Set the new DPI, retrieved from the wParam

		RECT* const prcNewWindow = (RECT*)lParam;
		SetWindowPos(m_hWnd,
			NULL,
			prcNewWindow->left,
			prcNewWindow->top,
			prcNewWindow->right - prcNewWindow->left,
			prcNewWindow->bottom - prcNewWindow->top,
			SWP_NOZORDER | SWP_NOACTIVATE);


		if (m_pm.GetRoot() != NULL) m_pm.GetRoot()->NeedUpdate();

		bHandled = false;

		return 0;
	}
};
