<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Progress from '@root/common/components/ui/progress/index.vue'

const percentage = ref(0)

onMounted(() => {
  setInterval(() => {
    percentage.value = percentage.value+1
    if (percentage.value > 100) {
      percentage.value = 0
    }
  }, 100)
})
</script>

<template>
  <div>
    <h1>Progress</h1>
    <div>
      <Progress
        class="task-item__progress"
        :percentage="10"
        height="10"
        background="backdrop-filter: blur(4px)"
        fillBorderRadius='50'
        railBorderRadius="50"
      ></Progress>
    </div>
    <div style="margin-top: 20px;">
      <Progress
        :percentage="50"
        :processing="true"
      />
    </div>
    <div style="margin-top: 20px;">
      <Progress
        type="circle"
        :percentage="50"
        :strokeWidth="1.5"
        :width="15"
      />
    </div>
    <div style="margin-top: 20px;">
      <Progress
        type="circle"
        :percentage="percentage"
        :strokeWidth="6"
        :width="100"
      />
    </div>
  </div>
</template>