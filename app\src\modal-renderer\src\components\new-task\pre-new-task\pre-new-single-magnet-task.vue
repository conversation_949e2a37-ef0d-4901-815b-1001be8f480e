<template>
  <div class="auto-parse-single-magnet">
    <Dialog
      :open="isOpen"
      :showTitleIcon="true"
      @update:open="handleOpenChange"
      :title="dialogTitle"
      :show-trigger="false"
      :show-cancel="false"
      :show-actions="isParsingComplete"
      :confirm-text="isParsingComplete ? '播放' : ''"
      :show-close-button="true"
      @confirm="handleConfirm"
      @close="handleClose"
      :prevent-default-close="true"
      class-prefix="pre-new-task single-magnet-dialog"
      :isCreateTask="true"
    >
      <!-- 解析中状态 -->
      <div
        class="single-task-content single-magnet parsing-loading parsing-failed"
        v-if="!isParsingComplete"
      >
        <div class="task-info-section">
          <!-- 左侧：视频缩略图/文件图标 -->
          <div class="bt-icon-container">
            <div class="file-icon-type file-type-folder-bt is-large"></div>
          </div>

          <div class="task-details-section">
            <!-- 任务详情 -->
            <div class="task-details">
              <div
                class="task-name"
                :class="{
                  isErrored: isErroredDuringCountdown,
                }"
              >
                {{ taskNameDuringCountdown }}
              </div>

              <div class="loading-container">
                <LottieAnimation
                  class="magnet-parsing-loading"
                  :animation-data="require('@root/common/assets/lottie/parsing.json')"
                  :auto-play="true"
                  :loop="true"
                />
              </div>
            </div>
          </div>

          <div class="retry-btn-container">
            <Button
              class="retry-btn"
              size="default"
              :disabled="isRetryButtonDisabled"
              @click="handleRetry"
            >
              {{ retryButtonText }}
            </Button>
          </div>
        </div>
      </div>
      <!-- 解析完成状态 -->
      <div
        class="parsing-complete parsing-complete-single-magnet"
        v-if="isParsingComplete"
      >
        <div class="single-task-content">
          <!-- 任务信息区域 -->
          <div :class="['task-info-section', { 'video-task-info': true }]">
            <!-- 左侧：视频缩略图/文件图标 -->
            <div class="bt-icon-container">
              <div class="file-icon-type file-type-folder-bt is-large"></div>
            </div>
            <!-- 右侧：任务详情 -->
            <div class="task-details-section">
              <!-- 任务详情 -->
              <div class="task-details">
                <div class="task-name">
                  {{ fileName }}
                </div>

                <div
                  class="task-size"
                  @click="handleShowTaskList"
                >
                  <span>已选 {{ checkedFileCount }}/{{ total }} 个文件</span>
                  <span>{{ fileSize }}</span>
                  <i
                    class="xl-icon-direction-down-m"
                    v-if="!showTaskList"
                  ></i>
                  <i
                    class="xl-icon-direction-up-m"
                    v-if="showTaskList"
                  ></i>
                </div>
              </div>
            </div>
            <!-- 配置图标 -->
            <div
              class="config-icon"
              @click="handleTaskSetting"
              title="任务设置"
            >
              <i class="xl-icon-general-configure-m"></i>
            </div>
          </div>
        </div>
        <!-- 文件列表区域  单磁力链任务只渲染文件列表-->
        <div :class="{ 'task-list-container': true, 'is-show': isParsingComplete && showTaskList }">
          <TaskList
            :task-data="taskData"
            :data-map="dataMap"
            :container-height="402"
            :custom-checked-keys="defaultCheckedKeys"
            :magnet-file-lists-only="true"
            :auto-expand-all="props.options?.autoExpandAll || false"
            :options-ext-data="props.optionsExtData || {}"
            @checkedFileIndexes="handleCheckedFileIndexes"
            @retryMagnetTask="handleRetryMagnetTask"
          />
        </div>
      </div>
      <!-- 添加左侧稍后按钮 -->
      <template #left-action>
        <TaskLaterButton
          v-if="shouldNeedTaskLaterButton"
          :task-data="taskData"
          :data-map="dataMap"
          :options-ext-data="optionsExtData || {}"
          :has-valid-tasks="hasValidTasks"
          :checked-file-indexes="checkedFileIndexes"
          scene="pre-new-single-magnet"
          @success="handleLaterSuccess"
          @error="handleLaterError"
          @cancel="handleLaterCancel"
          variant="outline"
          size="lg"
        >
          稍后
        </TaskLaterButton>
      </template>
      <template #right-action>
        <!-- 播放按钮 - 只在有可播放任务时显示 -->
        <PlayButton
          v-if="shouldNeedPlayButton"
          :checked-file-indexes="checkedFileIndexes"
          :data-map="dataMap || {}"
          :options-ext-data="optionsExtData || {}"
          :task-data="taskData"
          scene="pre-new-single-magnet"
        />
        <DownloadButton
          scene="pre-new-single-magnet"
          :selected-path-type="selectedPathType"
          @submit="handleDownloadButtonSubmit"
          :disabled="isDownloadButtonDisabled"
        ></DownloadButton>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
// ===== 导入依赖 =====
// Vue 核心
import { ref, onMounted, computed, watch, nextTick, onUnmounted } from 'vue'
import { PopUpNS } from '@root/common/pop-up'

// 组件导入
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import TaskList from '@root/modal-renderer/src/components/new-task/task-list/task-list.vue'
import Button from '@root/common/components/ui/button/index.vue'
import { LottieAnimation } from 'lottie-web-vue'
import DownloadButton from '@root/modal-renderer/src/components/new-task/download-button/download-button.vue'
import TaskLaterButton from '@root/modal-renderer/src/components/new-task/task-later-button/TaskLaterButton.vue'
import PlayButton from '@root/modal-renderer/src/components/new-task/play-button/PlayButton.vue'

// 类型定义
import type {
  IUrlWithType,
  IUrlDataMap,
  TaskFileSelectionMap,
  DownloadEventParams,
  DownloadButtonSubmitParams,
  TaskExtDataMap,
} from '@root/modal-renderer/types/new-task.type'
import { DownloadPathType, TaskParseStatusType } from '@root/modal-renderer/types/new-task.type'
import { TaskType } from '@root/common/task/base'
import * as PopUpTypes from '@root/common/pop-up/types'
import type { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'

// 工具函数和工具类
import { formatSize } from '@root/modal-renderer/src/utils.help'
import { usePositionMixinComponent } from '@root/modal-renderer/src/common/mixins'
import { useTaskSettingStore } from '@root/modal-renderer/src/stores/task-setting'
import { useNewTaskStore } from '@root/modal-renderer/src/stores/new-task'

// Composable 函数
import {
  useDownloadHandler,
  useCloseWindowHandler,
} from '@root/modal-renderer/src/composables/useTaskDownload'
import { useTaskSetting } from '@root/modal-renderer/src/composables/useTaskSetting'

// ===== Props 定义 =====
interface Props {
  taskData: IUrlWithType[]
  dataMap?: IUrlDataMap
  options?: ThunderNewTaskHelperNS.IShowPreNewTaskWindowOptions
  optionsExtData?: TaskExtDataMap
}

const props = withDefaults(defineProps<Props>(), {
  taskData: () => [],
  dataMap: () => ({}),
  options: () => ({}),
  optionsExtData: () => ({}),
})

// ===== Emits 定义 =====
const emit = defineEmits<{
  close: []
}>()

// ===== Store 和 Composable 实例化 =====
const taskSettingStore = useTaskSettingStore()
const newTaskStore = useNewTaskStore()
const { overridePosition, resizeToFitContent } = usePositionMixinComponent()
const { handleDownload } = useDownloadHandler()
const { handleCloseWindow } = useCloseWindowHandler()
const { showTaskSettingDialog: executeTaskSetting } = useTaskSetting()

// ===== 响应式数据 =====
const isOpen = ref(true)
console.log('[PreNewSingleMagnetTask] 初始化 showTaskList', props.options?.autoExpandAll)
const showTaskList = ref(props.options?.autoExpandAll || false)
const checkedFileIndexes = ref<TaskFileSelectionMap>({})

// LottieAnimation 组件引用
const lottieRef = ref()

// 重试状态跟踪
const isRetrying = ref(false)
const lastRetryUrl = ref<string>('')
const retryStartTime = ref<number>(0)

// 倒计时状态
const countdownSeconds = ref(0)
const countdownTimer = ref<number | null>(null)
const isInCountdown = ref(false)

// ===== 计算属性 =====
const detailInfo = computed(() => {
  if (props.taskData.length > 0 && props.taskData[0]?.url) {
    const firstTaskUrl = props.taskData[0].url
    const detail = props.dataMap?.[firstTaskUrl]
    console.log('[AutoCreateTask] detailInfo computed for URL:', firstTaskUrl, 'detail:', detail)
    return detail
  }
  return null
})

const taskUrl = computed(() => {
  return props.taskData?.[0].url || ''
})

const defaultCheckedKeys = computed(() => {
  // 从 new-task store 的 getOriginDataMapItem 函数获取数据
  if (taskUrl.value) {
    const originDataItem = newTaskStore.getOriginDataMapItem(taskUrl.value)
    const defaultCheckedFileIndexes = originDataItem?.defaultCheckedFileIndexes

    if (defaultCheckedFileIndexes && Array.isArray(defaultCheckedFileIndexes)) {
      // 将 number[] 转换为 string[]，因为 TaskList 组件的 custom-checked-keys 期望 string[] 类型
      const stringKeys = defaultCheckedFileIndexes.map(index => `${taskUrl.value}-${index}`)
      console.log(
        '📋 [PreNewSingleMagnetTask] 从 originDataMap 获取到 defaultCheckedFileIndexes:',
        defaultCheckedFileIndexes,
        '转换为字符串:',
        stringKeys
      )
      return stringKeys
    }
  }

  console.warn('📋 [PreNewSingleMagnetTask] 未找到 defaultCheckedFileIndexes 或无效，返回空数组')
  return []
})

const total = computed(() => {
  return detailInfo.value?.fileLists?.length || 0
})

const checkedFileCount = computed(() => {
  console.log(
    'checkedFileIndexes',
    checkedFileIndexes.value,
    taskUrl.value,
    checkedFileIndexes.value[taskUrl.value]?.fileIndexes.length
  )

  return checkedFileIndexes.value[taskUrl.value]?.fileIndexes.length || 0
})

const status = computed<TaskParseStatusType>(() => {
  const statusValue = detailInfo.value?.status || 'loading'
  return statusValue
})

const isParsingComplete = computed(() => {
  return status.value === 'success'
})

const isParsingFailed = computed(() => {
  return status.value === 'error'
})

const isParsing = computed(() => {
  return status.value === 'loading'
})

const fileSize = computed(() => {
  const fileSizeValue = detailInfo.value?.fileSize || 0
  return formatSize(fileSizeValue)
})

const fileName = computed(() => {
  const taskUrl = props.taskData?.[0]?.url
  if (taskUrl) {
    const customTaskNameFromOptions = props.optionsExtData?.[taskUrl]?.fileName
    if (customTaskNameFromOptions) {
      console.log('[SingleMagnetTask] 使用自定义任务名称:', customTaskNameFromOptions)
      return customTaskNameFromOptions
    }
  }

  return detailInfo.value?.fileName || detailInfo.value?.title || detailInfo.value?.url
})

const selectedPathType = computed(() => {
  const pathType = props.options?.selectedPathType
  if (pathType === 'local') {
    return DownloadPathType.Local
  } else if (pathType === 'cloud') {
    return DownloadPathType.Cloud
  }
  return undefined
})

// 计算属性：判断是否需要显示稍后按钮
const shouldNeedTaskLaterButton = computed(() => {
  // 如果 options.showLaterButton 明确设置为 false，则不显示
  // 其他情况（undefined、true 或其他值）都默认显示
  return props.options?.showLaterButton !== false
})

// 计算属性：判断是否需要显示播放按钮
const shouldNeedPlayButton = computed(() => {
  // 如果 options.showPlayButton 明确设置为 false，则不显示
  // 其他情况（undefined、true 或其他值）都默认显示
  return props.options?.showPlayButton !== false
})

const hasValidTasks = computed(() => {
  return props.taskData.length > 0 && isParsingComplete.value
})

const dialogTitle = computed(() => {
  return props.options?.title || '新建任务'
})

// 重试状态信息
const retryStateInfo = computed(() => {
  return {
    isRetrying: isRetrying.value,
    lastRetryUrl: lastRetryUrl.value,
    retryStartTime: retryStartTime.value,
    timeSinceRetry: retryStartTime.value > 0 ? Date.now() - retryStartTime.value : 0,
  }
})

// 倒计时按钮文案
const retryButtonText = computed(() => {
  if (isInCountdown.value && countdownSeconds.value > 0) {
    return `重试(${countdownSeconds.value}s)`
  }
  return '重试'
})

// 倒计时期间是否禁用按钮
const isRetryButtonDisabled = computed(() => {
  return !isParsingFailed.value || isInCountdown.value
})

// 倒计时期间的错误状态
const isErroredDuringCountdown = computed(() => {
  // 倒计时期间不显示错误状态
  return isParsingFailed.value && !isInCountdown.value
})

// 倒计时期间的任务名称
const taskNameDuringCountdown = computed(() => {
  // if (isInCountdown.value) {
  //   return '正在努力获取种子文件'
  // }

  // if (isParsing.value) {
  //   return '正在努力获取种子文件'
  // }

  if (isParsingFailed.value) {
    return '任务参数错误，无法继续下载'
  }

  return '正在努力获取种子文件'
})

// 计算下载按钮是否应该被禁用
const isDownloadButtonDisabled = computed(() => {
  // 如果解析未完成，禁用按钮
  if (!isParsingComplete.value) {
    return true
  }

  // 如果已选中的文件数小于等于0，禁用按钮
  const selectedCount = checkedFileCount.value || 0
  return selectedCount <= 0
})

// ===== 生命周期钩子 =====
onMounted(() => {
  showLoadingWindowSize()
})

// 组件卸载时清理倒计时

onUnmounted(() => {
  console.log('[SingleMagnetTask] 组件卸载，清理倒计时')
  stopCountdown()
})

/**
 * 判断数据更新是否来自于重试逻辑
 * @param url 当前任务的URL
 * @param newDataMap 新的数据映射
 * @returns boolean 是否来自于重试逻辑
 */
function isDataUpdateFromRetry(url: string, newDataMap: IUrlDataMap): boolean {
  // 检查是否正在重试且URL匹配
  if (!isRetrying.value || lastRetryUrl.value !== url) {
    return false
  }

  // 检查重试开始时间是否在合理范围内（比如5秒内）
  const currentTime = Date.now()
  const timeDiff = currentTime - retryStartTime.value
  const isWithinRetryTimeWindow = timeDiff > 0 && timeDiff < 5000 // 5秒内

  if (!isWithinRetryTimeWindow) {
    console.log('[SingleMagnetTask] 重试时间窗口已过期，不认为是重试更新')
    return false
  }

  // 检查数据状态是否从错误变为加载中或成功
  const currentDetail = newDataMap[url]
  if (currentDetail) {
    const status = currentDetail.status
    console.log(
      `[SingleMagnetTask] 检测到重试更新，URL: ${url}, 状态: ${status}, 时间差: ${timeDiff}ms`
    )
    return true
  }

  return false
}

/**
 * 开始倒计时
 */
function startCountdown() {
  console.log('[SingleMagnetTask] 开始10秒倒计时')
  isInCountdown.value = true
  countdownSeconds.value = 10

  countdownTimer.value = window.setInterval(() => {
    countdownSeconds.value--

    if (countdownSeconds.value <= 0) {
      stopCountdown()
    }
  }, 1000)
}

/**
 * 停止倒计时
 */
function stopCountdown() {
  console.log('[SingleMagnetTask] 停止倒计时')
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  isInCountdown.value = false
  countdownSeconds.value = 0
}

// 恢复窗口尺寸相关函数
function showLoadingWindowSize() {
  const defaultPositionOptions = {
    autoSize: false,
    show: false,
    selector: '.dialog-content',
    windowWidth: 680,
    windowHeight: 152,
    relatePos: PopUpTypes.RelatePosType.CenterParent,
  }
  overridePosition(defaultPositionOptions)
  resizeToFitContent()
  nextTick(() => {
    const currentWindow = PopUpNS.getCurrentWindow()
    if (currentWindow) {
      currentWindow.show()
    }
  })
}

function showCompletedWindowSize() {
  const defaultPositionOptions = {
    autoSize: false,
    show: true,
    selector: '.dialog-content',
    windowWidth: 680,
    windowHeight: 240,
    relatePos: PopUpTypes.RelatePosType.CenterParent,
  }
  overridePosition(defaultPositionOptions)
  resizeToFitContent()
  // nextTick(() => {
  //   const currentWindow = PopUpNS.getCurrentWindow()
  //   if (currentWindow) {
  //     currentWindow.show()
  //   }
  // })
}

function showTaskListWindowSize() {
  const defaultPositionOptions = {
    autoSize: false,
    show: true,
    selector: '.dialog-content',
    windowWidth: 680,
    windowHeight: 658,
    relatePos: PopUpTypes.RelatePosType.CenterParent,
  }
  overridePosition(defaultPositionOptions)
  resizeToFitContent()
  // nextTick(() => {
  //   const currentWindow = PopUpNS.getCurrentWindow()
  //   if (currentWindow) {
  //     currentWindow.show()
  //   }
  // })
}

// ===== 事件处理函数 =====
const handleOpenChange = (value: boolean) => {
  console.log('[AutoCreateTask] handleOpenChange called with:', value)
  isOpen.value = value
}

const handleClose = async () => {
  console.log('[SingleMagnetTask] handleClose called')

  try {
    handleCloseWindow('single-magnet-task-close')
  } catch (error) {
    console.error('[SingleMagnetTask] 关闭窗口失败:', error)
    emit('close')
  }
}

const handleConfirm = () => {
  // 播放功能现在由 PlayButton 组件处理
}

const handleRetry = async () => {
  const url = props.taskData?.[0]?.url || ''

  try {
    console.log('[SingleMagnetTask] 开始重试解析磁力链:', url)

    // 开始倒计时
    startCountdown()

    // 设置重试状态
    isRetrying.value = true
    lastRetryUrl.value = url
    retryStartTime.value = Date.now()

    // 直接使用 newTaskStore 中的解析函数
    const result = await newTaskStore.parseMagnetLinkInfo({
      url: url,
      forceReparse: true, // 重试时强制重新解析
    })

    if (result) {
      console.log('磁力链重新解析完成')
    } else {
      console.error('磁力链重新解析失败')
    }
  } catch (error) {
    console.error('重新解析磁力链失败:', error)
  } finally {
    // 延迟重置重试状态，给数据更新一些时间
    setTimeout(() => {
      isRetrying.value = false
      lastRetryUrl.value = ''
      retryStartTime.value = 0
    }, 1000)
  }
}

const handleCheckedFileIndexes = (checkedFiles: TaskFileSelectionMap) => {
  console.log('[AutoCreateTask] handleCheckedFileIndexes called with:', checkedFiles)
  checkedFileIndexes.value = checkedFiles
}

const handleRetryMagnetTask = (taskInfo: any) => {
  console.log('[AutoCreateTask] handleRetryMagnetTask called with:', taskInfo)
}

const handleDownloadButtonSubmit = (params: DownloadButtonSubmitParams) => {
  const downloadType: 'download' | 'cloud' =
    params.type === DownloadPathType.Local ? 'download' : 'cloud'

  const downloadParams: DownloadEventParams = {
    checkedFileIndexes: checkedFileIndexes.value,
    type: downloadType,
    path: params.path,
  }

  console.log('[SingleMagnetTask] 透传给父组件:', downloadParams)

  handleDownload(downloadParams)
}

const handleShowTaskList = () => {
  console.log(
    '[AutoCreateTask] handleShowTaskList called, current showTaskList:',
    showTaskList.value
  )
  showTaskList.value = !showTaskList.value
}

const handleTaskSetting = async () => {
  if (!taskUrl.value) {
    console.warn('[SingleMagnetTask] taskUrl 为空，无法打开任务设置')
    return
  }

  console.log('[SingleMagnetTask] 打开任务设置，URL:', taskUrl.value)

  const currentTaskSetting = taskSettingStore.getTaskSettings(taskUrl.value)

  await executeTaskSetting(taskUrl.value, currentTaskSetting)
}

// const handleButtonTooltipCancel = () => {
//   console.log('[AutoCreateTask] ButtonTooltip cancel event triggered')
//   handleClose()
// }

// const handleButtonTooltipSubmit = () => {
//   console.log('[AutoCreateTask] ButtonTooltip submit event triggered')
//   handleDownloadButtonSubmit({ type: DownloadPathType.Local })
// }

const handleLaterSuccess = (result: {
  success: boolean
  message: string
  savedCount?: number
  failedCount?: number
}) => {
  console.log('[SingleMagnetTask] TaskLaterButton 成功:', result)
  // TODO：加埋点
  // handleClose()
}

const handleLaterError = (error: string) => {
  console.error('[SingleMagnetTask] TaskLaterButton 失败:', error)
}

const handleLaterCancel = () => {
  console.log('[SingleMagnetTask] TaskLaterButton 取消')
  handleClose()
}

// ===== 监听器 =====
watch(
  () => props.taskData,
  newTaskData => {
    console.log('[AutoCreateTask] props.taskData changed:', newTaskData)

    if (newTaskData && newTaskData.length > 0) {
      newTaskData.forEach((item, index) => {
        const typeName = TaskType[item.taskType] || 'Unknown'
        console.log(
          `[AutoCreateTask] URL ${index}: ${item.url} -> Type: ${typeName} (${item.taskType})`
        )

        const urlDetail = props.dataMap?.[item.url]
        if (urlDetail) {
          console.log(`[AutoCreateTask] URL ${index} detail from dataMap:`, urlDetail)
        } else {
          console.log(`[AutoCreateTask] No detail found in dataMap for URL ${index}: ${item.url}`)
        }
      })
    }
  },
  { deep: true }
)

watch(
  () => props.dataMap,
  (newDataMap, oldDataMap) => {
    console.log('[AutoCreateTask] props.dataMap changed:', newDataMap)

    // 在倒计时期间不响应数据更新
    if (isInCountdown.value) {
      console.log('⚠️[SingleMagnetTask] 倒计时期间，忽略数据更新')
      return
    }

    if (newDataMap && Object.keys(newDataMap).length > 0) {
      Object.entries(newDataMap).forEach(([url, detail], index) => {
        console.log(`[AutoCreateTask] DataMap entry ${index}: ${url}`, detail)

        // 检查是否来自于重试逻辑
        const isFromRetry = isDataUpdateFromRetry(url, newDataMap)
        if (isFromRetry) {
          console.log(`[SingleMagnetTask] 检测到来自重试逻辑的数据更新: ${url}`)
          console.log(
            `[SingleMagnetTask] 重试状态: isRetrying=${isRetrying.value}, lastRetryUrl=${lastRetryUrl.value}`
          )

          // 可以在这里添加重试成功后的特殊处理逻辑
          if (detail.status === 'success') {
            console.log(`[SingleMagnetTask] 重试成功，任务解析完成: ${url}`)
            // 可以触发一些重试成功后的特殊行为
          } else if (detail.status === 'error') {
            console.log(`[SingleMagnetTask] 重试失败，任务解析失败: ${url}`)
            // 可以触发一些重试失败后的特殊行为
          }
        } else {
          console.log(`[SingleMagnetTask] 常规数据更新: ${url}`)
        }
      })
    }
  },
  { deep: true }
)

watch(
  [status, isParsing, isParsingFailed, isParsingComplete],
  ([newStatus, newIsParsing, newIsParsingFailed, newIsParsingComplete]) => {
    console.log('[AutoCreateTask] 状态总览:', {
      status: newStatus,
      isParsing: newIsParsing,
      isParsingFailed: newIsParsingFailed,
      isParsingComplete: newIsParsingComplete,
      detailInfo: detailInfo.value,
    })
  },
  { immediate: true }
)

watch([showTaskList, isParsing, isParsingFailed], () => {
  console.log('[SingleMagnetTask] 状态变化，重新计算窗口尺寸')

  nextTick(() => {
    if (showTaskList.value) {
      showTaskListWindowSize()
    } else {
      if (isParsing.value) {
        showLoadingWindowSize()
      } else {
        showCompletedWindowSize()
      }
    }
  })
})

// 监听 autoExpandAll 变化，但不在这里触发窗口尺寸调整
watch(
  () => props.options?.autoExpandAll,
  newAutoExpandAll => {
    if (newAutoExpandAll) {
      showTaskList.value = true
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
// 统一引用pre-new-task.scss
@import '@root/modal-renderer/src/components/new-task/pre-new-task/pre-new-task.scss';
</style>
