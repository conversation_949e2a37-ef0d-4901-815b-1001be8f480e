<script setup lang="ts">
import LinkItem from './components/LinkItem.vue'
import DetailItem from './components/DetailItem.vue'
import Breadcrumb, { IBreadcrumbItemData } from '@root/common/components/ui/breadcrumb/index.vue'
import { computed, onMounted, onUnmounted, ref, useTemplateRef, watch, nextTick } from 'vue'
import { useKeepAliveHooks } from '@/hooks/useKeepAliveHooks'
import Empty from './components/Empty.vue'
import { LinkHubHelper } from '@root/common/link-hub/client/link-hub-helper'
import { LinkRecordHelper } from '@root/common/link-hub/client/link-record-helper'
import Loading from '@root/common/components/ui/loading/index.vue'
import { useInfiniteScroll } from '@vueuse/core'
import { useRouter, useRoute } from 'vue-router'
import { EventDispatcher } from '@root/common/link-hub/client/event-dispatcher'
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'
import LoadingSpinner from '@root/common/components/ui/loading-spinner/index.vue'
import { useAlertDialog } from '@root/common/components/ui/Dialog/useAlertDialog'
import XMPMessage from '@root/common/components/ui/message/index'
import { RecycleScroller } from 'vue-virtual-scroller'

defineOptions({
  name: 'link-detail'
})

const alertDialog = useAlertDialog()

const props = defineProps<{
  linkData: ThunderClientAPI.dataStruct.dataModals.LinkRecord
}>()

const emit = defineEmits<{
  (e: 'renameSuccess', newName: string): void
  (e: 'deleteSuccess'): void
}>()

const router = useRouter()
const route = useRoute()

const el = useTemplateRef<RecycleScroller>('el')

// 分页相关状态
const page = ref(1)
const pageSize = ref(30)
const loading = ref(false)
const initialLoading = ref(true)
const totalLinkDetailCount = ref(0)

const linkData = ref<ThunderClientAPI.dataStruct.dataModals.LinkRecord>(props?.linkData)

// 面包屑数据
const curParentId = ref(route.params.id as string || linkData.value?.id || linkData.value?.url_hash || '')
const curBreadCrumbList = ref<IBreadcrumbItemData[]>([])

// 列表数据
const detailList = ref<ThunderClientAPI.dataStruct.dataModals.LinkRecord[]>([])

// 添加高亮ID
const highlightedLinkId = ref('')

const hasMore = computed(() => {
  if (detailList.value.length === 0) return true

  return detailList.value.length < totalLinkDetailCount.value
})

const fetchLinkDetail = async (linkId: string) => {
  try {
    // 获取父链接详情
    const res = await LinkHubHelper.getInstance().getLinks({
      config: {
        ids: [linkId],
        withPlaybackInfo: true,
      },
      limitCount: 1,
      userData: '',
      reload: false,
    })
    return res?.records?.links?.[0]
  } catch (error) {
    console.error('fetchLinkDetail error', error)
    return null
  }
}

const setParentLinks = async (linkId: string) => {
  try {
    curParentId.value = linkId
    
    // 重置面包屑
    curBreadCrumbList.value = []
    
    const breadcrumbPath = await findParentLinks(linkId)
    
    // 设置面包屑数据
    curBreadCrumbList.value = breadcrumbPath
  } catch (error) {
    console.error('setParentLinks error', error)
  }
}

// 递归查找父链接，返回从最上层到当前层的面包屑路径
const findParentLinks = async (parentId: string): Promise<IBreadcrumbItemData[]> => {
  try {
    const parentLink = await fetchLinkDetail(parentId)
    if (!parentLink) {
      return [{ id: parentId, title: '根目录' }]
    }
    
    // 存储当前父链接的面包屑数据
    const breadcrumbPath: IBreadcrumbItemData[] = []
      
    // 如果当前父链接还有上层父链接，则继续递归查找
    if (parentLink.parentid && parentLink.parentid !== '') {
      // 递归查找上层父链接
      const upperParentLinks = await findParentLinks(parentLink.parentid)
      breadcrumbPath.push(...upperParentLinks)
      // 将当前父链接添加到面包屑中
      breadcrumbPath.push({ id: parentLink.id || parentLink.url_hash || '', title: parentLink.name })
    } else {
      // 如果没有上层父链接，则当前父链接是最上层，添加为根目录
      linkData.value = parentLink
      breadcrumbPath.push({ id: parentLink.id || parentLink.url_hash || '', title: '根目录' })
    }
    
    return breadcrumbPath
  } catch (error) {
    console.error('findParentLinks error', error)
    return [{ id: parentId, title: '根目录' }]
  }

}

// 获取数据的方法
const fetchData = async (pageNum: number) => {
  const beginIndex = (pageNum - 1) * pageSize.value
  const data = await LinkRecordHelper.getInstance().loadLinkRecords({
    dataSrcConfig: { dataSourceType: 'TREE_FILE_LIST', dataSourceKey: curParentId.value },
    rangeInfo: {
      range: { beginIndex, count: pageSize.value },
    },
    reload: false,
  })

  console.log('Link Detail loadLinkRecords 全部数据', data)

  return data?.records?.links || []
}

const loadMore = async () => {
  if (loading.value || !hasMore.value) return
  loading.value = true
  try {
    const linkDetailData = await fetchData(page.value)

    console.log('从客户端获取的linkDetailData', linkDetailData)

    if (linkDetailData.length > 0) {
      // 去重，保留前面的，去除后面的重复项
      const existingIds = new Set(detailList.value.map(item => item.id))
      const newItems = linkDetailData.filter(item => !existingIds.has(item.id))
      detailList.value = [...detailList.value, ...newItems]
      page.value++
    }

  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
    initialLoading.value = false
  }
}

const fetchFirstPageLink = async () => {
  try {
    // 确保curParentId已经被正确设置
    const parentId = curParentId.value
    if (!parentId) {
      console.error('无效的父链接ID')
      return
    }
    
    const res = await LinkRecordHelper.getInstance().fetchFirstPage({
      dataSrcConfig: { dataSourceType: 'TREE_FILE_LIST', dataSourceKey: parentId },
      firstLoadCount: pageSize.value,
      reload: true
    })
    console.log('fetchFirstPageLink res', res)
    await loadMore()
    console.log('fetchFirstPageLink highlightedLinkId.value', highlightedLinkId.value)
    if (highlightedLinkId.value) {
      await handleLocateLink(highlightedLinkId.value)
    }
  } catch (error) {
    console.error('fetchFirstPageLink error', error)
  }
}

const handleBreadcrumbItemClick = (item: IBreadcrumbItemData, index: number) => {
  console.log('点击的面包屑数据', item, index)

  if (index === curBreadCrumbList.value.length - 1 || index < 0) {
    return
  }

  // 截取到点击的位置
  curBreadCrumbList.value = curBreadCrumbList.value.slice(0, index + 1)
  
  // 设置当前父ID为点击的面包屑项的ID
  curParentId.value = item.id ?? ''

  // 点击后取消高亮元素
  router.replace({
    query: {
      linkId: ''
    }
  })
  
  // 重置分页状态并重新加载数据
  page.value = 1
  detailList.value = []
  initialLoading.value = true
  // 重新加载数据
  fetchFirstPageLink()
}

const handleTotalCountChanged = (obj: ThunderClientAPI.dataStruct.event.LinkRecordEventDetail_TotalCountChanged) => {
  console.log('LinkRecordEvent_TotalCountChanged1', obj)
  if (obj.parentLinkId === curParentId.value) {
    totalLinkDetailCount.value = obj?.totalCount ?? 0
  }
}

// 使用 useKeepAliveHooks 监听页面生命周期
useKeepAliveHooks(route, {
  onActivated: (route) => {
    // 处理恢复页面的页面逻辑
  },
  onMounted: () => {
    EventDispatcher.getInstance().attachEvent('LinkRecordEvent_TotalCountChanged', handleTotalCountChanged)
  },
  // Clean up interval when component is unmounted
  onUnmounted: () => {
    EventDispatcher.getInstance().detachEvent('LinkRecordEvent_TotalCountChanged', handleTotalCountChanged)
  }
})

const handleLocateLink = async (linkId: string) => {
  // 如果列表中没有数据，则不进行滚动
  if (linkId && detailList.value.length > 0) {
    console.log('handleLocateLink', linkId)
    
    // 检查当前列表中是否已经有这个链接
    const existingLinkIndex = detailList.value.findIndex(link => 
      link.id === linkId
    )
    
    if (existingLinkIndex !== -1) {
      // 如果已经在列表中，滚动到该项
      if (el.value) {
        setTimeout(() => {
          el.value.scrollToItem(existingLinkIndex)
        }, 200)
      }
    } else {
      const link = await fetchLinkDetail(linkId)
      if (link?.id) {
        detailList.value = [link, ...detailList.value]
        if(el.value) {
          el.value.scrollToItem(0)
        }
      }
    }
  }
}

const reloadDetailList = async (linkId: string) => {
  curParentId.value = linkId
    
  // 重置状态
  page.value = 1
  detailList.value = []
  initialLoading.value = true
  
  // 重新加载详情和列表数据
  setParentLinks(linkId)
  fetchFirstPageLink()
}

// 监听route.query.linkId变化
watch(() => route.query.detailId, (newLinkId) => {
  if (newLinkId) {
    console.log('Detail.vue - route.query.linkId changed', newLinkId, initialLoading.value)
    highlightedLinkId.value = newLinkId as string
    if (initialLoading.value) {
      handleLocateLink(newLinkId as string)
    } else {
      reloadDetailList(route.params.id as string)
    }
  } else {
    console.log('Detail.vue - route.query.linkId changed clear', newLinkId, initialLoading.value)
    highlightedLinkId.value = ''
  }
}, { immediate: true })

// 监听route.params.id变化，当链接ID变化时重新加载数据
watch(() => route.params.id, (newId) => {
  if (newId) {
    console.log('Detail.vue - route.params.id changed', newId)
    const linkId = newId as string
    reloadDetailList(linkId)
  }
}, { immediate: true })

const handlePlay = (link: ThunderClientAPI.dataStruct.dataModals.LinkRecord) => {
  ConsumeManagerNs.consumeLink(link)
}


const handleRename = (newName: string) => {
  linkData.value = {
    ...linkData.value,
    name: newName
  }
  emit('renameSuccess', newName)
}

const handleDelete = () => {
  router.back()
  emit('deleteSuccess')
}

const handleRemoveDetailItem = async (item: ThunderClientAPI.dataStruct.dataModals.LinkRecord) => {
  const result = await alertDialog.confirm({
    title: '确定删除链接吗？',
    content: '删除后，账号下所有设备的对应链接将同步删除',
    variant: 'error',
    showTitleIcon: false,
    confirmText: '确认删除'
  })

  if (result !== false) {

    const deleteData = {
      ...linkData.value,
      filesRemovedIndexs: [String(item.file_index)]
    }

    console.log('删除的数据', deleteData)

    const res = await LinkRecordHelper.getInstance().updateLinkRecord(
      {
        link: deleteData
      })

    console.log('删除结果', res)

    if (res?.error?.message) {
      XMPMessage({
        message: res.error.message,
        type: 'error'
      })
    } else {
      XMPMessage({
        message: '删除成功',
        type: 'success'
      })
      detailList.value = detailList.value.filter((detailItem) => detailItem.id !== item.id)
    }
  }

}


const handleItemClick = (item: ThunderClientAPI.dataStruct.dataModals.LinkRecord) => {
  console.log('当前点击的 Detail Item 数据', item)
  // 点击后取消高亮元素
  router.replace({
    query: {
      detailId: ''
    }
  })
  if (item.is_dir) {
    // 更新当前父ID
    const parentId = item.id ?? item.url_hash ?? ''
    curParentId.value = parentId
    
    // 添加到面包屑
    curBreadCrumbList.value = [...curBreadCrumbList.value, { id: parentId, title: item.name }]
    
    // 重置分页状态
    page.value = 1
    detailList.value = []
    initialLoading.value = true
    
    // 重新加载数据
    fetchFirstPageLink()
  } else {
    handlePlay(item)
  }
}


useInfiniteScroll(
  el,
  () => {
    // load more
    console.log('loadMore begin!!!!!!!!!!!!!!!!!!!!!!!!!!', page.value)
    loadMore()
  },
  {
    distance: 10,
    canLoadMore: () => {
      return hasMore.value && !loading.value
    },
  }
)
</script>

<template>
  <div class="link-detail">
    <div class="link-detail-header" v-if="linkData">
      <LinkItem :link="linkData" from="linkDetail" @rename-success="handleRename"
        @delete-success="handleDelete" @play-link="() => handlePlay(linkData)"/>
      <div class="link-detail-header-separator"></div>
      <Breadcrumb v-if="curBreadCrumbList.length > 0" :list="curBreadCrumbList" @item-click="handleBreadcrumbItemClick" />
    </div>

    <Loading v-if="initialLoading" />

    <div v-if="!initialLoading" class="link-detail-content">
      <RecycleScroller ref="el" class="link-detail-wrapper" key-field="url_hash" :items="detailList" :item-size="70"
        data-scroll-container>

        <template v-slot="{ item, index }">
          <DetailItem :key="item.url_hash" :link-detail="item" @select-item="() => handleItemClick(item)" :index="index"
            @remove-item="() => handleRemoveDetailItem(item)" @play-item-link="() => handlePlay(item)" :highlighted="highlightedLinkId === item.id" />
        </template>

        <template #empty>
          <Empty v-if="detailList.length === 0" type="linkDetail" />
        </template>

        <template #after>
          <div class="link-detail-loading" v-if="loading">
            <LoadingSpinner />
          </div>
        </template>

      </RecycleScroller>
    </div>


  </div>
</template>

<style lang="scss" scoped>
.link-detail {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  overflow: hidden;

  &-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 32px;
    width: 100%;
  }

  &-header {
    padding: 0 40px;
    height: 136px;

    &-separator {
      width: 100%;
      height: 1px;
      background-color: var(--border-border-3, #F2F3F5);
      margin-bottom: 8px;
    }
  }

  &-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    position: relative;
  }

  &-wrapper {
    padding: 0px 22px 8px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
  }
}
</style>
