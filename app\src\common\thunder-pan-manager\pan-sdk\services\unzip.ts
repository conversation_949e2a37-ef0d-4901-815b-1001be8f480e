// @ts-nocheck
import { IRequestCommonConfig, IRequestClassImplement, IRequestCommonResponseT, IRequestHeader } from '..'
import { API_DECOMPRESS, Dictionary } from '../types'
import { formatSize, getFileExtension } from '../utils/drive'

export interface IUnzipApisCommonOptions<T> {
  params?: T
  headers?: IRequestHeader
}

export interface IGetFileTreeItem extends API_DECOMPRESS.DecompressFile {
  key: string
  suffix: string
  formatSize: string
  totalSize: number
  children?: IGetFileTreeItem[]
}

export interface IGetFileTreeResponse extends API_DECOMPRESS.ListDecompressResponse, IGetFileTreeItem {
}

class UnzipApis {
  private host: string
  private headers: IRequestHeader
  private config: IRequestCommonConfig
  private requestFn: IRequestClassImplement

  constructor(requestFn: IRequestClassImplement, config: IRequestCommonConfig) {
    this.requestFn = requestFn
    this.config = config
    this.initHost()
    this.initHeaders()
  }

  private initHost () {
    const DRIVE_API: Dictionary = {
      test: 'http://test.api-shoulei-ssl.xunlei.com',
      prod: 'https://api-shoulei-ssl.xunlei.com'
    }
    this.host = DRIVE_API[this.config.env || 'prod']
  }

  private  initHeaders () {
    this.headers = this.config.headers || {}
  }

  async getFileList (options: IUnzipApisCommonOptions<API_DECOMPRESS.ListDecompressRequest> = {}): Promise<IRequestCommonResponseT<API_DECOMPRESS.ListDecompressResponse>> {
    const url = `${this.host}/decompress/v1/list`;
    const data = {
      path: '',
      file_id: '',
      gcid: '',
      password: '',
      file_space: '',
      ...options.params
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_DECOMPRESS.ListDecompressResponse>
  }

  async getFileTree (
    options: IUnzipApisCommonOptions<API_DECOMPRESS.ListDecompressRequest> = {},
    parent: IGetFileTreeItem = { totalSize: 0 } as IGetFileTreeItem
  ): Promise<IRequestCommonResponseT<IGetFileTreeResponse>> {
    const res = await this.getFileList({
      params: {
        path: options.params.path,
        file_id: options.params.file_id,
        gcid: options.params.gcid,
        password: options.params.password,
        file_space: options.params.file_space,
      },
      headers: options.headers,
    })

    for (const item of res.data.files) {
      item['totalSize'] = Number(item.filesize)
      // 如果是文件夹，则拉取文件夹下面的数据
      if (item.kind === 'drive#folder') {
        const nextRes = await this.getFileTree({
          params: {
            path: item.path,
            file_id: options.params.file_id,
            gcid: options.params.gcid,
            password: options.params.password,
            file_space: options.params.file_space,
          },
          headers: options.headers
        }, item as IGetFileTreeItem)

        if (nextRes.success && res.data.status === 'OK') {
          item['children'] = nextRes.data.files
        }
      }
      // 用于计算所有文件大小
      parent.totalSize += Number(item['totalSize'])

      item['key'] = item.path + item.filename
      item['suffix'] = getFileExtension(item.filename).slice(1)
      item['formatSize'] = formatSize(item['totalSize'])
    }

    return res as IRequestCommonResponseT<IGetFileTreeResponse>
  }

  async getProgress (options: IUnzipApisCommonOptions<API_DECOMPRESS.DecompressProgressRequest> = {}): Promise<IRequestCommonResponseT<API_DECOMPRESS.DecompressProgressResponse>> {
    const url = `${this.host}/decompress/v1/progress`;
    const params = {
      task_id: '',
      ...options.params
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_DECOMPRESS.DecompressProgressResponse>
  }

  async postDecompress (options: IUnzipApisCommonOptions<API_DECOMPRESS.DecompressRequest> = {}): Promise<IRequestCommonResponseT<API_DECOMPRESS.DecompressResponse>> {
    const url = `${this.host}/decompress/v1/decompress`;
    const data: any = {
      gcid: '',
      file_id: '',
      password: '',
      default_parent: false,
      parent_id: '',
      files: [],
      parent_full_path: [],
      file_space: '',
      parent_space: '',
      ...options.params
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_DECOMPRESS.DecompressResponse>
  }
}

export default UnzipApis