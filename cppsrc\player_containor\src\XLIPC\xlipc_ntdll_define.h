#ifndef __XLIPC_NTDLL_DEFINE_H__
#define __XLIPC_NTDLL_DEFINE_H__
#include <Windows.h>

typedef struct _FILE_MODE_INFORMATION {
	ULONG Mode;
} FILE_MODE_INFORMATION, *PFILE_MODE_INFORMATION;

#define FileModeInformation 16

#ifndef STATUS_SUCCESS
# define STATUS_SUCCESS ((NTSTATUS) 0x00000000L)
#endif

typedef NTSTATUS(NTAPI *sNtQueryInformationFile)
(HANDLE FileHandle,
	PIO_STATUS_BLOCK IoStatusBlock,
	PVOID FileInformation,
	ULONG Length,
	FILE_INFORMATION_CLASS FileInformationClass);

#endif