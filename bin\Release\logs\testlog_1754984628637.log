[2025-08-12 15:43:48.638][12480:12464][error][xl::dk::CommonThunderStorage::Init]: CommonThunderStorage, begin to open taskdb, path=C:\xunlei\project\thunder_2025\bin\profiles\NewTaskDb.dat (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:30)
[2025-08-12 15:43:48.644][12480:8688][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE TaskBase(TaskId BIGINT PRIMARY KEY,Type INT,Status INT,StatusChangeTime BIGINT,SavePath NVARCHAR,TotalReceiveSize BIGINT,TotalSendSize BIGINT,TotalReceiveValidSize BIGINT,TotalUploadSize BIGINT,CreationTime BIGINT,FileCreated INT,CompletionTime BIGINT,DownloadingPeriod BIGINT,RemovingToRecycleTime BIGINT,FailureErrorCode INT,Url NVARCHAR,ReferenceUrl NVARCHAR,ResourceSize BIGINT,Name NVARCHAR,Cid NVARCHAR,Gcid NVARCHAR,Description NVARCHAR,CategoryId INT,ResourceQueryCid NVARCHAR,CreationRequestType INT,StartMode INT,NamingType INT,StatisticsReferenceUrl NVARCHAR,UserRead INT,FileSafetyFlag INT,Playable INT,BlockInfo BLOB,OpenOnComplete INT,SpecialType INT,Proxy BLOB,OriginReceiveSize BIGINT,P2pReceiveSize BIGINT,P2sReceiveSize BIGINT,OfflineReceiveSize BIGINT,VipReceiveSize BIGINT,VipResourceEnableNecessary INT,ConsumedVipSize BIGINT,Forbidden INT,OptionalChannelDataSize BLOB,OwnerProductId INT,UserData Text,UrlCodePage INT,ReferenceUrlCodePage INT,StatisticsReferenceUrlCodePage INT,GroupTaskId BIGINT,DownloadSubTask INT,TagValue INT,InnerNatReceiveSize BIGINT,AdditionFlag INT,ProductInfo NVARCHAR,Origin NVARCHAR,FreeDcdnReceiveSize BIGINT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 15:43:48.644][12480:8688][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE Category(CategoryId INT PRIMARY KEY,Name NVARCHAR,Description NVARCHAR,UserName NVARCHAR,Password NVARCHAR) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 15:43:48.644][12480:8688][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE P2spTask(TaskId BIGINT PRIMARY KEY,Cookie NVARCHAR,UseOriginResourceOnly INT,OriginResourceThreadCount INT,FileNameFixed INT,DisplayUrl NVARCHAR,UserAgent NVARCHAR) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 15:43:48.644][12480:8688][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE BtTask(TaskId BIGINT PRIMARY KEY,SeedFile NVARCHAR,InfoId NVARCHAR,DownloadFileOrderData BLOB,Tracker NVARCHAR,DisplayName NVARCHAR) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 15:43:48.644][12480:8688][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE BtFile(BtFileId INTEGER PRIMARY KEY,BtTaskId BIGINT,Status INT,FileIndex INT,Visible INT,Download INT,FilePath NVARCHAR,FileName NVARCHAR,FileSize BIGINT,FileOffset BIGINT,ReceivedSize BIGINT,Cid NVARCHAR,Gcid NVARCHAR,OptionalChannelDataSize BLOB,VideoHeadFirstTime INT,VideoHeadFirstStatus INT,FailureErrorCode INT,UserRead INT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 15:43:48.644][12480:8688][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=ALTER TABLE BtFile ADD COLUMN UserRead INT DEFAULT 0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 15:43:48.644][12480:8688][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE EmuleTask(TaskId BIGINT PRIMARY KEY,FileHash NVARCHAR,ConfigureFilePath NVARCHAR,VideoHeadFirstTime INT,VideoHeadFirstStatus INT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 15:43:48.644][12480:8688][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE GroupTask(TaskId BIGINT PRIMARY KEY,SubTaskOrder BLOB,DownloadInOrder INT,DownloadingSubTaskCount INT,SmartMoveTimeout INT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 15:43:48.644][12480:12464][info][xl::dk::DkProxyImpl::Init]: XDLInterface::Load nRet=0, appkey=xzcGMuWE1QX1A7MA^^SDK==26bfbf7a346bcf2ac776d5b7e1cb1c66 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:30)
[2025-08-12 15:43:48.779][12480:12464][info][xl::dk::DkProxyImpl::Init]: XL_Init nRet=0, appkey=xzcGMuWE1QX1A7MA^^SDK==26bfbf7a346bcf2ac776d5b7e1cb1c66 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:40)
[2025-08-12 15:43:48.779][12480:12464][info][xl::dk::CategoryManager::Load]: CategoryManager::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\CategoryManager.cpp:10)
[2025-08-12 15:43:49.043][12480:12464][info][xl::dk::TaskManager::LoadTask]: TaskManager begin load taskbase from storage (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:126)
[2025-08-12 15:43:49.047][12480:12464][info][xl::dk::TaskManager::LoadTask]: TaskManager end load taskbase from storage (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:129)
[2025-08-12 15:43:49.055][12480:12464][info][xl::dk::BtTask::Load]: BtTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:103)
[2025-08-12 15:43:49.060][12480:12464][info][xl::dk::BtTask::Load::<lambda_1>::operator ()]: BtTask::Load LoadBtFile before switch to storage thread (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:115)
[2025-08-12 15:43:49.060][12480:12464][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 15:43:49.063][12480:12464][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 15:43:49.064][12480:12464][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 15:43:49.064][12480:12464][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 15:43:49.064][12480:12464][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 15:43:49.064][12480:12464][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 15:43:49.064][12480:12464][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 15:43:49.065][12480:12464][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 15:43:49.065][12480:12464][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 15:43:49.065][12480:12464][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 15:43:49.065][12480:12464][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 15:43:49.066][12480:12464][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 15:43:49.066][12480:12464][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 15:43:49.067][12480:12464][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 15:43:49.067][12480:12464][info][xl::dk::BtTask::Load]: BtTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:103)
[2025-08-12 15:43:49.067][12480:12464][info][xl::dk::BtTask::Load::<lambda_1>::operator ()]: BtTask::Load LoadBtFile before switch to storage thread (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:115)
[2025-08-12 15:43:49.067][12480:12464][info][xl::dk::BtTask::Load]: BtTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:103)
[2025-08-12 15:43:49.067][12480:12464][info][xl::dk::BtTask::Load::<lambda_1>::operator ()]: BtTask::Load LoadBtFile before switch to storage thread (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:115)
[2025-08-12 15:43:49.067][12480:12464][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 15:43:49.068][12480:12464][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 15:43:49.068][12480:12464][info][xl::dk::TaskManager::LoadTask]: load finish, task count=12 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:231)
[2025-08-12 15:43:50.070][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:43:50.923][12480:12464][info][xl::dk::DkProxyImpl::SetCacheSize]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:1561)
[2025-08-12 15:43:50.923][12480:12464][info][xl::dk::DownloadDispatcher::SetMaxDownloadTask]: SetMaxDownloadTask nMax=5 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\DownloadDispatcher.cpp:174)
[2025-08-12 15:43:50.923][12480:12464][info][xl::dk::DkProxyImpl::UpdateNetDiscVODCachePath]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:1543)
[2025-08-12 15:43:50.923][12480:12464][info][xl::dk::DkProxyImpl::SetGlobalConnectionLimit]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:303)
[2025-08-12 15:43:50.924][12480:12464][info][xl::dk::DkProxyImpl::SetDownloadSpeedLimit]: enter nKBps=4294967295 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:142)
[2025-08-12 15:43:50.924][12480:12464][info][xl::dk::DkProxyImpl::SetDownloadSpeedLimit]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:144)
[2025-08-12 15:43:50.924][12480:12464][info][xl::dk::DkProxyImpl::SetUploadSpeedLimit]: enter nKBps=4294967295 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:192)
[2025-08-12 15:43:50.924][12480:12464][info][xl::dk::DkProxyImpl::SetUploadSpeedLimit]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:194)
[2025-08-12 15:43:51.070][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:43:52.071][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:43:53.070][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:43:54.077][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:43:55.084][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:43:56.092][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:43:57.101][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:43:58.106][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:43:59.118][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:00.125][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:01.129][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:02.138][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:03.145][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:04.149][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:05.151][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:06.151][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:07.161][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:08.175][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:09.179][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:10.193][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:11.203][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:12.216][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:13.224][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:14.239][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:15.250][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:16.251][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:17.258][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:18.267][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:19.276][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:20.282][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:21.283][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:22.291][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:23.298][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:24.306][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:25.305][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:26.318][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:27.318][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:28.333][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:29.334][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:30.346][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:31.359][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:32.366][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:33.379][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:34.393][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:35.394][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:36.396][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:37.402][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:38.415][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:39.419][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:40.433][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:41.445][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:42.458][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:43.461][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:44.467][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:45.467][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:46.482][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:47.489][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:48.502][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:49.511][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:50.512][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:51.526][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:52.540][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:53.554][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:54.568][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:55.583][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:56.592][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:57.607][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:58.617][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:44:59.631][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:00.640][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:01.650][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:02.663][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:03.666][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:04.671][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:05.673][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:06.684][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:07.695][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:08.696][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:09.699][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:10.708][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:11.721][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:12.724][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:13.730][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:14.730][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:15.744][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:16.757][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:17.763][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:18.773][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 15:45:19.782][12480:12464][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
