<script setup lang="ts">
import { onMounted, ref } from 'vue';
import SelectFileFolder, { IModelValue } from '../SelectFileFolder.vue'
import Select from '@root/common/components/ui/select/index.vue'
import { DOWNLOAD_SETTING_NAME_MAP, getSettingConfig, setSettingConfig } from '@root/modal-renderer/src/views/setting'
import CheckboxGroup, { ICheckoutGroupOptions } from '@root/common/components/ui/checkbox-group/index.vue';
import Tooltip from '@root/common/components/ui/tooltip/index.vue';

// 下载目录
const downloadPath = ref<IModelValue>({
  path: '',
  defaultPath: 'D:\\迅雷下载',
  pathName: DOWNLOAD_SETTING_NAME_MAP.DefaultPath,
  checkboxValue: [],
  checkboxOptions: [
    {
      label: '自动修改为上次使用的目录',
      name: DOWNLOAD_SETTING_NAME_MAP.UseLastCatalog,
      defaultValue: true,
      onChange: (checked: boolean, optionName: string) => {
        setSettingConfig(optionName, checked)
      },
    },
  ],
  onChange: (path: string, pathName: string) => {
    setSettingConfig(pathName, path)
  },
})

// 原始地址线程数
const orignHostThreads = ref('5')
const orignHostThreadsOptions = ref(Array.from({ length: 10 }, (_, i) => ({ name: `${i + 1}`, value: `${i + 1}` })))

// 限制全局最大同时下载资源数
const selectedLimitDownloadMaxNum = ref<string[]>([])
const limitDownloadMaxNumOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '限制全局最大同时下载资源数',
    name: DOWNLOAD_SETTING_NAME_MAP.MaxResourceLimit,
    tip: '如果您在使用迅雷下载时，发现网络响应变慢或掉线等情况。这可能是由于您的路由器性能难以支撑较大的同时下载资源数。您可以尝试降低此项数值，从而降低路由器的负载。建议您在保持网络稳定的前提下，让此项数值尽可能的大',
    action: {
      type: 'input',
      width: 104,
      value: '500',
      name: DOWNLOAD_SETTING_NAME_MAP.MaxResourceCount,
      onAction: (value: string, optionName: string) => {
        setSettingConfig(optionName, value)
      }
    },
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    defaultValue: false,
    errorText: '' // 添加错误文本属性
  }
])

// 启用“离开模式”
const selectedLeaveMode = ref<string[]>([])
const leaveModeOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '启用“离开模式”',
    name: DOWNLOAD_SETTING_NAME_MAP.AwayModeEnabled,
    defaultValue: false,
    tip: '启用“离开模式”后，在电脑处于“睡眠”状态时，迅雷仍然能帮您继续下载资源',
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  }
])

const initDefaultValue = async () => {
  orignHostThreads.value = await getSettingConfig(DOWNLOAD_SETTING_NAME_MAP.OrignHostThreads, '5') as string

  const leaveModeValue = await Promise.all(leaveModeOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectedLeaveMode.value = leaveModeValue

  const limitDownloadMaxNumValue = await Promise.all(limitDownloadMaxNumOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)

    if (option.action && option.name === DOWNLOAD_SETTING_NAME_MAP.MaxResourceLimit) {
      const value = await getSettingConfig(option.action?.name!, option.action?.value)
      option.action.value = value as string
    }

    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectedLimitDownloadMaxNum.value = limitDownloadMaxNumValue
}

const handleOrignHostThreadsSelect = (value: string) => {
  orignHostThreads.value = value
  setSettingConfig(DOWNLOAD_SETTING_NAME_MAP.OrignHostThreads, value)
}

onMounted(() => {
  initDefaultValue()
})
</script>

<template>
  <div class="settings-content-title">
    目录
  </div>

  <SelectFileFolder label="下载目录" v-model="downloadPath" select-type="local" />

  <div class="settings-content-divider"></div>

  <div class="settings-content-title">
    下载控制
  </div>

  <div class="download-setting">
    <div class="download-setting-left">
      <div class="download-setting-left-label">原始地址线程数</div>
      <Select v-model="orignHostThreads" :options="orignHostThreadsOptions" anchor-class="download-setting-select"
        content-class="download-setting-select-content" :max-height="160" @on-select="handleOrignHostThreadsSelect" />

      <Tooltip :max-width="200">
        <template #trigger>
          <i class="xl-icon-tips-question-circle-l"></i>
        </template>
        <template #content>
          指单个任务下载时，向原始下载地址建立的连接数。理论上连接数越多，速度越快。但过多会对资源提供商造成压力，且服务商自身会对连接数有限制，所以建议保持在建议值5。
        </template>
      </Tooltip>
    </div>
    <div class="download-setting-right">
      <CheckboxGroup :options="limitDownloadMaxNumOptions" v-model="selectedLimitDownloadMaxNum"
        :default-value="selectedLimitDownloadMaxNum" />
    </div>
  </div>
  <div class="settings-content-divider"></div>
  <CheckboxGroup title="其它" :options="leaveModeOptions" orientation="vertical" v-model="selectedLeaveMode" />
  <div class="settings-content-divider"></div>
</template>

<style scoped lang="scss">
.download-setting {
  display: flex;
  margin-top: 6px;
  justify-content: space-between;
  height: 32px;
  align-items: center;

  &-left,
  &-right {
    display: flex;
    gap: 8px;
    align-items: center;

    &-label {
      color: var(--font-font-2, #4E5769);
      font-size: 13px;
      line-height: 22px;
    }

    i {
      color: var(--font-font-3, #86909C);
    }
  }
}
</style>

<style lang="scss">
.download-setting-select {
  width: 74px !important;
  height: 32px !important;
  gap: 4px !important;


  input {
    width: 100%;
    color: var(--font-font-2, #4E5769);
    font-size: 13px;
  }

  i {
    color: var(--font-font-3, #898E97);
  }
}

.download-setting-select-content {
  width: 72px !important;
}

.download-setting-input-container {
  width: 104px !important;
  height: 32px !important;

  input {
    font-size: 13px !important;
    line-height: 22px !important;
    width: 100%;
  }
}

.download-setting-checkbox-label {
  color: var(--font-font-2, #4E5769);
  display: flex;
  align-items: center;
  gap: 4px;

  i {
    color: var(--font-font-3, #86909C);
  }
}
</style>
