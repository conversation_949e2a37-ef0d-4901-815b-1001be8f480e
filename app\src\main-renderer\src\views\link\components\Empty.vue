<script setup lang="ts">
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'
import { useUserStore } from '@/stores/user'
import { computed } from 'vue'

const userStore = useUserStore()


const props = defineProps<{
  type: 'linkList' | 'linkDetail'
}>()

const isLoggedIn = computed(() => {
  return !!userStore?.getUid
})

const handleAddLink = () => {
  ThunderNewTaskHelperNS.showNewTaskWindow();
}

</script>

<template>
  <div class="link-empty-container">
    <div class="link-empty-icon">
      <inline-svg
        :src="type === 'linkList' ? require('@root/common/assets/img/ic_link_empty.svg') : require('@root/common/assets/img/ic_link_detail_empty.svg')" />
    </div>
    <span class="link-empty-title">使用迅雷，从添加链接开始</span>
    <span class="link-empty-desc">
      {{ isLoggedIn ? '在这里可以找到你账号下所有设备最近使用过的链接' : '在这里查看你最近使用过的链接，登录后可同步其他设备最近使用链接' }}
    </span>
    <Button variant="primary" @click="handleAddLink">添加链接</Button>
  </div>
</template>

<style lang="scss" scoped>
.link-empty {
  &-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    width: 100%;
    padding-top: 131px;
  }

  &-icon {
    width: 140px;
    height: 140px;
  }

  &-title {
    margin-top: 18px;
    font-size: 13px;
    line-height: 22px;
    color: var(--font-font-1, #272E3B);
  }

  &-desc {
    margin-top: 8px;
    font-size: 12px;
    line-height: 20px;
    color: var(--font-font-3, #86909C);
    margin-bottom: 18px;
  }


}
</style>
