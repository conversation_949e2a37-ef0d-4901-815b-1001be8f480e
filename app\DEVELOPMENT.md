# 开发模式使用说明

## 问题背景

在开发过程中，当你修改了 `src/modal-renderer/src/views/create-task/create.vue` 等文件后，发现启动的客户端应用文案没有更新。这是因为应用运行在生产模式下，加载的是已打包的静态文件，而不是开发服务器。

## 解决方案

### 方法1：使用开发模式启动（推荐）

#### 使用批处理文件启动：
```bash
npm run dev:start
```

#### 手动启动：
1. 先启动modal-renderer开发服务器：
   ```bash
   npm run dev:modal
   ```
2. 等待开发服务器启动（端口9529）
3. 然后启动主应用：
   ```bash
   npm start
   ```

### 方法1.5：快速开发模式（简化版）
如果开发模式启动失败，可以使用快速开发模式：

```bash
npm run quick:dev
```

这个命令会：
1. 只构建modal-renderer
2. 重新构建整个应用
3. 启动应用

注意：每次修改create.vue后都需要重新运行这个命令。

### 方法1.6：简化开发模式（推荐，忽略TypeScript错误）
如果遇到TypeScript错误，可以使用简化开发模式：

```bash
npm run dev:simple
```

这个命令会：
1. 只构建modal-renderer（忽略TypeScript错误）
2. 启动应用

**优点**：快速、可靠、忽略TypeScript错误

### 方法2：强制重新构建

如果必须使用生产模式，可以强制重新构建：

```bash
# 清理构建缓存
npm run clean

# 重新构建
npm run build

# 启动应用
npm start
```

## 开发模式 vs 生产模式

### 开发模式特点：
- modal-renderer 通过 `http://localhost:9529` 加载
- 支持热重载，修改代码后立即生效
- 适合日常开发调试

### 生产模式特点：
- modal-renderer 通过 `file:///${__dirname}/modal-renderer/index.html` 加载
- 加载已打包的静态文件
- 需要重新构建才能看到更改

## 验证开发模式是否正常工作

1. **检查开发服务器**：
   - 确保9529端口有服务运行
   - 访问 `http://localhost:9529` 查看内容

2. **检查应用加载**：
   - 在Electron应用中按F12打开开发者工具
   - 查看Network标签页，确认加载的是localhost而不是本地文件

3. **测试热重载**：
   - 修改 `create.vue` 文件中的文案
   - 保存文件，查看是否立即生效

## 使用方法

### 启动开发模式：
```bash
npm run dev:start
```

### 测试开发模式：
```bash
npm run test:dev
```

### 仅启动modal-renderer开发服务器：
```bash
npm run dev:modal
```

## 注意事项

1. 开发模式需要先启动modal-renderer开发服务器
2. 确保9529端口没有被其他程序占用
3. 如果开发服务器启动失败，检查modal-renderer的依赖是否正确安装
4. 开发模式下，某些功能可能需要额外的配置

## 故障排除

### 问题1：开发服务器启动失败
```bash
# 检查modal-renderer依赖
cd src/modal-renderer
npm install
npm run dev
```

### 问题2：端口9529被占用
```bash
# 查找占用端口的进程
netstat -ano | findstr :9529
# 结束进程
taskkill /PID <进程ID> /F
```

### 问题3：应用仍然加载旧内容
- 检查是否真的在开发模式下运行
- 确认开发服务器正在运行
- 清除浏览器缓存

### 问题4：TypeScript构建错误
如果遇到TypeScript错误，可以：
1. 使用开发模式启动脚本（会自动处理依赖问题）
2. 或者手动清理并重新安装依赖

### 问题5：PowerShell执行策略错误
如果遇到以下错误：
```
无法加载文件 C:\nvm4w\nodejs\npm.ps1，因为在此系统上禁止运行脚本
```

解决方案：
1. 运行修复脚本：
   ```bash
   scripts\fix-powershell.bat
   ```

2. 或者手动修复：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
   ```

3. 或者使用批处理文件而不是npm命令：
   ```bash
   scripts\dev-simple.bat
   ```

## 推荐工作流程

1. **开发阶段**：
   ```bash
   npm run dev:start
   ```

2. **测试阶段**：
   ```bash
   npm run test:dev
   ```

3. **生产构建**：
   ```bash
   npm run build:start
   ```

这样，当你修改代码后可以立即看到效果，而不需要重新构建整个应用。 