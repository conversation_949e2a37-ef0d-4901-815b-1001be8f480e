<template>
  <div 
    :class="['search-bookmark-item', { focused }]"
    @click="handleSelect"
    @mouseenter="$emit('mouseenter')"
  >
    <div class="bookmark-icon">
      <i class="xl-icon-bookmark"></i>
    </div>
    <div class="bookmark-content">
      <div class="bookmark-title">
        <span v-html="highlightText(bookmark.title)"></span>
      </div>
      <div class="bookmark-url" v-if="bookmark.url">
        <span v-html="highlightText(bookmark.url)"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { shell } from 'electron'

interface BookmarkItem {
  id: string
  title: string
  url: string
  icon?: string
  time?: string
  type: string
}

const props = defineProps<{
  bookmark: BookmarkItem
  focused: boolean
  searchText?: string
}>()

const emit = defineEmits<{
  select: [bookmark: BookmarkItem]
  mouseenter: []
}>()

const handleSelect = () => {
  // 打开系统浏览器访问URL
  if (props.bookmark.url) {
    shell.openExternal(props.bookmark.url)
  }
  emit('select', props.bookmark)
}

// 高亮搜索文本
const highlightText = (text: string) => {
  if (!props.searchText || !text) return text
  
  const searchRegex = new RegExp(`(${props.searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(searchRegex, '<span class="highlight">$1</span>')
}
</script>

<style lang="scss" scoped>
.search-bookmark-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: var(--border-radius-M2, 10px);
  cursor: pointer;
  transition: background-color 0.2s ease;
  height: 68px;
  
  &:hover,
  &.focused {
    background: var(--background-bg-2, #F2F3F5);
  }
}

.bookmark-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  
  i {
    font-size: 16px;
    color: var(--font-font-3, #86909C);
  }
}

.bookmark-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.bookmark-title {
  font-size: 13px;
  color: var(--font-font-1, #1D2129);
  line-height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bookmark-url {
  font-size: 12px;
  color: var(--font-font-3, #86909C);
  line-height: 18px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.highlight) {
  color: var(--primary-primary-default, #226DF5);
  font-weight: 500;
}
</style> 