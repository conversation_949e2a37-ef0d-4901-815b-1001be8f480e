import { IPC_API_NAME, IPC_REMOTE_ID } from './common/ipc-define'
import { API_EVENT, API_FILE, API_SHARE, IRequestCommonResponseT, IRequestHeader, PanSDKManager } from './pan-sdk'
import { EFileSpaceFolderType, ICreateFolderParams, IFileApisGetFilesOptions, IGetFileAncestorsRequest, IGetFileInfoParams, IGetFilesParams, IGetFolderFilesParams } from './pan-sdk/services/file'

import { env, isShowLogger } from '../env'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { getPubKeys } from './common/pub-keys'
import { getXbaseConfig } from './manager/application'
import { IDriveApisCommonOptions, IGetAboutParams, IGetEventsParams, IGetUrlTasksParams, IGetUrlTaskStatusParams, TPrivilegeString } from './pan-sdk/services/drive'
import { IGetRestoreListParams, IGetShareListParams, IShareApisCommonOptions } from './pan-sdk/services/share'
import HttpImplement from './pan-sdk/utils/http'
import { ICheckSafeHasInitParams, ICheckVerificationCodeParams, IPasswordParams, IResetPasswordParams, ISafeApisCommonOptions, ISendVerificationCodeParams } from './pan-sdk/services/safe'

const logger = (...args) => {
  const now = new Date()
  if (isShowLogger) {
    console.log(`[${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}.${now.getMilliseconds()}][ThunderPanServerSDK]`, ...args)
  }
}

export interface IThunderPanServerSDKCtorParams {
  appVersion: string
  appVersionCode: string
  deviceId: string
  userId: () => Promise<string>
  md5: (str: string) => string
  request: (options: any) => any
}

class ThunderPanServerSDK {
  ServiceApiManager!: PanSDKManager

  /**
   * 获取 server 端实例，config 在首次调用一定要传
   * @param config 配置数据
   * @returns
   */
  static getInstance(): ThunderPanServerSDK {
    if (global.__thunder_pan_server_instance__) return global.__thunder_pan_server_instance__
    global.__thunder_pan_server_instance__ = new ThunderPanServerSDK()
    return global.__thunder_pan_server_instance__
  }

  constructor() {
    this._registerRemoteFunction()
  }

  public init(config: IThunderPanServerSDKCtorParams) {
    this.ServiceApiManager = new PanSDKManager(
      new HttpImplement({
        appVersion: config.appVersion,
        appVersionCode: config.appVersionCode,
        deviceId: config.deviceId,
        userId: config.userId,
        xbaseSDKConfig: getXbaseConfig(),
        pubKeys: getPubKeys(),
        md5Fn: config.md5,
        request: config.request
      }),
      {
        env: env,
      }
    )
  }

  private _commonResponse(res: IRequestCommonResponseT<any>) {
    if (res.success) {
      return res.data
    } else {
      throw res.error || res.originResponse
    }
  }

  private _registerRemoteFunction() {
    // 注册云盘插件的IPC别名
    client.start({ name: IPC_REMOTE_ID })

    client.registerFunctions({
      [IPC_API_NAME.GET_FILE_INFO]: (ctx: unknown, fileId: string, options: IFileApisGetFilesOptions<IGetFileInfoParams> = {}) => {
        logger(IPC_API_NAME.GET_FILE_INFO, fileId, options)
        return this.ServiceApiManager.getFileApisForServices().getFileInfo(fileId, options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_FOLDER_LIST]: async (ctx: unknown, parentId: string, space: string, page_token?: string, options: IFileApisGetFilesOptions<IGetFilesParams> = {}) => {
        logger(IPC_API_NAME.GET_FOLDER_LIST, parentId, space)
        return this.ServiceApiManager.getFileApisForServices().getAllFolderByParentId(parentId, space, page_token, options).then(this._commonResponse);;
      },
      [IPC_API_NAME.GET_ALL_FILE_LIST]: async (ctx: unknown, parentId: string, space: string, page_token: string, options: IFileApisGetFilesOptions<IGetFilesParams> = {}) => {
        logger(IPC_API_NAME.GET_ALL_FILE_LIST, parentId, space)
        return this.ServiceApiManager.getFileApisForServices().getAllFilesByParentId(parentId, space, page_token, options).then(this._commonResponse);;
      },
      [IPC_API_NAME.GET_ALL_FLATTEN_SUB_FILES]: async (ctx: unknown, parentId: string, space: string, page_token: string, options: IFileApisGetFilesOptions<IGetFolderFilesParams> = {}) => {
        logger(IPC_API_NAME.GET_ALL_FLATTEN_SUB_FILES, parentId, space)
        return this.ServiceApiManager.getFileApisForServices().getAllFlattenSubFilesByParentId(parentId, space, page_token, options).then(this._commonResponse);;
      },
      [IPC_API_NAME.GET_EVENTS]: async (ctx: unknown, options: IDriveApisCommonOptions<IGetEventsParams> = {}) => {
        logger(IPC_API_NAME.GET_EVENTS, options)
        return this.ServiceApiManager.getDriveApisForServices().getEvents(options).then(this._commonResponse)
      },
      [IPC_API_NAME.REPORT_EVENT]: async (ctx: unknown, options: IDriveApisCommonOptions<API_EVENT.DriveReportEventRequest> = {}) => {
        logger(IPC_API_NAME.REPORT_EVENT, options)
        return this.ServiceApiManager.getDriveApisForServices().reportEvents(options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_FILES]: (ctx: unknown, options: IFileApisGetFilesOptions<IGetFileInfoParams> = {}) => {
        logger(IPC_API_NAME.GET_FILES, options)
        return this.ServiceApiManager.getFileApisForServices().getFiles(options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_SHARE_LIST]: (ctx: unknown, options: IShareApisCommonOptions<IGetShareListParams> = {}) => {
        logger(IPC_API_NAME.GET_SHARE_LIST, options)
        return this.ServiceApiManager.getShareApisForServices().getShareList(options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_TRASH_LIST]: (ctx: unknown, page_token: string = '', options: IFileApisGetFilesOptions<IGetFilesParams> = {}) => {
        logger(IPC_API_NAME.GET_TRASH_LIST, page_token)
        return this.ServiceApiManager.getFileApisForServices().getAllTrashFiles(page_token, options).then(this._commonResponse)
      },
      [IPC_API_NAME.CLEAN_TRASH]: (ctx: unknown) => {
        logger(IPC_API_NAME.CLEAN_TRASH)
        return this.ServiceApiManager.getFileApisForServices().cleanTrash().then(this._commonResponse)
      },
      [IPC_API_NAME.GET_ABOUT]: (ctx: unknown, options: IDriveApisCommonOptions<IGetAboutParams> = {}) => {
        logger(IPC_API_NAME.GET_ABOUT, options)
        return this.ServiceApiManager.getDriveApisForServices().getAbout(options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_PRIVILEGE]: (ctx: unknown, privilege: TPrivilegeString, headers?: IRequestHeader) => {
        logger(IPC_API_NAME.GET_PRIVILEGE, privilege, headers)
        return this.ServiceApiManager.getDriveApisForServices().getPrivilege(privilege, headers).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_FLOW_ABOUT]: (ctx: unknown, headers?: IRequestHeader) => {
        logger(IPC_API_NAME.GET_FLOW_ABOUT, headers)
        return this.ServiceApiManager.getDriveApisForServices().getFlowAbout(headers).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_URL_TASK_LIST]: (ctx: unknown, options?: IDriveApisCommonOptions<IGetUrlTasksParams>) => {
        logger(IPC_API_NAME.GET_URL_TASK_LIST, options)
        return this.ServiceApiManager.getDriveApisForServices().getUrlTaskList(options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_URL_TASK_BY_ID]: (ctx: unknown, taskId: string, options: IDriveApisCommonOptions<null>) => {
        logger(IPC_API_NAME.GET_URL_TASK_BY_ID, taskId, options)
        return this.ServiceApiManager.getDriveApisForServices().getUrlTaskById(taskId, options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_URL_TASK_CHILDREN]: (ctx: unknown, taskId: string, options?: IDriveApisCommonOptions<IGetUrlTaskStatusParams>) => {
        logger(IPC_API_NAME.GET_URL_TASK_CHILDREN, taskId, options)
        return this.ServiceApiManager.getDriveApisForServices().getUrlTaskStatusById(taskId, options).then(this._commonResponse)
      },
      [IPC_API_NAME.BATCH_DELETE_URL_TASKS]: (ctx: unknown, taskIds: string[], options?: IDriveApisCommonOptions<null>) => {
        logger(IPC_API_NAME.BATCH_DELETE_URL_TASKS, taskIds, options)
        return this.ServiceApiManager.getDriveApisForServices().batchDeleteUrlTasks(taskIds, options).then(this._commonResponse)
      },
      [IPC_API_NAME.BATCH_GET_TASKS_STATUSES]: (ctx: unknown, taskIds: string[], options: IDriveApisCommonOptions<null> = {}) => {
        logger(IPC_API_NAME.BATCH_GET_TASKS_STATUSES, taskIds, options)
        return this.ServiceApiManager.getDriveApisForServices().batchGetUrlTaskStatuses(taskIds, options).then(this._commonResponse)
      },
      [IPC_API_NAME.BATCH_TRASH_FILES]: (ctx: unknown, options: IFileApisGetFilesOptions<API_FILE.DriveBatchTrashFilesRequest>) => {
        logger(IPC_API_NAME.BATCH_TRASH_FILES, options)
        return this.ServiceApiManager.getFileApisForServices().batchTrashFiles(options).then(this._commonResponse)
      },
      [IPC_API_NAME.BATCH_UNTRASH_FILES]: (ctx: unknown, options: IFileApisGetFilesOptions<API_FILE.DriveBatchUntrashFilesRequest>) => {
        logger(IPC_API_NAME.BATCH_UNTRASH_FILES, options)
        return this.ServiceApiManager.getFileApisForServices().batchUnTrash(options).then(this._commonResponse)
      },
      [IPC_API_NAME.BATCH_DELETE_FILES]: (ctx: unknown, options: IFileApisGetFilesOptions<API_FILE.DriveBatchDeleteFilesRequest>) => {
        logger(IPC_API_NAME.BATCH_DELETE_FILES, options)
        return this.ServiceApiManager.getFileApisForServices().batchDeleteFiles(options).then(this._commonResponse)
      },
      [IPC_API_NAME.CREATE_FOLDER]: (ctx: unknown, parentId: string, name: string, options?: IFileApisGetFilesOptions<ICreateFolderParams>) => {
        logger(IPC_API_NAME.CREATE_FOLDER, options)
        return this.ServiceApiManager.getFileApisForServices().createFolder(parentId, name, options).then(this._commonResponse)
      },
      [IPC_API_NAME.BATCH_COPY_FILES]: (ctx: unknown, options: IFileApisGetFilesOptions<API_FILE.DriveBatchCopyFilesRequest>) => {
        logger(IPC_API_NAME.BATCH_COPY_FILES, options)
        return this.ServiceApiManager.getFileApisForServices().batchCopyFiles(options).then(this._commonResponse)
      },
      [IPC_API_NAME.BATCH_MOVE_FILES]: (ctx: unknown, options: IFileApisGetFilesOptions<API_FILE.DriveBatchMoveFilesRequest>) => {
        logger(IPC_API_NAME.BATCH_MOVE_FILES, options)
        return this.ServiceApiManager.getFileApisForServices().batchMoveFiles(options).then(this._commonResponse)
      },
      [IPC_API_NAME.BATCH_UPDATE_FILES]: (ctx: unknown, options: IFileApisGetFilesOptions<API_FILE.DriveBatchUpdateFilesRequest>) => {
        logger(IPC_API_NAME.BATCH_UPDATE_FILES, options)
        return this.ServiceApiManager.getFileApisForServices().batchUpdate(options).then(this._commonResponse)
      },
      [IPC_API_NAME.BATCH_DELETE_SHARE]: (ctx: unknown, options: IShareApisCommonOptions<API_SHARE.DriveBatchDeleteSharesRequest>) => {
        logger(IPC_API_NAME.BATCH_DELETE_SHARE, options)
        return this.ServiceApiManager.getShareApisForServices().batchDeleteShare(options).then(this._commonResponse)
      },
      [IPC_API_NAME.CREATE_SHARE]: (ctx: unknown, options: IShareApisCommonOptions<API_SHARE.DriveCreateShareRequest>) => {
        logger(IPC_API_NAME.CREATE_SHARE, options)
        return this.ServiceApiManager.getShareApisForServices().createShare(options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_ALL_CATEGORY_FILE_COUNT]: (ctx: unknown, options: IFileApisGetFilesOptions<null>) => {
        logger(IPC_API_NAME.GET_ALL_CATEGORY_FILE_COUNT, options)
        return this.ServiceApiManager.getFileApisForServices().getAllCategoryFileCount(options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_File_ANCESTORS]: (ctx: unknown, fileId: string, options: IFileApisGetFilesOptions<IGetFileAncestorsRequest>) => {
        logger(IPC_API_NAME.GET_File_ANCESTORS, fileId, options)
        return this.ServiceApiManager.getFileApisForServices().getFileAncestors(fileId, options).then(this._commonResponse)
      },
      [IPC_API_NAME.CREATE_FILE]: (ctx: unknown, options: IFileApisGetFilesOptions<API_FILE.DriveCreateFileRequest>) => {
        logger(IPC_API_NAME.CREATE_FILE, options)
        return this.ServiceApiManager.getFileApisForServices().createFile(options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_FILE_BY_SPACE]: (ctx: unknown, space: string, folder_type: EFileSpaceFolderType, options: IFileApisGetFilesOptions<null>) => {
        logger(IPC_API_NAME.GET_FILE_BY_SPACE, space, folder_type, options)
        return this.ServiceApiManager.getFileApisForServices().getFileBySpace(space, folder_type, options).then(this._commonResponse)
      },
      [IPC_API_NAME.GET_RESTORE_LIST]: (ctx: unknown, options: IShareApisCommonOptions<IGetRestoreListParams>) => {
        logger(IPC_API_NAME.GET_RESTORE_LIST, options)
        return this.ServiceApiManager.getShareApisForServices().getRestoreList(options).then(this._commonResponse)
      },
      [IPC_API_NAME.DELETE_RESTORE_BY_ID]: (ctx: unknown, id: string, options: IShareApisCommonOptions<API_SHARE.DriveRestoreDeleteRequest>) => {
        logger(IPC_API_NAME.DELETE_RESTORE_BY_ID, id, options)
        return this.ServiceApiManager.getShareApisForServices().deleteRestoreById(id, options).then(this._commonResponse)
      },
      [IPC_API_NAME.SAFE_BOX_CHECK_HAS_INIT]: (ctx: unknown, options: ISafeApisCommonOptions<ICheckSafeHasInitParams>) => {
        logger(IPC_API_NAME.SAFE_BOX_CHECK_HAS_INIT, options)
        return this.ServiceApiManager.getSafeApisForServices().checkSafeHasInit(options).then(this._commonResponse)
      },
      [IPC_API_NAME.SAFE_BOX_CHECK_PASSWORD]: (ctx: unknown, options: ISafeApisCommonOptions<IPasswordParams>) => {
        logger(IPC_API_NAME.SAFE_BOX_CHECK_PASSWORD, options)
        return this.ServiceApiManager.getSafeApisForServices().checkPassword(options).then(this._commonResponse)
      },
      [IPC_API_NAME.SAFE_BOX_INIT_PASSWORD]: (ctx: unknown, options: ISafeApisCommonOptions<IPasswordParams>) => {
        logger(IPC_API_NAME.SAFE_BOX_INIT_PASSWORD, options)
        return this.ServiceApiManager.getSafeApisForServices().initPassword(options).then(this._commonResponse)
      },
      [IPC_API_NAME.SAFE_BOX_RESET_PASSWORD]: (ctx: unknown, options: ISafeApisCommonOptions<IResetPasswordParams>) => {
        logger(IPC_API_NAME.SAFE_BOX_RESET_PASSWORD, options)
        return this.ServiceApiManager.getSafeApisForServices().resetPassword(options).then(this._commonResponse)
      },
      [IPC_API_NAME.SAFE_BOX_SEND_VERIFICATION_CODE]: (ctx: unknown, options: ISafeApisCommonOptions<ISendVerificationCodeParams>) => {
        logger(IPC_API_NAME.SAFE_BOX_SEND_VERIFICATION_CODE, options)
        return this.ServiceApiManager.getSafeApisForServices().sendVerificationCode(options).then(this._commonResponse)
      },
      [IPC_API_NAME.SAFE_BOX_CHECK_VERIFICATION_CODE]: (ctx: unknown, options: ISafeApisCommonOptions<ICheckVerificationCodeParams>) => {
        logger(IPC_API_NAME.SAFE_BOX_CHECK_VERIFICATION_CODE, options)
        return this.ServiceApiManager.getSafeApisForServices().checkVerificationCode(options).then(this._commonResponse)
      },
      [IPC_API_NAME.SERVER_SEARCH_FILE_TREE_PATH_BY_ID]: async (ctx: unknown, id: string) => {
        logger(IPC_API_NAME.SERVER_SEARCH_FILE_TREE_PATH_BY_ID, id)
        let currentId = id
        const fileList: API_FILE.DriveFile[] = []
        do {
          const fileInfo = await this.ServiceApiManager.getFileApisForServices().getFileInfo(currentId).then(this._commonResponse)
          if (!fileInfo) break
          currentId = fileInfo.parent_id
          fileList.unshift(fileInfo)
          if (!fileInfo.parent_id) break
        } while (currentId)
        logger(IPC_API_NAME.SERVER_SEARCH_FILE_TREE_PATH_BY_ID, 2, fileList)
        return fileList
      },
    })
    logger('register functions done.')
  }
}

export { ThunderPanServerSDK }

