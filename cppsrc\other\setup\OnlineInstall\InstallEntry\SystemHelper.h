#pragma once
#include <vector>

class CSystemHelper
{
public:
	typedef struct _TOKEN_MANDATORY_LABEL {
		SID_AND_ATTRIBUTES Label;
	} TOKEN_MANDATORY_LABEL, *PTOKEN_MANDATORY_LABEL;

	#define SE_GROUP_INTEGRITY                 (0x00000020L)
	#define TokenIntegrityLevel					25

public:
	static HRESULT ExcuteExe(LPCTSTR lpszExeFilePath, LPCTSTR lpszParam, LPCTSTR lpszWorkDirectory = _T(""), BOOL bRequireAmdin = FALSE, HANDLE* phProcess = NULL);
	static HRESULT StartAsUser(LPCTSTR lpszExeFilePath, LPCTSTR lpszParam);

	static BOOL IsVista();
	static BOOL IsWin7();
	static BOOL IsVistaOrGreater();
	static BOOL IsUacOn();

	//static HRESULT OpenLowIntegrityMemoryMap(LPCTSTR lpszName, DWORD dwSize, <PERSON>O<PERSON> bClear, CAtlFileMapping<BYTE>& fileMemMap);

	struct ProcessInfo
	{
		ProcessInfo():hProcess(NULL)
		{}
		std::wstring strProcessName;
		HANDLE hProcess;
	};
	typedef std::vector<ProcessInfo> ProcessInfoList;
	typedef ProcessInfoList::iterator ProcessInfoListIte;
	static HRESULT FillProcessInfo(ProcessInfoList& listProcessInfo);

	static BOOL GetParentProcessId(DWORD& dwParentProcessId);

	static HRESULT EnableDebugPrivileges(HANDLE hProcess);

	static BOOL IsProcessExists(LPCTSTR lpszProcessName);
	static HRESULT KillProcess(LPCTSTR lpszProcessName);

	static HRESULT IsMutexExists(LPCTSTR lpszMutexName, BOOL& bOpen);
	static HRESULT IsWindowExists(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, BOOL& bOpen);

};
