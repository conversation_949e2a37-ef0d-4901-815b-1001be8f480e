import * as DownloadKernel from '../base';
import { DkThunderHelper } from './dk-thunder-helper';

export namespace URLHelperNS {

  /**
   * 判断是否为种子码
   */
  export function isMagnetCode(code: string): boolean {
    let ret: boolean = true;
    if (typeof code === 'string') {
      if (code.length === 40 || code.length === 32) {
        // 都是数字或者字母
        for (let i: number = 0; i < code.length; i++) {
          if (code.charCodeAt(i) > 127) {
            ret = false;
            break;
          }
        }
      } else {
        ret = false;
      }
    } else {
      ret = false;
    }
    return ret;
  }

  /**
   * 补齐种子码链接（添加 magnet 协议头）
   */
  export async function fixMagnetUrl(url: string) {
    let ret: string = '';
    do {
      if (typeof url !== 'string' || url === '') {
        break;
      }
      ret = url.trim();
      if (ret === '') {
        break;
      }
      const urlType: DownloadKernel.TaskType = await DkThunderHelper.getTaskTypeFromUrl(ret);
      if (urlType === DownloadKernel.TaskType.Unkown) {
        const bMagnetCode: boolean = isMagnetCode(ret);
        if (bMagnetCode) {
          ret = 'magnet:?xt=urn:btih:' + ret;
        }
      }
    } while (0);
    return ret;
  }


  /**
   * 简单判断url是否合法;不包含thunder、磁力链等
   * @param url 链接
   * @param fixMagnet 是否需要补齐种子码
   * @returns
   */
  export async function isUrlValid(url: string, fixMagnet: boolean = true) {
    fixMagnet = fixMagnet === undefined ? true : fixMagnet;
    let ret: boolean = false;
    do {
      if (typeof url !== 'string' || url === '') {
        break;
      }
      const urlTrim: string = url.trim();
      if (urlTrim === '') {
        break;
      }
      const urlType: DownloadKernel.TaskType = await DkThunderHelper.getTaskTypeFromUrl(urlTrim);
      if (urlType === DownloadKernel.TaskType.Unkown) {
        if (!fixMagnet) {
          break;
        }
        const bMagnetCode: boolean = isMagnetCode(urlTrim);
        if (bMagnetCode) {
          // 添加磁力链的前缀
          // let magnetUrl = 'magnet:?xt=urn:btih:' + urlTrim;
          ret = true;
        }
      } else {
        ret = true;
      }

      if (!ret) {
        if (urlTrim.match(/^file:\/\/\//)) {
          ret = true;
        }
      }
    } while (0);
    return ret;
  }

  /**
   * @description 判断是否是迅雷口令
   * @param content 待判断的字符串
   * @returns 返回true为迅雷口令,false非迅雷口令
   */
  export function isBirdKey(content: string): RegExpMatchArray | null {
    // 迅雷口令
    if (!content) {
      return null;
    }
    return content.match(/#[Xx]([0-9a-zA-Z])+#/g);
  }

  /**
   * @description 判断是否是云盘取回
   * @param content 待判断的字符串
   * @returns 返回true为迅雷云盘取回任务,false非云盘取回任务
   */
  export function isYunFetchBackTask(content: string): RegExpMatchArray | null {
    // 迅雷口令
    if (!content) {
      return null;
    }
    return content.match(/#\$ThunderPanFetchBack:([\s\S]+)\$#/);
  }

  export async function isP2spOrEmuleUrl(url: string) {
    let bP2sp: boolean = false;
    do {
      if (url === null || url === undefined || url === '') {
        break;
      }
      const urlTrim: string = url.trim();
      if (urlTrim === '') {
        break;
      }
      const urlType: DownloadKernel.TaskType = await ThunderHelper.getTaskTypeFromUrl(urlTrim);
      if (urlType === DownloadKernel.TaskType.P2sp || urlType === DownloadKernel.TaskType.Emule) {
        bP2sp = true;
        break;
      }
    } while (0);
    return bP2sp;
  }

  /**
   * @description 是否是支持的 URL 格式
   */
  export function isSupportUrl(url: string): boolean {
    url = url.toLowerCase();
    let ret: boolean = false;
    const httpScheme: string = 'http://';
    const httpsScheme: string = 'https://';
    const ftpScheme: string = 'ftp://';
    const ed2kScheme: string = 'ed2k://';
    const thunderScheme: string = 'thunder://';
    const magnetScheme: string = 'magnet:?';
    do {
      if (url.substr(0, httpScheme.length) === httpScheme) {
        ret = true;
        break;
      }
      if (url.substr(0, httpsScheme.length) === httpsScheme) {
        ret = true;
        break;
      }
      if (url.substr(0, ftpScheme.length) === ftpScheme) {
        ret = true;
        break;
      }
      if (url.substr(0, ed2kScheme.length) === ed2kScheme) {
        ret = true;
        break;
      }
      if (url.substr(0, thunderScheme.length) === thunderScheme) {
        ret = true;
        break;
      }
      if (url.substr(0, magnetScheme.length) === magnetScheme) {
        ret = true;
        break;
      }
    } while (0);
    return ret;
  }
}