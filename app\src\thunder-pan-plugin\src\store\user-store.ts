import { createGlobalState } from "@vueuse/core";
import { computed, reactive } from "vue";

export interface IUserStoreState {
  curUser: IUserInfo & IVipInfo
}

export interface IUserInfo {
  username?: string;
  usernewno?: string;
  userid?: string;
  userId?: string;
  sessionid?: string;
  nickName?: string;
  account?: string;
  rank?: string;
  order?: string;
  isSubAccount?: string;
  country?: string;
  province?: string;
  city?: string;
  birthday?: string;
  sex?: string;
  mobile?: string;
  isSpecialNum?: number;
  role?: number;
  todayScore?: string;
  personalSign?: string;
}

export enum VipType {
  normal = '0',
  normalVip= '2',
  platinum = '3',
  superv = '5'
}

export interface IVipInfo {
  vasid?: string;
  isVip?: string;
  vipLevel?: string;
  expireDate?: string;
  vasType?: VipType;
  payId?: string;
  payName?: string;
  vipGrow?: string;
  vipDayGrow?: string;
  isAutoDeduct?: string;
  isRemind?: string;
  isYear?: string;
  register?: string;
}

export const useUserStore = createGlobalState(() => {
  const userStoreState = reactive<IUserStoreState>({
    curUser: {}
  })

  const isSignin = computed((): boolean => {
    return !!userStoreState.curUser.userId && userStoreState.curUser.userId !== '0'
  })

  const curVipType = computed((): VipType => {
    if (userStoreState.curUser.isVip === '1' && userStoreState.curUser.vasType !== undefined) {
      return userStoreState.curUser.vasType
    }
    return VipType.normal
  })

  const isVip = computed((): boolean => {
    return userStoreState.curUser.isVip === '1' && ([VipType.platinum, VipType.superv, VipType.normalVip].includes(userStoreState.curUser.vasType || VipType.normal))
  })

  const isYear = computed((): boolean => {
    return userStoreState.curUser.isYear === '1'
  })

  const isSuperV = computed((): boolean => {
    return curVipType.value === VipType.superv
  })

  const isYearlySuper = computed((): boolean => {
    return isYear.value && isSuperV.value
  })

  const vipLevelType = computed((): string => {
    if (isSuperV.value) {
      if (userStoreState.curUser.vipLevel == '0' || !userStoreState.curUser.vipLevel) {
        return 'superV'
      }
      return 'superV' + userStoreState.curUser.vipLevel
    }
    return ''
  })

  const vipTypeString = computed(() => {
    if (curVipType.value === VipType.superv) {
      return 'svip'
    } else if ([VipType.normalVip, VipType.platinum].includes(curVipType.value)) {
      return 'vip'
    }
    return 'normal'
  })

  const fullVipTypeString = computed(() => {
    if (isSuperV.value) {
      if (isYear.value) {
        return 'super.year';
      } else {
        return 'super';
      }
    }
    if (isVip.value) {
      if (isYear.value) {
        return 'platinum.year';
      }
      return 'platinum';
    }
    return 'user';
  })

  const vipTypeCnString = computed(() => {
    if (curVipType.value === VipType.superv) {
      if (isYear.value) return '年费超级会员'
      return '超级会员'
    } else if ([VipType.normalVip, VipType.platinum].includes(curVipType.value)) {
      return '白金会员'
    }
    return '非会员'
  })

  const spaceVipType = computed(() => {
    if (isYear.value && curVipType.value === VipType.superv) {
      return 'super.year'
    }
    return curVipType.value
  })

  const privilegeType = computed(() => {
    if (isYear.value && curVipType.value === VipType.superv) {
      return 'superVYear';
    }
    if (curVipType.value === VipType.superv) {
      return 'superV';
    }
    if (curVipType.value === VipType.platinum) {
      return 'platinum';
    }
    return 'user';
  })

  const userStoreMutation = {
    setUserInfo: (userInfo: any) => {
      const VipInfo = userInfo?.vip_info?.[0] || {};

      userStoreState.curUser = {
        ...userStoreState.curUser,
        ...userInfo,
        ...VipInfo,
        ...{
          username: userInfo.name,
          userid: userInfo.id,
          userId: userInfo.id,
          isYear: VipInfo.is_year,
          isVip: VipInfo.is_vip,
          vasType: VipInfo.vas_type,
          vipLevel: VipInfo.level,
          mobile: userInfo.phone_number,
        }
      }
    }
  }

  return {
    isSignin,
    isVip,
    isYear,
    isSuperV,
    isYearlySuper,
    vipLevelType,
    vipTypeString,
    fullVipTypeString,
    vipTypeCnString,
    spaceVipType,
    privilegeType,
    curVipType,
    userStoreState,
    userStoreMutation,
  }
})
