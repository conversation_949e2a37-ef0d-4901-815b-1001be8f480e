<script setup lang="ts">
import { onMounted, ref, watch, reactive, useTemplateRef, onBeforeUnmount } from 'vue';
import { ICommonContextmenuOptions } from '.';
import { useElementVisibility } from '@vueuse/core';
import { IContextMenuItem } from '@root/common/thunder-pan-manager/pan-sdk/utils/file';

defineOptions({
  name: 'CommonContextmenu',
})

const props = withDefaults(defineProps<ICommonContextmenuOptions>(), {
  onClose: () => {},
  onMenuItemClick: () => {}
})

const visible = ref(false)
const $rootElement = useTemplateRef<HTMLDivElement>('$rootElement')
const rootElementIsVisible_ref = useElementVisibility($rootElement)
// 解决菜单在屏幕上乱跳的问题
const isRootElementVisible = ref(false)

// 防止显示到父容器的外部
const computedPosition = reactive({ x: 0, y: 0 });
const normalizePosition = () => {
  const rootBounding = document.body.getBoundingClientRect()
  const contextMenuBounding = $rootElement.value!.getBoundingClientRect()

  let x = props.clickPosition.x
  let y = props.clickPosition.y

  if (x + contextMenuBounding.width > rootBounding.right) {
    x -= contextMenuBounding.width
  }
  if (y + contextMenuBounding.height > rootBounding.bottom) {
    y -= (y + contextMenuBounding.height - rootBounding.bottom)
  }
  computedPosition.x = x
  computedPosition.y = y
  isRootElementVisible.value = true
}

const handleMenuItemMouseenter = (event: MouseEvent) => {
  const target = event.target as HTMLLIElement
  const childElement = target.querySelector('.common-contextmenu__sub-body') as HTMLDivElement

  if (childElement) {
    const rootBounding = document.body.getBoundingClientRect()
    const contextMenuBounding = target.getBoundingClientRect()
    const { width, right, bottom } = childElement.getBoundingClientRect()

    if (right > rootBounding.right) {
      childElement.style.left = `-${width + 6}px`
      childElement.classList.add('is-left')
    }

    if (bottom > rootBounding.bottom) {
      childElement.style.bottom = '0';
      childElement.style.marginBottom = `-${rootBounding.height - contextMenuBounding.bottom}px`
    }

    childElement.style.visibility = 'visible';
  }
}

const handleMenuItemMouseleave = (event) => {
  const target = event.target as HTMLLIElement
  const childElement = target.querySelector('.common-contextmenu__sub-body') as HTMLDivElement

  if (childElement) {
    childElement.classList.remove('is-left')
    childElement.style.visibility = 'hidden';
  }
}

const handleClickOutside = (e: MouseEvent) => {
  if (!e.composedPath().includes($rootElement.value!)) {
    props.onClose()
  }
}

const handleMenuItemClick = (item: IContextMenuItem) => {
  if (item.disabled && item.children) return

  props.onMenuItemClick(item)
  props.onClose()
}

const keys: any = { 37: 1, 38: 1, 39: 1, 40: 1 }
const wheelOpt = { passive: false }
const wheelEvent = 'onwheel' in document.createElement('div') ? 'wheel' : 'mousewheel'
const preventDefault = (e: Event) => {
  if (props.allowScroll) {
    props.onClose()
  } else {
    e.preventDefault()
  }
}
// 禁用键盘的方向键
function preventDefaultForScrollKeys (e: any) {
  if (keys[e.keyCode]) preventDefault(e)
  return false
}
const disableParentScroller = () => {
  const dom = props.parentElement
  if (dom) {
    dom.addEventListener('DOMMouseScroll', preventDefault, false) // older FF
    dom.addEventListener(wheelEvent, preventDefault, wheelOpt) // modern desktop
    dom.addEventListener('touchmove', preventDefault, wheelOpt) // mobile
    window.addEventListener('keydown', preventDefaultForScrollKeys, false)
  }
}

const enableParentScroller = () => {
  const dom = props.parentElement
  if (dom) {
    dom.removeEventListener('DOMMouseScroll', preventDefault, false)
    dom.removeEventListener(wheelEvent, preventDefault, false)
    dom.removeEventListener('touchmove', preventDefault, false)
    window.removeEventListener('keydown', preventDefaultForScrollKeys, false)
  }
}

watch(rootElementIsVisible_ref, () => {
  if (rootElementIsVisible_ref.value) {
    normalizePosition();
  }
})

onMounted(() => {
  visible.value = true
  disableParentScroller()
  window.addEventListener('click', handleClickOutside, true)
  window.addEventListener('contextmenu', handleClickOutside, true)
})

onBeforeUnmount(() => {
  enableParentScroller()
  window.removeEventListener('click', handleClickOutside, true)
  window.removeEventListener('contextmenu', handleClickOutside, true)
})

defineExpose({
  visible,
})
</script>

<template>
  <transition
    name="fade"
    @before-leave="onClose"
    @after-leave="$emit('destroy')"
  >
    <div
      v-show="visible"
      ref="$rootElement"
      class="common-contextmenu__body"
      :style="{ left: `${computedPosition.x}px`, top: `${computedPosition.y}px`, opacity: isRootElementVisible ? 1 : 0 }"
      @click.stop
    >
      <ul
        v-for="(menu, index) in menuList"
        :key="`menuBlock_${index}`"
        class="common-contextmenu__content"
      >
        <li
          v-for="item in menu"
          class="common-contextmenu__item"
          :class="{
            'is-disabled': item.disabled
          }"
          :key="item.name"
          @click.stop="handleMenuItemClick(item)"
          @mouseenter="handleMenuItemMouseenter($event)"
          @mouseleave="handleMenuItemMouseleave($event)"
        >
          <i class="icon" :class="item.iconLeft"></i>
          <span class="text">{{ item.name }}</span>
          <i v-if="item.iconRight":class="item.iconRight"></i>

          <div
            v-if="item.children"
            class="common-contextmenu__sub-body"
          >
            <ul
              v-for="(childMenu, childIndex) in item.children"
              :key="`childrenMenuBlock_${childIndex}`"
              class="common-contextmenu__content"
            >
              <li
                v-for="childItem in childMenu"
                class="common-contextmenu__item"
                :class="{
                  'is-disabled': childItem.disabled
                }"
                :key="childItem.name"
                @click.stop="handleMenuItemClick(childItem)"
              >
                <i class="icon" :class="childItem.iconLeft"></i>
                <span class="text">{{ childItem.name }}</span>
              </li>

              <div v-if="childIndex !== item.children.length - 1" class="menu-separator"></div>
            </ul>
          </div>
        </li>

        <div v-if="index !== menuList.length - 1" class="menu-separator"></div>
      </ul>
    </div>
  </transition>
</template>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: all 0s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.common-contextmenu__body {
  display: flex;
  flex-direction: column;
  position: fixed;
  z-index: var(--z-index-alert);
  padding: 6px;
  background-color: var(--background-background-elevated);
  border: 1px solid var(--border-border-2);
  border-radius: var(--border-radius-M);
  box-shadow: 0 6px 30px #272E3B29;

  .common-contextmenu__content {

    .common-contextmenu__item {
      width: 176px;
      height: 36px;
      padding: 7px 4px 7px 8px;
      border-radius: var(--border-radius-S);
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      .icon {
        flex-shrink: 0;
        width: 16px;
        height: 16px;
        margin-right: 8px;
        flex-shrink: 0;
        color: var(--font-font-1);
      }

      .text {
        flex-grow: 1;
        font-size: 13px;
        line-height: 22px;
        color: var(--font-font-1);
        -webkit-line-clamp: 1;
        display: -webkit-box;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        white-space: pre-wrap;
      }

      .common-contextmenu__sub-body {
        position: absolute;
        left: calc(100% + 6px);
        display: flex;
        flex-direction: column;
        padding: 6px;
        background-color: var(--background-background-elevated);
        border: 1px solid var(--border-border-2);
        border-radius: var(--border-radius-M);
        box-shadow: 0 6px 30px #272E3B29;
        visibility: hidden;

        &::before {
          content: '';
          height: 100%;
          width: 12px;
          position: absolute;
          left: -12px;
          top: 0;
        }

        &.is-left {
          &::before {
            display: none;
          }

          &::after {
            content: '';
            height: 100%;
            width: 12px;
            position: absolute;
            right: -12px;
            top: 0;
          }
        }
      }

      &:hover {
        background-color: var(--fill-fill-3);
      }

      &.is-disabled {
        cursor: default;

        .icon,
        .text {
          color: var(--font-font-4);
        }

        &:hover {
          background: none !important;
        }
      }
    }

    .menu-separator {
      width: 100%;
      height: 1px;
      margin: 2px 0;
      background-color: var(--border-border-3);
    }
  }
}
</style>
