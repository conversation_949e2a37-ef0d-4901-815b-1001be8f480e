import { API_FILE, API_TASK } from '../types'

export enum Phase {
  UNKNOWN = 'PHASE_TYPE_UNKNOW',
  PENDING = 'PHASE_TYPE_PENDING',
  RUNNING = 'PHASE_TYPE_RUNNING',
  COMPLETE = 'PHASE_TYPE_COMPLETE',
  ERROR = 'PHASE_TYPE_ERROR',
  PAUSED = 'PHASE_TYPE_PAUSED',
}

export const GB = 1024 * 1024 * 1024

function generateSizeInformation (size: string | number, sizeMap: string[], threshold = 1024) {
  size = parseInt(size as string)
  if (!size) {
    size = 0
  }
  let index = 0
  while (size > 1024) {
    size = size / 1024
    index++
  }
  if (size >= threshold) {
    size = size / threshold
    index++
  }
  return {
    size,
    unit: sizeMap[index]
  }
}

/**
 * 格式化大小，单位分别为 ['B', 'KB', 'MB', 'GB', 'TB']
 * @param size 大小数值
 * @param toFixed 保留小数位，默认 1
 * @returns
 */
export function formatSize (size: string | number, toFixed = 1): string {
  let flag = 1
  size = parseInt(size as string)
  if (size < 0) {
    size = -size
    flag = -1
  }
  const sizeMap = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const sizeInfo = generateSizeInformation(size, sizeMap, 999.9)
  return `${(flag * sizeInfo.size).toFixed(sizeInfo.unit === 'B' ? 0 : toFixed)}${sizeInfo.unit}`
}

/**
 * 根据服务端返回的文件类型，对本地逻辑所需的数据进行映射
 * @param file_category 文件类型
 * @returns 映射
 */
export enum FileCategory {
  OTHER = 'OTHER',            // 其他文件类型
  VIDEO = 'VIDEO',            // Video视频文件类型
  TEXT = 'TEXT',              // Text文本文件类型
  IMAGE = 'IMAGE',            // Image图像文件类型
  AUDIO = 'AUDIO',            // Audio音频文件类型
  ARCHIVE = 'ARCHIVE',        // Archive归档文件类型
  FONT = 'FONT',              // Font字体文件类型
  SUBTITLE = 'SUBTITLE',      // Subtitle字幕文件类型
  INSTALLER = 'INSTALLER',    // Installer安装包文件类型
}
export function getMapFileTypeByFileCategory (file_category?: FileCategory | API_FILE.DriveFileCategory) {
  switch (file_category) {
    case FileCategory.AUDIO:
    case FileCategory.VIDEO:
      return 'media'
    case FileCategory.IMAGE:
      return 'picture'
    case FileCategory.ARCHIVE:
      return 'unzip'
    default:
      return 'other'
  }
}

export function checkCanUnzip (
  condition: {
    postfix?: string[];
    mimeType?: string[]
  },
  postfix = '',
  mimeType = '',
  category?: FileCategory | API_FILE.DriveFileCategory
) {
  return category === FileCategory.ARCHIVE
    || (condition?.mimeType && condition?.mimeType.includes(mimeType))
    || (condition?.postfix && condition?.postfix.includes(postfix))
}

// 如果要新增类型，最好还是全局搜一下getPreviewType函数在哪里用到，主要留意一下TypeScript的报错。
export type PreviewType = 'picture' | 'media' | 'unzip' | 'torrent' | 'other' | 'mobile';
export interface IPreviewTypeParams  {
  extString?: string;
  mimeType?: string;
  category?: FileCategory | API_FILE.DriveFileCategory;
  condition?: {
    mimeType: any[];
    postfix: string[];
  };
}
export function getPreviewType ({
  extString = '',
  mimeType = '',
  category = FileCategory.OTHER,
  condition = { mimeType: [], postfix: ['.zip', '.rar'] }
}: IPreviewTypeParams): PreviewType {
  const res = getMapFileTypeByFileCategory(category)
  // 通过 file_category 判断出正常的文件类型，则直接返回
  if (res !== 'other') return res

  // res 返回为 other 则走原有逻辑，使用文件后缀进行类型判断
  const ext = extString.toLocaleLowerCase()
  // 在线解压
  if (checkCanUnzip(condition, ext, mimeType, category)) {
    return 'unzip'
  }
  // 安装包
  if (ext === '.apk') {
    return 'mobile'
  }
  // 种子
  if (ext === '.torrent') {
    return 'torrent'
  }
  // 音视频
  if (['video', 'music'].includes(getMediaType(ext, mimeType, category))) {
    return 'media'
  }
  // 图片 https://developer.mozilla.org/zh-CN/docs/Web/Media/Formats/Image_types
  const picturePreviewExt: string[] = [
    '.apng', '.bmp', '.gif', '.ico', '.cur', '.jpg', '.jpeg',
    '.jfif', '.pjpeg', '.pjp', '.png', '.svg', '.tif', '.tiff', '.webp'
  ]
  if (picturePreviewExt.includes(ext)) {
    return 'picture'
  }

  return 'other'
}

export function getMediaType (
  extString = '',
  mimeType = '',
  category?: FileCategory | API_FILE.DriveFileCategory
): 'music' | 'video' | 'other' {
  const ext = extString.toLocaleLowerCase()
  // 视频后缀
  const videoPreviewExt: string[] = [
    '.m2ts', '.asf', '.mpg', '.rmvb', '.rm', '.wmv', '.avi',
    '.mp4', '.mpeg', '.mkv', '.mov', '.ts', '.flv', '.3gp'
  ]
  // 音频后缀
  const musicPreviewExt: string[] = [
    '.aiff', '.cue', '.m4u', '.au', '.cdda', '.raw', '.wav',
    '.flac', '.tak', '.mp3', '.aac', '.wma', '.m4a', '.mid',
    '.mka', '.mp2', '.mpa', '.mpc', '.ape', '.ofr', '.ogg',
    '.ra', '.wv', '.tta', '.ac3', '.dts', '.nsf', '.mod',
    '.s3m', '.xm', '.it', '.vst'
  ]

  // 视频
  if (
    category === FileCategory.VIDEO
      || videoPreviewExt.includes(ext)
      || /^video/.test(mimeType)
  ) {
    return 'video'
  }
  // 音频
  if (
    category === FileCategory.AUDIO
      || musicPreviewExt.includes(ext)
      || /^audio/.test(mimeType)
  ) {
    return 'music'
  }

  return 'other'
}

export function getFileExtension (fileName: string): string {
  if (typeof fileName !== 'string') {
    return ''
  }
  const idx = fileName.lastIndexOf('.')
  return idx === -1 ? '' : fileName.slice(idx)
}

export function isSensitiveFile (item: {audit?: API_FILE.DriveAudit}): boolean {
  return ['STATUS_SENSITIVE_RESOURCE', 'STATUS_SENSITIVE_WORD', 'STATUS_INVALID_RESOURCE'].includes(item?.audit?.status ?? '')
}

export function isReadOnlyFile (item: API_FILE.DriveFile): boolean {
  return !Boolean(item.writable)
}

export function isSystemDirectory (type: string): boolean {
  return type !== 'NORMAL'
}

export function isFolder (file: API_FILE.DriveFile): boolean  {
  return file && file.kind === 'drive#folder'
}

export function isFile (file: API_FILE.DriveFile): boolean  {
  return file && file.kind === 'drive#file'
}

export function isTaskAdding (task: API_TASK.DriveTask): boolean {
  return task.phase == Phase.PENDING || task.phase == Phase.RUNNING;
}

// 风控过滤结果
export enum AuditStatus {
  // 未知
  UNKNOW = 'STATUS_UNKNOW',
  // 允许
  OK = 'STATUS_OK',
  // 敏感资源
  SENSITIVE_RESOURCE = 'STATUS_SENSITIVE_RESOURCE',
  // 文件名包含敏感词
  SENSITIVE_WORD = 'STATUS_SENSITIVE_WORD',
  // 流畅播文件失效
  STATUS_INVALID_RESOURCE = 'STATUS_INVALID_RESOURCE',
}
export function isPassAuditFilter (file: API_FILE.DriveFile): boolean {
  return file.audit === null || file?.audit?.status === AuditStatus.OK || file?.audit?.status === AuditStatus.UNKNOW
}

export type extendDriveFile = API_FILE.DriveFile & {__progress__?: string; __stat__?: string; __taskid__?: string; __kind__?: string}
export function isPassSpace (file: extendDriveFile): boolean {
  return !(file.space === 'SPACE_FAVORITE')
}

export function isPassComplete (file: extendDriveFile): boolean {
  return !((file.__kind__ === 'drive#task') || (file.phase === Phase.PENDING && !file.__kind__))
}

export function isFileInvalidToDownload (file: extendDriveFile) {
  return isSensitiveFile(file) || file.id === undefined || file.name === undefined || file.size === '0'
}

export function isTwoOrderedListEqual<T> (
  a: Array<T>,
  b: Array<T>,
  equalPredictFunction: (a: T, b: T) => boolean,
  sortFunction?: (a: T, b: T) => number,
) {
  if (a.length !== b.length) {
    return false
  }
  if (sortFunction) {
    a.sort(sortFunction)
    b.sort(sortFunction)
  }
  for (let i = 0; i < a.length; i++) {
    if (!equalPredictFunction(a[i], b[i])) {
      return false
    }
  }
  return true
}

/**
 * 获取解压错误码对应的错误信息
 * @param status
 * @returns
 */
export function genDecompressStatusMsg (status: string): string {
  const map: {[index: string]: string} = {
    PASS_WORD_ERROR: '解压密码错误',
    RUNNING_TASK: '当前正在进行该压缩包的解压任务，请稍后重试',
    EXPIRED: '已过期',
    DELETED: '已删除',
    INVALID_FILE_FORMAT: '非法压缩包',
    FILE_LIST_TIMEOUT: '大文件超时',
    NOT_FOUND: '未找到',
    AUDITING: '审核中',
    SENSITIVE_WORD: '文件名包含敏感词',
    SENSITIVE_RESOURCE: '包含敏感资源'
  }
  return map[status] || '无法查询压缩文件列表'
}

function isErrorWithMessage (error: unknown): error is Error {
  return (error as Error)?.message !== undefined
}

export function getErrorDescription (error: unknown): string {
  try {
    if (typeof error === 'object' && (error as any)?.error_description) {
      return (error as any).error_description
    }
    if (isErrorWithMessage(error)) {
      const errorInfo = JSON.parse((error as Error)?.message)
      return errorInfo.error_description
    }
    return ''
  } catch (err) {
    console.error('Error during parsing JSON', error)
    if ((error as Error).message) {
      return (error as Error).message
    }
    return ''
  }
}

export function isContainPanShareLink (url: string): boolean {
  return typeof url === 'string' && url.indexOf('pan.xunlei.com/s/') > -1
}

export function getShareIdAndPassCode (text: string) {
  const reg = /(?:http|https):\/\/pan\.xunlei\.com\/s\/([^\s\u4e00-\u9fa5]*)(?:\s*提取码[：:]([^\s]{4})|)/g
  let match
  const result: {[index: string]: string} = {}
  while ((match = reg.exec(text)) != null) {
    const [_, id, code] = match
    result[id] = code || ''
  }
  return result
}

/**
 * 对比两个字符串是否相似
 */
export function isNameSimilar(a: string, b: string): boolean {
  // 1.忽略大小写 2.比较相同字符数 3.先50%相似就认为相同
  let src = a.toLowerCase()
  let dst = b.toLowerCase()

  let nSameCount = 0;
  for (let i = 0; i < (src.length > dst.length ? dst.length : src.length); i++) {
    if (src.charAt(i) === dst.charAt(i)) {
      nSameCount += 1
    }
  }

  return nSameCount * 2 >= (src.length < dst.length ? dst.length : src.length)
}
