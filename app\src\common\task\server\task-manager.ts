import { server } from '@xunlei/node-net-ipc/dist/ipc-server';
import * as BaseType from '../base'
import {GetTaskManager} from '../impl/task-manager'
export class TaskManagerServer {
    static init() {
        GetTaskManager().attachTaskStatusChangeEvent((taskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
            server.fireClientEvent('TaskManagerTaskStatusChange', taskId, eOld, eNew);
        });
        GetTaskManager().attachTaskDetailChangeEvent((taskId, flags: BaseType.TaskDetailChangedFlags) => {
            server.fireClientEvent('TaskManagerTaskDetailChange', taskId,flags);
        });
        GetTaskManager().attachGroupSubTaskDetailChangeEvent((taskId: number, groupTaskId: number,
            flags: BaseType.TaskDetailChangedFlags) => {
            server.fireClientEvent('TaskManagerGroupSubTaskDetailChange', taskId, groupTaskId, flags);
        });
        GetTaskManager().attachGroupSubTaskStatusChangeEvent((taskId: number, groupTaskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
            server.fireClientEvent('TaskManagerGroupSubTaskStatusChange', taskId, groupTaskId, eOld, eNew);
        });
        GetTaskManager().attachTaskDeletedEvent((taskId: number, groupTaskId) => {
            server.fireClientEvent('TaskManagerTaskDeleted', taskId, groupTaskId);
        });
        GetTaskManager().attachBtSubTaskDetailChangeEvent((taskId: number, fileIndex: number, flags: BaseType.BtSubFileDetailChangeFlags) => {
            server.fireClientEvent('TaskManagerBtSubTaskDetailChange', taskId, fileIndex, flags);
        });
        GetTaskManager().attachBtSubTaskStatusChangeEvent((taskId: number, fileIndex: number, eOld: BaseType.BtSubFileStatus, eNew: BaseType.BtSubFileStatus) => {
            server.fireClientEvent('TaskManagerBtSubTaskStatusChange', taskId, fileIndex, eOld, eNew);
        });

        server.registerFunctions({
            TaskManagerUpdateMaxDownloadTaskCount: (c: any, context: any, n: number) => {
                GetTaskManager().updateMaxDownloadTaskCount(n);
            },
            TaskManagerSetGlobalExtInfo: (c: any, context: any, info: string, append: boolean) => {
                GetTaskManager().setGlobalExtInfo(info, append);
            },
            TaskManagerGetTpPeerId: async (c: any, context: any) => {
                return await GetTaskManager().getTpPeerId();
            },
            TaskManagerSetUserInfo: (userId: string, jumpKey: string) => {
                GetTaskManager().setUserInfo(userId, jumpKey);
            },
            TaskManagerFindRepeatTask: async (c: any, context: any, infos: BaseType.NewTaskSet[]) => {
                return await GetTaskManager().findRepeatTask(infos);
            },
            TaskManagerCheckRepeatAndCreateTask: async (c: any, context: any, infos: BaseType.NewTaskSet) => {
                let taskInfo = await GetTaskManager().checkRepeatAndCreateTask(infos);
                return {taskId: taskInfo.task ? taskInfo.task.getId() : 0, exist: taskInfo.exist};
            },

            TaskManagerCreateTask: async (c: any, context: any, infos: BaseType.NewTaskSet) => {
                let task = await GetTaskManager().createTask(infos);
                if (!task) {
                    return 0;
                }
                return task.getId();
            },
            TaskManagerCreateGroupTask: async (c: any, context: any, taskInfo: BaseType.NewTaskInfo, groupInfoList: BaseType.NewGroupTaskInfo[]) => {
                let task = await GetTaskManager().createGroupTask(taskInfo, groupInfoList);
                if (!task) {
                    return 0;
                }
                return task.getId();
            },
            TaskManagerCreateP2spTask: async (c: any, context: any, taskInfo: BaseType.NewTaskInfo, p2spInfo: BaseType.NewP2spTaskInfo) => {
                let task = await GetTaskManager().createP2spTask(taskInfo, p2spInfo);
                if (!task) {
                    return 0;
                }
                return task.getId();
            },
            TaskManagerCreateBtTask: async (c: any, context: any, taskInfo: BaseType.NewTaskInfo, btTaskInfo: BaseType.NewBtTaskInfo) => {
                let task = await GetTaskManager().createBtTask(taskInfo, btTaskInfo);
                if (!task) {
                    return 0;
                }
                return task.getId();
            },
            TaskManagerCreateEmuleTask: async (c: any, context: any, taskInfo: BaseType.NewTaskInfo, emuleTaskInfo: BaseType.NewEmuleTaskInfo) => {
                let task = await GetTaskManager().createEmuleTask(taskInfo, emuleTaskInfo);
                if (!task) {
                    return 0;
                }
                return task.getId();
            },
            TaskManagerCreateMagnetTask: async (c: any, context: any, taskInfo: BaseType.NewTaskInfo, magnetTaskInfo: BaseType.NewMagnetTaskInfo) => {
                let task = await GetTaskManager().createMagnetTask(taskInfo, magnetTaskInfo);
                if (!task) {
                    return 0;
                }
                return task.getId();
            },
            TaskManagerBatchStartTasks:(c: any, context: any, taskIds: number[]) => {
                GetTaskManager().batchStartTasks(taskIds);
            },
            TaskManagerBatchStopTasks: (c: any, context: any, taskIds: number[], r: BaseType.TaskStopReason) => {
                GetTaskManager().batchStopTasks(taskIds, r);
            },
            TaskManagerBatchDeleteTasks: (c: any, context: any, taskIds: number[], deleteFile: boolean) => {
                GetTaskManager().batchDeleteTasks(taskIds, deleteFile);
            },
            TaskManagerBatchRecycleTasks: (c: any, context: any, taskIds: number[]) => {
                GetTaskManager().batchRecycleTasks(taskIds);
            },
            TaskManagerStartDownloadAndPlay: (c: any, context: any, taskId: number) => {
                GetTaskManager().startDownloadAndPlay(taskId);
            },
            TaskManagerStopDownloadAndPlay: (c: any, context: any) => {
                GetTaskManager().stopDownloadAndPlay();
            },
            TaskManagerSetProxy: (c: any, context: any, host: string,
                      port: number,
                      userName: string,
                      passWord: string,
                      proxyType: BaseType.ProxyType) => {
                GetTaskManager().setProxy(host, port, userName, passWord, proxyType);
            },

            //categroymanager
            ////////////////////////////////////
            CategoryManagerSetCurrentPanUserId: async(c: any, content: any, id: string) => {
                GetTaskManager().GetCategoryManager().setCurrentPanUserId(id);
            },
            CategoryManagerGetCurrentPanCategoryId: async(c: any, content: any) => {
                let category = await GetTaskManager().GetCategoryManager().getCurrentPanCategory();
                if (category) {
                    return category.getId();
                }

                return -1;
            },
        });
    }
}