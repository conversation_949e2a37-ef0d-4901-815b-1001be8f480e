
export interface IHistoryTask {
  key: string;
  collection_id: number;
  create_time: string;
  filename: string;
  filesize: number;
  filetype: number;
  folder_id: number;
  task_id: number;
  url: string;
  record_type: number;
  remark_name: string;
  url_type: number;
  task_id_str: string;
}

export interface IResponseHistory {
  result: number
  task_info: IHistoryTask[]
  total: number
  message: string
}

export interface IResponseUpdateHistory {
  result: number
  task_info: IHistoryTask[]
  message: string
  remain_collection_num: number
}

export interface IResult {
  result: number
  message: string
}