import { ComponentInternalInstance, createVNode, render, shallowReactive, VNode } from 'vue'
import DialogConstructor from './component.vue'
import { API_FILE, API_SHARE } from '@root/common/thunder-pan-manager/pan-sdk/types'

let seed = 1

export interface DialogHandler {
  close: () => void
}

export type DialogContext = {
  id: string
  vnode: VNode
  handler: DialogHandler
  vm: ComponentInternalInstance
  props: any
}

export interface IShareDialogOptions {
  files: API_FILE.DriveFile[]
  preShareResult: API_SHARE.DriveCreateShareResponse
  onClose?: () => void
}

export const instances: any[] = shallowReactive([])

export const getInstance = (id: string) => {
  const idx = instances.findIndex((instance) => instance.id === id)
  const current = instances[idx]
  let prev: DialogContext | undefined
  if (idx > 0) {
    prev = instances[idx - 1]
  }
  return { current, prev }
}

const closeDialog = (instance: DialogContext) => {
  const idx = instances.indexOf(instance)
  if (idx === -1) return

  instances.splice(idx, 1)
  const { handler } = instance
  handler.close()
}

const createDialog = (options: IShareDialogOptions): DialogContext => {
  const id = `share_dialog_${seed++}`
  const container = document.createElement('div')

  const props = {
    ...options,
    id,
    onClose: () => {
      if (options.onClose) {
        options.onClose()
      }
      closeDialog(instance)
    },
    onDestroy: () => {
      render(null, container)
    },
  }

  const vnode = createVNode(DialogConstructor, props)
  render(vnode, container)
  document.body.appendChild(container.firstElementChild!)

  const vm = vnode.component!
  const handler = {
    close: () => {
      vm.exposed!.visible.value = false
    },
  }

  const instance = {
    id,
    vnode,
    vm,
    handler,
    props: (vnode.component as any).props,
  }

  return instance
}

export const CreateShareDialog = (options: IShareDialogOptions) => {
  const instance = createDialog(options)

  instances.push(instance)
  return instance.handler
}

export const closeAllShareDialog = () => {
  instances.forEach(instance => {
    instance.handler.close()
  })
}
