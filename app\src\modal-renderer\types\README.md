# 树形节点类型定义

本文档介绍了任务列表组件中使用的树形节点TypeScript类型定义。

## 概述

树形节点类型定义位于 `new-task.type.ts` 文件中，提供了完整的类型安全支持，包括：

- 分支节点（目录节点）
- 叶子节点（文件节点）
- 特殊空节点（磁力链解析状态节点）
- 类型守卫函数
- 映射表类型

## 核心类型

### 基础类型

```typescript
/** 文件分类类型 */
export type FileCategoryType = 'video' | 'image' | 'zip' | 'other'

/** 节点类型 */
export type NodeType = 'branch' | 'leaf'
```

### 节点接口

#### IBranchNode - 分支节点（目录节点）

```typescript
export interface IBranchNode extends IBaseNode {
  type: 'branch'
  children: (IBranchNode | ILeafNode)[]
  isRoot: boolean
  taskId?: string
  taskIndex?: number
  url?: string
}
```

**属性说明：**
- `type`: 固定为 'branch'
- `children`: 子节点数组，可以是分支节点或叶子节点
- `isRoot`: 是否为根节点
- `taskId`: 任务ID
- `taskIndex`: 任务索引（用于排序）
- `url`: 任务URL

#### ILeafNode - 叶子节点（文件节点）

```typescript
export interface ILeafNode extends IBaseNode {
  type: 'leaf'
  suffix: string
  realIndex: number
  default: boolean
  isRoot: false
  fileCategory: FileCategoryType
  isSpecialEmptyNode: boolean
  status?: TaskParseStatusType
  taskId?: string
  taskIndex?: number
  url?: string
  protocol?: 'HTTP' | 'FTP'
  addTime?: number
}
```

**属性说明：**
- `type`: 固定为 'leaf'
- `suffix`: 文件后缀名
- `realIndex`: 文件真实索引
- `default`: 是否默认选中
- `isRoot`: 固定为 false（叶子节点永远不是根节点）
- `fileCategory`: 文件分类
- `isSpecialEmptyNode`: 是否为特殊空节点
- `status`: 任务状态（仅特殊空节点有效）
- `taskId`: 任务ID
- `taskIndex`: 任务索引
- `url`: 任务URL
- `protocol`: 协议类型
- `addTime`: 添加时间

#### ISpecialEmptyNode - 特殊空节点

```typescript
export interface ISpecialEmptyNode extends Omit<ILeafNode, 'fileSize' | 'realIndex' | 'default' | 'isSpecialEmptyNode' | 'fileCategory'> {
  type: 'leaf'
  isSpecialEmptyNode: true
  status: TaskParseStatusType
  fileSize: 0
  realIndex: -1
  default: false
  fileCategory: 'other'
  taskId: string
  taskIndex: number
  url: string
}
```

**属性说明：**
- 继承自ILeafNode，但重写了特定属性
- `isSpecialEmptyNode`: 固定为 true
- `fileSize`: 固定为 0
- `realIndex`: 固定为 -1
- `default`: 固定为 false
- `fileCategory`: 固定为 'other'
- `status`: 任务状态（必需）
- `taskId`: 任务ID（必需）
- `taskIndex`: 任务索引（必需）
- `url`: 任务URL（必需）

#### INormalLeafNode - 普通叶子节点

```typescript
export interface INormalLeafNode extends Omit<ILeafNode, 'isSpecialEmptyNode'> {
  type: 'leaf'
  isSpecialEmptyNode: false
  fileSize: number
  realIndex: number
  default: boolean
  isRoot: false
  fileCategory: FileCategoryType
  status?: TaskParseStatusType
}
```

**属性说明：**
- 继承自ILeafNode，但`isSpecialEmptyNode`固定为 false
- 代表真实的文件节点
- 包含完整的文件信息

### 映射表类型

```typescript
/** 分支节点映射表类型 */
export type BranchMap = Record<string, IBranchNode>

/** 叶子节点映射表类型 */
export type LeafMap = Record<string, ILeafNode>

/** 路径到key的映射表类型 */
export type PathToKeyMap = Record<string, string>
```

### 联合类型

```typescript
/** 节点联合类型 */
export type TreeNode = IBranchNode | ILeafNode
```

## 类型守卫函数

提供了多个类型守卫函数，用于在运行时判断节点类型：

```typescript
/** 判断是否为分支节点 */
export const isBranchNode = (node: TreeNode): node is IBranchNode => {
  return node.type === 'branch'
}

/** 判断是否为叶子节点 */
export const isLeafNode = (node: TreeNode): node is ILeafNode => {
  return node.type === 'leaf'
}

/** 判断是否为特殊空节点 */
export const isSpecialEmptyNode = (node: TreeNode): node is ISpecialEmptyNode => {
  return node.type === 'leaf' && node.isSpecialEmptyNode === true
}

/** 判断是否为普通叶子节点 */
export const isNormalLeafNode = (node: TreeNode): node is INormalLeafNode => {
  return node.type === 'leaf' && node.isSpecialEmptyNode === false
}
```

## 使用示例

### 基本使用

```typescript
import {
  IBranchNode,
  ILeafNode,
  TreeNode,
  isBranchNode,
  isLeafNode,
} from './new-task.type'

// 创建分支节点
const branchNode: IBranchNode = {
  type: 'branch',
  name: '文件夹',
  key: 'folder-1',
  fullPath: 'folder-1',
  parent: null,
  icon: 'file-type-folder',
  level: 0,
  taskType: TaskType.Magnet,
  fileSize: 0,
  isRoot: true,
  children: [],
}

// 创建叶子节点
const leafNode: ILeafNode = {
  type: 'leaf',
  name: '文件.mp4',
  key: 'file-1',
  fullPath: 'folder-1/文件.mp4',
  parent: branchNode,
  icon: 'file-type-video',
  level: 1,
  taskType: TaskType.Magnet,
  fileSize: 1024 * 1024 * 100, // 100MB
  isRoot: false,
  suffix: 'mp4',
  realIndex: 0,
  default: true,
  fileCategory: 'video',
  isSpecialEmptyNode: false,
}

// 使用类型守卫
function processNode(node: TreeNode) {
  if (isBranchNode(node)) {
    console.log('分支节点:', node.name, '子节点数量:', node.children.length)
  } else if (isLeafNode(node)) {
    console.log('叶子节点:', node.name, '文件大小:', node.fileSize)
  }
}
```

### 映射表使用

```typescript
import { BranchMap, LeafMap, PathToKeyMap } from './new-task.type'

// 创建映射表
const branchMap: BranchMap = {}
const leafMap: LeafMap = {}
const pathToKeyMap: PathToKeyMap = {}

// 添加节点
branchMap['folder-1'] = branchNode
leafMap['file-1'] = leafNode
pathToKeyMap['folder-1/文件.mp4'] = 'file-1'

// 查找节点
const node = branchMap['folder-1'] || leafMap['file-1']
```

### 文件分类

```typescript
import { FileCategoryType } from './new-task.type'

function getFileCategory(fileName: string): FileCategoryType {
  const videoExtensions = ['.mp4', '.avi', '.mkv']
  const imageExtensions = ['.jpg', '.png', '.gif']
  const zipExtensions = ['.zip', '.rar', '.7z']
  
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  
  if (videoExtensions.includes(extension)) return 'video'
  if (imageExtensions.includes(extension)) return 'image'
  if (zipExtensions.includes(extension)) return 'zip'
  return 'other'
}
```

## 注意事项

1. **类型安全**: 使用类型守卫函数确保类型安全
2. **属性完整性**: 创建节点时确保所有必需属性都已设置
3. **特殊空节点**: 特殊空节点有固定的属性值，不能随意修改
4. **文件大小**: 分支节点的文件大小通过计算子节点得出
5. **层级关系**: 通过parent属性建立节点间的层级关系

## 相关文件

- `new-task.type.ts`: 主要类型定义文件
- `tree-node-example.ts`: 使用示例文件
- `task-list.vue`: 实际使用这些类型的组件

## 更新日志

- 2024-01-XX: 初始版本，添加完整的树形节点类型定义
- 支持分支节点、叶子节点、特殊空节点
- 提供类型守卫函数和映射表类型
- 添加详细的使用示例和文档 