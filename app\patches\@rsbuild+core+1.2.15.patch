diff --git a/node_modules/@rsbuild/core/dist/index.cjs b/node_modules/@rsbuild/core/dist/index.cjs
index 6d16489..12e4f41 100644
--- a/node_modules/@rsbuild/core/dist/index.cjs
+++ b/node_modules/@rsbuild/core/dist/index.cjs
@@ -4566,7 +4566,8 @@ throw new Error('Failed to load Node.js addon: "${name}"\\n' + error);
     }
     let isClientCompiler = (compiler)=>{
         let { target } = compiler.options;
-        return !!target && (Array.isArray(target) ? target.includes('web') : 'web' === target);
+        const targets = castArray(target)
+        return !!target && (targets.includes('web') || targets.includes('electron-renderer'));
     }, isNodeCompiler = (compiler)=>{
         let { target } = compiler.options;
         return !!target && (Array.isArray(target) ? target.includes('node') : 'node' === target);
diff --git a/node_modules/@rsbuild/core/dist/index.js b/node_modules/@rsbuild/core/dist/index.js
index 1e7ef26..97ddb7e 100644
--- a/node_modules/@rsbuild/core/dist/index.js
+++ b/node_modules/@rsbuild/core/dist/index.js
@@ -4460,7 +4460,8 @@ async function getResolvedClientConfig(clientConfig, serverConfig) {
 }
 let isClientCompiler = (compiler)=>{
     let { target } = compiler.options;
-    return !!target && (Array.isArray(target) ? target.includes('web') : 'web' === target);
+    const targets = castArray(target)
+    return !!target && (targets.includes('web') || targets.includes('electron-renderer'));
 }, isNodeCompiler = (compiler)=>{
     let { target } = compiler.options;
     return !!target && (Array.isArray(target) ? target.includes('node') : 'node' === target);
