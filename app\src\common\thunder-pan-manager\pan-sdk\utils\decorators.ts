type DebounceOptions = {
  leading?: boolean;
  trailing?: boolean;
  maxWait?: number;
};
/**
 * 防抖装饰器函数，使用 @Debounce 进行修饰目标函数，上下文需要配合 @Bind 使用
 * @param wait 等待时间 ms
 * @param options 其他参数
 */
export function Debounce(
  wait:number,
  options: DebounceOptions = {}
) {
  return function(target: any, key: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    let timeout: string | number | NodeJS.Timeout;
    let previousCallTimestamp: number = 0;
    let result: any;

    descriptor.value = function(...args: any[]) {
      const now = Date.now();
      const isFirstCall = !timeout;

      const invokeFunction = () => {
        result = originalMethod.apply(this, args);
        previousCallTimestamp = now;
      };

      const shouldInvokeFunction = isFirstCall || options.trailing || (now - previousCallTimestamp >= wait);

      if (timeout) {
        clearTimeout(timeout);
      }

      if (shouldInvokeFunction) {
        if (options.leading && isFirstCall) {
          invokeFunction();
        } else {
          timeout = setTimeout(() => {
            invokeFunction();
            timeout = null;
          }, wait);
        }
      }

      if (options.maxWait && now - previousCallTimestamp >= options.maxWait) {
        invokeFunction();
      }

      return result;
    };

    return descriptor;
  };
}

type ThrottleOptions = {
  leading?: boolean;
  trailing?: boolean;
};
/**
 * 节流装饰器函数，使用 @Throttle 进行修饰目标函数，上下文需要配合 @Bind 使用
 * @param wait 等待时间 ms
 * @param options 其他参数
 */
export function Throttle(wait: number, options: ThrottleOptions = {}) {
  return function(target: any, key: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    let isThrottled = false;
    let lastArgs: any[] | null = null;
    // @ts-ignore
    let timeout;
    let lastInvokeTime: number = 0;

    descriptor.value = function(...args: any[]) {
      const now = Date.now();

      if (!isThrottled) {
        if (options.leading === false) {
          lastInvokeTime = now;
        }

        originalMethod.apply(this, args);
        isThrottled = true;

        if (options.trailing !== false) {
          timeout = setTimeout(() => {
            isThrottled = false;
            if (lastArgs) {
              descriptor.value.apply(this, lastArgs);
              lastArgs = null;
            }
          }, wait - (now - lastInvokeTime));
        }
      } else if (options.trailing !== false) {
        lastArgs = args;
      }
    };

    return descriptor;
  };
}

/**
 * 绑定上下文装饰器函数，使用 @Bind 进行修饰目标函数，不需要传参
 */
export function Bind(_: any, _2: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;

  descriptor.value = function(...args: any[]) {
    return originalMethod.apply(this, args);
  };

  return descriptor;
}