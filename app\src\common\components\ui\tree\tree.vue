<template>
  <div class="td-tree">
    <td-tree-node
      v-for="node in data"
      :key="getNode<PERSON><PERSON>(node)"
      :node="node"
      :level="0"
      :has-children="hasNodeChildren(node)"
      :expandable="hasNodeChildren(node)"
      :expanded="node.expanded"
      :data-expanded="node.expanded"
      :selected-node="selectedNode"
      @click-label="handleNodeClick"
      @update:expanded="handleNodeExpanded"
    >
      <template #node="nodeProps">
        <slot
          name="node"
          :node="nodeProps.node"
        ></slot>
      </template>
      <template
        v-if="$slots.label"
        #label="nodeProps"
      >
        <slot
          name="label"
          :node="nodeProps.node"
        ></slot>
      </template>
      <template
        v-if="$slots.icon"
        #icon="nodeProps"
      >
        <slot
          name="icon"
          :node="nodeProps.node"
        ></slot>
      </template>
      <template
        v-if="$slots.prefix"
        #prefix="nodeProps"
      >
        <slot
          name="prefix"
          :node="nodeProps.node"
        ></slot>
      </template>
    </td-tree-node>
  </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import TreeNode from './treeNode.vue'

export default defineComponent({
  components: {
    TdTreeNode: TreeNode,
  },

  props: {
    data: {
      type: Array,
      default: () => [],
    },
    nodeKey: {
      type: String,
      default: 'id',
    },
    selectedNode: {
      type: Object,
      default: null
    }
  },

  emits: ['node-click', 'node-expanded', 'node-collapsed', 'node-selected'],

  setup(props, { emit }) {
    // 添加选中节点的状态管理
    const selectedNode = ref(props.selectedNode)

    // 监听 props 数据变化
    watch(() => props.selectedNode, (newVal, oldVal) => {
      console.log('selectedNode变化:', {
        old: oldVal,
        new: newVal,
        oldId: oldVal ? oldVal[props.nodeKey] : null,
        newId: newVal ? newVal[props.nodeKey] : null
      })
      selectedNode.value = newVal
    }, { deep: true })

    watch(() => props.nodeKey, (newVal, oldVal) => {
      console.log('nodeKey变化:', {
        old: oldVal,
        new: newVal
      })
    })

    watch(() => props.data, (newVal, oldVal) => {
      console.log('data变化:', {
        oldLength: oldVal ? oldVal.length : 0,
        newLength: newVal ? newVal.length : 0,
        oldData: oldVal,
        newData: newVal
      })
    }, { deep: true })

    const getNodeKey = node => {
      return node[props.nodeKey] || node.label || Math.random()
    }

    const handleNodeExpanded = (node, expanded) => {
      console.log('handleNodeExpanded', node, ` ${expanded ? '展开' : '折叠'}`)
      if (expanded) {
        console.log('展开')
        emit('node-expanded', node)
      } else {
        console.log('折叠')
        emit('node-collapsed', node)
      }
    }

    // 检查节点是否有子节点
    const hasNodeChildren = node => {
      return node && node.children && Array.isArray(node.children) && node.children.length > 0
    }

    // 处理节点点击事件
    const handleNodeClick = (node) => {
      // 发送原有的node-click事件
      emit('node-click', node)

      // 调试信息：输出选中节点的详细信息
      console.log('=== 节点选中调试信息 ===')
      console.log('选中的节点:', node)
      console.log('节点ID/Key:', getNodeKey(node))
      console.log('节点标签:', node.label)
      console.log('节点层级:', node.level || '根节点')
      console.log('是否有子节点:', hasNodeChildren(node))
      console.log('当前selectedNode:', selectedNode.value)
      console.log('节点对象引用比较:', selectedNode.value === node)
      console.log('========================')

      // 设置任何节点为选中状态（移除叶子节点限制）
      selectedNode.value = node
      emit('node-selected', node)
    }

    return {
      selectedNode,
      getNodeKey,
      handleNodeExpanded,
      hasNodeChildren,
      handleNodeClick,
    }
  },
})
</script>

<style lang="scss">
@import './tree.scss';
</style>
