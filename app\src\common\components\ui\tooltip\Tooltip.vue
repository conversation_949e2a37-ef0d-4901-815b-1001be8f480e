<script lang='ts' setup>
import { ref, watch, computed, reactive, onMounted } from 'vue'
import { type Placement, createPopper, type Instance, type Options } from '@popperjs/core'
import { on } from 'events'
interface defineEmits {
  (e: 'visible-change', value: boolean): void
}
interface ITooltipProps {
  content?: string
  trigger?: 'hover' | 'click'
  placement?: Placement
  manual?: boolean
  popperOptions?: Partial<Options>
}

const props = withDefaults(defineProps<ITooltipProps>(), {
  placement: 'bottom',
  trigger: 'hover',
  manual: false,
  popperOptions: undefined,
})


const emits = defineEmits<defineEmits>()
const popperNode = ref<HTMLElement>()
const triggerNode = ref<HTMLElement>()
let popperInstance: Instance | null = null

const isTooltipVisible = ref(false)

const finalOptions = computed(() => {
  return {
    placement: props.placement,
    ...props.popperOptions,
  }
})

const toggleTooltip = (visible: boolean) => {
  console.log('>>>>>>>>>>>>>> 123')
  isTooltipVisible.value = visible
  emits('visible-change', visible)
}

let event: Record<string, any> = reactive({})
const attachEvents = () => {
  if (props.trigger === 'hover') {
    event['mouseenter'] = toggleTooltip(true)
    event['mouseleave'] = toggleTooltip(false)
  } else if (props.trigger === 'click') {
    event['click'] = toggle
  }
}



const toggle = () => {
  isTooltipVisible.value = !isTooltipVisible.value
  emits('visible-change', isTooltipVisible.value)
}



watch(() => props.trigger, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    event = {}
    attachEvents()
  }
})

watch(isTooltipVisible, (newVal) => {
  if (newVal && triggerNode.value && popperNode.value) {
    popperInstance = createPopper(
      triggerNode.value,
      popperNode.value,
      {
        placement: props.placement
      }
    )
  } else {
    popperInstance?.destroy()
    popperInstance = null
  }
}, { flush: 'post' })

onMounted(() => {
  attachEvents()
})
</script>

<template>
  <div class="td-tooltip">
    <div ref="triggerNode" class="td-tooltip-trigger" v-on="event" @click="toggle">
      <slot></slot>
    </div>
    <!-- <Transition name="tooltip-fade"> -->
    <div v-if="isTooltipVisible" ref="popperNode" class="td-tooltip-popper">
      <slot name="content">
        {{ props.content || '' }}
      </slot>
    </div>
    <!-- </Transition> -->
  </div>
</template>

<style lang='scss' module='style'>
.td-tooltip {}

.tooltip-popper {}

.td-tooltip-popper {
  z-index: 1;
}

.tooltip-fade-fade-enter,
.tooltip-fade-fade-active {
  transition: opacity 2s
}

.tooltip-fade-enter-from,
.tooltip-fade-enter-to {
  opacity: 0;
}
</style>