import {DownloadFileRecordCallApiProxy  } from '../call-api-impl';

export class DownloadFileRecord {
    private static instance: DownloadFileRecord;
    private apiProxy: DownloadFileRecordCallApiProxy = new DownloadFileRecordCallApiProxy();

    public static GetInstance(): DownloadFileRecord {
        if (!DownloadFileRecord.instance) {
            if (global.DownloadFileRecordClientInstance) {
                DownloadFileRecord.instance = global.DownloadFileRecordClientInstance;
            } else {
                DownloadFileRecord.instance = new DownloadFileRecord();
                global.DownloadFileRecordClientInstance = DownloadFileRecord.instance;
            }
        }
        return DownloadFileRecord.instance;
    }

    public async query(url: string, index: number): Promise<string> {
        let info = await this.apiProxy.CallApi('DownloadFileRecordQuery', url, index);
        if (info.bSucc) {
            return info.result as string;
        }
        return '';
    }
}