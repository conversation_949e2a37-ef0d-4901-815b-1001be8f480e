import { CallApiProxyImplWithIpcClient } from '../call-api-impl';
import * as BaseType from '../base'
export class P2spTask {
    private apiProxy: CallApiProxyImplWithIpcClient | null = null;
    taskId: number = 0;
    constructor(apiProxy: CallApiProxyImplWithIpcClient, id: number) {
        this.apiProxy = apiProxy;
        this.taskId = id;
    }

    public addHttpHeaders(field: string, value: string): void {
        this.apiProxy!.CallApi('TaskManagerTaskP2spTaskAddHttpHeaders', this.taskId, field, value);
    }
}