import * as DownloadKernel from '../base';
import { <PERSON>k<PERSON><PERSON><PERSON> } from './dk-helper';

function isIPv6(address: string) {
  // 使用正则表达式检查IPv6地址的格式
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  return ipv6Regex.test(address);
}

export namespace DkThunderHelper {

  interface ParsedPath {
    root: string;
    dir: string;
    base: string;
    ext: string;
    name: string;
  }

  export function parsePath(filePath: string): ParsedPath {
    const parsedPath: ParsedPath = {
      root: '',
      dir: '',
      base: '',
      ext: '',
      name: ''
    };

    const parts = filePath.split('/');
    const fileName = parts.pop() || '';
    parsedPath.base = fileName;

    const dir = parts.join('/');
    parsedPath.dir = dir || '.';

    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex !== -1) {
      parsedPath.ext = fileName.slice(lastDotIndex);
      parsedPath.name = fileName.slice(0, lastDotIndex);
    } else {
      parsedPath.name = fileName;
    }

    // On UNIX, root is always '/'
    parsedPath.root = '/';

    return parsedPath;
  }

  export function isSupportP2spUrl(url: string): boolean {
    url = url.toLowerCase();
    let ret: boolean = false;
    const httpScheme: string = 'http://';
    const httpsScheme: string = 'https://';
    const ftpScheme: string = 'ftp://';
    do {
      if (url.substr(0, httpScheme.length) === httpScheme) {
        ret = true;
        break;
      }
      if (url.substr(0, httpsScheme.length) === httpsScheme) {
        ret = true;
        break;
      }
      if (url.substr(0, ftpScheme.length) === ftpScheme) {
        ret = true;
        break;
      }
    } while (0);
    return ret;
  }

  export async function getTaskTypeFromUrl(url: string): Promise<DownloadKernel.TaskType> {
    let urlType: DownloadKernel.TaskType = await DkHelper.getTaskTypeFromUrl(url);
    if (urlType === DownloadKernel.TaskType.Unkown) {
      if (isSupportP2spUrl(url)) {
        let results = /:\/\/\[(.+?)\].*/.exec(url);
        if (!results) {
          // ftp://xlftp:xlftp@[2408:80e7:311:0:8000::20]/prometheus/prometheus-v1.tar.gz
          // results = /^ftp:\/\/.*?\[(.+?)\].*/.exec(url);

          // http://test:autotest@[2408:80e7:311:0:a000::78]:8083/1G/1GB.txt
          results = /.+?:\/\/.*?\[(.+?)\].*/.exec(url);
        }
        if (results && results[1]) {
          if (isIPv6(results[1])) {
            urlType = DownloadKernel.TaskType.P2sp;
          }
        }
      }
    }
    return urlType;
  }
}