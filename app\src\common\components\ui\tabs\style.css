.TabsRoot {
  display: flex;
  width: 400px;
  height: 38px;
  align-items: flex-start;
}

.TabsList {
  flex-shrink: 0;
  display: flex;
  position: relative;
  gap: 32px;
}

.TabsTrigger {
  font-family: inherit;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  line-height: 22px;
  color: var(--font-font-2, #4E5769);
  user-select: none;
  cursor: pointer;
}

.TabsTrigger[data-state='active'] {
  color: var(--primary-primary-font-default, #226DF5);
  box-shadow: inset 0 -1px 0 0 currentColor, 0 1px 0 0 currentColor;
}

.TabsTrigger:focus {
  position: relative;
}