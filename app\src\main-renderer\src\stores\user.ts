import { defineStore } from 'pinia'
import { UserInfo, IUserVipInfo } from '@root/common/account/account-type'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: {} as UserInfo,
    uid: '' as string,
    downVipInfo: {} as IUserVipInfo,
  }),
  getters: {
    getUid(): string { return this.userInfo.sub },
    getVipType(): number { return Number(this.downVipInfo?.vas_type ?? 0) },
    getIsVip(): boolean { return this.downVipInfo?.is_vip === '1' },
    getIsYear(): boolean { return this.downVipInfo?.is_year === '1' },
    getIsPlatina(): boolean { return this.getIsVip && this.getVipType === 3 },
    getIsSVip(): boolean { return this.getIsVip && this.getVipType === 5 },
    getIsSVipYear(): boolean { return this.getIsVip && this.getIsSVip && this.getIsYear },
  },
  actions: {
    async setUserInfo(info: UserInfo) {
      console.log('>>>>>>>>>>>>>>>>>> val', info)
      this.userInfo = info
      this.uid = info.sub || ''
      if (info.sub) {
        //
        this.downVipInfo = (info?.vip_info || []).find((item: any) => {
          return item.user_vas === '2'
        }) || {} as IUserVipInfo
      }
    }
  },
})