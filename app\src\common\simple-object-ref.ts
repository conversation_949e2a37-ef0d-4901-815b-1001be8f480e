import path from 'path';
import requireNodeFile from '../common/require-node-file';
import { CallApiProxy } from './call-api';
import { GetXxxNodePath } from './xxx-node-path';

const thunderHelper: any = requireNodeFile(path.join(GetXxxNodePath(), 'thunder_helper.node'));

export class SimpleObjectRef {
  public id: string = '';
  public apiProxy: CallApiProxy;
  private nativeLifeObj: any;

  constructor(apiProxy: CallApiProxy, id: string) {
    this.id = id;
    this.apiProxy = apiProxy;
    this.nativeLifeObj = new thunderHelper.SimpleObjectRefAddon(id, (id: string) => {
      console.log('SimpleObjectRef obj destory id=', id);
      this.apiProxy!.DestoryObject(id);
    });
  }
}