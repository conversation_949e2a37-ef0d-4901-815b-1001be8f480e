<script setup lang="ts">
import { onMounted, ref, onUnmounted } from 'vue'
import TrashItem from './TrashItem.vue'
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import XMPMessage from '@root/common/components/ui/message/index'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { API_FILE } from "@root/common/thunder-pan-manager/pan-sdk/types"
import Empty from './Empty.vue'
import Loading from '@root/common/components/ui/loading/index.vue'
import { AccountHelper } from '@root/common/account/client/accountHelper'
import { AccountHelperEventKey } from '@root/common/account/account-type'



const emit = defineEmits<{
  (e: 'cloudTrashUpdate', cloudTrashLength: number): void
}>()

const contextMenuList = [
  {
    key: 'resetCloud',
    label: '还原',
  },
  {
    key: 'deleteCloud',
    label: '删除',
    icon: 'xl-icon-general-close-l',
  },
]

const showDeleteDialog = ref(false)

// 分页相关状态
const nextPageToken = ref('')
const loading = ref(false)
const initialLoading = ref(true)
const hasMore = ref(true)

// 列表数据
const cloudTrashList = ref<API_FILE.DriveFile[]>([])
const curTrashItem = ref<API_FILE.DriveFile>()

// 模拟获取数据的方法
const fetchData = async (pageToken: string) => {
  const res = await ThunderPanClientSDK.getInstance().getAllTrashFiles(pageToken, {
    params: {
      order: 'MODIFY_TIME_DESC'
    }
  })
  return res
}

const loadMore = async () => {
  if (loading.value || !hasMore.value) return
  loading.value = true
  try {
    const { data } = await fetchData(nextPageToken.value)

    nextPageToken.value = data?.next_page_token ?? ''
    const trashData = data?.files ?? []

    if (trashData.length > 0) {
      cloudTrashList.value = [...cloudTrashList.value, ...trashData]
    }
    emit('cloudTrashUpdate', cloudTrashList.value.length)

    hasMore.value = !!nextPageToken.value

  } catch (error) {
    console.error('加载数据失败:', error)
    hasMore.value = false
    nextPageToken.value = ''
  } finally {
    loading.value = false
    initialLoading.value = false
  }
}

const updateTrashList = () => {
  cloudTrashList.value = cloudTrashList.value.filter(item => item.id !== curTrashItem.value?.id)
  emit('cloudTrashUpdate', cloudTrashList.value.length)
}

const handleDiaglogConfirm = async () => {
  try {
    await ThunderPanClientSDK.getInstance().batchDeleteFiles({ params: { ids: [curTrashItem.value?.id ?? ''] } })
    XMPMessage({
      message: '删除成功',
      type: 'success'
    })
    updateTrashList()
  } catch (error) {
    console.log('删除回收站云盘文件失败', error)
  }
}

const handleReset = async () => {
  try {
    await ThunderPanClientSDK.getInstance().batchUnTrash({ params: { ids: [curTrashItem.value?.id ?? ''] } })
    XMPMessage({
      message: '文件已还原',
      type: 'success'
    })
    updateTrashList()
  } catch (error) {
    console.log('还原回收站云盘文件失败', error)
  }
}

const initCloudTrash = async () => {
  nextPageToken.value = ''
  cloudTrashList.value = []
  hasMore.value = true
  initialLoading.value = true
  loading.value = false
  loadMore()
}

const handleClearAll = async () => {
  await ThunderPanClientSDK.getInstance().cleanTrash()
  cloudTrashList.value = []
  emit('cloudTrashUpdate', 0)
  XMPMessage({
    message: '清空成功',
    type: 'success'
  })
}

onMounted(() => {
  initCloudTrash()
  AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, initCloudTrash)
})

onUnmounted(() => {
  AccountHelper.getInstance().detachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, initCloudTrash)
})

const handleOperation = (key: string, item: API_FILE.DriveFile) => {
  curTrashItem.value = item
  switch (key) {
    case 'resetCloud':
      handleReset()
      break
    case 'deleteCloud':
      showDeleteDialog.value = true
      break
    default:
      break
  }
}

const handleDoubleClick = () => {
  XMPMessage({
    message: '回收站暂时不支持查看，请还原后再试',
    type: 'info'
  })
}

defineExpose({
  handleClearAll
})

</script>

<template>

  <Loading v-if="initialLoading || loading" />

  <RecycleScroller v-slot="{ item }" class="cloud-trash-wrapper" key-field="id" :items="cloudTrashList" :item-size="80"
    @scroll-end="loadMore" data-scroll-container>
    <TrashItem :key="item.id" type="cloud" :context-menu="contextMenuList"
      @operation="(key: string) => handleOperation(key, item)" :item-data="item"
      :selected="curTrashItem?.id === item.id" @double-click="handleDoubleClick" />
  </RecycleScroller>

  <Empty v-if="!initialLoading && cloudTrashList.length === 0" type="cloud" />

  <Dialog title="确定要删除吗" variant="error" :show-trigger="false" v-model:open="showDeleteDialog"
    @confirm="handleDiaglogConfirm" @cancel="showDeleteDialog = false" confirm-text="确认删除">
    文件删除后将无法恢复
  </Dialog>

</template>

<style scoped lang="scss">
.cloud-trash {
  &-wrapper {
    padding: 8px 22px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}
</style>