import {
    AccountHelperEventKey,
    UserInfo, SessionInfo,
    ISignUpParams,
    ISignInWithPasswordParams,
    ISignWithVerificationParams,
} from '@root/common/account/account-type';
import { AccountCallApiProxyImplWithIpcServer } from '../account-call-api-impl'
import { 
    IGetVerificationResponseData,
    IDeviceAuthorizeResponseData,
} from '@xbase/electron_auth_kit'
import {
    IGetCredentialsParams,
    Credentials
} from '@xbase/electron_auth_types_kit'
import { ISubscribeParams } from '@xbase/electron_sync_kit';
import {
    IDeviceAuthorizeParams,
    IStartDeviceAuthorizationResponseData,
} from '@xbase/electron_account_kit'
import {
  ResponseError,
} from '@xbase/electron_common_kit'
import { 
    ILoginHistory, 
    ILastLoginData 
} from '@root/common/account/impl/login-options-define'

export class AccountHelper {
    private apiProxy: AccountCallApiProxyImplWithIpcServer;
    private static instance: AccountHelper;
    constructor() {
        this.apiProxy = new AccountCallApiProxyImplWithIpcServer();
        this.apiProxy.Init();
    }
    public static getInstance(): AccountHelper {
        if (!AccountHelper.instance) {
            if (global.AccountHelperClientInstance) {
                AccountHelper.instance = global.AccountHelperClientInstance;
            } else {
                AccountHelper.instance = new AccountHelper();
                global.AccountHelperClientInstance = AccountHelper.instance;
            }
        }
        return AccountHelper.instance;
    }

    public async initSDK(): Promise<void> {
        await this.apiProxy.CallApi('AccountHelperInitSDK');
    }

    public async isSdkInitReady () {
        const res = await this.apiProxy.CallApi('AccountHelperIsInitReady')
        return (res.result as boolean) ?? false;
    }

  public async whenReady() {
    await this.apiProxy.CallApi('AccountHelperWhenReady');
  }

    // 注册账号，发送验证码到手机号
    public async sendSignUpVerificationWithPhoneNumber(phoneNumber: string): Promise<IGetVerificationResponseData> {
        let info = await this.apiProxy.CallApi('AccountHelperSendSignUpVerificationWithPhoneNumber', phoneNumber);
        if (info.bSucc) {
            return info.result as IGetVerificationResponseData;
        }
        return Promise.reject(info.error);
    }

    // 注册账号
    public async signUp(params: ISignUpParams): Promise<void> {
      const result = await this.apiProxy.CallApi('AccountHelperSignUp', params);
      if (!result?.bSucc) {
        return Promise.reject(result.error);
      }
    }

    // 自动登录
    public async autoSignIn(): Promise<void> {
      const result = await this.apiProxy.CallApi('AccountHelperAutoSignIn');
      if (!result?.bSucc) {
        return Promise.reject(result.error);
      }
    }

    // 设备授权
    public async deviceAuthorize(param: IDeviceAuthorizeParams): Promise<IDeviceAuthorizeResponseData> {
        let info = await this.apiProxy.CallApi('AccountHelperDeviceAuthorize', param);
        if (info.bSucc) {
            return info.result as IDeviceAuthorizeResponseData;
        }
        return {} as any;
    }

    // 结束设备授权
    public async stopDeviceAuthorization(): Promise<void> {
        await this.apiProxy.CallApi('AccountHelperStopDeviceAuthorization');
    }

    // 账号密码登录
    public async signInWithPassword(params: ISignInWithPasswordParams): Promise<void> {
      const result = await this.apiProxy.CallApi('AccountHelperSignInWithPassword', params);
      if (!result?.bSucc) {
        return Promise.reject(result.error);
      }
    }

    // 账号+验证码登录
    public async signInWithVerification(params: ISignWithVerificationParams): Promise<boolean> {
      let info = await this.apiProxy.CallApi('AccountHelperSignInWithVerification', params);
      if (!info?.bSucc) {
        return Promise.reject(info.error);
      }
      return (info.result as boolean) ?? false;
    }

    // 手机号登录发送验证码
    public async sendSignInVerificationWithPhoneNumber(phoneNumber: string): Promise<IGetVerificationResponseData> {
        let info = await this.apiProxy.CallApi('AccountHelperSendSignInVerificationWithPhoneNumber', phoneNumber);
        if (info.bSucc) {
            return info.result as IGetVerificationResponseData;
        }
        return Promise.reject(info.error);
    }

    // 获取扫码登录的二维码
    public async getSignInQrcode(): Promise<IStartDeviceAuthorizationResponseData> {
        let info = await this.apiProxy.CallApi('AccountHelperGetSignInQrcode');
        if (info.bSucc) {
            return info.result as IStartDeviceAuthorizationResponseData;
        }
        return Promise.reject(info.error);
    }

    // 退出登录
    public async signOut(): Promise<void> {
      const result = await this.apiProxy.CallApi('AccountHelperSignOut');
      if (!result?.bSucc) {
        return Promise.reject(result.error);
      }
    }

    // 登录凭证，实时有效（对应的每次请求前需要调用这个去刷新 Credentials）
    public async getCredentials(params: IGetCredentialsParams): Promise<Credentials> {
        let info = await this.apiProxy.CallApi('AccountHelperGetCredentials', params);
        if (info.bSucc) {
            return info.result as Credentials;
        }
        return Promise.reject(info.error);
    }

    //获取历史登录账号列表
    public async getLoginHistoryList(): Promise<ILoginHistory[]> {
        let info = await this.apiProxy.CallApi('AccountHelperGetLoginHistoryList');
        if (info.bSucc) {
            return info.result as ILoginHistory[];
        }
        return [];
    }

    // 获取上次登录账号
    public async getLastLoginData(): Promise<ILastLoginData> {
        let info = await this.apiProxy.CallApi('AccountHelperGetLastLoginData');
        if (info.bSucc) {
            return info.result as ILastLoginData;
        }
        return {} as any;
    }

    // 单次删除登录历史记录
    public async deleteLoginHistory(history: ILoginHistory): Promise<boolean> {
      let info = await this.apiProxy.CallApi('AccountHelperDeleteLoginHistory', history);
      if (!info?.bSucc) {
        return Promise.reject(info.error);
      }
      return (info.result as boolean) ?? false;
    }

    // 删除所有登录历史记录
    public async clearLoginHistory(): Promise<boolean> {
      let info = await this.apiProxy.CallApi('AccountHelperClearLoginHistory');
      if (!info?.bSucc) {
        return Promise.reject(info.error);
      }
      return (info.result as boolean) ?? false;
    }

    public async isSignIn(): Promise<boolean> {
        let info = await this.apiProxy.CallApi('AccountHelperIsSignIn');
        return (info.result as boolean) ?? false;
    }

    public async isVip(): Promise<boolean> {
        let info = await this.apiProxy.CallApi('AccountHelperIsVip');
        return (info.result as boolean) ?? false;
    }

    public async isSuperVip(): Promise<boolean> {
        let info = await this.apiProxy.CallApi('AccountHelperIsSuperVip');
        return (info.result as boolean) ?? false;
    }

    public async isPlatinumVip(): Promise<boolean> {
        let info = await this.apiProxy.CallApi('AccountHelperIsPlatinumVip');
        return (info.result as boolean) ?? false;
    }

    public async isYear(): Promise<boolean> {
        let info = await this.apiProxy.CallApi('AccountHelperIsYear');
        return (info.result as boolean) ?? false;
    }

    public async vasType(): Promise<string> {
        let info = await this.apiProxy.CallApi('AccountHelperVasType');
        if (info.bSucc) {
            return info.result as string;
        }
        return '';
    }

    public async vipLevel(): Promise<string> {
        let info = await this.apiProxy.CallApi('AccountHelperVipLevel');
        if (info.bSucc) {
            return info.result as string;
        }
        return '';
    }

    public attachEvent(event: AccountHelperEventKey, listener: any): void {
        this.apiProxy.AttachServerEvent(event, listener);
    }

    public detachEvent(event: AccountHelperEventKey, listener: any): void {
        this.apiProxy.DetachServerEvent(event, listener);
    }

    public async getUserInfo(): Promise<UserInfo> {
        let info = await this.apiProxy.CallApi('AccountHelperGetUserInfo');
        if (info.bSucc) {
            return info.result as UserInfo;
        }
        return {} as any;
    }
    public async getOldAccountSessionInfo(): Promise<SessionInfo> {
        let info = await this.apiProxy.CallApi('AccountHelperGetOldAccountSessionInfo');
        if (info.bSucc) {
            return info.result as SessionInfo;
        }
        return {} as any;
    }
    public async getBearerAuthorization(): Promise<string> {
        let info = await this.apiProxy.CallApi('AccountHelperGetBearerAuthorization');
        if (info.bSucc) {
            return info.result as string;
        }
        return '';
    }

    public async getDeviceID(): Promise<string> {
        let info = await this.apiProxy.CallApi('AccountHelperGetBearerAuthorization');
        if (info.bSucc) {
            return info.result as string;
        }
        return '';
    }

    public async syncClientIsConnected (): Promise<boolean> {
        let info = await this.apiProxy.CallApi('AccountHelperSyncClientIsConnected');
        if (info.bSucc) {
            return info.result as boolean;
        }
        return false;
    }

    public async syncClientSubscribe (params: ISubscribeParams): Promise<void> {
        await this.apiProxy.CallApi('AccountHelperSyncClientSubscribe', params);
    }

  public async syncClientUnsubscribe (params: ISubscribeParams): Promise<void> {
    await this.apiProxy.CallApi('AccountHelperSyncClientUnsubscribe', params);
  }
  
  public async translateResponseError(err: ResponseError): Promise<string> {
    let safeError: any = err;
    if (err instanceof ResponseError) {
        try {
            safeError = err.toJsonString()
        } catch (error) {
            //
        }
    }
    const result = await this.apiProxy.CallApi('AccountHelperTranslateResponseError', safeError)
    return result.result ?? '';
  }
}