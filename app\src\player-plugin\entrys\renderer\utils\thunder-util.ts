// =================================================================
// @description: 一些公用的功能函数导出
// @author:      maolanghua
// @date:        2018.04.02
// =================================================================
import * as path from 'path'
import { FileSystemAWNS } from '@root/common/fs-utilities';

const xmpTempSubPath: string = 'Thunder Network\\Xmp6\\';

// 去掉首位引号
function padQuot(temp: string): string {
  let ret: string = temp;
  if (temp.indexOf('"') === 0 && temp.lastIndexOf('"') === temp.length - 1) {
    ret = temp.substring(1, temp.length - 1);
  } else if (temp.indexOf("'") === 0 && temp.lastIndexOf("'") === temp.length - 1) {
    ret = temp.substring(1, temp.length - 1);
  }
  return ret;
}

export namespace ThunderUtil {
  // 文件大小数字->字符串的格式化
  export function formatSize(size: number, fractionDigits?: number): string {
    fractionDigits = fractionDigits ? fractionDigits : 2;
    let ret: string = '0B';
    if (typeof size === 'number' && size > 0) {
      let subfix: string[] = ['B', 'KB', 'MB', 'GB', 'TB'];
      let fixindex: number = 0;
      let remain: number = size;
      while (remain >= 1000) {
        if (fixindex >= 4) {
          break;
        }
        remain = remain / 1024;
        fixindex += 1;
      }

      if (String(remain).indexOf('.') === -1) {
        ret = remain + subfix[fixindex];
      } else {
        let sizestr: string = remain.toFixed(fractionDigits);
        ret = sizestr + subfix[fixindex];
      }
    }

    return ret;
  }

  /**
   * @description 字符是否为数字
   */
  export function isDigital(ch: string): boolean {
    let bDigital: boolean = false;
    if (/^\d+$/.test(ch)) {
      bDigital = true;
    }
    return bDigital;
  }

  /**
   * @description 字符是否为字母
   */
  export function isAlpha(ch: string): boolean {
    let bAlpha: boolean = false;
    if (/[A-Za-z]/.test(ch)) {
      bAlpha = true;
    }
    return bAlpha;
  }

  /**
   * @description 字符是否为大写字母
   */
  export function isUpperCase(ch: string): boolean {
    let ret: boolean = false;
    if (/[A-Z]/.test(ch)) {
      ret = true;
    }
    return ret;
  }

  /**
   * @description 字符是否为小写字母
   */
  export function isLowerCase(ch: string): boolean {
    let ret: boolean = false;
    if (/[a-z]/.test(ch)) {
      ret = true;
    }
    return ret;
  }

  /**
   * @description 字符是否为汉字
   */
  export function isChinese(ch: string): boolean {
    let ret: boolean = false;
    if (/[\u4E00-\u9FA5]/.test(ch)) {
      ret = true;
    }
    return ret;
  }

  /**
   * @description 替换非数字字符为空串
   */
  export function replaceNonDigital(value: string): string {
    let ret: string = value.replace(/[^\d]/g, '');
    return ret;
  }

  /**
   * @description 替换非英文字母为空串
   */
  export function replaceNonAlpha(value: string): string {
    let ret: string = value.replace(/[^A-Za-z]/g, '');
    return ret;
  }

  /**
   * @description 替换非单词字符为空串，非单词字符为 a-z A-Z 0-9 以及下划线
   */
  export function replaceNonWord(value: string): string {
    let ret: string = value.replace(/[^\W]/g, '');
    return ret;
  }

  /**
   * @description: 对象的深拷贝
   */
  export function deepCopy(obj: any): any {
    let str: string = JSON.stringify(obj);
    let result: any = null;
    try {
      result = JSON.parse(str);
    } catch (error) {
      console.log(error);
    }
    return result;
  }

  /**
   * @description 交换数组元素
   * @example 上移： swap(arr, index, index - 1) ; 下移： swap(arr, index, index + 1)
   */
  export function swap(arr: any[], index1: number, index2: number): any[] {
    do {
      if (index1 < 0 || index1 >= arr.length) {
        break;
      }
      if (index2 < 0 || index2 >= arr.length) {
        break;
      }
      if (index1 === index2) {
        break;
      }
      arr[index1] = arr.splice(index2, 1, arr[index1])[0];
    } while (0);
    return arr;
  }

  /**
   * @description 比较两个字符串是否相等，不区分大小写
   */
  export function compareNocase(str1: string, str2: string): boolean {
    let equal: boolean = false;
    do {
      if (str1 === undefined && str2 === undefined) {
        equal = true;
        break;
      }
      if (str1 === undefined || str2 === undefined) {
        break;
      }
      if (typeof str1 !== 'string' || typeof str2 !== 'string') {
        break;
      }
      equal = str1.toLowerCase() === str2.toLowerCase();
    } while (0);
    return equal;
  }

  /**
   * @description 解析命令行，类似于CommandLineToArgvW
   */
  export function parseCommandLine(cmdline: string): string[] {
    let tokenBegin: number = 0;
    let quot: string = '';
    let bQuotING: boolean = false; // 是否已经遍历到一个引号
    let ret: string[] = [];
    let len: number = cmdline.length;
    for (let i: number = 0; i < len; i++) {
      let char: string = cmdline[i];
      if (char === '"' || char === "'") {
        if (quot === '') {
          bQuotING = true;
          quot = char;
        } else if (quot === char) {
          bQuotING = false;
          quot = '';
        }
      }

      // 如果没有遇到引号，遇到了空格，则分隔参数
      if (char === ' ' && !bQuotING && i !== len - 1) {
        let temp: string = cmdline.substring(tokenBegin, i);
        if (temp.trim() !== '') {
          ret.push(padQuot(temp));
        }
        tokenBegin = i + 1;
      } else if (i === len - 1) {
        let temp: string = cmdline.substring(tokenBegin);
        if (temp.trim() !== '') {
          ret.push(padQuot(temp));
        }
      }
    }
    return ret;
  }

  export async function getXmpTempPath(bCreate?: boolean, subPath?: string): Promise<string> {
    const os: any = await import('os');
    let tempDir: string = path.join(os.tmpdir(), xmpTempSubPath);
    if (subPath) {
      tempDir = path.join(tempDir, subPath);
    }
    if (bCreate !== undefined && bCreate) {
      await FileSystemAWNS.mkdirsAW(tempDir);
    }

    return tempDir;
  }

  /**
   * 给链接添加 query string
   * @param url 链接
   * @param query query 参数
   */
  export function setQueryString(url: string, query: { [prop: string]: any }): string {
    Object.keys(query).forEach((key: string, index: number) => {
      url += index === 0 ? '?' : '&';
      url += `${key}=${encodeURIComponent(query[key])}`;
    });

    return url;
  }

  /**
   * @method getQueryString
   * @desc 获取链接参数的值
   * @param url {String} - 链接地址
   * @param name {String} - 参数名称
   * @returns {String} - 参数值
   */
  export function getQueryString(url: string, name: string): string {
    let ret: string = '';
    if (url && name) {
      ret = url.match(new RegExp(`(^${name}|[?|&]${name})=([^&#]+)`)) ? RegExp.$2 : '';
      try {
        ret = decodeURIComponent(ret);
      } catch (error) {
        //
      }
    }

    return ret;
  }

  // export function isClipboardTextFormatAvailable(): boolean {
  //   let ret: boolean = false;
  //   let formats: string[] = clipboard.availableFormats();
  //   for (let format of formats) {
  //     if (format === 'text/plain') {
  //       ret = true;
  //       break;
  //     }
  //   }
  //   return ret;
  // }

  /**
   * @description html关键字的高亮效果函数 叠加样式
   * @param: targetString 要高亮显示关键字的整句字符串
   * @param: searchString 待搜索的关键字
   * @param: bgColor 高亮的颜色效果RGB 如#FF0000
   */
  export function keywordsHighLight(targetString: string, searchString: string, bgColor: string): string {
    if (!targetString) {
      return '';
    }
    if (!searchString) {
      return targetString;
    }
    if (targetString.length === 0) {
      return targetString;
    }
    if (searchString.length === 0) {
      return targetString;
    }
    // 包含反斜杠的特殊处理,无法通过该关键字形成正则,或者关键字无法高亮显示
    let reg: RegExp = /\\/;
    let newSearchString: string[] = searchString.split(' ');
    newSearchString = newSearchString.filter(
      (el: string): boolean => {
        return el.trim().length > 0;
      }
    );
    if (newSearchString.length === 0) {
      return targetString;
    }

    for (let i: number = 0; i < newSearchString.length; i++) {
      if (newSearchString[i].search(reg) > 0) {
        return targetString;
      }
    }

    bgColor = bgColor === undefined || bgColor === null ? '#FF0000' : bgColor;
    let returnStr: string = '';

    let specialChars: string[] = ['\\[', '\\^', '\\*', '\\(', '\\)', '\\|', '\\?', '\\$', '\\.', '\\+'];

    let totalregx: string = '';
    let backchar: string = '|';
    for (let i: number = 0; i < newSearchString.length; i++) {
      for (let index: number = 0; index < specialChars.length; index++) {
        let reg: RegExp = new RegExp(specialChars[index], 'g');
        newSearchString[i] = newSearchString[i].replace(reg, specialChars[index]);
      }
      if (i === newSearchString.length - 1) {
        backchar = '';
      }
      totalregx = totalregx.concat(newSearchString[i], backchar);
    }
    let replaceReg: RegExp = new RegExp(totalregx, 'gi');
    returnStr = targetString.replace(
      replaceReg,
      (sub: string): string => {
        return '<span style= "color:' + bgColor + '">' + sub + '</span>';
      }
    );
    return returnStr;
  }

  export function convertTimeToMinutes(hour: number, minute: number, second: number): number {
    let seconds: number = 3600 * hour + 60 * minute + second;
    return seconds;
  }

  export function convertMinuteToTime(seconds: number): number[] {
    let hour: number = Math.floor(seconds / 3600);
    let minute: number = Math.floor((seconds / 60) % 60);
    let second: number = Math.floor(seconds % 60);
    return [hour, minute, second];
  }

  export function formatTimeNumber(nb: number): string {
    let ret: string = '00';
    if (nb >= 10) {
      ret = nb.toString();
    } else {
      ret = '0' + nb.toString();
    }
    return ret;
  }

  export function createGUID(): string {
    let format: string = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';
    let r: any;
    return format.replace(/[xy]/g, (c: any, v: any) => {
      (r = (Math.random() * 16) | 0), (v = c === 'x' ? r : (r & 0x3) | 0x8);
      return v.toString(16);
    });
  }

  export function formatDate(fmt: string, date: Date): string {
    let helpfmt: any = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'h+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds()
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (let k in helpfmt) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1 ? helpfmt[k] : ('00' + helpfmt[k]).substr(('' + helpfmt[k]).length)
        );
      }
    }
    return fmt;
  }

  export function formatMilliSecondTime2(time: number): string {
    time = Math.floor(time);
    let millisecond: string = (time % 1000).toString();
    if (millisecond.length === 1) {
      millisecond = '00' + millisecond;
    } else if (millisecond.length === 2) {
      millisecond = '0' + millisecond;
    }
    // 转换成秒
    time = Math.floor(time / 1000);
    let times: number[] = [];
    while (time > 0) {
      let temp: number = Math.floor(time / 60);
      times.push(time - temp * 60);
      time = temp;
      if (times.length >= 3) {
        break;
      }
    }

    let timeStr: string = '';
    if (times.length === 1) {
      // 只有秒
      timeStr = formatTimeNumber(times[0]) + '秒';
    } else if (times.length === 2) {
      // 只有分秒
      timeStr = formatTimeNumber(times[1]) + '分' + formatTimeNumber(times[0]) + '秒';
    } else if (times.length === 3) {
      timeStr =
        formatTimeNumber(times[2]) + '时' + formatTimeNumber(times[1]) + '分' + formatTimeNumber(times[0]) + '秒';
    } else {
      timeStr = `0秒`;
    }
    return timeStr;
  }

  export function formatMilliSecondTime(time: number, isMilliSecond?: boolean, sept: string = ':'): string {
    time = Math.floor(time);
    let millisecond: string = (time % 1000).toString();
    if (millisecond.length === 1) {
      millisecond = '00' + millisecond;
    } else if (millisecond.length === 2) {
      millisecond = '0' + millisecond;
    }
    // 转换成秒
    time = Math.floor(time / 1000);
    let times: number[] = [];
    while (time > 0) {
      let temp: number = Math.floor(time / 60);
      times.push(time - temp * 60);
      time = temp;
      if (times.length >= 3) {
        break;
      }
    }

    let timeStr: string = '';
    if (times.length === 1) {
      // 只有秒
      timeStr = `00${sept}00${sept}` + formatTimeNumber(times[0]);
    } else if (times.length === 2) {
      // 只有分秒
      timeStr = `00${sept}` + formatTimeNumber(times[1]) + `${sept}` + formatTimeNumber(times[0]);
    } else if (times.length === 3) {
      timeStr = formatTimeNumber(times[2]) + sept + formatTimeNumber(times[1]) + sept + formatTimeNumber(times[0]);
    } else {
      timeStr = `00${sept}00${sept}00`;
    }
    if (isMilliSecond) {
      timeStr += sept + millisecond;
    }
    return timeStr;
  }

  /**
   * @description 秒转换成时分秒
   */
  export function formatSeconds(senconds: number): string {
    let ret: string = '';
    do {
      if (senconds <= 0) {
        ret = '00分00秒';
        break;
      }

      let hours: number = Math.floor(senconds / 3600);
      let mins: number = Math.floor(senconds / 60) % 60;
      let secs: number = Math.floor(senconds % 60);
      if (hours > 0) {
        ret = hours < 10 ? '0' + hours + '时' : '' + hours + '时';
      }
      ret = ret + (mins < 10 ? '0' + mins + '分' : '' + mins + '分');
      ret = ret + (secs < 10 ? '0' + secs : '' + secs) + '秒';
    } while (0);

    return ret;
  }

  export function isDef(v: any): boolean {
    return v !== undefined && v !== null;
  }

  export function isUndef(v: any): boolean {
    return v === undefined || v === null;
  }

  export function setStyle(el: HTMLElement, style: CSSStyleDeclaration): void {
    Object.entries(style).forEach(
      ([prop, value]: any[]): void => {
        el.style[prop] = value;
      }
    );
  }

  export function setCSSProperties(el: HTMLElement, properties: { [name: string]: string }): void {
    Object.entries(properties).forEach(
      ([name, value]: string[]): void => {
        el.style.setProperty(name, value);
      }
    );
  }

  export function versionCompare(v1: string, v2: string): number {
    let v1Split: string[] = v1.split('.');
    let v2Split: string[] = v2.split('.');
    let result: number = 0;

    for (let i: number = 0; i < v1Split.length; i++) {
      if (Number(v1Split[i]) - Number(v2Split[i]) > 0) {
        result = 1;
        break;
      } else if (Number(v1Split[i]) - Number(v2Split[i]) < 0) {
        result = -1;
        break;
      }
    }

    return result;
  }

  // export function getElementRelative(element: HTMLElement): Point {
  //   let actualLeft: number = element.offsetLeft;
  //   let actualTop: number = element.offsetTop;
  //   let current: HTMLElement = element.offsetParent as HTMLElement;
  //   while (current !== null) {
  //     actualLeft += current.offsetLeft;
  //     actualTop += current.offsetTop;
  //     current = current.offsetParent as HTMLElement;
  //   }
  //   return {
  //     x: actualLeft,
  //     y: actualTop
  //   };
  // }

  export async function sleep(time: number): Promise<void> {
    await new Promise<void>(
      (res: any, rej: any): void => {
        setTimeout(res, time);
      }
    );
  }
}
