<script setup lang="ts">
import Button from '@root/common/components/ui/button/index.vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'

import { EPanPage, useHistoryStore } from '../../store/history-store'
import { BaseManager } from '../../manager/base-manager'
import { computed } from 'vue'
import { formatSize } from '@root/common/thunder-pan-manager/pan-sdk/utils/drive'
import { useDriveRouterStore } from '../../store/drive-router-store'

const { pushState } = useHistoryStore()
const { isInSafeBoxFolder } = useDriveRouterStore()

const baseAbout = BaseManager.getInstance().getAbout()

const spaceText = computed(() => {
  if (isInSafeBoxFolder.value) {
    return '保险箱空间：' + formatSize(baseAbout.safeBoxSpaceUsage, 1) + '/' + formatSize(baseAbout.safeBoxSpaceLimit, 1)
  }
  return '空间：' + formatSize(baseAbout.spaceUsage, 1) + '/' + formatSize(baseAbout.spaceLimit, 1)
})

const dropdownMenuList = [
  {
    key: 'share',
    label: '我的分享',
    icon: 'xl-icon-general-share-m',
  },
]

function handleDropdownMenuSelect(key: string) {
  if (key === 'share') {
    pushState({
      page: EPanPage.SHARE
    })
  }
}

function handleExpand() {
  window.__VueGlobalProperties__.$message({
    message: '尚未支持该功能，敬请期待',
    type: 'info'
  })
}
</script>

<template>
  <div class="header">
    <div class="left-title">
      <span class="title">云盘</span>
      <div class="space">
        <span>{{ spaceText }}</span>
        <span class="expand-btn" @click="handleExpand">扩展></span>
      </div>
    </div>

    <div class="right-operation">
      <DropdownMenu :items="dropdownMenuList" @select="handleDropdownMenuSelect" align="end">
        <Button variant="ghost" size="sm" :is-icon="true">
          <i class="xl-icon-more"></i>
        </Button>
      </DropdownMenu>
    </div>
  </div>
</template>

<style scoped lang="scss">
.header {
  width: 100%;
  height: 64px;
  padding: 11px 40px 11px 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;

  .left-title {
    display: flex;
    align-items: center;

    .title {
      font-size: 26px;
      font-weight: 700;
      padding: 4px;
    }

    .space {
      font-size: 12px;
      margin-left: 12px;
      margin-top: 10px;
      color: var(--font-font-3);

      .expand-btn {
        color: var(--primary-primary-default);
        cursor: pointer;
        margin-left: 4px;
      }
    }
  }
}
</style>
