<template>

  <div>

    <h1>Confirm Dialog</h1>
     <button @click="handleClose">关闭</button> <button @click="handleOk">确定</button> <button
      @click="handleCancel"
    >
       取消</button
    >
  </div>

</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue'
  import { PopUpNS } from '@root/common/pop-up'
  import * as PopUpTypes from '@root/common/pop-up/types'
  import { config } from '@root/common/config/config'

  onMounted(() => {
    ;(window as any).__config__ = config
  })

  const handleClose = async () => {
    const currentWindow = PopUpNS.getCurrentWindow()
    await currentWindow.close()
    // emit('close')
  }

  const handleOk = async () => {
    const currentWindow = PopUpNS.getCurrentWindow()
    await currentWindow.close(PopUpTypes.Action.OK, 'hello world')
  }

  const handleCancel = async () => {
    const currentWindow = PopUpNS.getCurrentWindow()
    await currentWindow.close(PopUpTypes.Action.Cancel)
  }
</script>

