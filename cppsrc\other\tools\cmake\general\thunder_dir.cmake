# usage: 
# SUBDIRLIST(SUBDIRS ${MY_CURRENT_DIR} false)
# FOREACH(subdir ${SUBDIRS})
  # do_some_thing
# ENDFOREACH()
MACRO(SUBDIRLIST result curdir abs)
	FILE(GLOB children RELATIVE ${curdir} ${curdir}/*)
	SET(dirlist "")
	FOREACH(child ${children})
		IF(IS_DIRECTORY ${curdir}/${child})
			if (true STREQUAL ${abs})
				LIST(APPEND dirlist ${curdir}/${child})
			else ()
				LIST(APPEND dirlist ${child})
			endif ()
		ENDIF()
	ENDFOREACH()
	SET(${result} ${dirlist})
ENDMACRO()

MACRO(FETCHMODULES paths files _current_dir)
	set (paths "")
	set (files "")
	FILE(GLOB_RECURSE children LIST_DIRECTORIES false ${_current_dir}/*.cmake)
	FOREACH(child ${children})
		get_filename_component(parent ${child} DIRECTORY)
		get_filename_component(name_without_extension ${child} NAME_WE)
		LIST(APPEND ${paths} ${parent})
		LIST(APPEND ${files} ${name_without_extension})
	ENDFOREACH()
	LIST(REMOVE_DUPLICATES ${paths})
	LIST(REMOVE_DUPLICATES ${files})
ENDMACRO()