<template>
  <div class="tree-demo">
    <h1>Tree 树形控件演示</h1>

    <!-- 基础用法 -->
    <section class="demo-section demo-section-1">
      <h2>基础用法</h2>
      <p>
        1最简单的树形控件，点击节点可以展开/收起子节点。注意：叶子节点（无子节点）不会显示展开图标
      </p>
      <div class="demo-wrapper">
        <Tree
          :data="basicTreeData"
          @node-click="handleNodeClick"
          @node-expanded="handleNodeExpanded"
          @node-collapsed="handleNodeCollapsed"
        />
      </div>
      <details class="code-details">
        <summary>查看代码</summary>
        <pre><code>&lt;Tree 
  :data="basicTreeData" 
  @node-click="handleNodeClick"
  @node-expanded="handleNodeExpanded"
  @node-collapsed="handleNodeCollapsed"
/&gt;</code></pre>
      </details>
    </section>

    <!-- 自定义图标 -->
    <section class="demo-section">
      <h2>自定义图标</h2>
      <p>为不同类型的节点自定义图标</p>
      <div class="demo-wrapper">
        <Tree
          :data="iconTreeData"
          @node-click="handleNodeClick"
        >
          <template #icon="{ node }">
            <i :class="getIconClass(node)"></i>
          </template>
        </Tree>
      </div>
    </section>

    <!-- 懒加载树 -->
    <section class="demo-section">
      <h2>懒加载树</h2>
      <p>点击节点时异步加载子节点数据</p>
      <div class="demo-wrapper">
        <Tree
          :data="lazyTreeData"
          @node-click="handleLazyNodeClick"
        >
          <template #node="{ node }">
            <div class="lazy-node">
              <span>{{ node.label }}</span>
              <span
                class="loading-text"
                v-if="node.loading"
              >
                加载中...
              </span>
            </div>
          </template>
        </Tree>
      </div>
    </section>

    <!-- 自定义节点内容 -->
    <section class="demo-section">
      <h2>自定义节点内容</h2>
      <p>完全自定义节点的渲染内容</p>
      <div class="demo-wrapper">
        <Tree
          :data="customContentTreeData"
          @node-click="handleNodeClick"
        >
          <template #node="{ node }">
            <div class="custom-node">
              <span class="node-title">{{ node.label }}</span>
              <span
                class="node-badge"
                v-if="node.badge"
              >
                {{ node.badge }}
              </span>
              <span
                class="node-size"
                v-if="node.size"
              >
                {{ formatFileSize(node.size) }}
              </span>
              <div class="node-actions">
                <button
                  class="action-btn"
                  @click.stop="editNode(node)"
                >
                  编辑
                </button>
                <button
                  class="action-btn danger"
                  @click.stop="deleteNode(node)"
                >
                  删除
                </button>
              </div>
            </div>
          </template>
        </Tree>
      </div>
    </section>

    <!-- 可搜索的树 -->
    <section class="demo-section">
      <h2>可搜索的树</h2>
      <p>提供搜索功能，可以快速定位节点</p>
      <div class="demo-wrapper">
        <div class="search-wrapper">
          <input
            class="search-input"
            v-model="searchKeyword"
            placeholder="搜索节点..."
            @input="handleSearch"
          />
          <button
            class="clear-btn"
            @click="clearSearch"
          >
            清空
          </button>
        </div>
        <Tree
          :data="filteredTreeData"
          @node-click="handleNodeClick"
        >
          <template #label="{ node }">
            <span v-html="highlightText(node.label, searchKeyword)"></span>
          </template>
        </Tree>
      </div>
    </section>

    <!-- 事件日志 -->
    <section class="demo-section">
      <h2>事件日志</h2>
      <div class="event-log">
        <h3>最近的事件:</h3>
        <ul>
          <li
            class="event-item"
            v-for="(event, index) in eventLog"
            :key="index"
          >
            <span class="event-time">{{ event.time }}</span>
            <span class="event-type">{{ event.type }}</span>
            <span class="event-detail">{{ event.detail }}</span>
          </li>
        </ul>
        <button
          class="clear-btn"
          @click="clearEventLog"
        >
          清空日志
        </button>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import Tree from './tree.vue'

// 基础树数据 - 包含不同类型的节点用于测试展开图标的显示逻辑
const basicTreeData = ref([
  {
    id: '1',
    label: '一级节点 1 (有子节点)',
    expanded: true,
    children: [
      {
        id: '1-1',
        label: '二级节点 1-1 (有子节点)',
        children: [
          {
            id: '1-1-1',
            label: '三级节点 1-1-1 (叶子节点)',
            children: [
              {
                id: '1-1-1-1',
                label: '四级节点 1-1-1-1 (叶子节点)',
                children: [
                  {
                    id: '1-1-1-1-1',
                    label: '五级节点 1-1-1-1-1 (叶子节点)',
                    children: [{ id: '1-1-1-1-1-1', label: '六级节点 1-1-1-1-1 (叶子节点)' }],
                  },
                ],
              },
              {
                id: '1-1-1-2',
                label: '四级节点 1-1-1-2 (叶子节点)',
              },
            ],
          },
          { id: '1-1-2', label: '三级节点 1-1-2 (叶子节点)' },
          { id: '1-1-3', label: '三级节点 1-1-3 (叶子节点)' },
        ],
      },
      {
        id: '1-2',
        label: '二级节点 1-2 (有子节点)',
        children: [
          { id: '1-2-1', label: '三级节点 1-2-1 (叶子节点)' },
          { id: '1-2-2', label: '三级节点 1-2-2 (叶子节点)' },
        ],
      },
      {
        id: '1-3',
        label: '二级节点 1-3 (叶子节点，无展开图标)',
      },
    ],
  },
  {
    id: '2',
    label: '一级节点 2 (有子节点)',
    children: [
      { id: '2-1', label: '二级节点 2-1 (叶子节点)' },
      { id: '2-2', label: '二级节点 2-2 (叶子节点)' },
    ],
  },
  {
    id: '3',
    label: '一级节点 3 (叶子节点，无展开图标)',
  },
  {
    id: '4',
    label: '一级节点 4 (空children数组，无展开图标)',
    children: [],
  },
])

// 带图标的树数据
const iconTreeData = ref([
  {
    id: 'icon-1',
    label: '我的文档',
    type: 'folder',
    expanded: true,
    children: [
      { id: 'icon-1-1', label: '工作报告.docx', type: 'word' },
      { id: 'icon-1-2', label: '项目计划.xlsx', type: 'excel' },
      { id: 'icon-1-3', label: '演示文稿.pptx', type: 'ppt' },
      { id: 'icon-1-4', label: '技术文档.pdf', type: 'pdf' },
      { id: 'icon-1-5', label: '说明文档.txt', type: 'text' },
    ],
  },
  {
    id: 'icon-2',
    label: '媒体文件',
    type: 'folder',
    children: [
      { id: 'icon-2-1', label: '头像.jpg', type: 'img' },
      { id: 'icon-2-2', label: '动画.gif', type: 'gif' },
      { id: 'icon-2-3', label: '教程视频.mp4', type: 'video' },
      { id: 'icon-2-4', label: '背景音乐.mp3', type: 'music' },
    ],
  },
  {
    id: 'icon-3',
    label: '压缩包',
    type: 'folder',
    children: [
      { id: 'icon-3-1', label: '项目源码.zip', type: 'zip' },
      { id: 'icon-3-2', label: '种子文件.torrent', type: 'bt' },
      { id: 'icon-3-3', label: '系统镜像.iso', type: 'iso' },
    ],
  },
  {
    id: 'icon-4',
    label: '程序文件',
    type: 'folder',
    children: [
      { id: 'icon-4-1', label: '安装程序.exe', type: 'exe' },
      { id: 'icon-4-2', label: '手机应用.apk', type: 'apk' },
      { id: 'icon-4-3', label: 'iOS应用.ipa', type: 'ipa' },
      { id: 'icon-4-4', label: 'macOS安装包.dmg', type: 'dmg' },
    ],
  },
  {
    id: 'icon-5',
    label: '开发文件',
    type: 'folder',
    children: [
      { id: 'icon-5-1', label: 'index.html', type: 'html' },
      { id: 'icon-5-2', label: 'script.js', type: 'code' },
      { id: 'icon-5-3', label: '字幕文件.srt', type: 'subtitle' },
      { id: 'icon-5-4', label: '未知文件', type: 'unknown' },
    ],
  },
])

// 自定义内容树数据
const customContentTreeData = ref([
  {
    id: 'custom-1',
    label: '项目根目录',
    badge: 'NEW',
    size: 1024000,
    children: [
      { id: 'custom-1-1', label: 'index.js', size: 2048 },
      { id: 'custom-1-2', label: 'style.css', size: 1024, badge: 'HOT' },
      { id: 'custom-1-3', label: 'config.json', size: 512 },
    ],
  },
  {
    id: 'custom-2',
    label: 'node_modules',
    size: 50240000,
    children: [
      { id: 'custom-2-1', label: 'vue', size: 1024000 },
      { id: 'custom-2-2', label: 'lodash', size: 512000 },
    ],
  },
])

// 懒加载树数据
const lazyTreeData = ref([
  { id: 'lazy-1', label: '动态节点 1', hasChildren: true },
  { id: 'lazy-2', label: '动态节点 2', hasChildren: true },
  { id: 'lazy-3', label: '静态节点 3' },
])

// 搜索相关
const searchKeyword = ref('')
const originalSearchTreeData = [
  {
    id: 'search-1',
    label: 'JavaScript',
    children: [
      { id: 'search-1-1', label: 'Vue.js' },
      { id: 'search-1-2', label: 'React' },
      { id: 'search-1-3', label: 'Angular' },
    ],
  },
  {
    id: 'search-2',
    label: 'CSS',
    children: [
      { id: 'search-2-1', label: 'Sass' },
      { id: 'search-2-2', label: 'Less' },
      { id: 'search-2-3', label: 'Stylus' },
    ],
  },
  {
    id: 'search-3',
    label: 'HTML',
    children: [
      { id: 'search-3-1', label: 'HTML5' },
      { id: 'search-3-2', label: 'WebComponents' },
    ],
  },
]

// 事件日志
const eventLog = ref([])

// 计算属性：过滤后的搜索树数据
const filteredTreeData = computed(() => {
  if (!searchKeyword.value) {
    return originalSearchTreeData
  }

  const filterNodes = nodes => {
    return nodes.reduce((acc, node) => {
      const matchesSearch = node.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
      const filteredChildren = node.children ? filterNodes(node.children) : []

      if (matchesSearch || filteredChildren.length > 0) {
        acc.push({
          ...node,
          children: filteredChildren,
          expanded: true, // 搜索时自动展开
        })
      }

      return acc
    }, [])
  }

  return filterNodes(originalSearchTreeData)
})

// 方法：添加事件日志
const addEventLog = (type, detail) => {
  eventLog.value.unshift({
    time: new Date().toLocaleTimeString(),
    type,
    detail,
  })
  if (eventLog.value.length > 10) {
    eventLog.value = eventLog.value.slice(0, 10)
  }
}

// 事件处理器
const handleNodeClick = node => {
  console.log('节点点击:', node)
  addEventLog('节点点击', `点击了节点: ${node.label}`)
}

const handleNodeExpanded = node => {
  console.log('节点展开:', node)
  addEventLog('节点展开', `展开了节点: ${node.label}`)
}

const handleNodeCollapsed = node => {
  console.log('节点折叠:', node)
  addEventLog('节点折叠', `折叠了节点: ${node.label}`)
}

// 懒加载处理
const handleLazyNodeClick = async node => {
  if (node.hasChildren && !node.children) {
    node.loading = true

    // 模拟异步加载
    await new Promise(resolve => setTimeout(resolve, 1000))

    node.children = [
      { id: `${node.id}-1`, label: `${node.label} - 子节点 1` },
      { id: `${node.id}-2`, label: `${node.label} - 子节点 2` },
      { id: `${node.id}-3`, label: `${node.label} - 子节点 3` },
    ]
    node.expanded = true
    node.loading = false

    addEventLog('懒加载', `加载了节点 ${node.label} 的子节点`)
  }
}

// 搜索处理
const handleSearch = () => {
  addEventLog('搜索', `搜索关键词: ${searchKeyword.value}`)
}

const clearSearch = () => {
  searchKeyword.value = ''
  addEventLog('搜索', '清空了搜索关键词')
}

// 工具方法
const getIconClass = node => {
  const iconMap = {
    folder: 'file-icon-type file-type-folder',
    word: 'file-icon-type file-type-word',
    excel: 'file-icon-type file-type-excel',
    ppt: 'file-icon-type file-type-ppt',
    pdf: 'file-icon-type file-type-pdf',
    text: 'file-icon-type file-type-text',
    img: 'file-icon-type file-type-img',
    gif: 'file-icon-type file-type-gif',
    video: 'file-icon-type file-type-video',
    music: 'file-icon-type file-type-music',
    zip: 'file-icon-type file-type-zip',
    bt: 'file-icon-type file-type-bt',
    iso: 'file-icon-type file-type-iso',
    exe: 'file-icon-type file-type-exe',
    apk: 'file-icon-type file-type-apk',
    ipa: 'file-icon-type file-type-ipa',
    dmg: 'file-icon-type file-type-dmg',
    html: 'file-icon-type file-type-html',
    code: 'file-icon-type file-type-code',
    subtitle: 'file-icon-type file-type-subtitle',
    unknown: 'file-icon-type file-type-unknown',
  }
  return iconMap[node.type] || 'file-icon-type file-type-default'
}

const formatFileSize = bytes => {
  if (bytes === 0) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + units[i]
}

const highlightText = (text, keyword) => {
  if (!keyword) return text
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const editNode = node => {
  alert(`编辑节点: ${node.label}`)
  addEventLog('编辑', `编辑了节点: ${node.label}`)
}

const deleteNode = node => {
  if (confirm(`确定要删除节点 "${node.label}" 吗？`)) {
    // 这里应该实现删除逻辑
    addEventLog('删除', `删除了节点: ${node.label}`)
  }
}

const clearEventLog = () => {
  eventLog.value = []
}
</script>

<style scoped>
@import '@root/common/assets/css/file-icon.css';

.tree-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background: #fafafa;
}

.demo-section h2 {
  margin-top: 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #1890ff;
  color: #333;
}

.demo-section p {
  margin-bottom: 16px;
  color: #666;
}

.demo-wrapper {
  max-height: 300px;
  padding: 16px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
}

.code-details {
  margin-top: 16px;
}

.code-details pre {
  padding: 12px;
  overflow-x: auto;
  border-radius: 4px;
  background: #f5f5f5;
}

.custom-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.node-title {
  flex: 1;
}

.node-badge {
  margin: 0 8px;
  padding: 2px 6px;
  border-radius: 10px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
}

.node-size {
  margin-right: 8px;
  color: #999;
  font-size: 12px;
}

.node-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  padding: 2px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  background: white;
  font-size: 12px;
  cursor: pointer;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.action-btn.danger:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.lazy-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-text {
  color: #1890ff;
  font-size: 12px;
}

.search-wrapper {
  display: flex;
  margin-bottom: 16px;
  gap: 8px;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.clear-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.clear-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.event-log {
  max-height: 200px;
  padding: 16px;
  overflow-y: auto;
  border-radius: 4px;
  background: #f5f5f5;
}

.event-log h3 {
  margin-top: 0;
  margin-bottom: 12px;
}

.event-log ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.event-item {
  display: flex;
  padding: 4px 0;
  gap: 12px;
  border-bottom: 1px solid #e6e6e6;
  font-size: 12px;
}

.event-time {
  min-width: 80px;
  color: #999;
}

.event-type {
  min-width: 80px;
  color: #1890ff;
  font-weight: bold;
}

.event-detail {
  color: #333;
}

:deep(mark) {
  padding: 1px 2px;
  background: #fffbe6;
  color: #d48806;
}

/* 调整树形控件中图标的大小 */
:deep(.td-tree-node__image-icon .file-icon-type) {
  width: 40px;
  height: 40px;
  zoom: 0.5;
  vertical-align: middle;
}
</style>
