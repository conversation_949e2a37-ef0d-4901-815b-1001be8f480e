import { ref, watch } from 'vue';
import * as BaseType from '@root/common/task/base';
import { TaskManager } from '@root/common/task/impl/task-manager';
import { useUserStore } from '@/stores/user'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import { taskExtraFunc } from '@/common/task-extra'
import { PopUpNS } from '@root/common/pop-up';
import * as PopUpTypes from '@root/common/pop-up/types';
import { DownloadModelManagerImpl } from '@root/common/ui-operation/impl/download-model'
import XMPMessage from '@root/common/components/ui/message';
import { REMIND_SETTING_NAME_MAP, getSettingConfig } from '@root/modal-renderer/src/views/setting';
import { useTaskErrorCode } from '@/stores/taskErrorCode';
import { useRouter } from 'vue-router';
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'


// import { downloadErrStr } from '@root/common/utils'

export const useMatchDownloads = () => {
  const downloadTasksList = ref<BaseType.IDownSimpleTaskInfo[]>([])
  const finishTasksList = ref<BaseType.IDownSimpleTaskInfo[]>([])

  const loginCategoryId = ref<number>(-1)

  const speedTotal = ref(0)

  const uerStore = useUserStore();
  const taskErrorCodeStore = useTaskErrorCode();

  const uidWatch = watch(() => uerStore.uid, async (newVal) => {
    if (newVal) {
      TaskManager.GetInstance().GetCategoryManager().setCurrentPanUserId(newVal)
      const category = await TaskManager.GetInstance().GetCategoryManager().getCurrentPanCategory()
      loginCategoryId.value = category?.getId() ?? -1
      const downloadView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Downloading)
      const completedView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Completed)
      const downloadIds = downloadView?.getTasks()
      const finishIds = completedView?.getTasks()
      if (downloadIds) {
        for (let id of downloadIds) {
          let task = await TaskManager.GetInstance().findTaskById(id);
          if (!task) continue
          const taskBase = task.getTaskBase();
          const changeBase = changeSingleTask(taskBase)
          downloadTasksList.value.push(changeBase)
        }
      }
      if (finishIds) {
        for (let id of finishIds) {
          let task = await TaskManager.GetInstance().findTaskById(id);
          if (!task) continue
          const taskBase = task.getTaskBase();
          const changeBase = changeSingleTask(taskBase)
          finishTasksList.value.push(changeBase)
        }
        completedView?.getTasks()
      }
    } else {
      loginCategoryId.value = -1
      downloadTasksList.value = downloadTasksList.value.filter(item => !item.isPanTask)
      finishTasksList.value = finishTasksList.value.filter(item => !item.isPanTask)
    }
  }, { immediate: true })
  /** 获取下载数量 */
  const getDownloadCount = async (task: BaseType.TaskBase): Promise<[number, number] | null> => {
    const isGroupTask = task.taskType === BaseType.TaskType.Group
    const isBtTask = task.taskType === BaseType.TaskType.Bt
    if (isGroupTask) {
      return Promise.all([
        taskExtraFunc.getGroupDownloadTotal(task.taskId),
        taskExtraFunc.getGroupDownloadCount(task.taskId)
      ])
    } else if (isBtTask){
      return Promise.all([
        taskExtraFunc.getDownloadTotal(task.taskId),
        taskExtraFunc.getDownloadCount(task.taskId)
      ])
    }
    return null
  }
  /** 任务状态发生变化获取正在下载数量 */
  const setDownloadCount = async () => {
    let speed = 0
    const category = await TaskManager.GetInstance().GetCategoryManager().getCategoryById(-1)
    const downloadCount = category?.getDownloadingTaskCount()
    const downloadSpeed = category?.getDownloadTotalSpeed() // 获取速度
    speed += downloadSpeed || 0
    console.log('>>>>>>>> downloadCount', downloadCount)
    // sidebarInfoStore.setDownloadByKey(IDownloadCountInfoType.downCount, downloadCount || 0)
    if (loginCategoryId.value !== -1) {
      const category = await TaskManager.GetInstance().GetCategoryManager().getCategoryById(loginCategoryId.value)
      const panDownCount = category?.getDownloadingTaskCount()
      const panDownloadSpeed =category?.getDownloadTotalSpeed()
      speed += panDownloadSpeed || 0
      console.log('>>>>>>>> panDownCount', downloadCount)
      // sidebarInfoStore.setDownloadByKey(IDownloadCountInfoType.panDownCount, panDownCount || 0)
    }
    speedTotal.value = speed
  }
  /** 跟新任务数据 */
  const updateTaskData = async (taskId: number, flags: number) => {
    let task = await TaskManager.GetInstance().findTaskById(taskId);
    if (!task) return
    const taskBase = task.getTaskBase()
    // console.log('>>>>>>>>>>>>>>>>> taskBase', taskBase)
    const changeTaskBase = changeSingleTask(taskBase)
    if (taskBase.taskStatus === BaseType.TaskStatus.Succeeded) {
      const index = finishTasksList.value.findIndex(item => item.taskId === taskId)
      index > -1 && (finishTasksList.value[index] = changeTaskBase)
    } else {
      const index = downloadTasksList.value.findIndex(item => item.taskId === taskId)
      index > -1 && (downloadTasksList.value[index] = changeTaskBase)
    }
  }

  const updateTaskStatus = async (taskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
    const task = await TaskManager.GetInstance().findTaskById(taskId);
    if (!task) return;
    const taskBase = task.getTaskBase();
    // setDownloadCount()
    if (taskBase.taskStatus === BaseType.TaskStatus.Succeeded) {
      const index = finishTasksList.value.findIndex(item => item.taskId === taskId)
      index > -1 && (finishTasksList.value[index].taskStatus = eNew)
    } else {
      const index = downloadTasksList.value.findIndex(item => item.taskId === taskId)
      index > -1 && (downloadTasksList.value[index].taskStatus = eNew)
    }
    downloadInform(taskBase, eNew)
  }
  
  /** 下载通知处理 */
  const downloadInform = async (taskBase: BaseType.TaskBase, eNew: BaseType.TaskStatus) => {
    const isBackgroundTask = taskBase.background
    console.log('>>>>>>>>>>>>>> isBackgroundTask', isBackgroundTask)
    if (taskBase.taskType === BaseType.TaskType.Magnet || isBackgroundTask) {
      // 磁力任务和后台任务不展示下面的成功失败弹窗
      return
    }
    if (eNew === BaseType.TaskStatus.Failed) {
      const remindSetting = await getSettingConfig(REMIND_SETTING_NAME_MAP.FailSuggest, true)
      // 如果设置不提醒，则不展示
      if (!remindSetting) {
        return
      }
      const fileIcon = TaskUtilHelper.getTaskIcon(taskBase.taskName || '', taskBase.taskType)
      const errorCode = taskErrorCodeStore.getTaskErrorInfoByCode(taskBase.errorCode)
      PopUpNS.showDownloadTaskMessageDlg({
        title: '下载出错',
        taskName: taskBase.taskName || '',
        infoText: errorCode,
        infoType: 'error',
        fileIconClass: fileIcon,
        confirmText: '立即查看',
        showCancel: false,
        task: taskBase
      })
    } 
    if (eNew === BaseType.TaskStatus.Succeeded) {
      const remindSetting = await getSettingConfig(REMIND_SETTING_NAME_MAP.Finish, true)
      console.log('>>>>>>>>>>>>>> remindSetting', remindSetting)
      // 如果设置不提醒，则不展示
      if (!remindSetting) {
        return
      }
      const fileType = TaskUtilHelper.getFileType(taskBase.taskName || '');
      const isPlayable = fileType === 'video' || fileType === 'audio'
      const fileIcon = TaskUtilHelper.getTaskIcon(taskBase.taskName || '', taskBase.taskType)
      const taskSize = TaskUtilHelper.formatFileSize(taskBase.fileSize || 0)
      const counts = await getDownloadCount(taskBase)
      let infoText = ''
      if (counts) {
        infoText = `已选${counts[1]}/${counts[0]} 个文件 · ${taskSize}`
      } else {
        infoText = `${taskSize}`
      }

      PopUpNS.showDownloadTaskMessageDlg({
        title: '下载完成',
        taskName: taskBase.taskName || '',
        infoText: infoText,
        fileIconClass: fileIcon,
        infoType: 'success',
        confirmText: isPlayable ? '立即播放' : '打开文件',
        cancelText: '打开文件夹',
        showCancel: true,
        task: taskBase
      })

    }
  }
  // 任务详情变更
  TaskManager.GetInstance().attachTaskDetailChangeEvent(updateTaskData);

  // 任务状态变更
  TaskManager.GetInstance().attachTaskStatusChangeEvent(updateTaskStatus);

  async function installTaskListener (
    categoryId: number,
    viewId: BaseType.CategoryViewID,
    reason: BaseType.TaskInsertReason,
    ids: number[]
  ) {
    if (![loginCategoryId.value, -1].includes(categoryId)) { return }
    // 添加任务展示消息
    if (reason === BaseType.TaskInsertReason.Create) {
      // 创建成功事件监听
      // TODO: 若窗口在顶层，调用正确的方法将窗口置顶
      XMPMessage({
        message: '已加入下载列表',
        type: 'success',
        duration: 2000,
      });
    }
    try {
      for (let id of ids) {
        let task = await TaskManager.GetInstance().findTaskById(id);
        if (!task) continue
        const taskBase = task.getTaskBase();
        if (reason === BaseType.TaskInsertReason.Create) {
          // 创建成功事件监听
          DownloadModelManagerImpl.GetInstance().positionDownloadTask({taskId: id, isPassive: false})
        }
        const changeTaskBase = changeSingleTask(taskBase)
        if (viewId === BaseType.CategoryViewID.Downloading) {
          const index = downloadTasksList.value.findIndex(item => item.taskId === taskBase.taskId)
          if (index > -1) {
            downloadTasksList.value.splice(index, 1, changeTaskBase);
          } else {
            downloadTasksList.value.push(changeTaskBase);
          }
        } else if (viewId === BaseType.CategoryViewID.Completed) {
          const index = finishTasksList.value.findIndex(item => item.taskId === taskBase.taskId);
          if (index > -1) {
            finishTasksList.value.splice(index, 1, changeTaskBase);
          } else {
            finishTasksList.value.push(changeTaskBase);
          }
        }
      }
    } catch (error) {
      console.log('>>>>attachInsertEvent error', error)
    }
  }

  async function removeTaskListener (categoryId: number, viewId: BaseType.CategoryViewID, ids: number[]) {
    for (let id of ids) {
      const task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) continue
      const taskBase = task.getTaskBase();
      const isPanTask = task.isPanTask()
      console.log('>>>>>>>>>>>>>>>>> 移除', isPanTask)
      let index = -1
      if (viewId === BaseType.CategoryViewID.Downloading) {
        const index = downloadTasksList.value.findIndex(item => item.taskId === id);
        if (index > -1) {
          downloadTasksList.value.splice(index, 1)
        }
      } else if (viewId === BaseType.CategoryViewID.Completed) {
        const index = finishTasksList.value.findIndex(item => item.taskId === id);
        if (index > -1) {
          finishTasksList.value.splice(index, 1)
        }
      };
    }
  }

  /** 新建任务、从新下载、任务下载完成、任务删除到回收站、从回收站恢复任务，都会触发此事件 */
  let categoryManager = TaskManager.GetInstance().GetCategoryManager();
  categoryManager.attachInsertEvent(async (
    categoryId: number,
    viewId: BaseType.CategoryViewID,
    reason: BaseType.TaskInsertReason,
    ids: number[]
  ) => {
    console.log('>>>>>>> install task[categoryId, viewId, reason, ids]', categoryId, viewId, reason, ids)
    if (reason === BaseType.TaskInsertReason.LoadTaskBasic) { return }

    installTaskListener(categoryId, viewId, reason, ids)
  });

  categoryManager.attachRemoveEvent(async (categoryId: number, viewId: BaseType.CategoryViewID, ids: number[]) => {
    // 新建任务、从新下载、任务下载完成、任务删除到回收站、从回收站恢复任务，都会触发此事件
    console.log('task remove from some categroy view categoryid=', categoryId, ',viewId=', viewId, ',ids=', ids);
    removeTaskListener(categoryId, viewId, ids)
  });

  /** 下载中列表任务 */
  const getDownloadTasksList = async () => {
    // TODO: 云盘  setCurrentPanUserId  getCurrentPanCategory
    let category = await categoryManager.getCategoryById(-1);
    // 下载tab中的任务
    let downloadView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Downloading);
    const downloadIds = downloadView!.getTasks();
    for (let id of downloadIds) {
      let task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) continue
      const taskBase = task.getTaskBase();
      const changeTaskBase = changeSingleTask(taskBase)
      downloadTasksList.value.push(changeTaskBase)
      console.log('load task, download view,task name=', taskBase);
    }
  }

  /** 下载完成列表任务 */
  const getFinishTasksList = async () => {
    console.log('>>>>>>>>>>>>>> finishTasksList')
    // 加载任务
    try {
      const category = await categoryManager.getCategoryById(-1);
      console.log('>>>>>>>>>>>>> category', category)
      const completeView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Completed);
      console.log('>>>>>>>>>>>>>>> completeView', completeView)
      const finishIds = completeView!.getTasks();
      console.log('>>>>> 总任务数', finishIds.length)
      console.log('>>>>>>>>>>>> finishIds', finishIds)
      for (let finishId of finishIds) {
        const task = await TaskManager.GetInstance().findTaskById(finishId);
        if (!task) continue
        let taskBase = task.getTaskBase();
        console.log('>>>> finishTasksList', taskBase);
        const changeTaskBase = changeSingleTask(taskBase)
        finishTasksList.value.push(changeTaskBase)
      }
    } catch (error) {
      console.log('> getFinishTasksList error', error)
    }
  }

  /** 暂停所有任务 */
  const stopAllTask = async () => {
    // 需要暂停云盘任务
    if(loginCategoryId.value !== -1) {
      let categoryPan = await categoryManager.getCategoryById(loginCategoryId.value);
      // 下载tab中的任务
      console.log('>>>>>>>>>>>>> categoryPan', categoryPan)
      let downloadPPanView = categoryPan!.getCategoryViewFromId(BaseType.CategoryViewID.Downloading);
      downloadPPanView && downloadPPanView.stopAllTask()
    }
    let category = await categoryManager.getCategoryById(-1);
    // 下载tab中的任务
    console.log('>>>>>>>>>>>>> category', category)
    let downloadView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Downloading);
    downloadView && downloadView.stopAllTask()
  }

  /** 开始所有下载任务 */
  const startAllTask = async () => {
    // 需要开始云盘任务
    if(loginCategoryId.value !== -1) {
      let categoryPan = await categoryManager.getCategoryById(loginCategoryId.value);
      // 下载tab中的任务
      console.log('>>>>>>>>>>>>> categoryPan', categoryPan)
      let downloadPanView = categoryPan!.getCategoryViewFromId(BaseType.CategoryViewID.Downloading);
      downloadPanView && downloadPanView.startAllTask()
    }
    let category = await categoryManager.getCategoryById(-1);
    // 下载tab中的任务
    console.log('>>>>>>>>>>>>> category', category)
    let downloadView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Downloading);
    downloadView && downloadView.startAllTask()
  }

  function changeSingleTask(taskBase: BaseType.TaskBase): BaseType.IDownSimpleTaskInfo {
    return {
      taskId: taskBase.taskId,
      taskName: taskBase.taskName || '',
      taskStatus: taskBase.taskStatus,
      taskType: taskBase.taskType,
      fileSize: taskBase.fileSize || 0,
      downloadSize: taskBase.downloadSize || 0,
      downloadSpeed: taskBase.downloadSpeed || 0,
      createTime: taskBase.createTime,
      completionTime: taskBase.completionTime,
      errorCode: taskBase.errorCode,
      savePath: taskBase.savePath,
      url: taskBase.url,
      isPanTask: taskBase.isPanTask,
      downloadSubTask: taskBase.downloadSubTask,
    }
  }

  // =============set==============
  const createTask = async () => {
    ThunderNewTaskHelperNS.showNewTaskWindow();
  }

  getDownloadTasksList()
  getFinishTasksList()

  return {
    getDownloadTasksList,
    getFinishTasksList,
    stopAllTask,
    startAllTask,
    finishTasksList,
    downloadTasksList,
    createTask,
  }
}