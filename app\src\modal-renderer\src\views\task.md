# Thunder 2025 下载任务模块（Task Module）文档

## 1. 模块概述

Thunder 2025 下载任务模块是整个迅雷下载引擎的核心组件，负责管理各类下载任务的创建、暂停、恢复、删除等生命周期操作。该模块提供了丰富的API，支持多种下载协议，包括P2SP（迅雷专有协议）、BT（BitTorrent）、eMule（电驴）、磁力链接等，并支持任务分组管理。

## 2. 目录结构

```
app/src/common/task/
├── base.ts                 # 基础类型定义和接口声明
├── call-api-impl.ts        # IPC通信实现
├── client/                 # 客户端实现
│   ├── task-manager.ts     # 任务管理器客户端
│   ├── task.ts             # 基础任务类客户端
│   ├── p2sp-task.ts        # P2SP任务客户端
│   ├── bt-task.ts          # BT任务客户端
│   ├── group-task.ts       # 任务组客户端
│   ├── emule-task.ts       # 电驴任务客户端
│   └── dk-helper.ts        # 下载内核辅助工具
├── impl/                   # 具体实现
│   ├── task-manager.ts     # 任务管理器实现
│   ├── task.ts             # 基础任务类实现
│   ├── p2sp-task.ts        # P2SP任务实现
│   ├── bt-task.ts          # BT任务实现
│   ├── bt-file.ts          # BT文件实现
│   ├── group-task.ts       # 任务组实现
│   ├── emule-task.ts       # 电驴任务实现
│   ├── category.ts         # 分类实现
│   ├── category-manager.ts # 分类管理器实现
│   ├── category-view.ts    # 分类视图实现
│   └── dk-helper.ts        # 下载内核辅助工具实现
└── server/                 # 服务端实现
    ├── server.ts           # 服务器入口
    ├── task-manager.ts     # 任务管理器服务端
    ├── task.ts             # 任务服务端
    └── dk-helper.ts        # 下载内核辅助工具服务端
```

## 3. 核心组件

### 3.1 任务管理器（TaskManager）

任务管理器是整个下载模块的核心，负责管理所有下载任务，提供统一的任务创建、查询、删除等功能，同时提供事件监听机制。

**主要功能**：

- 创建各类下载任务
- 批量操作任务（暂停、删除、回收）
- 查找重复任务
- 任务事件监听（状态变化、详情变化等）
- 设置全局下载参数（最大下载任务数等）

### 3.2 基础任务类（Task）

所有类型的下载任务的基类，提供通用的任务操作接口。

**主要功能**：

- 开始/暂停任务
- 删除/回收任务
- 重命名任务
- 重新下载任务
- 转换为具体任务类型（P2SP、BT等）

### 3.3 各类具体任务

#### 3.3.1 P2SP任务（P2spTask）

迅雷特有的多源下载协议任务，支持HTTP、FTP等基础协议，并通过P2P加速。

**特有功能**：

- 添加HTTP头部信息
- 设置下载线程数

#### 3.3.2 BT任务（BtTask）

BitTorrent协议下载任务，支持种子文件和磁力链接。

**特有功能**：

- 选择下载文件
- 获取种子文件信息
- 管理BT子文件状态

#### 3.3.3 任务组（GroupTask）

将多个相关任务组合在一起管理的任务集合。

**特有功能**：

- 添加/移除子任务
- 批量管理子任务状态

#### 3.3.4 电驴任务（EmuleTask）

支持电驴（eMule）协议的下载任务。

**特有功能**：

- 管理电驴特有资源

## 4. 使用示例

### 4.1 初始化任务管理器

```typescript
import { TaskManager } from 'app/src/common/task/client/task-manager'

// 获取任务管理器单例
const taskManager = TaskManager.GetInstance()

// 初始化任务管理器
await taskManager.init()
```

### 4.2 创建P2SP下载任务（HTTP/FTP下载）

```typescript
import * as BaseType from 'app/src/common/task/base'
import { TaskManager } from 'app/src/common/task/client/task-manager'

// 获取任务管理器单例
const taskManager = TaskManager.GetInstance()

// 创建P2SP任务（HTTP下载）
const newTaskInfo: BaseType.NewTaskSet = {
  taskInfo: {
    taskType: BaseType.TaskType.P2sp,
    taskBaseInfo: {
      savePath: 'D:/Downloads',
      taskName: '示例文件.zip',
    },
  },
  p2spInfo: {
    url: 'https://example.com/file.zip',
    refUrl: 'https://example.com', // 引用页面，有些网站需要
    cookie: 'session=xxx', // 如需要Cookie验证
    userAgent: 'Mozilla/5.0...', // 自定义User-Agent
  },
}

// 创建任务
const task = await taskManager.createTask(newTaskInfo)
if (task) {
  // 立即开始下载
  await task.start()

  // 获取P2SP特有功能
  const p2spTask = task.toP2spTask()

  // 添加自定义HTTP头
  p2spTask.addHttpHeaders('Authorization', 'Bearer token123')
}
```

### 4.3 创建BT下载任务

```typescript
import * as BaseType from 'app/src/common/task/base'
import { DkHelper } from 'app/src/common/task/client/dk-helper'
import { TaskManager } from 'app/src/common/task/client/task-manager'

// 获取任务管理器单例
const taskManager = TaskManager.GetInstance()

// 首先解析种子文件
const torrentPath = 'D:/Downloads/example.torrent'
const btInfo = await DkHelper.parseTorrent(torrentPath)

if (btInfo) {
  // 创建BT任务
  const newTaskInfo: BaseType.NewTaskSet = {
    taskInfo: {
      taskType: BaseType.TaskType.Bt,
      taskBaseInfo: {
        savePath: 'D:/Downloads',
        taskName: btInfo.title,
      },
    },
    btInfo: {
      seedFile: torrentPath,
      displayName: btInfo.title,
      fileRealIndexLists: btInfo.fileLists.map((_, index) => index), // 下载所有文件
      fileLists: btInfo.fileLists,
      infoId: btInfo.infoId,
      tracker: btInfo.trackerUrls.join(','),
    },
  }

  // 创建任务
  const task = await taskManager.createTask(newTaskInfo)
  if (task) {
    // 转换为BT任务
    const btTask = task.toBtTask()

    // 开始下载
    await task.start()

    // 监听BT子文件状态变化
    taskManager.attachBtSubTaskStatusChangeEvent((task, fileIndex, oldStatus, newStatus) => {
      console.log(`BT子文件 ${fileIndex} 状态变化: ${oldStatus} -> ${newStatus}`)
    })
  }
}
```

### 4.4 创建磁力链接下载任务

```typescript
import * as BaseType from 'app/src/common/task/base'
import { DkHelper } from 'app/src/common/task/client/dk-helper'
import { TaskManager } from 'app/src/common/task/client/task-manager'

// 获取任务管理器单例
const taskManager = TaskManager.GetInstance()

// 磁力链接
const magnetUrl = 'magnet:?xt=urn:btih:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'

// 创建磁力链接任务
const newTaskInfo: BaseType.NewTaskSet = {
  taskInfo: {
    taskType: BaseType.TaskType.Magnet,
    taskBaseInfo: {
      savePath: 'D:/Downloads',
      taskName: '未命名磁力任务',
    },
  },
  magnetInfo: {
    url: magnetUrl,
    torrentFilePath: 'D:/Downloads/Torrents/', // 种子保存路径
  },
}

// 创建任务
const task = await taskManager.createTask(newTaskInfo)
if (task) {
  // 开始下载
  await task.start()
}
```

### 4.5 创建任务组

```typescript
import * as BaseType from 'app/src/common/task/base'
import { TaskManager } from 'app/src/common/task/client/task-manager'

// 获取任务管理器单例
const taskManager = TaskManager.GetInstance()

// 创建任务组
const newTaskInfo: BaseType.NewTaskSet = {
  taskInfo: {
    taskType: BaseType.TaskType.Group,
    taskBaseInfo: {
      savePath: 'D:/Downloads',
      taskName: '资源合集',
    },
  },
  groupInfo: [
    {
      baseInfo: {
        taskType: BaseType.TaskType.P2sp,
        taskBaseInfo: {
          savePath: 'D:/Downloads',
          taskName: '文件1.zip',
        },
      },
      taskType: BaseType.TaskType.P2sp,
      p2spInfo: {
        url: 'https://example.com/file1.zip',
      },
    },
    {
      baseInfo: {
        taskType: BaseType.TaskType.P2sp,
        taskBaseInfo: {
          savePath: 'D:/Downloads',
          taskName: '文件2.zip',
        },
      },
      taskType: BaseType.TaskType.P2sp,
      p2spInfo: {
        url: 'https://example.com/file2.zip',
      },
    },
  ],
}

// 创建任务
const task = await taskManager.createTask(newTaskInfo)
if (task) {
  // 转换为任务组
  const groupTask = task.toGroupTask()

  // 开始下载
  await task.start()

  // 监听任务组子任务状态变化
  taskManager.attachGroupSubTaskStatusChangeEvent((subTask, groupTaskId, oldStatus, newStatus) => {
    console.log(`任务组子任务 ${subTask.getId()} 状态变化: ${oldStatus} -> ${newStatus}`)
  })
}
```

### 4.6 任务操作与状态监控

```typescript
import * as BaseType from 'app/src/common/task/base'
import { TaskManager } from 'app/src/common/task/client/task-manager'

// 获取任务管理器单例
const taskManager = TaskManager.GetInstance()

// 监听任务状态变化
taskManager.attachTaskStatusChangeEvent((task, oldStatus, newStatus) => {
  console.log(
    `任务 ${task.getId()} 状态变化: ${BaseType.TaskStatus[oldStatus]} -> ${BaseType.TaskStatus[newStatus]}`
  )

  // 下载完成时的操作
  if (newStatus === BaseType.TaskStatus.Succeeded) {
    console.log(`任务 ${task.getId()} 下载完成!`)
  }

  // 下载失败时的操作
  if (newStatus === BaseType.TaskStatus.Failed) {
    console.log(`任务 ${task.getId()} 下载失败!`)
  }
})

// 监听任务详情变化
taskManager.attachTaskDetailChangeEvent((task, flags) => {
  // 判断变化的是什么属性
  if (flags & BaseType.TaskDetailChangedFlags.TaskSpeed) {
    console.log(`任务 ${task.getId()} 速度发生变化`)
  }

  if (flags & BaseType.TaskDetailChangedFlags.Progress) {
    console.log(`任务 ${task.getId()} 进度发生变化`)
  }
})

// 暂停任务
async function pauseTask(taskId: number) {
  const task = new Task(taskManager.apiProxy, taskId)
  await task.stop(BaseType.TaskStopReason.Manual)
}

// 恢复任务
async function resumeTask(taskId: number) {
  const task = new Task(taskManager.apiProxy, taskId)
  await task.start()
}

// 删除任务（保留文件）
async function deleteTaskKeepFiles(taskId: number) {
  const task = new Task(taskManager.apiProxy, taskId)
  await task.deleteTask(false)
}

// 删除任务（同时删除文件）
async function deleteTaskWithFiles(taskId: number) {
  const task = new Task(taskManager.apiProxy, taskId)
  await task.deleteTask(true)
}

// 批量操作任务
async function batchOperateTasks(taskIds: number[]) {
  // 批量暂停
  taskManager.batchStopTasks(taskIds, BaseType.TaskStopReason.Manual)

  // 批量删除（保留文件）
  taskManager.batchDeleteTasks(taskIds, false)

  // 批量移入回收站
  taskManager.batchRecycleTasks(taskIds)
}
```

### 4.7 设置全局参数

```typescript
import { TaskManager } from 'app/src/common/task/client/task-manager'

// 获取任务管理器单例
const taskManager = TaskManager.GetInstance()

// 设置最大同时下载任务数
taskManager.updateMaxDownloadTaskCount(5)

// 设置全局扩展信息
taskManager.setGlobalExtInfo('{"customSetting":"value"}', false)

// 设置用户信息（用于VIP加速等）
taskManager.setUserInfo('user123', 'jumpKey456')
```

## 5. 事件系统

任务管理器提供了丰富的事件监听机制，用于响应任务状态变化。

### 5.1 可用事件

| 事件名称                            | 说明                 | 回调参数                                  |
| ----------------------------------- | -------------------- | ----------------------------------------- |
| TaskManagerTaskStatusChange         | 任务状态变化         | (task, oldStatus, newStatus)              |
| TaskManagerTaskDetailChange         | 任务详情变化         | (task, flags)                             |
| TaskManagerGroupSubTaskDetailChange | 任务组子任务详情变化 | (task, groupTaskId, flags)                |
| TaskManagerGroupSubTaskStatusChange | 任务组子任务状态变化 | (task, groupTaskId, oldStatus, newStatus) |
| TaskManagerTaskDeleted              | 任务被删除           | (taskId)                                  |
| TaskManagerBtSubTaskDetailChange    | BT子文件详情变化     | (task, fileIndex, flags)                  |
| TaskManagerBtSubTaskStatusChange    | BT子文件状态变化     | (task, fileIndex, oldStatus, newStatus)   |

### 5.2 事件使用示例

```typescript
import * as BaseType from 'app/src/common/task/base'
import { TaskManager } from 'app/src/common/task/client/task-manager'

// 获取任务管理器单例
const taskManager = TaskManager.GetInstance()

// 任务状态变化事件
const onTaskStatusChange = (task, oldStatus, newStatus) => {
  console.log(
    `任务状态变化: ${BaseType.TaskStatus[oldStatus]} -> ${BaseType.TaskStatus[newStatus]}`
  )
}

// 注册事件
taskManager.attachTaskStatusChangeEvent(onTaskStatusChange)

// 取消注册事件
taskManager.detachTaskStatusChangeEvent(onTaskStatusChange)
```

## 6. 技术架构

Thunder 2025 下载模块采用分层架构设计：

1. **客户端层（client/）**：提供给应用程序调用的API接口，负责与服务端通信。
2. **实现层（impl/）**：具体功能实现，包含各类任务的实际操作逻辑。
3. **服务端层（server/）**：提供IPC服务，接收客户端请求并调用对应的实现。
4. **基础定义（base.ts）**：定义各种类型、枚举和接口，确保整个系统类型安全。
5. **IPC通信（call-api-impl.ts）**：实现客户端与服务端之间的进程间通信。

该架构设计使得下载引擎可以运行在独立进程中，提高系统稳定性和安全性。

## 7. 任务列表UI组件核心算法分析

### 7.1 parseMultipleTasksFileList 函数概述

`parseMultipleTasksFileList` 函数是 TaskList 组件中的核心算法，负责将多个不同类型的下载任务数据（磁力链、P2SP、Ed2k）解析并构建成统一的树形结构，用于在UI界面中展示文件列表。

**主要功能：**

- 支持多任务并行处理（磁力链 + P2SP + Ed2k）
- 动态构建树形文件结构
- 处理任务状态变化（解析中、解析失败、解析成功）
- 维护节点映射关系和选中状态

### 7.2 函数流程分析

#### 7.2.1 初始化阶段

```typescript
// 1. 清空映射表
branchMap.value = {} // 分支节点映射表（目录）
leafMap.value = {} // 叶子节点映射表（文件）
multiTaskFilesCache.value = {} // 多任务文件缓存
multiTaskCheckedKeys.value = {} // 多任务选中状态

// 2. 创建根节点
setBranch('', '', defaultTaskType, 0)
```

#### 7.2.2 任务预处理阶段

```typescript
// 3. 验证和预处理任务数据
taskInfoArray?.forEach((task, index) => {
  console.log(`任务${index}预览:`, {
    title: task?.title,
    taskId: task?.infoId,
    status: task?.status,
    taskType: task?.taskType || defaultTaskType,
    fileCount: task?.fileLists?.length || 0,
    hasFiles: !!(task?.fileLists && task.fileLists.length > 0),
    url: task?.url,
  })
})
```

#### 7.2.3 核心处理循环

```typescript
// 4. 遍历每个任务进行处理
taskInfoArray.forEach((taskData, taskIndex) => {
  // 4.1 提取任务基本信息
  const currentTaskId = taskData.infoId || `task-${taskIndex}`
  const taskTitle = taskData.title || `任务 ${taskIndex + 1}`
  const currentTaskType = taskData.taskType !== undefined ? taskData.taskType : defaultTaskType

  // 4.2 检查已存在节点
  let existingNodeIndex = branchMap.value[''].children.findIndex(
    child => child.taskId === currentTaskId
  )
  let existingNode =
    existingNodeIndex !== -1 ? branchMap.value[''].children[existingNodeIndex] : null

  // 4.3 根据任务类型分别处理
  // ...具体处理逻辑见下文
})
```

#### 7.2.4 最终整理阶段

```typescript
// 5. 排序和结构冻结
branchMap.value[''].children.sort((a, b) => {
  const aIndex = a.taskIndex !== undefined ? a.taskIndex : 999
  const bIndex = b.taskIndex !== undefined ? b.taskIndex : 999
  return aIndex - bIndex
})

// 6. 设置默认选中和计算大小
checkDefault()
caculateBranchSize(branchMap.value[''])

// 7. 冻结树形数据
tree.value = Object.freeze(branchMap.value[''].children)
```

### 7.3 不同任务类型的处理差异

#### 7.3.1 磁力链（Magnet/BT）任务处理

磁力链任务的特点是需要先解析获取种子信息，然后构建多层文件目录结构。

**处理逻辑：**

```typescript
// 1. 无文件列表时的处理（解析中/失败）
if (!hasFileLists && !isP2spTask) {
  // 创建特殊空节点显示状态
  const specialNodeData = {
    fileName: taskTitle,
    isSpecialEmptyNode: true,
    status: taskStatus, // 'loading' | 'error' | 'success'
    taskId: currentTaskId,
  }

  // 根据状态显示不同文本
  const displayName = getStatusDisplayName(taskStatus, taskData.url)
  // 例如：'正在努力获取种子文件（magnet:?xt=...）'
  //      '获取失败（magnet:?xt=...）'
}

// 2. 有文件列表时的处理（解析成功）
else {
  // 2.1 创建任务分支节点
  const taskBranchName = `${taskTitle}_${currentTaskId}`
  let taskBranch = setBranch('', taskBranchName, currentTaskType, 0)

  // 2.2 遍历文件列表构建目录结构
  taskData.fileLists.forEach(file => {
    const prefixedFilePath = `${taskBranchName}\\${file.filePath}`

    // 2.3 构建多层目录结构
    let pwd = taskBranchName
    let currentLevel = 1

    file.filePath.split('\\').forEach(dirname => {
      if (dirname !== '') {
        setBranch(pwd, dirname, currentTaskType, currentLevel)
        pwd = pwd ? `${pwd}\\${dirname}` : dirname
        currentLevel++
      }
    })

    // 2.4 创建文件节点
    const fileLevel = prefixedFilePath.split('\\').length - 1
    setLeaf(prefixedFilePath.slice(0, -1), fileWithTaskId, currentTaskType, fileLevel)
  })
}
```

**磁力链任务的结构特点：**

- 根节点：任务分支（如 "电影合集\_task123"）
- 中间节点：目录结构（如 "Season1\\Episode01"）
- 叶子节点：具体文件（如 "movie.mkv"）
- 状态节点：解析中/失败时的特殊提示节点

#### 7.3.2 P2SP 任务处理

P2SP任务是直链下载，通常是单文件，不需要复杂的目录结构。

**处理逻辑：**

```typescript
// 1. 检查是否有文件信息
const hasFileInfo = isP2spTask && (taskData.fileName || taskData.url)

// 2. 无文件信息时（创建失败）
if (isP2spTask && !hasFileInfo && taskData.status !== 'success') {
  // 创建特殊空节点
  const specialLeaf = {
    name: getStatusDisplayName(taskStatus, taskData.url),
    isSpecialEmptyNode: true,
    status: taskStatus,
    taskType: currentTaskType,
  }
  // 直接挂载到根节点
}

// 3. 有文件信息时（创建成功）
else if (isP2spTask && hasFileInfo) {
  // 3.1 提取文件信息
  const fileName = taskData.fileName || extractFileNameFromUrl(taskData.url) || taskTitle
  const fileSize = taskData.fileSize || 0

  // 3.2 创建单文件节点
  const p2spLeaf = {
    type: 'leaf',
    name: fileName,
    size: fileSize,
    suffix: getSuffix(fileName),
    parent: branchMap.value[''], // 直接挂载到根节点
    default: true, // P2SP任务默认选中
    icon: TaskUtilHelper.getTaskIcon(fileName),
    taskType: currentTaskType,
    isSpecialEmptyNode: false,
    fileCategory: taskData.fileCategory, // 'video' | 'image' | 其他
  }

  // 3.3 直接添加到根节点children
  branchMap.value[''].children.push(p2spLeaf)
}
```

**P2SP任务的结构特点：**

- 扁平结构：文件直接挂载在根节点下
- 单文件：每个P2SP任务对应一个文件节点
- 默认选中：P2SP任务创建后默认被选中下载
- 文件分类：支持根据 fileCategory 显示不同图标

### 7.4 状态管理和节点转换

#### 7.4.1 节点状态转换机制

函数支持动态更新已存在的节点，处理任务状态变化：

```typescript
// 检查现有节点并处理转换
if (existingNode) {
  if (existingNode.isSpecialEmptyNode) {
    // 特殊空节点 -> 正常节点（解析成功）
    branchMap.value[''].children.splice(existingNodeIndex, 1)
    delete leafMap.value[currentTaskId]
    existingNode = null // 重置，后续重新创建
  } else if (existingNode.type === 'branch') {
    // 更新分支节点信息
    existingNode.name = taskBranchName
    existingNode.taskIndex = taskIndex
    existingNode.children = [] // 清理子节点，重新构建
  } else if (existingNode.type === 'leaf' && !existingNode.isSpecialEmptyNode) {
    // 更新文件节点信息
    existingNode.name = fileName
    existingNode.size = fileSize
    existingNode.taskIndex = taskIndex
  }
}
```

#### 7.4.2 状态显示文本生成

```typescript
const getStatusDisplayName = (status, url) => {
  switch (status) {
    case TaskParseStatus.LOADING:
    case 'loading':
      return `正在努力获取种子文件（${url || ''}）`
    case TaskParseStatus.ERROR:
    case 'error':
      return `获取失败（${url || ''}）`
    case TaskParseStatus.SUCCESS:
    case 'success':
      return `解析成功（${url || ''}）`
    default:
      return `正在努力获取种子文件（${url || ''}）`
  }
}
```

### 7.5 关键数据结构

#### 7.5.1 分支节点结构（目录）

```typescript
const branch = {
  type: 'branch',
  name: '目录名称',
  children: [], // 子节点数组
  fullPath: '完整路径',
  key: '唯一标识',
  parent: parentNode, // 父节点引用
  icon: 'file-type-folder',
  level: 0, // 层级深度
  taskType: TaskType.Bt, // 任务类型
  taskId: 'task123', // 任务ID
  taskIndex: 0, // 任务索引（用于排序）
}
```

#### 7.5.2 叶子节点结构（文件）

```typescript
const leaf = {
  type: 'leaf',
  name: '文件名.ext',
  size: 1048576, // 文件大小（字节）
  suffix: 'ext', // 文件扩展名
  fullPath: '完整路径',
  key: '唯一标识',
  parent: parentNode, // 父节点引用
  default: true, // 是否默认选中
  icon: 'file-type-video',
  level: 2, // 层级深度
  taskType: TaskType.P2sp,
  taskId: 'task123',
  taskIndex: 0,
  realIndex: 0, // 文件在任务中的真实索引
  isSpecialEmptyNode: false,
  fileCategory: 'video', // P2SP专用：文件类别
}
```

#### 7.5.3 特殊空节点结构（状态提示）

```typescript
const specialEmptyNode = {
  type: 'leaf',
  name: '正在努力获取种子文件（magnet:?xt=...）',
  size: 0,
  fullPath: 'task123',
  key: 'task123',
  parent: rootNode,
  default: false, // 特殊节点不可选中
  icon: 'xl-icon-down-hint', // 状态图标
  level: 0,
  taskType: TaskType.Bt,
  taskId: 'task123',
  taskIndex: 0,
  isSpecialEmptyNode: true, // 标识为特殊节点
  status: 'loading', // 任务状态
  realIndex: -1, // 特殊标识
}
```

### 7.6 性能优化策略

1. **节点复用机制**：检查已存在节点，避免重复创建
2. **增量更新**：只更新变化的节点，保持其他节点不变
3. **延时排序**：所有节点处理完毕后统一排序
4. **对象冻结**：最终结果使用 `Object.freeze()` 防止意外修改
5. **映射表缓存**：使用 `branchMap` 和 `leafMap` 快速查找节点

### 7.7 错误处理和边界情况

1. **空任务数组**：创建 "暂无解析任务" 的提示节点
2. **任务数据缺失**：使用默认值填充必要字段
3. **文件路径异常**：处理空路径和特殊字符
4. **URL解析失败**：提供 fallback 文件名
5. **状态转换冲突**：按优先级处理节点类型转换

这个函数是整个任务列表组件的核心，通过统一的数据结构处理不同类型的下载任务，为用户提供一致的文件管理体验。

## 8. 注意事项

1. 所有异步操作都应使用 `async/await` 或 Promise 处理
2. 创建任务时需提供完整的任务信息，包括保存路径、任务名称等
3. 监听事件时，记得在组件卸载时取消事件注册，避免内存泄漏
4. 批量操作任务时，确保提供正确的任务ID数组
5. 用户文件操作（如删除）需谨慎，建议提供确认机制
