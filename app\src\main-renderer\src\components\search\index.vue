<template>
  <div class="search-container" :class="{ 'is-focused': isSearchFocused }" ref="searchContainerRef">
    <div class="app-header-search" :class="{ 'is-focused': showSearchDropdown }">
      <i class="xl-icon-search"></i>
      <input
        ref="searchInputRef"
        v-model="searchText"
        placeholder="搜文件、贴链接"
        class="search-input"
        @focus="handleSearchFocus"
        @blur="handleSearchBlur"
        @input="handleSearchInput"
      />
      <i 
        v-if="searchText" 
        class="xl-icon-close search-clear-btn"
        @mousedown.prevent.stop
        @click.stop="handleSearchClear"
      ></i>
    </div>
    
    <!-- 搜索下拉框 -->
    <SearchDropdown
      v-if="showSearchDropdown"
      :search-text="searchText"
      @select="handleSearchSelect"
      @locate-link="handleLocateLink"
      @locate-download="handleLocateDownload"
      @locate-cloud="handleLocateCloud"
      @update-search-text="handleUpdateSearchText"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, useTemplateRef } from 'vue'
import { onClickOutside } from '@vueuse/core'
import SearchDropdown from './search-dropdown.vue'
import { LinkFile, CloudFile, TaskBase } from './types'

const emit = defineEmits<{
  (e: 'search', text: string): void
  (e: 'select', result: any): void
  (e: 'focus'): void
  (e: 'blur'): void
}>()

// 搜索相关状态
const searchText = ref('')
const showSearchDropdown = ref(false)
const searchInputRef = ref<HTMLElement>()
const searchContainerRef = useTemplateRef<HTMLElement>('searchContainerRef')
const isSearchFocused = ref(false)

// 搜索相关方法
const handleSearchFocus = () => {
  isSearchFocused.value = true
  showSearchDropdown.value = true
  emit('focus')
}

const hideSearchDropdown = () => {
  isSearchFocused.value = false
  showSearchDropdown.value = false
}

const handleSearchBlur = () => {
  // 延迟检查，给下拉框内的点击事件时间执行
  nextTick(() => {
    const activeElement = document.activeElement
    const isWindowFocused = document.hasFocus();

    // 情况1：窗口失去焦点（如点击了桌面或其他应用）
    if (!isWindowFocused) {
      hideSearchDropdown()
      return;
    }

    console.log('activeElement', activeElement)
    
    const isStillFocusedInContainer = searchContainerRef.value?.contains(activeElement)
    
    // 如果焦点不在容器内，才关闭下拉框
    if (!isStillFocusedInContainer) {
      hideSearchDropdown()
      emit('blur')
    }
  })
}

const handleSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target?.value || ''
  searchText.value = value
  // 输入时显示下拉框
  emit('search', value)
  // 如果下拉框未显示，则显示下拉框
  if (!showSearchDropdown.value) {
    console.log('>>>>>>>>>>>>>>>>>> 输入时显示下拉框')
    showSearchDropdown.value = true
    isSearchFocused.value = true
  }
}

const handleSearchClear = () => {
  searchText.value = ''
  // 清除后保持下拉框显示
  emit('search', '')
}

const handleSearchSelect = (result: any) => {
  // 选择结果后隐藏下拉框
  showSearchDropdown.value = false
  isSearchFocused.value = false
  if (searchInputRef.value) {
    (searchInputRef.value as any).blur()
  }
  emit('select', result)
}

const handleLocateLink = (link: LinkFile) => {
  handleSearchSelect(link)
}

const handleLocateDownload = (task: TaskBase) => {
  handleSearchSelect(task)
}

const handleLocateCloud = (file: CloudFile) => {
  handleSearchSelect(file)
}

const handleUpdateSearchText = (text: string) => {
  // 更新输入框内容
  searchText.value = text
  // 保持焦点在输入框上
  nextTick(() => {
    if (searchInputRef.value) {
      searchInputRef.value.focus()
    }
  })
}

onClickOutside(
  searchContainerRef,
  () => {
    hideSearchDropdown()
  },
  { ignore: [] },
)

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    showSearchDropdown.value = false
    isSearchFocused.value = false
    if (searchInputRef.value) {
      (searchInputRef.value as any).blur()
    }
  }
}

// 导出focused状态供父组件使用
defineExpose({
  isSearchFocused
})

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style lang="scss" scoped>
// 搜索容器样式
.search-container {
  position: relative;
  max-width: 260px;
  transition: width 0.1s ease;
  flex: 1 1 auto;

  &.is-focused {
    max-width: 480px;
  }
}

.app-header-search {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  height: 36px;
  padding: 0 12px;
  border-radius: var(--border-radius-S, 6px);
  background: var(--button-button2-default, #F2F3F5);

  &.is-focused {
    border-radius: var(--border-radius-S, 6px);
    border: 2px solid var(--border-border-primary-search, #D3E2FD);
    background: var(--background-background-container, #FFF);
  }

  i.xl-icon-search {
    color: var(--font-font-3, #86909C);
    font-size: 16px;
    flex-shrink: 0;
  }

  .search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    color: var(--font-font-1, #1D2129);
    font-size: 13px;
    line-height: 1;

    &::placeholder {
      color: var(--font-font-3, #86909C);
    }
  }

  .search-clear-btn {
    width: 16px;
    height: 16px;
    cursor: pointer;
    color: var(--font-font-3, #86909C);
    font-size: 12px;
    transition: color 0.2s ease;
    flex-shrink: 0;

    &:hover {
      color: var(--font-font-1, #1D2129);
    }
  }
}
</style> 