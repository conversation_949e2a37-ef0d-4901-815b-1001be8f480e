<script setup lang="ts">
import Button from '@root/common/components/ui/button/index.vue'
import TDCheckbox from '@root/common/components/ui/checkbox/index.vue'

import { computed, ref } from 'vue';

const props = withDefaults(defineProps<{
  selectAll: boolean
  selectedFiles?: any[]
  indeterminate?: boolean
}>(), {
  selectedFiles: () => ([])
})

const emit = defineEmits<{
  (e: 'cleanPicked'): void
  (e: 'checkChange', selectAll: boolean): void
  (e: 'toolbarItemClick', type: string, files: any[]): void
  (e: 'copyLink', files: any[]): void
  (e: 'cancelShare', files: any[]): void
}>()

const isEnableCopy = computed(() => {
  let bEnable: boolean = false;
  for (let i = 0; i < props.selectedFiles.length; i++) {
    if (props.selectedFiles[i]?.title) {
      // 有title，分享连接未失效
      bEnable = true
      break
    }
  }

  return bEnable
})

function handleCleanPicked() {
  emit('cleanPicked')
}

function handleCheckChange(isCheck: boolean) {
  emit('checkChange', !props.selectAll || props.indeterminate)
}

function handleCopyLink() {
  emit('copyLink', props.selectedFiles)
}

function handleCancelShare() {
  emit('cancelShare', props.selectedFiles)
}
</script>

<template>
  <div class="file-list-header">
    <div class="file-name-sorter">
      <!-- 复选框 -->
      <div class="checkbox">
        <TDCheckbox label="" :model-value="selectAll" :indeterminate="indeterminate"
          @update:model-value="handleCheckChange" />
      </div>

      <div class="text">
        <span v-if="!selectedFiles.length">文件名</span>

        <div v-else class="selected">
          <span>已选中 {{ selectedFiles.length }} 项</span>
          <span class="cancel-selected" @click="handleCleanPicked">取消选中</span>
        </div>
      </div>

      <!-- 多选快捷操作工具栏 -->
      <div v-if="selectedFiles.length" class="toolbar">
        <Button variant="ghost" size="sm" :disabled="!isEnableCopy" @click="handleCopyLink">
          <i class="xl-icon-general-copy-m"></i>
          复制链接
        </Button>
        <Button variant="ghost" size="sm" @click="handleCancelShare">
          <i class="xl-icon-general-cancel-m"></i>
          取消分享
        </Button>
      </div>
    </div>

    <div class="file-view-sorter">
      <span>浏览数</span>
    </div>

    <div class="file-extract-sorter">
      <span>提取数</span>
    </div>

    <div class="file-surplus-sorter">
      <span>可提取数</span>
    </div>

    <!-- 文件大小 -->
    <div class="file-duration-sorter">
      <span>剩余时长</span>
    </div>

    <!-- 修改时间 -->
    <div class="file-time-sorter">
      <span>分享时间</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.file-list-header {
  flex-shrink: 0;
  height: 40px;
  margin: 0 28px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 24px;
  font-size: 12px;
  line-height: 22px;
  color: var(--font-font-1, #272E3B);

  .file-name-sorter {
    flex-grow: 1;
    display: flex;
    align-items: center;

    .checkbox {
      margin-right: 12px;
    }

    .text {
      display: flex;

      .selected {
        display: flex;
        gap: 8;

        .cancel-selected {
          cursor: pointer;
          color: var(--primary-primary-default);
        }
      }
    }

    .toolbar {
      border: 1px solid var(--border-border-2);
      border-radius: var(--border-radius-M);
      margin-left: 8px
    }
  }

  .file-view-sorter {
    flex-shrink: 0;
    width: 40px;
  }

  .file-extract-sorter {
    flex-shrink: 0;
    width: 40px;
  }

  .file-surplus-sorter {
    flex-shrink: 0;
    width: 48px;
  }

  .file-duration-sorter {
    flex-shrink: 0;
    width: 48px;
  }

  .file-time-sorter {
    flex-shrink: 0;
    width: 110px;
  }
}
</style>
