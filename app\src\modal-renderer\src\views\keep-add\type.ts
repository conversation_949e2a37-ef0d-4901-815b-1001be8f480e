
// import { BtFileDownloadInfo } from '@root/common/task/base';

export interface IChangeTaskInfo {
  rootBtTaskId?: number; // 用于区分bt任务
  taskId?: number;
  realIndex?: number,
  fileSize: number,
  downloadSize: number,
  fileName: string,
  download: boolean,
  filePath: string,
  fileStatus?: number,
  errorCode?: number,
  taskType?: number,
}

interface ITreeItem {
  key: string;
  name: string;
  parent: IBranch;
  type: 'branch' | 'leaf';
  progressStr: string;
  progressNum: number;
  fileSize: number;
  downloadSize: number;
  rootBtTaskId: number; // 用于区分是否为bt任务
  isComplete: boolean;
  realIndex?: number; // bt子文件索引
  download: boolean;
  taskType?: number;
  fileStatus?: number;
  taskId?: number;
}
export interface ILeaf extends ITreeItem {
  fullPath: string;
  // data: IChangeTaskInfo;
}
export interface IBranch extends ITreeItem {
  children: (ILeaf | IBranch)[];
  // data: {
  //   downloadSize: number;
  //   fileName: string;
  //   fileSize: number;
  //   isComplete: boolean;
  //   progress: number;
  //   downloadTotal: number;
  //   status?: number;
  //   taskType?: number;
  //   taskId?: number; // 如果是任务组的话就是子任务taskid
  //   index?: number; // 子文件索引
  //   errCode?: number; // 错误码
  //   download: boolean;
  // };
}

export interface IPostOrderBrachRs {
  downloadSize: number;
  fileSize: number;
  isAllComplete: boolean;
  downloadTotal: number;
}