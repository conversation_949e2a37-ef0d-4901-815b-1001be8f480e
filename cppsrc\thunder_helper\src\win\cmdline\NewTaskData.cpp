#include "NewTaskData.h"
#include "../text/transcode.h"

CNewTaskData::CNewTaskData() 
	: m_unFixed(0)
	, m_nOnlyFromOrigin(0)
	, m_nThreadNum(0)
	, m_dwTickCount(0)
	, m_nThunderStarted(0)
	, m_nStartMode(-1)
{
}

CNewTaskData::~CNewTaskData()
{
}

void CNewTaskData::SetURL(LPCWSTR szUrl)
{
	if (szUrl == NULL)
	{
		return;
	}
	std::wstring strUrl(szUrl);
	if (strUrl.length() > 0)
	{
		size_t i = 0;
		for (i = strUrl.length() - 1; i >= 0; --i)
		{
			wchar_t c = strUrl.at(i);
			if (c != '\n' && c != '\r')
			{
				break;
			}
		}
		if (i < strUrl.length() - 1)
		{
			strUrl = strUrl.substr(0, strUrl.length() - 1);
		}
	}
	m_wstrUrl = strUrl.c_str();
}

std::string CNewTaskData::GetURL()
{
	std::string strUrl;
	xl::text::transcode::Unicode_to_UTF8(m_wstrUrl.c_str(), m_wstrUrl.length(), strUrl);
	return strUrl;
}

void CNewTaskData::SetFileName(LPCWSTR szFileName)
{
	if (szFileName)
	{
		m_wstrFileName = szFileName;
	}	
}

std::string CNewTaskData::GetFileName()
{
	std::string strFileName;
	xl::text::transcode::Unicode_to_UTF8(m_wstrFileName.c_str(), m_wstrFileName.length(), strFileName);
	return strFileName;
}

void CNewTaskData::SetFileNameFixedFlag(UINT unFixedFlag)
{
	m_unFixed = unFixedFlag;
}

void CNewTaskData::SetCodePage(LPCWSTR szCodePage)
{
	if (szCodePage)
	{
		m_wstrCodePage = szCodePage;
	}
}

void CNewTaskData::SetDesc(LPCWSTR szDesc)
{
	if (szDesc)
	{
		m_wstrDesc = szDesc;
	}
}

void CNewTaskData::SetUserAgent(LPCWSTR szUserAgent)
{
	if (szUserAgent)
	{
		m_wstrUserAgent = szUserAgent;
	}
}

void CNewTaskData::SetRefUrl(LPCWSTR szRefUrl)
{
	if (szRefUrl)
	{
		m_wstrRefUrl = szRefUrl;
	}
}

std::string CNewTaskData::GetUserAgent()
{
	std::string userAgent;
	xl::text::transcode::Unicode_to_UTF8(m_wstrUserAgent.c_str(), m_wstrUserAgent.length(), userAgent);
	return userAgent;
}

std::string CNewTaskData::GetRefUrl()
{
	std::string strRefer;
	xl::text::transcode::Unicode_to_UTF8(m_wstrRefUrl.c_str(), m_wstrRefUrl.length(), strRefer);
	return strRefer;
}

void CNewTaskData::SetCID(LPCWSTR szCID)
{
	if (szCID)
	{
		m_wstrCID = szCID;
	}
}

std::string CNewTaskData::GetCID()
{
	std::string strCID;
	xl::text::transcode::Unicode_to_UTF8(m_wstrCID.c_str(), m_wstrCID.length(), strCID);
	return strCID;
}

void CNewTaskData::SetStatRefUrl(LPCWSTR szStatRefUrl)
{
	if (szStatRefUrl)
	{
		m_wstrStatRefUrl = szStatRefUrl;
	}
}

void CNewTaskData::SetCookie(LPCWSTR szCookie)
{
	if (szCookie)
	{
		m_wstrCookie = szCookie;
	}
}

std::string CNewTaskData::GetCookie()
{
	std::string strCookie;
	xl::text::transcode::Unicode_to_UTF8(m_wstrCookie.c_str(), m_wstrCookie.length(), strCookie);
	return strCookie;
}

void CNewTaskData::SetOnlyFromOrigin(int nOnlyFromOrigin)
{
	m_nOnlyFromOrigin = nOnlyFromOrigin;
}

void CNewTaskData::SetThreadNum(int nThreadNum)
{
	m_nThreadNum = nThreadNum;
}

void CNewTaskData::SetPath(LPCWSTR szPath)
{
	if (szPath)
	{
		m_wstrPath = szPath;
	}
}

void CNewTaskData::SetStatClick(LPCWSTR szStatClick)
{
	if (szStatClick)
	{
		m_wstrStatClick = szStatClick;
	}
}

std::string CNewTaskData::GetStatClick()
{
	std::string strStatClick;
	xl::text::transcode::Unicode_to_UTF8(m_wstrStatClick.c_str(), m_wstrStatClick.length(), strStatClick);
	return strStatClick;
}

void CNewTaskData::SetBrowserSource(LPCWSTR szBrowserSource)
{
	if (szBrowserSource)
	{
		m_wstrBrowserSource = szBrowserSource;
	}	
}

std::string CNewTaskData::GetBrowserSource()
{
	std::string strBrowserSource;
	xl::text::transcode::Unicode_to_UTF8(m_wstrBrowserSource.c_str(), m_wstrBrowserSource.length(), strBrowserSource);
	return strBrowserSource;
}

void CNewTaskData::SetTickCount(DWORD dwTickCount)
{
	m_dwTickCount = dwTickCount;
}

void CNewTaskData::SetThunderStarted(int nThunderStarted)
{
	m_nThunderStarted = nThunderStarted;
}

void CNewTaskData::SetCreateOriginType(TaskCreateType tcType)
{
	m_taskCreateOriginType = tcType;
}

TaskCreateType CNewTaskData::GetSpecialType()
{
	return m_taskCreateOriginType;
}

void CNewTaskData::SetStartMode(int nStartMode)
{
	m_nStartMode = nStartMode;
}

void CNewTaskData::SetUserData(LPCWSTR szUserData)
{
	if (szUserData)
	{
		m_wstrUserData = szUserData;
	}
}

std::string CNewTaskData::GetUserData()
{
	std::string strUserData;
	xl::text::transcode::Unicode_to_UTF8(m_wstrUserData.c_str(), m_wstrUserData.length(), strUserData);
	return strUserData;
}

int CNewTaskData::GetStartMode()
{
	return m_nStartMode;
}

UINT CNewTaskData::GetFileNameFixedFlags()
{
	return m_unFixed;
}