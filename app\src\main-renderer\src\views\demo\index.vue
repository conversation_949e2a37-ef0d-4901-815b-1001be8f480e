<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { computed } from 'vue'

const router = useRouter()
const route = useRoute()

const handleClick = (path: string) => {
  router.push(`/demo/${path}`)
}

const showList = computed(() => route.path === '/demo')
</script>

<template>
  <div class="demo-container">
    <div v-if="showList">
      <h1>demo</h1>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('button')">
          <p>Button</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('input')">
          <p>Input</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('dropdown-menu')">
          <p>DropdownMenu</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('context-menu')">
          <p>ContextMenu</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('tooltip')">
          <p>Tooltip</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('file-icon')">
          <p>FileIcon</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('progress')">
          <p>Progress</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('checkbox')">
          <p>CheckBox</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('dialog')">
          <p>Dialog</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
      <div class="demo-container-content">
        <div class="demo-container-content-item" @click="() => handleClick('tree')">
          <p>Tree</p>
          <i class="xl-icon-arrow-right"></i>
        </div>
      </div>
    </div>
    <router-view></router-view>
  </div>
</template>

<style lang="scss" scoped>
.demo-container {
  padding: 11px 36px;
  overflow: auto;
}

.demo-container-content {
  .demo-container-content-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0;

    &:hover {
      background: #F2F3F5;
      cursor: pointer;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #E5E6EB;
    }
  }
}
</style>