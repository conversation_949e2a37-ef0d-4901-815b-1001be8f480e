<template>
  <AlertDialogRoot v-model:open="open" :modal="modal">
    <AlertDialogPortal :to="appendTo">
      <AlertDialogOverlay :class="['thunder-message-box__mask', modalClass]" @click="handleMaskClick" />
      <AlertDialogContent
        :class="['thunder-message-box', customClass]"
        :style="boxStyle"
        tabindex="-1"
        @keydown.esc="handleEsc"
        @click.stop
      >
        <div class="thunder-message-box__header">
          <span class="thunder-message-box__title">
            <i v-if="icon" :class="['thunder-message-box__icon', icon]"></i>
            {{ title }}
          </span>
          <i v-if="showClose" class="thunder-message-box__close" @click="handleClose">
            <template v-if="closeIcon">
              <component :is="closeIcon" v-if="typeof closeIcon === 'object'" />
              <i v-else :class="closeIcon" />
            </template>
            <template v-else>
              <i class="xl-icon-general-close-m" />
            </template>
          </i>
        </div>
        <AlertDialogDescription>
          <div class="thunder-message-box__content" v-if="message">
            <template v-if="dangerouslyUseHTMLString">
              <div v-html="message"></div>
            </template>
            <template v-else-if="isMessageVNode">
              <component :is="message" />
            </template>
            <template v-else>
              <div>{{ message }}</div>
            </template>
          </div>
        </AlertDialogDescription>
        <div class="thunder-message-box__footer">
          <AlertDialogCancel
            v-if="showCancelButton"
            as="button"
            :class="['thunder-message-box__btn', 'thunder-message-box__btn-cancel-default', cancelButtonClass]"
            :disabled="cancelButtonLoading"
            @click="handleCancel"
          >
            <span v-if="cancelButtonLoading && cancelButtonLoadingIcon" class="thunder-message-box__btn-icon"><component :is="cancelButtonLoadingIcon" /></span>
            <span v-else>{{ cancelButtonText }}</span>
          </AlertDialogCancel>
          <AlertDialogAction
            v-if="showConfirmButton"
            as="button"
            :class="['thunder-message-box__btn', 'thunder-message-box__btn-confirm-default', confirmButtonClass, type === 'warning' ? 'thunder-message-box__btn-confirm-warning' : '']"
            :disabled="confirmButtonLoading"
            @click="handleConfirm"
          >
            <span v-if="confirmButtonLoading && confirmButtonLoadingIcon" class="thunder-message-box__btn-icon"><component :is="confirmButtonLoadingIcon" /></span>
            <span v-else>{{ confirmButtonText }}</span>
          </AlertDialogAction>
        </div>
      </AlertDialogContent>
    </AlertDialogPortal>
  </AlertDialogRoot>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed, isVNode } from 'vue'
import {
  AlertDialogRoot,
  AlertDialogPortal,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from 'reka-ui'

const props = defineProps({
  width: { type: [String, Number], default: 460 },
  height: { type: [String, Number], default: '' },
  title: { type: String, default: '' },
  message: { type: [String, Object, Function], default: '' },
  dangerouslyUseHTMLString: { type: Boolean, default: false },
  type: { type: String, default: '' },
  icon: { type: [String, Object], default: '' },
  closeIcon: { type: [String, Object], default: '' },
  customClass: { type: String, default: 'default' },
  customStyle: { type: Object, default: () => ({}) },
  modalClass: { type: String, default: '' },
  showClose: { type: Boolean, default: true },
  beforeClose: { type: Function },
  distinguishCancelAndClose: { type: Boolean, default: false },
  showCancelButton: { type: Boolean, default: false },
  showConfirmButton: { type: Boolean, default: true },
  cancelButtonText: { type: String, default: '取消' },
  confirmButtonText: { type: String, default: '确定' },
  cancelButtonClass: { type: String, default: '' },
  confirmButtonClass: { type: String, default: '' },
  closeOnClickModal: { type: Boolean, default: true },
  closeOnPressEscape: { type: Boolean, default: true },
  callback: { type: Function },
  cancelButtonLoading: { type: Boolean, default: false },
  confirmButtonLoading: { type: Boolean, default: false },
  cancelButtonLoadingIcon: { type: [String, Object], default: '' },
  confirmButtonLoadingIcon: { type: [String, Object], default: '' },
  appendTo: { type: String, default: 'body' },
  modal: { type: Boolean, default: true }
})
const emit = defineEmits(['confirm', 'cancel', 'close'])
const open = ref(true)

const isMessageVNode = computed(() => typeof props.message === 'object' && isVNode(props.message))

const boxStyle = computed(() => {
  const style = { ...props.customStyle }
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  }
  return style
})

function handleClose() {
  if (props.beforeClose) {
    props.beforeClose('close', { ...props }, () => close('close'))
  } else {
    close('close')
  }
}
function handleCancel() {
  if (props.beforeClose) {
    props.beforeClose('cancel', { ...props }, () => close('cancel'))
  } else {
    close('cancel')
  }
}
function handleConfirm() {
  if (props.beforeClose) {
    props.beforeClose('confirm', { ...props }, () => close('confirm'))
  } else {
    close('confirm')
  }
}
function handleMaskClick() {
  if (props.closeOnClickModal) handleClose()
}
function handleEsc(e: KeyboardEvent) {
  if (props.closeOnPressEscape) handleClose()
}
function close(action: 'close' | 'cancel' | 'confirm') {
  open.value = false
  emit(action)
  if (props.callback) props.callback(action)
}

</script>

<style lang="scss" scoped>
.thunder-message-box__mask {
  position: fixed;
  z-index: 88;
  left: 0; top: 0; right: 0; bottom: 0;
  inset: 0;
  background: var(--background-background-mask, rgba(39, 46, 59, 0.2));
  animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
}
.thunder-message-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99;
  flex: 0 0 auto;
  border-radius: var(--border-radius-L, 12px);
  box-shadow: var(--box-shadow-3, 0px 6px 30px 0px rgba(39, 46, 59, 0.16));
  background: var(--background-background-container, #fff);
  padding: 24px;
  outline: none;
}
.thunder-message-box__header {
  height: 28px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.thunder-message-box__title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--font-font-1, #272E3B);
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 22px;
}
.thunder-message-box__icon {
  font-size: 20px;
  font-weight: 400;
  margin-right: 8px;
  color: var(--primary-primary-default, #226DF5);
}
.thunder-message-box__close {
  font-size: 20px;
  color: var(--font-font-3, #898E97);
  cursor: pointer;
}
.thunder-message-box__content {
  padding: 8px 0;
  color: var(--font-font-2, #4E5769);
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.thunder-message-box__footer {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.thunder-message-box__btn {
  width: 84px;
  height: 40px;
  border-radius: var(--border-radius-M, 8px);
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  border: none;
  outline: none;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.thunder-message-box__btn-cancel-default {
  color: var(--font-font-2, #4E5769);
  background: var(--button-button2-default, #F2F3F5);
}

.thunder-message-box__btn-confirm-default {
  color: var(--button-button1-font-default, #FFF);
  background: var(--button-button1-default, #272E3B);
}

.thunder-message-box__btn-confirm-warning {
  background: var(--button-button-warn-default, #FFECE8);
  color: var(--button-button-warn-font-default, #FF4D4F);
}


</style> 