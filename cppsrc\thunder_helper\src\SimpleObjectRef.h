#ifndef XL_9E15699A_2684_4841_8408_88ED8AB8E910
#define XL_9E15699A_2684_4841_8408_88ED8AB8E910

#ifdef WIN32
#include <js_native_api.h>
#include <js_native_api_types.h>
#elif defined(__APPLE__) && defined(__MACH__)
#include <node_api.h>
#include <node.h>
#include <v8.h>
#endif
#include <string>
#include <memory>
#include <AddonOpt.h>

class RefValue {
public:
    RefValue(std::string strId, std::shared_ptr< NapiFunctionWarp> pFunc) : m_strId(strId), m_pFunc(pFunc) {}
    ~RefValue() {
        auto argv = m_pFunc->NewArgv(1);
        argv->PushString(m_strId);
        m_pFunc->Call(std::move(argv), nullptr);
    }
    
private:
    std::string m_strId;
    std::shared_ptr< NapiFunctionWarp> m_pFunc{ nullptr };
};

class SimpleObjectRefAddon {
public:
    static void Init(napi_env env, napi_value exports);

private:
    static napi_value GetValue(napi_env env, napi_callback_info info);
    static napi_value JSConstructor(napi_env env, napi_callback_info info);
};

#endif