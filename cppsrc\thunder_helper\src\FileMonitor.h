﻿#ifndef XL_CE5D4382_024A_4C6B_BA43_8495A620D683
#define XL_CE5D4382_024A_4C6B_BA43_8495A620D683
#ifdef WIN32
#ifndef WIN32_LEAN_AND_MEAN
	#define WIN32_LEAN_AND_MEAN             // 从 Windows 头文件中排除极少使用的内容
#endif
#include <windows.h>
#include <Shlwapi.h>
#elif defined(__linux__)
#include <sys/inotify.h>
#include <unistd.h>
#elif defined(__APPLE__) && defined(__MACH__)
#include <CoreServices/CoreServices.h>
#include <node_api.h>
#include <node.h>
#include <v8.h>
#endif

#include <functional>
#include <string>
#include <thread>
#include <atomic>
#include <vector>

#include <js_native_api.h>
#include <js_native_api_types.h>
#include <AddonOpt.h>

class FileMonitorAddon {
public:
	static void Init(napi_env env, napi_value exports);

private:
	static napi_value Watch(napi_env env, napi_callback_info info);
	static napi_value JSConstructor(napi_env env, napi_callback_info info);
};

#endif