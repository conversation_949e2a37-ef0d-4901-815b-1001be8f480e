/*!
     Transformation Matrix v2.0
     (c) Epistemex 2014-2015
     www.epistemex.com
     By <PERSON>
     Contributions by le<PERSON><PERSON>.
     License: MIT, header required.
     */

/*!
  * vue-tippy v6.6.0
  * (c) 2024 
  * @license MIT
  */

/*!
 * perfect-scrollbar v1.5.6
 * Copyright 2024 <PERSON><PERSON><PERSON>, MDBootstrap and Contributors
 * Licensed under MIT
 */

/*!
 * pinia v3.0.2
 * (c) 2025 <PERSON> <PERSON>
 * @license MIT
 */

/*! #__NO_SIDE_EFFECTS__ */

/*! ../../../../response_error */

/*! ../../../ReporterUtility */

/*! ../../../TraceInfoUtility */

/*! ../../../models */

/*! ../../../response_error */

/*! ../../../response_error/ErrorDetailUtility */

/*! ../../DeviceInfoUtility */

/*! ../../ResponseErrorUriUtility */

/*! ../../Utility */

/*! ../../analytics */

/*! ../../database_manager */

/*! ../../error_details */

/*! ../../log */

/*! ../../part_analytics_info */

/*! ../../request */

/*! ../../response_error */

/*! ../DeviceInfoUtility */

/*! ../Interface */

/*! ../ReporterUtility */

/*! ../analytics */

/*! ../analytics/analysis_report_service/AnalyticsInfoRecordProcesserHandler */

/*! ../analytics/analysis_report_service/AnalyticsInfoReportProcesserHandler */

/*! ../analytics_utility */

/*! ../base_request */

/*! ../database_manager */

/*! ../event_emitter */

/*! ../i18n */

/*! ../log */

/*! ../models */

/*! ../request */

/*! ../resources */

/*! ../response_error */

/*! ./AnalyticsInfoTableManager */

/*! ./AnalyticsManager */

/*! ./AnalyticsRecordProcesser */

/*! ./AnalyticsReportProcesser */

/*! ./AnalyticsUtility */

/*! ./BaseRequest */

/*! ./BaseRequestUtility */

/*! ./Common */

/*! ./DatabaseManager */

/*! ./DebugInfoDetail */

/*! ./Decorators */

/*! ./DeviceInfoUtility */

/*! ./EnvManager */

/*! ./Error */

/*! ./ErrorCode */

/*! ./ErrorDetailUtility */

/*! ./ErrorDetails */

/*! ./ErrorInfoDetail */

/*! ./EventEmitter */

/*! ./EventPublisherSpecificPartAnalyticsInfo */

/*! ./FunctionBeginCallSpecificPartAnalyticsInfo */

/*! ./FunctionResultSpecificPartAnalyticsInfo */

/*! ./IAnalyticsAble */

/*! ./InitStateManager */

/*! ./Interface */

/*! ./Interfaces */

/*! ./KeyValueTableManager */

/*! ./LocalizationReader */

/*! ./LocalizedMessageDetail */

/*! ./Lock */

/*! ./LogFunctionCallInfo */

/*! ./LogUtility */

/*! ./Logger */

/*! ./MainProcessRemote */

/*! ./Models */

/*! ./OriginalErrorInfoDetail */

/*! ./RecordAbleEventEmitter */

/*! ./RecordFunctionCallInfo */

/*! ./Recorder */

/*! ./RendererProcessRemote */

/*! ./ReporterUtility */

/*! ./RequestClient */

/*! ./RequestClientUtility */

/*! ./RequestFunctionWrapper */

/*! ./RequestSpecificPartAnalyticsInfo */

/*! ./ResponseError */

/*! ./ResponseErrorUriUtility */

/*! ./Result */

/*! ./SimpleBrowserWindow */

/*! ./SimpleConsole */

/*! ./SimpleStorage */

/*! ./Sqlite3DatabaseClient */

/*! ./Sqlite3StatementClient */

/*! ./Sqlite3StatementExtUtility */

/*! ./Sqlite3StatementUtility */

/*! ./TraceInfoUtility */

/*! ./TypeUtility */

/*! ./UpdateResponseError */

/*! ./Utility */

/*! ./WeakRefWrapper */

/*! ./_CommonShared */

/*! ./analysis_report_service */

/*! ./analytics */

/*! ./analytics_manager */

/*! ./analytics_utility */

/*! ./base_request */

/*! ./common */

/*! ./database_manager */

/*! ./decorators */

/*! ./en_US */

/*! ./error_details */

/*! ./error_details/DebugInfoDetail */

/*! ./errror_details */

/*! ./event_emitter */

/*! ./exts */

/*! ./exts/errror_details */

/*! ./i18n */

/*! ./lang */

/*! ./lock */

/*! ./log */

/*! ./main */

/*! ./models */

/*! ./part_analytics_info */

/*! ./process_remote */

/*! ./record_function_call_info */

/*! ./request */

/*! ./request_client */

/*! ./resources */

/*! ./response_error */

/*! ./response_error2 */

/*! ./simple_interfaces */

/*! ./sqlite3_utility */

/*! ./text-handler/ErrorTextHandler */

/*! ./update_response_error */

/*! ./zh_CN */

/*! @xbase/electron_base_kit */

/*! buffer */

/*! electron */

/*! os */

/*! path */

/*! sqlite3 */

/*!*********************!*\
  !*** external "os" ***!
  \*********************/

/*!**********************!*\
  !*** ./src/index.ts ***!
  \**********************/

/*!***********************!*\
  !*** external "path" ***!
  \***********************/

/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/

/*!**************************!*\
  !*** ./src/lock/Lock.ts ***!
  \**************************/

/*!**************************!*\
  !*** ./src/log/index.ts ***!
  \**************************/

/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/

/*!***************************!*\
  !*** ./src/i18n/index.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/lock/index.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/log/Logger.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/log/Models.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/main/index.ts ***!
  \***************************/

/*!***************************!*\
  !*** external "electron" ***!
  \***************************/

/*!****************************!*\
  !*** ./src/TypeUtility.ts ***!
  \****************************/

/*!****************************!*\
  !*** ./src/main/Common.ts ***!
  \****************************/

/*!******************************!*\
  !*** ./src/request/index.ts ***!
  \******************************/

/*!*******************************!*\
  !*** ./src/WeakRefWrapper.ts ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/log/Decorators.ts ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/log/LogUtility.ts ***!
  \*******************************/

/*!********************************!*\
  !*** ./src/analytics/index.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/main/EnvManager.ts ***!
  \********************************/

/*!*********************************!*\
  !*** ./src/InitStateManager.ts ***!
  \*********************************/

/*!**********************************!*\
  !*** ./src/DeviceInfoUtility.ts ***!
  \**********************************/

/*!***********************************!*\
  !*** ./src/main/_CommonShared.ts ***!
  \***********************************/

/*!************************************!*\
  !*** ./src/event_emitter/index.ts ***!
  \************************************/

/*!*************************************!*\
  !*** ./src/i18n/resources/index.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/process_remote/index.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/response_error/Error.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/response_error/index.ts ***!
  \*************************************/

/*!**************************************!*\
  !*** ./src/process_remote/Common.ts ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/response_error2/index.ts ***!
  \**************************************/

/*!***************************************!*\
  !*** ./src/analytics/models/index.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/database_manager/index.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/response_error/Utility.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/response_error2/Result.ts ***!
  \***************************************/

/*!****************************************!*\
  !*** ./src/analytics/models/Models.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/analytics_utility/index.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/i18n/LocalizationReader.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/log/LogFunctionCallInfo.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/simple_interfaces/index.ts ***!
  \****************************************/

/*!*****************************************!*\
  !*** ./src/i18n/resources/Interface.ts ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/response_error/ErrorCode.ts ***!
  \*****************************************/

/*!******************************************!*\
  !*** ./src/analytics/ReporterUtility.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/i18n/resources/lang/en_US.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/i18n/resources/lang/index.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/i18n/resources/lang/zh_CN.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/response_error/exts/index.ts ***!
  \******************************************/

/*!*******************************************!*\
  !*** ./src/analytics/TraceInfoUtility.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/event_emitter/EventEmitter.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/request/base_request/index.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** external "@xbase/electron_base_kit" ***!
  \*******************************************/

/*!*********************************************!*\
  !*** ./src/request/request_client/index.ts ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/response_error/ResponseError.ts ***!
  \*********************************************/

/*!************************************************!*\
  !*** ./src/response_error/decorators/index.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/simple_interfaces/SimpleConsole.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/simple_interfaces/SimpleStorage.ts ***!
  \************************************************/

/*!*************************************************!*\
  !*** ./src/analytics_utility/IAnalyticsAble.ts ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/database_manager/DatabaseManager.ts ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/process_remote/MainProcessRemote.ts ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/request/base_request/BaseRequest.ts ***!
  \*************************************************/

/*!**************************************************!*\
  !*** ./src/analytics/analytics_manager/index.ts ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/request/request_client/Interfaces.ts ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/response_error/ErrorDetailUtility.ts ***!
  \**************************************************/

/*!***************************************************!*\
  !*** ./src/analytics_utility/AnalyticsUtility.ts ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./src/i18n/text-handler/ErrorTextHandler.ts ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./src/response_error/error_details/index.ts ***!
  \***************************************************/

/*!*****************************************************!*\
  !*** ./src/event_emitter/RecordAbleEventEmitter.ts ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/process_remote/RendererProcessRemote.ts ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/request/request_client/RequestClient.ts ***!
  \*****************************************************/

/*!******************************************************!*\
  !*** ./src/database_manager/KeyValueTableManager.ts ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./src/simple_interfaces/SimpleBrowserWindow.ts ***!
  \******************************************************/

/*!*******************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/index.ts ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/response_error/ResponseErrorUriUtility.ts ***!
  \*******************************************************/

/*!********************************************************!*\
  !*** ./src/analytics/analysis_report_service/index.ts ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./src/request/base_request/BaseRequestUtility.ts ***!
  \********************************************************/

/*!*********************************************************!*\
  !*** ./src/response_error/exts/errror_details/index.ts ***!
  \*********************************************************/

/*!**********************************************************!*\
  !*** ./src/response_error/error_details/ErrorDetails.ts ***!
  \**********************************************************/

/*!***********************************************************!*\
  !*** ./src/analytics/analysis_report_service/Recorder.ts ***!
  \***********************************************************/

/*!************************************************************!*\
  !*** ./src/request/request_client/RequestClientUtility.ts ***!
  \************************************************************/

/*!*************************************************************!*\
  !*** ./src/analytics/analytics_manager/AnalyticsManager.ts ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./src/response_error/error_details/DebugInfoDetail.ts ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./src/response_error/error_details/ErrorInfoDetail.ts ***!
  \*************************************************************/

/*!**************************************************************!*\
  !*** ./src/request/request_client/RequestFunctionWrapper.ts ***!
  \**************************************************************/

/*!*******************************************************************!*\
  !*** ./src/analytics/analysis_report_service/decorators/index.ts ***!
  \*******************************************************************/

/*!********************************************************************!*\
  !*** ./src/response_error/error_details/LocalizedMessageDetail.ts ***!
  \********************************************************************/

/*!*********************************************************************!*\
  !*** ./src/analytics/analytics_manager/AnalyticsRecordProcesser.ts ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./src/analytics/analytics_manager/AnalyticsReportProcesser.ts ***!
  \*********************************************************************/

/*!**********************************************************************!*\
  !*** ./src/response_error/decorators/update_response_error/index.ts ***!
  \**********************************************************************/

/*!***********************************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/Sqlite3DatabaseClient.ts ***!
  \***********************************************************************/

/*!************************************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/Sqlite3StatementClient.ts ***!
  \************************************************************************/

/*!*************************************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/Sqlite3StatementUtility.ts ***!
  \*************************************************************************/

/*!***************************************************************************!*\
  !*** ./src/response_error/exts/errror_details/OriginalErrorInfoDetail.ts ***!
  \***************************************************************************/

/*!****************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/AnalyticsInfoTableManager.ts ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/index.ts ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./src/database_manager/sqlite3_utility/Sqlite3StatementExtUtility.ts ***!
  \****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/common.ts ***!
  \*****************************************************************************/

/*!************************************************************************************!*\
  !*** ./src/response_error/decorators/update_response_error/UpdateResponseError.ts ***!
  \************************************************************************************/

/*!**************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/AnalyticsInfoRecordProcesserHandler.ts ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/AnalyticsInfoReportProcesserHandler.ts ***!
  \**************************************************************************************/

/*!*********************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/decorators/record_function_call_info/index.ts ***!
  \*********************************************************************************************/

/*!*******************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/RequestSpecificPartAnalyticsInfo.ts ***!
  \*******************************************************************************************************/

/*!**************************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/decorators/record_function_call_info/RecordFunctionCallInfo.ts ***!
  \**************************************************************************************************************/

/*!**************************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/EventPublisherSpecificPartAnalyticsInfo.ts ***!
  \**************************************************************************************************************/

/*!**************************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/FunctionResultSpecificPartAnalyticsInfo.ts ***!
  \**************************************************************************************************************/

/*!*****************************************************************************************************************!*\
  !*** ./src/analytics/analysis_report_service/part_analytics_info/FunctionBeginCallSpecificPartAnalyticsInfo.ts ***!
  \*****************************************************************************************************************/

/**
* @vue/compiler-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/

/**
* @vue/compiler-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/

/**!
* tippy.js v6.3.7
* (c) 2017-2021 atomiks
* MIT License
*/