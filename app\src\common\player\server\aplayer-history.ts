import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import * as BaseType from '../base'
import { AplayerStack } from '../impl/aplayer-stack';

export class AplayerServerPlayHistory {
    static init() {
        AplayerStack.GetInstance().getPlayHistory().attachPreparedEvent(() => {
            client.broadcastEvent('AplayerPlayHistoryPrepared');
        });
        AplayerStack.GetInstance().getPlayHistory().attachItemUpdate((id: number, bNewAdd: boolean) => {
            let item = AplayerStack.GetInstance().getPlayHistory().getItemById(id);
            client.broadcastEvent('AplayerPlayHistoryItemUpdate', item, bNewAdd);
        });
        AplayerStack.GetInstance().getPlayHistory().attachItemDeleteEvent((id: number) => {
            client.broadcastEvent('AplayerPlayHistoryItemDelete', id)
        });

        client.registerFunctions({
            AplayerPlayHistoryDeleteItem: (context: any, id: number) => {
                AplayerStack.GetInstance().getPlayHistory().deleteItem(id);
            },
            AplayerPlayHistoryDeleteAllItem: (context: any) => {
                AplayerStack.GetInstance().getPlayHistory().deleteAllItem()
            },
            AplayerPlayHistoryGetItemById: (context: any, id: number) => {
                return AplayerStack.GetInstance().getPlayHistory().getItemById(id);
            },
            AplayerPlayHistoryPlayItem: (context: any, id: number) => {
                AplayerStack.GetInstance().getPlayHistory().playItem(id);
            },
            AplayerPlayHistoryGetList: (context: any): any => {
                return AplayerStack.GetInstance().getPlayHistory().getList();
            },
            AplayerPlayHistoryGetSelect: (context: any): any => {
                return AplayerStack.GetInstance().getPlayHistory().getSelect();
            },
            AplayerPlayHistoryIsPrepared: (context: any): any => {
                return AplayerStack.GetInstance().getPlayHistory().isPrepared();
            },
        });
    }
}