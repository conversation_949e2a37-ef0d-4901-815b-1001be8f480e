<template>
  <div class="mock-task-list-container">
    <h2>任务列表组件测试</h2>

    <div class="test-controls">
      <button @click="switchTestData">切换测试数据</button>

      <span>当前数据集: {{ currentDataSetName }}</span>
    </div>
    <!-- 
    <TaskSetting
      :taskUrl="'https://example.com/file.zip'"
      :initialOpenAfterDownload="true"
      :initialThreadCount="8"
      :initialComment="'这是一个重要的下载任务'"
    /> -->

    <TaskList
      :magnetTaskInfo="currentMagnetTasks"
      :p2spTaskInfo="currentP2spTasks"
      @checkedFileIndexes="handleCheckedFileIndexes"
      @retryMagnetTask="handleRetryMagnetTask"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import TaskList from '../create-task/task-list.vue'
import TaskSetting from '@root/modal-renderer/src/components/task-setting/task-setting.vue'

// 导入磁力链测试数据
import DefaultEmptyTaskInfo from '../create-task/table-test-file/task-info-small-empty'
import DefaultLabiEmptyTaskInfo from '../create-task/table-test-file/task-info-magnet-labi-empty'
import DefaultLabiTaskInfoError from '../create-task/table-test-file/task-info-magnet-labi-error'
import DefaultLabiTaskInfo from '../create-task/table-test-file/task-info-magnet-labi'
import DefaultTaskInfo from '../create-task/table-test-file/task-info-small'

// 导入P2SP测试数据
import DefaultP2spTaskInfo from '../create-task/table-test-file/task-info-p2sp-ed2k'
import DefaultP2spTaskInfoError from '../create-task/table-test-file/task-info-p2sp-error'
import DefaultP2spTaskInfoLoading from '../create-task/table-test-file/task-info-p2sp-loading'

// 导入Ed2k测试数据
import { Ed2kTaskInfo } from '../create-task/table-test-file/task-info-p2sp-ed2k'

// 测试数据集
const testDataSets = [
  {
    name: '初始状态（空任务+解析中）',
    magnetTasks: [DefaultEmptyTaskInfo, DefaultLabiEmptyTaskInfo],
    p2spTasks: [DefaultP2spTaskInfoLoading, DefaultP2spTaskInfoError, ...Ed2kTaskInfo],
  },
  {
    name: '成功状态（有文件+解析完成）',
    magnetTasks: [DefaultTaskInfo, DefaultLabiTaskInfoError],
    p2spTasks: [DefaultP2spTaskInfo, DefaultP2spTaskInfoError, ...Ed2kTaskInfo],
  },
  {
    name: '混合状态（磁力链+P2SP混合）',
    magnetTasks: [DefaultTaskInfo, DefaultLabiTaskInfo],
    p2spTasks: [DefaultP2spTaskInfo, DefaultP2spTaskInfoLoading, ...Ed2kTaskInfo],
  },
  {
    name: '空状态测试',
    magnetTasks: [],
    p2spTasks: [],
  },
]

// 增加一个数据集模拟ed2k任务
const testDataSets2 = [
  {
    name: '初始状态（空任务+解析中）',
    p2spTasks: [...Ed2kTaskInfo],
  },
]

// 增加一个数据集模拟单个磁力链
const testDataSets3 = [
  {
    name: '初始状态（空任务+解析中）',
    magnetTasks: [DefaultEmptyTaskInfo, DefaultLabiEmptyTaskInfo],
  },
  {
    name: '成功状态（有文件+解析完成）',
    magnetTasks: [DefaultTaskInfo, DefaultLabiTaskInfoError],
  },
  {
    name: '混合状态（磁力链+P2SP混合）',
    magnetTasks: [DefaultTaskInfo, DefaultLabiTaskInfo],
  },
  {
    name: '空状态测试',
    magnetTasks: [],
  },
]
// 当前数据集索引
const currentDataSetIndex = ref(0)

// 当前数据集
const currentDataSet = computed(() => testDataSets2[currentDataSetIndex.value])

// 当前数据集名称
const currentDataSetName = computed(() => currentDataSet.value.name)

// 当前磁力链任务
const currentMagnetTasks = computed(() => currentDataSet.value.magnetTasks)

// 当前P2SP任务
const currentP2spTasks = computed(() => currentDataSet.value.p2spTasks)

// 切换测试数据
const switchTestData = () => {
  currentDataSetIndex.value = (currentDataSetIndex.value + 1) % testDataSets.length
  console.log('🔄 切换到测试数据集:', currentDataSet.value.name)
}

// 处理选中文件变化
const handleCheckedFileIndexes = checkedData => {
  console.log('📋 选中的文件索引发生变化:', checkedData)
}

// 处理重试磁力链任务
const handleRetryMagnetTask = taskInfo => {
  console.log('🔄 重试磁力链任务:', taskInfo)
  alert(`重试任务: ${taskInfo.title}`)
}

// 模拟数据动态更新（3秒后自动切换到成功状态）
setTimeout(() => {
  if (currentDataSetIndex.value === 0) {
    currentDataSetIndex.value = 1
    console.log('⏰ 自动切换到成功状态数据集')
  }
}, 3000)
</script>

<style lang="scss" scoped>
.mock-task-list-container {
  padding: 20px;

  h2 {
    margin-bottom: 16px;
    color: #333;
  }

  .test-controls {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    gap: 16px;
    border-radius: 4px;
    background: #f5f5f5;

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      background: #1890ff;
      color: white;
      cursor: pointer;

      &:hover {
        background: #40a9ff;
      }
    }

    span {
      color: #666;
      font-weight: 500;
    }
  }
}
</style>
