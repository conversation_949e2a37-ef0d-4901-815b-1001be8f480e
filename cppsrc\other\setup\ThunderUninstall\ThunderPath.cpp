#include "StdAfx.h"
#include ".\thunderpath.h"
#include ".\helper\SystemHelper.h"

CThunderPath::CThunderPath(void)
{
}

CThunderPath::~CThunderPath(void)
{
}


CThunderPath* CThunderPath::GetInstance()
{
	static CThunderPath s_thunderPath;
	return &s_thunderPath;
}


const static PCTSTR THUNDER_SUBPATH = _T("Thunder Network\\Thunder\\");

BOOL CThunderPath::IsInSystemDirectory(LPCTSTR lpszPath)
{
	TCHAR szSystemPath[MAX_PATH] = {0};
	GetSystemDirectory(szSystemPath, MAX_PATH);
	return PathIsSameRoot(szSystemPath, lpszPath);
}



HRESULT CThunderPath::Initialize(LPCTSTR lpszInstallPath)
{
	if (m_strThunderInallPath == lpszInstallPath)
	{
		return S_OK;
	}
	CPathHelper::Initialize();

	m_strThunderInallPath = lpszInstallPath;
	while (m_strThunderInallPath.length() != 0 && m_strThunderInallPath[m_strThunderInallPath.size() - 1] == _T('\\'))
	{
		m_strThunderInallPath = m_strThunderInallPath.substr(0, m_strThunderInallPath.size() - 1);
	}
	if (CSystemHelper::IsVistaOrGreater() &&  IsInSystemDirectory(lpszInstallPath))
	{
		TCHAR szPath[MAX_PATH] = {0};
		::PathCombine(szPath, CPathHelper::GetPublicDocumentPath(), THUNDER_SUBPATH);
		m_strThunderUserDataPath = szPath;

		::PathCombine(szPath, CPathHelper::GetProgramDataPath(), THUNDER_SUBPATH);
		m_strThunderProgramDataPath = szPath;
	}
	else
	{
		m_strThunderUserDataPath = m_strThunderInallPath;
		m_strThunderProgramDataPath = m_strThunderInallPath;
	}

	TCHAR szPath[MAX_PATH] = {0};
	::PathCombine(szPath, CPathHelper::GetLocalLowAppDataPath(), THUNDER_SUBPATH);
	m_strThunderLocalLowPath = szPath;

	::PathCombine(szPath, m_strThunderLocalLowPath.c_str(), _T("BHO"));
	m_strThunderBHOConfigPath = szPath;

	CPathHelper::XLPathCombine(m_strThunderInallPath.c_str(), _T("Program"), m_strThunderProgramPath);

	CPathHelper::XLPathCombine(m_strThunderUserDataPath.c_str(), _T("skin"), m_strThunderSkinPath);
	CPathHelper::XLPathCombine(m_strThunderUserDataPath.c_str(), _T("Profiles"), m_strThunderProfilesPath);

	CPathHelper::XLPathCombine(m_strThunderProgramDataPath.c_str(), _T("addins"), m_strThunderAddinsPath);
	CPathHelper::XLPathCombine(m_strThunderProgramDataPath.c_str(), _T("data"), m_strThunderDataPath);

	if (CSystemHelper::IsVistaOrGreater())
	{
		TCHAR szPath[MAX_PATH] = {0};
		::PathCombine(szPath, CPathHelper::GetPublicPath(), _T("Thunder Network"));
		m_strThunderAppDataPath = szPath;

		TCHAR szProgramPath[MAX_PATH] = {0};
		::PathCombine(szProgramPath, CPathHelper::GetProgramDataPath(), _T("Thunder Network"));
		m_strThunderCommonDataPath = szProgramPath;
	}
	else
	{
		TCHAR szPath[MAX_PATH] = {0};
		::PathCombine(szPath, CPathHelper::GetAllUserAppDataPath(), _T("Thunder Network"));
		m_strThunderAppDataPath = szPath;
		m_strThunderCommonDataPath = szPath;
	}

	return XEC_Success;
}