<script setup lang="ts">
import { platform } from '@root/common/env';
import { TippyOptions } from 'vue-tippy';

const props = defineProps<{
  isPin?: boolean
  isWindowMax?: boolean
}>()

const emit = defineEmits<{
  (e: 'minize'): void
  (e: 'max'): void
  (e: 'close'): void
  (e: 'pin', isPrePin: boolean): void
}>()

function genTooltipOptionsForControlBtn(content: string, placement: 'top' | 'bottom' = 'top'): TippyOptions {
  let offset: [number, number] = [0, 20]
  let delay: [number, number] | undefined
  if (placement === 'bottom') {
    offset = [0, 3]
    delay = [800, 0]
  }

  return {
    content,
    placement,
    arrow: false,
    offset,
    theme: 'tooltip',
    duration: 300,
    maxWidth: 'none',
    delay,
  }
}
</script>

<template>
  <div class="window-operation-bar">
    <!-- 置顶 -->
    <!-- <tooltip v-if="isPin" v-bind="genTooltipOptionsForControlBtn('取消置顶', 'bottom')">
      <div class="operation-icon" @click="emit('pin', false)">
        <inline-svg :src="require('@root/common/assets/img/ic_ceilingoff_24.svg')" />
      </div>
    </tooltip>
    <tooltip v-else v-bind="genTooltipOptionsForControlBtn('置顶', 'bottom')">
      <div class="operation-icon" @click="emit('pin', true)">
        <inline-svg :src="require('@root/common/assets/img/ic_ceiling_24.svg')" />
      </div>
    </tooltip> -->
    <template v-if="platform.isWindows">
      <!-- 最小化 -->
      <tooltip v-bind="genTooltipOptionsForControlBtn('最小化', 'bottom')">
        <div class="operation-icon" @click="emit('minize')">
          <inline-svg :src="require('@root/common/assets/img/ic_minimize.svg')" />
        </div>
      </tooltip>
      <!-- 最大化 & 恢复 -->
      <tooltip v-if="isWindowMax" v-bind="genTooltipOptionsForControlBtn('还原', 'bottom')">
        <div class="operation-icon" @click="emit('max')">
          <inline-svg :src="require('@root/common/assets/img/ic_revert.svg')" />
        </div>
      </tooltip>
      <tooltip v-else v-bind="genTooltipOptionsForControlBtn('最大化', 'bottom')">
        <div class="operation-icon" @click="emit('max')">
          <inline-svg :src="require('@root/common/assets/img/ic_full.svg')" />
        </div>
      </tooltip>
      <!-- 关闭 -->
      <tooltip v-bind="genTooltipOptionsForControlBtn('关闭', 'bottom')">
        <div class="operation-icon" @click="emit('close')">
          <inline-svg :src="require('@root/common/assets/img/ic_close.svg')" />
        </div>
      </tooltip>
    </template>
  </div>
</template>

<style scoped lang="scss">
.window-operation-bar {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  gap: 2px;
  background-color: black;

  .operation-icon {
    width: 36px;
    height: 36px;
    cursor: pointer;
    color: var(--font-1);
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      cursor: pointer;
      background-color: var(--font-white-5);
      border-radius: var(--radius-8);
      color: var(--font-white-100);
    }
  }
}
</style>
