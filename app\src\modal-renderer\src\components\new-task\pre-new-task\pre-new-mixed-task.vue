<template>
  <Dialog
    :open="visible"
    :showTitleIcon="true"
    @update:open="handleOpenChange"
    :title="dialogTitle"
    :show-trigger="false"
    :show-cancel="false"
    :show-actions="true"
    :confirm-text="isParsingComplete ? '播放' : ''"
    :show-close-button="true"
    @confirm="handleConfirm"
    @close="() => handleClose('dialog-close')"
    :prevent-default-close="true"
    class-prefix="pre-new-task pre-new-mixed-task"
    :isCreateTask="true"
  >
    <!-- 解析完成状态 -->
    <div class="parsing-complete">
      <TaskList
        :task-data="taskData"
        :data-map="dataMap"
        :container-height="358"
        :table-height="240"
        :show-selection-count="true"
        :auto-expand-all="props.options?.autoExpandAll || false"
        @checkedFileIndexes="handleCheckedFileIndexes"
        @retryMagnetTask="handleRetryMagnetTask"
      />
    </div>

    <!-- 操作按钮 -->
    <template #actions>
      <div class="task-actions">
        <TaskLaterButton
          :task-data="taskData"
          :data-map="dataMap"
          :has-valid-tasks="hasValidTasks"
          :options-ext-data="optionsExtData || {}"
          :checked-file-indexes="checkedFileIndexes"
          scene="pre-new-mixed-task"
          @success="handleLaterSuccess"
          @error="handleLaterError"
          @cancel="handleLaterCancel"
          variant="secondary"
          size="lg"
        >
          稍后
        </TaskLaterButton>
        <div class="right-actions">
          <!-- 播放按钮 - 只在有可播放任务时显示 -->
          <PlayButton
            class="play-button"
            :checked-file-indexes="checkedFileIndexes"
            :data-map="dataMap || {}"
            :options-ext-data="optionsExtData || {}"
            :task-data="taskData"
            scene="pre-new-mixed-task"
            size="lg"
          />

          <DownloadButton
            scene="pre-new-mixed-task"
            :selected-path-type="selectedPathType"
            @submit="handleDownloadButtonSubmit"
            :disabled="isDownloadButtonDisabled"
          />
        </div>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import TaskList from '@root/modal-renderer/src/components/new-task/task-list/task-list.vue'
import DownloadButton from '@root/modal-renderer/src/components/new-task/download-button/download-button.vue'
import TaskLaterButton from '@root/modal-renderer/src/components/new-task/task-later-button/TaskLaterButton.vue'
import PlayButton from '@root/modal-renderer/src/components/new-task/play-button/PlayButton.vue'
import { PopUpNS } from '@root/common/pop-up'

import type {
  IUrlWithType,
  IUrlDataMap,
  TaskFileSelectionMap,
  DownloadEventParams,
  DownloadButtonSubmitParams,
  TaskExtDataMap,
} from '@root/modal-renderer/types/new-task.type'
import { DownloadPathType } from '@root/modal-renderer/types/new-task.type'
import { TaskType } from '@root/common/task/base'

// 导入 Provide/Inject 组合式函数
import {
  useDownloadHandler,
  useCloseWindowHandler,
} from '@root/modal-renderer/src/composables/useTaskDownload'

import * as PopUpTypes from '@root/common/pop-up/types'
import { usePositionMixinComponent } from '@root/modal-renderer/src/common/mixins'

// 导入类型定义
import type { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'
import { formatSize } from '@root/modal-renderer/src/utils.help'

// 使用基类的逻辑
const { overridePosition, resizeToFitContent } = usePositionMixinComponent()

const defaultPositionOptions = ref({
  autoSize: false,
  show: false,
  windowWidth: 680,
  windowHeight: 534,
  relatePos: PopUpTypes.RelatePosType.CenterParent,
  selector: '.dialog-content',
})

// Props 定义
interface Props {
  taskData: IUrlWithType[]
  dataMap?: IUrlDataMap
  visible?: boolean
  options?: ThunderNewTaskHelperNS.IShowPreNewTaskWindowOptions
  optionsExtData?: TaskExtDataMap
}

// Emits 事件类型定义
interface Emits {
  cancel: []
  'update:visible': [value: boolean]
  download: [params: DownloadEventParams]
}

const props = withDefaults(defineProps<Props>(), {
  taskData: () => [],
  dataMap: () => ({}),
  visible: true,
  options: () => ({}),
  optionsExtData: () => ({}),
})

const emit = defineEmits<Emits>()

// 使用 Provide/Inject 获取下载处理函数
const { handleDownload } = useDownloadHandler()

// 使用 Provide/Inject 获取关闭处理函数
const { handleCloseWindow } = useCloseWindowHandler()

// 响应式数据
const isParsingComplete = ref(false)
const isParsingFailed = ref(false)
const selectedTasks = ref<boolean[]>([])

// 记录子组件传递回来的选中文件索引
const checkedFileIndexes = ref<TaskFileSelectionMap>({})

// 计算属性
const selectedTasksCount = computed(() => {
  const count = selectedTasks.value.filter(Boolean).length
  return count
})

const totalSize = computed(() => {
  if (!props.taskData.length) return '0 B'

  const total = props.taskData.reduce((sum, task) => {
    const urlDetail = getUrlDetailFromDataMap(task.url)
    return sum + (urlDetail?.fileSize || 0)
  }, 0)

  return formatSize(total)
})

const dialogTitle = computed(() => {
  return props.options?.title || '新建任务'
})

// 将options.selectedPathType转换为DownloadPathType枚举
const selectedPathType = computed(() => {
  const pathType = props.options?.selectedPathType
  if (pathType === 'local') {
    return DownloadPathType.Local
  } else if (pathType === 'cloud') {
    return DownloadPathType.Cloud
  }
  return undefined
})

// 工具函数
const getTaskDisplayName = (task: IUrlWithType): string => {
  // 优先从 optionsExtData 获取自定义任务名称
  const customTaskNameFromOptions = props.optionsExtData?.[task.url]?.fileName
  if (customTaskNameFromOptions) {
    return customTaskNameFromOptions
  }

  // 其次从dataMap获取fileName
  const urlDetail = getUrlDetailFromDataMap(task.url || '')
  return urlDetail?.fileName || task.url || '未知文件'
}

const getFileType = (task: IUrlWithType): string => {
  // 首先尝试从dataMap获取fileType
  const urlDetail = getUrlDetailFromDataMap(task.url || '')

  if (urlDetail?.fileType) {
    return urlDetail.fileType.toUpperCase()
  }

  // 如果dataMap中没有，尝试从URL提取
  const fileName = urlDetail?.fileName || task.url || ''
  const extension = fileName.split('.').pop()

  return extension ? extension.toUpperCase() : 'UNKNOWN'
}

// 方法
const handleOpenChange = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = (scene: string = 'default') => {
  try {
    handleCloseWindow(scene)
  } catch (error) {
    console.error('[MixedTask] 关闭窗口失败:', error)
    emit('cancel')
  }
}

const handleConfirm = () => {
  // 播放功能现在由 PlayButton 组件处理
}

const handleCheckedFileIndexes = (checkedFiles: TaskFileSelectionMap) => {
  checkedFileIndexes.value = checkedFiles
}

const handleRetryMagnetTask = (taskInfo: any) => {
  // 处理重试磁力链的逻辑
}

/**
 * 处理 DownloadButton 提交事件
 */
const handleDownloadButtonSubmit = (params: DownloadButtonSubmitParams) => {
  // 构造下载参数
  const downloadParams: DownloadEventParams = {
    checkedFileIndexes: checkedFileIndexes.value,
    type: params.type === DownloadPathType.Local ? 'download' : 'cloud',
    path: params.path,
  }

  // 使用 Provide/Inject 方式直接调用父组件的下载处理函数
  handleDownload(downloadParams)

  // 关闭当前对话框
  handleClose('download-submit')
}

// 根据URL从dataMap获取详细信息的工具函数
const getUrlDetailFromDataMap = (url: string) => {
  return props.dataMap?.[url]
}

// 计算属性：判断是否有有效任务
const hasValidTasks = computed(() => {
  // 只判断taskData是否为空
  return props.taskData.length > 0
})

// 计算属性：计算已选中的文件数
const selectedFileCount = computed(() => {
  let totalCount = 0

  Object.values(checkedFileIndexes.value).forEach(selection => {
    if (selection?.fileIndexes && Array.isArray(selection.fileIndexes)) {
      totalCount += selection.fileIndexes.length
    }
  })

  return totalCount
})

// 计算属性：判断是否禁用下载按钮
const isDownloadButtonDisabled = computed(() => {
  return selectedFileCount.value <= 0
})

/**
 * 处理 TaskLaterButton 成功事件
 */
const handleLaterSuccess = (result: {
  success: boolean
  message: string
  savedCount?: number
  failedCount?: number
}) => {
  // TODO:埋点
  // handleClose('task_later_success')
}

/**
 * 处理 TaskLaterButton 错误事件
 */
const handleLaterError = (error: string) => {
  console.error('[MixedTask] TaskLaterButton 失败:', error)
}

/**
 * 处理 TaskLaterButton 取消事件
 */
const handleLaterCancel = () => {
  handleClose('task_later_cancel')
}

function updateWindowSize() {
  overridePosition(defaultPositionOptions.value)
  resizeToFitContent()
}

function showCurrentWindow() {
  const currentWindow = PopUpNS.getCurrentWindow()
  console.log('🔍 [onMounted] 显示窗口:', currentWindow)
  if (currentWindow) {
    currentWindow.show()
  } else {
    console.error('🔍 [showCurrentWindow] 获取当前窗口失败')
  }
}

onMounted(() => {
  console.info('🔍 [PreNewMixedTask] 挂载')
  updateWindowSize()
  nextTick(async () => {
    // 延迟显示窗口，确保所有内容都已渲染
    setTimeout(() => {
      showCurrentWindow()
    }, 100)
  })
})

watch(
  () => props,
  () => {
    updateWindowSize()
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
@import '@root/modal-renderer/src/components/new-task/pre-new-task/pre-new-task.scss';
</style>
