import { CallApiProxyImplWithIpcClient } from '../call-api-impl';
import * as BaseType from '../base'
import { P2spTask } from './p2sp-task';
import { GroupTask } from './group-task';
import { EmuleTask } from './emule-task';
import { BtTask } from './bt-task';

export class Task {
    private apiProxy: CallApiProxyImplWithIpcClient;
    taskId: number = 0;
    constructor(apiProxy: CallApiProxyImplWithIpcClient, id: number) {
        this.apiProxy = apiProxy;
        this.taskId = id;
    }

    public getId(): number {
        return this.taskId;
    }

    public toP2spTask(): P2spTask {
        return new P2spTask(this.apiProxy!, this.taskId);
    }

    public toGroupTask(): GroupTask {
        return new GroupTask(this.apiProxy, this.taskId);
    }

    public toEmuleTask(): EmuleTask {
        return new EmuleTask(this.apiProxy, this.taskId);
    }

    public toBtTask(): BtTask {
        return new BtTask(this.apiProxy, this.taskId);
    }

    public async initApiProxy(apiProxy: CallApiProxyImplWithIpcClient): Promise<void> {
        this.apiProxy = apiProxy;
    }

    public async isSupportPlay(): Promise<boolean> {
        let ret = await this.apiProxy!.CallApi('TaskManagerTaskIsSupportPlay', this.taskId);
        if (ret.bSucc) {
            return ret.result as boolean;
        }
        return false;
    }

    public async deleteTask(isDelete: boolean): Promise<number> {
        let ret = await this.apiProxy!.CallApi('TaskManagerTaskDeleteTask', this.taskId, isDelete);
        if (ret.bSucc) {
            return ret.result as number;
        }
        return 0;
    }

    public async start(): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskStartTask', this.taskId);
    }

    public async stop(r: BaseType.TaskStopReason): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskStopTask', this.taskId, r);
    }

    public async startNoWait(): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskStartNoWait', this.taskId);
    }

    public async recycle(): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskRecycle', this.taskId);
    }

    public async recoverFromRecycle(): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskRecoverFromRecycle', this.taskId);
    }

    public async reDownload(): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskReDownload', this.taskId);
    }

    public async rename(name: string): Promise<void> {
        await this.apiProxy!.CallApi('TaskManagerTaskReName', this.taskId, name);
    }

    public setDownloadStrategy(strategy: BaseType.DownloadStrategy, index: number): void {
        this.apiProxy!.CallApi('TaskManagerTaskSetDownloadStrategy', this.taskId, strategy, index);
    }

    public enableDcdnWithVipCert(vipCert: string, fileIndex: number): void {
        this.apiProxy!.CallApi('TaskManagerTaskEnableDcdnWithVipCert', this.taskId, vipCert, fileIndex);
    }

    public disableDcdnWithVipCert(fileIndex: number): void {
        this.apiProxy!.CallApi('TaskManagerTaskDisableDcdnWithVipCert', this.taskId, fileIndex);
    }

    public updateDcdnWithVipCert(vipCert: string, fileIndex: number): void {
        this.apiProxy!.CallApi('TaskManagerTaskUpdateDcdnWithVipCert', this.taskId, vipCert, fileIndex);
    }

    public setTaskExtStat(fileIndex: number, key: string, value: string): void {
        this.apiProxy!.CallApi('TaskManagerTaskSetTaskExtStat', this.taskId, fileIndex, key, value);
    }

    public setUserData(key: string, field: string, value: string): void {
        this.apiProxy!.CallApi('TaskManagerTaskSetUserData', this.taskId, key, field, value);
    }

    public async getTaskBase(): Promise<BaseType.TaskBase | null> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskGetTaskBase', this.taskId);
        if (info.bSucc) {
            return info.result as BaseType.TaskBase;
        }
        return null;
    }

    public async getUrl(): Promise<string> {
        let info = await this.apiProxy!.CallApi('TaskManagerTaskGetTaskUrl', this.taskId);
        if (info.bSucc) {
            return info.result as string;
        }
        return '';
    }
}