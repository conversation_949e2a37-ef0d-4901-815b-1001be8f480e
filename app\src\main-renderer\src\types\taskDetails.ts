import type { BtFileDownloadInfo, TaskBase, BtSubFileStatus} from '@root/common/task/base'
interface ITreeItem {
  key: string;
  name: string;
  parent: IBranch;
  type: 'branch' | 'leaf' | 'bt';
}
export interface ILeaf extends ITreeItem {
  fullPath: string;
  data: IExtraFileInfo;
}
export interface IBranch extends ITreeItem {
  children: (ILeaf | IBranch)[];
  data: {
    downloadSize: number;
    fileName: string;
    fileSize: number;
    isComplete: boolean;
    progress: number;
    downloadTotal: number;
    status?: number;
    taskType?: number;
    taskId?: number; // 如果是任务组的话就是子任务taskid
    index?: number; // 子文件索引
    errCode?: number; // 错误码
    isSupportPlay?: boolean;
    isExistDown: boolean;
    downFailNum: number;
    completedNum: number;
  };
}
export interface IExtraFileInfo {
  isComplete: boolean; // 是否完成
  progress: number; // 进度
  fileName: string; // 文件名
  fileSize: number; // 文件大小，
  taskId?: number; // 如果是任务组的话就是子任务taskid
  index?: number; // 子文件索引s
  filePath?: string; // 相对保存目录的路径
  url?: string; // 如果是任务组的话就是子任务url
  isNeedDownload?: boolean; // 是否需要下载
  downloadSize: number; // 已经下载的文件大小
  status?: number; // 子文件(任务)状态, status = 3时表示已完成 BtSubFileStatus|TaskStatus
  errCode?: number; // 错误码
  errMessage?: string; // 错误提示
  isSupportPlay?: boolean;
  isDelete?: boolean;
  taskType?: number;
  downloadTotal?: number; // 此处为bt的任务
}

export interface ISourceData {
  index: number;
  downloadSize: number;
  fileSize: number;
  status: number;
  errCode: number;
  filePath: string;
  fileName: string;
  isDownload: boolean;
  isSupportPlay: boolean;
}

export interface IFileInfo {
  taskId?: number; // 如果是任务组的话就是子任务taskId
  index?: number; // 子文件索引
  filePath?: string; // 相对保存目录的路径
  fileName?: string; // 文件名
  url?: string; // 如果是任务组的话就是子任务url
  isNeedDownload?: number; // 是否需要下载
  fileSize?: number; // 文件大小，
  downloadSize?: number; // 已经下载的文件大小
  cid?: string;
  gcid?: string;
  status?: number; // 子文件(任务)状态
  errCode?: number; // 错误码
  errMessage?: string; // 错误提示
  isSupportPlay?: boolean;
  isDelete?: boolean;
}

// export interface BriefTaskBase {
//   groupTaskId: number,
//   taskId: number,
//   taskName: string,
//   savePath: string,
//   taskType: TaskType,
//   taskStatus: TaskStatus,
//   isPanTask: boolean,
//   downloadSubTask: boolean,
//   fileSize: number,
//   downloadSize: number,
//   downloadSpeed: number,
//   createTime?: number,
//   completionTime?: number,
//   gcid: string,
//   cid: string,
//   errorCode?: number,
// }


export interface IHistorySpeedInfo {
  progress?: number;
  downloadSpeed?: number;
  addSpeed?: number;
  vipSpeedColor?: number;
}
export interface IHistorySpeedInfoMap {
  [index: number]: IHistorySpeedInfo;
}
export interface IHistoryTaskSpeedInfo {
  taskId?: number;
  aveSpeed?: number;
  maxSpeed?: number;
  progress?: number;
  speedInfoMap?: IHistorySpeedInfoMap;
}