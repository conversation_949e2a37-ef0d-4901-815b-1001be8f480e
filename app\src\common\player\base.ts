export interface XlRequestHeaders {
  [k: string]: string | string[] | undefined
};

export interface PlayerControlInitParam {
  xmp: boolean;
  openPlayerLog: boolean;
  logDir: string
  codecPath: string;
  subtitleCachePath: string;
  peerid: string;
  version: string;
  versionCode: number;
  configPath: string;
  dbDir: string; // 播放记录的数据库存放位置
  dbName: string;
  panPlayCache: string; // 云播的边下边播的缓存目录
  downloadServerDir: string;//传输库所在目录
}

export enum MediaState {
  MsUnKnown = -1,
  MsOpenning,
  MsSucc,
  MsPause,
  MsPlay,
  MsFailed,
  MsStop,
}
export enum MediaType {
  MtPan = 0, // 云盘播放
  MtNas, // NAS播放
  MtDownload, // 已完成播放或者边下边播
  MtWeb,
  MtOhosXmpLocal,
  MtNewXmpLocal,
  MtMacThunder,
  MtScrape, // 刮削播放
	MtUniversal, // PC迅雷上的万能播放
}
export interface MediaSpecialAttrPan {
  panFileId: string; //云盘文件fileid
  panRatioId: string; //云盘文件的清晰度,如果传入了就用指定的清晰度播放，否则用服务器记录的清晰度播放，如果没有记录，用本地记录的，还是没有，就用fileinfo里面的默认的播放
  panSpace?: string; // 云播：保险箱
  shareId?: string; // 分享播放相关
  passCodeToken?: string; // 分享播放相关
}

export interface MediaSpecialAttrNas {
  fileId: string; // nas 也是走云盘接口
  space: string;
  nfoId: number;
}

export interface MediaSpecialAttrTask {
  taskId: number; //下载任务的taskid
  fileIndex: number; //下载任务的index
  url?: string;
}

export interface LinkSubFileInfo {
  name: string;
  index: number;
}

export interface LinkInfo {
	name: string;
  url: string;
  index: number;
  subFileInfos: LinkSubFileInfo[];
}

export interface  MediaSpecialAttrUniversal {
	links: LinkInfo[],
	index: number,
};

export interface MediaAttribute {
  name: string; // 显示的名称
  gcid: string;
  playUrl: string; // 播放的url

  playFrom: string; // 播放来源
  zipPlay: number; // 是否压缩播放，1: 是 0：不是
  dlnaPlay: number; // 是否投屏播放 1: 是 0：不是

  mediaType: MediaType; // 播放的类型
  pan?: MediaSpecialAttrPan;
  task?: MediaSpecialAttrTask;
  nas?: MediaSpecialAttrNas;
  universal?: MediaSpecialAttrUniversal;
}

export interface RatioItem {
  mediaId: string;
  mediaName: string;
  resolutionName: string;
  iconLink: string;
  needMoreQuota: number; // 当前清晰度是否需要更高的身份 1：需要 0：不需要
  origin: number; // 是否原画 1：原画 0：非
  // 当needMoreQuota为false的时候，非会员可以播放，
  // 当needMoreQuota为true的时候，如果vip_type==vip.super，则需要超会，否则会员
  vipType: string;
}

export interface PlayListItem {
  id: string; // 该item的标识
  name: string; // 显示的名称
  url: string;
  mediaType: MediaType; // 播放的类型
}

export interface PlayHistoryItem {
  id: number;
  name: string; // 显示的名称
  mediaType: MediaType; // 播放的类型
  pos: number;
  duration: number;
  filePath: string;
  playTime: number;
  size: number;
  pan?: MediaSpecialAttrPan;
  task?: MediaSpecialAttrTask;
}

export interface PlayListItemMediaInfo {
  duration: number;
  pos: number;
  size: number;
  local: boolean; // 是否本地播放
  snapshot: string;
}

export enum OpenMediaReason {
  Omr_Normal = 0,
  Omr_CrashReopen, // 崩溃重新拉起
  Omr_ChangeHardwareSpeedup, // 软硬解码切换
  Omr_FailureReopen, // 播放失败重新播放
}

export enum SubtitleItemType {
  UnKnown = -1,
  Embed = 0, // 内挂
  Online, // 在线
  Local, // 本地
  Pan, // 云盘
}

// 云播的时候，online的字幕可能会当成内挂字幕；nas播放的时候，云盘里面的字幕当local字幕
export enum SubtitleCategory {
  Min = -1,
  Embed = 0, // 内挂
  Online, // 在线
  Local, // 本地
  Pan, // 云盘
  Max,
}

export interface SubtitleFontStyle {
  fontSize: number;
  fontColor: string;
  fontName: string;
}

export interface SubtitleItemDisplayInfo {
  // 字幕的唯一标识
  id: string;
  // 外面显示的字幕名称
  displayName: string;
  // 字幕路径
  filePath: string;
  // gcid
  gcid: string;
  // 字幕类型
  category: SubtitleCategory;
}

export interface PlayErrInfo {
  errCode: number;
  errMsg: string;
}

export interface PlaySpeedDisplayInfo {
  id: string;
  name: string;
  // 切换提示语的倍速显示
  showValue: string;
  needYearSuperVip: number;
  needSuperVip: number;
  needVip: number;
}

export enum ListPlaySequence {
  Single = 0, // 单个播放
  OneByOne, // 顺序播放
  SingleLoop, // 单个循环
  OneByOneLoop, // 循环播放
}

export enum ImageRatioType {
  Origin = 0,
  Stretch,
  Type_16_9,
  Type_4_3,
  Type_21_9
}

export interface ImageRatioItem {
  id: ImageRatioType,
  name: string,
}

export type TOpenMediaFrom =
  | 'associate_file' // 关联文件
  | 'xunlei' // 迅雷
  | 'right_click' // 文件右键

  | 'xl_download' // -> from:'file', filefrom: 'xl_download' 
  | 'xl_yunpan'  // -> from:'file', filefrom: 'xl_yunpan'
  | 'ali_yunpan' // -> from:'file', filefrom: 'ali_yunpan' 
  | 'bd_yunpan' // -> from:'file', filefrom: 'bd_yunpan' 
  | 'local_file' // -> from:'file', filefrom: 'local_file' 

  | 'front_page_open_file' // 首页_打开文件
  | 'open_link' // 打开链接
  | 'search' // 搜索
  | 'record' // 观看记录
  | 'front_page_record' // 首页_观看记录

  | 'drag_file' // 拖拽文件
  | 'rk_open_file' // 右键_打开文件
  | 'rk_open_folder' // 右键_打开文件夹
  | 'rk_open_link' // 右键_打开文件夹

  | 'recent-documents' // 应用右键 doct 最近历史

  // 片库
  | 'media_recent' // 片库最近
  | 'media_movie' // 片库电影
  | 'media_tv' // 片库电视剧
  | 'media_other' // 片库未匹配