<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import Button from '@root/common/components/ui/button/index.vue'
import SearchInput from '../search/index.vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import { PopUpNS } from '@root/common/pop-up'
import * as PopUpTypes from '@root/common/pop-up/types'
import { BrowserHelperNs } from '@root/common/helper/browser-config-helper'
import { ThunderHelper } from '@root/common/thunder-helper'
import { requestHelper } from '@/utils/request'
import XMPMessage from '@root/common/components/ui/message/index'

const props = defineProps<{
  isPin?: boolean
  isWindowMax?: boolean
  canGoBack?: boolean
  canGoForward?: boolean
  canRefresh?: boolean
}>()

const emit = defineEmits<{
  (e: 'minize'): void
  (e: 'max'): void
  (e: 'close'): void
  (e: 'search'): void
  (e: 'goBack'): void
  (e: 'goForward'): void
  (e: 'refresh'): void
  (e: 'add'): void
}>()

// 浏览器插件状态管理
const pluginStatus = ref({
  hasActiveBrowser: false,
  needsAttention: false,
  loading: false
})

// 计算是否显示红点
const showPluginRedDot = computed(() => {
  return pluginStatus.value.needsAttention && !pluginStatus.value.hasActiveBrowser
})

// 菜单状态
const showMoreSubmenu = ref(false)

// 搜索输入框ref
const searchInputRef = ref<InstanceType<typeof SearchInput> | null>(null)

// 处理按钮点击
const handleGoBack = () => {
  if (props.canGoBack !== false) {
    emit('goBack')
  }
}

const handleGoForward = () => {
  if (props.canGoForward !== false) {
    emit('goForward')
  }
}

const handleRefresh = () => {
  if (props.canRefresh !== false) {
    emit('refresh')
  }
}

// 插件相关处理
const handlePluginClick = async () => {
  console.log('打开浏览器插件设置')
  await PopUpNS.showBrowserConfigGuid()
  checkPluginStatus() // Re-check after dialog closes
}

// 检查插件状态
const checkPluginStatus = async () => {
  if (pluginStatus.value.loading) return

  pluginStatus.value.loading = true
  try {
    const browserInfos = await BrowserHelperNs.getInfos()
    const browsers = Array.from(browserInfos.values())

    const hasActiveBrowser = browsers.some(info =>
      info.browserExist && info.browserAddonInstall && info.browserAddonOpen
    )

    const needsAttention = browsers.some(info =>
      info.browserExist && (!info.browserAddonInstall || !info.browserAddonOpen)
    )

    pluginStatus.value = {
      hasActiveBrowser,
      needsAttention,
      loading: false
    }

    console.log('插件状态检查完成:', pluginStatus.value)
  } catch (error) {
    console.error('检查插件状态失败:', error)
    pluginStatus.value.loading = false
  }
}

// 主菜单项（包含子菜单结构）
const mainMenuItems = computed(() => [
  {
    key: 'settings',
    label: '设置',
    icon: 'xl-icon-setting'
  },
  {
    key: 'checkUpdate',
    label: '检查更新',
    icon: 'xl-icon-update'
  },
  {
    key: 'contact',
    label: '联系客服',
    icon: 'xl-icon-customer-service'
  },
  {
    key: 'more',
    label: '更多',
    icon: 'xl-icon-more',
    children: [
      {
        key: 'diagnose',
        label: '下载诊断',
        icon: 'xl-icon-diagnose'
      },
      {
        key: 'about',
        label: '关于迅雷',
        icon: 'xl-icon-about'
      },
      {
        key: 'privacy',
        label: '隐私声明',
        icon: 'xl-icon-privacy'
      }
    ]
  },
  {
    key: 'exit',
    label: '退出迅雷',
    icon: 'xl-icon-exit'
  }
])

// 统一菜单选择处理
const handleMenuSelect = (key: string) => {
  switch (key) {
    case 'settings':
      handleSettings()
      break
    case 'checkUpdate':
      handleCheckUpdate()
      break
    case 'contact':
      handleContactSupport()
      break
    case 'diagnose':
      handleDiagnose()
      break
    case 'about':
      handleAbout()
      break
    case 'privacy':
      handlePrivacy()
      break
    case 'exit':
      handleExit()
      break
  }
}

// 各种功能处理函数
const handleSettings = () => {
  PopUpNS.showSettingDlg()
}

const handleCheckUpdate = async () => {
  XMPMessage({ message: '敬请期待！' })
  console.log('检查更新')
  // TODO: 校验当前版本是否为最新
  // 模拟检查更新逻辑
  // const isLatest = Math.random() > 0.5 // 模拟结果
  // if (isLatest) {
  //   console.log('当前已是最新版本')
  //   // TODO: 触发最新版本弹窗
  // } else {
  //   console.log('发现新版本，开始下载')
  //   // TODO: 下载新版本
  // }
}

const handleContactSupport = async () => {
  await requestHelper.ensureUserInfo()
  const pid = 'F8633F78AF46TJEQ'
  const version = requestHelper.getAppVersionCode()
  const url = `https://misc-xl9-ssl.xunlei.com/pages/dist/#/feedback?version=${version}&pid=${pid}`
  ThunderHelper.openURLByDefault(url)
}

const handleDiagnose = () => {
  XMPMessage({ message: '敬请期待！' })
  // console.log('触发下载诊断弹窗')
  // TODO: 触发下载诊断弹窗
}

const handleAbout = () => {
  XMPMessage({ message: '敬请期待！' })
  // console.log('触发关于迅雷版本弹窗')
  // TODO: 触发版本弹窗
}

const handlePrivacy = () => {
  const url = 'https://i.xunlei.com/policy/privacy.html'
  ThunderHelper.openURLByDefault(url)
}

const handleExit = async () => {
  emit('close')
}

// 组件挂载时检查插件状态
onMounted(() => {
  checkPluginStatus()
})
</script>

<template>
  <div class="app-header-container">
    <div class="app-header-left">
      <div class="app-header-operation none-draggable">
        <Tooltip>
          <template #trigger>
            <div class="app-header-operation-btn">
              <Button variant="ghost" is-icon size="sm" :disabled="props.canGoBack === false"
                :class="{ 'disabled': props.canGoBack === false }" @click="handleGoBack">
                <i class="xl-icon-arrow-left"></i>
              </Button>
            </div>
          </template>
          <template #content>
            后退
          </template>
        </Tooltip>

        <Tooltip>
          <template #trigger>
            <div class="app-header-operation-btn">
              <Button variant="ghost" is-icon size="sm" :disabled="props.canGoForward === false"
                :class="{ 'disabled': props.canGoForward === false }" @click="handleGoForward">
                <i class="xl-icon-arrow-right"></i>
              </Button>
            </div>
          </template>
          <template #content>
            前进
          </template>
        </Tooltip>

        <Tooltip>
          <template #trigger>
            <div class="app-header-operation-btn">
              <Button variant="ghost" is-icon size="sm" :disabled="props.canRefresh === false"
                :class="{ 'disabled': props.canRefresh === false }" @click="handleRefresh">
                <i class="xl-icon-refresh"></i>
              </Button>
            </div>
          </template>
          <template #content>
            刷新
          </template>
        </Tooltip>
      </div>

      <!-- 搜索容器 -->
      <SearchInput class="none-draggable" ref="searchInputRef" />

      <Button variant="secondary" @click="emit('add')" v-if="!searchInputRef?.isSearchFocused"
        class="app-header-add-btn none-draggable">
        <i class="xl-icon-add app-header-add-icon"></i>
        新建
      </Button>

    </div>
    <div class="app-header-right none-draggable">
      <div class="app-header-operation">

        <!-- 浏览器插件按钮 -->
        <Tooltip>
          <template #trigger>
            <div class="app-header-operation-btn">
              <Button variant="ghost" is-icon size="sm" @click="handlePluginClick" class="plugin-button">
                <i class="xl-icon-topbar-plug-in"></i>
                <div v-if="showPluginRedDot" class="red-dot"></div>
              </Button>
            </div>
          </template>
          <template #content>
            万能下载插件
          </template>
        </Tooltip>

        <!-- 主菜单按钮 -->
        <DropdownMenu :items="mainMenuItems" :align="'end'" :side="'bottom'" :sideOffset="8" :subSideOffset="4"
          contentClass="app-header-menu-content" itemClass="app-header-menu-item"
          subTriggerClass="app-header-menu-subtrigger" subContentClass="app-header-submenu-content"
          @select="handleMenuSelect">
          <Tooltip>
            <template #trigger>
              <div class="app-header-operation-btn">
                <Button variant="ghost" is-icon size="sm">
                  <i class="xl-icon-topbar-more"></i>
                </Button>
              </div>
            </template>
            <template #content>
              主菜单
            </template>
          </Tooltip>
        </DropdownMenu>
        <div class="app-header-divider"></div>
        <div class="app-header-operation-btn">
          <Button variant="ghost" is-icon size="sm" @click="emit('minize')">
            <i class="xl-icon-topbar-minimize"></i>
          </Button>
        </div>
        <div class="app-header-operation-btn" v-if="props.isWindowMax">
          <Button variant="ghost" is-icon size="sm" @click="emit('max')">
            <i class="xl-icon-topbar-revert"></i>
          </Button>
        </div>
        <div class="app-header-operation-btn" v-else>
          <Button variant="ghost" is-icon size="sm" @click="emit('max')">
            <i class="xl-icon-topbar-maximize"></i>
          </Button>
        </div>
        <div class="app-header-operation-btn">
          <Button variant="ghost" is-icon size="sm" @click="emit('close')">
            <i class="xl-icon-topbar-close"></i>
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .app-header-add-btn {
    height: 36px;
    line-height: 13px;

    i {
      color: var(--font-font-3, #86909C);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;

      i {
        color: var(--font-font-4, #C9CDD4);
      }
    }
  }

  .app-header-operation {
    gap: 4px;
    display: flex;
    align-items: center;

    &-btn {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;

      button {
        color: var(--font-font-3, #86909C);
      }

    }
  }

  .app-header-left {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 14px;

    .app-header-add-icon {
      color: var(--font-font-2, #4E5769);
    }
  }

  .app-header-divider {
    width: 1px;
    height: 14px;
    background: var(--border-border-2, #E5E6EB);
    margin: 0 10px;
  }

  // 浏览器插件按钮样式
  .plugin-button {
    position: relative;

    .red-dot {
      position: absolute;
      top: 2px;
      right: 2px;
      width: 8px;
      height: 8px;
      background: var(--functional-error-default, #FF4D4F);
      border-radius: 50%;
      border: 1px solid var(--background-background-container, #fff);
      animation: redDotPulse 2s ease-in-out infinite;
    }
  }

  @keyframes redDotPulse {

    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }
}

// 主菜单样式
:global(.app-header-menu-content) {
  min-width: 180px;
  z-index: 9999;
}

:global(.app-header-menu-item) {
  display: flex;
  align-items: center;
  gap: 8px;

  .LeftSlot {
    i {
      color: var(--font-font-2, #4E5769);
      font-size: 16px;
    }
  }
}

:global(.app-header-menu-subtrigger) {
  display: flex;
  align-items: center;
  gap: 8px;

  .LeftSlot {
    i {
      color: var(--font-font-2, #4E5769);
      font-size: 16px;
    }
  }

  .DropdownMenuArrow {
    margin-left: auto;
  }
}

:global(.app-header-submenu-content) {
  min-width: 140px;
  z-index: 10000;
}
</style>
