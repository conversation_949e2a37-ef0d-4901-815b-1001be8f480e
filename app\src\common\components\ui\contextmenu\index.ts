import { ComponentInternalInstance, createVNode, render, shallowReactive, VNode } from 'vue'
import ComponentConstructor from './component.vue'
import { IContextMenuItem } from '@root/common/thunder-pan-manager/pan-sdk/utils/file'

let seed = 1

export interface CommonContextmenuHandler {
  close: () => void
}

export type CommonContextmenuContext = {
  id: string
  vnode: VNode
  handler: CommonContextmenuHandler
  vm: ComponentInternalInstance
  props: any
}

export interface ICommonContextmenuPosition {
  x: number
  y: number
}

export interface ICommonContextmenuOptions {
  menuList: IContextMenuItem[][]
  clickPosition: ICommonContextmenuPosition
  parentElement?: HTMLElement
  allowScroll?: boolean
  onClose?: () => void
  onMenuItemClick?: (item: IContextMenuItem) => void
}

export const instances: any[] = shallowReactive([])

export const getInstance = (id: string) => {
  const idx = instances.findIndex((instance) => instance.id === id)
  const current = instances[idx]
  let prev: CommonContextmenuContext | undefined
  if (idx > 0) {
    prev = instances[idx - 1]
  }
  return { current, prev }
}

const handleClose = (instance: CommonContextmenuContext) => {
  const idx = instances.indexOf(instance)
  if (idx === -1) return

  instances.splice(idx, 1)
  const { handler } = instance
  handler.close()
}

const handleCreateCommonContextmenu = (options: ICommonContextmenuOptions): CommonContextmenuContext => {
  const id = `common_contextmenu_${seed++}`
  const container = document.createElement('div')

  const props = {
    ...options,
    id,
    onClose: () => {
      if (options.onClose) {
        options.onClose()
      }
      handleClose(instance)
    },
    onDestroy: () => {
      render(null, container)
    },
  }

  const vnode = createVNode(ComponentConstructor, props)
  render(vnode, container)
  document.body.appendChild(container.firstElementChild!)

  const vm = vnode.component!
  const handler = {
    close: () => {
      vm.exposed!.visible.value = false
    },
  }

  const instance = {
    id,
    vnode,
    vm,
    handler,
    props: (vnode.component as any).props,
  }

  return instance
}

export const CreateCommonContextmenu = (options: ICommonContextmenuOptions) => {
  const instance = handleCreateCommonContextmenu(options)

  instances.push(instance)
  return instance.handler
}

export const closeAllContextmenu = () => {
  instances.forEach(instance => {
    instance.handler.close()
  })
}
