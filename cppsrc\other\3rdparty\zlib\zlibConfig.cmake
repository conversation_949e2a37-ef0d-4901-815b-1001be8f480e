set(name <PERSON><PERSON><PERSON>)
string(TOLOWER ${name} name_lower)
list(APPEND ${name}_INCLUDE ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/include)
list(APPEND ${name}_LIBRARY_DIRS ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/lib/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE})
list(APPEND ${name}_LIBS ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/lib/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE}/${name_lower}1.lib)
set(${name}_BINS_PATH ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/bin/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE}/)
message("     [Check 3rd-Party] ${name}_BINS_PATH: ${${name}_BINS_PATH}")
# 判断目录是否存在
if(EXISTS ${${name}_BINS_PATH})
    file(GLOB_RECURSE ${name}_BINS ${name}_BINS_PATH "${${name}_BINS_PATH}/*.*")
    message("                       ${name}_BINS: ${${name}_BINS}")    
else()
#    message("--------------Directory is NOT exist.")
endif()