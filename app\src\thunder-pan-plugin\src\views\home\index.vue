<script setup lang="ts">
import EmptyHolder from '@/components/empty-holder/index.vue'
import Button from '@root/common/components/ui/button/index.vue'
import DriveFileList from './drive-file/index.vue'
import CloudAddList from './cloud-add/index.vue'
import TransferFileList from './transfer-file/index.vue'
import FilterPop from '@root/common/components/ui/filter-pop/index.vue'

import { computed, onMounted, ref, useTemplateRef, watch } from 'vue';
import { ITabItem, TabsManager, ETabId } from '../../manager/tabs-manager'
import { FileOperationHelper } from '@/utils/file-operation';
import { useDriveRouterStore } from '@/store/drive-router-store';
import { useUserStore } from '@/store/user-store'
import { PanBusinessHelper } from '@/utils/business-helper'
import { FilterManager, IFilterData } from '@/manager/filter-manager'
import { GlobalEventHelper } from '@/utils/global-event-helper'
import { useCloudAddRouterStore } from '@/store/cloud-add-router-store'
import { useTransferRouterStore } from '@/store/transfer-router-store'
import { EPanPage, useHistoryStore } from '@/store/history-store'

const { isSignin } = useUserStore()
const { pushState } = useHistoryStore()
const { routerList_computed: cloudAddRouterList } = useCloudAddRouterStore()
const { currentParentFile: driveParentFile, routerList_computed: driveRouteList } = useDriveRouterStore()
const { currentParentFile: transferParentFile, routerList_computed: transferRouterList } = useTransferRouterStore()

const tabs = TabsManager.getInstance().init()
const currentTab = TabsManager.getInstance().getCurrentTab()
const currentFilterData = FilterManager.getInstance().getCurrentFilterData()

const isShowFilterPop = ref(false)
const FileListVm = useTemplateRef('DriveFileListVm')

const currentTabId = computed(() => currentTab.id)
const currentFilterKey = computed(() => {
  let filterKey = `${currentTabId.value}_`

  if (currentTab.id === ETabId.ALL) {
    filterKey += driveParentFile.value.id
  } else if (currentTab.id === ETabId.TRANSFER_FILE) {
    filterKey += transferParentFile.value.id
  }

  return filterKey
})
const currentTabParentFile = computed(() => currentTab.id === ETabId.ALL ? driveParentFile : transferParentFile)

function handleClickTabItem(tabItem: ITabItem) {
  let currentRouterList = driveRouteList
  if (tabItem.id === ETabId.TRANSFER_FILE) {
    currentRouterList = transferRouterList
  } else if (tabItem.id === ETabId.CLOUD_ADD) {
    currentRouterList = cloudAddRouterList
  }

  pushState({
    page: EPanPage.HOME,
    tab: tabItem.id,
    routes: currentRouterList.value as any
  })
  TabsManager.getInstance().setCurrentTab(tabItem.id)
}

function handleCreateFolder () {
  const { id, space } = driveParentFile.value
  FileOperationHelper.getInstance().createFolder(id, space)
}

function handleOpenFilter() {
  isShowFilterPop.value = true
}

function handleFilterPopClose() {
  isShowFilterPop.value = false
}

function handleFilterReset () {
  FilterManager.getInstance().resetByKey(currentFilterKey.value)
}

function handleFilterPopConfirm(result: IFilterData[]) {
  FilterManager.getInstance().updateFilterByKey(currentFilterKey.value, result)
  handleFilterPopClose()
}

function handleOpenSigninDialog () {
  PanBusinessHelper.getInstance().openSigninDialog()
}

function handleUpdateFilterManager () {
  FilterManager.getInstance().setCurrentCategory(currentFilterKey.value)
}

watch([ currentTabId, currentTabParentFile ], () => {
  handleUpdateFilterManager()
}, { deep: true })

onMounted(() => {
  FilterManager.getInstance().on(FilterManager.EventKey.FILTERS_UPDATED, (key, value) => {
    switch (currentTabId.value) {
      case ETabId.ALL: {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.DRIVE_LIST_REFRESH)
        break
      }
      case ETabId.CLOUD_ADD: {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.CLOUD_ADD_LIST_REFRESH)
        break
      }
      case ETabId.TRANSFER_FILE: {
        GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.TRANSFER_LIST_REFRESH)
        break
      }
    }
  })
})
</script>

<template>
  <div class="content">
    <!-- 内容与-头部 -->
    <div class="content-header">
      <div class="header-left">
        <Button
          v-for="tabItem in tabs"
          size="sm"
          :key="tabItem.id"
          :variant="currentTabId === tabItem.id ? 'default' : 'secondary'"
          @click="handleClickTabItem(tabItem)"
        >
          <tooltip :content="tabItem.count > 0 ? tabItem.count : ''">
            <span>{{ tabItem.title }}</span>
            <span v-if="tabItem.count > 0"> · {{ tabItem.count > 999 ? '999+' : tabItem.count }}</span>
          </tooltip>
        </Button>
      </div>

      <div class="header-right">
        <Button
          size="sm"
          variant="outline"
          :disabled="currentTabId !== ETabId.ALL || !isSignin"
          @click="handleCreateFolder"
        >
          <i class="xl-icon-general-newfolder-m"></i>
          <span>新建文件夹</span>
        </Button>

        <div class="filter-btn__wrapper">
          <Button
            size="sm"
            variant="outline"
            right-icon="xl-icon-topbar-close"
            :disabled="!currentFilterData.setting.enable || !isSignin"
            :has-right-icon="currentFilterData.count > 0"
            @click="handleOpenFilter"
            @right-icon-click="handleFilterReset"
          >
            <div
              class="filter-button-content"
              :class="{
                'is-active': currentFilterData.count > 0
              }"
            >
              <i class="xl-icon-filter"></i>
              <span v-if="currentFilterData.count > 0">筛选 · {{ currentFilterData.count }}</span>
            </div>
          </Button>

          <FilterPop
            v-if="isShowFilterPop"
            :filters-source="currentFilterData.sources"
            :filters-category-data="currentFilterData.categoryFilter"
            @close="handleFilterPopClose"
            @confirm="handleFilterPopConfirm"
          />
        </div>
      </div>
    </div>

    <!-- 内容区 -->
    <div class="content-body">
      <EmptyHolder
        v-if="!isSignin"
        text="请登录后查看"
        button-text="登录"
        @button-click="handleOpenSigninDialog"
      ></EmptyHolder>

      <template v-else>
        <DriveFileList v-show="currentTabId === 'all'" ref="DriveFileListVm" />
        <CloudAddList v-show="currentTabId === 'cloud-add'" />
        <!-- <TransferFileList v-show="currentTabId === 'transfer-file'" /> -->
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content {
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  .content-header {
    flex-shrink: 0;
    padding: 8px 40px;
    display: flex;
    justify-content: space-between;

    .header-left {
      display: flex;
      gap: 12px;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 13px;

      .filter-btn__wrapper {
        position: relative;

        .filter-button-content {
          display: flex;
          align-items: center;
          gap: 6px;

          &.is-active {
            color: var(--primary-primary-default);
          }
        }
      }
    }
  }

  .content-body {
    flex-grow: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
  }
}
</style>
