import { CallApiProxy } from '@root/common/call-api';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';

//import { server } from '@xunlei/node-net-ipc/dist/ipc-server';

export class Player<PERSON>allApiProxy implements CallApiProxy {
  private clientName: string = 'player';
  public async CallApi(name: string, ...args: any[]): Promise<{ bSucc: boolean, result?: any }> {
    try {
      let result = await client.callRemoteClientFunction(this.clientName, name, ...args);
      return {
        bSucc: true,
        result: result[0],
      }
    } catch (e) {
      return { bSucc: false };
    }
  }

  public AttachServerEvent(name: string, callback: (...args: any[]) => void): number {
    return client.attachServerEvent(name, (c: any, ...args: any[]) => {
      callback(...args);
    });
  }

  public DetachServerEvent(name: string, cookie: number): void {
    client.detachServerEvent(name, cookie);
  }
  public async DestoryObject(id: string): Promise<void> {
    client.callServerFunction('DestoryObject', id);
  }
}