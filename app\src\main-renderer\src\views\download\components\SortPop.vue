<script lang="ts" setup>
import { ref, onUnmounted, watch, nextTick, computed } from 'vue'
import { ISortMap } from '../types'

const emits = defineEmits<{
  (event: 'update:show', value: boolean): void
  (e: 'confirm', result: string): void
}>()

const props = withDefaults(defineProps<{
  sourceSortMap: ISortMap[]
  show: boolean
  selectSort: { download: string, completed: string }
  isDownload: boolean
}>(), {
  sourceSortMap: () => [],
  show: false
})

const downloadSortPop = ref<HTMLElement | null>(null)
const eventListeners = ref<{ type: string; handler: (e: Event) => void }[]>([])

const selectVal = computed(() => {
  const { download, completed } = props.selectSort
  if (props.isDownload) {
    return download
  }
  return completed
})

const isNode = (target: EventTarget | null): target is Node => {
  return target !== null && 'contains' in target
}

const bindGlobalEvents = () => {
  // 监听文档的点击、滚动、触摸移动事件
  // 'click', 'scroll', 'touchmove', 'contextmenu', 'blur', 
  ['wheel'].forEach(event => {
    const handler = (e: Event) => {
      // 若点击目标是菜单自身，不隐藏
      if (downloadSortPop.value && isNode(e.target) && downloadSortPop.value.contains(e.target)) return
      handleClose()
    }
    // 存储事件监听器引用
    eventListeners.value.push({
      type: event,
      handler: handler
    })
    document.addEventListener(event, handler)
  })
}

const handleConfirm = (val: string) => {
  emits('confirm', val)
}

function handleClose () {
  emits('update:show', false)
}

// 解绑全局事件
const unbindGlobalEvents = () => {
  eventListeners.value.forEach(({ type, handler }) => {
    document.removeEventListener(type, handler)
  })

  // 清空事件监听器数组
  eventListeners.value = []
}

watch(() => props.show, val => {
  nextTick(() => {
    if (val) {
      downloadSortPop.value?.focus()
      bindGlobalEvents()
    } else {
      unbindGlobalEvents()
    }
  })
}, { immediate: true })

onUnmounted(() => {
  unbindGlobalEvents()
});

</script>

<template>
  <div
    v-if="props.show"
    ref="downloadSortPop"
    :class="$style.sortPop"
    tabindex="0"
    @blur="handleClose"
  >
    <div :class="$style.row" v-for="item in sourceSortMap" :key="item.key">
      <div :class="$style.title">
        {{ item.title }}
      </div>
      <div :class="$style.sortPopList">
        <div
          :class="[$style.sortPopItem, (selectVal === sort.val) && $style.isSelect]"
          v-for="sort in item.list"
          :key="sort.val"
          @click="handleConfirm(sort.val)"
        >
          {{ sort.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" module>
.sortPop {
  position: absolute;
  top: 40px;
  right: -45px;
  width: 254px;
  border-radius: var(--border-radius-M);
  box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
  background: var(--background-background-container);
  padding: 18px;
  z-index: 1;

  .row {
    margin-top: 24px;

    &:first-of-type {
      margin-top: 0;
    }
  }

  .title {
    color: var(--font-font-1, rgba(39, 46, 59, 1));
    line-height: 22px;
    font-size: 13px;
  }

  .sortPopList {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
  }

  .sortPopItem {
    background: var(--button-button2-default);
    border-radius: var(--border-radius-S);
    color: var(--font-font-2);
    font-size: 13px;
    width: 100px;
    height: 32px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    border: 1px solid transparent;

    &:hover {
      background: var(--button-button2-hover);
    }

    &.isSelect {
      background: var(--primary-primary-background-default);
      color: var(--primary-primary-default);
      border-color: var(--primary-primary-default);
      box-sizing: border-box;
    }
  }

  .sortPopItemLabel {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    text-align: center;
    cursor: pointer;

  }
}
</style>