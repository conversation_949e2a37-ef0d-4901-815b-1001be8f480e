import { defineStore } from 'pinia'
import crypto from 'crypto'
import { ThunderHelper } from '@root/common/thunder-helper'
import * as BaseType from '@root/common/task/base';

interface IErrorItem {
  code?: string;
  message?: string;
}
interface IErrorCode {
  [key: string]: IErrorItem[];
}

interface IErrorTask {
  pc_client?: IErrorCode;
}
interface IErrorCodeResponse {
  result: number;
  message?: string;
  detailMessage?: string;
  task_info?: IErrorTask;
}

export const useTaskErrorCode = defineStore('taskErrorCode', {
  state: () => ({
    codeMap: new Map<string, Map<number, string>>(),
    errorCode: {} as IErrorCode,
    codes: {} as IErrorCode,
  }),
  actions: {
    generateMd5(data: any): string {
      let ret: string = '';
      const md5: crypto.Hash = crypto.createHash('md5');
      if (md5 !== null) {
        ret = md5.update(data).digest('hex');
      }
      return ret;
    },
    getThunderVersionNumber(): number {
      // client可能尚未启动
      const thunderPath: string = process.execPath
      const thunderVersionStr: string = ThunderHelper.getFileVersion(thunderPath);
      let thunderVersionNumber: number = 0;
      const version: string[] = thunderVersionStr.split('.');
      if (version && version.length === 4) {
        const v1: number = Number(version[0]).valueOf();
        const v2: number = Number(version[1]).valueOf();
        const v3: number = Number(version[2]).valueOf();
        const productType: number = 0x80;
        thunderVersionNumber = productType * Math.pow(2, 24) + v1 * Math.pow(2, 16) + v2 * Math.pow(2, 8) + v3;
      }
      return thunderVersionNumber;
    },
    getTaskErrorInfoByCode(errCode: number = 0): string {
      let ret: string = '未知错误，无法继续下载';
      // 当7XX错误码没有对应的提示时，比如706、707，让其对应采用701的提示
      if (errCode > 704 && errCode <= 750) {
        // 原来这里是799映射到701，改成751到799的不映射，这样便于中台直接下发错误码，
        // 根据服务器错误码动态显示内容
        errCode = 701;
      }
      const message: string = this.getMessage(BaseType.ErrorType.Download, errCode);
      if (message) {
        ret = message;
      } else {
        switch (errCode) {
          case 1:
            ret = '任务创建失败，无法继续下载';
            break;
          case 2:
            ret = '任务参数错误，无法继续下载';
            break;
          case 3:
            ret = '链接失效，无法继续下载';
            break;
          case 4:
            ret = '任务配置文件错误，无法继续下载';
            break;
          case 5:
            ret = '任务连接超时，无法继续下载';
            break;
          case 6:
            ret = '文件校验失败，无法继续下载';
            break;
          case 7:
            ret = '疑似包含违规内容，无法下载';
            break;
          case 8:
            ret = '下载引擎加速出错，无法继续下载';
            break;
          case 9:
            ret = '文件路径超出系统限制，无法继续下载';
            break;
          case 201:
            ret = '文件创建失败，无法继续下载';
            break;
          case 202:
            ret = '文件写入失败，无法继续下载';
            break;
          case 203:
            ret = '文件读取失败，无法继续下载';
            break;
          case 204:
            ret = '文件重命名失败，无法继续下载';
            break;
          case 205:
            ret = '磁盘空间不足，无法继续下载';
            break;
          case 211:
          case 212:
            ret = '当前下载目录无法写入数据，请尝试下载到其它目录';
            break;
          case 213:
            ret = '当前磁盘格式不支持大文件下载';
            break;
          case 401:
            ret = '权限验证未通过，无法继续下载';
            break;
          case 402:
            ret = '原始资源不存在，且未找到候选资源，无法继续下载';
            break;
          case 601:
            ret = 'BT任务已存在，无法继续下载';
            break;
          case 701:
            ret = '疑似包含违规内容，无法下载';
            break;
          case 702:
            ret = '账号存在异常，无法继续下载';
            break;
          case 703:
            ret = '根据当地法律法规，文件无法下载';
            break;
          case 704:
            ret = '应版权方要求，文件无法下载';
            break;
          case 20000:
          case BaseType.TaskError.DownloadSDKCrash:
          case BaseType.TaskError.DownloadSDKMissing:
            ret = '下载引擎未启动，无法继续下载';
            break;
          case 10002:
            ret = '种子文件不存在，无法继续下载';
            break;
          default:
            break;
        }
      }
      return ret;
    },
    getMessage(type: BaseType.ErrorType, code: number): string {
      let message: string = '';
      const map = this.codeMap.get(type);
      if (map) {
        message = map.get(code) || '';
      }
      return message;
    },
    getData(type: BaseType.ErrorType): string {
      let data: string = '';
      if (this.codes && this.codes[type]) {
        data = JSON.stringify(this.codes[type]);
      }
      return data;
    },
    setCodeMap(type: BaseType.ErrorType, list: IErrorItem[]): void {
      const map: Map<number, string> = new Map();
      for (const item of list) {
        if (item.code) {
          // 判断区间
          const len: number = item.code.length;
          const pos: number = item.code.search(/\-/);
          if (pos > 0 && pos < len - 1) {
            const start: number = Number(item.code.substring(0, pos));
            const end: number = Number(item.code.substring(pos + 1, len));
            for (let code: number = start; code <= end; code++) {
              map.set(code, item.message || '');
            }
          } else {
            const code: number = Number(item.code);
            map.set(code, item.message || '');
          }
        }
      }
      this.codeMap.set(type, map);
    },
    /** 获取远程错误码描述 */
    async getTaskConfigs () {
      const clientName: string = 'xl_pc';
      const clientVersion: number = (await this.getThunderVersionNumber()) || 0;
      console.log('>>>>>>>>>>>>> clientVersion', clientVersion)
      const timeStamp: number = Math.floor(new Date().getTime() / 1000);
      const random: number = Math.random();
      let key: string = '!@#$%^&*()QAZ' + clientName + clientVersion + timeStamp + random;
      key = this.generateMd5(key);
      key = key.toUpperCase();
      const uriParam: string =
        'client_name=' +
        clientName +
        '&client_version=' +
        clientVersion +
        '&ts=' +
        timeStamp +
        '&r=' +
        random +
        '&key=' +
        key;
      console.log('key', key);
      const url: string = `https://download-code-lixian-vip.xunlei.com/errcode?${uriParam}`;
      console.log('url', url);
      let configs: IErrorCodeResponse | null = null;
      try {
        const res = await fetch(url, { method: 'GET' })
        console.log('>>>>>>>>>>>>>>>>>>>>> res', res)
        if (res.ok) {
          const data = await res.json();
          console.log('>>>>>>>>>>>>>>>> data', data)
          if (data.result === 0) {
            configs = data;
          }
        }
      } catch (e) {
        console.error(e);
      }
      return configs;
    },
    async initTaskConfigs () {
      try {
        const response = await this.getTaskConfigs();
        console.log('>>>>>>>>>>>>>>> response', response)
        if (response && response.result === 0 && response.task_info && response.task_info.pc_client) {
          this.codes = response.task_info.pc_client;
          if (this.codes.speed_code) {
            this.setCodeMap(BaseType.ErrorType.Speed, this.codes.speed_code);
          }
          if (this.codes.download_code) {
            this.setCodeMap(BaseType.ErrorType.Download, this.codes.download_code);
          }
          if (this.codes.dcdn_code) {
            this.setCodeMap(BaseType.ErrorType.Dcdn, this.codes.dcdn_code);
          }
        }
      } catch (error) {
        console.error('>>> initTaskConfigs', error)
      }
    }
  },
})