{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["auto-imports.d.ts", "env.d.ts", "node_modules", "dist", "build", "release", "app-old"]}, "formatter": {"enabled": true, "indentStyle": "space"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"useConst": "info", "useNodejsImportProtocol": "info", "noUselessElse": "off", "useTemplate": "off", "noInferrableTypes": "off", "noNonNullAssertion": "off", "useImportType": "info", "noUnusedTemplateLiteral": "off", "noParameterAssign": "info", "useEnumInitializers": "info", "useShorthandFunctionType": "off", "useLiteralEnumMembers": "off", "useSingleVarDeclarator": "off"}, "suspicious": {"noExplicitAny": "info", "noImplicitAnyLet": "info", "noAsyncPromiseExecutor": "off", "noDoubleEquals": "warn"}, "complexity": {"noUselessConstructor": "info", "noForEach": "off", "noUselessTernary": "off", "noStaticOnlyClass": "off", "useOptionalChain": "off", "noThisInStatic": "off", "noBannedTypes": "off"}, "correctness": {"noVoidTypeReturn": "info", "noSwitchDeclarations": "info", "noConstantCondition": "info"}}}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "asNeeded", "trailingCommas": "all"}}}