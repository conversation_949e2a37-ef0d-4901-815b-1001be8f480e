<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import { PopUpNS } from '@root/common/pop-up'
import { ConsumeManagerNs } from '@root/common/consume/client/consume'
import { DownloadModelManagerClient } from '@root/common/ui-operation/client/download-model'
import XMPMessage from '@root/common/components/ui/message';
import SvgIcon from '@root/common/components/ui/Dialog/svg-icon.vue'
import { usePositionMixinComponent } from '@/common/mixins';
import * as PopUpTypes from '@root/common/pop-up/types'
import * as BaseType from '@root/common/task/base'

interface IProps {
  title?: string
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
  // Content props
  taskName?: string
  infoText?: string
  infoType?: 'success' | 'error'
  fileIconClass?: string
  task?: BaseType.TaskBase
}

const props = defineProps<{
  options: {
    data: IProps,
  }
}>()

const data = ref<IProps>(props.options.data)

// 使用基类的逻辑
const { overridePosition, resizeToFitContent } = usePositionMixinComponent();

resizeToFitContent(400, 172)

// 重写控制位置基类非响应式数据
overridePosition({
  autoSize: true,
  show: true,
  relatePos: PopUpTypes.RelatePosType.RightBottom,
  selector: '.download-task-message-container',
})

const handleClose = async () => {
  const currentWindow = PopUpNS.getCurrentWindow()
  currentWindow.close()
}



let autoCloseTimer: any = null

const handleAutoClose = () => {
  autoCloseTimer = setTimeout(() => {
    handleClose()
  }, 10000)
}

const resetAutoCloseTimer = () => {
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer)
  }
  handleAutoClose()
}

const clearAutoCloseTimer = () => {
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer)
  } 
}


onMounted(() => {
  console.log('>>>>>>>>>>>>>>>>>> data', data.value)
  handleAutoClose()

  document.addEventListener('mouseenter', clearAutoCloseTimer)
  document.addEventListener('mouseleave', resetAutoCloseTimer)
})

onBeforeUnmount(() => {
  document.removeEventListener('mouseenter', clearAutoCloseTimer)
  document.removeEventListener('mouseleave', resetAutoCloseTimer)
  clearAutoCloseTimer()
})

watch(() => props.options.data, (newVal) => {
  console.log('>>>>>>>>>>>>>>>>>> data', newVal)
  if (newVal) {
    data.value = newVal
    resetAutoCloseTimer()
  }
})


/** 打开文件 */
const openFile = async (task: BaseType.TaskBase) => {
  console.log('>>>>>>> open file', task)
  const isSuccess = await ConsumeManagerNs.openTaskFolder(task.taskId)
  console.log('> code', isSuccess)
  if (isSuccess) {
    XMPMessage({
      message: '打开文件夹成功',
      type: 'success'
    })
  }
}

/** 消费文件 */
const customFile = (task: BaseType.TaskBase) => {
  console.log('>>>> 播放handlePlay', task)
  ConsumeManagerNs.consumeTask(task.taskId, -1)
}

const handleConfirm = () => {
  if (data.value?.task) {
    if (data.value?.infoType === 'success') {
      customFile(data.value?.task)
    } else {
      DownloadModelManagerClient.GetInstance().positionDownloadTask({taskId: data.value?.task.taskId, isPassive: true})
    }
  }
  
  handleClose()
}
const handleCancel = () => {
  if (data.value?.task) {
    openFile(data.value?.task)
  }
  handleClose()
}
</script>

<template>
  <div class="download-task-message-container">
    <div class="download-task-message-header draggable">
      <div class="download-task-message-title">
        <SvgIcon
          type="thunder"
        />
        {{ data.title }}
      </div>
      <Button variant="ghost" is-icon @click="handleClose" class="download-task-message-close-button none-draggable">
        <i class="xl-icon-general-close-m"></i>
      </Button>
    </div>
    <div class="download-task-message-content-box">
      <div class="file-info-box">
        <div :class="['file-icon-type', data?.fileIconClass]"></div>
        <div class="file-details">
          <div class="file-name">{{ data?.taskName }}</div>
          <div :class="['file-info', `info-${data?.infoType}`]">{{ data?.infoText }}</div>
        </div>
      </div>
    </div>
    <div class="download-task-message-footer">
      <Button v-if="data?.showCancel" class="download-task-message-button" variant="secondary" @click="handleCancel">{{ data?.cancelText }}</Button>
      <Button class="download-task-message-button" @click="handleConfirm">{{ data?.confirmText }}</Button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.download-task-message-container {
  width: 400px;
  height: 172px;
  padding: 14px 20px 20px 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  .download-task-message-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 20px;
    margin-bottom: 14px;
    font-size: 13px;
    color: var(--font-font-1, #272E3B);
  }
  .download-task-message-close-button {
    width: 20px;
    height: 20px;
  }
  .download-task-message-title {
    width: 100%;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    svg {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }
  }
  .download-task-message-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    width: 100%;
    height: 32px;
    margin-top: 20px;
  }
  .download-task-message-button {
    width: auto;
    padding: 0 22px;
    height: 32px;
    border-radius: var(--border-radius-S, 6px);
    font-size: 13px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.download-task-message-content-box {
  width: 400px;
  height: 52px;
  display: flex;
  align-items: center;
  gap: 8px;
  .file-info-box {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  .file-icon-type {
    width: 40px;
    height: 40px;
  }
  .file-details {
    width: 308px;
    display: flex;
    flex-direction: column;
    gap: 3px;
    .file-name {
      width: 100%;
      line-height: 22px;
      font-size: 14px;
      color: var(--font-font-1, #272e3b);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .file-info {
      font-size: 12px;
      line-height: 20px;
      text-align: left;

      &.info-success {
        color: var(--font-font-3, #86909c);
      }
      &.info-error {
        color: var(--functional-error-default, #ff4d4f);
      }
    }
  }
}
</style> 
<style lang="scss">

</style>