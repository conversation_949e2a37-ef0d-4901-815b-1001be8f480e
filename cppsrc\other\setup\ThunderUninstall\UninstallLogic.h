

/* this ALWAYS GENERATED file contains the definitions for the interfaces */


 /* File created by MIDL compiler version 6.00.0361 */
/* at Tue Aug 23 10:08:54 2011
 */
/* Compiler settings for .\InstallLogic.idl:
    Oicf, W1, Zp8, env=Win32 (32b run)
    protocol : dce , ms_ext, c_ext, robust
    error checks: allocation ref bounds_check enum stub_data 
    VC __declspec() decoration level: 
         __declspec(uuid()), __declspec(selectany), __declspec(novtable)
         DECLSPEC_UUID(), MIDL_INTERFACE()
*/
//@@MIDL_FILE_HEADING(  )

#pragma warning( disable: 4049 )  /* more than 64k source lines */


/* verify that the <rpcndr.h> version is high enough to compile this file*/
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif

#include "rpc.h"
#include "rpcndr.h"

#ifndef __RPCNDR_H_VERSION__
#error this stub requires an updated version of <rpcndr.h>
#endif // __RPCNDR_H_VERSION__

#ifndef COM_NO_WINDOWS_H
#include "windows.h"
#include "ole2.h"
#endif /*COM_NO_WINDOWS_H*/

#ifndef __InstallLogic_h__
#define __InstallLogic_h__

#if defined(_MSC_VER) && (_MSC_VER >= 1020)
#pragma once
#endif

/* Forward Declarations */ 

#ifndef __IInstallLogic_FWD_DEFINED__
#define __IInstallLogic_FWD_DEFINED__
typedef interface IInstallLogic IInstallLogic;
#endif 	/* __IInstallLogic_FWD_DEFINED__ */


#ifndef __InstallOperatorImpl_FWD_DEFINED__
#define __InstallOperatorImpl_FWD_DEFINED__

#ifdef __cplusplus
typedef class InstallOperatorImpl InstallOperatorImpl;
#else
typedef struct InstallOperatorImpl InstallOperatorImpl;
#endif /* __cplusplus */

#endif 	/* __InstallOperatorImpl_FWD_DEFINED__ */


/* header files for imported files */
#include "oaidl.h"
#include "ocidl.h"

#ifdef __cplusplus
extern "C"{
#endif 

void * __RPC_USER MIDL_user_allocate(size_t);
void __RPC_USER MIDL_user_free( void * ); 

/* interface __MIDL_itf_InstallLogic_0000 */
/* [local] */ 




extern RPC_IF_HANDLE __MIDL_itf_InstallLogic_0000_v0_0_c_ifspec;
extern RPC_IF_HANDLE __MIDL_itf_InstallLogic_0000_v0_0_s_ifspec;

#ifndef __IInstallLogic_INTERFACE_DEFINED__
#define __IInstallLogic_INTERFACE_DEFINED__

/* interface IInstallLogic */
/* [unique][uuid][object] */ 


EXTERN_C const IID IID_IInstallLogic;

#if defined(__cplusplus) && !defined(CINTERFACE)
    
    MIDL_INTERFACE("E7C5182F-48EB-42a0-88F6-C6196C873B98")
    IInstallLogic : public IUnknown
    {
    public:
    };
    
#else 	/* C style interface */

    typedef struct IInstallLogicVtbl
    {
        BEGIN_INTERFACE
        
        HRESULT ( STDMETHODCALLTYPE *QueryInterface )( 
            IInstallLogic * This,
            /* [in] */ REFIID riid,
            /* [iid_is][out] */ void **ppvObject);
        
        ULONG ( STDMETHODCALLTYPE *AddRef )( 
            IInstallLogic * This);
        
        ULONG ( STDMETHODCALLTYPE *Release )( 
            IInstallLogic * This);
        
        END_INTERFACE
    } IInstallLogicVtbl;

    interface IInstallLogic
    {
        CONST_VTBL struct IInstallLogicVtbl *lpVtbl;
    };

    

#ifdef COBJMACROS


#define IInstallLogic_QueryInterface(This,riid,ppvObject)	\
    (This)->lpVtbl -> QueryInterface(This,riid,ppvObject)

#define IInstallLogic_AddRef(This)	\
    (This)->lpVtbl -> AddRef(This)

#define IInstallLogic_Release(This)	\
    (This)->lpVtbl -> Release(This)


#endif /* COBJMACROS */


#endif 	/* C style interface */




#endif 	/* __IInstallLogic_INTERFACE_DEFINED__ */



#ifndef __InstallLogicLib_LIBRARY_DEFINED__
#define __InstallLogicLib_LIBRARY_DEFINED__

/* library InstallLogicLib */
/* [helpstring][version][uuid] */ 


EXTERN_C const IID LIBID_InstallLogicLib;

EXTERN_C const CLSID CLSID_InstallOperatorImpl;

#ifdef __cplusplus

class DECLSPEC_UUID("57F03323-C5FF-4ca7-8E55-087AACF046B1")
InstallOperatorImpl;
#endif
#endif /* __InstallLogicLib_LIBRARY_DEFINED__ */

/* Additional Prototypes for ALL interfaces */

/* end of Additional Prototypes */

#ifdef __cplusplus
}
#endif

#endif


