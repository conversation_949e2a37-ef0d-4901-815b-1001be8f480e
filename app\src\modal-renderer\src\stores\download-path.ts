import { computed, ref } from 'vue'

import { defineStore } from 'pinia'

import { DownloadPathNS } from '@root/common/config/download-path'
import { ThunderHelper } from '@root/common/thunder-helper'
import { validatePath } from '@root/modal-renderer/src/utils/new-task-util'

import type { PartitionSpaceInfo, PathDetailInfo } from '../../types/new-task.type'

/**
 * Download Path Store
 * 专门管理下载路径、驱动器列表和相关状态
 */
export const useDownloadPathStore = defineStore('downloadPath', () => {
  // ===== 状态管理 =====

  /**
   * 当前选中的下载路径
   */
  const currentDownloadPath = ref<string>('')

  /**
   * 驱动器/路径详细信息列表
   * 包含系统驱动器和历史下载路径
   */
  const pathInfoList = ref<PathDetailInfo[]>([])

  /**
   * 历史下载路径列表
   * 存储用户最近使用的下载路径
   */
  const historyDownloadPaths = ref<PathDetailInfo[]>([])

  /**
   * 系统驱动器列表
   * 存储系统可用的驱动器信息
   */
  const systemDrives = ref<PathDetailInfo[]>([])

  /**
   * 当前悬停的项目
   */
  const hoveredItem = ref<string>('')

  /**
   * 是否正在加载路径信息
   */
  const isLoadingPathInfo = ref<boolean>(false)

  /**
   * 默认下载路径
   */
  const defaultDownloadPath = ref<string>('')

  /**
   * 初始化标志，避免重复初始化
   */
  const isInitialized = ref<boolean>(false)

  // ===== 计算属性 =====

  /**
   * 是否有路径信息
   */
  const hasPathInfo = computed(() => pathInfoList.value.length > 0)

  /**
   * 是否有历史下载路径
   */
  const hasHistoryPaths = computed(() => historyDownloadPaths.value.length > 0)

  /**
   * 是否有系统驱动器
   */
  const hasSystemDrives = computed(() => systemDrives.value.length > 0)

  /**
   * 路径信息总数
   */
  const pathInfoCount = computed(() => pathInfoList.value.length)

  /**
   * 可删除的路径信息（通常是历史记录）
   */
  const removablePathInfo = computed(() => pathInfoList.value.filter(path => path.canDelete))

  /**
   * 不可删除的路径信息（通常是系统驱动器）
   */
  const nonRemovablePathInfo = computed(() => pathInfoList.value.filter(path => !path.canDelete))

  /**
   * 合并的驱动器列表（用于显示）
   */
  const displayDrives = computed(() => {
    // 先显示系统驱动器，再显示历史路径
    return [...systemDrives.value, ...historyDownloadPaths.value]
  })

  // ===== Actions =====

  /**
   * 设置当前下载路径
   * @param path - 要设置的路径
   * @param shouldRefresh - 是否在设置后刷新路径信息列表，默认为 true
   */
  const setCurrentDownloadPath = async (path: string, shouldRefresh: boolean = true) => {
    console.log('[DownloadPathStore] 设置当前下载路径:', path)
    currentDownloadPath.value = path
    console.log('[DownloadPathStore] 当前下载路径已更新为:', currentDownloadPath.value)
    
    // 如果需要刷新，则更新路径信息列表
    if (shouldRefresh) {
      try {
        console.log('[DownloadPathStore] 设置路径后自动刷新路径信息列表')
        await forceRefreshPathInfoList()
      } catch (error) {
        console.error('[DownloadPathStore] 刷新路径信息列表失败:', error)
      }
    }
  }

  /**
   * 设置路径信息列表
   * @param pathList - 路径信息数组
   */
  const setPathInfoList = (pathList: PathDetailInfo[]) => {
    pathInfoList.value = [...pathList]
  }

  /**
   * 设置历史下载路径
   * @param paths - 历史路径数组
   */
  const setHistoryDownloadPaths = (paths: PathDetailInfo[]) => {
    historyDownloadPaths.value = [...paths]
  }

  /**
   * 设置系统驱动器列表
   * @param drives - 驱动器数组
   */
  const setSystemDrives = (drives: PathDetailInfo[]) => {
    systemDrives.value = [...drives]
  }

  /**
   * 添加历史下载路径
   * @param path - 要添加的路径信息
   */
  const addHistoryDownloadPath = (path: PathDetailInfo) => {
    // 检查路径是否已存在
    const existingIndex = historyDownloadPaths.value.findIndex(p => p.dir === path.dir)

    if (existingIndex > -1) {
      // 如果存在，更新该路径信息并移到最前面
      historyDownloadPaths.value.splice(existingIndex, 1)
    }

    // 将新路径添加到最前面
    historyDownloadPaths.value.unshift(path)

    // 限制历史记录数量（例如最多保存10个）
    if (historyDownloadPaths.value.length > 10) {
      historyDownloadPaths.value = historyDownloadPaths.value.slice(0, 10)
    }
  }

  /**
   * 移除指定的历史下载路径
   * @param pathDir - 要移除的路径目录
   */
  const removeHistoryDownloadPath = (pathDir: string) => {
    const index = historyDownloadPaths.value.findIndex(p => p.dir === pathDir)
    if (index > -1) {
      historyDownloadPaths.value.splice(index, 1)
    }
  }

  /**
   * 移除指定的路径信息
   * @param drive - 要移除的驱动器信息
   */
  const removePathInfo = (drive: PathDetailInfo) => {
    // 从路径信息列表中移除
    const pathIndex = pathInfoList.value.findIndex(p => p.dir === drive.dir)
    if (pathIndex > -1) {
      pathInfoList.value.splice(pathIndex, 1)
    }

    // 从历史路径中移除
    removeHistoryDownloadPath(drive.dir)
  }

  /**
   * 清空历史下载路径
   */
  const clearHistoryDownloadPaths = () => {
    historyDownloadPaths.value = []
    // 同时从路径信息列表中移除可删除的路径
    pathInfoList.value = pathInfoList.value.filter(path => !path.canDelete)
  }

  /**
   * 设置悬停项目
   * @param item - 悬停的项目标识
   */
  const setHoveredItem = (item: string) => {
    hoveredItem.value = item
  }

  /**
   * 清除悬停项目
   */
  const clearHoveredItem = () => {
    hoveredItem.value = ''
  }

  /**
   * 设置加载状态
   * @param loading - 是否正在加载
   */
  const setLoadingPathInfo = (loading: boolean) => {
    isLoadingPathInfo.value = loading
  }

  /**
   * 设置默认下载路径
   * @param path - 默认路径
   */
  const setDefaultDownloadPath = (path: string) => {
    defaultDownloadPath.value = path
  }

  /**
   * 根据路径目录查找路径信息
   * @param dir - 路径目录
   * @returns 匹配的路径信息或undefined
   */
  const findPathInfoByDir = (dir: string): PathDetailInfo | undefined => {
    return pathInfoList.value.find(path => path.dir === dir)
  }

  /**
   * 根据路径查找本地磁盘路径信息
   * 从路径中提取磁盘信息，例如：C:\迅雷下载 -> C盘
   * @param path - 完整路径
   * @returns 本地磁盘路径信息或undefined
   */
  const findLocalPanPathInfoByDir = (path: string): PathDetailInfo | undefined => {
    // 先验证路径
    const validatedPath = validatePath(path)

    try {
      // 使用 ThunderHelper.getPartitionSpace 获取磁盘空间信息
      const spaceInfo = ThunderHelper.getPartitionSpace(validatedPath)
      
      // 确保 spaceInfo 符合 PartitionSpaceInfo 类型
      const defaultSpaceInfo: PartitionSpaceInfo = {
        total: 0,
        free: 0,
      }

      const newPathInfo: PathDetailInfo = {
        dir: validatedPath,
        alias: validatedPath, // 使用磁盘路径作为别名
        canDelete: false, // 系统磁盘不可删除
        spaceInfo: spaceInfo || defaultSpaceInfo,
      }

      console.log('[DownloadPathStore] 路径空间信息:', newPathInfo)
      return newPathInfo
    } catch (error) {
      console.error('[DownloadPathStore] findLocalPanPathInfoByDir: 获取磁盘空间信息失败:', error)
      return undefined
    }
  }

  /**
   * 检查路径是否存在于历史记录中
   * @param dir - 路径目录
   * @returns 是否存在
   */
  const hasHistoryPath = (dir: string): boolean => {
    return historyDownloadPaths.value.some(path => path.dir === dir)
  }

  /**
   * 更新路径信息
   * @param updatedPath - 更新的路径信息
   */
  const updatePathInfo = (updatedPath: PathDetailInfo) => {
    const index = pathInfoList.value.findIndex(p => p.dir === updatedPath.dir)
    if (index > -1) {
      pathInfoList.value[index] = { ...updatedPath }
    }

    // 如果是历史路径，同时更新历史记录
    const historyIndex = historyDownloadPaths.value.findIndex(p => p.dir === updatedPath.dir)
    if (historyIndex > -1) {
      historyDownloadPaths.value[historyIndex] = { ...updatedPath }
    }
  }

  /**
   * 获取当前下载路径
   * 优先返回当前下载路径，如果为空则返回默认下载路径，如果默认路径也为空则返回第一个可用路径
   * @returns 当前下载路径
   */
  const getCurrentDownloadPath = (): string => {
    // 优先返回当前下载路径
    if (currentDownloadPath.value && currentDownloadPath.value.trim()) {
      return currentDownloadPath.value
    }

    // 如果当前路径为空，返回默认路径
    if (defaultDownloadPath.value && defaultDownloadPath.value.trim()) {
      return defaultDownloadPath.value
    }

    // 如果默认路径也为空，返回第一个可用路径
    if (pathInfoList.value.length > 0) {
      return pathInfoList.value[0].dir
    }

    // 如果都没有，返回默认后备路径
    console.warn('[DownloadPathStore] 无可用下载路径，使用后备路径')
    return 'C:\\download'
  }

  /**
   * 初始化路径信息列表
   * 获取所有下载路径详细信息并更新 store
   */
  const initializePathInfoList = async (): Promise<void> => {
    // 如果已经初始化过且数据不为空，直接返回
    if (isInitialized.value && pathInfoList.value.length > 0) {
      console.log('[DownloadPathStore] 路径信息已初始化，跳过重复初始化')
      return
    }

    try {
      setLoadingPathInfo(true)
      console.log('[DownloadPathStore] 开始初始化路径信息列表...')

      const paths = await DownloadPathNS.getAllPaths()

      // 获取到path后，遍历每个path，再去获取path对应的 磁盘空间信息
      const pathsWithSpaceInfo = await Promise.all(
        paths.map(async path => {
          const spaceInfo = ThunderHelper.getPartitionSpace(path.dir)

          // 确保spaceInfo符合PartitionSpaceInfo类型
          const defaultSpaceInfo: PartitionSpaceInfo = {
            total: 0,
            free: 0,
          }

          const pathDetail: PathDetailInfo = {
            dir: path.dir,
            alias: path.alias || path.dir, // 确保 alias 不为空
            canDelete: path.canDelete ?? false, // 确保 canDelete 是布尔值
            spaceInfo: spaceInfo || defaultSpaceInfo, // 确保spaceInfo不为undefined
          }
          return pathDetail
        })
      )

      // 更新 store 中的路径信息列表
      setPathInfoList(pathsWithSpaceInfo)

      // 获取配置的默认下载路径
      const configDefaultPath = await DownloadPathNS.getDefaultPath()
      
      // 处理默认下载路径设置
      if (configDefaultPath && configDefaultPath.trim()) {
        // 使用配置中的默认路径
        setDefaultDownloadPath(configDefaultPath)
        console.log('[DownloadPathStore] 设置默认下载路径:', configDefaultPath)
      } else if (pathsWithSpaceInfo.length > 0) {
        // 如果配置中没有默认路径，则使用第一个路径作为默认路径
        const firstPath = pathsWithSpaceInfo[0]
        setDefaultDownloadPath(firstPath.dir)
        console.log('[DownloadPathStore] 配置中无默认路径，使用第一个路径作为默认:', firstPath.dir)
      }

      // 如果没有当前下载路径，设置为默认路径
      if (!currentDownloadPath.value || currentDownloadPath.value.trim() === '') {
        await setCurrentDownloadPath(defaultDownloadPath.value, false)
        console.log('[DownloadPathStore] 设置当前下载路径:', currentDownloadPath.value)
      }

      // 标记为已初始化
      isInitialized.value = true

      console.log('[DownloadPathStore] 路径信息列表初始化完成:', pathsWithSpaceInfo)
      console.log('[DownloadPathStore] 默认下载路径:', defaultDownloadPath.value)
      console.log('[DownloadPathStore] 当前下载路径:', currentDownloadPath.value)
    } catch (error) {
      console.error('[DownloadPathStore] 初始化路径信息列表失败:', error)
      // 出错时设置为空数组，但不标记为已初始化，以便下次重试
      setPathInfoList([])
    } finally {
      setLoadingPathInfo(false)
    }
  }

  /**
   * 重新加载路径信息列表
   * 刷新当前的路径信息
   */
  const refreshPathInfoList = async (): Promise<void> => {
    await initializePathInfoList()
  }

  /**
   * 强制重新初始化路径信息列表
   * 忽略初始化状态，强制重新获取路径信息
   */
  const forceRefreshPathInfoList = async (): Promise<void> => {
    console.log('[DownloadPathStore] 强制刷新路径信息列表')
    isInitialized.value = false
    await initializePathInfoList()
  }

  /**
   * 重置Store状态
   */
  const resetStore = () => {
    currentDownloadPath.value = ''
    pathInfoList.value = []
    historyDownloadPaths.value = []
    systemDrives.value = []
    hoveredItem.value = ''
    isLoadingPathInfo.value = false
    defaultDownloadPath.value = ''
    isInitialized.value = false
  }

  // ===== 导出 =====
  return {
    // 状态
    currentDownloadPath,
    pathInfoList,
    historyDownloadPaths,
    systemDrives,
    hoveredItem,
    isLoadingPathInfo,
    defaultDownloadPath,
    isInitialized,

    // 计算属性
    hasPathInfo,
    hasHistoryPaths,
    hasSystemDrives,
    pathInfoCount,
    removablePathInfo,
    nonRemovablePathInfo,
    displayDrives,

    // 方法
    setCurrentDownloadPath,
    setPathInfoList,
    setHistoryDownloadPaths,
    setSystemDrives,
    addHistoryDownloadPath,
    removeHistoryDownloadPath,
    removePathInfo,
    clearHistoryDownloadPaths,
    setHoveredItem,
    clearHoveredItem,
    setLoadingPathInfo,
    setDefaultDownloadPath,
    findPathInfoByDir,
    findLocalPanPathInfoByDir,
    hasHistoryPath,
    updatePathInfo,
    getCurrentDownloadPath,
    initializePathInfoList,
    refreshPathInfoList,
    forceRefreshPathInfoList,
    resetStore,
  }
})
