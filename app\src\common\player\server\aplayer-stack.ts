import { LifeObjectContainor } from '@root/common/life-object-containor';
import requireNodeFile from '@root/common/require-node-file';
import { GetXxxNodePath } from '@root/common/xxx-node-path';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import path from 'path';
import * as BaseType from '../base';
import { AplayerMedia } from '../impl/aplayer-media';
import { AplayerStack } from '../impl/aplayer-stack';

let thunderHelper = requireNodeFile(path.join(GetXxxNodePath(), 'thunder_helper.node'))
export class AplayerServerStack {
  private audioTrackPreparedCookie: number = 0;
  private audioTrackSelectChangeCookie: number = 0;
  private ratioPreparedCookie: number = 0
  private progressChangedCookie: number = 0;
  private progressSetToLatestCookie: number = 0;
  private playStateChangeCookie: number = 0;
  private playBufferEventCookie: number = 0;

  private subtitlePreparedCookie: number = 0;
  private subtitleSelectChangeCookie: number = 0;
  private subtitleManualAddCookie: number = 0;

  init() {
    AplayerStack.GetInstance().attachMediaChangeEvent((m: AplayerMedia) => {
      console.log('server attachMediaChangeEvent fire');
      let id = LifeObjectContainor.GetInstance().addObject(m);
      client.broadcastEvent('AplayerStackMediaChangeEvent', id);

      this.audioTrackPreparedCookie = m.attachAudioTrackPreparedEvent(() => {
        client.broadcastEvent('AplayerMeidaAudioTrackPrepared', id);
      });
      this.audioTrackSelectChangeCookie = m.attachAudioTrackSelectChangeEvent((index: number) => {
        client.broadcastEvent('AplayerMeidaAudioTrackSelectChange', id, index);
      });
      this.ratioPreparedCookie = m.attachRatioPreparedEvent(() => {
        client.broadcastEvent('AplayerMeidaRatioPrepared', id);
      });
      this.progressChangedCookie = m.attachProgressChangedEvent((p: number) => {
        client.broadcastEvent('AplayerMeidaProgressChange', id, p);
      });
      this.progressSetToLatestCookie = m.attachProgressSetToLatestEvent((pos: number) => {
        client.broadcastEvent('AplayerMeidaProgressSetToLatest', id, pos);
      });
      this.playStateChangeCookie = m.attachPlayStateChangeEvent((s: any) => {
        client.broadcastEvent('AplayerMeidaPlayStateChange', id, s);
      });
      this.playBufferEventCookie = m.attachPlayBufferEvent((buffer: boolean, local: boolean, t: any, speed: number) => {
        client.broadcastEvent('AplayerMeidaPlayBuffer', id, buffer, local, t, speed);
      });
      this.subtitlePreparedCookie = m.getSubtitleManager().attachPreparedEvent(() => {
        client.broadcastEvent('AplayerSubtitleManagerPrepared', id);
      });
      this.subtitleSelectChangeCookie = m.getSubtitleManager().attachSelectChangeEvent((subtitleItemId: string, type: BaseType.SubtitleCategory, isAuto: boolean) => {
        client.broadcastEvent('AplayerSubtitleManagerSelectChange', id, subtitleItemId, type, isAuto);
      });
      this.subtitleManualAddCookie = m.getSubtitleManager().attachManualAdd((subtitleItemId: string) => {
        client.broadcastEvent('AplayerSubtitleManagerManualAdd', id, subtitleItemId);
      });
    });
    AplayerStack.GetInstance().attachMediaClosedEvent((m: AplayerMedia) => {
      let id = LifeObjectContainor.GetInstance().addObject(m);
      client.broadcastEvent('AplayerStackMediaCloseEvent', id);

      m.detachAudioTrackPreparedEvent(this.audioTrackPreparedCookie);
      this.audioTrackPreparedCookie = 0;
      m.detachAudioTrackSelectChangeEvent(this.audioTrackSelectChangeCookie);
      this.audioTrackSelectChangeCookie = 0;
      m.detachRatioPreparedEvent(this.ratioPreparedCookie);
      this.ratioPreparedCookie = 0;
      m.detachProgressChangedEvent(this.progressChangedCookie);
      this.progressChangedCookie = 0;
      m.detachProgressSetToLatestEvent(this.progressSetToLatestCookie);
      this.progressSetToLatestCookie = 0;
      m.detachPlayStateChangeEvent(this.playStateChangeCookie);
      this.playStateChangeCookie = 0;
      m.detachPlayBufferEvent(this.playBufferEventCookie);
      this.playBufferEventCookie = 0;
      m.getSubtitleManager().detachPreparedEvent(this.subtitlePreparedCookie);
      this.subtitlePreparedCookie = 0;
      m.getSubtitleManager().detachSelectChangeEvent(this.subtitleSelectChangeCookie);
      this.subtitleSelectChangeCookie = 0;
      m.getSubtitleManager().detachManualAdd(this.subtitleManualAddCookie);
      this.subtitleManualAddCookie = 0;
    });

    AplayerStack.GetInstance().attachInitFinishEvent(() => {
      client.broadcastEvent('AplayerStackInitFinishEvent');
    });
    AplayerStack.GetInstance().attachQuitPlayEvent((m: AplayerMedia) => {
      let id = LifeObjectContainor.GetInstance().addObject(m);
      client.broadcastEvent('AplayerStackQuitPlayEvent', id);
    });

    client.registerFunctions({
      AplayerStackGetCurrPlayMedia: (context: any): string => {
        let id: string = '';
        let m = AplayerStack.GetInstance().getCurrPlayMedia();
        if (m) {
          id = LifeObjectContainor.GetInstance().addObject(m);
        }
        return id;
      },
      AplayerStackOpenMedia: (context: any, attr: BaseType.MediaAttribute) => {
        AplayerStack.GetInstance().openMedia(attr);
      },
      AplayerStackReOpenMedia: (context: any, r: BaseType.OpenMediaReason) => {
        AplayerStack.GetInstance().reOpenMedia(r);
      },
      AplayerStackCloseMedia: (context: any) => {
        AplayerStack.GetInstance().closeMedia();
      },
      AplayerStackPauseMedia: (context: any) => {
        AplayerStack.GetInstance().pauseMedia();
      },
      AplayerStackPlayMedia: (context: any) => {
        AplayerStack.GetInstance().playMedia();
      },
      AplayerStackGetSupportedPlaySpeedList: (context: any) => {
        return AplayerStack.GetInstance().getSupportedPlaySpeedList();
      },
      AplayerStackGetCurrentPlaySpeedId: (context: any) => {
        return AplayerStack.GetInstance().getCurrentPlaySpeedId();
      },
      AplayerStackSwitchSpeed: (context: any, id: string) => {
        AplayerStack.GetInstance().switchSpeed(id);
      },
      AplayerStackIsSilent: async (context: any) => {
        return await AplayerStack.GetInstance().isSilent();
      },
      AplayerStackSetSilent: (context: any, b: boolean) => {
        AplayerStack.GetInstance().setSilent(b);
      },
      AplayerStackGetVolume: async (context: any) => {
        return await AplayerStack.GetInstance().getVolume();
      },
      AplayerStackSetVolume: (context: any, n: number) => {
        AplayerStack.GetInstance().setVolume(n);
      },
      AplayerStackGetCurrentPlaySequence: async (context: any) => {
        return await AplayerStack.GetInstance().getCurrentPlaySequence();
      },
      AplayerStackSetPlaySequence: (context: any, seq: any) => {
        AplayerStack.GetInstance().setPlaySequence(seq);
      },
      AplayerStack: (context: any) => {
        AplayerStack.GetInstance().closePlayWindow();
      },
      AplayerStackIsMediaLocalPlay: async (context: any, attr: any) => {
        return await AplayerStack.GetInstance().isMediaLocalPlay(attr);
      },
      AplayerStackCreateSnapshot: async (context: any, filePath: string, out: string, w: number, h: number) => {
        return await AplayerStack.GetInstance().createSnapshot(filePath, out, w, h);
      },
      AplayerStackGetAplayerVersion: async (context: any) => {
        return await AplayerStack.GetInstance().getAplayerVersion();
      },
      AplayerStackImageRatio: (context: any, id: any) => {
        AplayerStack.GetInstance().imageRatio(id);
      },
      AplayerStackGetImageRatioItems: (context: any): any => {
        return AplayerStack.GetInstance().getImageRatioItems();
      },
      AplayerStackImageRotation: (context: any, angle: number): any => {
        AplayerStack.GetInstance().imageRotation(angle);
      },
      AplayerStackImageFlip: (context: any, bHorizontal: boolean, bRestore: boolean): any => {
        AplayerStack.GetInstance().imageFlip(bHorizontal, bRestore);
      },
      AplayerStackClosePlayWindow: (context: any) => {
        AplayerStack.GetInstance().closePlayWindow();
      },
      AplayerStackAddFloatShowRect: (context: any, name: string, rects: any[]) => {
        thunderHelper.addFloatWinodwShowRect(name, rects);
      },
      AplayerStackDeleteFloatShowRect: (context: any, name: string) => {
        thunderHelper.delFloatWinodwShowRect(name);
      },
    });
  }
}