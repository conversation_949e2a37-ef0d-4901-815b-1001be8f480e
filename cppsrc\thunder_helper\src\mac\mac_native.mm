
#import "mac_native.h"
#include <AppKit/AppKit.h>
#include <CoreGraphics/CoreGraphics.h>
#include <Foundation/Foundation.h>
#include <iostream>
#include <map>
#include <string>
#include <objc/objc.h>
#include <objc/message.h>
#include "SystemUtil.h"
//void task_callback(const char* result) {
//    printf("Task Info JSON: %s\n", result);
//}

// 用于存储指针和句柄映射
// static std::map<uintptr_t, id> viewMap;
 
// 实际的 NSView 创建和添加实现
int64_t createPlayerWindowImpl(int64_t parentWindowHandle) {
        // 将 uintptr_t 转换为 NSView
        NSView *parentView = (__bridge NSView *)(void *)parentWindowHandle;
//        NSView* parentView = reinterpret_cast<NSView*>(parentWindowHandle);
        if (!parentView) {
            NSLog(@"Invalid parent window handle");
            return 0;
        }

        // 创建一个新的 NSView 作为播放器窗口
        NSView* playerView = [[NSView alloc] initWithFrame:parentView.bounds];
        [playerView setWantsLayer:YES];
        playerView.layer.backgroundColor = [[NSColor blackColor] CGColor]; // 默认黑色背景
        
        // 确保在主线程执行 UI 操作
        dispatch_async(dispatch_get_main_queue(), ^{
            // [parentView addSubview:playerView];
             NSView *theView = parentView;
            [theView addSubview:playerView positioned:NSWindowBelow relativeTo:nil];
            // 设置自动布局约束，使播放器填满父视图
            playerView.translatesAutoresizingMaskIntoConstraints = NO;
            [NSLayoutConstraint activateConstraints:@[
                [playerView.leadingAnchor constraintEqualToAnchor:parentView.leadingAnchor],
                [playerView.trailingAnchor constraintEqualToAnchor:parentView.trailingAnchor],
                [playerView.topAnchor constraintEqualToAnchor:parentView.topAnchor],
                [playerView.bottomAnchor constraintEqualToAnchor:parentView.bottomAnchor]
            ]];
            
        });
        uintptr_t viewHandle = reinterpret_cast<uintptr_t>(playerView);
        // viewMap[viewHandle] = playerView;
        return (int64_t)viewHandle;
}

char * getPeerId() {
    NSString *peerId = [SystemUtil getPeerId];
    const char *cStr = [peerId UTF8String];
    char* result = strdup(cStr); // 复制字符串
    return result;
}

void preventSleep() {
    [SystemUtil preventSleep];
}

void allowSleep() {
    [SystemUtil allowSleep];
}
