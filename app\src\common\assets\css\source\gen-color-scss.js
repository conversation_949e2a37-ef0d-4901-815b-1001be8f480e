/* eslint-disable camelcase */
/* eslint-disable @typescript-eslint/no-var-requires */
// 引入路径模块
const path = require('path')
const fs = require('fs')

const SOURCE_FILE_PATH = path.join(__dirname, './color-variable.json')
const TARGET_PATH = path.join(__dirname, '../color-variable.scss')

// Function to transform variable name to CSS variable format
function transformVariableName (name) {
  // Replace / with - in variable names (e.g., "font/font-1" -> "--font-font-1")
  return `--${name.replace(/\//g, '-')}`
}

try {
  // Read the source JSON file
  const data = fs.readFileSync(SOURCE_FILE_PATH, 'utf8')
  const colorData = JSON.parse(data)

  // Initialize string containers for each theme
  let lightThemeStr = ''
  let darkThemeStr = ''
  let lightSvipThemeStr = ''
  let darkSvipThemeStr = ''
  let rootStr = ''

  // Process collections
  colorData.collections.forEach(collection => {
    const name = collection.name

    if (name.toLowerCase() === 'colors') {
      // Process "Colors" collection for all theme modes
      collection.modes.forEach(mode => {
        const modeName = mode.name

        if (modeName === 'Light brand') {
          // Process Light brand variables
          mode.variables.forEach(variable => {
            if (variable.type === 'color') {
              const cssVarName = transformVariableName(variable.name)
              lightThemeStr += `  ${cssVarName}: ${variable.value};\n`
            }
          })
        } else if (modeName === 'Dark brand') {
          // Process Dark brand variables
          mode.variables.forEach(variable => {
            if (variable.type === 'color') {
              const cssVarName = transformVariableName(variable.name)
              darkThemeStr += `  ${cssVarName}: ${variable.value};\n`
            }
          })
        } else if (modeName === 'Light svip') {
          // Process Light svip variables
          mode.variables.forEach(variable => {
            if (variable.type === 'color') {
              const cssVarName = transformVariableName(variable.name)
              lightSvipThemeStr += `  ${cssVarName}: ${variable.value};\n`
            }
          })
        } else if (modeName === 'Dark svip') {
          // Process Dark svip variables
          mode.variables.forEach(variable => {
            if (variable.type === 'color') {
              const cssVarName = transformVariableName(variable.name)
              darkSvipThemeStr += `  ${cssVarName}: ${variable.value};\n`
            }
          })
        }
      })
    } else if (name === '圆角') {
      // Process "圆角" (rounded corners) collection for root
      // Check if it has modes
      if (collection.modes && collection.modes.length > 0) {
        // Use the first mode's variables (assuming "Mode 1")
        const roundedCornerMode = collection.modes[0]
        if (roundedCornerMode.variables) {
          roundedCornerMode.variables.forEach(variable => {
            const cssVarName = transformVariableName(variable.name)
            // Do not add 'px' to values as requested
            rootStr += `  ${cssVarName}: ${variable.value}px;\n`
          })
        }
      }
    }
  })

  // Combine all sections into one CSS file
  const cssContent =
    `:root {
${rootStr}}

body {
${lightThemeStr}}

body.is-dark {
${darkThemeStr}}

body.is-svip {
${lightSvipThemeStr}}

body.is-dark.is-svip {
${darkSvipThemeStr}}
`

  // Write to single CSS file
  fs.writeFileSync(TARGET_PATH, cssContent)

  console.log('Successfully generated CSS theme file:')
  console.log(`- ${TARGET_PATH}`)

} catch (err) {
  console.error(`Error processing color variables: ${err}`)
}
