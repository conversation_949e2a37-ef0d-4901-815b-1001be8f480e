#ifndef __INSTALLENV_H__
#define __INSTALLENV_H__

#include <string>

class CInstallEnv
{
public:
    CInstallEnv();
    ~CInstallEnv();

public:
    static CInstallEnv* GetInstance();

    BOOL Initialize(HINSTANCE hInstance, LPTSTR lpszCmdLine);

private:
    long InitWorkPath();
    BOOL InitResoure();
    static unsigned _stdcall UncompressSDKThreadProc(void* vpParam);
    BOOL ExtractResource(LPCTSTR lpszResName, const wchar_t* lpszFilePath);
    BOOL AddDownloadSDKToFireWall();
    BOOL UncompressResource(const wchar_t* lpszSrcFilePath, const wchar_t* lpszDstFileDir);

private:
    static inline HRESULT IsDirectoryExists(LPCTSTR lpszDirPath, BOOL& bExists);
    static inline HRESULT CreateDirectory(LPCTSTR lpszDirpath);
    static inline HRESULT GetFileVersion(LPCTSTR lpszFilePath, int& nVer1, int& nVer2, int& nVer3, int& nVer4);
    static inline HRESULT GetFileVersion(LPCTSTR lpszFilePath, std::wstring& strVersion);

private:
    std::wstring m_strWorkPath;
    std::wstring m_strResPath;
    std::wstring m_strSDKPath;
    HANDLE m_hDownloadSDKEvent;
    HMODULE m_hModInstallEntry;
};

#define theInstallEnv CInstallEnv::GetInstance()

#endif  //__INSTALLENV_H__