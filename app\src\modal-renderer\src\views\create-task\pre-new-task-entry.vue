<template>
  <!-- 
    预解析任务入口组件
    功能：接收外部传入的任务数据，对taskData进行累积合并，其他选项进行覆盖更新
    使用场景：当需要分批传入任务数据时，避免重复覆盖，实现任务数据的增量添加
  -->
  <CreateTaskComponent :options="internalOptions" />
</template>

<script setup lang="ts">
import CreateTaskComponent from './create.vue'
import { watch, ref } from 'vue'
import { useNewTaskStore } from '../../stores/new-task'
import type { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'
import type { INewTaskDataItem } from '@root/modal-renderer/types/new-task.type'
import { TaskMergeUtils } from '@root/modal-renderer/src/utils/task-merge-utils'

/**
 * 预解析任务入口组件
 *
 * 主要功能：
 * 1. 接收外部传入的options配置
 * 2. 对taskData进行累积合并（新增任务追加到现有任务列表）
 * 3. 对其他配置选项进行覆盖更新（新配置完全替换旧配置）
 * 4. 维护内部状态，确保数据的一致性和连续性
 *
 * 使用场景：
 * - 批量导入任务时，支持分批传入，避免后一批覆盖前一批
 * - 动态更新任务配置时，保持任务数据的完整性
 * - 作为任务创建的中间层，统一管理任务数据的合并逻辑
 */

// 定义Props接口
interface Props {
  options?: {
    taskData?: ThunderNewTaskHelperNS.INewTaskData[]
    [key: string]: any
  }
}

// 定义props
const props = withDefaults(defineProps<Props>(), {
  options: () => ({}),
})

// 维护组件内部的options变量
// 作用：作为内部状态，管理taskData的累积合并和其他选项的覆盖更新
const internalOptions = ref<{
  taskData?: ThunderNewTaskHelperNS.INewTaskData[]
  [key: string]: any
}>({
  ...props.options,
  taskData: [], // 初始化为空数组，避免第一次合并时出现重复
})

// store 实例
const newTaskStore = useNewTaskStore()

// 监听 props.options.taskData 的变化
// 功能：实现taskData的累积合并，新任务追加到现有任务列表
watch(
  () => props.options.taskData,
  newTaskData => {
    console.log('🔄 预解析任务 props.options.taskData 变化:', newTaskData)

    if (newTaskData && Array.isArray(newTaskData)) {
      // 获取当前内部的taskData
      const currentTaskData = internalOptions.value.taskData || []

      // 使用工具类进行智能合并
      const { mergedTaskData, updatedOriginMap, urlCount } = TaskMergeUtils.mergeAndUpdate(
        currentTaskData,
        newTaskData,
        newTaskStore.getOriginDataMap()
      )

      // 更新内部options，保持其他字段不变，只更新taskData
      internalOptions.value = {
        ...internalOptions.value,
        taskData: mergedTaskData,
      }

      // 更新 originDataMap
      console.log('📋 更新后的originDataMap:', updatedOriginMap)
      newTaskStore.setOriginDataMap(updatedOriginMap)

      // 将合并后的任务数据写入到 store 的 rawTaskData 中
      newTaskStore.setRawTaskData(mergedTaskData as INewTaskDataItem[])

      console.log('✅ 智能合并完成:', {
        合并后的taskData长度: mergedTaskData.length,
        合并后的taskData: mergedTaskData,
        URL去重后的数量: urlCount,
      })
    }
  },
  { deep: true, immediate: true }
)

// 监听其他options属性的变化（除了taskData）
// 功能：对其他配置选项进行覆盖更新，保持taskData的合并状态
watch(
  () => props.options,
  (newOptions, oldOptions) => {
    console.log('🔄 预解析任务 props.options 其他属性变化:', newOptions)

    // 提取非 taskData 字段
    const { taskData: newTaskData, ...newOtherOptions } = newOptions || {}
    const { taskData: oldTaskData, ...oldOtherOptions } = oldOptions || {}

    // 检查非 taskData 字段是否发生变化
    const otherOptionsChanged = JSON.stringify(newOtherOptions) !== JSON.stringify(oldOtherOptions)
    if (!otherOptionsChanged) return

    // 获取当前的 taskData（保持合并状态）
    const currentTaskData = internalOptions.value.taskData || []

    // 更新内部options：其他字段覆盖，taskData保持合并状态
    internalOptions.value = {
      ...newOtherOptions,
      taskData: currentTaskData,
    }

    console.log('✅ 其他选项已覆盖，taskData保持合并状态')
  },
  { deep: true, immediate: true }
)
</script>

<style scoped>
/* 预解析任务入口组件的特定样式 */
</style>
