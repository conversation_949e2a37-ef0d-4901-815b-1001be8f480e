#include "xlipc_stream.h"
#include <assert.h>
#include "xlipc_define.h"

XLIPCStream::XLIPCStream(void) : index_(0)
{
}

XLIPCStream::~XLIPCStream(void)
{
	Clear();
}

long XLIPCStream::WriteUtf8( const char* lpString )
{
	assert(lpString);
	if (lpString == NULL)
	{
		return XLIPC_RESULT_INVALID_PARAM;
	}

	size_t nLength = ::strlen(lpString);

	Data data;
	data.enum_type_ = XLIPC_PTYPE_STRING;
	data.data_ = (void*)new char[sizeof(size_t) + nLength + 1];

	*(size_t*)data.data_ = nLength + 1;
	strcpy_s((char*)data.data_ + sizeof(size_t), nLength + 1, lpString);

	data_.push_back(data);
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteUnicode( const wchar_t* lpString )
{
	assert(lpString);
	if (lpString == NULL)
	{
		return XLIPC_RESULT_INVALID_PARAM;
	}

	size_t nLength = ::wcslen(lpString);

	Data data;
	data.enum_type_ = XLIPC_PTYPE_WSTRING;
	data.data_ = (void*)new char[sizeof(size_t) + (nLength + 1) * sizeof(wchar_t)];

	*(size_t*)data.data_ = (nLength + 1) * sizeof(wchar_t);
	::wcscpy_s((wchar_t*)((char*)data.data_ + sizeof(size_t)), nLength + 1, lpString);

	data_.push_back(data);
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteWord( WORD wValue )
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_WORD;
	data.data_ = (PVOID)wValue;

	data_.push_back(data);
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteDWord( DWORD dwValue )
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_DWORD;
	data.data_ = (PVOID)(INT_PTR)dwValue;

	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteInt(int iValue)
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_INT;
	data.data_ = (PVOID)(INT_PTR)iValue;

	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteUint(unsigned int iValue) {
	Data data;
	data.enum_type_ = XLIPC_PTYPE_UINT;
	data.data_ = (PVOID)(UINT_PTR)iValue;

	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteBoolean(bool bValue)
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_BOOLEAN;
	data.data_ = (PVOID)bValue;

	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteInt64(long long value)
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_INT64;
	data.data_ = (void*)new char[sizeof(__int64)];
	*(long long*)data.data_ = value;

	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteUint64(unsigned long long value) {
	Data data;
	data.enum_type_ = XLIPC_PTYPE_UINT64;
	data.data_ = (void*)new char[sizeof(unsigned __int64)];
	*(unsigned long long*)data.data_ = value;

	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteBytes( const unsigned char* lpBuffer, size_t nLength )
{
	assert(lpBuffer);
	if (lpBuffer == NULL)
	{
		return XLIPC_RESULT_INVALID_PARAM;
	}

	assert(nLength > 0);
	if (nLength <= 0)
	{
		return XLIPC_RESULT_INVALID_PARAM;
	}

	Data data;
	data.enum_type_ = XLIPC_PTYPE_BYTES;
	data.data_ = (void*)new char[sizeof(size_t) + nLength];
	*(size_t*)data.data_ = nLength;

	memcpy((char*)data.data_ + sizeof(size_t), lpBuffer, nLength);

	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadUtf8( char* lpString, size_t nLength, size_t* lpcbLength )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_STRING);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_STRING)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	size_t cbLength = *(size_t*)data_[index_].data_;
	const char* lpBegin = (char*)data_[index_].data_ + sizeof(size_t);

	if (lpString == NULL)
	{
		if (lpcbLength)
		{
			*lpcbLength = cbLength;
		}
		return XLIPC_RESULT_SUCCESS;
	}
	else
	{
		if (nLength < cbLength)
		{
			return XLIPC_RESULT_BUFFERTOOSMALL;
		}

		strncpy_s(lpString, cbLength, lpBegin, cbLength);

		if (lpcbLength)
		{
			*lpcbLength = cbLength;
		}

		index_++;

		return XLIPC_RESULT_SUCCESS;
	}
}

long XLIPCStream::ReadUnicode( wchar_t* lpString, size_t nLength, size_t* lpcbLength )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_WSTRING);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_WSTRING)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	size_t cbLength = *(size_t*)data_[index_].data_;
	const wchar_t* lpBegin = (wchar_t*)((char*)data_[index_].data_ + sizeof(size_t));

	if (lpString == NULL)
	{
		if (lpcbLength)
		{
			*lpcbLength = cbLength;
		}
		return XLIPC_RESULT_SUCCESS;
	}
	else
	{
		if (nLength < cbLength)
		{
			return XLIPC_RESULT_BUFFERTOOSMALL;
		}

		wcscpy_s(lpString, cbLength, lpBegin);

		if (lpcbLength)
		{
			*lpcbLength = cbLength;
		}

		index_++;

		return XLIPC_RESULT_SUCCESS;
	}
}

long XLIPCStream::ReadWord( WORD* wValue )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_WORD);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_WORD)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*wValue = (WORD)data_[index_].data_;
	index_++;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadDWord( DWORD* dwValue )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_DWORD);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_DWORD)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*dwValue = (DWORD)(DWORD_PTR)data_[index_].data_;
	index_++;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadInt( int* iValue )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_INT);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_INT)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*iValue = (int)(INT_PTR)data_[index_].data_;
	index_++;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadUint(unsigned int* iValue) {
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_UINT);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_UINT)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*iValue = (unsigned int)(UINT_PTR)data_[index_].data_;
	index_++;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadBoolean(bool* bValue)
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_BOOLEAN);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_BOOLEAN)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*bValue = !!data_[index_].data_;
	index_++;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::Readint64( __int64* value )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_INT64);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_INT64)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*value = *(__int64*)data_[index_].data_;
	index_++;

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadUint64(unsigned __int64* value) {
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_UINT64);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_UINT64)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*value = *(unsigned __int64*)data_[index_].data_;
	index_++;

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadBytes( unsigned char* lpBuffer, size_t nLength, size_t* lpcbLength )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_BYTES);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_BYTES)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	size_t cbLength = *(size_t*)data_[index_].data_;
	const char* lpBegin = (char*)data_[index_].data_ + sizeof(size_t);

	if (lpBuffer == NULL)
	{
		if (lpcbLength)
		{
			*lpcbLength = cbLength;
		}
		return XLIPC_RESULT_SUCCESS;
	}
	else
	{
		if (nLength < cbLength)
		{
			return XLIPC_RESULT_BUFFERTOOSMALL;
		}

		memcpy(lpBuffer, lpBegin, cbLength);

		if (lpcbLength)
		{
			*lpcbLength = cbLength;
		}

		index_++;

		return XLIPC_RESULT_SUCCESS;
	}
}

void XLIPCStream::Clear()
{
	for (size_t i = 0; i < data_.size(); i++)
	{
		const unsigned long type = data_[i].enum_type_;

		if ( type == XLIPC_PTYPE_STRING || 
			type == XLIPC_PTYPE_WSTRING || 
			type == XLIPC_PTYPE_BYTES ||
			type == XLIPC_PTYPE_INT64 ||
			type == XLIPC_PTYPE_UINT64 ||
			type == XLIPC_PTYPE_DOUBLE ||
			type == XLIPC_PTYPE_CALLBACK)
		{
			delete[] (char*)data_[i].data_;
		}
		else if(type == XLIPC_PTYPE_ARRAY || type == XLIPC_PTYPE_STREAM)
		{
			((XLIPCStream*)data_[i].data_)->Release();
		}
	}

	data_.clear();
	index_ = 0;
}

long XLIPCStream::Encode( char* lpBuffer, size_t nLength, size_t* lpcbLength )
{
	if (lpBuffer == NULL)
	{
		*lpcbLength = GetEncodeBufferNeedLength();
		return XLIPC_RESULT_SUCCESS;
	}
	else
	{
		size_t needLength = GetEncodeBufferNeedLength();
		if (nLength < needLength)
		{
			return XLIPC_RESULT_BUFFERTOOSMALL;
		}

		if (lpcbLength != NULL)
		{
			*lpcbLength = needLength;
		}

		char* lpCurrent = lpBuffer;

		for (size_t i = 0; i < data_.size(); i++)
		{
			*(size_t*)lpCurrent = data_[i].enum_type_;
			lpCurrent += sizeof(size_t);

			if (data_[i].enum_type_ == XLIPC_PTYPE_STRING || 
				data_[i].enum_type_ == XLIPC_PTYPE_WSTRING || 
				data_[i].enum_type_ == XLIPC_PTYPE_BYTES)
			{
				*(size_t*)lpCurrent = *(size_t*)data_[i].data_;
				lpCurrent += sizeof(size_t);

				memcpy(lpCurrent, (char*)data_[i].data_ + sizeof(size_t), *(size_t*)data_[i].data_);
				lpCurrent +=  *(size_t*)data_[i].data_;
			}
			else if(data_[i].enum_type_ == XLIPC_PTYPE_STREAM || data_[i].enum_type_ == XLIPC_PTYPE_ARRAY)
			{
				XLIPCStream* pStream = (XLIPCStream*)data_[i].data_;
				size_t len = pStream->GetEncodeBufferNeedLength();
				*(size_t*)lpCurrent = len;
				lpCurrent += sizeof(size_t);

				pStream->Encode(lpCurrent, len, NULL);
				lpCurrent += len;
			}
			else if (data_[i].enum_type_ == XLIPC_PTYPE_INT64 || data_[i].enum_type_ == XLIPC_PTYPE_CALLBACK)
			{
				*(__int64*)lpCurrent = *(__int64*)data_[i].data_;
				lpCurrent += sizeof(__int64);
			}
			else if (data_[i].enum_type_ == XLIPC_PTYPE_UINT64)
			{
				*(unsigned __int64*)lpCurrent = *(unsigned __int64*)data_[i].data_;
				lpCurrent += sizeof(unsigned __int64);
			}
			else if (data_[i].enum_type_ == XLIPC_PTYPE_DOUBLE)
			{
				*(double*)lpCurrent = *(double*)data_[i].data_;
				lpCurrent += sizeof(double);
			}
			else
			{
				*(size_t*)lpCurrent = (size_t)(INT_PTR)data_[i].data_;
				lpCurrent += sizeof(PVOID);
			}
		}

		return XLIPC_RESULT_SUCCESS;
	}
}

long XLIPCStream::Decode( const char* lpBuffer, size_t nLength )
{
	assert(lpBuffer);
	if (lpBuffer == NULL)
	{
		return XLIPC_RESULT_INVALID_PARAM;
	}
	if (nLength == 0)
	{
		return XLIPC_RESULT_SUCCESS;
	}

	const char* lpBegin = lpBuffer;
	const char* lpEnd = lpBuffer + nLength;
	while (lpBegin < lpEnd)
	{
		Data data = {XLIPC_PTYPE_NULL};
		data.enum_type_ = *(unsigned long*)lpBegin;

		lpBegin += sizeof(size_t);

		if (data.enum_type_ == XLIPC_PTYPE_STRING || 
			data.enum_type_ == XLIPC_PTYPE_WSTRING || 
			data.enum_type_ == XLIPC_PTYPE_BYTES)
		{
			size_t length = *(size_t*)lpBegin;
			lpBegin += sizeof(size_t);

			if (length + (char*)lpBegin > lpBuffer + nLength)
			{
				Clear();
				return XLIPC_RESULT_INVALID_BUFFER;
			}

			data.data_ = (void*)new char[sizeof(size_t) + length];

			*(size_t*)(data.data_) = length;
			memcpy((char*)data.data_ + sizeof(size_t), lpBegin, length);

			lpBegin += length;

			data_.push_back(data);
		}
		else if(data.enum_type_ == XLIPC_PTYPE_STREAM || data.enum_type_ == XLIPC_PTYPE_ARRAY)
		{
			size_t length = *(size_t*)lpBegin;
			lpBegin += sizeof(size_t);

			if (length + (char*)lpBegin > lpBuffer + nLength)
			{
				Clear();
				return XLIPC_RESULT_INVALID_BUFFER;
			}

			XLIPCStream* pStream = new XLIPCStream;
			pStream->Decode(lpBegin, length);

			lpBegin += length;

			data.data_ = pStream;
			data_.push_back(data);
		}
		else if (data.enum_type_ == XLIPC_PTYPE_INT64 || data.enum_type_ == XLIPC_PTYPE_CALLBACK)
		{
			data.data_ = (void*)new char[sizeof(__int64)];
			*(__int64*)data.data_ = *(__int64*)lpBegin;

			lpBegin += sizeof(__int64);

			data_.push_back(data);
		}
		else if (data.enum_type_ == XLIPC_PTYPE_UINT64)
		{
			data.data_ = (void*)new char[sizeof(unsigned __int64)];
			*(unsigned __int64*)data.data_ = *(unsigned __int64*)lpBegin;

			lpBegin += sizeof(unsigned __int64);

			data_.push_back(data);
		}
		else if (data.enum_type_ == XLIPC_PTYPE_DOUBLE)
		{
			data.data_ = (void*)new char[sizeof(double)];
			*(double*)data.data_ = *(double*)lpBegin;

			lpBegin += sizeof(double);
			data_.push_back(data);
		}
		else
		{
			/*
			XLIPC_PTYPE_BYTE		4
			XLIPC_PTYPE_WORD		5
			XLIPC_PTYPE_DWORD		6
			XLIPC_PTYPE_INT			7
			XLIPC_PTYPE_LONG		8
			*/
			data.data_ = (void*)(*(size_t*)lpBegin);

			lpBegin += sizeof(PVOID);

			data_.push_back(data);
		}
	}

	return XLIPC_RESULT_SUCCESS;
}

size_t XLIPCStream::GetEncodeBufferNeedLength() const
{
	size_t nTotalLength = 0;

	for (size_t i = 0; i < data_.size(); i++)
	{
		nTotalLength += sizeof(size_t);

		const unsigned long type = data_[i].enum_type_;
		if ( type == XLIPC_PTYPE_STRING || 
			type == XLIPC_PTYPE_WSTRING || 
			type == XLIPC_PTYPE_BYTES)
		{
			size_t nLength = *(size_t*)data_[i].data_;
			//assert(nLength < 0xFFFF);

			nTotalLength += sizeof(size_t);
			nTotalLength += *(size_t*)data_[i].data_;
		}
		else if(type == XLIPC_PTYPE_STREAM || type == XLIPC_PTYPE_ARRAY)
		{
			nTotalLength += sizeof(size_t);

			XLIPCStream* pStream = (XLIPCStream*)data_[i].data_;
			nTotalLength += pStream->GetEncodeBufferNeedLength();
		}
		else if (type == XLIPC_PTYPE_INT64 || type == XLIPC_PTYPE_CALLBACK)
		{
			nTotalLength += sizeof(__int64);
		}
		else if (type == XLIPC_PTYPE_UINT64)
		{
			nTotalLength += sizeof(unsigned __int64);
		}
		else if (type == XLIPC_PTYPE_DOUBLE)
		{
			nTotalLength += sizeof(double);
		}
		else
		{
			/*
			XLIPC_PTYPE_BYTE		4
			XLIPC_PTYPE_WORD		5
			XLIPC_PTYPE_DWORD		6
			XLIPC_PTYPE_INT			7
			XLIPC_PTYPE_LONG		8
			*/
			nTotalLength += sizeof(PVOID);
		}
	}	

	return nTotalLength;
}

size_t XLIPCStream::GetIntEncodeBufferNeedLength()
{
	return sizeof(size_t) * 2;
}

long XLIPCStream::WriteByte( BYTE bValue )
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_BYTE;
	data.data_ = (PVOID)bValue;

	data_.push_back(data);
	return XLIPC_RESULT_SUCCESS;	
}

long XLIPCStream::ReadByte( BYTE* bValue )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_BYTE);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_BYTE)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*bValue = (BYTE)data_[index_].data_;
	index_++;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteArray(XLIPCStream* pStream)
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_ARRAY;
	pStream->AddRef();
	data.data_ = pStream;

	data_.push_back(data);
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteStream( XLIPCStream* pStream )
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_STREAM;
	pStream->AddRef();
	data.data_ = pStream;

	data_.push_back(data);
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadStream( XLIPCStream** pStream )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_STREAM || data_[index_].enum_type_ == XLIPC_PTYPE_ARRAY);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_STREAM && data_[index_].enum_type_ != XLIPC_PTYPE_ARRAY)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*pStream = (XLIPCStream*)data_[index_].data_;
	(*pStream)->AddRef();
	index_++;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteLong( long lValue )
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_LONG;
	data.data_ = (PVOID)(LONG_PTR)lValue;

	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadLong( long* lValue )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_LONG);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_LONG)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*lValue = (long)(LONG_PTR)data_[index_].data_;
	index_++;

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::GetParamType( unsigned long* lpType )
{
	//assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(lpType);
	*lpType = data_[index_].enum_type_;

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteDouble( double value )
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_DOUBLE;
	data.data_ = (void*)new char[sizeof(double)];
	*(double*)data.data_ = value;


	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::WriteCallback(__int64 func_ref)
{
	Data data;
	data.enum_type_ = XLIPC_PTYPE_CALLBACK;
	data.data_ = (void*)new char[sizeof(__int64)];
	*(long long*)data.data_ = func_ref;

	data_.push_back(data);

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadDouble( double* value )
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_DOUBLE);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_DOUBLE)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*value = *(double*)data_[index_].data_;
	index_++;

	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::ReadCallback(__int64* func_ref)
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}

	assert(data_[index_].enum_type_ == XLIPC_PTYPE_CALLBACK);
	if (data_[index_].enum_type_ != XLIPC_PTYPE_CALLBACK)
	{
		return XLIPC_RESULT_ERROR_TYPE;
	}

	*func_ref = *(__int64*)data_[index_].data_;
	index_++;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::UpdateInt(int iValue)
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}
	Data& data = data_[index_];
	data.data_ = (PVOID)(INT_PTR)iValue;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::UpdateSize(size_t iValue)
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}
	Data& data = data_[index_];
	data.data_ = (PVOID)iValue;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::Next()
{
	assert(index_ < data_.size());
	if (index_ >= data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}
	index_++;
	return XLIPC_RESULT_SUCCESS;
}

bool XLIPCStream::IsEOF() const
{
	return index_ == data_.size();
}

size_t XLIPCStream::GetCurrentIndex() const
{
	return index_;
}

size_t XLIPCStream::GetSize() const
{
	return data_.size();
}

long XLIPCStream::Reset()
{
	index_ = 0;
	return XLIPC_RESULT_SUCCESS;
}

long XLIPCStream::MoveTo(size_t index)
{
	assert(index <= data_.size());
	if (index > data_.size())
	{
		return XLIPC_RESULT_NOMOREDATA;
	}
	index_ = index;
	return XLIPC_RESULT_SUCCESS;
}