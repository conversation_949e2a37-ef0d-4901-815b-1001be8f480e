import { defineStore } from 'pinia'
import { taskExtraFunc } from '@/common/task-extra'
import * as BaseType from '@root/common/task/base'
import { TaskManager } from '@root/common/task/impl/task-manager';
import { IFileInfo, IBranch, ILeaf, IHistoryTaskSpeedInfo, IHistorySpeedInfo, IExtraFileInfo, ISourceData } from '@/types/taskDetails'
import { calculateProgress, isNumber, isObject } from '@/utils'

import path from 'path';
import { GetProfilesPath } from '@root/common/xxx-node-path'
import { FileSystemAWNS } from '@root/common//fs-utilities';
import { useTaskErrorCode } from '@/stores/taskErrorCode'
interface IResPostOrderBranch { downloadSize: number, fileSize: number, isAllComplete: boolean, downloadTotal: number }
interface IResPostOrderBranch {
  downloadSize: number
  fileSize: number
  isAllComplete: boolean
  downloadTotal: number
  downFailNum: number
  completedNum: number
  isExistDown: boolean
}
export const useTaskDetailStore = defineStore('taskDetail', {
  state: () => ({
    taskInfo: {} as BaseType.TaskBase,
    groupTasks: [] as IFileInfo[],
    taskSourceList: [] as BaseType.BtFileDownloadInfo[],
    taskStatusListener: 0, // 任务状态监听
    taskDetailListener: 0, // 任务详情监听
    taskSubStatusListener: 0, // 子任务状态监听
    taskSubDetailListener: 0, // 子任务详情监听
    groupTaskStatusListener: 0, // 任务组子状态监听
    groupTaskDetailListener: 0, // 任务组子任务详情监听
    groupTaskDelDetailListener: 0, // 任务组彻底删除监听
    leafMap: {} as { [name: string]: ILeaf },
    branchMap: {} as { [name: string]: IBranch },
    taskTreeData: {} as { [name: string]: IBranch }, // 任务树数据
    btTaskTreeData: {} as { [name: string]: IBranch }, // bt任务树数据
    downAddTotal: 0, // 下载添加数量
    downloadAllTotal: 0, // 总文件数量
    downloadCount: 0,
    checkedKeys: [] as string[],
    //======================
    taskSpeedMap: new Map() as Map<number, IHistoryTaskSpeedInfo>,
    speedPath: '', // 速度存储路径
    isSupportPlay: false,
    isInsertBtTask: false, // 是否插入bt任务, 可以用于判断操作数据
    currentViewTaskId: 0, // 当前查看任务id
  }),
  
  getters: {
    // 当前查看任务id
    isPanTask: (state) => state.taskInfo.isPanTask, // 是否为云盘任务
    currentTaskId: (state) => state.taskInfo.taskId,
    downloadSpeed: (state) => {
      return state.taskInfo.downloadSpeed || 0
    },
    taskType: (state) => state.taskInfo.taskType,
    taskStatus: (state) => state.taskInfo.taskStatus,
    taskName: (state) => state.taskInfo.taskName,
    isGroupTask: (state) => state.taskInfo.taskType === BaseType.TaskType.Group,
    isBtTask: (state) => state.taskInfo.taskType === BaseType.TaskType.Bt,
    singleTask: (state) => state.taskInfo.taskType !== BaseType.TaskType.Group && state.taskInfo.taskType !== BaseType.TaskType.Bt,
  },
  actions: {
    /** 点击bt时插入bt文件同时监听bt任务情况 */
    async insertBtTask (taskId: number | undefined) {
      if (!taskId) { return }
      // 获取bt数据源
      this.isInsertBtTask = true
      this.currentViewTaskId = taskId
      const resList: BaseType.BtFileDownloadInfo[] = await this.getBtTaskSourceList(taskId)
      
      const changeList: IExtraFileInfo[] = []
      for (const res of resList) {
        const changeFile = {
          index: res.realIndex,
          downloadSize: res.downloadSize,
          fileSize: res.fileSize,
          status: res.fileStatus, // BtSubFileStatus
          errCode: res.errorCode,
          filePath: res.filePath,
          fileName: res.fileName,
          isNeedDownload: res.download, //该子文件是否下载
          isSupportPlay: res.supportPlay, //是否支持播放
          isComplete: res.fileStatus === BaseType.BtSubFileStatus.Complete,
          progress: 0,
        }
        changeList.push(changeFile)
      }
      const task = await TaskManager.GetInstance().findTaskById(taskId);
      let taskBase;
      if (task) {
        taskBase = task.getTaskBase()
      }
      
      this.parseFileList(changeList, taskBase)
      console.log('>>>>>>>>> branchMap', this.branchMap)
      this.btTaskTreeData = this.branchMap

      this.clearBrachMap()
      this.listenerBtSubTask()
    },
    clearBrachMap () {
      this.leafMap = {} as { [name: string]: ILeaf }
      this.branchMap = {} as { [name: string]: IBranch }
    },
    /** 退出时关闭bt监听 */
    backBtSubTask () {
      this.cancelListenerBtSubTask()
    },
    /** 初始化任务详情 */
    async initTaskDetail (taskId: number) {
      const task = await TaskManager.GetInstance().findTaskById(taskId)
      if (!task) {
        console.error('>>>> 为获取到task信息')
        return
      }
      console.log('>>>>>>>>>task', task)
      this.isSupportPlay = !!(await task?.isSupportPlay())
      this.taskInfo = await task.getTaskBase()
      // let dataPath = GetProfilesPath();
      // this.speedPath = path.join(dataPath, 'TaskSpeedInfo');
      this.getDownloadCount(this.isGroupTask)
      
      // 单文件不需要解析树
      await this.getTaskSourceList(this.taskInfo.taskId, this.taskInfo.taskType)
      this.listenerTask()
    },
    /** 获取源数据 */
    async getTaskSourceList (taskId: number, type?: BaseType.TaskType) {
      this.currentViewTaskId = taskId
      if (type === BaseType.TaskType.Bt) {
        const resList: BaseType.BtFileDownloadInfo[] = await this.getBtTaskSourceList(taskId)
        // 转换成 IExtraFileInfo
        const changeList: IExtraFileInfo[] = []
        for (const res of resList) {
          const changeFile = {
            index: res.realIndex,
            downloadSize: res.downloadSize,
            fileSize: res.fileSize,
            status: res.fileStatus, // BtSubFileStatus
            errCode: res.errorCode,
            filePath: res.filePath,
            fileName: res.fileName,
            isNeedDownload: res.download, //该子文件是否下载
            isSupportPlay: res.supportPlay, //是否支持播放
            isComplete: res.fileStatus === BaseType.BtSubFileStatus.Complete,
            progress: 0,
          }
          // console.log('>>>>>>>>>>>>>>> bt', changeFile)
          changeList.push(changeFile)
        }
        this.parseFileList(changeList, this.taskInfo)
      } else if (type === BaseType.TaskType.Group) {
        // 任务组
        console.log('>>>>>>>>>>>>>>> 任務組', this.taskInfo)
        const taskSourceId = await taskExtraFunc.getGroupSubTaskIds(this.taskInfo.taskId)
        console.log('>>>>>>>>>>>>>>> taskSourceId', taskSourceId)
        if (this.taskInfo.isPanTask) { // 云盘任务
          const panTaskList = await this.parseGroupPanTask(taskSourceId)
          console.log('>>>>>>>>>>>>>>> panTaskList', panTaskList)
          this.parseFileList(panTaskList, this.taskInfo)
          
        } else { // 普通任务组
          // 判断是否存在bt任务
          const taskList = await this.parseGroupInBtTask(taskSourceId)
          console.log('>>>>>>>>>>>>>>> taskList', taskList)
          this.parseFileList(taskList, this.taskInfo)
        }
      }
      this.taskTreeData = this.branchMap
      // 清空数据
      this.clearBrachMap()
    },
    /** 创建任务组，其中bt任务组 */
    async parseGroupInBtTask (taskSourceId: number[]): Promise<IExtraFileInfo[]> {
      // 完整文件路径
      const savePath = path.join(this.taskInfo.savePath || '', this.taskInfo.taskName || '') + '\\'
      let fileList: IExtraFileInfo[] = []
      let taskBaseList: BaseType.TaskBase[] = []
      console.log('>>>>>>>>>>>>>>>>> savePath', savePath)
      for (let id of taskSourceId) {
        let task = await TaskManager.GetInstance().findTaskById(id);
        if (!task) continue
        const taskBase = task.getTaskBase();
        taskBaseList.push(taskBase)
        console.log('>>>>>>>>> taskBase', taskBase)
        const supportPlay = await task.isSupportPlay()
        let fullPath = taskBase.savePath || ''
        let filePath = fullPath.replace(savePath, '').split('\\')
        filePath.pop()
        // const filePathStr = filePath.join('\\') + '\\'
        let downloadTotal = 0
        let completedNum = 0
        if (taskBase.taskType === BaseType.TaskType.Bt) {
          downloadTotal = await taskExtraFunc.getDownloadCount(id)
          completedNum = await taskExtraFunc.getCompleteCount(id)
          console.log('>>>>>>>>>>>>>>>>>>>>>> downloadTotal', downloadTotal)
        }
        const params: IExtraFileInfo = {
          // index: -1,
          // filePath: filePathStr, // 先注释掉
          taskType: taskBase.taskType,
          taskId: taskBase.taskId,
          status: taskBase.taskStatus,
          fileSize: taskBase.fileSize || 0,
          downloadSize: taskBase.downloadSize || 0,
          fileName: taskBase.taskName || '',
          isComplete: taskBase.taskStatus === BaseType.TaskStatus.Succeeded || taskBase.taskStatus === BaseType.TaskStatus.Seeding,
          progress: calculateProgress(taskBase.fileSize, taskBase.downloadSize),
          isSupportPlay: supportPlay,
          errCode: taskBase.errorCode,
          downloadTotal,
          url: taskBase.url,
          isNeedDownload: taskBase.downloadSubTask,
          completedNum,
        }
        fileList.push(params)
      }
      console.log('>>>>>> taskBaseList', taskBaseList)
      return fileList
    },

    /** 任务组转换不含bt */
    async parseGroupPanTask (taskSourceId: number[]): Promise<IExtraFileInfo[]> {
      const savePath = path.join(this.taskInfo.savePath || '', this.taskInfo.taskName || '')
      // const isSingleFile: boolean = await taskExtraFunc.isSingleBT(this.taskInfo.taskId);
      let exist: boolean = true;
      let fileList: IExtraFileInfo[] = []
      // const taskName = this.taskInfo.taskName || '';
      console.log('>>>>>>>>>>>>>>>>> savePath', savePath)
      for (let i: number = 0; i < taskSourceId.length; ++i) {
        const taskId = taskSourceId[i]
        const task = await TaskManager.GetInstance().findTaskById(taskId)
        const taskBase = task && task.getTaskBase()
        console.log('>>>>>>>>>>>> taskBase', taskBase)
        // 排除前面绝对路径
        if (!taskBase) { continue }
        let fullPath = path.join(taskBase.savePath || '', taskBase.taskName || '')
        console.log('>>>>>>>>>>>>> fullPath', fullPath)
        let filePath = (taskBase?.savePath ?? '').replace(savePath, '') + '\\'
        console.log('>>>>>>>>>>>>> filePath', filePath)
        // 删除开始 \\
        filePath = filePath.replace(/^\\/, '')
        const index = -1
        const fileName = taskBase.taskName || ''
        const url = taskBase.url || ''
        const isNeedDownload = taskBase.downloadSubTask
        const fileSize = taskBase.fileSize || 0
        const downloadSize = taskBase.downloadSize || 0
        const status = taskBase.taskStatus
        const errCode = taskBase.errorCode
        const isSupportPlay = await task.isSupportPlay()
        const fileInfo: IExtraFileInfo = {
          taskId,
          index,
          filePath, 
          fileName,
          url,
          isNeedDownload,
          fileSize,
          downloadSize,
          status,
          errCode,
          isSupportPlay,
          isComplete: status === BaseType.TaskStatus.Succeeded || status === BaseType.TaskStatus.Seeding,
          progress: 0,
        };

        if (fileInfo.errCode !== 0) {
          const taskErrorCodeStore = useTaskErrorCode();
          fileInfo.errMessage = taskErrorCodeStore.getTaskErrorInfoByCode(fileInfo.errCode);
        } else {
          fileInfo.errMessage = '';
        }
        // 下载完成的子任务，检查文件是否已删除
        if (fileInfo.status === BaseType.TaskStatus.Succeeded) {
          exist = await FileSystemAWNS.existsAW(fullPath);
          fileInfo.isDelete = !exist;
        } else {
          fileInfo.isDelete = false;
        }
        fileList.push(fileInfo)
      }
      return fileList
    },

    /** 获取bt源数据 */
    getBtTaskSourceList (taskId: number) {
      this.listenerBtSubTask()
      return taskExtraFunc.getBtFileInfos(taskId)
    },

    /** 清空bt源数据 */
    clearBTTaskSourceList () {
      this.currentViewTaskId = this.taskInfo.taskId
      this.cancelListenerBtSubTask()
      this.btTaskTreeData = {}
    },

    /** 获取下载数量 */
    async getDownloadCount (isGroup: boolean) {
      if (isGroup) {
        this.downloadAllTotal = await taskExtraFunc.getGroupDownloadTotal(this.taskInfo.taskId)
        this.downAddTotal = await taskExtraFunc.getGroupDownloadCount(this.taskInfo.taskId)
      } else if (this.isBtTask){
        this.downloadAllTotal = await taskExtraFunc.getDownloadTotal(this.taskInfo.taskId)
        this.downAddTotal = await taskExtraFunc.getDownloadCount(this.taskInfo.taskId)
      }
      console.log('><>>>>>> this.downAddTotal', this.downAddTotal, this.downloadAllTotal)
    },

    unInit () {
      console.log('================== unInit')
      this.isInsertBtTask = false
      this.taskInfo = {} as BaseType.TaskBase
      this.groupTasks = []
      this.taskSourceList = [] as BaseType.BtFileDownloadInfo[]
      this.clearBrachMap()
      this.cancelListenerTask()
    },
    // 监听
    listenerTask () {
      if (this.isGroupTask) {
        this.listenerGroupTask()
      }
      this.taskDetailListener = TaskManager.GetInstance().attachTaskDetailChangeEvent(async (taskId: number, flags: number) => {
        if (this.taskInfo.taskId === taskId) {
          console.log('= 监听任务详情变化', taskId, flags)
          let task = await TaskManager.GetInstance().findTaskById(taskId);
          if (!task) return
          const taskBase = task.getTaskBase()
          this.taskInfo = taskBase
          this.getDownloadCount(this.isGroupTask)
        }
      });
      this.taskStatusListener = TaskManager.GetInstance().attachTaskStatusChangeEvent(async (taskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
        if (this.taskInfo.taskId === taskId) {
          console.log('= 监听任务状态变化', taskId, eOld, eNew)
          this.taskInfo.taskStatus = eNew
          // 计算一下已添加文件数
          this.getDownloadCount(this.isGroupTask)
        }
      });
    },
    /** 更新任务组信息 */
    async updateGroupInfo(taskBase: BaseType.TaskBase) {
      const pathStr = taskBase.savePath || ''
      const fileName = taskBase.taskName || ''
      const fullPath = path.join(pathStr, fileName)
      const savePath = path.join(this.taskInfo.savePath || '', this.taskInfo.taskName || '') + '\\'
      let filePath = fullPath.replace(savePath, '').split('\\')
      const pathArr = this.transformArray(filePath)
      await this.calculateGroupTask(pathArr, this.taskTreeData[''], taskBase)
    },
    async calculateGroupTask (pathArr: string[], treeData: IBranch, taskBase: BaseType.TaskBase, isFind=false): Promise<IResPostOrderBranch|undefined> {
      if (!treeData) { return }
      const children: (IBranch | ILeaf)[] = treeData.children;
      let downloadSize: number = 0;
      let fileSize: number = 0;
      let isAllComplete: boolean = children.length > 0 ? true : false;
      let downloadTotal = 0
      let downFailNum = 0 // 下载失败数量
      let isExistDown = false;
      let completedNum = 0 // 下载完成数量
      let isFindLeaf = isFind
      // 排序 先找叶子节点
      for (const i in children) {
        const child = children[i]
        if (this.isLeaf(child) || this.isBt(child)) {
          if (child.fullPath === pathArr[0] && pathArr.length === 1) {
            console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>> 找到叶子节点', taskBase)
            isFindLeaf = true
            // 判断任务是否完成
            const isComplete = taskBase?.taskStatus === BaseType.TaskStatus.Succeeded;
            isAllComplete = isAllComplete && isComplete
            let progress: number = calculateProgress(taskBase.fileSize, taskBase.downloadSize);
            if (!isComplete) {
              progress = progress >= 1 ? 0.9999 : progress;
            }
            let total = 0
            if (taskBase.downloadSubTask) {
              if (taskBase.taskType === BaseType.TaskType.Bt) {
                total = await taskExtraFunc.getDownloadCount(taskBase.taskId)
                const resCompletedNum = await taskExtraFunc.getCompleteCount(taskBase.taskId)
                completedNum += resCompletedNum || 0
                downloadTotal += (total || 0)
              } else {
                console.log('>>>>>>>>>>>>>>>>>> isComplete1', isComplete, taskBase?.taskStatus)
                const isDown = taskBase?.taskStatus === BaseType.TaskStatus.Started || taskBase?.taskStatus === BaseType.TaskStatus.StartPending;
                const isFail = taskBase?.taskStatus === BaseType.TaskStatus.Failed || taskBase?.taskStatus === BaseType.TaskStatus.Unkown;
                if (isDown) {
                  isExistDown = isExistDown || isDown
                } else if (isFail) {
                  downFailNum += 1
                } else if (isComplete) {
                  completedNum += 1
                }
                downloadTotal += 1
              }
              downloadSize += taskBase.downloadSize || 0;
              fileSize += taskBase.fileSize || 0;
            }
            // 不在此处判断任务组子任务是否可以播放，组件右击时自动判断
            const changeFile: IExtraFileInfo = {
              taskId: taskBase.taskId,
              downloadSize: taskBase.downloadSize || 0,
              downloadTotal: total,
              fileSize: taskBase.fileSize || 0,
              status: taskBase.taskStatus, // BtSubFileStatus
              errCode: taskBase.errorCode,
              filePath: taskBase.savePath,
              fileName: taskBase.taskName || '',
              isNeedDownload: taskBase.downloadSubTask, //该子文件是否下载
              isComplete,
              progress,
              completedNum, // 对bt使用
            }
            children[i].data = changeFile
          } else if (child.data?.isNeedDownload) { // 需要下载文件才计算
            const isComplete = child.data?.status === BaseType.TaskStatus.Succeeded;
            isAllComplete = isAllComplete && isComplete
            downloadSize += isComplete ? child.data.fileSize : child.data.downloadSize || 0;
            fileSize += child.data.fileSize;
            let progress: number = calculateProgress(child.data.fileSize, child.data.downloadSize);
            if (!isComplete) {
              progress = progress >= 1 ? 0.9999 : progress;
            }
            // if (taskBase.taskType === BaseType.TaskType.Bt) {
            //   downloadAllTotal += child.data?.downloadTotal || 0
            // } else {
            //   downloadAllTotal += 1
            // }
            if (taskBase.taskType === BaseType.TaskType.Bt) {
              completedNum += (await taskExtraFunc.getCompleteCount(taskBase.taskId)) || 0
              downloadTotal += child.data?.downloadTotal || 0
            } else {
              const isDown = taskBase?.taskStatus === BaseType.TaskStatus.Started || taskBase?.taskStatus === BaseType.TaskStatus.StartPending;
              const isFail = taskBase?.taskStatus === BaseType.TaskStatus.Failed || taskBase?.taskStatus === BaseType.TaskStatus.Unkown;
              if (isDown) {
                isExistDown = isExistDown || isDown
              } else if (isFail) {
                downFailNum += 1
              }
              if (isComplete) {
                completedNum += 1
              }
              downloadTotal += 1
              console.log('> isComplete3', isComplete, completedNum, isFail, isFail)
            }
            downloadSize += taskBase.downloadSize || 0;
            fileSize += taskBase.fileSize || 0;
          }
        } else if (this.isBranch(child)) {
          if (child.key === pathArr[0] && !isFindLeaf) {
            console.log('> 找到分支节点 child', child, pathArr)
            const res = await this.calculateGroupTask(pathArr.slice(1), child, taskBase, isFindLeaf)
            console.log('>>>>>>>>>>>>>>> res', res)
            isAllComplete = isAllComplete && !!res?.isAllComplete;
            if (res && res.downloadTotal > 0) {
              downloadTotal += res?.downloadTotal ?? 0
              downloadSize += res?.downloadSize ?? 0;
              fileSize += res?.fileSize ?? 0;
              completedNum += res?.completedNum
              downFailNum += res?.downFailNum
              isExistDown = isExistDown || res?.isExistDown
            }
          } else {
            downloadTotal += child?.data?.downloadTotal ?? 0
            downloadSize += child?.data?.downloadSize ?? 0;
            fileSize += child?.data?.fileSize ?? 0;
            isAllComplete = child?.data?.isComplete ?? false;
            completedNum += child?.data?.completedNum
            downFailNum += child?.data?.downFailNum
            isExistDown = isExistDown || child?.data?.isExistDown
          }
        }
      }

      let progress: number = calculateProgress(fileSize, downloadSize);
      if (!isAllComplete) {
        progress = progress >= 1 ? 0.9999 : progress;
      }
      treeData.data = {
        ...treeData.data,
        downloadSize: downloadSize,
        fileSize: fileSize,
        isComplete: isAllComplete,
        progress,
        downloadTotal: downloadTotal, completedNum, downFailNum, isExistDown
      };
      return { downloadTotal, fileSize, isAllComplete, downloadSize, completedNum, downFailNum, isExistDown }
    },
    /** 任务组 */
    listenerGroupTask () {
      // 任务组子任务状态变化
      this.groupTaskStatusListener = TaskManager.GetInstance().attachGroupSubTaskStatusChangeEvent(async (taskId: number, groupTaskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
        const task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) return
        const taskBase = task.getTaskBase()
        console.log('>>>>>>>>>>>>> 更新任务组子任务状态', taskBase)
        this.updateGroupInfo(taskBase)
      })
      // 任务组子任务详情变化
      this.groupTaskDetailListener = TaskManager.GetInstance().attachGroupSubTaskDetailChangeEvent(async (taskId: number, groupTaskId: number, flags: BaseType.TaskDetailChangedFlags) => {
        const task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) return
        const taskBase = task.getTaskBase()
        console.log('>>>>>>>>>>>>> 更新任务组子任务详情', taskBase)
        this.updateGroupInfo(taskBase)
      })
      // 任务组子任务彻底删除监听
      // this.groupTaskDelDetailListener = TaskManager.GetInstance().attachTaskDeletedEvent(async (taskId: number) => {
      //   console.log('= 监听任务组删除', taskId)
      //   const task = await TaskManager.GetInstance().findTaskById(taskId);
      //   if (!task) return
      //   const taskBase = task.getTaskBase()
      //   this.updateGroupInfo(taskBase)
      // })
    },

    /** 更新bt信息 */
    updateBtInfo(leftTask: BaseType.BtFileDownloadInfo) {
      const pathStr = leftTask.filePath
      const fileName = leftTask.fileName
      const fullPath = path.join(pathStr, fileName)
      console.log('>>>>>>>>>>>>> fullPath', fullPath)
      const pathArr = this.transformArray(fullPath.split('\\'))
      console.log('>>>>>>>>>>>>>> this.taskTreeData1', this.taskTreeData[''].children)
      if (this.isInsertBtTask) {
        this.calculateBtTask(pathArr, this.btTaskTreeData[''], leftTask)
      } else {
        this.calculateBtTask(pathArr, this.taskTreeData[''], leftTask)
      }
      console.log('>>>>>>>>>>>>>> this.taskTreeData2', this.taskTreeData[''].children)
    },

    calculateBtTask(pathArr: string[], treeData: IBranch, leftTask: BaseType.BtFileDownloadInfo, isFind=false) {
      // console.log('> treeData', treeData, leftTask, pathArr)
      if (!treeData) { return }
      const children: (IBranch | ILeaf)[] = treeData.children
      let downloadAllSize: number = 0;
      let fileAllSize: number = 0;
      let isAllComplete: boolean = children.length > 0 ? true : false;
      let downloadAllTotal = 0
      let isFindLeaf = isFind
      let downFailNum = 0 // 下载失败数量
      let isExistDown = false;
      let completedNum = 0 // 下载完成数量
      for (const i in children) {
        const child = children[i]
        if (this.isLeaf(child)) {
          if (child.fullPath === pathArr[0] && pathArr.length === 1) {
            isFindLeaf = true
            // 判断任务是否完成
            const isComplete = leftTask.fileStatus === BaseType.BtSubFileStatus.Complete;
            const isFail = leftTask.fileStatus === BaseType.BtSubFileStatus.Failed
            const isDownloading =  leftTask.fileStatus === BaseType.BtSubFileStatus.Downloading
            isAllComplete = isAllComplete && isComplete
            let progress: number = calculateProgress(leftTask.fileSize, leftTask.downloadSize);
            if (!isComplete) {
              progress = progress >= 1 ? 0.9999 : progress;
            } else {
              completedNum += 1
            }
            if (isFail) {
              downFailNum += 1
            }
            isExistDown = isExistDown || isDownloading
            if (leftTask.download) {
              downloadAllTotal += 1
              downloadAllSize += leftTask.downloadSize || 0;
              fileAllSize += leftTask.fileSize;
            }
            const changeFile = {
              index: leftTask.realIndex,
              downloadSize: leftTask.downloadSize,
              fileSize: leftTask.fileSize,
              status: leftTask.fileStatus, // BtSubFileStatus
              errCode: leftTask.errorCode,
              filePath: leftTask.filePath,
              fileName: leftTask.fileName,
              isNeedDownload: leftTask.download, //该子文件是否下载
              isSupportPlay: leftTask.supportPlay, //是否支持播放
              isComplete,
              progress,
            }
            children[i].data = changeFile
          } else {
            const status = this.getFileStatus(child.data.status, this.taskInfo?.taskType)
            const isComplete = status === BaseType.BtSubFileStatus.Complete;
            const isFail = status === BaseType.BtSubFileStatus.Failed
            const isDownloading =  status === BaseType.BtSubFileStatus.Downloading
            if (child.data.isNeedDownload) { // 需要下载文件才计算
              isAllComplete = isAllComplete && isComplete
              downloadAllSize += isComplete ? child.data.fileSize : child.data.downloadSize || 0;
              fileAllSize += child.data.fileSize;
              let progress: number = calculateProgress(child.data.fileSize, child.data.downloadSize);
              if (!isComplete) {
                progress = progress >= 1 ? 0.9999 : progress;
              } else {
                completedNum += 1
              }
              downloadAllTotal += 1
              if (isFail) {
                downFailNum += 1
              }
              isExistDown = isExistDown || isDownloading
            }
          }
        } else if (this.isBranch(child)) {
          if (!isFindLeaf && child.key === pathArr[0]) {
            const res = this.calculateBtTask(pathArr.slice(1), child, leftTask, isFindLeaf)
            isAllComplete = isAllComplete && !!res?.isAllComplete;
            if (res && res.downloadAllTotal > 0) {
              downloadAllTotal += res?.downloadAllTotal ?? 0
              downloadAllSize += res?.downloadAllSize ?? 0;
              fileAllSize += res?.fileAllSize ?? 0;
              completedNum += res.completedNum || 0
              downFailNum += res.downFailNum
              console.log('>>>>>>>>>>>>>>> isExistDown', isExistDown, res)
              isExistDown = isExistDown || res.isExistDown
            }
          } else { // 找到子文件不再继续深入计算
            downloadAllTotal += child?.data?.downloadTotal ?? 0
            downloadAllSize += child?.data?.downloadSize ?? 0;
            fileAllSize += child?.data?.fileSize ?? 0;
            isAllComplete = child?.data?.isComplete ?? false;
            completedNum += child?.data?.completedNum || 0
            downFailNum += child?.data?.downFailNum
            isExistDown = isExistDown || child?.data?.isExistDown
          }
        }
      }

      let progress: number = calculateProgress(fileAllSize, downloadAllSize);
      if (!isAllComplete) {
        progress = progress >= 1 ? 0.9999 : progress;
      }
      treeData.data = {
        ...treeData.data,
        downloadSize: downloadAllSize,
        fileSize: fileAllSize,
        isComplete: isAllComplete,
        progress,
        downloadTotal: downloadAllTotal,downFailNum,
        isExistDown,
        completedNum,
      };
      return { downloadAllSize, fileAllSize, isAllComplete, downloadAllTotal, downFailNum,
        isExistDown,
        completedNum}
    },
    
    transformArray(arr: string[]) {
      if (arr.length === 0) return [];
      
      let result: string[] = []
      arr.forEach(((item, index) => {
        const str: string[] = arr.slice(0,  index + 1)
        result.push(str.join('\\'))
      }))
      
      return result;
    },

    /** 监听bt子任务变化 */
    async listenerBtSubTask () {
      const task = await taskExtraFunc.getBtTask(this.currentViewTaskId);

      this.taskSubDetailListener = TaskManager.GetInstance().attachBtSubTaskDetailChangeEvent(async(taskId: number, fileIndex: number, flags: BaseType.BtSubFileDetailChangeFlags) => {
        console.log('= 监听bt子任务详情变化', taskId, fileIndex, flags, this.currentViewTaskId, taskId);
        if (this.currentViewTaskId === taskId) {
          
          const leftTask = await task.getBtFileInfoByIndex(fileIndex);
          if (!leftTask) { return };
          console.log('>>>>>>>> 子任务详情变化 更新任务组子任务详情', leftTask);
          this.updateBtInfo(leftTask)
        }
      })
      this.taskSubStatusListener = TaskManager.GetInstance().attachBtSubTaskStatusChangeEvent(async(taskId: number, fileIndex: number, eOld: BaseType.BtSubFileStatus, eNew: BaseType.BtSubFileStatus) => {
        console.log('= 监听bt子任务状态变化', taskId,'---' ,fileIndex, '---' ,eOld,'---' ,eNew)
        if (this.currentTaskId === taskId) {
          const leftTask = await task.getBtFileInfoByIndex(fileIndex)
          console.log('> 子任务状态变化 更新任务组子任务详情', leftTask)
          if (!leftTask) return
          // 跟新对应位置的子文件信息
          if (!leftTask) { return };
          console.log('>>>>>>>> 子任务详情变化 更新任务组子任务详情', leftTask);
          this.updateBtInfo(leftTask)
        }
      })
    },
    /** 取消监听bt子任务变化 */
    cancelListenerBtSubTask () {
      console.log('>>>>>>>>>>>>>>>>>>> 取消bt任务监听')
      this.taskSubDetailListener && TaskManager.GetInstance().detachBtSubTaskDetailChangeEvent(this.taskSubDetailListener)
      this.taskSubStatusListener && TaskManager.GetInstance().detachBtSubTaskStatusChangeEvent(this.taskSubStatusListener)
      this.taskSubDetailListener = 0
      this.taskSubStatusListener = 0
    },
    // 取消监听
    cancelListenerTask () {
      this.taskDetailListener && TaskManager.GetInstance().detachTaskDetailChangeEvent(this.taskDetailListener)
      this.taskStatusListener && TaskManager.GetInstance().detachTaskStatusChangeEvent(this.taskStatusListener)
      this.cancelListenerBtSubTask()
      this.groupTaskDetailListener && TaskManager.GetInstance().detachGroupSubTaskDetailChangeEvent(this.groupTaskDetailListener)
      this.groupTaskStatusListener && TaskManager.GetInstance().detachGroupSubTaskStatusChangeEvent(this.groupTaskStatusListener)
      this.groupTaskDelDetailListener && TaskManager.GetInstance().detachTaskDeletedEvent(this.groupTaskDelDetailListener)
      this.taskDetailListener = 0
      this.taskStatusListener = 0
      this.groupTaskDetailListener = 0
      this.groupTaskStatusListener = 0
    },
    setLeaf(pwd: string, leafData: IExtraFileInfo): ILeaf {
      const fullPath: string = (pwd ? `${pwd}\\${leafData.fileName}` : leafData.fileName) || ''
      const branch: IBranch = this.branchMap[pwd];
      let leaf: ILeaf = this.leafMap[fullPath];
      if (!leaf) {
        leaf = {
          type: leafData.taskType === BaseType.TaskType.Bt ? 'bt' : 'leaf',// 判断是否为bt
          fullPath,
          name: leafData.fileName || '',
          key: fullPath,
          parent: branch,
          data: leafData
        };
        this.leafMap[fullPath] = leaf;

        branch.children.push(leaf);
      }
      // console.log('>>>>>>>>>>>> leafData', leafData)
      branch.data.fileSize += leafData.fileSize;
      let branchParent: any = branch.parent;
      while (branchParent) {
        branchParent.data.fileSize += leafData.fileSize;
        branchParent = branchParent.parent;
      }

      return leaf;
    },
    /** 设置节点信息 */
    setBranch(pwd: string, branchName: string): IBranch {
      const parent: IBranch = this.branchMap[pwd];
      const fullPath: string = pwd ? `${pwd}\\${branchName}` : branchName;
      let branch: IBranch = this.branchMap[fullPath];

      if (!branch) {
        // 初始化
        branch = this.branchMap[fullPath] = {
          type: 'branch',
          name: branchName,
          children: [],
          key: fullPath,
          parent,
          data: {
            fileSize: 0,
            downloadSize: 0,
            isComplete: false,
            fileName: branchName,
            progress: 0,
            downloadTotal: 0
          }
        };

        if (parent) {
          parent.children.push(branch);
        }
      }

      return branch;
    },

    /** 解析树 */
    parseFileList(subFiles: IExtraFileInfo[], taskBase: BaseType.TaskBase): void {
      const filePathCache: { [filePath: string]: boolean } = {};
      this.branchMap = {};
      this.leafMap = {};

      this.setBranch('', '');

      subFiles.forEach((file: IExtraFileInfo): void => {
        const filePath = file.filePath || '';

        if (!filePathCache[filePath]) {
          filePathCache[filePath] = true;
          let pwd: string = '';

          filePath.split('\\').forEach((dirname: string): void => {
            if (dirname !== '') {
              this.setBranch(pwd, dirname);
              pwd = pwd ? `${pwd}\\${dirname}` : dirname;
            }
          });
        }

        this.setLeaf(filePath.slice(0, -1), file);
      })

      this.calculateTree(taskBase)
      console.log('= initTaskDetail  leafMap', this.leafMap)
      console.log('>>>>>>>>>>>>>>>>>>> initTaskDetail  branchMap', this.branchMap)
    },

    /** 计算文件数 */
    calculateTree(taskBase: BaseType.TaskBase) {
      const parent: IBranch = this.branchMap['']
      this.postOrderBranch(parent, taskBase)
    },

    /** 后续遍历计算树的大小文件数进度 */
    postOrderBranch (branchData: IBranch, taskBase: BaseType.TaskBase): IResPostOrderBranch | undefined {
      if (!branchData) { return }
      const children: (IBranch | ILeaf)[] = branchData.children;
      let downloadSize = 0;
      let fileSize = 0;
      let isAllComplete = children.length > 0 ? true : false;
      let downloadTotal = 0 // 下载总数
      let downFailNum = 0 // 下载失败数量
      let isExistDown = false;
      let completedNum = 0 // 下载完成数量
      for (const child of children) {
        if (this.isLeaf(child)) {
          // 判断文件是否需要下载
          const status = this.getFileStatus(child.data.status, taskBase?.taskType)
          const isComplete = status === BaseType.BtSubFileStatus.Complete
          const isFail = status === BaseType.BtSubFileStatus.Failed
          const isDownloading = status === BaseType.BtSubFileStatus.Downloading
          if (child.data.isNeedDownload) {
            downloadSize += child.data.downloadSize || 0;
            fileSize += child.data.fileSize;
            downloadTotal += 1
            if (isComplete) {
              completedNum += 1
            }
            if (isFail) {
              downFailNum += 1
            }
            if (isDownloading) {
              console.log('>>>>>>>>>>>>>>> 正在下载',isExistDown, child)
            }
            isExistDown = isExistDown || isDownloading
          }
          isAllComplete = isAllComplete && isComplete
        } else if (this.isBranch(child)) {
          const ret: IResPostOrderBranch | undefined = this.postOrderBranch(child, taskBase);
          if (ret) {
            downloadTotal += ret.downloadTotal
            downloadSize += ret.downloadSize;
            fileSize += ret.fileSize;
            isAllComplete = isAllComplete && ret.isAllComplete;
            completedNum += ret.completedNum || 0
            downFailNum += ret.downFailNum
            console.log('>>>>>>>>>>>>>>> isExistDown', isExistDown, ret)
            isExistDown = isExistDown || ret.isExistDown
          }
        }
      }
      let progress: number = calculateProgress(fileSize, downloadSize);
      if (!isAllComplete) {
        progress = progress >= 1 ? 0.9999 : progress;
      }
      branchData.data = {
        ...branchData.data,
        downloadSize,
        fileSize,
        isComplete: isAllComplete,
        progress,
        downloadTotal,
        isExistDown,
        downFailNum,
        completedNum,
      };
      return {
        downloadSize,
        fileSize,
        isAllComplete,
        downloadTotal,
        downFailNum,
        isExistDown,
        completedNum,
      }
    },

    isLeaf(item: ILeaf | IBranch): item is ILeaf {
      return item.type === 'leaf';
    },

    isBranch(item: ILeaf | IBranch): item is IBranch {
      return item.type === 'branch';
    },

    isBt(item: ILeaf | IBranch): item is IBranch {
      return item.type === 'bt';
    },

    // 将任务组的TaskStatus转化为BtSubFileStatus, 通常只检查完成和失败
    getFileStatus(status: number|undefined, type: number|undefined): BaseType.BtSubFileStatus|undefined {
      if (type === BaseType.TaskType.Bt) {
        return status;
      }
      // 转换成bt状态
      if (typeof status === 'number' && [BaseType.TaskStatus.Seeding, BaseType.TaskStatus.Succeeded].includes(status)) {
        return BaseType.BtSubFileStatus.Complete;
      } else if (status === BaseType.TaskStatus.Failed || status === BaseType.TaskStatus.Unkown) {
        return BaseType.BtSubFileStatus.Failed;
      } else if (status === BaseType.TaskStatus.Stopped || status === BaseType.TaskStatus.StopPending) {
        return BaseType.BtSubFileStatus.Waiting;
      }
      return BaseType.BtSubFileStatus.Downloading;
    },

    startTask(task: BaseType.TaskBase) {
      taskExtraFunc.startTaskById(task.taskId)
    },

    retryDownload (task: BaseType.TaskBase) {
      taskExtraFunc.recycleTaskById(task.taskId)
    },
    
  },
})