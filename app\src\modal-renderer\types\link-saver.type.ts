/// <reference path="../../common/link-hub/impl/thunder-client-api.d.ts" />

/**
 * 链接保存场景枚举
 */
export enum LinkSaveScene {
  CREATE_TASK = 'create_task',
  PRE_NEW_TASK = 'pre_new_task',
  BATCH_OPERATION = 'batch_operation',
  AUTO_CREATE = 'auto_create',
  CUSTOM = 'custom',
}

/**
 * 链接保存选项
 */
export interface LinkSaveOptions {
  scene: LinkSaveScene | string
  /** 要添加的操作类型数组 */
  actions: ThunderClientAPI.dataStruct.dataModals.ActionType[]
  /** 链接状态，必须传递 */
  status: ThunderClientAPI.dataStruct.dataModals.LinkStatus
  ignoreEvent?: boolean
  customData?: Record<string, any>
  /** 最大并发数，默认无限制 */
  maxConcurrency?: number
  /** 是否等待所有Promise执行完成才返回结果，默认为false（立即返回） */
  waitForCompletion?: boolean
}

/**
 * savePendingAllTasks 函数的参数对象
 */
export interface SavePendingAllTasksParams {
  /** 所有URL及其类型的数组 */
  allUrlsWithType: any[]
  /** 任务数据映射 */
  dataMap: Record<string, any>
  /** URL额外数据映射（可选） */
  urlExtraDataMap?: any
  /** 选中的文件索引映射（可选） */
  checkedFileIndexes?: any
  /** 保存选项 */
  options?: LinkSaveOptions
  /** 选项额外数据 */
  optionsExtData?: any
}

/**
 * 链接保存结果
 */
export interface LinkSaveResult {
  success: boolean
  savedCount: number
  failedCount: number
  errors: Array<{
    url: string
    error: string
  }>
  scene: string
}
