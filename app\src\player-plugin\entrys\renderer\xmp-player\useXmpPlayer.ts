import { provide, inject, useTemplateRef, type InjectionKey, type Ref } from "vue";
import type XmpPlayer from './xmp-player.vue';

export const XmpPlayer_ProvideKey = {
  xmpPlayer: Symbol() as InjectionKey<Ref<InstanceType<typeof XmpPlayer>>>
}

export function useXmpPlayerProvider(refSelector: string) {
  const $xmpPlayer = useTemplateRef<InstanceType<typeof XmpPlayer>>(refSelector)
  provide(XmpPlayer_ProvideKey.xmpPlayer, $xmpPlayer as any)
}

export function useXmpPlayer() {
  return inject(XmpPlayer_ProvideKey.xmpPlayer)
}