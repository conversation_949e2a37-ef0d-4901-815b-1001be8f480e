import * as DownloadKernel from '@root/common/task/impl/task-manager'
import * as DownloadKernelType from '@root/common/task/base'
import { BtTask } from '@root/common/task/impl/bt-task';
import { AccInfo, AccVipType, Command, ForbiddenStatus, IVipGlobalHandler, SecStatus } from './command';
import { NormalTokenCommand } from './normal-token-command';
import { StatusCommand} from './status-command';
import {AccountHelper} from '@root/common/account/client/accountHelper';

export class VipTask {
  private cmds: Map<number, Command> = new Map();
  private runningIndex: Set<number> = new Set();
  private statusCmds: Map<number, Command> = new Map();
  private taskId: number;
  private globalHandler: IVipGlobalHandler;
  private accInfo: AccInfo = {
    forbiddenStatus: ForbiddenStatus.None,
    secStatus: SecStatus.None,
    isVipChannel: false,
    isSuperVipChannel: false,
  }
  private latestEndTime = 0;

  constructor(globalHandler: IVipGlobalHandler, taskId: number) {
    this.taskId = taskId;
    this.globalHandler = globalHandler;
  }
  public getTaskId(): number {
    return this.taskId;
  }

  public getGlobalGetter(): IVipGlobalHandler {
    return this.globalHandler;
  }

  public async onUserInfoChange() {
    let keys: number[] = [];
    this.runningIndex.forEach((index) => {
      this.onEndAcc(index!);
      keys.push(index!);
    });
    this.cmds.clear();
    this.accInfo.isSuperVipChannel = false;
    this.accInfo.isVipChannel = false;
    if (await AccountHelper.getInstance().isSuperVip()) {
      this.accInfo.isSuperVipChannel = true;
      this.accInfo.isVipChannel = true;
    }
    if (await AccountHelper.getInstance().isPlatinumVip()) {
      this.accInfo.isVipChannel = true;
    }
    this.maybeAccInfoChange();

    keys.forEach((index) => {
      this.onBeginAcc(index);
    });
  }

  // 会根据当前状态，决定进行哪种加速，
  public async onBeginAcc(fileIndex: number) {
    let beginTime = (new Date()).getTime();
    let task = await DownloadKernel.GetTaskManager().findTaskById(this.taskId);
    if (!task) {
      return;
    }
    if ((task.getTaskStatus() === DownloadKernelType.TaskStatus.Stopped) ||
      (task.getTaskStatus() === DownloadKernelType.TaskStatus.Failed) ||
      (task.getTaskStatus() === DownloadKernelType.TaskStatus.Succeeded)) {
      return;
    }

    if (this.cmds.has(fileIndex)) {
      return;
    }

    if (!this.runningIndex.has(fileIndex)) {
      this.runningIndex.add(fileIndex);
    }

    // 是否有gcid了
    if (task.getType() === DownloadKernelType.TaskType.Bt) {
      if (fileIndex === -1) {
        return;
      }
      let btTask = task.toExtra<BtTask>();
      let btFile = await btTask.getBtFileByIndex(fileIndex);
      if (!btFile || btFile.getGcid().length === 0 || btFile.getCid().length === 0) {
        return;
      }
    } else if (task.getType() === DownloadKernelType.TaskType.Group) {
      let groupSubTask = await DownloadKernel.GetTaskManager().findTaskById(fileIndex);
      if (!groupSubTask || groupSubTask.getGcid().length === 0 || groupSubTask.getCid().length === 0) {
        return;
      }
    } else if (task.getGcid().length === 0 || task.getCid().length === 0) {
      return;
    }
    if (beginTime < this.latestEndTime) {
      return;
    }
    // 如果还没有查询状态，就先查询状态
    if(!this.statusCmds.has(fileIndex)) {
      let c = new StatusCommand(this, fileIndex);
      this.statusCmds.set(fileIndex, c);
      c.execute();
    }
    // 因为前面有异步，所以要再判断一次
    if (!this.cmds.has(fileIndex)) {
      let c = new NormalTokenCommand(this, fileIndex);
      this.cmds.set(fileIndex, c);
      c.execute();
    }
  }
  // 下载完成或者暂停,不能异步
  public onEndAcc(fileIndex: number) {
    this.latestEndTime = (new Date()).getTime();
    if (this.cmds.has(fileIndex)) {
      this.cmds.get(fileIndex)!.cancel();
      this.cmds.delete(fileIndex);
    }
    if (this.runningIndex.has(fileIndex)) {
      this.runningIndex.delete(fileIndex);
      DownloadKernel.GetTaskManager().findTaskById(this.taskId).then((task) => {
        if (task) {
          if (task.getType() === DownloadKernelType.TaskType.Group) {
            DownloadKernel.GetTaskManager().findTaskById(fileIndex).then((groupSubTask) => {
              if (groupSubTask) {
                groupSubTask.disableDcdnWithVipCert(-1);
              }
            });
          } else {
            task.disableDcdnWithVipCert(fileIndex);
          }
        }
      });
    }
  }

  public async onNewToken(fileIndex: number, token: string) {
    let task = await DownloadKernel.GetTaskManager().findTaskById(this.taskId);
    if (!task) {
      console.error('vipdownload, onNewToken, not found task, taskid=', this.taskId, ',fileIndex=', fileIndex, ',token=', token);
      return;
    }
    this.accInfo.isSuperVipChannel = false;
    this.accInfo.isVipChannel = false;
    if (await AccountHelper.getInstance().isSuperVip()) {
      this.accInfo.isSuperVipChannel = true;
      this.accInfo.isVipChannel = true;
    }
    if (await AccountHelper.getInstance().isPlatinumVip()) {
      this.accInfo.isVipChannel = true;
    }
    this.maybeAccInfoChange();

    console.info('vipdownload, onNewToken, taskid=', this.taskId, ',fileIndex=', fileIndex, ',token=', token);
    if (task.getType() === DownloadKernelType.TaskType.Group) {
      let groupSubTask = await DownloadKernel.GetTaskManager().findTaskById(fileIndex);
      if (groupSubTask) {
        groupSubTask.enableDcdnWithVipCert(token, -1);
      }
    } else {
      task.enableDcdnWithVipCert(token, fileIndex);
    }
  }

  public async onNewStatus(fileIndex: number, isForbidden: boolean, isSec: boolean) {
    let task = await DownloadKernel.GetTaskManager().findTaskById(this.taskId);
    if (!task) {
      return;
    }

    let bNotify = false;
    if (this.accInfo.forbiddenStatus !== ForbiddenStatus.YesForbidden) {
      // 已经处于封禁状态，那就封禁了
      this.accInfo.forbiddenStatus = isForbidden ? ForbiddenStatus.YesForbidden : ForbiddenStatus.NoForbidden;
      bNotify = true;
    }
    if (this.accInfo.secStatus !== SecStatus.YesSec) {
      this.accInfo.secStatus = isSec ? SecStatus.YesSec : SecStatus.NoSec;
      bNotify = true;
    }
    if (bNotify) {
      this.maybeAccInfoChange();
    }
  }

  public maybeAccInfoChange() {
    this.globalHandler.onAccInfoChange(this.taskId, this.accInfo);
  }
}