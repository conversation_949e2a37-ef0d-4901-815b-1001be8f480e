// import { server } from '@xunlei/node-net-ipc/dist/ipc-server';
// import * as BaseType from '../base'
// import { AplayerStack } from '../impl/aplayer-stack';
import { AplayerServerStack } from './aplayer-stack';
import { AplayerServerMedia } from './aplayer-meida';
import { AplayerServerPlayList } from './play-list';
import { AplayerServerSubtitleManager } from './subtilte-manager';
import { AplayerServerPlayHistory } from './aplayer-history';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';

export class AplayerServer {
    private static instance: AplayerServer;
    private stack: AplayerServerStack = new AplayerServerStack();

    constructor() {

    }

    public static GetInstance(): AplayerServer {
        if (!AplayerServer.instance) {
            if (global.AplayerServerInstance) {
                AplayerServer.instance =  global.AplayerServerInstance; 
            } else {
                AplayerServer.instance = new AplayerServer();
                global.AplayerServerInstance = AplayerServer.instance;
            }
        }
        return AplayerServer.instance;
    }

    init() {
        this.stack.init();
        AplayerServerMedia.init();
        AplayerServerPlayList.init();
        AplayerServerSubtitleManager.init();
        AplayerServerPlayHistory.init();
        client.registerFunctions({
            IsAplayerServerInitFinished: () => {
                return true;
            }
        })
        client.broadcastEvent('AplayerServerInitFinished');
    }
}