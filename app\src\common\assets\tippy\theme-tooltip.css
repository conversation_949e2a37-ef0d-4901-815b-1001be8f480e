.tippy-box[data-theme~="tooltip"] {
  padding: 6px 12px 6px 12px;
  border-radius: var(--border-radius-S);
  background: var(--background-neutral-6, #131416E5);
  box-shadow: 0 4px 6px #0000001A, 0 0 1px #0000004D;

  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  text-align: center;
  color: var(--font-white-100);
}

.tippy-box[data-theme~="playerwnd"] {
  padding: 0;
  border-radius: var(--border-radius-S);
  background: var(--background-neutral-6, #131416E5);
  box-shadow: 0 4px 6px #0000001A, 0 0 1px #0000004D;

  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  text-align: center;
  color: var(--font-white-100);
}

.tippy-box[data-theme~="new-task"] {
  padding: 8px 12px;
  border: 1px solid var(--border-border-2, #E5E6EB);
  text-align: left;
  max-width: 300px;
  word-break: break-word;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
  border-radius: var(--border-radius-S, 6px);
  background: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80));
  color: var(--white-white-900, #FFF);
  font-family: "Microsoft YaHei";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

/* new-task theme 的箭头样式 */
.tippy-box[data-theme~="new-task"] .tippy-arrow {
  color: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80));
}