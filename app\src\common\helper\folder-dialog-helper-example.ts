import { FolderDialogHelper } from './folder-dialog-helper';

/**
 * FolderDialogHelper 使用示例
 * 
 * 这个文件展示了如何在Vue组件中使用FolderDialogHelper来处理文件夹和文件选择对话框
 */

export namespace FolderDialogHelperExamples {

  /**
   * 示例1: 简单选择文件夹
   */
  export async function simpleSelectDirectory() {
    const selectedPath = await FolderDialogHelper.selectDirectory();
    if (selectedPath) {
      console.log('选择的文件夹:', selectedPath);
      return selectedPath;
    } else {
      console.log('用户取消了选择');
      return null;
    }
  }

  /**
   * 示例2: 带默认路径和标题的文件夹选择
   */
  export async function selectDirectoryWithDefaults() {
    const selectedPath = await FolderDialogHelper.selectDirectory(
      'C:\\Users\\<USER>\\Projects',
      buttonLabel: '确定',
      properties: ['createDirectory'] // 允许创建新文件夹
    });

    if (!result.canceled && result.filePath) {
      console.log('选择的路径:', result.filePath);
      return result.filePath;
    }
    return null;
  }

  /**
   * 示例6: Vue组件中的使用方法
   */
  export const vueComponentExample = `
<script setup lang="ts">
import { FolderDialogHelper } from '@root/common/helper/folder-dialog-helper';

const handleSelectFolder = async () => {
  try {
    // 方法1: 使用便捷方法
    const path = await FolderDialogHelper.selectDirectory(undefined, '选择文件夹');
    if (path) {
      console.log('选择的文件夹:', path);
      // 处理选择的路径
    }
    
    // 方法2: 使用完整配置
    const result = await FolderDialogHelper.openDirectory({
      title: '选择下载文件夹',
      defaultPath: 'C:\\\\Downloads',
      properties: ['openDirectory', 'createDirectory']
    });
    
    if (!result.canceled && result.filePath) {
      console.log('选择的文件夹:', result.filePath);
      // 处理选择的路径
    }
  } catch (error) {
    console.error('打开文件夹选择对话框失败:', error);
  }
};
</script>
  `;
} 