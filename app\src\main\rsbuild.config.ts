import { defineConfig } from '@rsbuild/core'
import { pluginSvgr } from '@rsbuild/plugin-svgr'
import path from 'node:path'

const { NODE_ENV } = process.env

export default defineConfig({
  output: {
    target: 'node',
    cleanDistPath: false,
    sourceMap: true,
    distPath: {
      root: path.join(process.cwd(), '../../dist'),
      js: '.',
    },
    filename: {
      js: '[name].js',
    },
    dataUriLimit: {
      // svg: 1,
    },
  },
  resolve: {
    alias: {
      '@root': path.join(process.cwd(), '../../src'),
      '@': path.join(process.cwd(), 'src'),
      "@xbase/electron_base_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_base_kit/dist/cjs/development/index.js"),
      "@xbase/electron_common_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_common_kit/dist/cjs/development/index.js"),
      "@xbase/electron_auth_types_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_auth_types_kit/dist/cjs/development/index.js"),
      "@xbase/electron_captcha_types_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_captcha_types_kit/dist/cjs/development/index.js"),
      "@xbase/electron_auth_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_auth_kit/dist/cjs/development/index.js"),
      "@xbase/electron_captcha_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_captcha_kit/dist/cjs/development/index.js"),
      "@xbase/electron_sync_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_sync_kit/dist/cjs/development/index.js"),
      "@xbase/electron_account_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_account_kit/dist/cjs/development/index.js"),
      "@xbase/electron_default_plugins_kit": path.join(process.cwd(), "../../node_modules/", "@xbase/electron_default_plugins_kit/dist/cjs/development/index.js"),
    },
  },
  plugins: [pluginSvgr()],
  tools: {
    rspack: {
      target: 'electron-main',
      entry: {
        main: path.join(__dirname, 'index.ts'),
      },
    },
  },
})
