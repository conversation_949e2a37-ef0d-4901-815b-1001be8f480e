#ifndef __UNINSTALLONLINECONFIG_H__
#define __UNINSTALLONLINECONFIG_H__

#include "HttpDownloader.h"

class CUninstallOnlineConfig :
    public IHttpDownloadEvent
{
public:
    CUninstallOnlineConfig();
    ~CUninstallOnlineConfig();

public:
    static CUninstallOnlineConfig* GetInstance();
    int Initilize();
    bool IsRemainExtensions() const;

private:
    // IDownloadEvent
    virtual long OnDownloadBegin(void* pUserData);
    virtual long OnDownloadProgressChanged(DWORD dwProgress, DWORD dwSpeed, void* pUserData);
    virtual long OnDownloadFinish(long hrStatus, const wchar_t* pszSavePath, void* pUserData);

    BOOL ParseOnlineConfig(const std::string& strContent);

private:
    CHttpDownloader m_downloader;
    bool m_remainExt;
};

#define theOnlineConfigManager CUninstallOnlineConfig::GetInstance()

#endif  //__UNINSTALLONLINECONFIG_H__
