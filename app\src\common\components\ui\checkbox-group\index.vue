<script setup lang="ts">
import { computed } from 'vue';
import { CheckboxRoot, CheckboxIndicator, CheckboxGroupRoot } from 'reka-ui'
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import Select, { ISelectItem } from '@root/common/components/ui/select/index.vue'

// 定义操作类型
interface CheckboxAction {
  type?: 'input' | 'link' | 'select'
  options?: ISelectItem[]
  disabled?: boolean
  label?: string
  value?: string
  name?: string
  width?: number
  onAction?: (value: string, optionName: string) => void
  onKeyDown?: (e: KeyboardEvent, optionName: string) => void
}

export interface ICheckoutGroupOptions {
  label: string
  name: string
  // label 后缀
  labelSuffix?: string
  subLabel?: string
  action?: CheckboxAction
  tip?: string
  errorText?: string
  defaultValue?: boolean
  onChange?: (checked: boolean, optionName: string) => void  // 选项变化回调
}

const props = withDefaults(defineProps<{
  modelValue: string[]
  options: ICheckoutGroupOptions[],
  title?: string
  defaultValue?: string[]
  orientation?: 'horizontal' | 'vertical'
}>(), {
  orientation: 'horizontal',
})


// 创建计算属性来处理双向绑定
const localModelValue = computed({
  get: () => props.modelValue,
  set: (value: string[]) => {
    handleUpdateModelValue(value)
  }
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string[]): void
  (e: 'optionChange', checked: boolean, optionName: string): void
}>()

const handleUpdateModelValue = (value: string[]) => {
  emit('update:modelValue', value)

  // 检查哪些选项发生了变化
  const previousValue = props.modelValue
  const added = value.filter(v => !previousValue.includes(v))
  const removed = previousValue.filter(v => !value.includes(v))

  // 触发添加的选项的回调
  added.forEach(optionName => {
    const option = props.options.find(opt => opt.name === optionName)
    if (option?.onChange) {
      option.onChange(true, optionName)
    }
    emit('optionChange', true, optionName)
  })

  // 触发移除的选项的回调
  removed.forEach(optionName => {
    const option = props.options.find(opt => opt.name === optionName)
    if (option?.onChange) {
      option.onChange(false, optionName)
    }
    emit('optionChange', false, optionName)
  })
}

</script>

<template>
  <div class="CheckboxGroupTitle" v-if="title">
    <span>{{ title }}</span>
  </div>

  <CheckboxGroupRoot class="CheckboxGroupRoot" :orientation="orientation" v-model="localModelValue">
    <div v-for="item in options" :key="item.name" class="CheckboxGroupItemWrapper">
      <div class="CheckboxGroupItem">
        <CheckboxRoot class="CheckboxRoot" :id="item.name" :value="item.name">
          <CheckboxIndicator class="CheckboxIndicator">
            <i class="xl-icon-hook"></i>
          </CheckboxIndicator>
        </CheckboxRoot>
        <label class="Label" :for="item.name">
          {{ item.label }}
        </label>

        <!-- 操作区域 -->
        <div v-if="item.action" class="CheckboxGroupAction">

          <!-- 按钮操作 -->
          <Tooltip :default-open="!!item.errorText" :side="'top'" :align="'start'" :show-arrow="true"
            contentClass="checkbox-group-tip-content" :auto-open="false" arrowClass="checkbox-group-tip-arrow">
            <template #trigger>
              <xl-input :disabled="!modelValue.includes(item.name)" v-if="item.action.type === 'input'"
                :style="{ height: '32px', ...(item.action.width ? { width: item.action.width + 'px' } : {}) }"
                v-model="item.action.value" :need-debounce="true"
                @update:model-value="(value: string) => item?.action?.onAction && item.action.onAction(value, item?.action?.name ?? '')"
                @keydown="(e: KeyboardEvent) => item?.action?.onKeyDown && item.action.onKeyDown(e, item.name)" />
            </template>
            <template #content v-if="item.errorText">
              {{ item.errorText }}
            </template>
          </Tooltip>

          <!-- 选择操作 -->
          <Select :disable="!modelValue.includes(item.name)" v-if="item.action.type === 'select'"
            :options="item.action.options ?? []" v-model="item.action.value!" anchor-class="checkbox-group-select"
            content-class="checkbox-group-select-content" :max-height="160"
            @onSelect="(value: string) => item?.action?.onAction && item.action.onAction(value, item?.action?.name ?? '')" />

          <!-- 链接操作 -->
          <a v-if="item.action.type === 'link'" class="CheckboxGroupLink"
            @click="item?.action?.onAction && item.action.onAction('', item?.action?.name ?? '')">
            {{ item.action.label }}
          </a>
        </div>

        <div class="Label" v-if="item.labelSuffix">
          {{ item.labelSuffix }}
        </div>

        <div class="CheckboxGroupTip" v-if="item.tip">
          <Tooltip :default-open="!!item.errorText" :max-width="200">
            <template #trigger>
              <i class="xl-icon-tips-question-circle-l"></i>
            </template>
            <template #content>
              {{ item.tip }}
            </template>
          </Tooltip>
        </div>
      </div>
      <div class="CheckboxGroupItemSubLabel" v-if="item.subLabel">
        {{ item.subLabel }}
      </div>
    </div>
  </CheckboxGroupRoot>
</template>

<style scoped lang="scss">
.CheckboxGroupTitle {
  color: var(--font-font-3, #86909C);
  font-size: 13px;
  line-height: 22px;
  margin-bottom: 6px;
  height: 32px;
  display: flex;
  align-items: center;
}

.CheckboxGroupRoot {
  display: flex;
}

.CheckboxGroupRoot[data-orientation='vertical'] {
  flex-direction: column;
  gap: 6px;
}

.CheckboxGroupRoot[data-orientation='horizontal'] {
  flex-direction: row;
  gap: 24px;
}

.CheckboxGroupItemWrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.CheckboxGroupItem {
  display: flex;
  height: 32px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

.CheckboxGroupItemSubLabel {
  color: var(--font-font-4, #C9CDD4);
  font-size: 12px;
  line-height: 20px;
}

.CheckboxRoot {
  width: 14px;
  height: 14px;
  border-radius: var(--border-radius-XS, 3px);
  border: 1px solid var(--border-border-1, #C9CDD4);
}

.CheckboxRoot:hover {
  border-color: var(--button-button1-default, rgba(39, 46, 59, 1));
  cursor: pointer;
}

.CheckboxRoot[data-state='checked'] {
  background-color: var(--button-button1-default, rgba(39, 46, 59, 1));
  border-color: var(--button-button1-default, rgba(39, 46, 59, 1));
  position: relative;
}


.CheckboxIndicator {
  color: var(--background-background-elevated);

  i {
    font-size: 12px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

}

.Label {
  color: var(--font-font-2, #4E5769);
  font-size: 13px;
  line-height: 22px;
  min-width: fit-content;
}

.CheckboxGroupTip {
  color: var(--font-font-3, #86909C);
}

.CheckboxGroupLink {
  color: var(--primary-font-default, #3F85FF);
  font-size: 13px;
  line-height: 22px;
  cursor: pointer;
  margin-left: -4px;
}

.CheckboxGroupAction {
  display: flex;
  align-items: center;
}
</style>

<style lang="scss">
.checkbox-group-tip-content {
  background: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
  height: 32px !important;
  padding: 0 12px !important;
  color: var(--white-white-900, #FFF) !important;
  line-height: 20px !important;
}

.checkbox-group-tip-arrow {
  fill: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
}

.checkbox-group-select {
  width: 104px !important;
  height: 32px !important;
  gap: 4px !important;


  input {
    width: 100%;
    color: var(--font-font-2, #4E5769);
    font-size: 13px;
  }

  i {
    color: var(--font-font-3, #898E97);
  }
}

.checkbox-group-select-content {
  width: 104px !important;
}
</style>