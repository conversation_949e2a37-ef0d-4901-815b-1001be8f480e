#pragma  once




struct HandleCloser
{
	void operator()(HANDLE hHnd)
	{
		CloseHandle(hHnd);
	}
};



template<typename HndT = HANDLE, typename RlsT = HandleCloser, HndT InvalidValue = NULL>
class CAutoHandle
{
public:
	CAutoHandle():m_hHandle(InvalidValue)
	{
	}
	CAutoHandle(HANDLE hHandle):m_hHandle(hHandle)
	{
	}
	~CAutoHandle()
	{
		Release();
	}
	HRESULT Release()
	{
		if (m_hHandle != InvalidValue)
		{
			RlsT rls;
			rls(m_hHandle);
			m_hHandle = InvalidValue;
		}
		return S_OK;
	}

	operator HANDLE()
	{
		return m_hHandle;
	}
	CAutoHandle& operator = (HANDLE hnd)
	{
		Release();
		m_hHandle = hnd;
		return *this;
	}
	bool IsInit()
	{
		return m_hHandle != InvalidValue;
	}

private:
	CAutoHandle(const CAutoHandle& hnd);
	CAutoHandle& operator = (CAutoHandle& hnd);

private:
	HndT m_hHandle;
};
