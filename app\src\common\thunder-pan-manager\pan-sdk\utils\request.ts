/**
 * 请求实现的工具类
 */

export interface IXbaseSDKConfig {
  algVersion: string
  clientId: string
  packageName: string;
  signKey: string
}

export interface IPubKey {
  alg: string
  salt: string
}

export interface IGenCaptchaSignParams {
  xbaseSDKConfig: IXbaseSDKConfig
  pubKeys: IPubKey[]
  timestamp: string
  appVersion: string
  deviceId: string
  md5Fn: (str: string) => string
}

/**
 * 生成请求所需的熊端签名
 * @param params 生成参数
 * @returns
 */
export function genCaptchaSign (params: IGenCaptchaSignParams) {
  const { xbaseSDKConfig, pubKeys, timestamp, appVersion, deviceId, md5Fn } = params;
  // 初始签名内容
  let captchaSign = xbaseSDKConfig.clientId + appVersion + xbaseSDKConfig.packageName + deviceId + timestamp;
  // 按服务端 pubKey 轮次加密，使用上一次的 sign + salt 进行 md5 加密
  pubKeys.forEach(item => {
    if (item.alg === 'md5') {
      captchaSign = md5Fn(`${captchaSign}${item.salt}`);
    }
  });
  // 最终签名结果为 algVersion + captchaSign，algVersion 默认为 1，固定值参考 xbaseSDKConfig
  return `${xbaseSDKConfig.algVersion}.${captchaSign}`;
}

/**
 * 获取请求所需的熊盾 action
 * @param method 请求方法
 * @param url 请求连接
 * @returns
 */
export function getCaptchaAction (method: string, url: string) {
  if (method.toLocaleUpperCase() === 'GET') {
    return 'GET:CAPTCHA_TOKEN';
  }

  const matchUrl = url.split(/(?<=xunlei\.com)/g);
  const path = matchUrl.length === 2 ? matchUrl[1] : null;

  if (path === null) {
    return 'GET:CAPTCHA_TOKEN';
  }
  return `${method}:${path}`;
}
