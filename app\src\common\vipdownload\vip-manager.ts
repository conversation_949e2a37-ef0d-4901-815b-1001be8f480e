import * as DownloadKernel from '@root/common/task/impl/task-manager'
import * as DownloadKernelType from '@root/common/task/base'
import { VipTask } from './task';
import { VipGlobalData, IVipGlobalHandler, AccInfo} from './command'
import { EventEmitter } from 'events';
import { AccountHelper } from '@root/common/account/impl/accountHelper';

export enum VipEventType {
  ACC_INFO_CHANGE = 'acc_info_change',
}
export class VipManager implements IVipGlobalHandler {
  private static g_VipManager: VipManager | null;
  private tasks: Map<number, VipTask> = new Map();
  private runningTask: Set<number> = new Set();
  private globalData: VipGlobalData| null = null;
  private eventEmitter: EventEmitter = new EventEmitter();

  public static getInstance(): VipManager {
    if (!VipManager.g_VipManager) {
      VipManager.g_VipManager = new VipManager();
    }

    return VipManager.g_VipManager;
  }

  public getData(): VipGlobalData {
    return this.globalData!;
  }

  public onAccInfoChange(taskId: number, info: AccInfo): void {
    this.eventEmitter.emit(VipEventType.ACC_INFO_CHANGE, taskId, info);
  }

  public on(name: VipEventType, listener: any) {
    this.eventEmitter.on(name, listener);
  }

  public off(name: VipEventType, listener: any): void {
    this.eventEmitter.off(name, listener);
  }

  public async init(globalData: VipGlobalData) {
    console.log('========================1');
    this.globalData = globalData;
    DownloadKernel.GetTaskManager().attachTaskStatusChangeEvent((taskId: number, eOld: DownloadKernelType.TaskStatus,  eNew: DownloadKernelType.TaskStatus) => {
        if (eNew === DownloadKernelType.TaskStatus.Started) {
          this.runningTask.add(taskId);
          this.onBeginAcc(taskId, -1);
        } else if (eNew === DownloadKernelType.TaskStatus.Stopped || eNew === DownloadKernelType.TaskStatus.Succeeded || eNew === DownloadKernelType.TaskStatus.Failed) {
          this.runningTask.delete(taskId);
          this.onEndAcc(taskId, -1);
        }
      });
    DownloadKernel.GetTaskManager().attachTaskDetailChangeEvent((taskId: number, flags: DownloadKernelType.TaskDetailChangedFlags) => {
      if ((flags & DownloadKernelType.TaskDetailChangedFlags.Gcid) || (flags & DownloadKernelType.TaskDetailChangedFlags.Cid)) {
        this.onBeginAcc(taskId, -1);
      }
    });
    DownloadKernel.GetTaskManager().attachBtSubTaskStatusChangeEvent((taskId: number, fileIndex: number, eOld: DownloadKernelType.BtSubFileStatus, eNew: DownloadKernelType.BtSubFileStatus) => {
      if (eNew === DownloadKernelType.BtSubFileStatus.Downloading) {
        this.onBeginAcc(taskId, fileIndex);
      } else {
        this.onEndAcc(taskId, fileIndex);
      }
    });
    DownloadKernel.GetTaskManager().attachBtSubTaskDetailChangeEvent((taskId: number, fileIndex: number, flags: DownloadKernelType.BtSubFileDetailChangeFlags) => {
      if ((flags & DownloadKernelType.BtSubFileDetailChangeFlags.Gcid) || (flags & DownloadKernelType.BtSubFileDetailChangeFlags.Cid)) {
        this.onBeginAcc(taskId, fileIndex);
      }
    });
    DownloadKernel.GetTaskManager()
      .attachGroupSubTaskStatusChangeEvent((taskId: number, groupTaskId: number, eOld: DownloadKernelType.TaskStatus,
        eNew: DownloadKernelType.TaskStatus) => {
        if (eNew === DownloadKernelType.TaskStatus.Started) {
          this.onBeginAcc(groupTaskId, taskId);
        } else if (eNew === DownloadKernelType.TaskStatus.Stopped || eNew === DownloadKernelType.TaskStatus.Succeeded ||
          eNew === DownloadKernelType.TaskStatus.Failed) {
          this.onEndAcc(groupTaskId, taskId);
        }
      });
    DownloadKernel.GetTaskManager()
      .attachGroupSubTaskDetailChangeEvent((taskId: number, groupTaskId: number, flags: DownloadKernelType.TaskDetailChangedFlags) => {
        if ((flags & DownloadKernelType.TaskDetailChangedFlags.Gcid) ||
          (flags & DownloadKernelType.TaskDetailChangedFlags.Cid)) {
          this.onBeginAcc(groupTaskId, taskId);
        }
      });
      console.log('========================2');
    AccountHelper.getInstance().attachEvent('AccountHelper_EventKey_SignInSuccess' as any, () => {
      this.onUserInfoChange();
    });
    AccountHelper.getInstance().attachEvent('AccountHelper_EventKey_SignOut' as any, () => {
      this.onUserInfoChange();
    });
    AccountHelper.getInstance().attachEvent('AccountHelper_EventKey_UserInfoChange' as any, () => {
      this.onUserInfoChange();
    });
  }

  private async onBeginAcc(taskId: number, fileIndex: number) {
    let task = await DownloadKernel.GetTaskManager().findTaskById(taskId);
    if (task && task.isPanTask()) {
      return;
    }

    if (!this.tasks.has(taskId)) {
      let vipTask = new VipTask(this, taskId);
      this.tasks.set(taskId, vipTask);
    }
    if (task!.getType() === DownloadKernelType.TaskType.Bt && fileIndex === -1) {
      return;
    }

    this.tasks.get(taskId)!.onBeginAcc(fileIndex);
  }
  private onEndAcc(taskId: number, fileIndex: number) {
    this.tasks.get(taskId)?.onEndAcc(fileIndex);
  }

  private onUserInfoChange() {
    this.runningTask.forEach((taskId: number) => {
      if (this.tasks.has(taskId)) {
        this.tasks.get(taskId)!.onUserInfoChange();
      }
    })
  }
}