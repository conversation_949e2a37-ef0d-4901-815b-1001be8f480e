<template>
  <div class="task-capabilities-example">
    <h3>任务能力示例组件</h3>
    
    <!-- 磁力链解析示例 -->
    <div class="section">
      <h4>磁力链解析</h4>
      <input 
        v-model="magnetUrl" 
        placeholder="输入磁力链接" 
        class="input"
      />
      <button @click="handleParseMagnet" :disabled="parsing">
        {{ parsing ? '解析中...' : '解析磁力链' }}
      </button>
      <button @click="handleRetryParseMagnet" :disabled="!magnetUrl">
        重试解析
      </button>
    </div>

    <!-- 任务创建示例 -->
    <div class="section">
      <h4>任务创建</h4>
      <input 
        v-model="downloadUrl" 
        placeholder="输入下载链接" 
        class="input"
      />
      <button @click="handleCreateP2spTask" :disabled="!downloadUrl">
        创建P2SP任务
      </button>
      <button @click="handleCreateEmuleTask" :disabled="!downloadUrl">
        创建Emule任务
      </button>
    </div>

    <!-- 播放任务示例 -->
    <div class="section">
      <h4>播放任务</h4>
      <button @click="handlePlayTasks" :disabled="!hasValidTasks">
        播放选中任务
      </button>
    </div>

    <!-- 下载任务示例 -->
    <div class="section">
      <h4>下载任务</h4>
      <button @click="handleDownloadTasks" :disabled="!hasValidTasks">
        下载选中任务
      </button>
      <button @click="handleDownloadTasksAsGroup" :disabled="!hasValidTasks">
        合并为任务组下载
      </button>
    </div>

    <!-- 状态显示 -->
    <div class="section">
      <h4>状态信息</h4>
      <p>当前保存路径: {{ taskSavePath }}</p>
      <p>是否有有效任务: {{ hasValidTasks ? '是' : '否' }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useCreateTaskCapabilities } from '@root/modal-renderer/src/composables/useCreateTaskCapabilities'
import type { 
  TaskFileSelectionMap, 
  IUrlDataMap, 
  IUrlWithTypeArray,
  DownloadEventParams 
} from '@root/modal-renderer/types/new-task.type'

// 使用创建任务能力
const {
  downloadTasks,
  downloadTasksWithGroup,
  playTasks,
  parseMagnetLink,
  retryParseMagnetLink,
  createBtTask,
  createP2spTask,
  createEmuleTask,
  getTaskSavePath,
  isValidTasks,
} = useCreateTaskCapabilities()

// 响应式数据
const magnetUrl = ref('')
const downloadUrl = ref('')
const parsing = ref(false)

// 模拟的任务数据（实际使用中应该从父组件传递）
const mockTaskData = ref<IUrlWithTypeArray>([])
const mockDataMap = ref<IUrlDataMap>({})
const mockCheckedFileIndexes = ref<TaskFileSelectionMap>({})

// 计算属性
const taskSavePath = computed(() => getTaskSavePath())
const hasValidTasks = computed(() => isValidTasks(mockTaskData.value))

// 事件处理函数
const handleParseMagnet = async () => {
  if (!magnetUrl.value.trim()) {
    alert('请输入磁力链接')
    return
  }

  parsing.value = true
  try {
    const success = await parseMagnetLink(magnetUrl.value.trim())
    if (success) {
      alert('磁力链解析成功')
    } else {
      alert('磁力链解析失败')
    }
  } catch (error) {
    console.error('解析磁力链时出错:', error)
    alert('解析磁力链时出错')
  } finally {
    parsing.value = false
  }
}

const handleRetryParseMagnet = async () => {
  if (!magnetUrl.value.trim()) {
    alert('请输入磁力链接')
    return
  }

  parsing.value = true
  try {
    const success = await retryParseMagnetLink(magnetUrl.value.trim())
    if (success) {
      alert('磁力链重试解析成功')
    } else {
      alert('磁力链重试解析失败')
    }
  } catch (error) {
    console.error('重试解析磁力链时出错:', error)
    alert('重试解析磁力链时出错')
  } finally {
    parsing.value = false
  }
}

const handleCreateP2spTask = async () => {
  if (!downloadUrl.value.trim()) {
    alert('请输入下载链接')
    return
  }

  try {
    const task = await createP2spTask(downloadUrl.value.trim())
    if (task) {
      alert(`P2SP任务创建成功，任务ID: ${task.getId()}`)
    } else {
      alert('P2SP任务创建失败')
    }
  } catch (error) {
    console.error('创建P2SP任务时出错:', error)
    alert('创建P2SP任务时出错')
  }
}

const handleCreateEmuleTask = async () => {
  if (!downloadUrl.value.trim()) {
    alert('请输入Emule链接')
    return
  }

  try {
    const task = await createEmuleTask(downloadUrl.value.trim())
    if (task) {
      alert(`Emule任务创建成功，任务ID: ${task.getId()}`)
    } else {
      alert('Emule任务创建失败')
    }
  } catch (error) {
    console.error('创建Emule任务时出错:', error)
    alert('创建Emule任务时出错')
  }
}

const handlePlayTasks = () => {
  // playTasks(
  //   mockCheckedFileIndexes.value,
  //   mockDataMap.value,
  //   mockTaskData.value,
  //   'task-capabilities-example'
  // )
}

const handleDownloadTasks = async () => {
  const params: DownloadEventParams = {
    checkedFileIndexes: mockCheckedFileIndexes.value,
    type: 'download',
  }

  try {
    await downloadTasks(params)
    alert('任务下载开始')
  } catch (error) {
    console.error('下载任务时出错:', error)
    alert('下载任务时出错')
  }
}

const handleDownloadTasksAsGroup = async () => {
  try {
    await downloadTasksWithGroup(`任务组_${Date.now()}`)
    alert('任务组下载开始')
  } catch (error) {
    console.error('下载任务组时出错:', error)
    alert('下载任务组时出错')
  }
}
</script>

<style scoped>
.task-capabilities-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.section h4 {
  margin-top: 0;
  color: #333;
}

.input {
  width: 300px;
  padding: 8px;
  margin-right: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  padding: 8px 16px;
  margin-right: 10px;
  margin-bottom: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background-color: #0056b3;
}

p {
  margin: 5px 0;
  color: #666;
}
</style> 