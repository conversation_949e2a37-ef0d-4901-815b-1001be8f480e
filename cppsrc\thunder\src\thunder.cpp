﻿
#define WIN32_LEAN_AND_MEAN             // 从 Windows 头文件中排除极少使用的内容
// Windows 头文件
#include <windows.h>
// C 运行时头文件
#include <stdlib.h>
#include <malloc.h>
#include <memory.h>
#include <tchar.h>
#include <Shlwapi.h>
#include <shellapi.h>
#include <string>
#include <xlcommon.h>
#include "./shellcommand/XLShellCommandManager.h"
#include "./shellcommand/XmpMutex.h"
#include "bugreport_wrap.h"
#include <peer_id/peer_id.h>
#include <AdminShell/admin_shell_recv.h>
#include <XmpAssociate/xmp-associate.h>
#include <ShlObj.h>
#include <ShellScalingApi.h>
#include "ThunderCore.h"
#define MAX_LOADSTRING 100

typedef int(__cdecl* FunRunMain)(HINSTANCE instance, HINSTANCE, LPSTR cmd, int);
typedef void(* InitPlayerContainor)();

const char* kType = "--type=";
const char* kRender = "--type=renderer";
const char* kGpu = "--type=gpu-process";
const char* kUtility = "--type=utility";
const char* kPlugin = "--type=xdas-plugin-process";

static unsigned s_ustatThreadId = 0;
static unsigned int s_uStatCookie = 0;
HANDLE KInitStatEvent = NULL;
HANDLE KUninitStatEvent = NULL;
unsigned int kStatThreadId = 0;

static PCWSTR XMPXDASMUTEX = L"xmp_xdas_app_mutex";
static PCWSTR XMPXDASADMINSHELLMUTEX = L"xmp_xdas_admin_shell_mutex";

#define WM_UNINIT_STAT_EVENT (WM_USER + 200)

std::string GetProcessType(LPCSTR lpszCmdLine)
{
	std::string process_type = "";
	std::string command_line(lpszCmdLine);
	do
	{
		std::string::size_type index = std::string::npos;
		index = command_line.find(kType);
		if (index == std::string::npos)
		{
			process_type = "Browser";
			break;
		}

		index = command_line.find(kRender);
		if (index != std::string::npos)
		{
			process_type = "Render";
			break;
		}

		index = command_line.find(kGpu);
		if (index != std::string::npos)
		{
			process_type = "GPU";
			break;
		}

		index = command_line.find(kPlugin);
		if (index != std::string::npos)
		{
			process_type = "Plugin";
			break;
		}

		index = command_line.find(kUtility);
		if (index != std::string::npos)
		{
			process_type = "Utility";
			break;
		}
	} while (false);

	return process_type;
}

static std::string ReplaceAll(std::string& src, const std::string& from, const std::string& to) {
	size_t start_pos = 0;
	while ((start_pos = src.find(from, start_pos)) != std::string::npos) {
		src.replace(start_pos, from.length(), to);
		start_pos += to.length(); // 移动到替换后的位置
	}
}

BOOL TryPost2PreviousProcess(const wchar_t* lpCmdLine, std::wstring& strCmdToExcute)
{
	std::wstring wstrCmdLine(lpCmdLine);
	//xl::text::transcode::ANSI_to_Unicode(lpCmdLine, strlen(lpCmdLine), wstrCmdLine);
	//std::string strCmdLine = lpCmdLine;
	//ReplaceAll(strCmdLine, "/AgentFile ", "-AgentFile:");
	//int nIndex = strCmdLine.find("-Restart");
	//if (nIndex != std::string::npos)
	//{
	//	int nIndex = strCmdLine.find(":", nIndex);
	//	std::string strPid = strCmdLine.Right(strCmdLine.GetLength() - nIndex - 1);
	//	DWORD pid = (DWORD)_ttoi(strPid);
	//	//TODO
	//	//ThunderProcess::ExitPreviousProcess(pid);
	//}

	////解析出启动来源
	//nIndex = strCmdLine.find("-StartType");
	//CString strStartType;
	//if (nIndex != -1)
	//{
	//	int nBegin = strCmdLine.Find(L":", nIndex);
	//	int nEnd = strCmdLine.Find(L" ", nBegin);
	//	if (nEnd == -1)
	//	{
	//		nEnd = strCmdLine.GetLength();
	//	}
	//	strStartType = strCmdLine.Mid(nBegin + 1, nEnd - nBegin);
	//}

	BOOL bRet = true;
	if (!xlShellCommandManager.Init(wstrCmdLine.c_str()))
	{
		bRet = false;
		do
		{
			//if (!ThunderProcess::IsPreviousProcessExist(lpCmdLine))
			//{
			//	//上一个迅雷进程被杀死，一定要等到上一个进程的窗口消失，才重新初始化
			//	HWND hwndShellCommand = ::FindWindow(XL_WINDOW_SHELLCOMMAND_NAME, XL_WINDOW_SHELLCOMMAND_NAME);
			//	while (hwndShellCommand)
			//	{
			//		hwndShellCommand = ::FindWindow(XL_WINDOW_SHELLCOMMAND_NAME, XL_WINDOW_SHELLCOMMAND_NAME);
			//	}
			//	xlShellCommandManager.CreateShellCommandWnd();
			//	break;
			//}

			// 如果是空命令 或者只有StartType命令 或者只有StartType和quicklaunch命令，那么就发送一个激活主窗口的命令。
			// 以后如果starttype的参数会影响主界面是否激活, 这里记得改
			DWORD dwCmdCount = 0;
			xlShellCommandManager.GetCommandCount(dwCmdCount);

			std::wstring wstrFirstCommand;
			std::wstring wstrSecondCommand;
			xlShellCommandManager.GetCommandByIndex(0, wstrFirstCommand);
			if (dwCmdCount > 1)
			{
				xlShellCommandManager.GetCommandByIndex(1, wstrSecondCommand);
			}
			bool hasStartTypeCmd = (StrStrIW(wstrFirstCommand.c_str(), L"-starttype:") == wstrFirstCommand.c_str()
				|| StrStrIW(wstrSecondCommand.c_str(), L"-starttype:") == wstrSecondCommand.c_str());
			/*bool hasQuickLaunchCmd = (StrStrI(wstrFirstCommand.c_str(), L"-quicklaunch:") == wstrFirstCommand.c_str()
				|| StrStrI(wstrSecondCommand.c_str(), L"-quicklaunch:") == wstrSecondCommand.c_str());
			bool hasUnfinishTaskNum = (StrStrI(wstrFirstCommand.c_str(), L"-UnfinishTaskNum:") == wstrFirstCommand.c_str()
				|| StrStrI(wstrSecondCommand.c_str(), L"-UnfinishTaskNum:") == wstrSecondCommand.c_str());*/
			/*if (dwCmdCount == 0
				|| (dwCmdCount == 1 && hasStartTypeCmd)
				|| (dwCmdCount == 2 && hasStartTypeCmd && (hasQuickLaunchCmd || hasUnfinishTaskNum))
				)*/
			if (dwCmdCount == 0
				|| (dwCmdCount == 1 && hasStartTypeCmd))
			{
				std::wstring wstrPostCommand = L"-window:bringtotop ";
				//if (hasQuickLaunchCmd || hasUnfinishTaskNum)
				//{
				//	if (StrStrI(wstrFirstCommand.c_str(), L"-quicklaunch:") == wstrFirstCommand.c_str()
				//		|| StrStrI(wstrFirstCommand.c_str(), L"-UnfinishTaskNum:") == wstrFirstCommand.c_str()
				//		)
				//	{
				//		wstrPostCommand += wstrFirstCommand;
				//	}
				//	else
				//	{
				//		wstrPostCommand += wstrSecondCommand;
				//	}
				//}
				//// 激活的统计改由前一个thunder.exe发送
				//// 这里要把starttype一起传过去，不过要换个参数名
				//wstrPostCommand += L"-ActiveType:";
				//wstrPostCommand += strStartType.GetBuffer();
				xlShellCommandManager.PostCommandLine(wstrPostCommand.c_str());
			}
			else
			{
				xlShellCommandManager.PostCommandLine(wstrCmdLine.c_str());
			}
			return false;
		} while (true);
	}

	strCmdToExcute = wstrCmdLine;
	return true;
}

std::string GetFileVersion(std::string strFile) {
	std::string strVersion;
	do
	{
		auto nInfoSize = ::GetFileVersionInfoSize(strFile.c_str(), 0);
		if (nInfoSize < 0) {
			break;
		}

		void* buffer = new char[nInfoSize + 4];
		if (::GetFileVersionInfo(strFile.c_str(), 0, nInfoSize, buffer))
		{
			uint32_t file_info_size = 0;
			VS_FIXEDFILEINFO* file_info = NULL;
			if (::VerQueryValue(buffer, "\\", (void**)&file_info, &file_info_size))
			{
				int ver1 = (int)(((uint64_t)file_info->dwFileVersionMS) >> 16);
				int ver2 = (int)(((uint64_t)file_info->dwFileVersionMS) & 0xFFFF);
				int ver3 = (int)(((uint64_t)file_info->dwFileVersionLS) >> 16);
				int ver4 = (int)(((uint64_t)file_info->dwFileVersionLS) & 0xFFFF);
				char ver_buf[64] = { 0 };
				sprintf(ver_buf, "%d.%d.%d.%d", ver1, ver2, ver3, ver4);
				strVersion = ver_buf;
			}
		}
		delete[] buffer;

	} while (false);
	return strVersion;
}

void InitBugreport(bool slient, const std::string& strType)
{
	XL_InitBugReport();

	XL_SetExtraInfo("process_type", strType.c_str());
	std::wstring wstrType;
	xl::text::transcode::ANSI_to_Unicode(strType.c_str(), strType.length(), wstrType);

	TCHAR szExePath[MAX_PATH] = { 0 };
	GetModuleFileName(NULL, szExePath, MAX_PATH);

	TCHAR szPath[MAX_PATH] = { 0 };
	GetTempPath(MAX_PATH, szPath);
	PathAppend(szPath, "Thunder Network\\xl25\\");
	std::wstring wstrPath;
	xl::text::transcode::ANSI_to_Unicode(szPath, strlen(szPath), wstrPath);
	XL_SetBugReportRootDir(wstrPath.c_str());

	std::string strPeerId = peer_id::acquire_peerid(flag::XUNLEI_X);
	XL_SetPeerID(strPeerId.c_str());//设备标识

	std::string strVersion = GetFileVersion(szExePath);
	std::wstring wstrVersion;
	xl::text::transcode::ANSI_to_Unicode(strVersion.c_str(), strVersion.length(), wstrVersion);

	std::wstring productID = L"thunder11";
	XL_InitBugHandler(L"迅雷", NULL, NULL, productID.c_str(), wstrVersion.c_str());
	if (slient)
	{
		XL_SetReportShowMode(XLBUGREPORT_SHOWMODE_SILENT);
	}
	else
	{
		XL_SetReportShowMode(XLBUGREPORT_SHOWMODE_NORMAL);
	}
	XL_SetAlwaysSendReport(TRUE);

	std::wstring custom_info;
	custom_info += L"process_type=";
	custom_info += wstrType;
	custom_info += L"\n";

	unsigned int buffer_length = custom_info.length();
	wchar_t* buffer = new wchar_t[buffer_length];
	memcpy(buffer, custom_info.c_str(), buffer_length * sizeof(wchar_t));

	CustomInfo* info = new CustomInfo();
	info->buffer = buffer;
	info->buffer_size = buffer_length * sizeof(wchar_t);
	XL_SetCustomInfo(info);
}

bool IsPlayer(LPCSTR lpCmdLine)
{
	bool ret = false;
	do
	{
		if (!lpCmdLine)
		{
			break;
		}

		std::string strCmdline(lpCmdLine);
		transform(strCmdline.begin(), strCmdline.end(), strCmdline.begin(), tolower);
		int nLen = (int)strCmdline.length();
		if (nLen <= 0)
		{
			break;
		}

		auto n = strCmdline.find("--server-id=");
		if (n == std::string::npos)
		{
			break;
		}

		ret = true;

	} while (0);
	return ret;
}

int RunMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPTSTR lpCmdLine, int nCmdShow)
{
	do
	{
		auto processType = GetProcessType(lpCmdLine);
		auto bSlient = (processType == "GPU" || processType == "Utility");
		InitBugreport(bSlient, processType);
		::OutputDebugStringA(processType.c_str());
		if (processType == "Browser") {
			int* p = nullptr;
			if (IsPlayer(lpCmdLine)) {
				char path[MAX_PATH] = { 0 };
				::GetModuleFileNameA(NULL, path, MAX_PATH);
				::PathAppendA(path, "..\\player\\containor.dll");
				HINSTANCE hDll = ::LoadLibraryExA(path, NULL, LOAD_WITH_ALTERED_SEARCH_PATH);
				InitPlayerContainor fInit = (InitPlayerContainor)::GetProcAddress(hDll, "InitContainor");
				fInit();

				return 0;
			}
			else {
				// 处理命令行，防止 "E:\text.rmvb" /sopenfrom 这样的命令行 "E:\text.rmvb" 会被系统默认干掉（右键添加到迅雷影音播放列表 shlext传过来的参数
				wchar_t wszExePath[MAX_PATH] = { 0 };
				::GetModuleFileNameW(NULL, wszExePath, MAX_PATH);
				::PathStripPathW(wszExePath);

				std::wstring wstrCmdLine;
				xl::text::transcode::ANSI_to_Unicode(lpCmdLine, strlen(lpCmdLine), wstrCmdLine);

				LPWSTR cmdLine = ::GetCommandLineW();
				if (cmdLine)
				{
					int nNumArgs = 0;
					LPWSTR* szArgList = ::CommandLineToArgvW(cmdLine, &nNumArgs);
					std::wstring strArg(szArgList[0]);
					if (strArg.find(wszExePath) == std::wstring::npos)
					{
						// 第一个参数没有本身exe名称
						wstrCmdLine = cmdLine;
					}
				}

				std::wstring wstrCmdToExecute;
				if (!TryPost2PreviousProcess(wstrCmdLine.c_str(), wstrCmdToExecute))
				{
					break;
				}

				// 单实例判断
				if (ThunderMutex::IsMutexExist(XMPXDASMUTEX))
				{
					break;
				}

				ThunderCore.ShellCommandManagerRegister();
				// 启动命令执行后退出
				if (!xlShellCommandManager.ExecuteCommand())
				{
					break;
				}

				xlShellCommandManager.PostCommandLine(wstrCmdToExecute.c_str());
			}
		}

		char electron_path[MAX_PATH] = { 0 };
		::GetModuleFileNameA(NULL, electron_path, MAX_PATH);
		::PathAppendA(electron_path, "..\\XDASKernel.dll");
		HINSTANCE hDll = ::LoadLibraryExA(electron_path, NULL, LOAD_WITH_ALTERED_SEARCH_PATH);

		FunRunMain fRunMain = (FunRunMain)::GetProcAddress(hDll, "RunMain");
		int err_value = ::GetLastError();
		//XdasNamespace::setCurrentAppUserModelID();
		if (nullptr != fRunMain)
		{
			fRunMain(hInstance, hPrevInstance, (char*)(lpCmdLine), nCmdShow);
		}
	} while (false);

	return 0;
}


#ifndef CLOSE_LOG
std::shared_ptr<spdlog::logger> g_xl_logger = nullptr;
#endif

#include <dwmapi.h>
#pragma comment(lib, "Shcore.lib")
int APIENTRY WinMain(_In_ HINSTANCE hInstance,
                     _In_opt_ HINSTANCE hPrevInstance,
                     _In_ LPSTR    lpCmdLine,
                     _In_ int       nCmdShow)
{
	/*BOOL b = false;
	DwmIsCompositionEnabled(&b);
	if (b) {
		MessageBox(0, "DwmIsCompositionEnabled", 0, 0);
	}
	else {
		MessageBox(0, "not DwmIsCompositionEnabled", 0, 0);
	}
	return 0;*/

	SetProcessDpiAwareness(PROCESS_SYSTEM_DPI_AWARE);
    char szExePath[MAX_PATH] = { 0 };
    GetModuleFileName(NULL, szExePath, MAX_PATH);
    char szDllPath[MAX_PATH] = { 0 };

    /*::PathCombine(szDllPath, szExePath, _T("..\\dk.dll"));
    HINSTANCE hDll2 = ::LoadLibraryA(szDllPath);

    ::PathCombine(szDllPath, szExePath, _T("..\\playercontrol.dll"));
    HINSTANCE hDll3 = ::LoadLibraryA(szDllPath);*/


    RunMain(hInstance, hPrevInstance, lpCmdLine, nCmdShow);
    return 0;
   
}
