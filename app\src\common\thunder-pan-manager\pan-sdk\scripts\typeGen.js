const sw2dts = require('sw2dts')
const axios = require('axios')
const fs = require('fs')

const swaggerJson = [
  'file', 'task', 'share', 'event'
]

async function convert (definition) {
  const res = await axios.get(`http://drive.office.k8s.xunlei.cn/apis/${definition}.swagger.json`)
  const name = definition.toUpperCase()
  const option = {
    namespace: `API_${name}`
  }
  sw2dts.convert(res.data, option).then(dts => {
    console.log(`OUTPUT: API_${name}.d.ts`)
    dts = dts.replace('declare namespace', 'export namespace')

    // 针对 API_EVENT 定制替换
    if (name === 'EVENT') {
      dts = dts.replace('reference_resource?: ProtobufAny', 'reference_resource?: ReferenceResource')
      dts = `import { ReferenceResource } from "..";\n\n` + dts
    }
    fs.writeFileSync(`./types/api/API_${name}.d.ts`, dts, 'utf-8')
  })
}

swaggerJson.forEach(def => {
  convert(def)
})
