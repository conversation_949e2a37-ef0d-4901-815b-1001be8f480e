<script lang="ts" setup>
import { SubtitleCategory, type SubtitleItemDisplayInfo } from '@root/common/player/base';
import { useElementVisibility } from '@vueuse/core';
import { sleep } from '../../utils/tools';
import { computed, nextTick, onMounted, useTemplateRef, watch } from 'vue';

const props = defineProps<{
  idx: number
  item: SubtitleItemDisplayInfo
  activeItem?: SubtitleItemDisplayInfo | null
}>()

const isActive_computed = computed(() => props.activeItem?.id === props.item.id)

const $item = useTemplateRef('$item')
const visible_ref = useElementVisibility($item)

async function scrollIntoView() {
  await sleep(20)
  if (isActive_computed.value && !visible_ref.value) {
    $item.value?.scrollIntoView()
  }
}

onMounted(() => {
  scrollIntoView()
})

defineExpose({
  scrollIntoView
})
</script>

<template>
  <div v-bind="$attrs" ref="$item" class="subtitles-item" :class="{ 'popover-item-active': isActive_computed }"
    :title="item.displayName">
    <!-- 内挂字幕自带序号 -->
    <template v-if="item.category !== SubtitleCategory.Embed">
      {{ idx + 1 }}.
    </template>
    {{ item.displayName }}
  </div>
</template>

<style lang="scss" scoped>
@import '../xmp-player-mixin.scss';

.subtitles-item {
  @include Controls-Popover-Item();

  display: block;
  line-height: 40px;

  @include truncate();
}
</style>