import { type } from "node:os"

declare namespace ThunderClientAPI {
    namespace dataStruct {
        namespace common {
            interface ErrorInfo {
                result: number
                message: string
            }
            // 暂定
            type SortOrderType = 'asc' | 'desc' | 'default' | 'none'
            type SortKeyType = 'name' | 'size' | 'time'
            interface SortOrder {
                sortOrderKey: SortKeyType // 排序的key, eg: "name", "size", "time"
                sortOrderType: SortOrderType // 排序的方式, eg: "asc", "desc"
            }
            interface ListFilter {
                filterKey: string // 过滤的key, eg: "name", "size", "time"
                filterValue: string // 过滤的值, eg: "xxx", ">100", "<100"
            }
            interface Range {
                beginIndex: number
                count: number
            }
            interface PageRange {
                range: Range
                sortOrder?: SortOrder // 请求列表是需要指定的排序方式
                listFilter?: ListFilter // 过滤条件
            }
            type DataSourceType = 'ALL_LINKS' | 'SUB_FILE_LIST' | 'LABEL' 
            interface DataSourceConfig {
                dataSrcType: DataSourceType

                // ALL_LINKS类型则不需要指定parentLinkId
                // 如果是SUB_FILE_LIST类型，dataSrcName则填parentLinkId
                // 如果是LABEL类型，则dataSrcName填label标签key（待定）
                dataSrcName?: string
            }
            interface FetchFirstPageParam {
                dataSrcConfig?: DataSourceConfig // 缺省值时为获取全部链接
                firstLoadCount: number
                reload: boolean
            }
            interface FetchFirstPageResult {
                dataSrcConfig: DataSourceConfig
                totalCount: number
                hasLoadFinished: boolean
                error: common.ErrorInfo
            }

            interface FetchNextPageParam {
                dataSrcConfig?: DataSourceConfig // 缺省值时为获取全部链接
                loadCount: number
                reload: boolean
            }
            interface FetchNextPageResult {
                dataSrcConfig: DataSourceConfig
                hasLoadFinished: boolean
                error: common.ErrorInfo
            }

        }

        namespace dataModals {
            type MediaType = 'MEDIA_UNKNOWN' | 'MEDIA_VIDEO' | 'MEDIA_AUDIO' | 'MEDIA_IMAGE' | 'MEDIA_ARCHIVE' | 'MEDIA_TEXT' | 'MEDIA_INSTALLER' | 'MEDIA_FONT' | 'MEDIA_SUBTITLE'
            type UrlType = 'magnet' | 'http' | 'ftp' | 'ed2k' | 'thunder'
            type AuditState = 'AUDIT_UNKNOWN' | 'AUDIT_ACCEPTED' | 'AUDIT_REJECTED'
            type ActionType = 'ACTION_UNKNOWN' | 'ACTION_DOWNLOAD' | 'ACTION_CLOUD_DOWNLOAD' | 'ACTION_TODO' | 'ACTION_PLAY'
            interface AuditInfo {
                status: AuditState,
                message: string,
            }
            interface LinkRecord {
                url_hash: string
                url: string
                id?: string // 服务端索引id
                url_type: UrlType
                parentid: string
                file_index: number
                status: string
                gcid: string
                name: string
                child_count: number
                selected_count: number
                files_size: number
                size: number // int64_t 映射为 number
                is_dir: boolean
                add_time: number // int64_t 映射为 number，产品认为的添加时间
                action: string
                icon_link: string
                thumbnail_link: string
                media_type: MediaType
                file_ext: string
                audit: AuditInfo
                params: string

                // 本地缓存扩展字段
                can_video_play: boolean
                creation_time: number // int64_t
                last_modified_time: number // int64_t
                cloudfile_add_time: number // int64_t
                last_videoplay_time: number // int64_t
                download_time: number // int64_t
                file_restore_time: number // int64_t
                cloudfile_path: string
                localfile_path: string
                audit_state: AuditState
                audit_msg: string
                sync_state: number // 当前记录的同步状态，本地缓存，还是已经从服务端会不回来
            }
            interface LinkInfo {
                url: string
                url_hash?: string // 修改Link时填写，如果不填写则自动生成
                url_type: UrlType
                name: string // 链接UI显示名称
                gcid: string
                size: number // 对应资源文件总大小
                create_time: number // int64_t 映射为 number
                file_ext: string // 文件扩展名
                action: ActionType // 来源: ACTION_DOWNLOAD: 下载, ACTION_CLOUD_DOWNLOAD: 云添加, ACTION_TODO: 稍后, ACTION_PLAY: 播放
                local_saved_path: string // 文件保存路径

                // 包含子文件夹的链接信息
                is_folder: boolean
                total_count: number // 子文件总数量
                selected_count: number // 已选中的子文件数量
                selected_size: number // 已选中的子文件总大小
                fileList: FileItem[]
                filesAppended?: FileItem[] // 修改时，添加的子文件
                filesRemovedIndexs?: string[] // 修改时，删除的子文件索引列表
            }

            type LabelType = 'LABEL_UNKNOWN' | 'LABEL_LINK_STAT' | 'LABEL_LINK'
            interface LabelInfo {
                key: string
                name: string
                value: string
                type: LabelType
            }
            interface LabelList {
                labels: LabelInfo[]
            }
            interface FileItem {
                url_hash: string
                file_index: number // BT文件索引
                file_ext: string // 文件扩展名
                file_name: string // 文件UI显示名称
                file_path: string // BT文件路径
                local_saved_path: string
                gcid: string
                file_size: number
            }

            interface SyncLinksToServerParam {
                userData: string
            }
            interface SyncLinksToServerResult {
                userData: string
                syncTaskId: string
                error: common.ErrorInfo
            }
            interface GetLabelsParam {
                ignoreEvent?: boolean
            }
            interface GetLabelsResult {
                labels?: LabelList
                error: common.ErrorInfo
            }

            interface LoadLinkRecordsParam {
                rangeInfo: common.PageRange
                dataSourceConfig?: DataSourceConfig // 缺省值时为获取全部链接
                reload: boolean
            }
            interface LoadLinkRecordsResult {
                dataSourceConfig: DataSourceConfig
                rangeInfo: common.PageRange
                records?: { links: LinkRecord[] },
                error: common.ErrorInfo
            }
            interface GetLinkRecordsParam {
                urls?: string[] // 需要获取的链接列表
                linkIds?: string[] // 需要获取的链接ID列表
                userData: string
                reload: boolean
            }
            interface GetLinkRecordsResult {
                records?: { links: LinkRecord[] },
                userData: string
                error: common.ErrorInfo
            }

            interface InsertLinkRecordParam {
                link: LinkInfo
                ignoreEvent?: boolean
            }
            interface InsertLinkRecordResult {
                linkId: string
                error: common.ErrorInfo
            }

            interface RemoveLinkRecordsParam {
                linkIds: string[]
                ignoreEvent?: boolean
            }
            interface RemoveLinkRecordsResult {
                error: common.ErrorInfo
            }

            interface GetAssociateCloudFileParam {
                url: string
                file_index?: number // 如果存在表示url是一个磁力
            }
            interface GetAssociateCloudFileResult {
                drive_file_info: Map<string, string> // key: file_index, value: cloud_file_id
                error: common.ErrorInfo
            }

        }
        namespace event {

            interface LinkHubEventDetail_DataSouceChanged {
                reason: string
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_TotalCountChanged {
                dataSrcInfo: Common.DataSourceConfig
                totalCount: number
                hasFinished: boolean
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_SyncToServerStatus {
                syncTaskId: string
                taskCount: number
                syncedCount: number
                failedCount: number
                hasFinished: number
                errInfo: common.ErrorInfo
            }
            enum TriggerType {
                LOCAL = 0,
                SERVER = 1,
                UNKNOWN = 2,
            }
            interface LinkRecordEventDetail_RecordAdded {
                linkId: string
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_RecordUpdated {
                linkId: string
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_RecordRemoved {
                linkId: string
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_LabelInfoChanged {
                labels: dataModals.LabelInfo[]
                errInfo: common.ErrorInfo
            }



        }
    }
    namespace auth {
        interface UserAccountInfo {
            userId: string
            result: boolean
            error: dataStruct.common.ErrorInfo
        }
        interface IVerifyAccountResult {
            userId: string
            result: boolean
            error: dataStruct.common.ErrorInfo
        }
    }
    namespace app {
        interface IThunderClient {
            getClientId(): string
            getBizProvider(): biz.IBizProvider
            getEventDispatcher(): base.IEventDispatcher
        }
    }

    namespace appConfig {
        interface IDevConfig {
            getItem(key: string): string
            setItem(key: string, value: string): void
        }
    }
    namespace base {
        interface Event<T extends keyof EventDetails> {
            /** 事件类型 */
            type: T

            /** 事件内容 */
            detail: EventDetails[T]
        }

        interface EventDetails {
            LinkHubEvent_DataSouceChanged: dataStruct.event.LinkHubEventDetail_DataSouceChanged
            LinkRecordEvent_TotalCountChanged: dataStruct.event.LinkRecordEventDetail_TotalCountChanged
            LinkRecordEvent_SyncToServerStatus: dataStruct.event.LinkRecordEventDetail_SyncToServerStatus
            LinkRecordEvent_LabelInfoChanged: dataStruct.event.LinkRecordEventDetail_LabelInfoChanged
            LinkRecordEvent_RecordAdded: dataStruct.event.LinkRecordEventDetail_RecordAdded
            LinkRecordEvent_RecordUpdated: dataStruct.event.LinkRecordEventDetail_RecordUpdated
            LinkRecordEvent_RecordRemoved: dataStruct.event.LinkRecordEventDetail_RecordRemoved
        }


        interface IEventDispatcher {
            attachEvent<T extends keyof ThunderClientAPI.base.EventDetails>(event: T, listener: any): void;
            detachEvent<T extends keyof ThunderClientAPI.base.EventDetails>(event: T, listener: any): void;
        }

    }

    namespace biz {
        interface ILinkHubDataManager {
            // 同步进度信息通过监听事件过去
            syncLinksToServer(param: dataStruct.dataModals.SyncLinksToServerParam): Promise<dataStruct.dataModals.SyncLinksToServerResult>

            // 获取标签信息
            getLabels(param: dataStruct.dataModals.GetLabelsParam): Promise<dataStruct.dataModals.GetLabelsResult>
            
            // 入库链接记录
            saveLink(param: dataStruct.dataModals.InsertLinkRecordParam): Promise<dataStruct.dataModals.InsertLinkRecordResult>
            // 根据条件查询链接记录，建议直接使用loadLinkRecords返回的数据；
            getLinks(param: dataStruct.dataModals.GetLinkRecordsParam): Promise<dataStruct.dataModals.GetLinkRecordsResult>

            // 分页加载链接记录（包括分页加载子目录、相应标签分类下的link），参数会微调
            loadLinkRecords(param: dataStruct.dataModals.LoadLinkRecordsParam): Promise<dataStruct.dataModals.LoadLinkRecordsResult>
            // 通过linkId修改已有入库链接信息
            updateLinkRecord(param: dataStruct.dataModals.UpdateLinkRecordParam): Promise<dataStruct.dataModals.UpdateLinkRecordResult>
            removeLinkRecords(param: dataStruct.dataModals.RemoveLinkRecordsParam): Promise<dataStruct.dataModals.RemoveLinkRecordsResult>

            // 通过url+index获取云盘对应文件信息
            getAssociateCloudFile(param: dataStruct.dataModals.GetAssociateCloudFileParam): Promise<dataStruct.dataModals.GetAssociateCloudFileResult>

            // fetch接口内部会自动调用(LoadLinkRecordsParam的reload参数为true会调用fetch)，通常不需要主动调用，只有特殊场景时使用：
            // 1.通常是主动强制刷新数据
            // 2.如果需要一次性拉取全部云端文件，使用fetchNextPage，loadCount填-1
            // 3.如果一个页面需要预加载，可以调用fetchFirstPage
            fetchFirstPage(param: dataStruct.common.FetchFirstPageParam): Promise<dataStruct.common.FetchFirstPageResult>
            fetchNextPage(param: dataStruct.common.FetchNextPageParam): Promise<dataStruct.common.FetchNextPageResult>

        }
        interface IBizProvider {
            getLinkHubDataManager(): ILinkHubDataManager
        }
    }

}