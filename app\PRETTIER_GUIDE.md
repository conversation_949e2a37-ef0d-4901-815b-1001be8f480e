# Prettier 配置指南

## 项目中的 Prettier 配置

本项目已配置了 Prettier 来统一代码格式，支持以下文件类型：
- TypeScript (.ts, .tsx)
- JavaScript (.js, .jsx)
- Vue (.vue)
- CSS (.css)
- SCSS (.scss)
- JSON (.json)
- Markdown (.md)

## 配置特点

### 主要格式化规则 (.prettierrc)
- 使用单引号：`singleQuote: true`
- 行宽：100字符
- 不使用分号：`semi: false`
- 尾随逗号：ES5格式
- 箭头函数参数不加括号：`arrowParens: "avoid"`
- 行结尾：LF
- Vue文件缩进script和style标签

### Import 排序
使用 `@trivago/prettier-plugin-sort-imports` 插件自动排序import语句：
1. Electron相关包
2. Rsbuild相关包
3. VueUse相关包
4. 项目内部包（@xbase、@xunlei）
5. Vue相关包
6. 第三方包
7. 项目内部模块（@/、@root/）
8. 相对路径导入

### CSS属性排序
使用 `prettier-plugin-css-order` 插件按SMACSS方法论排序CSS属性。

## 使用方法

### 1. 手动格式化
```bash
# 格式化所有支持的文件
npm run format:prettier

# 检查文件格式是否符合规范（不修改文件）
npm run format:prettier:check

# 列出不符合格式规范的文件
npm run lint:prettier
```

### 2. VS Code 集成
项目已配置 `.vscode/settings.json`，启用了：
- 保存时自动格式化
- 默认使用 Prettier 作为格式化工具
- 针对不同文件类型的格式化器配置

确保安装了 "Prettier - Code formatter" 扩展。

### 3. 格式化特定文件类型
```bash
# 只格式化 Vue 文件
npx prettier --write "src/**/*.vue"

# 只格式化 TypeScript 文件
npx prettier --write "src/**/*.{ts,tsx}"

# 只格式化样式文件
npx prettier --write "src/**/*.{css,scss}"
```

## 忽略文件

`.prettierignore` 文件配置了需要忽略的文件和目录：
- 构建输出目录 (dist/, build/, output/)
- 依赖目录 (node_modules/)
- 生成的类型文件 (*.d.ts)
- 二进制文件和资源文件
- 特定业务目录 (thunder-pan 文件夹)

## 团队协作建议

1. **编辑器配置**：团队成员都应该配置编辑器支持 Prettier
2. **提交前检查**：建议在提交代码前运行 `npm run format:prettier:check`
3. **CI/CD 集成**：可以在持续集成中添加格式检查步骤
4. **代码审查**：如果发现格式问题，可以要求先运行格式化再提交

## 与现有 Biome 的关系

项目中同时存在 Biome 和 Prettier 配置：
- Biome：`npm run format` 
- Prettier：`npm run format:prettier`

团队可以选择统一使用其中一种，建议选择 Prettier 因为它对 Vue 和复杂项目的支持更好。

## 常见问题

### Q: 格式化后代码看起来奇怪？
A: 检查是否有语法错误，Prettier 无法格式化有语法错误的文件。

### Q: 某些文件没有被格式化？
A: 检查文件是否在 `.prettierignore` 中被忽略，或者文件扩展名是否在支持列表中。

### Q: Import 排序不正确？
A: 确保 import 语句语法正确，插件会根据配置的 `importOrder` 规则进行排序。 