#ifndef XMP_ADMIN_SHELL_ADMIN_SHELL_RECV_H
#define XMP_ADMIN_SHELL_ADMIN_SHELL_RECV_H

#include "./base.h"

class AdminShellRecv
{
public:
	AdminShellRecv();
	~AdminShellRecv();

public:
	static AdminShellRecv *GetInstance();
	bool InitAdminShell(const std::string &cmd_line);
	void SetCommandCallback(std::function<void(const std::string&)>&& func);
	void OnShellCommand();

public:
	LRESULT OnCopyData(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/);

private:
	static LRESULT WINAPI WndProc(HWND hWnd, UINT unMsg, WPARAM wParam, LPARAM lParam);
	bool ParseBootShellCmdLine(const std::string &cmd_line);
private:
	bool is_init_{ false };
	HANDLE parent_event_{ NULL };
	DWORD parent_process_id_{ 0 };
	std::string cmd_line_;
	BOOL is_command_callback_setted_{ false };


	std::function<void(const std::string&)> m_cmdCb;
};

#endif //XMP_ADMIN_SHELL_ADMIN_SHELL_SEND_H
