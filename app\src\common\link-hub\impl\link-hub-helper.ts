/// <reference path="../impl/thunder-client-api.d.ts" />

import { getThunderClient } from '../impl/thunder-client'

export class Link<PERSON><PERSON><PERSON><PERSON>per implements ThunderClientAPI.biz.ILinkHubPresenter {
    private static instance: LinkHubHelper | null = null;
    private constructor() {
    }
    public static getInstance(): Link<PERSON><PERSON>Helper {
        if (!LinkHubHelper.instance) {
            if (global.LinkHubHelperClientInstance) {
                LinkHubHelper.instance = global.LinkHubHelperClientInstance;
            } else {
                LinkHubHelper.instance = new LinkHubHelper();
                global.LinkHubHelperClientInstance = LinkHubHelper.instance;
            }
        }
        return LinkHubHelper.instance!;
    }
    public syncLinksToServer(param: ThunderClientAPI.dataStruct.dataModals.SyncLinksToServerParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.SyncLinksToServerResult> {
            return getThunderClient().getBizProvider().getLinkHubPresenter().syncLinksToServer(JSON.stringify(param));
    }
    public async getLinks(param: ThunderClientAPI.dataStruct.dataModals.GetLinkRecordsParam)
    : Promise<ThunderClientAPI.dataStruct.dataModals.GetLinkRecordsResult> {
        return getThunderClient().getBizProvider().getLinkHubPresenter().getLinks(JSON.stringify(param));
    }
    public async searchLinks(param: ThunderClientAPI.dataStruct.dataModals.SearchLinkRecordsParam)
    : Promise<ThunderClientAPI.dataStruct.dataModals.SearchLinkRecordsResult> {
        return getThunderClient().getBizProvider().getLinkHubPresenter().searchLinks(JSON.stringify(param));
    }
    public async saveLink(param: ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordResult> {
        return getThunderClient().getBizProvider().getLinkHubPresenter().saveLink(JSON.stringify(param));
    }
    public async saveLinks(param: ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordsResult> {
        return getThunderClient().getBizProvider().getLinkHubPresenter().saveLinks(JSON.stringify(param));
    }
    async savePlaybackRecord(param: ThunderClientAPI.dataStruct.dataModals.InsertPlaybackRecordParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.InsertPlaybackRecordResult> {
        return getThunderClient().getBizProvider().getLinkHubPresenter().savePlaybackRecord(JSON.stringify(param));
    }
    async getPlaybackRecords(param: ThunderClientAPI.dataStruct.dataModals.GetPlaybackRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.GetPlaybackRecordsResult> {
        return getThunderClient().getBizProvider().getLinkHubPresenter().getPlaybackRecords(JSON.stringify(param));
    }
    async getAssociateCloudFile(param: ThunderClientAPI.dataStruct.dataModals.GetAssociateCloudFileParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.GetAssociateCloudFileResult> {
        return getThunderClient().getBizProvider().getLinkHubPresenter().getAssociateCloudFile(JSON.stringify(param));
    }
}