#include "stdafx.h"
#include "tbc_string.h"

#include <codecvt>
#include <cstring>
#include <utility>

TbcString::TbcString()
{
    data_ = new wchar_t[1];
    data_[0] = L'\0';
}

TbcString::TbcString(const TbcString & other)
{
    wchar_t* str = other.data_;
    if (str == nullptr)
    {
        str = L"";
    }

    size_t size = wcslen(str) + 1;
    data_ = new wchar_t[size];
    wcscpy_s(data_, size, str);
    data_[size - 1] = L'\0';
}

TbcString::TbcString(const wchar_t * str)
{
    if (str == nullptr)
    {
        str = L"";
    }

    size_t size = wcslen(str) + 1;
    data_ = new wchar_t[size];
    wcscpy_s(data_, size, str);
    data_[size - 1] = L'\0';
}

TbcString::TbcString(const std::wstring& str)
{
    size_t size = str.length() + 1;
    data_ = new wchar_t[size];
    wcscpy_s(data_, size, str.c_str());
    data_[size - 1] = L'\0';
}

TbcString::~TbcString()
{
    if (data_ != nullptr)
    {
        delete[] data_;
    }
}

TbcString & TbcString::operator=(const TbcString& other)
{
    if (this == &other)
    {
        return *this;
    }

    return *this = other.data_;
}

TbcString& TbcString::operator=(const wchar_t* str)
{
    do 
    {
        if (str == data_)
        {
            break;
        }

        if (str == nullptr)
        {
            str = L"";
        }

        if (data_ != nullptr)
        {
            delete[] data_;
        }

        size_t size = wcslen(str) + 1;
        data_ = new wchar_t[size];
        wcscpy_s(data_, size, str);
        data_[size - 1] = L'\0';

    } while (false);

    return *this;
}

TbcString& TbcString::operator=(const std::wstring& str)
{
    return *this = str.c_str();
}

TbcString& TbcString::operator+=(const TbcString& other)
{
    return *this += other.data_;
}

TbcString& TbcString::operator+=(const wchar_t* str)
{
    do 
    {
        if (str == nullptr)
        {
            break;
        }

        size_t size_new = wcslen(str);
        if (size_new == 0)
        {
            break;
        }

        wchar_t* old_str = data_;

        size_t size_old = 0;
        if (old_str != nullptr)
        {
            size_old = wcslen(old_str);
        }

        size_t total_size = size_new + size_old + 1;
        data_ = new wchar_t[total_size];

        if (old_str != nullptr)
        {
            wcscpy_s(data_, total_size, old_str);

            delete[] old_str;
        }

        wcscpy_s(data_ + size_old, total_size - size_old, str);
        data_[total_size - 1] = L'\0';

    } while (false);

    return *this;
}

TbcString& TbcString::operator+=(const std::wstring& str)
{
    return *this += str.c_str();
}

bool TbcString::operator==(const TbcString & other) const
{
    if (this == &other)
    {
        return true;
    }

    return *this == other.data_;
}

bool TbcString::operator==(const wchar_t* str) const
{
    int result = wcscmp(data_, str);
    return result == 0;
}

bool TbcString::operator==(const std::wstring& str) const
{
    return *this == str.c_str();
}

bool TbcString::operator!=(const TbcString& other) const
{
    if (this == &other)
    {
        return false;
    }

    return *this != other.data_;
}

bool TbcString::operator!=(const wchar_t* str) const
{
    if (data_ == nullptr)
    {
        return false;
    }

    if (str == nullptr)
    {
        return false;
    }

    int result = wcscmp(data_, str);

    return result != 0;
}

bool TbcString::operator!=(const std::wstring& str) const
{
    return *this != str.c_str();
}

bool TbcString::Empty() const
{
    return Length() == 0;
}

int TbcString::Length() const
{
    int length = 0;
    if (data_ != nullptr)
    {
        length = wcslen(data_);
    }
    return length;
}

const wchar_t * TbcString::c_str() const
{
    return data_;
}

std::string TbcString::ToString() const
{
    if (data_ == nullptr || wcscmp(data_, L"") == 0)
    {
        return std::string("");
    }

    std::wstring_convert< std::codecvt_utf8<wchar_t> > convert_utf8;

    return convert_utf8.to_bytes(data_);
}
