<script setup lang="ts">
import ContextMenu from '@root/common/components/ui/context-menu/index.vue'
import { ref, computed } from 'vue'

// 选中项的状态管理
const selectedItems = ref<Set<number>>(new Set())

// 切换选中状态
const toggleSelect = (index: number, event: MouseEvent) => {
  // 如果按住 Ctrl 键，则进行多选
  if (event.ctrlKey) {
    if (selectedItems.value.has(index)) {
      selectedItems.value.delete(index)
    } else {
      selectedItems.value.add(index)
    }
  } else {
    // 单选模式

    if (event.button === 2 && selectedItems.value.size > 1) {
      return
    }

    selectedItems.value.clear()
    selectedItems.value.add(index)
  }
}

// 根据选中状态生成不同的菜单项
const contextMenuList = computed(() => {
  const isMultiSelect = selectedItems.value.size > 1

  if (isMultiSelect) {
    // 多选菜单
    return [
      {
        key: 'batch-play',
        label: '批量播放',
        icon: 'xl-icon-general-play-m1',
      },
      {
        key: 'batch-download',
        label: '批量下载',
        icon: 'xl-icon-download-l',
      },
      {
        key: 'batch-delete',
        label: '批量删除',
        icon: 'xl-icon-delete',
        hasSeparator: true,
      },
    ]
  } else {
    // 单选菜单
    return [
      {
        key: 'play',
        label: '播放',
        icon: 'xl-icon-general-play-m1',
      },
      {
        key: 'download',
        label: '下载',
        icon: 'xl-icon-download-l',
      },
      {
        key: 'local',
        label: '前往下载位置',
        children: [
          {
            key: 'local',
            label: '本地目录',
          },
          {
            key: 'cloud',
            label: '云盘目录',
          },
        ],
      },
      {
        key: 'rename',
        label: '重命名(R)',
      },
      {
        key: 'copy',
        label: '复制链接(C)',
      },
      {
        key: 'delete',
        label: '删除链接(D)',
        icon: 'xl-icon-delete',
        hasSeparator: true,
      },
    ]
  }
})

const handleSelect = (key: string) => {
  const selectedIndices = Array.from(selectedItems.value)
  console.log('选中的项:', selectedIndices)
  console.log('菜单操作:', key)
}

</script>

<template>
  <h1>ContextMenu</h1>

  <div style="display: flex; flex-direction: column; gap: 10px; align-items: center; margin-bottom: 10px;">
    <ContextMenu :items="contextMenuList" @select="handleSelect" v-for="(_, index) in 3" :key="index"
      :disabled="index === 1">
      <div @click="(e) => toggleSelect(index, e)" @click.right="(e) => toggleSelect(index, e)"
        style="width: 600px; height: 30px; border: 1px dashed #000; display: flex; flex-direction: column; align-items: center; justify-content: center;">
        <div style="width: 100%; height: 50px; background-color: red; border: 1px solid blue; cursor: pointer;"
          :style="{ backgroundColor: selectedItems.has(index) ? '#ff9999' : 'red' }"> item {{ index + 1 }} {{ index ===
            1 ? '右键不能点击' : '' }} <span v-if="selectedItems.has(index)" style="margin-left: 8px; color: white;">✓</span>
        </div>
      </div>
    </ContextMenu>

  </div>

  <div style="margin-top: 20px; color: #666;">
    <p>提示：</p>
    <ul>
      <li>点击项目可以选中/取消选中</li>
      <li>按住 Ctrl 键点击可以进行多选</li>
      <li>右键点击会显示对应的菜单（单选/多选）</li>
    </ul>
  </div>
</template>