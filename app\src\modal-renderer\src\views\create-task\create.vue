<script setup lang="ts">
// ===== 第三方库 =====
import { onMounted, ref, watch, computed, onUnmounted,provide } from 'vue'
import { storeToRefs } from 'pinia'
import { useEventListener } from '@vueuse/core'
import { DkHelper } from '@root/common/task/impl/dk-helper'

// ===== Vue 组件 =====
import AutoCreateTaskComponent from '@root/modal-renderer/src/views/create-task/auto-create.vue'
import { NewEditableTask } from '@root/modal-renderer/src/components/new-task/new-editable-task'

// ===== 基础类型定义 =====
import * as BaseType from '@root/common/task/base'

// ===== UI 组件和工具 =====
//@ts-ignore
import { useAlertDialog } from '@root/common/components/ui/Dialog/useAlertDialog.js'
import XMPMessage from '@root/common/components/ui/message/index'

// ===== 任务管理相关 =====
import { TaskManager } from '@root/common/task/client/task-manager'
import { FileOperationHelper } from '@root/common/helper/file-operation-helper'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'

// ===== 配置管理 =====
import { DownloadPathNS } from '@root/common/config/download-path'
import { config } from '@root/common/config/config'

// ===== 云盘相关 =====
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'

// ===== 弹窗相关 =====
import { PopUpNS } from '@root/common/pop-up'
import * as PopUpTypes from '@root/common/pop-up/types'

// ===== 工具函数 =====
import {
  isFtpUrl,
  parseMultilineUrls,
  extractFileNameFromUrl as extractFileNameFromUrlUtil,
} from '@root/modal-renderer/src/utils.help'
import { ThunderHelper } from '@root/common/thunder-helper'
import {
  LinkSaver,
} from '@root/modal-renderer/src/utils/link-saver'
import { LinkSaveScene, type LinkSaveOptions } from '@root/modal-renderer/types/link-saver.type'

// ===== 常量定义 =====
import { MAX_MAGNET_FILE_COUNT } from '@root/modal-renderer/src/utils/constants'
import { parseThunderUrlIfNeeded } from '@root/modal-renderer/src/utils/new-task-util'

// ===== 类型定义 =====
import {
  type TaskSettings,
  type INewTaskDataItem,
  type IUrlWithType,
  type DownloadEventParams,
  DownloadPathType,
  type TaskError,
} from '@root/modal-renderer/types/new-task.type'

// ===== Pinia Stores =====
import { useNewTaskConfigStore } from '@root/modal-renderer/src/stores/new-task-config'
import { useNewTaskStore } from '@root/modal-renderer/src/stores/new-task'
import { useTaskCreationStore } from '@root/modal-renderer/src/stores/task-creation'
import { useDownloadPathStore } from '@root/modal-renderer/src/stores/download-path'
import { useTaskSettingStore } from '@root/modal-renderer/src/stores/task-setting'
import { useUserStore } from '@root/modal-renderer/src/stores/user'
import { useDownloadCloudPathStore } from '@root/modal-renderer/src/stores/download-cloud-path'

// ===== 组合式函数 =====
import { usePositionMixinComponent } from '@root/modal-renderer/src/common/mixins'
import {
  provideDownloadHandler,
  provideCloseWindowHandler,
} from '@root/modal-renderer/src/composables/useTaskDownload'
import { useAuth } from '@root/modal-renderer/src/composables/useAuth'

// ===== 基础组合式函数和工具 =====
const alertDialog = useAlertDialog()
const { requireAuth, initializeAuth, cleanupAuth, showLoginDialog } = useAuth()

// ===== Props 和 Emits 定义 =====
interface Props {
  options?:
    | ThunderNewTaskHelperNS.IShowNewTaskWindowOptions
    | (ThunderNewTaskHelperNS.IShowPreNewTaskWindowOptions & {
        taskData?: INewTaskDataItem[]
      })
}

const props = withDefaults(defineProps<Props>(), {
  options: () => ({}),
})

const emit = defineEmits(['close', 'confirm', 'cancel'])

// ===== 位置和窗口相关 =====
const { overridePosition, resizeToFitContent } = usePositionMixinComponent()
const defaultPositionOptions = {
  autoSize: true,
  show: false,
  windowWidth: 680,
  windowHeight: 316,
  relatePos: PopUpTypes.RelatePosType.CenterParent,
}

// ===== Pinia Stores =====
const newTaskConfigStore = useNewTaskConfigStore()
const newTaskStore = useNewTaskStore()
const taskCreationStore = useTaskCreationStore()
const downloadPathStore = useDownloadPathStore()
const taskSettingStore = useTaskSettingStore()
const downloadCloudPathStore = useDownloadCloudPathStore()
const userStore = useUserStore()

// ===== Store 方法解构 =====
const {
  handleWindowBlur,
  handleWindowFocus,
  handleVisibilityChange,
  setAllUrlsWithType,
  setAllUrls,
  setDataMap,
  getDataMapItem,
  setUrlExtraDataMap,
  getUrlExtraDataMapItem,
  setCheckedFileIndexes,
  getCheckedFileIndexedByUrl,
  setOptionsExtData,
  setParsedTaskData,
  setIsLoading,
  setIsDiskSpaceInsufficient,
  setShowDuplicateTaskDialog,
  clearAllTaskStates,
} = newTaskStore

// ===== Store 响应式状态 =====
const {
  isWindowBlurred,
  allUrlsWithType,
  dataMap,
  urlExtraDataMap,
  checkedFileIndexes,
  optionsExtData,
  isLoading,
  isDiskSpaceInsufficient,
} = storeToRefs(newTaskStore)

const { isMergeTask, freeCloudAddCount } = storeToRefs(newTaskConfigStore)

// ===== 计算属性 =====
const isAutoCreateTask = computed(() => {
  return (
    props.options &&
    'taskData' in props.options &&
    props.options.taskData &&
    props.options.taskData.length > 0
  )
})

const taskDataFromOptions = computed(() => {
  if (
    props.options &&
    'taskData' in props.options &&
    props.options.taskData &&
    Array.isArray(props.options.taskData)
  ) {
    console.log(
      '[AutoCreateTaskView] Found taskData array with length:',
      props.options.taskData.length
    )
    return props.options.taskData
  }

  console.log('[AutoCreateTaskView] No valid taskData found, returning empty array')
  return []
})

// 使用 store 中的 getTaskSavePath
const taskSavePath = computed(() => {
  return newTaskStore.getTaskSavePath()
})

// ===== 响应式数据 =====
const hasShownLoginDialog = ref(false)

// ===== 工具函数 =====
// getDuplicateTasksDownloadDetails 函数已移动到 task-creation.ts store 中

/**
 * 处理单个URL，获取其真实URL和任务类型
 * 参考 create-task/index.vue 中 handleInputChangeDelay 函数的URL处理逻辑
 * @param url 原始URL
 * @returns 包含真实URL和任务类型的对象
 */
async function processUrlWithType(url: string): Promise<IUrlWithType> {
  try {
    // 首先尝试解析Thunder链接（如果是的话）
    const realUrl = await parseThunderUrlIfNeeded(url)

    // 然后获取真实URL的任务类型
    const taskType = await DkHelper.getTaskTypeFromUrl(realUrl)

    console.log('[AutoCreateTaskView] URL处理完成:', { originalUrl: url, realUrl, taskType })
    return { url: realUrl, taskType: taskType }
  } catch (error) {
    console.error('[AutoCreateTaskView] 处理URL失败:', url, error)
    // 如果处理失败，默认为P2sp类型
    return { url, taskType: BaseType.TaskType.P2sp }
  }
}

/**
 * 从taskData中提取所有URLs并获取其类型
 * 遍历taskData，提取每个任务的URL，并通过DkHelper获取任务类型
 */
async function extractUrlsFromTaskData() {
  console.log('[AutoCreateTaskView] extractUrlsFromTaskData called')

  const taskData = taskDataFromOptions.value

  if (!taskData || taskData.length === 0) {
    console.log('[AutoCreateTaskView] No task data to extract URLs from')
    setAllUrls([])
    await setAllUrlsWithType([])
    return
  }

  console.log('[AutoCreateTaskView] Extracting URLs from task data:', taskData)

  // 第一步：提取所有URLs
  const extractedUrls: string[] = []

  taskData.forEach((task, index) => {
    console.log(`[AutoCreateTaskView] Processing task ${index} for URL extraction:`, task)

    // 从任务中提取URL
    let taskUrl = ''
    if (task.url) {
      taskUrl = task.url
    } else if (task.fileName && task.fileName.startsWith('http')) {
      // 有些情况下URL可能存储在fileName字段中
      taskUrl = task.fileName
    }

    if (taskUrl) {
      extractedUrls.push(taskUrl)
      console.log(`[AutoCreateTaskView] Extracted URL from task ${index}:`, taskUrl)
    } else {
      console.warn(`[AutoCreateTaskView] No URL found in task ${index}:`, task)
    }
  })

  // 临时存储提取的URLs，稍后在去重合并后统一更新allUrls
  console.log('[AutoCreateTaskView] All extracted URLs:', extractedUrls)

  // 第二步：异步处理URL，获取真实URL和类型
  if (extractedUrls.length > 0) {
    try {
      console.log(
        '[AutoCreateTaskView] Starting URL type detection for',
        extractedUrls.length,
        'URLs'
      )

      const urlsWithTypePromises = extractedUrls.map(async (url, index) => {
        console.log(`[AutoCreateTaskView] Processing URL ${index}:`, url)
        return await processUrlWithType(url)
      })

      const processedUrlsWithType = await Promise.all(urlsWithTypePromises)

      // 🔥 过滤掉taskType为Unknown的URL
      const newUrlsWithType = processedUrlsWithType.filter(item => {
        if (item.taskType === BaseType.TaskType.Unkown) {
          console.warn('过滤掉未知类型的URL:', item.url)
          return false
        }
        return true
      })

      // 🔥 直接使用新输入的URL数组，完全替换旧数据，不进行合并
      const mergedUrlsWithType = newUrlsWithType

      // 🔥 解析完成后，统一更新状态
      // 从合并后的数组中提取URL，保持与allUrlsWithType的同步
      setAllUrls(mergedUrlsWithType.map(item => item.url))
      await setAllUrlsWithType([...mergedUrlsWithType])

      console.log('所有URLs及其类型（Thunder已解析，已去重）:', allUrlsWithType.value)

      // 按类型分组显示统计信息
      const typeStats = allUrlsWithType.value.reduce(
        (stats, item) => {
          const typeName = BaseType.TaskType[item.taskType] || 'Unknown'
          stats[typeName] = (stats[typeName] || 0) + 1
          return stats
        },
        {} as Record<string, number>
      )

      console.log('[AutoCreateTaskView] URL type statistics:', typeStats)
    } catch (error) {
      console.error('[AutoCreateTaskView] 批量处理URL类型检测时出错:', error)
    }
  }
}

// 解析和验证任务数据
const parseTaskData = async () => {
  console.log('[AutoCreateTaskView] parseTaskData called')

  const taskData = taskDataFromOptions.value

  if (!taskData || taskData.length === 0) {
    console.log('[AutoCreateTaskView] No task data to parse')
    setParsedTaskData([])
    return
  }

  console.log('[AutoCreateTaskView] Parsing task data:', taskData)

  // 验证和处理每个任务项
  const validTaskData: INewTaskDataItem[] = []

  taskData.forEach((task, index) => {
    console.log(`[AutoCreateTaskView] Processing task ${index}:`, task)

    // 基本验证
    if (!task) {
      console.warn(`[AutoCreateTaskView] Task ${index} is null or undefined`)
      return
    }

    // 确保必要的字段存在
    const processedTask: INewTaskDataItem = {
      ...task,
      // 如果没有 fileName，尝试从 url 提取
      fileName: task.fileName || extractFileNameFromUrlUtil(task.url) || `Task_${index + 1}`,
      // 确保 fileSize 是数字
      fileSize:
        typeof task.fileSize === 'number'
          ? task.fileSize
          : task.fileSize
            ? parseInt(String(task.fileSize))
            : 0,
      // 确保有默认的任务类型，但保持原有类型
      taskType: task.taskType,
    }

    console.log(`[AutoCreateTaskView] Processed task ${index}:`, processedTask)
    validTaskData.push(processedTask)
  })

  console.log('[AutoCreateTaskView] Final parsed task data:', validTaskData)
  setParsedTaskData(validTaskData)

  // 解析完任务数据后，提取URLs并获取类型
  await extractUrlsFromTaskData()

  // 处理 extData：如果 props.options.extData 存在，将对应 URL 的详情存储到独立的 extDataFromOptions 中
  if (props.options && 'extData' in props.options && props.options.extData) {
    console.log('[AutoCreateTaskView] Processing extData:', props.options.extData)

    // 将 extData 存储到 store 中，避免被覆盖
    setOptionsExtData({ ...props.options.extData })
    console.log('[AutoCreateTaskView] Stored extData to store:', optionsExtData.value)
  }
}

// 监听 props.options 变化（组件卸载时自动停止）
const optionsWatcher = watch(
  () => props.options,
  async newOptions => {
    console.log('[AutoCreateTaskView] Options changed:', newOptions)
    if (newOptions && 'type' in newOptions && newOptions.type === 'torrent') {
      // 直接把taskData的值更给allUrlsWithType
      if (
        newOptions &&
        'taskData' in newOptions &&
        newOptions.taskData &&
        Array.isArray(newOptions.taskData)
      ) {
        const newUrlsWithType = newOptions.taskData.map(item => ({
          url: item.url,
          taskType: item.taskType as BaseType.TaskType,
        }))
        console.log(
          '[AutoCreateTaskView] 调用 setAllUrlsWithType 前的 allUrlsWithType 值:',
          allUrlsWithType.value,
          'newUrlsWithType',
          newUrlsWithType
        )
        await setAllUrlsWithType(newUrlsWithType)
        console.log(
          '[AutoCreateTaskView] 调用 setAllUrlsWithType 后的 allUrlsWithType 值:',
          allUrlsWithType.value
        )
      }
    } else {
      await parseTaskData()
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

// 使用 watch + storeToRefs 监听 allUrlsWithType 变化（Pinia 最佳实践，组件卸载时自动停止）
const allUrlsWithTypeWatcher = watch(
  allUrlsWithType,
  (newUrlsWithType, oldUrlsWithType) => {
    console.log('[CreateTask] allUrlsWithType 变化检测:', {
      new: newUrlsWithType,
      old: oldUrlsWithType,
      newLength: newUrlsWithType?.length || 0,
      oldLength: oldUrlsWithType?.length || 0,
    })

    if (newUrlsWithType && newUrlsWithType.length > 0) {
      console.log('[CreateTask] ✅ allUrlsWithType 通过 watch 检测到变化:', newUrlsWithType)

      // 处理 URL 变化
      handleUrlsWithTypeChange(newUrlsWithType)
    }
  },
  { deep: true, immediate: false }
)

// 处理 URL 变化的函数
const handleUrlsWithTypeChange = async (newUrlsWithType: any[]) => {
  console.log('[CreateTask] 处理 URL 变化:', newUrlsWithType)

  for (const urlWithType of newUrlsWithType) {
    const { url, taskType } = urlWithType

    try {
      await processUrlByTaskType(url, taskType)
    } catch (error) {
      console.error(`处理URL时出错: ${url}`, error)
    }
  }
}

async function handleInputChangeDelay(newValue: string) {
  console.log('文本内容变化:', newValue)

  // 🔥 如果输入为空，立即清空所有状态
  if (!newValue || !newValue.trim()) {
    clearAllTaskStates()
    return
  }

  // 🔥 输入不为空时，先解析新的URLs，不立即清空现有状态
  // 按回车切割输入内容
  let urls = parseMultilineUrls(newValue)

  // 过滤掉空的URL并去重，保持第一次出现的顺序
  const validUrls = urls
    .filter(url => url.trim()) // 过滤掉空的URL
    .map(url => url.trim()) // 去除前后空格
    .filter((url, index, array) => array.indexOf(url) === index) // 去重，保持第一次出现的顺序

  console.log('去重后的有效URLs:', validUrls)

  // ========== 异步处理URL（包含Thunder解析和类型识别） ==========
  try {
    const urlsWithTypePromises = validUrls.map(async url => {
      try {
        // 首先尝试解析Thunder链接（如果是的话）
        const realUrl = await parseThunderUrlIfNeeded(url)

        // 然后获取真实URL的任务类型
        const taskType = await DkHelper.getTaskTypeFromUrl(realUrl)

        console.log('handleInputChangeDelay', { taskType })
        return { url: realUrl, taskType }
      } catch (error) {
        console.error('处理URL失败:', url, error)
        // 如果处理失败，默认为P2sp类型
        return { url, taskType: BaseType.TaskType.P2sp }
      }
    })

    const processedUrlsWithType = await Promise.all(urlsWithTypePromises)

    // 🔥 过滤掉taskType为Unknown的URL
    const newUrlsWithType = processedUrlsWithType.filter(item => {
      if (item.taskType === BaseType.TaskType.Unkown) {
        console.warn('过滤掉未知类型的URL:', item.url)
        return false
      }
      return true
    })

    // 🔥 直接使用新输入的URL数组，完全替换旧数据，不进行合并
    const mergedUrlsWithType = newUrlsWithType

    // 🔥 解析完成后，统一更新状态
    // 从合并后的数组中提取URL，保持与allUrlsWithType的同步
    setAllUrls(mergedUrlsWithType.map(item => item.url))
    await setAllUrlsWithType([...mergedUrlsWithType])

    console.log('所有URLs及其类型（Thunder已解析，已去重）:', allUrlsWithType.value)

    // 🔥 清理不再需要的 dataMap 和 urlExtraDataMap 条目
    const currentUrls = new Set(mergedUrlsWithType.map(item => item.url))
    const newDataMap = {}
    const newUrlExtraDataMap = {}

    // 只保留当前URLs对应的数据
    for (const url of currentUrls) {
      if (getDataMapItem(url)) {
        newDataMap[url] = getDataMapItem(url)
      }
      if (getUrlExtraDataMapItem(url)) {
        newUrlExtraDataMap[url] = getUrlExtraDataMapItem(url)
      }
    }

    // 更新清理后的数据映射
    setDataMap(newDataMap)
    setUrlExtraDataMap(newUrlExtraDataMap)
  } catch (error) {
    console.error('批量处理URL时出错:', error)
  }
}

/**
 * 通用URL处理函数
 * 根据URL类型调用相应的处理函数，体现函数式编程思想
 * @param url 待处理的URL
 * @param taskType URL类型
 */
async function processUrlByTaskType(url: string, taskType: BaseType.TaskType): Promise<void> {
  const trimmedUrl = url.trim()
  console.log('processUrlByTaskType', trimmedUrl, taskType)
  switch (taskType) {
    case BaseType.TaskType.Magnet:
      await newTaskStore.parseMagnetLinkInfo({ url: trimmedUrl })
      break

    case BaseType.TaskType.P2sp:
      // p2sp任务和ftp任务返回的taskType都是P2sp，需要自动检测协议类型
      const protocol = isFtpUrl(trimmedUrl) ? 'FTP' : 'HTTP'
      await newTaskStore.parseP2spLinkInfo({ url: trimmedUrl, protocol })
      break

    case BaseType.TaskType.Emule:
      await newTaskStore.parseEmuleLinkInfo({ url: trimmedUrl })
      break

    case BaseType.TaskType.Bt:
      console.warn('检测到本地BT种子文件:', trimmedUrl)
      await newTaskStore.parseLocalTorrentAndUpdateDataMap({
        torrentPath: trimmedUrl,
        key: trimmedUrl,
        forceReparse: false,
      })
      break

    default:
      console.warn('未知的URL类型，不处理:', trimmedUrl, taskType)
      // 对于未知类型，默认尝试作为HTTP链接处理
      // await getP2spLinkInfo({ url: trimmedUrl, protocol: 'HTTP' })
      break
  }
}

// 提供下载处理函数给所有子组件
provideDownloadHandler(handleAutoCreateDownload)

// 提供关闭窗口处理函数给所有子组件（内部函数）
provideCloseWindowHandler(closeWindow)

/**
 * 关闭当前弹窗
 */
async function closeWindow(scene?: string): Promise<void> {
  try {
    console.log('🔄 弹窗关闭场景:', !scene)
    // 检查是否传递了scene参数
    if (!scene || !scene.trim()) {
      console.error('❌ 关闭窗口失败：场景编号为空')
      return
    }
    const currentWindow = PopUpNS.getCurrentWindow()
    await currentWindow.close()
    console.log('✅ 弹窗已关闭，场景:', scene)
  } catch (error) {
    console.warn('❌ 关闭弹窗失败:', error, scene)
  }
}

// 初始化任务管理器
function initTaskManager() {
  TaskManager.GetInstance().init()
}

initTaskManager()

/**
 * 检查本地磁盘空间是否足够存储选中的文件
 * 通过遍历checkedFileIndexes获取选中文件的总大小，通过downloadPathStore的pathInfoList获取当前路径的剩余空间大小，
 * 判断文件总大小是否大于本地路径的剩余空间，如果大于，直接提示空间不足
 * @returns Promise<boolean> 返回true表示空间足够，false表示空间不足
 */
async function checkLocalDiskSpace(): Promise<boolean> {
  try {
    console.log('[CreateTask] 开始检查本地磁盘空间是否足够')

    // 使用已有的函数获取选中文件的总大小
    const totalSelectedFileSize = getSelectedFilesTotalSize()

    // 从downloadPathStore获取当前路径的剩余空间信息
    const currentPath = taskSavePath.value

    // 从currentPath 获取是哪个磁盘 比如c:\\迅雷下载 ， 那就是c盘， 通过调用
    const pathInfo = downloadPathStore.findLocalPanPathInfoByDir(currentPath)

    console.log('pathInfo', pathInfo)

    if (!pathInfo || !pathInfo.spaceInfo) {
      console.error('[CreateTask] 无法获取当前路径的空间信息:', currentPath)
      // 如果无法获取空间信息，为了安全起见，提示空间不足
      XMPMessage({
        message: '无法获取本地磁盘空间信息，请稍后重试',
        type: 'warning',
      })
      return false
    }

    const { free: availableSpace } = pathInfo.spaceInfo
    console.log(`[CreateTask] 本地磁盘剩余空间: ${availableSpace} 字节`)

    // 比较文件总大小和本地磁盘剩余空间
    if (totalSelectedFileSize > availableSpace) {
      console.warn(
        `[CreateTask] 本地磁盘空间不足: 需要 ${totalSelectedFileSize} 字节，剩余 ${availableSpace} 字节`
      )

      const formattedTotalSize = FileOperationHelper.formatSize(totalSelectedFileSize)
      const formattedAvailableSpace = FileOperationHelper.formatSize(availableSpace)

      const driveSpaceInfo = getLogicalDriveList()
      console.log(
        '[CreateTask] 驱动器空间信息:',
        `本地磁盘空间不足，选中文件总大小 ${formattedTotalSize}，磁盘剩余空间 ${formattedAvailableSpace}`,
        driveSpaceInfo
      )

      setIsDiskSpaceInsufficient(true)

      // showDriveSpaceDialog({
      //   driveSpaceInfo,
      //   currentSavePath: taskSavePath.value,
      //   requiredSpace: formattedTotalSize,
      // })

      return false
    }

    console.log('[CreateTask] 本地磁盘空间充足，可以继续操作')
    setIsDiskSpaceInsufficient(false)
    return true
  } catch (error) {
    console.error('[CreateTask] 检查本地磁盘空间时出错:', error)
    // 如果检查过程中出错，为了安全起见，提示空间不足
    XMPMessage({
      message: '检查本地磁盘空间时出错，请稍后重试',
      type: 'error',
      duration: 2000,
    })
    return false
  }
}

/**
 * 检查磁力链文件数量是否超出限制
 * 统计所有选中的磁力链任务中的文件数量，如果超过1000个文件则提示超出限制
 * @returns boolean 返回true表示文件数量在限制内，false表示超出限制
 */
function checkMagnetFileCountLimit(): boolean {
  try {
    console.log('[CreateTask] 开始检查磁力链文件数量限制')

    // 使用 getMagnetCount 函数获取磁力链文件数量
    const totalMagnetFileCount = newTaskStore.getMagnetCount()

    // 检查是否超出最大文件数量的限制
    if (totalMagnetFileCount > MAX_MAGNET_FILE_COUNT) {
      console.warn(
        `[CreateTask] 磁力链文件数量超出限制: ${totalMagnetFileCount} > ${MAX_MAGNET_FILE_COUNT}`
      )
      showOverLimitAlert()

      return false
    }

    console.log('[CreateTask] 磁力链文件数量在限制内，可以继续操作')
    return true
  } catch (error) {
    console.error('[CreateTask] 检查磁力链文件数量限制时出错:', error)
    // 如果检查过程中出错，为了安全起见，提示超出限制
    showOverLimitAlert()
    return false
  }
}

// 函数-超出提醒
function showOverLimitAlert() {
  alertDialog.open({
    title: '单次任务上限提醒',
    content: `单次最多添加 ${MAX_MAGNET_FILE_COUNT} 个任务，超出部分请分批提交`,
    confirmText: '我知道了',
    showCancel: false,
    showCloseButton: true,
    variant: 'thunder',
    showTitleIcon: true,
    alwaysOnTop: true,
  })
}

function handleDownload() {
  console.log('点击下载按钮')
  setIsDiskSpaceInsufficient(false)
  handleConfirm()
}

//  下载选中的磁力链，需要获取选中的磁力链文件的下标index
const handleConfirm = async () => {
  try {
    // 检查磁力链文件数量是否超出限制
    const isMagnetFileCountValid = checkMagnetFileCountLimit()
    if (!isMagnetFileCountValid) {
      console.log('[CreateTask] 磁力链文件数量超出限制，取消下载操作')
      return
    }

    // 首先检查本地磁盘空间是否足够
    const hasEnoughSpace = await checkLocalDiskSpace()
    if (!hasEnoughSpace) {
      console.log('[CreateTask] 本地磁盘空间不足，取消下载操作')
      return
    }

    // 判断是否合并为任务组
    if (isMergeTask.value) {
      // 合并为任务组的逻辑
      await taskCreationStore.buildTaskGroup()
      // 任务组创建逻辑比较复杂，暂时保持原有的成功提示逻辑
      console.log('toast 任务成功2')
      closeWindow('task_confirmed')
      // showMessage('创建任务成功', 'success', 'task_confirmed')
    } else {
      // 分别处理每个任务的逻辑
      const result = await handleIndividualTasksConfirm()

      // 使用专门的函数处理任务创建结果
      await processIndividualTasksResult(result)
    }

    // 创建任务完成后记录下载路径到历史记录
    await recordDownloadPath(taskSavePath.value, DownloadPathType.Local)

    savePendingAllTasks()
  } catch (error) {
    console.error('创建任务过程中出错:', error)
    // 即使出错也尝试记录路径
    await recordDownloadPath(taskSavePath.value, DownloadPathType.Local)
  }
}

/**
 * 处理个别任务创建的结果
 * @param result handleIndividualTasksConfirm 的返回结果
 * @returns Promise<boolean> 是否全部任务成功
 */
async function processIndividualTasksResult(result: {
  success: boolean
  successCount: number
  failureCount: number
  duplicateCount: number
  totalCount: number
  errors: Array<{ url: string; taskType: BaseType.TaskType; error: Error }>
}): Promise<boolean> {
  console.info('processIndividualTasksResult', result)
  let allTasksSuccessful = true
  let successMessage = '创建任务成功'

  // 检查是否存在重复任务，使用 duplicateCount 判断
  const hasDuplicateTasks = result.duplicateCount > 0

  console.info('hasDuplicateTasks', hasDuplicateTasks, 'duplicateCount:', result.duplicateCount)

  // 如果有重复任务，获取重复任务详情并显示对话框
  if (hasDuplicateTasks || taskCreationStore.duplicateTaskIds.length > 0) {
    console.log('检测到重复任务，准备显示重复任务对话框')
    const duplicateDetails = await taskCreationStore.getDuplicateTasksDownloadDetails()

    // 如果有重复任务，显示对话框
    if (duplicateDetails.length > 0) {
      ThunderNewTaskHelperNS.showDuplicateTaskWindow({
        duplicateTaskIds: taskCreationStore.duplicateTaskIds,
        urlsWithType: taskCreationStore.duplicateUrlsWithType,
        dataMap: taskCreationStore.duplicateDataMap,
        taskSettings: taskCreationStore.duplicateTaskSettings,
        taskSavePath: taskSavePath.value,
        urlExtraDataMap: urlExtraDataMap.value,
        checkedFileIndexes: checkedFileIndexes.value,
        optionsExtData: optionsExtData.value,
        isDiskSpaceInsufficient: isDiskSpaceInsufficient.value,
        // 新增：传递 originDataMap
        originDataMap: newTaskStore.getOriginDataMap(),
      })

      closeWindow('duplicate_task')
    }

    return false // 有重复任务时不显示成功提示
  }

  // 处理任务创建结果并显示相应提示
  if (result.success && result.successCount > 0) {
    // 全部成功
    successMessage = `成功创建 ${result.successCount} 个任务`
    allTasksSuccessful = true
  } else if (result.successCount > 0 && result.failureCount > 0) {
    // 部分成功
    allTasksSuccessful = false
    console.warn(`部分任务创建失败 - 成功: ${result.successCount}, 失败: ${result.failureCount}`)
    XMPMessage({
      message: `${result.successCount} 个任务创建成功，${result.failureCount} 个任务创建失败`,
      type: 'warning',
    })
  } else if (result.failureCount > 0) {
    // 全部失败
    allTasksSuccessful = false
    console.error(`所有任务创建失败 - 失败数量: ${result.failureCount}`)
    XMPMessage({
      message: `任务创建失败`,
      type: 'error',
    })
  } else if (result.totalCount === 0) {
    // 没有任务需要创建
    allTasksSuccessful = false
    console.warn('没有选中的任务需要创建')
    XMPMessage({
      message: '请先选择要下载的任务',
      type: 'warning',
    })
  }

  // 如果所有任务都成功，显示成功提示
  if (allTasksSuccessful) {
    closeWindow('task_confirmed')
  }

  return allTasksSuccessful
}

/**
 * 处理分别创建各个任务的确认逻辑
 * @returns 返回执行结果统计信息
 */
async function handleIndividualTasksConfirm(): Promise<{
  success: boolean
  successCount: number
  failureCount: number
  duplicateCount: number
  totalCount: number
  errors: Array<{ url: string; taskType: BaseType.TaskType; error: Error }>
}> {
  const taskPromises: Array<{
    url: string
    taskType: BaseType.TaskType
    promise: Promise<any>
  }> = []

  // 第一步：为所有需要创建的任务创建 Promise，实现并行处理
  for (const urlWithType of allUrlsWithType.value) {
    const { url, taskType } = urlWithType

    let taskPromise: Promise<any> | null = null

    // 根据任务类型创建对应的 Promise
    if (taskType === BaseType.TaskType.Magnet) {
      const { status, torrentPath } = urlExtraDataMap.value[url]
      if (status === 'success') {
        taskPromise = taskCreationStore.createBtTaskFromTorrent({
          torrentPath,
          originUrl: url,
        })
      }
    } else if (taskType === BaseType.TaskType.Bt) {
      const isSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0
      if (isSelected) {
        taskPromise = taskCreationStore.createBtDownloadTask({
          torrentPath: url,
          originUrl: url,
        })
      }
    } else if (taskType === BaseType.TaskType.P2sp) {
      const isSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0
      if (isSelected) {
        taskPromise = taskCreationStore.createP2spDownloadTask(url)
      }
    } else if (taskType === BaseType.TaskType.Emule) {
      const isSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0
      if (isSelected) {
        taskPromise = taskCreationStore.createEd2kTask(url)
      }
    }

    // 如果创建了 Promise，添加到数组中
    if (taskPromise) {
      taskPromises.push({
        url,
        taskType,
        promise: taskPromise,
      })
    }
  }

  // 第二步：并行执行所有任务创建，使用 Promise.allSettled 确保所有任务都会被处理
  const results = await Promise.allSettled(taskPromises.map(item => item.promise))

  console.log('results11', results)
  // 第三步：处理结果，收集错误信息
  const errors: { url: string; taskType: BaseType.TaskType; error: Error }[] = []
  let duplicateCount = 0

  results.forEach((result, index) => {
    console.log('result12', result, index)
    const { url, taskType } = taskPromises[index]

    if (result.status === 'rejected') {
      const error = result.reason
      console.error(`创建任务失败 (URL: ${url}, 类型: ${taskType}):`, error)

      // 检查是否是重复任务错误
      const taskError = error as TaskError
      if (taskError.type === 'existed') {
        console.log(`跳过重复任务: ${url} (${taskError.taskType})`)
        duplicateCount++
        return // 重复任务不算错误
      }

      // 收集其他类型的错误
      console.warn(`任务创建出错: ${url}`, error)
      errors.push({
        url,
        taskType,
        error: error instanceof Error ? error : new Error(String(error)),
      })
    } else {
      console.log(`✅ 任务创建成功 (URL: ${url}, 类型: ${taskType})`)
    }
  })

  // 第四步：计算统计信息
  const totalCount = taskPromises.length
  const successCount = results.filter(result => result.status === 'fulfilled').length
  const failureCount = errors.length
  const success = errors.length === 0 // 只有没有真正的错误才算成功

  console.info(
    `任务创建完成 - 成功: ${successCount}, 失败: ${failureCount}, 重复跳过: ${duplicateCount}`
  )

  if (errors.length > 0) {
    console.warn(`创建任务过程中发生 ${errors.length} 个错误:`, errors)
  }

  // 返回执行结果
  return {
    success,
    successCount,
    failureCount,
    duplicateCount,
    totalCount,
    errors,
  }
}

// 在创建bt任务之前， 应先有一个解析函数去获取torrent地址对应的详情， 然后去更改allurlswithtype datamap

const handleCancel = async () => {
  console.log('Task creation cancelled')
  setIsLoading(true)
  await new Promise(resolve => setTimeout(resolve, 3000))
  setIsLoading(false)
}

async function savePendingAllTasks() {
  console.log('[CreateTask] 使用新的LinkSaver工具保存所有待处理任务')

  try {
    const linkSaver = LinkSaver.getInstance()

    const options: LinkSaveOptions = {
      scene: LinkSaveScene.CREATE_TASK,
      actions: ['ACTION_DOWNLOAD'],
      ignoreEvent: false,
      status: 'STATUS_NORMAL', 
    }

    const result = await linkSaver.savePendingAllTasks({
      allUrlsWithType: allUrlsWithType.value,
      dataMap: dataMap.value,
      urlExtraDataMap: urlExtraDataMap.value,
      checkedFileIndexes: checkedFileIndexes.value,
      optionsExtData: optionsExtData.value, // 添加 optionsExtData 参数
      options,
    })

    console.log('[CreateTask] 链接保存结果:', result)

    if (result.success) {
      console.log(`[CreateTask] 成功保存 ${result.savedCount} 个任务到链接中心`)
    } else {
      console.warn(`[CreateTask] 保存完成，成功: ${result.savedCount}，失败: ${result.failedCount}`)
      result.errors.forEach(error => {
        console.error(`[CreateTask] 保存失败: ${error.url} - ${error.error}`)
      })
    }

    return result
  } catch (error) {
    console.error('[CreateTask] 保存所有待处理任务时出错:', error)
    throw error
  }
}

function getTaskSettings(taskId: string): TaskSettings {
  return taskSettingStore.getTaskSettings(taskId)
}

/**
 * 记录下载路径到历史记录
 * 纯函数：接收路径参数，返回记录结果，无外部依赖
 * Record download path to history
 * Pure function: receives path parameter, returns recording result, no external dependencies
 *
 * @param savePath 要记录的下载路径 The download path to record
 * @param type 下载路径类型 The type of download path
 * @returns Promise<boolean> 返回是否成功记录 Returns whether the recording was successful
 */
async function recordDownloadPath(savePath: string, type: DownloadPathType): Promise<boolean> {
  // 参数验证
  if (!savePath || !savePath.trim()) {
    console.warn('记录下载路径失败：路径为空')
    return false
  }

  try {
    if (type === DownloadPathType.Local) {
      await DownloadPathNS.addPath(savePath.trim())
    } else if (type === DownloadPathType.Cloud) {
      await downloadCloudPathStore.setRecentSaveDefaultFolder(
        downloadCloudPathStore.getCurrentCloudPath()!
      )
    }

    // 记录上次使用的类型
    config.setValue('TaskDefaultSettings', 'LogicChoosed', type === DownloadPathType.Local)
    config.setValue('TaskDefaultSettings', 'CloudChoosed', type === DownloadPathType.Cloud)!
    console.log('成功记录下载路径到历史记录:', savePath)
    return true
  } catch (error) {
    console.error('记录下载路径失败:', error, '路径:', savePath)
    return false
  }
}

async function handleDownloadButtonSubmit(params: {
  type: DownloadPathType
  path?: string
  scene?: string
}) {
  const { type, path = '', scene } = params

  console.log('[CreateTask] 收到 DownloadButton:', type, '路径:', path, '场景:', scene)

  if (type === DownloadPathType.Local) {
    handleDownload()
  } else if (type === DownloadPathType.Cloud) {
    // 调用添加到云盘逻辑，传递云盘父文件夹ID, path是空字符串，表示是添加到云盘根目录
    if (freeCloudAddCount.value <= 0) {
      XMPMessage({
        message: '今日云添加次数已用完，请明日再试',
        type: 'warning',
      })
    } else {
      handleCloudDownload(path)
    }
  }
}

/**
 * 异步保存云盘下载链接到 LinkHub
 * @param parentId 云盘父文件夹ID
 */
async function saveCloudDownloadLinksToHub(parentId: string) {
  try {
    const linkSaver = LinkSaver.getInstance()

    const options: LinkSaveOptions = {
      scene: LinkSaveScene.BATCH_OPERATION,
      actions: ['ACTION_CLOUD_DOWNLOAD'],
      ignoreEvent: false,
      customData: {
        parentId: parentId,
        timestamp: Date.now(),
      },
      status: 'STATUS_NORMAL', 
    }

    await linkSaver.savePendingAllTasks({
      allUrlsWithType: allUrlsWithType.value,
      dataMap: dataMap.value,
      urlExtraDataMap: urlExtraDataMap.value,
      checkedFileIndexes: checkedFileIndexes.value,
      optionsExtData: optionsExtData.value, // 添加 optionsExtData 参数
      options,
    })

    console.log('[CreateTask] 云盘下载链接已保存到 LinkHub')
  } catch (linkSaveError) {
    console.error('[CreateTask] 保存云盘下载链接失败:', linkSaveError)
    // 链接保存失败不影响云盘下载流程
  }
}

/**
 * 处理云盘下载
 * @param parentId 云盘父文件夹ID
 */
async function handleCloudDownload(parentId: string) {
  console.log('开始云盘下载，父文件夹ID:', parentId)

  try {
    // 首先检查云盘空间是否足够
    const hasEnoughSpace = await checkCloudDriveSpace()
    if (!hasEnoughSpace) {
      console.log('[CreateTask] 云盘空间不足，取消云盘下载操作')
      return
    }

    // 调用批量添加到云盘接口
    await batchAddUrlsToDrive(parentId)

    // 异步保存链接到 LinkHub，不阻塞主流程
    saveCloudDownloadLinksToHub(parentId)

    console.log('toast 任务成功1')

    // 记录下载路径
    await recordDownloadPath(downloadCloudPathStore.currentPathDisplayName, DownloadPathType.Cloud)

    // closeWindow('cloud_download_completed')
    // 调用封装的成功提示函数
    showMessage('已加入云添加列表', 'success', 'cloud_download_completed')
  } catch (error) {
    console.error('云盘下载过程中出错:', error)
  }
}

/**
 * 批量添加任务到云盘
 * @param parentId 云盘父文件夹ID
 * @param groupName 可选的任务组名称
 */
async function batchAddUrlsToDrive(parentId: string, groupName?: string) {
  console.log('开始批量添加任务到云盘，父文件夹ID:', parentId, '任务组名称:', groupName)

  try {
    // 获取当前云盘路径
    const currentCloudPath = downloadCloudPathStore.getCurrentCloudPath()
    console.log('[CreateTask] 当前云盘路径:', currentCloudPath)

    const tasks: any[] = []

    // 遍历所有URL，根据taskType处理不同类型的任务
    for (const urlWithType of allUrlsWithType.value) {
      const { url, taskType } = urlWithType
      const dataInfo = dataMap.value[url]

      if (!dataInfo) {
        console.warn('找不到任务数据信息:', url)
        continue
      }

      // 根据任务类型处理
      switch (taskType) {
        case BaseType.TaskType.Magnet:
          // 处理磁力链任务
          const magnetStatus = urlExtraDataMap.value[url]?.status
          if (magnetStatus === 'success') {
            const isSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0

            if (isSelected) {
              // 获取选中的文件下标
              const selectedFileIndexes = getCheckedFileIndexedByUrl({ url })

              // 构造磁力链任务数据
              const magnetTask = {
                path: currentCloudPath?.fullFolderpathStr || '',
                name: newTaskStore.getTaskDisplayName(url),
                url: url,
                parentId: parentId,
                files: selectedFileIndexes.map(index => index.toString()), // 将文件下标转换为字符串数组
                play: false,
                space: '', // 根据需要设置空间类型
              }
              tasks.push(magnetTask)
              console.log('添加磁力链任务到批量任务列表:', magnetTask)
            }
          }
          break

        case BaseType.TaskType.P2sp:
          // 处理P2SP任务
          const isSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0

          if (isSelected) {
            const p2spTask = {
              path: currentCloudPath?.fullFolderpathStr || '',
              name: newTaskStore.getTaskDisplayName(url),
              url: url,
              parentId: parentId,
              files: [], // P2SP任务没有子文件，设置为空数组
              play: false,
              space: '',
            }
            tasks.push(p2spTask)
            console.log('添加P2SP任务到批量任务列表:', p2spTask)
          }
          break

        case BaseType.TaskType.Emule:
          // 处理Emule任务
          const emuleIsSelected = checkedFileIndexes.value[url]?.fileIndexes?.length > 0

          if (emuleIsSelected && dataInfo.detail) {
            const emuleTask = {
              path: currentCloudPath?.fullFolderpathStr || '',
              name: newTaskStore.getTaskDisplayName(url),
              url: url,
              parentId: parentId,
              files: [], // Emule任务没有子文件，设置为空数组
              play: false,
              space: '',
            }
            tasks.push(emuleTask)
            console.log('添加Emule任务到批量任务列表:', emuleTask)
          }
          break

        default:
          console.warn('未知的任务类型:', taskType, url)
          break
      }
    }

    if (tasks.length > 0) {
      // 调用批量添加到云盘接口
      const options: any = {
        autoSelectTab: true,
      }
      if (groupName) {
        options.groupName = groupName
      }

      console.log(
        '调用批量添加到云盘接口，任务数量:',
        tasks.length,
        '参数:',
        tasks,
        '选项:',
        options
      )

      const result = await ThunderPanClientSDK.getInstance().batchAddUrlsToDrive(tasks, options)

      console.log('批量添加到云盘结果:', result)

      if (result.success) {
        console.log('批量添加到云盘成功:', result.data)

        // 可以在这里添加成功提示
        // XMPMessage({
        //   message: `成功添加 ${tasks.length} 个任务到云盘`,
        //   type: 'success',
        // })
      } else {
        console.error('批量添加到云盘失败:', result.error)
        throw new Error(result.error || '批量添加到云盘失败')
      }
    } else {
      console.log('没有选中的任务需要添加到云盘')
    }
  } catch (error) {
    console.error('批量添加到云盘时出错:', error)
    throw error
  }
}

//自动拉起的任务-下载
function handleAutoCreateDownload(params: DownloadEventParams) {
  console.log('[create] handleAutoCreateDownload:', params)
  setCheckedFileIndexes(params.checkedFileIndexes)
  // 将 DownloadEventParams 转换为 handleDownloadButtonSubmit 的参数格式
  const downloadPathType =
    params.type === 'download' ? DownloadPathType.Local : DownloadPathType.Cloud
  handleDownloadButtonSubmit({
    type: downloadPathType,
    path: params.path,
    scene: 'auto-create-task',
  })
}

//接收参数， 关闭自动创建任务 (保持原有签名兼容性)
function handleAutoCreateTaskClose(params: { scene: string }) {
  console.log('handleAutoCreateTaskClose 被调用，参数:', params)

  if (!params || !params.scene || !params.scene.trim()) {
    console.error('❌ 关闭窗口失败：场景编号为空')
    return
  }

  closeWindow(params.scene.trim())
}

/**
 * 处理 TaskLaterButton 成功事件
 */
const handleLaterSuccess = (result: {
  success: boolean
  message: string
  savedCount?: number
  failedCount?: number
}) => {
  console.info('[CreateTask] TaskLaterButton 成功:', result)
  // TODO：加埋点
  // tasklaterbutton组件内部已弹出提示 关闭弹窗 跳转到全链接
  // 显示成功提示
  // XMPMessage({
  //   message: result.message,
  //   type: 'success',
  // })

  // showMessage(`已加入全部链接`, 'success', 'task_later_success')

  // toAllLinkPage()
  // 关闭面板
  // closeWindow('task_later_success')
}

/**
 * 处理 TaskLaterButton 错误事件
 */
const handleLaterError = (error: string) => {
  console.error('[CreateTask] TaskLaterButton 失败:', error)

  // 显示错误提示
  XMPMessage({
    message: error,
    type: 'error',
  })
}

// 新增：处理重复任务对话框事件
const handleDuplicateTaskClose = () => {
  console.log('用户关闭重复任务对话框')
  setShowDuplicateTaskDialog(false)
  taskCreationStore.clearDuplicateTaskDetails()
  taskCreationStore.clearDuplicateTaskUrls()
}

const handleDuplicateTaskSkip = () => {
  console.log('用户选择跳过重复任务')

  // 重置所有重复任务相关的变量
  setShowDuplicateTaskDialog(false)
  taskCreationStore.clearDuplicateTaskDetails()
  taskCreationStore.clearDuplicateTaskUrls()

  // 显示提示信息
  // XMPMessage({
  //   message: '已跳过重复任务',
  //   type: 'info',
  // })
}

/**
 * 显示提示消息，支持配置消息类型和是否需要关闭窗口
 */
function showMessage(
  message: string,
  type: 'success' | 'error' | 'warning' = 'success',
  closeWindowScene?: string
) {
  XMPMessage({
    message,
    type,
    duration: 800,
    onClose: closeWindowScene ? () => closeWindow(closeWindowScene) : undefined,
  })
}

onMounted(async () => {
  const driveSpaceInfo = getLogicalDriveList()
  console.log('[CreateTask] 初始化时获取驱动器空间信息:', driveSpaceInfo)
  console.log('[CreateTask] 组件初始化时失焦状态:', isWindowBlurred.value)

  // 初始化认证状态
  await initializeAuth()

  // 检查登录状态，如果未登录则显示登录弹窗
  // const isLoggedIn = await requireAuth()
  // if (!isLoggedIn) {
  //   console.log('[CreateTask] 用户未登录，不弹出登录弹窗')
  //   // 标记已经在组件中显示了登录弹窗
  //   hasShownLoginDialog.value = true
  //   // 弹出登录弹窗
  //   showLoginDialog()
  //   // 注意：这里不需要等待，因为登录弹窗是异步的
  //   // 用户登录成功后，会通过事件监听器自动更新状态
  //   // return
  // }

  // 获取当前用户云添加次数
  newTaskConfigStore.setCurrentUserCloudAddQuotas()

  console.log('[CreateTask] 用户已登录，继续初始化')
  // 继续组件的初始化逻辑
  // initializeComponentAfterLogin()

  // 新增：设置窗口失焦事件监听器
  console.log('[CreateTask] 设置窗口失焦事件监听器')

  // 监听窗口失焦事件（组件卸载时自动清理）
  useEventListener(window, 'blur', handleWindowBlur, { passive: true })

  // 监听窗口获得焦点事件（组件卸载时自动清理）
  useEventListener(window, 'focus', handleWindowFocus, { passive: true })

  // 监听文档可见性变化事件（组件卸载时自动清理）
  useEventListener(document, 'visibilitychange', handleVisibilityChange, { passive: true })

  // 监听页面隐藏事件（当用户切换到其他标签页或应用时，组件卸载时自动清理）
  useEventListener(document, 'pagehide', handleWindowBlur, { passive: true })

  // 监听页面显示事件（当用户回到当前标签页时，组件卸载时自动清理）
  useEventListener(document, 'pageshow', handleWindowFocus, { passive: true })
})

function setLoggedWindowSize() {
  overridePosition(defaultPositionOptions)
}
// 增加一个函数把窗口大小设置为 0 和 0
function setWindowSizeToZero() {
  const defaultPositionOptions = {
    autoSize: false,
    show: false,
    windowWidth: 0,
    windowHeight: 0,
    relatePos: PopUpTypes.RelatePosType.CenterParent,
  }

  overridePosition(defaultPositionOptions)
}
// 增加一个函数把窗口大小设置为 0 和 0
function setUnLoggedWindowSize() {
  setWindowSizeToZero()
}

// 监听登录状态变化，当用户登录成功时继续初始化（组件卸载时自动停止）
const loginStatusWatcher = watch(
  () => userStore.isLogged,
  (newStatus, oldStatus) => {
    // 未登录-弹窗大小设置为0 * 0
    if (newStatus === false) {
      setUnLoggedWindowSize()
    }

    // 已登录-弹窗大小设置为680 * 316
    if (newStatus === true) {
      setLoggedWindowSize()
    }
    if (newStatus && !oldStatus) {
      // 登录状态从 false 变为 true，说明用户刚刚登录成功
      if (hasShownLoginDialog.value) {
        // 场景2：在组件中显示了登录弹窗后登录成功
        console.log('[CreateTask] 场景2：用户在组件中登录成功，继续初始化')
        hasShownLoginDialog.value = false // 重置标志
        initializeComponentAfterLogin()
      } else {
        // 场景1：打开组件时用户已经登录
        console.log('[CreateTask] 场景1：用户打开组件时已经登录')
        // 这种情况在 onMounted 中已经处理了，这里不需要重复处理
      }
    }
  }
)

// 组件登录后的初始化逻辑
function initializeComponentAfterLogin() {
  console.log('[CreateTask] 开始组件登录后的初始化')
  // 在这里添加需要在用户登录后执行的初始化逻辑
  // 例如：加载用户数据、初始化任务列表等
}

// 组件卸载时清理所有监听器和资源
onUnmounted(() => {
  console.log('[CreateTask] 组件卸载，开始清理所有监听器和资源')

  // 手动停止所有 watch 监听器（虽然会自动停止，但显式停止更安全）
  if (allUrlsWithTypeWatcher) {
    allUrlsWithTypeWatcher()
    console.log('[CreateTask] 已停止 allUrlsWithType 监听器')
  }

  if (optionsWatcher) {
    optionsWatcher()
    console.log('[CreateTask] 已停止 options 监听器')
  }

  if (loginStatusWatcher) {
    loginStatusWatcher()
    console.log('[CreateTask] 已停止登录状态监听器')
  }

  // 组件销毁时重置所有 store 状态到初始值
  newTaskConfigStore.resetAll()
  console.log('[CreateTask] 已重置 newTaskConfigStore')

  // 新增：清空重复任务列表
  taskCreationStore.clearDuplicateTaskUrls()
  console.log('[CreateTask] 已清空重复任务列表')

  // 清空 new-task store 的所有变量
  clearAllTaskStates()
  console.log('[CreateTask] 已清空 new-task store 状态')

  // 清理认证相关资源
  cleanupAuth()
  console.log('[CreateTask] 已清理认证资源')

  console.log('[CreateTask] 组件卸载完成，所有监听器和资源已清理')
})

/**
 * 检查云盘空间是否足够存储选中的文件
 * 通过遍历checkedFileIndexes获取选中文件的总大小，通过getCurrentUserDriveQuotas获取云盘剩余空间大小，
 * 判断文件总大小是否大于云盘的剩余空间，如果大于，直接提示空间不足
 * @returns Promise<boolean> 返回true表示空间足够，false表示空间不足
 */
async function checkCloudDriveSpace(): Promise<boolean> {
  try {
    console.log('[CreateTask] 开始检查云盘空间是否足够')

    // 使用新的函数获取选中文件的总大小
    const totalSelectedFileSize = getSelectedFilesTotalSize()

    // 获取云盘剩余空间
    const driveQuotasResult = await ThunderPanClientSDK.getInstance().getCurrentUserDriveQuotas()

    if (!driveQuotasResult.success || !driveQuotasResult.data) {
      console.error('[CreateTask] 获取云盘配额信息失败:', driveQuotasResult.error)
      showMessage('无法获取云盘空间信息，请稍后重试', 'warning')
      return false
    }

    const { surplus } = driveQuotasResult.data
    console.log(`[CreateTask] 云盘剩余空间: ${surplus} 字节`)

    // 比较文件总大小和云盘剩余空间
    if (totalSelectedFileSize > surplus) {
      console.warn(
        `[CreateTask] 云盘空间不足: 需要 ${totalSelectedFileSize} 字节，剩余 ${surplus} 字节`
      )

      showMessage(`云盘空间不足`, 'warning')

      return false
    }

    console.log('[CreateTask] 云盘空间充足，可以继续操作')
    return true
  } catch (error) {
    console.error('[CreateTask] 检查云盘空间时出错:', error)
    showMessage('检查云盘空间时出错，请稍后重试', 'error')
    return false
  }
}

/**
 * 获取选中文件的总大小
 * 通过遍历checkedFileIndexes计算所有选中文件的总大小
 * @returns number 返回选中文件的总大小（字节）
 */
function getSelectedFilesTotalSize(): number {
  let totalSelectedFileSize = 0

  // 遍历checkedFileIndexes，计算所有选中文件的总大小
  for (const [url, selectionInfo] of Object.entries(checkedFileIndexes.value)) {
    if (!selectionInfo?.fileIndexes?.length) {
      continue // 跳过没有选中文件的任务
    }

    const dataInfo = dataMap.value[url]
    if (!dataInfo) {
      console.warn(`[CreateTask] 未找到URL对应的数据信息: ${url}`)
      continue
    }

    // 根据任务类型计算文件大小
    const urlWithType = allUrlsWithType.value.find(item => item.url === url)
    if (!urlWithType) {
      console.warn(`[CreateTask] 未找到URL对应的任务类型信息: ${url}`)
      continue
    }

    const { taskType } = urlWithType

    switch (taskType) {
      case BaseType.TaskType.Magnet:
        // 磁力链任务：计算选中文件的大小
        if (dataInfo.fileLists && Array.isArray(dataInfo.fileLists)) {
          for (const fileIndex of selectionInfo.fileIndexes) {
            const file = dataInfo.fileLists[fileIndex]
            if (file && typeof file.fileSize === 'number') {
              totalSelectedFileSize += file.fileSize
            }
          }
        }
        break

      case BaseType.TaskType.P2sp:
        // P2SP任务：使用单个文件的大小
        if (typeof dataInfo.fileSize === 'number') {
          totalSelectedFileSize += dataInfo.fileSize
        }
        break

      case BaseType.TaskType.Emule:
        // Emule任务：使用单个文件的大小
        if (typeof dataInfo.fileSize === 'number') {
          totalSelectedFileSize += dataInfo.fileSize
        }
        break

      default:
        console.warn(`[CreateTask] 未知的任务类型: ${taskType}, URL: ${url}`)
        break
    }
  }

  console.log(`[CreateTask] 选中文件总大小: ${totalSelectedFileSize} 字节`)
  return totalSelectedFileSize
}

/**
 * 获取逻辑驱动器字符串和空间信息
 * 先调用 ThunderHelper 的 getLogicalDriveStrings 方法获取逻辑驱动器字符串
 * 再调用 ThunderHelper.getFreePartitionSpace 获取每个驱动器的空间大小
 * @returns Array<{drive: string, freeSpace: number, totalSpace: number}> 返回驱动器及其剩余空间信息数组
 */
function getLogicalDriveList(): Array<{ drive: string; freeSpace: number; totalSpace: number }> {
  try {
    console.log(`[CreateTask] 获取逻辑驱动器字符串`)

    // 调用 ThunderHelper 的 getLogicalDriveStrings 方法获取驱动器列表
    const logicalDriveStrings = ThunderHelper.getLogicalDriveStrings()

    console.log(`[CreateTask] 逻辑驱动器字符串: ${logicalDriveStrings}`)

    // 对每个驱动器获取剩余空间信息
    const driveSpaceInfo: Array<{ drive: string; freeSpace: number; totalSpace: number }> = []

    for (const drive of logicalDriveStrings) {
      try {
        // 调用 ThunderHelper.getFreePartitionSpace 获取该驱动器的剩余空间
        const freeSpace = ThunderHelper.getFreePartitionSpace(drive)
        const space = ThunderHelper.getPartitionSpace(drive)

        driveSpaceInfo.push({
          drive: drive,
          freeSpace: space.free,
          totalSpace: space.total,
        })

        console.log(`[CreateTask] 驱动器 ${drive} 剩余空间: ${freeSpace} 字节`)
      } catch (error) {
        console.warn(`[CreateTask] 获取驱动器 ${drive} 空间信息失败:`, error)
        // 如果获取失败，仍然添加到结果中，但空间设为 -1
        driveSpaceInfo.push({
          drive: drive,
          freeSpace: -1,
          totalSpace: -1,
        })
      }
    }

    console.log(`[CreateTask] 驱动器空间信息:`, driveSpaceInfo)
    return driveSpaceInfo
  } catch (error) {
    console.error('[CreateTask] 获取逻辑驱动器字符串失败:', error)
    return []
  }
}

async function showDriveSpaceDialog(options: {
  driveSpaceInfo: Array<{ drive: string; freeSpace: number; totalSpace: number }>
  currentSavePath: string
  requiredSpace: string
}) {
  console.log('[CreateTask] 显示驱动器空间对话框')
  const currentWindow = PopUpNS.getCurrentWindow()
  const baseOptions = {
    parentId: currentWindow.id || -1,
    relatePos: PopUpTypes.RelatePosType.CenterParent,
    title: '驱动器空间',
    windowWidth: 680,
    windowHeight: 316,
    currentSavePath: options.currentSavePath,
    requiredSpace: options.requiredSpace,
    replaceView: true, // 该值设置为true，则如果已经存在窗口，则更新数据到当前窗口
    singleton: true, // 关键：确保只有一个创建任务窗口
    driveSpaceInfo: options.driveSpaceInfo,
  }
  const payload = await PopUpNS.popup('drive-space-dialog', baseOptions, {
    resizable: true,
    alwaysOnTop: true,
  })
  if (payload.action === PopUpTypes.Action.OK) {
    const args = payload.args as any
    const picked = args.picked
    console.log('[CreateTask] 用户选择的操作:', picked, 'payload', payload)
    // 8. 选择已勾选的云盘路径
    const selectedPath = args.path
    // selectedDrive.id = '' 选择的是根目录
    if (selectedPath) {
      await downloadPathStore.setCurrentDownloadPath(selectedPath, false)

      // 添加到历史记录
      await DownloadPathNS.addPath(selectedPath)

      // 手动刷新路径信息列表，确保包含最新的路径和磁盘空间信息
      await downloadPathStore.forceRefreshPathInfoList()
    }
  }
}

// 新增：处理右键菜单项点击
const handleContextMenuAction = (action: string) => {
  console.log('执行右键菜单操作:', action)
  // 新组件已经处理了具体的操作，这里只需要记录日志
}

// 磁盘空间不足状态给子组件
provide('isDiskSpaceInsufficient', isDiskSpaceInsufficient)
</script>

<template>
  <div
    v-if="userStore.isLogged"
    :class="{ 'window-blurred': isWindowBlurred }"
  >
    <div
      class="pre-new-task"
      v-if="isAutoCreateTask"
    >
      <!-- 自动创建任务 start -->
      <AutoCreateTaskComponent
        :task-data="allUrlsWithType"
        :data-map="dataMap"
        :options-ext-data="optionsExtData"
        :options="options"
        @close="handleAutoCreateTaskClose"
      ></AutoCreateTaskComponent>
    </div>

    <div v-if="!isAutoCreateTask">
      <NewEditableTask
        :isLoading="isLoading"
        @inputChange="handleInputChangeDelay"
        @downloadButtonSubmit="handleDownloadButtonSubmit"
        @laterSuccess="handleLaterSuccess"
        @laterError="handleLaterError"
        @contextMenuAction="handleContextMenuAction"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '@root/modal-renderer/src/views/create-task/scss/create-task.scss';
</style>
