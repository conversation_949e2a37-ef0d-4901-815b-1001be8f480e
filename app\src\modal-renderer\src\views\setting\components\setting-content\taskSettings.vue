<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Select from '@root/common/components/ui/select/index.vue'
import CheckboxGroup, { ICheckoutGroupOptions } from '@root/common/components/ui/checkbox-group/index.vue';
import { TASK_SETTING_NAME_MAP, getSettingConfig, setSettingConfig } from '@root/modal-renderer/src/views/setting';

// 速度控制
const selectedSpeedControl = ref<string[]>([])
const speedControlOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '全局下载速度低于',
    name: TASK_SETTING_NAME_MAP.TaskCountAutoAdd,
    defaultValue: false,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    labelSuffix: 'KB/s自动增加同时下载任务数',
    action: {
      type: 'input',
      width: 104,
      value: '100',
      name: TASK_SETTING_NAME_MAP.TaskCountAutoAddRate,
      onAction: (value: string, optionName: string) => {
        setSettingConfig(optionName, value)
      }
    },
    tip: '当您使用迅雷进行排队下载时，开启本选项能够尽量\n充分利用您的网络带宽进行下载。本选项合理的设定\n值应为您的网络带宽最大下载速度的80%。假设您最\n大下载速度曾达到1000KB/s，则此项合理的设定值\n应为800KB/s。',
  },
  {
    label: '优先下载小于',
    name: TASK_SETTING_NAME_MAP.OpenNoLimitDownload,
    defaultValue: false,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    labelSuffix: 'MB的任务',
    action: {
      type: 'select',
      width: 104,
      value: '50',
      options: [
        { name: '5', value: '5' },
        { name: '10', value: '10' },
        { name: '20', value: '20' },
        { name: '30', value: '30' },
        { name: '50', value: '50' },
        { name: '100', value: '100' },
      ],
      name: TASK_SETTING_NAME_MAP.NoLimitDownloadFileSize,
      onAction: (value: string, optionName: string) => {
        setSettingConfig(optionName, value)
      }
    },
  }
])

// 任务数
const selectedTaskCount = ref<string>('')

// 其他
const selectedOther = ref<string[]>([])
const otherOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '启动迅雷后自动开始未完成任务',
    name: TASK_SETTING_NAME_MAP.AutoStartUnFinishedTask,
    defaultValue: false,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
  {
    label: '自动将低速任务移动至列尾',
    name: TASK_SETTING_NAME_MAP.AutoSlowTask2Tail,
    defaultValue: false,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
  {
    label: '自动删除“文件不存在”的任务',
    name: TASK_SETTING_NAME_MAP.AutoDeleteNotExistFile,
    defaultValue: false,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
])

const initDefaultValue = async () => {
  const speedControlValue = await Promise.all(speedControlOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)

    if ([TASK_SETTING_NAME_MAP.TaskCountAutoAdd, TASK_SETTING_NAME_MAP.OpenNoLimitDownload].includes(option.name)) {
      if (option.action) {
        const value = await getSettingConfig(option.action?.name!, option.action?.value)
        option.action.value = value as string
      }
    }
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectedSpeedControl.value = speedControlValue as string[]

  const taskCountValue = await getSettingConfig(TASK_SETTING_NAME_MAP.MaxRunningTaskCount, '5') as string
  selectedTaskCount.value = taskCountValue

  const otherValue = await Promise.all(otherOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectedOther.value = otherValue as string[]
}

const handleTaskCountSelect = (value: string) => {
  setSettingConfig(TASK_SETTING_NAME_MAP.MaxRunningTaskCount, value)
}

onMounted(() => {
  initDefaultValue()
})
</script>

<template>
  <CheckboxGroup :options="speedControlOptions" v-model="selectedSpeedControl" title="速度控制" orientation="vertical" />

  <div class="settings-content-divider"></div>
  <div class="settings-content-title">
    任务数
  </div>

  <div class="task-setting-item">
    <span>
      同时下载最大任务数
    </span>
    <Select v-model="selectedTaskCount"
      :options="Array.from({ length: 50 }, (_, i) => ({ name: `${i + 1}`, value: `${i + 1}` }))"
      anchor-class="task-setting-max-download-task-num-select"
      content-class="task-setting-max-download-task-num-select-content" :max-height="160"
      @on-select="handleTaskCountSelect" />
  </div>

  <div class="settings-content-divider"></div>

  <CheckboxGroup title="其他" :options="otherOptions" v-model="selectedOther" orientation="vertical" />

  <div class="settings-content-divider"></div>

</template>

<style scoped lang="scss">
.task-setting-item {
  display: flex;
  gap: 8px;
  align-items: center;

  color: var(--font-font-2, #4E5769);
  font-size: 13px;
  line-height: 22px;
}
</style>

<style lang="scss">
.task-setting-max-download-task-num-select,
.task-setting-max-download-task-num-select-content {
  width: 74px !important;
}

.task-setting-max-download-task-num-select {
  height: 32px !important;
  gap: 4px !important;

  input {
    width: 100%;
    color: var(--font-font-2, #4E5769);
    font-size: 13px;
  }

  i {
    color: var(--font-font-3, #898E97);
  }
}
</style>