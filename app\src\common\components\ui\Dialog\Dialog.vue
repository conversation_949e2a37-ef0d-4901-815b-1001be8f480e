<script setup>
import { computed } from 'vue'
import {
  AlertDialogRoot,
  AlertDialogPortal,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogTrigger,
} from 'reka-ui'

// 导入SVG图标组件
import SvgIcon from './svg-icon.vue'

// 可复用的配置
import { alertDialogVariants } from './variants'

// 设置继承属性为 false，避免属性自动添加到根元素
defineOptions({
  inheritAttrs: false,
})

// 定义组件属性
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: '提示',
  },
  // 内容
  content: {
    type: String,
    default: '',
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确认',
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消',
  },
  // 是否显示取消按钮
  showCancel: {
    type: Boolean,
    default: true,
  },
  // 是否显示右上角关闭按钮
  showCloseButton: {
    type: Boolean,
    default: true,
  },
  // 是否显示触发器按钮
  showTrigger: {
    type: Boolean,
    default: false,
  },
  // 触发器按钮文本
  triggerText: {
    type: String,
    default: '',
  },
  // 是否显示标题图标
  showTitleIcon: {
    type: Boolean,
    default: false,
  },
  // 对话框变体：info, success, warning, error, thunder
  variant: {
    type: String,
    default: 'thunder',
    validator: value => ['info', 'success', 'warning', 'error', 'thunder'].includes(value),
  },
  // 控制对话框的开关状态 - 受控模式核心
  open: {
    type: Boolean,
    default: undefined,
  },
  // 是否处于加载状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 是否阻止默认的关闭行为
  preventDefaultClose: {
    type: Boolean,
    default: false,
  },
  // 类名前缀，用于自定义组件类名
  classPrefix: {
    type: String,
    default: '',
  },
  // 是否允许content包含HTML（注意安全性）
  allowHtml: {
    type: Boolean,
    default: false,
  },
  contentStyle: {
    type: String,
    default: '',
  },
  // 是否显示底部操作按钮区域
  showActions: {
    type: Boolean,
    default: true,
  },
  // 是否是创建任务的弹窗
  isCreateTask: {
    type: Boolean,
    default: false,
  },
  // 对话框宽度
  width: {
    type: [String, Number],
    default: undefined,
  },
  // 对话框高度
  height: {
    type: [String, Number],
    default: undefined,
  },
  // 是否启用拖拽功能
  draggable: {
    type: Boolean,
    default: true,
  },
  // 是否置顶显示
  alwaysOnTop: {
    type: Boolean,
    default: false,
  },
  modal: {
    type: Boolean,
    default: false,
  },
  // 新增position属性
  position: {
    type: String,
    default: 'center',
    // 第一个是水平方向，第二个是垂直方向
    validator: v => ['center', 'top', 'top-left', 'top-right', 'bottom', 'bottom-left', 'bottom-right', 'left', 'left-top', 'left-bottom', 'right', 'right-top', 'right-bottom'].includes(v)
  },
  disableHeaderDraggable: {
    type: Boolean,
    default: false,
  }
})

const positionClasses = computed(() => {
  const [primary, secondary] = props.position.split('-')
  return [primary, secondary || 'center'].map(v => `position-${v}`)
})

// 定义事件
const emit = defineEmits(['confirm', 'cancel', 'close', 'update:open'])

// 处理确认操作
const handleConfirm = () => {
  emit('confirm')
}

// 处理取消操作
const handleCancel = () => {
  emit('cancel')
}

// 处理右上角关闭按钮操作
const handleClose = () => {
  emit('close')
  if (!props.preventDefaultClose) {
    emit('update:open', false)
  }
}

// 根据变体获取样式
const getVariantClasses = computed(() => {
  return alertDialogVariants[props.variant] || alertDialogVariants.info
})

// 计算合并后的内容样式
const computedContentStyle = computed(() => {
  const styles = []

  // 添加用户自定义样式
  if (props.contentStyle) {
    styles.push(props.contentStyle)
  }

  // 添加宽度样式
  if (props.width !== undefined) {
    const widthValue = typeof props.width === 'number' ? `${props.width}px` : props.width
    styles.push(`width: ${widthValue}`)
  }

  // 添加高度样式
  if (props.height !== undefined) {
    const heightValue = typeof props.height === 'number' ? `${props.height}px` : props.height
    styles.push(`height: ${heightValue}`)
  }

  return styles.join('; ')
})

// 处理对话框开关状态变化
const handleOpenChange = value => {
  emit('update:open', value)
}

// 生成带前缀的类名
const getPrefixedClass = className => {
  if (!props.classPrefix) return ''

  return props.classPrefix
    .split(' ')
    .filter(prefix => prefix.trim())
    .map(prefix => `${prefix.trim()}-${className}`)
    .join(' ')
}
</script>

<template>
  <div :class="$attrs.class">
    <AlertDialogRoot
      :open="open"
      :modal="modal"
      @update:open="handleOpenChange"
      :preventDefaultClose="preventDefaultClose"
    >
      <slot
        v-if="showTrigger"
        name="trigger"
      >
        <AlertDialogTrigger :class="['dialog-trigger', getPrefixedClass('trigger')]">
          <slot name="trigger-content">{{ triggerText }}</slot>
        </AlertDialogTrigger>
      </slot>
      <AlertDialogPortal class="dialog-portal" :class="{ 'draggable': !disableHeaderDraggable }">
        <AlertDialogOverlay
          :class="[
            'dialog-overlay none-draggable',
            getPrefixedClass('overlay'),
            { 'always-on-top': props.alwaysOnTop }
          ]"
        />
        <AlertDialogContent
          :class="[
            'dialog-content',
            { draggable: props.draggable },
            `variant-${props.variant}`,
            getPrefixedClass('content'),
            { 'create-task-container': props.isCreateTask },
            { 'always-on-top': props.alwaysOnTop },
            ...positionClasses,
          ]"
          :style="computedContentStyle"
        >
          <!-- 右上角关闭按钮 -->
          <AlertDialogCancel
            v-if="showCloseButton"
            :class="['dialog-close-button none-draggable', getPrefixedClass('close-button')]"
            @click="handleClose"
            :disabled="loading"
          >
            <SvgIcon type="close" />
          </AlertDialogCancel>
          <AlertDialogTitle
            :class="['dialog-title', getVariantClasses.title, getPrefixedClass('title')]"
          >
            <slot
              v-if="showTitleIcon"
              name="title-icon"
            >
              <SvgIcon
                v-if="variant === 'info'"
                type="info"
              />
              <SvgIcon
                v-else-if="variant === 'success'"
                type="success"
              />
              <SvgIcon
                v-else-if="variant === 'warning'"
                type="warning"
              />
              <SvgIcon
                v-else-if="variant === 'error'"
                type="error"
              />
              <SvgIcon
                v-else-if="variant === 'thunder'"
                type="thunder"
              />
            </slot>
            <span>{{ title }}</span>
          </AlertDialogTitle>
          <AlertDialogDescription
            :class="[
              'dialog-description',
              { 'none-draggable': props.draggable },
              getPrefixedClass('description'),
            ]"
          >
            <slot>
              <div
                v-if="allowHtml"
                v-html="content"
              ></div>
              <div v-else>{{ content }}</div>
            </slot>
          </AlertDialogDescription>
          <div
            v-if="showActions"
            :class="[
              'dialog-actions',
              getPrefixedClass('actions'),
              { 'none-draggable': props.draggable },
            ]"
          >
            <slot name="actions">
              <div :class="['dialog-actions-left', getPrefixedClass('actions-left')]">
                <!-- 预留左侧操作区域插槽 -->
                <slot name="left-action"></slot>
              </div>

              <div :class="['dialog-actions-right', getPrefixedClass('actions-right')]">
                <!-- 自定义右侧操作区域插槽 -->
                <slot name="right-action">
                  <!-- 默认的取消和确认按钮 -->
                  <!-- 根据 preventDefaultClose 属性决定使用 button 还是 AlertDialogCancel -->
                  <button
                    v-if="showCancel && preventDefaultClose"
                    :class="[
                      'dialog-btn',
                      'dialog-cancel',
                      getVariantClasses.cancel,
                      getPrefixedClass('btn'),
                      getPrefixedClass('cancel'),
                    ]"
                    @click="handleCancel"
                    :disabled="loading"
                  >
                    {{ cancelText }}
                  </button>
                  <AlertDialogCancel
                    v-else-if="showCancel"
                    :class="[
                      'dialog-btn',
                      'dialog-cancel',
                      getVariantClasses.cancel,
                      getPrefixedClass('btn'),
                      getPrefixedClass('cancel'),
                    ]"
                    @click="handleCancel"
                    :disabled="loading"
                  >
                    {{ cancelText }}
                  </AlertDialogCancel>
                  <button
                    v-if="preventDefaultClose"
                    :class="[
                      'dialog-btn',
                      'dialog-action',
                      getVariantClasses.action,
                      getPrefixedClass('btn'),
                      getPrefixedClass('action'),
                    ]"
                    @click="handleConfirm"
                    :disabled="loading"
                  >
                    <slot name="confirm-content">{{ loading ? '处理中...' : confirmText }}</slot>
                  </button>
                  <AlertDialogAction
                    v-else
                    :class="[
                      'dialog-btn',
                      'dialog-action',
                      getVariantClasses.action,
                      getPrefixedClass('btn'),
                      getPrefixedClass('action'),
                    ]"
                    @click="handleConfirm"
                    :disabled="loading"
                  >
                    <slot name="confirm-content">{{ loading ? '处理中...' : confirmText }}</slot>
                  </AlertDialogAction>
                </slot>
              </div>
            </slot>
          </div>
        </AlertDialogContent>
      </AlertDialogPortal>
    </AlertDialogRoot>
  </div>
</template>

<style lang="scss" scoped>
@import '@root/common/assets/css/color-variable.scss';
// @import '@root/common/assets/css/main.scss';
@import './Dialog.scss';
</style>
