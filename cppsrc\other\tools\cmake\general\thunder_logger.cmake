MACRO(THUNDER_LOG_INFO msg)
	message (STATUS "*********** [THUNDER Info] ${msg} *********")
ENDMACRO()

MACRO(THUNDER_LOG_WARN msg)
	message (WARNING "*********** [THUNDER Warning] ${msg} *********")
ENDMACRO()

MACRO(THUNDER_LOG_ERROR msg)
	message (FATAL_ERROR "*********** [THUNDER Error] ${msg} *********")
ENDMACRO()

# path is relative for THUNDER git root path 
MACRO(THUNDER_LOG_PROJECT_REVISION_INFO path)
	set(${PROJECT_NAME}_REVISION "unknow")
	if (NOT GIT_FOUND)
		find_package(Git)
	endif ()

	if (GIT_FOUND)
		execute_process(COMMAND ${GIT_EXECUTABLE} log -1 --pretty=format:%h -- ${path}
			WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
			OUTPUT_VARIABLE  ${PROJECT_NAME}_REVISION
			OUTPUT_STRIP_TRAILING_WHITESPACE
		)
	endif ()
	THUNDER_LOG_INFO("${PROJECT_NAME}'s revision is ${${PROJECT_NAME}_REVISION}")
	unset (${PROJECT_NAME}_REVISION)
ENDMACRO()