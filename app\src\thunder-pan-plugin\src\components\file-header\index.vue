<script setup lang="ts">
import TDCheckbox from '@root/common/components/ui/checkbox/index.vue'
import SorterIcon from '@/components/sorter-icon/index.vue'
import Toolbar, { IMenuItem, IToolbarOperationType } from './toolbar.vue'

import { TypeDriveSortInfo } from '@/manager/drive-file-manager';
import { SortFunctionMapType, SortOrderType } from '@root/common/thunder-pan-manager/pan-sdk/utils/file';
import { computed } from 'vue';

const props = withDefaults(defineProps<{
  selectAll: boolean
  selectedFiles?: any[]
  indeterminate?: boolean
  toolbarPrimaryOperation: IToolbarOperationType[]
  toolbarDropdownMenuList?: IMenuItem[]
  sortInfo?: TypeDriveSortInfo
  enableNameSorter?: boolean
  enableSizeSorter?: boolean
  enableTimeSorter?: boolean
}>(), {
  selectedFiles: () => ([]),
  sortType: 'time',
  enableNameSorter: true,
  enableSizeSorter: true,
  enableTimeSorter: true,
})

const emit = defineEmits<{
  (e: 'cleanPicked'): void
  (e: 'checkChange', selectAll: boolean): void
  (e: 'toolbarItemClick', itemKey: string, files: any[]): void
  (e: 'sorterClick', type: TypeDriveSortInfo): void
}>()

const enableSorter = computed(() => !props.selectedFiles.length)

function handleCleanPicked () {
  emit('cleanPicked')
}

function handleCheckChange (isCheck: boolean) {
  emit('checkChange', !props.selectAll || props.indeterminate)
}

function handleToolbarMenuItemClick (itemKey) {
  emit('toolbarItemClick', itemKey, props.selectedFiles)
}

function handleSorterClick (type: SortFunctionMapType) {
  if (!enableSorter.value) return

  const info: TypeDriveSortInfo = { type, order: SortOrderType.DESC }

  if (props.sortInfo?.type === type) {
    info.order = -1 * props.sortInfo.order
  }

  emit('sorterClick', info)
}
</script>

<template>
  <div class="file-list-header">
    <div class="file-name-sorter">
      <!-- 复选框 -->
      <div class="checkbox">
        <TDCheckbox label="" :model-value="selectAll" :indeterminate="indeterminate" @update:model-value="handleCheckChange" />
      </div>

      <div class="text">
        <div
          v-if="!selectedFiles.length"
          class="file-sorter-wrapper"
          :class="{
            'is-visible': enableSorter,
            'enable-sorter': enableNameSorter
          }"
          @click="handleSorterClick('name')"
        >
          <span>文件名</span>

          <SorterIcon v-if="enableNameSorter" :disabled="sortInfo?.type !== 'name'" :asc="sortInfo?.order === SortOrderType.ASC" />
        </div>

        <div v-else class="selected">
          <span>已选中 {{ selectedFiles.length }} 项</span>
          <span class="cancel-selected" @click="handleCleanPicked">取消选中</span>
        </div>
      </div>

      <!-- 多选快捷操作工具栏 -->
      <Toolbar
        :is-show="!!selectedFiles.length"
        :primary-operation="toolbarPrimaryOperation"
        :dropdown-menu-list="toolbarDropdownMenuList"
        @menu-item-click="handleToolbarMenuItemClick"
      />
    </div>

    <!-- 文件大小 -->
    <div
      class="file-sorter-wrapper file-size-sorter"
      :class="{
        'is-visible': enableSorter,
        'enable-sorter': enableSizeSorter
      }"
      @click="handleSorterClick('size')"
    >
      <span>大小</span>

      <SorterIcon v-if="enableSizeSorter" :disabled="sortInfo?.type !== 'size'" :asc="sortInfo?.order === SortOrderType.ASC" />
    </div>

    <!-- 修改时间 -->
    <div
      class="file-sorter-wrapper file-time-sorter"
      :class="{
        'is-visible': enableSorter,
        'enable-sorter': enableTimeSorter
      }"
      @click="handleSorterClick('modified_time')"
    >
      <span>修改时间</span>

      <SorterIcon v-if="enableTimeSorter" :disabled="sortInfo?.type !== 'modified_time'" :asc="sortInfo?.order === SortOrderType.ASC" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.file-list-header {
  flex-shrink: 0;
  height: 40px;
  margin: 0 28px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 24px;
  font-size: 12px;
  line-height: 22px;
  color: var(--font-font-1, #272E3B);

  .file-name-sorter {
    flex-grow: 1;
    display: flex;
    align-items: center;

    .checkbox {
      margin-right: 12px;
    }

    .text {
      display: flex;

      .selected {
        display: flex;
        gap: 8;

        .cancel-selected {
          cursor: pointer;
          color: var(--primary-primary-default);
        }
      }
    }
  }

  .file-size-sorter {
    flex-shrink: 0;
    width: 58px;
  }

  .file-time-sorter {
    flex-shrink: 0;
    width: 110px;
  }

  .file-sorter-wrapper {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: default;
    pointer-events: none;
    visibility: hidden;

    &.is-visible {
      visibility: visible;
    }

    &.enable-sorter {
      cursor: pointer;
      pointer-events: all;
    }
  }
}
</style>
