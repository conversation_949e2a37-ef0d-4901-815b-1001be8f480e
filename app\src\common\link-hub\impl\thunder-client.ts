/// <reference path="./thunder-client-api.d.ts" />
// 假设 ThunderClientAPI.node 提供了实际的实现
import * as path from 'path'
import { env } from "@root/common/env"
import requireNodeFile from '../../require-node-file'
import {
    GetXxxNodePath,
    GetProfilesPath
} from '../../xxx-node-path'
let ThunderInterface: any
try {
    ThunderInterface = requireNodeFile(path.join(GetXxxNodePath(), 'linkhub/ThunderClientAPI.node'));
} catch (error) {
}
import {
    AccountHelper,
} from '../../account/impl/accountHelper'
import {
    AccountHelperEventKey
} from '@root/common/account/account-type'
import {
    IUserProfile,
} from '@xbase/electron_auth_kit'

import {
    EventEmitter,
    EventEmitterListener,
} from '@xbase/electron_common_kit'
import { AsyncRemoteCall } from '@root/common/renderer-async-remote-call';
import { ThunderHelper } from '@root/common/thunder-helper';

interface IUserBaseInfo extends IUserProfile {
    profile_folder?: string
    app_config: {
        env: string;
        user_agent?: string;
        client_id?: string;
        base_urls?: Map<string, string>;
        server_proxy?: Map<string, string>;
        peerid?: string;
    }
}
enum UserInfoChangedReason {
    UNKNOWN = 0,
    LOGIN_STATUS_CHANGED = 1,
    PASSWORD_CHANGED = 2,
    EMAIL_CHANGED = 3,
    // ...其他原因
}

export class UserAccountProxy {
    private accountInfoProxy_: any
    constructor() {
        if (ThunderInterface?.auth) {
            this.accountInfoProxy_ = new ThunderInterface.auth.UserAccountProxy()
            this.accountInfoProxy_.setCallbacks(
                this.requestCredentialsInfo.bind(this),
                this.requestCaptchaToken.bind(this))
        }
    }
    public init(): void {
        // 监听 AccountHelper 的事件
        AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, async () => {
            this.getUserInfo(false).then((userInfo: IUserBaseInfo) => {
                this.accountInfoProxy_?.setLoginStatus?.(1)
                this.accountInfoProxy_?.onUserDataChanged?.(UserInfoChangedReason.LOGIN_STATUS_CHANGED, JSON.stringify(userInfo))
            }
            ).catch((error) => {
                console.error('获取用户信息失败:', error)
            })
        })

        AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_FAILURE, async () => {
            this.accountInfoProxy_?.setLoginStatus?.(2)
        })

        AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_OUT, async () => {
            this.accountInfoProxy_?.setLoginStatus?.(3)
            this.accountInfoProxy_?.onUserDataChanged(UserInfoChangedReason.LOGIN_STATUS_CHANGED, JSON.stringify({}))

        })
        AccountHelper.getInstance().attachEvent(AccountHelperEventKey.USER_INFO_CHANGE, async () => {
            this.getUserInfo(false).then((userInfo: IUserBaseInfo) => {
                this.accountInfoProxy_?.onUserDataChanged?.(UserInfoChangedReason.UNKNOWN, JSON.stringify(userInfo))
            }
            ).catch((error) => {
                console.error('获取用户信息失败:', error)
            })
        })
    }

    // 给c++调用接口
    public requestCredentialsInfo(reqIdx: number, renew: boolean): void {
        this.getCredentialsInfo(renew)
            .then((result) => {
                this.accountInfoProxy_?.setCredentialsInfo?.(reqIdx, result)
            })
            .catch((error) => {
                this.accountInfoProxy_?.setErrorInfo?.(reqIdx, error.message)
            });
    }
    public requestCaptchaToken(reqIdx: number, method: string, url: string, captcha_meta: any, renew: boolean): void {
        this.getCaptchaToken(method, url, captcha_meta, renew)
            .then((result) => {
                this.accountInfoProxy_?.setCaptchaToken?.(reqIdx, result)
            })
            .catch((error) => {
                this.accountInfoProxy_?.setErrorInfo?.(reqIdx, error.message)
            });
    }

    async getUserInfo(refresh: boolean): Promise<IUserBaseInfo> {
        const userInfo = await AccountHelper.getInstance().account.getUserProfile({ renew: refresh }) as IUserBaseInfo;
        userInfo.profile_folder = GetProfilesPath();
        userInfo.app_config = userInfo.app_config || {};
        userInfo.app_config.env = env as string;
        const contents: any = await AsyncRemoteCall.GetInstance().getCurrentWebContents();
        // let session: any = mainWnd.webContents.session;
        const session: any = contents.session;
        userInfo.app_config.user_agent = await session.getUserAgent();
        console.log('thunder client getUserInfo ua=', userInfo.app_config.user_agent,',env=',userInfo.app_config.env)
        userInfo.app_config.peerid = ThunderHelper.getPeerId();
        userInfo.app_config.client_id = 'XW-G4v1H72tgfJym'
        const base_urls = {
            prod_url: 'https://api-gateway-pan.xunlei.com',
            test_url: 'https://test-api-gateway-pan.xunlei.com',
            debug_url: 'https://api-gateway-pan.xunlei.com',
            
            // 按照模块配置url信息
            prod_url_linkhub: 'https://api-gateway-pan.xunlei.com',
            test_url_linkhub: 'https://test-api-gateway-pan.xunlei.com',
            debug_url_linkhub: 'https://api-gateway-pan.xunlei.com',

            prod_url_playback: 'https://api-shoulei-ssl.xunlei.com',
            test_url_playback: 'http://test.api-shoulei-ssl.xunlei.com',
            debug_url_playback: 'https://api-shoulei-ssl.xunlei.com',
        };
        userInfo.app_config.base_urls = base_urls as any;
        /* 后续通过设置页配置获取 
        const server_proxy = {
            proxy_info_server: '127.0.0.1',
            proxy_info_user: '',
            proxy_info_password: '',
            proxy_info_pacurl: '',
            proxy_info_bypasslist: '',
            proxy_info_mode: '',
            proxy_info_type: '1',
            proxy_info_port: '8888',
        };
        userInfo.app_config.server_proxy = server_proxy as any;
        console.log('======================userInfo', userInfo )
        */
        
        return userInfo
    }

    async getCredentialsInfo(refresh: boolean): Promise<string> {
        return AccountHelper.getInstance().getBearerAuthorization()
        /*
        const credentials = await AccountHelper.getInstance().account.getCredentials({ renew: refresh })
        return credentials.toJsonString()
        */
    }
    private getCaptchaAction(method: string, url: string): string {
        if (method.toLocaleUpperCase() === 'GET') {
            return 'GET:CAPTCHA_TOKEN'
        }

        const matchUrl = url.split(/(?<=xunlei\.com)/g)
        const path = matchUrl.length === 2 ? matchUrl[1] : null

        if (path === null) {
            return 'GET:CAPTCHA_TOKEN'
        }
        return `${method}:${path}`
    }

    async getCaptchaToken(method: string, url: string, captcha_meta: any, refresh: boolean): Promise<string> {
        const act = this.getCaptchaAction(method || 'GET', url)
        const token = await AccountHelper.getInstance().account.captcha.getCaptchaToken({
            action: act, meta: captcha_meta, renew: refresh
        })
        return token
    }
}


export enum EventDispatcherEventKey {
    SYNC_CLIENT_EVENT_MESSAGE_ARRIVED = 'EventDispatcher_EventKey_Sync_Client_Message_Arrived',
}
export class EventDispatcher implements ThunderClientAPI.base.IEventDispatcher {
    private client_: any;
    private readonly _eventEmitter: EventEmitter = new EventEmitter()
    constructor(clientId: string) {
        console.info('EventDispatcher constructor called with clientId:', clientId);
        this.client_ = ThunderInterface?.base?.getEventDispatcher?.(clientId);
        this.setup((type, detail) => {
            console.info('emit:', type, " event:", detail);
            this._eventEmitter.emit(EventDispatcherEventKey.SYNC_CLIENT_EVENT_MESSAGE_ARRIVED,
                { type, detail });
        }).then((result: boolean) => {
            console.info('EventDispatcher setup result:', result);
        }).catch((error: any) => {
            console.error('EventDispatcher setup failed:', error);
        });
    }
    async setup<T extends keyof ThunderClientAPI.base.EventDetails>(
        callback: (type: T, event: ThunderClientAPI.base.Event<T>) => void,
    ): Promise<boolean> {
        return await this.client_?.setup(callback);
    }

    attachEvent<T extends keyof ThunderClientAPI.base.EventDetails>(event: T,
        listener: (payload: ThunderClientAPI.base.EventDetails[T]) => void): void {
        this._eventEmitter.on(event, listener);
    }

    detachEvent<T extends keyof ThunderClientAPI.base.EventDetails>(event: T,
        listener: (payload: ThunderClientAPI.base.EventDetails[T]) => void): void {
        this._eventEmitter.off(event, listener);
    }

}

export class ThunderClient implements ThunderClientAPI.app.IThunderClient {
    private client_: any;
    private eventDispatcher_?: EventDispatcher;
    private accountProxy_?: UserAccountProxy
    constructor(clientId?: string) {
        const _clientId = clientId ? clientId : '';
        this.client_ = ThunderInterface?.app?.getThunderClient?.(_clientId);
    }

    getClientId(): string {
        return this.client_?.getClientId?.();
    }

    async updateUserAccount(account: ThunderClientAPI.auth.UserAccountInfo): Promise<ThunderClientAPI.auth.IVerifyAccountResult> {
        return await this.client_?.updateUserAccount?.(account)
    }

    async resetUserAccount(): Promise<boolean> {
        return await this.client_?.resetUserAccount?.()
    }

    hasUserAccount(): boolean {
        return this.client_?.hasUserAccount?.()
    }

    getBizProvider(): ThunderClientAPI.biz.IBizProvider {
        return this.client_?.getBizProvider?.() as ThunderClientAPI.biz.IBizProvider;
    }

    getEventDispatcher(): ThunderClientAPI.base.IEventDispatcher {
        if (!this.eventDispatcher_) {
            this.eventDispatcher_ = new EventDispatcher(this.getClientId());
        }
        return this.eventDispatcher_;
    }

    getUserAccountProxy(): UserAccountProxy {
        if (!this.accountProxy_) {
            this.accountProxy_ = new UserAccountProxy();
        }
        return this.accountProxy_;
    }
}

let thunderClientInstance: ThunderClient | null = null;
export function getThunderClient(clientId?: string): ThunderClient {
    if (!thunderClientInstance) {
        thunderClientInstance = new ThunderClient(clientId);
    }
    return thunderClientInstance;
}