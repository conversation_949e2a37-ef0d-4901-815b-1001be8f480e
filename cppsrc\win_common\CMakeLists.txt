﻿if (${CMAKE_SYSTEM_NAME} STREQUAL Windows)
    cmake_minimum_required(VERSION 3.16)
else()
    message(STATUS, "common cmake version,not support platform: ${CMAKE_SYSTEM_NAME}")
endif()

project(win_common)
#set(CMAKE_CXX_STANDARD 20)
set(CMAKE_INCLUDE_CURRENT_DIR ON)
set_property(GLOBAL PROPERTY USE_FOLDERS ON)

file(GLOB_RECURSE ALL_SOURCE ${CMAKE_CURRENT_SOURCE_DIR}/*)
foreach(fileItem ${ALL_SOURCE})
    # Get the directory of the source file
    get_filename_component(PARENT_DIR "${fileItem}" DIRECTORY)
    # Remove common directory prefix to make the group
    string(REPLACE "${CMAKE_CURRENT_SOURCE_DIR}" "" GROUP "${PARENT_DIR}")
    # Make sure we are using windows slashes
    string(REPLACE "/" "\\" GROUP "${GROUP}")
    # Group into "Source Files" and "Header Files"
    set(GROUP "${GROUP}")
    source_group("${GROUP}" FILES "${fileItem}")
endforeach()

include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/third_part/include)
include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/libuv1.44/include)
include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/common/include)

add_library(${PROJECT_NAME} STATIC ${ALL_SOURCE})
target_link_libraries(${PROJECT_NAME} common libuv)

if (${CMAKE_SYSTEM_NAME} STREQUAL Windows)
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Zi /MTd")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi /MT")
    set(VS_STARTUP_PROJECT ${PROJECT_NAME})
    add_definitions(
                -D_CRT_SECURE_NO_WARNINGS
                -D_CRT_NONSTDC_NO_WARNINGS
                -DWIN32
                -D_WINDOWS
    			-DWIN32_LEAN_AND_MEAN # 解决windows.h和winsock2.h的冲突问题
                -DRAPIDJSON_HAS_STDSTRING
                #-DSPDLOG_WCHAR_TO_UTF8_SUPPORT #日志支持宽字符使用
                )

                set_target_properties(${PROJECT_NAME} PROPERTIES
                COMPILE_OPTIONS "/MT$<$<CONFIG:Debug>:d>"
            )

    #set_target_properties(${PROJECT_NAME} PROPERTIES FOLDER XLLibrary)
    target_compile_options(${PROJECT_NAME} PRIVATE 
        /wd4828
        /wd4091
    )
else()
    message(FATAL_ERROR, "common,not support platform: ${CMAKE_SYSTEM_NAME}")
endif()