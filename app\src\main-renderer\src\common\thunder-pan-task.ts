import { taskExtraFunc } from '@/common/task-extra'

export const userDataPan = {
  key: 'userDataPanExtendInfo',
  field: 'panTaskInfo',
  defaultVal: '',
}

export interface PanTaskInfo {
  phase: string
  path: string
  panFileId: string
  panTaskId: string
}

export enum PhaseStatus {
  pending = 'PHASE_TYPE_PENDING',
  running = 'PHASE_TYPE_RUNNING',
  error = 'PHASE_TYPE_ERROR',
  complete = 'PHASE_TYPE_COMPLETE'
}

export async function getUserDataPan(taskId: number, uid: string): Promise<PanTaskInfo> {
  const info = await taskExtraFunc.getUserData(
    taskId,
    userDataPan.key,
    `${userDataPan.field}_${uid}`,
    userDataPan.defaultVal
  )
  return initPanInfos(info)
}

export async function setUserDataPan(taskId: number, uid: string, val: PanTaskInfo) {
  return taskExtraFunc.getUserData(taskId, userDataPan.key, `${userDataPan.field}_${uid}`, JSON.stringify(val))
}

/**
 * 
 * @param taskId 下载taskId
 * @param uid 用户uid
 * @param key 存储key
 * @param value 存储值
 * @returns 
 */
export async function setUserDataPanByKey(taskId: number, uid: string, valList: {[key: string]: any}) {
  let info = await getUserDataPan(taskId, uid)
  info = {...info, ...valList}
  console.log('>>>>>>>>> info', info)
  return taskExtraFunc.setUserData(taskId, userDataPan.key, `${userDataPan.field}_${uid}`, JSON.stringify(info))
}

export async function setUserDataGroupTaskPanByKey(taskId: number, uid: string, valList: {[key: string]: any}[]) {

}

export function initPanInfos (info: string| null): PanTaskInfo {
  const initData = {
    phase: '',
    progress: 0,
    path: '',
    panFileId: '',
    panTaskId: ''
  }
  if (!info) {
    return initData
  } else {
    try {
      const parseInfo = JSON.parse(info)
      const { phase='', path='', panFileId='', panTaskId='' } = parseInfo
      return { phase, path, panFileId, panTaskId }
    } catch (error) {
      console.log('>>>>>>>>>>> 无法解析', error)
      return initData
    }
  }
}

/** 选中保存路径 */
export function selectPanPath(saveInfo) {
  console.log('>>>>>>>>> saveInfo', saveInfo)
  const data = saveInfo.data || {}
  const panSavePathId = data.selectedItem.id
  const panSavePath = data.selectedItem.name
  let savePathList = [] as string[]
  let savePathIdList = [] as string[]
  for (const path of data.path) {
    savePathList.push(path.name)
    savePathIdList.push(path.id)
  }
  savePathList.push(panSavePath)
  savePathIdList.push(panSavePathId)
  console.log('>>>>>>>>>>>>> panTaskData', savePathIdList, savePathList)
  return savePathList.join('//')
}
