import { PopUpNS } from '@root/common/pop-up'

/**
 * 获取 DOM 元素距离屏幕右侧的距离
 * @param element DOM 元素
 * @returns Promise<number> 距离屏幕右侧的像素值
 */
export async function getDistanceToScreenRight(element: HTMLElement): Promise<number> {
  try {
    if (!element) {
      console.warn('[ScreenHelper] DOM 元素未找到')
      return 0
    }

    // 获取元素的客户端边界矩形
    const rect = element.getBoundingClientRect()
    
    // 获取元素右边缘的屏幕坐标
    const elementRightPoint = {
      x: rect.right,
      y: rect.top,
    }

    // 转换为窗口坐标（屏幕坐标）
    const screenPoint = await PopUpNS.toWindowPoint(elementRightPoint)
    
    if (!screenPoint) {
      console.warn('[ScreenHelper] 无法获取屏幕坐标')
      return 0
    }

    // 获取屏幕宽度
    const screenWidth = window.screen.width
    
    // 计算距离屏幕右侧的距离
    const distanceToRight = screenWidth - screenPoint.x
    
    console.log('[ScreenHelper] 元素右边缘屏幕坐标:', screenPoint.x)
    console.log('[ScreenHelper] 屏幕宽度:', screenWidth)
    console.log('[ScreenHelper] 距离屏幕右侧距离:', distanceToRight)
    
    return Math.max(0, distanceToRight) // 确保返回值不为负数
  } catch (error) {
    console.error('[ScreenHelper] 获取距离屏幕右侧距离失败:', error)
    return 0
  }
}

/**
 * 检查 DOM 元素距离屏幕右侧的距离是否大于指定宽度
 * @param element DOM 元素
 * @param width 要检查的宽度值（像素）
 * @returns Promise<boolean> 如果距离大于指定宽度返回true，否则返回false
 */
export async function isDistanceToRightGreaterThan(element: HTMLElement, width: number): Promise<boolean> {
  try {
    if (!element) {
      console.warn('[ScreenHelper] DOM 元素未找到')
      return false
    }

    // 使用已有的函数获取距离
    const distanceToRight = await getDistanceToScreenRight(element)
    
    // 检查是否大于指定宽度
    const isGreaterThan = distanceToRight > width
    
    console.log('[ScreenHelper] 距离屏幕右侧距离:', distanceToRight)
    console.log(`[ScreenHelper] 距离是否大于${width}px:`, isGreaterThan)
    
    return isGreaterThan
  } catch (error) {
    console.error('[ScreenHelper] 检查距离屏幕右侧距离失败:', error)
    return false
  }
}
