import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { mainRendererContext } from '@xunlei/node-net-ipc/dist/ipc-base';
import { EventEmitter } from 'events'
import {
  ResponseError,
} from '@xbase/electron_common_kit'
import {
  AccountHelperEventKey,
} from '@root/common/account/account-type'

export class AccountCallApiProxyImplWithIpcServer {
  private eventContainor: EventEmitter = new EventEmitter();

  private on(event: AccountHelperEventKey): number {
    return client.attachServerEvent(event, (context: unknown, ...args: any[]) => {
      console.log('attach login event fire', event, ...args);
      if (event === AccountHelperEventKey.DEVICE_AUTHORIZATION && args?.[3]) {
        const errObj = args[3];
        args[3] = ResponseError.fromJsonString(JSON.stringify(errObj)) ?? errObj;
      }
      this.eventContainor.emit(event, ...args);
    });
  }

  private off(event: AccountHelperEventKey, id: number): void {
    console.log('detach login ipc event', event, id);
    client.detachServerEvent(event, id);
  }

  private ipc_event_ids: { [event: string]: number } = {};

  public Init() {
    // this.on(AccountHelperEventKey.SDK_INIT_READY);
    // this.on(AccountHelperEventKey.REFRESH_CREDENTIALS);
    // this.on(AccountHelperEventKey.SIGN_IN_SUCCESS);
    // this.on(AccountHelperEventKey.SIGN_OUT);
    // this.on(AccountHelperEventKey.USER_INFO_CHANGE);
    // this.on(AccountHelperEventKey.SYNC_CLIENT_EVENT_CONNECTED);
    // this.on(AccountHelperEventKey.SYNC_CLIENT_EVENT_MESSAGE_ARRIVED);
    // this.on(AccountHelperEventKey.DEVICE_AUTHORIZATION);
  }
  public async CallApi(name: string, ...args: any[]): Promise<{ bSucc: boolean, result?: any, error?: any }> {
    try {
      const res = await client.callRemoteClientFunction(mainRendererContext, name, ...args);
      if (!res?.[1]) {
        return {
          bSucc: true,
          result: res[0],
        }
      }
      const errObj = res[1];
      return {
        bSucc: false,
        error: ResponseError.fromJsonString(JSON.stringify(errObj)) ?? errObj,
      }
    } catch (err) {
      return {
        bSucc: false,
        error: err,
      }
    }
  }

  public AttachServerEvent(name: AccountHelperEventKey, callback: (...args: any[]) => void) {
    const count = this.eventContainor.listenerCount(name);
    console.log('attach login event count', name, count);
    if (count === 0) {
      this.ipc_event_ids[name] = this.on(name);
    }
    this.eventContainor.on(name, callback);
  }

  public DetachServerEvent(name: AccountHelperEventKey, callback: (...args: any[]) => void): void {
    this.eventContainor.off(name, callback);
    const count = this.eventContainor.listenerCount(name);
    console.log('detach login event count', name, count);
    if (count === 0) {
      const id = this.ipc_event_ids[name];
      if (id) {
        this.off(name, id);
      }
    }
  }
}