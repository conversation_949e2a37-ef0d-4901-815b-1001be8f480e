<script setup lang="ts">
import ShareList from './list/index.vue'
import Loading from '@root/common/components/ui/loading/index.vue'
import EmptyHolder from '@/components/empty-holder/index.vue'

import { computed, nextTick, onMounted, ref, useTemplateRef } from 'vue'
import { SharePageManager } from '../../manager/share-page-manager'
import { GlobalEventHelper } from '@/utils/global-event-helper'
import { useUserStore } from '@/store/user-store'
import { PanBusinessHelper } from '@/utils/business-helper'

const { isSignin } = useUserStore()
const shareData = SharePageManager.getInstance().getCurrentData()

const isLoading = ref(false)

const shareList = computed(() => shareData.list)
const pickedIds = computed(() => shareData.pickedIds)
const ShareListVm = useTemplateRef('ShareListVm')

function handleCleanPicked () {
  SharePageManager.getInstance().cleanPicked()
}

async function handleListScrollEnd() {
  if (shareData.listPageToken) {
    await SharePageManager.getInstance().getList()
    // 手动更新一下滚动条，避免 UI 滚动有问题
    await nextTick()
    ShareListVm.value?.updateScroller()
  }
}

async function handleRefresh () {
  isLoading.value = true
  await SharePageManager.getInstance().getList({ reset: true })
  isLoading.value = false
}

function handleOpenSigninDialog () {
  PanBusinessHelper.getInstance().openSigninDialog()
}

onMounted(() => {
  handleRefresh()
  // 全局事件监听
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.SHARE_LIST_CLEAN_PICKED, () => {
    handleCleanPicked()
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.SHARE_LIST_REFRESH, () => {
    handleRefresh()
  })
})
</script>

<template>
  <div class="share-container">
    <div class="header">
      <div class="title">我的分享</div>
    </div>

    <div class="body">
      <EmptyHolder
        v-if="!isSignin"
        text="请登录后查看"
        button-text="登录"
        @button-click="handleOpenSigninDialog"
      ></EmptyHolder>

      <template v-else>
        <Loading v-if="isLoading" />

        <EmptyHolder v-else-if="!shareList.length"></EmptyHolder>

        <ShareList
          v-else
          ref="ShareListVm"
          :picked-ids="pickedIds"
          :file-list="shareList"
          @scroll-end="handleListScrollEnd"
        />
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.share-container {
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  .header {
    flex-shrink: 0;
    height: 64px;
    padding: 11px 40px 11px 36px;

    .title {
      padding: 4px;
      font-size: 26px;
      font-weight: 700;
    }
  }

  .body {
    flex-grow: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    position: relative;
  }
}
</style>
