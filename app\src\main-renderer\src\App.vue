<script setup lang="ts">
import { onBeforeUnmount, onMounted } from 'vue'
import { PluginLoader } from '@/common/plugin-loader'
import Main from './views/index.vue';
import { MainRenderUIHelper } from '@root/common/main-renderer-ui-helper';

onMounted(() => {
  PluginLoader.getInstance().loadConfig()
})

onBeforeUnmount(() => {
  MainRenderUIHelper.getInstance().reset()
})
</script>

<template>
  <Main />
</template>


<style lang="scss" src="./assets/css/xl-icon.scss"></style>

<style>
::-webkit-scrollbar {
  width: 10px;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-neutral-350);
  background-clip: content-box;
  border: 2px solid transparent;
  border-radius: 40px;
}

::-webkit-scrollbar-track {
  border-radius: 40px;
}
</style>