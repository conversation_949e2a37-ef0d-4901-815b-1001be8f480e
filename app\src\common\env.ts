import dotenv from 'dotenv';
import path from 'path';

dotenv.config({ path: path.resolve(__dirname, '../../.env') });

export const isDev = process.env.NODE_ENV === 'development';

export const port = Number(process.env.PORT) || 9527;

export const platform = {
  isWindows: process.platform === "win32",
  isMacOS: process.platform === "darwin",
  isLinux: process.platform === "linux"
};

export const isShowLogger = true // !!process.env.PUBLIC_LOGGER

export type TEnv = 'prod' | 'test'
const {THUNDER_ENV} = process.env;
export const env: TEnv = !!THUNDER_ENV ? THUNDER_ENV as TEnv : 'prod'
console.log('======================', env, THUNDER_ENV, process.env )
// 内网环境一键配置
// export const env: TEnv = 'test'

export const isProcess = typeof window === 'undefined'
export const isRenderer = !isProcess 