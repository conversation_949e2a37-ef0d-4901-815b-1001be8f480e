import DriveApis from './services/drive'
import FileApis from './services/file'
import SafeApis from './services/safe'
import ShareApis from './services/share'
import UnzipApis from './services/unzip'
import { IRequestClassImplement } from './types'
import { HTTP_COMMON } from './types/http'

/**
 * 通用请求返回值声明
 */
export interface IRequestCommonResponse {
  success: boolean
  data?: Object | string | ArrayBuffer
  error?: Object | string
  // 是否为请求实例内部错误
  requestPromiseError?: boolean
  // 原始响应数据
  originResponse?: any
}

/**
 * 通用的指定返回值类型的声明
 */
export interface IRequestCommonResponseT<T> {
  success: boolean
  data?: T
  error?: Object | string
  // 是否为请求实例内部错误
  requestPromiseError?: boolean
  // 原始响应数据
  originResponse?: any
}

/**
 * 通用请求参数声明
 */
export interface IRequestCommonOptions {
  // 请求头 HEADER
  header?: Record<string, any>
  // 作用于 GET 请求
  params?: Record<string, any>
  // 作用于 POST 请求
  data?: Record<string, any>
}

// 运行环境（test：开发/内网环境，prod：正式环境）
export type TEnv = 'test' | 'prod'

/**
 * 基础配置声明
 */
export interface IRequestCommonConfig {
  // 运行环境（test：开发/内网环境，prod：正式环境）
  env?: TEnv
  // 请求头（必传字段）
  headers?: IRequestHeader
}

/**
 * 通用请求头，包含必传字段
 */
export interface IRequestHeader {
  "x-captcha-token"?: string;
  "x-client-plugin-version"?: string;
  'Authorization'?: string;
  "x-peer-id"?: string;
  "x-client-version-code"?: string;
  "x-client-id"?: string;
  "x-device-id"?: string;
  "x-request-id"?: string;
  'Cookie'?: string;
  'content-type'?: HTTP_COMMON.ContentType
  [key: string]: string | string[] | number | boolean | null | undefined
}

export class PanSDKManager {
  config: IRequestCommonConfig;
  requestFn: IRequestClassImplement;

  constructor(requestFn: IRequestClassImplement, config: IRequestCommonConfig) {
    this.requestFn = requestFn;
    this.config = config;
  }

  /**
   * 获取云盘基础信息相关的服务端数据获取接口
   * @returns {DriveApis} DriveApis
   */
  getDriveApisForServices (config?: IRequestCommonConfig) {
    return new DriveApis(this.requestFn, { ...this.config, ...config });
  }

  /**
   * 获取文件相关的服务端数据获取接口
   * @returns {FileApis} FileApis
   */
  getFileApisForServices (config?: IRequestCommonConfig) {
    return new FileApis(this.requestFn, { ...this.config, ...config });
  }

  /**
   * 获取分享相关的服务端数据获取接口
   * @returns {ShareApis} ShareApis
   */
  getShareApisForServices (config?: IRequestCommonConfig) {
    return new ShareApis(this.requestFn, { ...this.config, ...config });
  }

  /**
   * 获取保险箱相关的服务端数据获取接口
   * @returns {ShareApis} ShareApis
   */
  getSafeApisForServices (config?: IRequestCommonConfig) {
    return new SafeApis(this.requestFn, { ...this.config, ...config });
  }

  /**
   * 获取在线解压相关的服务端数据获取接口
   * @returns {ShareApis} ShareApis
   */
  getUnzipApisForServices (config?: IRequestCommonConfig) {
    return new UnzipApis(this.requestFn, { ...this.config, ...config });
  }
}
