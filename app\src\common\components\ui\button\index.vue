<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue'
import type { HTMLAttributes } from 'vue'
import { Primitive, type PrimitiveProps } from 'reka-ui'

interface Props extends PrimitiveProps {
  variant?: 'default' | 'primary' | 'secondary' | 'warning' | 'weak-lead' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
  isIcon?: boolean
  class?: HTMLAttributes['class']
  hasIcon?: boolean
  hasRightIcon?: boolean
  rightIcon?: string
  leftIcon?: string
  hasLeftIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  as: 'button',
  variant: 'default',
  size: 'default',
  hasIcon: false,
  isIcon: false,
  hasRightIcon: false,
  rightIcon: 'xl-icon-triangle-down',
  hasLeftIcon: false,
  leftIcon: ''
})

// 定义事件
const emit = defineEmits<{
  rightIconClick: [event: MouseEvent]
}>()

// 获取当前实例
const instance = getCurrentInstance()

// 检查是否有 rightIconClick 监听器
const hasRightIconClickListener = computed(() => {
  return instance?.vnode.props && 'onRightIconClick' in instance.vnode.props
})

// 右侧图标点击处理
const handleRightIconClick = (event: MouseEvent) => {
  // 只有在有监听器时才执行回调，但不阻止事件冒泡
  if (hasRightIconClickListener.value) {
    emit('rightIconClick', event)
  }
}

const buttonClasses = computed(() => {
  const classes = ['button']

  if (props.variant) {
    classes.push(`button--${props.variant}`)
  }

  if (props.size) {
    if (props.isIcon) {
      classes.push(`button--icon-${props.size}`)
    } else {
      classes.push(`button--size-${props.size}`)
    }
  }

  if (props.hasIcon) {
    classes.push('has-icon')
  }

  if (props.class) {
    classes.push(props.class)
  }

  if (props.hasRightIcon) {
    classes.push('button--has-right-icon')
  }

  if (props.hasLeftIcon) {
    classes.push('button--has-left-icon')
  }

  return classes.join(' ')
})

</script>

<template>
  <Primitive data-slot="button" :as="props.as" :as-child="props.asChild" :class="buttonClasses">
    <i v-if="props.hasLeftIcon" :class="props.leftIcon"></i>
    <slot />
    <div class="button-right-icon" v-if="props.hasRightIcon" @click.stop="handleRightIconClick">
      <div class="button-right-split-line"></div>
      <i :class="props.rightIcon"></i>
    </div>
  </Primitive>
</template>

<style lang="scss" scoped>
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  white-space: nowrap;
  font-size: 13px;
  font-weight: 400;
  transition: background-color 0.15s ease-in-out;
  outline: none;
  flex-shrink: 0;
  cursor: pointer;
  user-select: none;
  border: none;
  padding: 0px 12px;
  background: none;
  box-sizing: border-box;
}

.button:disabled {
  pointer-events: none;

  i {
    color: var(--font-font-4, #C9CDD4);
  }
}

.button--default {
  color: var(--button-button1-font-default, #FFF);
  background: var(--button-button1-default, #272E3B);

  .button-right-split-line {
    background: var(--white-white-200, rgba(255, 255, 255, 0.20));
  }

  .button-right-icon {
    color: var(--button-button1-font-default, #FFF);
  }
}

.button--default:hover {
  background: var(--button-button1-hover, #4E5769);
}

.button--default:active {
  background: var(--button-button1-active, #1D2129);
}

.button--default:disabled {
  background: var(--button-button1-disabled, #A9AEB8);
  color: var(--button-button1-font-disable, rgba(255, 255, 255, 0.50));
}

.button--primary {
  color: var(--button-button1-font-default, #FFF);
  background: var(--primary-primary-default, #226DF5);
}

.button--primary:hover {
  background: var(--primary-primary-hover, #488BF7);
}

.button--primary:active {
  background: var(--primary-primary-active, #154FCB);
}

.button--primary:disabled {
  background: var(--primary-primary-disabled, #97C4FB);
  color: var(--button-button1-font-disable, rgba(255, 255, 255, 0.50));
}

.button--secondary {
  background: var(--button-button2-default, #F2F3F5);
  color: var(--font-font-2, #4E5769);
}

.button--secondary:hover {
  background: var(--button-button2-hover, #E5E6EB);
}

.button--secondary:active {
  background: var(--button-button2-active, #C9CDD4);
}

.button--secondary:disabled {
  background: var(--button-button2-disabled, #F7F8FA);
  color: var(--font-font-4, #C9CDD4);
}

.button--warning {
  background: var(--button-button-warn-default, #FFECE8);
  color: var(--button-button-warn-font-default, #FF4D4F);
}

.button--warning:hover {
  background: var(--button-button-warn-hover, #FFDAD4);
}

.button--warning:active {
  background: var(--button-button-warn-active, #FFCFC9);
}

.button--warning:disabled {
  background: var(--button-button-warn-disable, #FFECE8);
  color: var(--button-button-warn-font-disable, #FBACA3);
}

.button--weak-lead {
  background: var(--button-button-lead-default, rgba(34, 109, 245, 0.10));
  color: var(--button-button-lead-font-default-2, #226DF5);

}

.button--weak-lead:hover {
  background: var(--button-button-lead-hover, rgba(34, 109, 245, 0.18));
}

.button--weak-lead:active {
  background: var(--button-button-lead-active, rgba(34, 109, 245, 0.26));
}

.button--weak-lead:disabled {
  background: var(--button-button-lead-disable, rgba(34, 109, 245, 0.10));
  color: var(--button-button-warn-font-disable-2, rgba(34, 109, 245, 0.50));
}


.button--outline {
  border: 1px solid var(--border-border-2, #E5E6EB);
  color: var(--font-font-2, #4E5769);
  box-sizing: border-box;
}

.button--outline:hover {
  border: 1px solid var(--border-border-2, #E5E6EB);
  background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
}

.button--outline:active {
  border: 1px solid var(--border-border-2, #E5E6EB);
  background: var(--fill-fill-2, rgba(12, 24, 49, 0.06));
}

.button--ghost {
  color: var(--font-font-2, #4E5769);
}

.button--ghost:hover {
  background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
}

.button--ghost:active {
  background: var(--fill-fill-2, rgba(12, 24, 49, 0.06));
}

.button--outline:disabled,
.button--ghost:disabled {
  color: var(--font-font-4, #C9CDD4);
}

.button--size-default {
  height: 40px;
  border-radius: var(--border-radius-M, 8px);
}

.button--icon-default {
  height: 40px;
  width: 40px;
  padding: 0;
  border-radius: var(--border-radius-M, 8px);
}

.button--size-sm {
  height: 32px;
  border-radius: var(--border-radius-S, 6px);
}

.button--icon-sm {
  height: 32px;
  width: 32px;
  padding: 0;
  border-radius: var(--border-radius-S, 6px);
}

.button--size-lg {
  height: 48px;
  padding: 0px 16px;
  border-radius: var(--border-radius-M2, 10px);
}

.button--icon-lg {
  height: 48px;
  width: 48px;
  padding: 0;
  border-radius: var(--border-radius-M2, 10px);
}

.button--has-right-icon {
  padding-right: 0;
}

.button-right-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--font-font-2);
  cursor: pointer;
  height: 100%;
  width: 100%;
  padding-right: 12px;
}

.button-right-split-line {
  width: 1px;
  height: 16px;
  margin-left: 8px;
  background: var(--fill-fill-2, rgba(12, 24, 49, 0.06));
}

.button--has-left-icon {
  padding-left: 10px;
}
</style>
