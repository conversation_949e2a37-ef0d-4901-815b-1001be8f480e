install(FILES ${CMAKE_SOURCE_DIR}/configs/cacert.pem DESTINATION ${prj_config_install_dir})
install(FILES ${CMAKE_SOURCE_DIR}/sysconfigs/wsRootCa.crt DESTINATION ${prj_sysconfig_install_dir})
IF (MSVC)
    install(FILES ${CMAKE_SOURCE_DIR}/3rdparty/WES/win/bin/core.dll DESTINATION ${prj_binary_install_dir})
    install(FILES ${CMAKE_SOURCE_DIR}/3rdparty/WES/win/lib/core.lib DESTINATION ${prj_lib_install_dir})
    install(FILES ${CMAKE_SOURCE_DIR}/3rdparty/WES/win/pdb/core.pdb DESTINATION ${prj_pdb_install_dir})
ELSEIF (APPLE)
    if(BUILD_ARM64_SUITE)
        install(FILES ${CMAKE_SOURCE_DIR}/3rdparty/WES/arm64/bin/libcore.3.0.0.0.dylib DESTINATION ${prj_binary_install_dir})
        install(FILES ${CMAKE_SOURCE_DIR}/3rdparty/WES/arm64/bin/libcore.dylib DESTINATION ${prj_binary_install_dir})
    else()
        install(FILES ${CMAKE_SOURCE_DIR}/3rdparty/WES/mac/bin/libcore.3.0.0.0.dylib DESTINATION ${prj_binary_install_dir})
        install(FILES ${CMAKE_SOURCE_DIR}/3rdparty/WES/mac/bin/libcore.dylib DESTINATION ${prj_binary_install_dir})
    endif()
ELSE ()
    message(FATAL_ERROR "Now is '${CMAKE_SYSTEM_NAME}' OS. not supported yet.")
ENDIF ()
