<script setup lang="ts">
import CheckboxGroup, { ICheckoutGroupOptions } from '@root/common/components/ui/checkbox-group/index.vue'
import { DownloadPathNS } from '@root/common/config/download-path';
import { getSettingConfig } from '@root/modal-renderer/src/views/setting';
import { onMounted } from 'vue';
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'


export interface IModelValue {
  path: string
  defaultPath: string
  pathName: string
  checkboxValue: string[]
  checkboxOptions?: ICheckoutGroupOptions[]
  onChange?: (path: string, pathName: string) => void
}

const props = withDefaults(defineProps<{
  label: string
  modelValue: IModelValue
  selectType?: 'cloud' | 'local'
}>(), {
  selectType: 'cloud'
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: IModelValue): void
}>()

const handleOpenFolder = async () => {
  let selectedPath = ''
  if (props.selectType === 'local') {
    selectedPath = await DownloadPathNS.choosePath(props.modelValue.path)
  } else {
    const res = await ThunderPanClientSDK.getInstance().openPathSelectorDialog({})
    if (res.data) {
      const { selectedItem, path } = res.data
      selectedPath = [...path, selectedItem].map(item => item.name).join('\\')
    }
  }

  if (selectedPath && selectedPath.trim()) {
    emit('update:modelValue', {
      ...props.modelValue,
      path: selectedPath
    })
    props.modelValue.onChange?.(selectedPath, props.modelValue.pathName)
  }
}

const initDefaultValue = async () => {
  const path = await getSettingConfig(props.modelValue.pathName, props.modelValue.defaultPath)
  let checkboxValue: string[] = []
  if (props.modelValue.checkboxOptions) {
    checkboxValue = await Promise.all(props.modelValue.checkboxOptions.map(async (option) => {
      const value = await getSettingConfig(option.name, option.defaultValue)
      if (value) {
        return option.name
      }
      return ''
    }).filter(item => !!item))
  }

  emit('update:modelValue', {
    ...props.modelValue,
    path: path as string,
    checkboxValue: checkboxValue
  })
}

onMounted(() => {
  initDefaultValue()
})

</script>

<template>
  <div class="select-file-folder">
    <div class="select-file-folder-label">{{ label }}</div>
    <div class="select-file-folder-content">
      <xl-input v-model="modelValue.path" :input-class="'select-file-folder-input'" style="height: 32px;"
        :readonly="true">
        <template #right>
          <i class="xl-icon-general-openfolder-l" @click="handleOpenFolder"></i>
        </template>
      </xl-input>
      <CheckboxGroup v-if="modelValue.checkboxOptions" :options="modelValue.checkboxOptions" orientation="vertical"
        v-model="modelValue.checkboxValue" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.select-file-folder {
  display: flex;
  gap: 16px;
  align-items: flex-start;

  &-label {
    display: flex;
    height: 32px;
    align-items: center;
    color: var(--font-font-2, #4E5769);
    font-size: 13px;
    line-height: 22px;
  }

  &-content {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
}
</style>
<style lang="scss">
.select-file-folder {
  .select-file-folder-content {
    .select-file-folder-input {
      color: var(--font-font-2, #4E5769);
      font-size: 12px;
      line-height: 20px;
    }

    .xl-icon-general-openfolder-l {
      color: var(--font-font-3, #9FA6B2);
      cursor: pointer;
    }
  }
}
</style>
