#ifndef VERSION_PROXY_H
#define VERSION_PROXY_H
#include <Windows.h>
#include <string>
#include <vector>

typedef DWORD (APIENTRY* FuncGetFileVersionInfoSizeW)(
	_In_        LPCWSTR lptstrFilename, /* Filename of version stamped file */
	_Out_opt_ LPDWORD lpdwHandle       /* Information for use by GetFileVersionInfo */
);

typedef BOOL (APIENTRY* FuncGetFileVersionInfoW)(
	_In_                LPCWSTR lptstrFilename, /* Filename of version stamped file */
	_Reserved_          DWORD dwHandle,          /* Information from GetFileVersionSize */
	_In_                DWORD dwLen,             /* Length of buffer for info */
	_Out_writes_bytes_(dwLen) LPVOID lpData            /* Buffer to place the data structure */
);

typedef BOOL (APIENTRY* FuncVerQueryValueW)(
	_In_ LPCVOID pBlock,
	_In_ LPCWSTR lpSubBlock,
	_Outptr_result_buffer_(_Inexpressible_("buffer can be PWSTR or DWORD*")) LPVOID* lplpBuffer,
	_Out_ PUINT puLen
);

class VersionProxy
{ 
public:	
	static DWORD GetFileVersionInfoSizeW(LPCWSTR lptstrFilename, LPDWORD lpdwHandle);
	static BOOL GetFileVersionInfoW(LPCWSTR lptstrFilename, DWORD dwHandle, DWORD dwLen, LPVOID lpData);
	static BOOL VerQueryValueW(LPCVOID pBlock, LPCWSTR lpSubBlock, LPVOID* lplpBuffer, PUINT puLen);
	static BOOL DynLoadVersionDll();


public:
	static HMODULE m_hVersion;
	static FuncGetFileVersionInfoSizeW m_funcGetFileVersionInfoSizeW;
	static FuncGetFileVersionInfoW m_funcGetFileVersionInfoW;
	static FuncVerQueryValueW m_funcVerQueryValueW;
};
#endif