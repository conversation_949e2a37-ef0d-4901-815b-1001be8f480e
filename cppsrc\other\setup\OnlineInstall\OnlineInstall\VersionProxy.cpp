#include "stdafx.h"
#include "VersionProxy.h"
#include "etwlogger.h"

#include <assert.h>
#include <Shlwapi.h>

HMODULE VersionProxy::m_hVersion = NULL;
FuncGetFileVersionInfoSizeW VersionProxy::m_funcGetFileVersionInfoSizeW = NULL;
FuncGetFileVersionInfoW VersionProxy::m_funcGetFileVersionInfoW = NULL;
FuncVerQueryValueW VersionProxy::m_funcVerQueryValueW = NULL;

BOOL VersionProxy::DynLoadVersionDll()
{
	BOOL bSucc = FALSE;
	do
	{
		if (VersionProxy::m_hVersion)
		{
			bSucc = TRUE;
			break;
		}

		wchar_t wszPath[MAX_PATH] = { 0 };
		UINT len = ::GetSystemDirectory(wszPath, MAX_PATH);
		if (len == 0)
		{
			ERROR_XLOG(L"GetSystemDirectory failed, when dyn load version dll,errcode=%d", ::GetLastError());
			break;
		}

		::PathCombine(wszPath, wszPath, L"version.dll");
		VersionProxy::m_hVersion = ::LoadLibrary(wszPath);
		if (!VersionProxy::m_hVersion)
		{
			ERROR_XLOG(L"LoadLibrary failed, when dyn load version dll,errcode=%d, path=%s", ::GetLastError(), wszPath);
			break;
		}

		VersionProxy::m_funcGetFileVersionInfoSizeW = (FuncGetFileVersionInfoSizeW)::GetProcAddress(VersionProxy::m_hVersion, "GetFileVersionInfoSizeW");
		if (!VersionProxy::m_funcGetFileVersionInfoSizeW)
		{
			ERROR_XLOG(L"GetProcAddress of 'GetFileVersionInfoSizeW' failed, when dyn load version dll,errcode=%d", ::GetLastError());
			break;
		}

		VersionProxy::m_funcGetFileVersionInfoW = (FuncGetFileVersionInfoW)::GetProcAddress(VersionProxy::m_hVersion, "GetFileVersionInfoW");
		if (!VersionProxy::m_funcGetFileVersionInfoW)
		{
			ERROR_XLOG(L"GetProcAddress of 'GetFileVersionInfoW' failed, when dyn load version dll,errcode=%d", ::GetLastError());
			break;
		}

		VersionProxy::m_funcVerQueryValueW = (FuncVerQueryValueW)::GetProcAddress(VersionProxy::m_hVersion, "VerQueryValueW");
		if (!VersionProxy::m_funcVerQueryValueW)
		{
			ERROR_XLOG(L"GetProcAddress of 'VerQueryValueW' failed, when dyn load version dll,errcode=%d", ::GetLastError());
			break;
		}

		bSucc = TRUE;
	} while (false);

	return bSucc;
}

DWORD VersionProxy::GetFileVersionInfoSizeW(LPCWSTR lptstrFilename, LPDWORD lpdwHandle)
{
	assert(VersionProxy::m_funcGetFileVersionInfoSizeW);
	return VersionProxy::m_funcGetFileVersionInfoSizeW(lptstrFilename, lpdwHandle);
}

BOOL VersionProxy::GetFileVersionInfoW(LPCWSTR lptstrFilename, DWORD dwHandle, DWORD dwLen, LPVOID lpData)
{
	assert(VersionProxy::m_funcGetFileVersionInfoW);
	return VersionProxy::m_funcGetFileVersionInfoW(lptstrFilename, dwHandle, dwLen, lpData);
}

BOOL VersionProxy::VerQueryValueW(LPCVOID pBlock, LPCWSTR lpSubBlock, LPVOID* lplpBuffer, PUINT puLen)
{
	assert(VersionProxy::m_funcVerQueryValueW);
	return VersionProxy::m_funcVerQueryValueW(pBlock, lpSubBlock, lplpBuffer, puLen);
}