<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import CheckboxGroup, { ICheckoutGroupOptions } from '@root/common/components/ui/checkbox-group/index.vue';
import RadioGroup, { IRadioGroupOptions } from '@root/common/components/ui/radio-group/index.vue';
import Select, { ISelectItem } from '@root/common/components/ui/select/index.vue';
import { ADVANCED_SETTING_NAME_MAP, getSettingConfig, setSettingConfig } from '@root/modal-renderer/src/views/setting';
import { PopUpNS } from '@root/common/pop-up';
import { config } from '@root/common/config/config'
import { useAlertDialog } from '@root/common/components/ui/Dialog/useAlertDialog'

const alertDialog = useAlertDialog()

// BT下载
const selectedBTDownload = ref<string[]>([])
const btDownloadOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '下载种子文件后自动打开新建面板',
    name: ADVANCED_SETTING_NAME_MAP.AutoBTNewTask,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    defaultValue: false
  },
  {
    label: '启动时关联BT种子文件（.torrent 文件）',
    name: ADVANCED_SETTING_NAME_MAP.AssocTorrent,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    defaultValue: false
  },
])

// 下载磁盘缓存
const selectedDownloadDiskCache = ref<string>('256')
const downloadDiskCacheOptions = ref<IRadioGroupOptions[]>([
  { label: '128M', value: '128' },
  { label: '256M', value: '256' },
  { label: '512M', value: '512' },
  { label: '1024M', value: '1024' },
])

// 下载速度
const selectedDownloadSpeed = ref<string[]>([])
const downloadSpeedOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '开启镜像服务器加速',
    name: ADVANCED_SETTING_NAME_MAP.OpenMirrorImageIncreaseSpeed,
    defaultValue: false,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    tip: '全网数据挖掘，自动匹配与原始资源相同的镜像供用户下载'
  },
  {
    label: '开启迅雷P2P加速',
    name: ADVANCED_SETTING_NAME_MAP.OpenXunleiP2PIncreaseSpeed,
    defaultValue: false,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    tip: '利用P2P技术进行用户间加速，该通道产生的上传流量会慢慢提升通道的健康度，从而提升通道加速效果'
  },
])

// 下载代理设置
const selectedDownloadProxy = ref<string>('0')
const downloadProxyOptions = ref<IRadioGroupOptions[]>([
  { label: '不使用代理服务器', value: '0' },
  { label: '使用IE代理服务器', value: '1' },
  { label: '使用自定义代理服务器', value: '2' },
])

// 代理
const proxyNameList = ref<ISelectItem[]>([{ name: '直接连接', value: '直接连接' }])
const connectTypeHub = ref<string>('直接连接')
const connectTypeHttp = ref<string>('直接连接')
const connectTypeFtp = ref<string>('直接连接')

const proxyList = ref<{
  name: string,
  server: string,
  port: string,
  type: string,
  username: string,
  password: string,
}[]>([])

const initDefaultValue = async () => {
  const btDownloadValue = await Promise.all(btDownloadOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectedBTDownload.value = btDownloadValue as string[]

  const downloadDiskCacheValue = await getSettingConfig(ADVANCED_SETTING_NAME_MAP.DiskCacheSelect, '256') as string
  selectedDownloadDiskCache.value = downloadDiskCacheValue

  const proxyTypeValue = await getSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyType, '0') as string
  selectedDownloadProxy.value = proxyTypeValue

  const downloadSpeedValue = await Promise.all(downloadSpeedOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectedDownloadSpeed.value = downloadSpeedValue as string[]

  const proxyNameListValue = await getSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyName, ['直接连接']) as string[]
  proxyNameList.value = proxyNameListValue.map(item => ({ name: item, value: item }))

  const proxyListValue = await getSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyList, []) as string[]

  proxyList.value = proxyListValue.map(item => {
    const [name, server, port, type, username, password] = item.split(';')
    return { name, server, port, type, username, password }
  })

}

const handleDownloadDiskCacheChange = (value: string) => {
  setSettingConfig(ADVANCED_SETTING_NAME_MAP.DiskCacheSelect, value)
}

const handleAddProxy = () => {
  PopUpNS.showAddProxyDlg()
}

const handleSelectProxy = (value: string, name: string) => {
  setSettingConfig(name, value)
}

const handleApplyProxy = (index: number) => {
  const item = proxyList.value[index]
  connectTypeHub.value = item.name
  connectTypeHttp.value = item.name
  connectTypeFtp.value = item.name

  setSettingConfig(ADVANCED_SETTING_NAME_MAP.ConnectTypeHub, item.name)
  setSettingConfig(ADVANCED_SETTING_NAME_MAP.ConnectTypeHttp, item.name)
  setSettingConfig(ADVANCED_SETTING_NAME_MAP.ConnectTypeFtp, item.name)

}

const handleEditProxy = (index: number) => {
  PopUpNS.showAddProxyDlg(proxyList.value[index])
}

const handleDeleteProxy = async (index: number) => {
  const result = await alertDialog.confirm({
    title: '提示',
    content: '你确定要删除此代理吗？',
    variant: 'error',
    showTitleIcon: false,
    confirmText: '确定'
  })

  if (result !== false) {
    // 删除 ProxyList
    const preValues = await getSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyList, []) as string[]
    const newValues = preValues.filter((_, i) => i !== index)
    setSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyList, newValues)

    // 删除 ProxyName
    const item = proxyList.value[index]
    const names = await getSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyName, []) as string[]
    const newNames = names.filter(name => name !== item.name)
    setSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyName, newNames)

    // 修改 nameSelect
    proxyNameList.value = newNames.map(name => ({ name: name, value: name }))

    // 如果当前代理选中的是删除的这个，需要替换成直接链接
    if (connectTypeHub.value === item.name) {
      connectTypeHub.value = '直接连接'
      setSettingConfig(ADVANCED_SETTING_NAME_MAP.ConnectTypeHub, '直接连接')
    }

    if (connectTypeHttp.value === item.name) {
      connectTypeHttp.value = '直接连接'
      setSettingConfig(ADVANCED_SETTING_NAME_MAP.ConnectTypeHttp, '直接连接')
    }

    if (connectTypeFtp.value === item.name) {
      connectTypeFtp.value = '直接连接'
      setSettingConfig(ADVANCED_SETTING_NAME_MAP.ConnectTypeFtp, '直接连接')
    }
  }
}

const handleConfigValueChanged = async (section: string, key: string, preValue: any, newValue: any, isClick: boolean) => {
  if (section === 'ProxyList' && key === 'proxys') {
    proxyList.value = newValue.map(item => {
      const [name, server, port, type, username, password] = item.split(';')
      return { name, server, port, type, username, password }
    })

    const newNames = ['直接连接', ...proxyList.value.map(item => item.name)]
    proxyNameList.value = newNames.map(item => ({ name: item, value: item }))

    setSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyName, newNames)
  }
}

const handleDownloadProxyChange = (value: string) => {
  setSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyType, value)
}


onMounted(() => {
  initDefaultValue()

  config.on('OnConfigValueChanged', handleConfigValueChanged)
})


onUnmounted(() => {
  config.off('OnConfigValueChanged', handleConfigValueChanged)
})


</script>

<template>
  <CheckboxGroup title="BT下载" :options="btDownloadOptions" v-model="selectedBTDownload" orientation="vertical" />

  <div class="settings-content-divider"></div>

  <RadioGroup title="下载磁盘缓存 （磁盘缓存越大，下载速度越快，占用资源越多）" :options="downloadDiskCacheOptions"
    v-model="selectedDownloadDiskCache" orientation="horizontal" @change="handleDownloadDiskCacheChange" />

  <div class="settings-content-divider"></div>

  <CheckboxGroup title="下载速度" :options="downloadSpeedOptions" v-model="selectedDownloadSpeed" orientation="vertical" />

  <div class="settings-content-divider"></div>

  <RadioGroup title="下载代理设置 （该设置仅对下载生效，对网页浏览无效）" :options="downloadProxyOptions" v-model="selectedDownloadProxy"
    orientation="vertical" @change="handleDownloadProxyChange" />

  <div class="custom-proxy-container">
    <div class="custom-proxy-content">
      <div class="custom-proxy-content-item">
        <span>迅雷服务器连接</span>
        <Select v-model="connectTypeHub" :options="proxyNameList" anchor-class="custom-proxy-select"
          content-class="custom-proxy-select-content" :max-height="160"
          @on-select="(value) => handleSelectProxy(value, ADVANCED_SETTING_NAME_MAP.ConnectTypeHub)"
          :disable="selectedDownloadProxy !== '2'" />
      </div>
      <div class="custom-proxy-content-item">
        <span>HTTP连接</span>
        <Select v-model="connectTypeHttp" :options="proxyNameList" anchor-class="custom-proxy-select"
          content-class="custom-proxy-select-content" :max-height="160"
          @on-select="(value) => handleSelectProxy(value, ADVANCED_SETTING_NAME_MAP.ConnectTypeHttp)"
          :disable="selectedDownloadProxy !== '2'" />
      </div>
      <div class="custom-proxy-content-item">
        <span>FTP连接</span>
        <Select v-model="connectTypeFtp" :options="proxyNameList" anchor-class="custom-proxy-select"
          content-class="custom-proxy-select-content" :max-height="160"
          @on-select="(value) => handleSelectProxy(value, ADVANCED_SETTING_NAME_MAP.ConnectTypeFtp)"
          :disable="selectedDownloadProxy !== '2'" />
      </div>
      <div class="settings-content-divider"></div>


    </div>

    <div class="custom-proxy-add">
      <div class="custom-proxy-add-title">
        <span>代理管理</span>
        <a @click="handleAddProxy">添加</a>
      </div>
      <div class="custom-proxy-add-table">
        <div class="custom-proxy-add-table-header">
          <div>名称</div>
          <div>服务器</div>
          <div>端口</div>
          <div>类型</div>
          <div>操作</div>
        </div>
        <div class="custom-proxy-add-table-body">
          <div v-for="(item, index) in proxyList" :key="item.name" class="custom-proxy-add-table-body-item">
            <div><span>{{ item.name }}</span></div>
            <div><span>{{ item.server }}</span></div>
            <div><span>{{ item.port }}</span></div>
            <div><span>{{ item.type }}</span></div>
            <div>
              <a @click="handleApplyProxy(index)">应用</a>
              <a @click="handleEditProxy(index)">修改</a>
              <a @click="handleDeleteProxy(index)">删除</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<style scoped lang="scss">
a {
  color: var(--primary-primary-font-default, #226DF5);
  font-size: 13px;
  line-height: 22px;
  cursor: pointer;
  text-decoration: none;
}

.custom-proxy {
  &-container {
    padding-left: 24px;
  }

  &-content {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: 6px;

    &-item {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 32px;

      span {
        color: var(--font-font-2, #4E5769);
        font-size: 13px;
        line-height: 22px;
        display: inline-block;
        width: 92px;
      }
    }

    .settings-content-divider {
      margin: 8px 0;
    }
  }

  &-add {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: 6px;

    &-title {
      display: flex;
      align-items: center;
      gap: 16px;
      height: 32px;

      span {
        color: var(--font-font-2, #4E5769);
        font-size: 13px;
        line-height: 22px;
      }
    }

    &-table {
      width: 100%;
      margin-top: 6px;
      border-radius: var(--border-radius-S, 6px);
      border: 1px solid var(--border-border-3, #F2F3F5);

      &-header {
        background: var(--fill-fill-4, rgba(12, 24, 49, 0.02));
        display: flex;

        div {
          color: var(--font-font-3, #86909C);
          font-size: 12px;
          line-height: 20px;
          display: flex;
          height: 40px;
          padding: 8px;
          align-items: center;
          gap: 4px;
          align-self: stretch;
          width: 90px;

          &:first-child {
            width: 130px;
          }

          &:last-child {
            width: 136px;
          }
        }
      }

      &-body {
        display: flex;
        flex-direction: column;

        &-item {
          display: flex;

          div {
            display: flex;
            height: 40px;
            padding: 8px;
            align-items: center;
            width: 90px;

            span {
              color: var(--font-font-2, #4E5769);
              font-size: 12px;
              line-height: 20px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

            }


            &:first-child {
              width: 130px;
            }

            &:last-child {
              display: flex;
              gap: 16px;
              width: 136px
            }
          }

          &:not(:last-child) {
            border-bottom: 1px solid var(--border-border-3, #F2F3F5);
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.custom-proxy-select {
  width: 104px !important;
  height: 32px !important;
  gap: 4px !important;

  input {
    width: 100%;
    color: var(--font-font-2, #4E5769);
    font-size: 13px;
  }

  i {
    color: var(--font-font-3, #898E97);
  }
}

.custom-proxy-select-content {
  width: 104px !important;
}
</style>