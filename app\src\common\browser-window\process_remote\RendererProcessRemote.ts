import { ipc<PERSON><PERSON><PERSON>, Ipc<PERSON><PERSON>er<PERSON>vent } from 'electron'

import { buildXbaseRemoteNamespaceChannelString } from './Common'

class RendererProcessRemote {
  get uuid(): string {
    return this._uuid
  }

  private _uuid: string = ''

  constructor() {}

  prepare() {
    // 获取并存储主进程分配的UUID
    // this.invoke('register-window').then((uuid: string) => {
    //   console.log('RendererProcessRemote invoke register-window', uuid)
    //   this._uuid = uuid
    // })
    // ! 有bug, 导致刷新卡死
    // window.addEventListener('beforeunload', (_event) => {
    //   this.sendSync('window-beforeunload', {
    //     uuid: this._uuid,
    //   })
    // })
  }

  invoke(channel: string, ...args: any[]): Promise<any> {
    return ipcRenderer.invoke(
      buildXbaseRemoteNamespaceChannelString(channel),
      ...args,
    )
  }

  on(
    channel: string,
    listener: (event: IpcRendererEvent, ...args: any[]) => void,
  ): this {
    // Xbase.Remote.BrowserWindow.objectId-channel
    ipcRenderer.on(buildXbaseRemoteNamespaceChannelString(channel), listener)
    return this
  }

  once(
    channel: string,
    listener: (event: IpcRendererEvent, ...args: any[]) => void,
  ): this {
    ipcRenderer.once(buildXbaseRemoteNamespaceChannelString(channel), listener)
    return this
  }

  postMessage(channel: string, message: any, transfer?: MessagePort[]): void {
    ipcRenderer.postMessage(
      buildXbaseRemoteNamespaceChannelString(channel),
      message,
      transfer,
    )
  }

  // removeAllListeners(channel: string): this {
  //   ipcRenderer.removeAllListeners(
  //     buildXbaseRemoteNamespaceChannelString(channel),
  //   );
  //   return this;
  // }

  removeListener(channel: string, listener: (...args: any[]) => void): this {
    ipcRenderer.removeListener(
      buildXbaseRemoteNamespaceChannelString(channel),
      listener,
    )
    return this
  }

  send(channel: string, ...args: any[]): void {
    ipcRenderer.send(buildXbaseRemoteNamespaceChannelString(channel), ...args)
  }

  sendSync(channel: string, ...args: any[]): any {
    return ipcRenderer.sendSync(
      buildXbaseRemoteNamespaceChannelString(channel),
      ...args,
    )
  }

  sendTo(webContentsId: number, channel: string, ...args: any[]): void {
    ipcRenderer.sendTo(
      webContentsId,
      buildXbaseRemoteNamespaceChannelString(channel),
      ...args,
    )
  }

  sendToHost(channel: string, ...args: any[]): void {
    ipcRenderer.sendToHost(
      buildXbaseRemoteNamespaceChannelString(channel),
      ...args,
    )
  }

  // buildXbaseRemoteNamespaceString(str: string): string {
  //   return buildXbaseRemoteNamespaceChannelString(str);
  // }

  // // 向主进程发送请求调用
  // invokeMainMethod(method: string, ...args: any[]) {
  //   if (this.uuid) {
  //     ipcRenderer.send('invoke-main-method', this.uuid, method, ...args);
  //   } else {
  //     console.error('UUID not set yet.');
  //   }
  // }

  // // 向主进程监听事件
  // listenToMainEvent(eventName: string) {
  //   if (this.uuid) {
  //     ipcRenderer.send('listen-to-main-event', this.uuid, eventName);
  //   } else {
  //     console.error('UUID not set yet.');
  //   }
  // }
}

const rendererProcessRemote = new RendererProcessRemote()

export { rendererProcessRemote }
