import { computed, ref } from 'vue'
import { defineStore } from 'pinia'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import type { CloudPathDetailInfo } from '@root/modal-renderer/types/new-task.type'
import { config } from '@root/common/config/config';

/**
 * 云盘路径数据接口
 */
export interface IRecentDownloadPathData {
  id: string
  name: string
  fullFolderpathStr: string
}

/**
 * Download Cloud Path Store
 * 专门管理云盘下载路径、最近使用文件夹和相关状态
 */
export const useDownloadCloudPathStore = defineStore('downloadCloudPath', () => {
  // ===== 状态管理 =====

  /**
   * 最近保存的文件夹列表
   */
  const recentSaveFolders = ref<IRecentDownloadPathData[]>([])

  /**
   * 默认保存文件夹
   */
  const defaultSaveFolder = ref<IRecentDownloadPathData | null>(null)

  /**
   * 当前选中的云盘路径
   */
  const currentCloudPath = ref<IRecentDownloadPathData | null>(null)

  /**
   * 是否正在加载数据
   */
  const isLoading = ref<boolean>(false)

  /**
   * 初始化标志，避免重复初始化
   */
  const isInitialized = ref<boolean>(false)

  // ===== 计算属性 =====

  /**
   * 是否有最近保存的文件夹
   */
  const hasRecentFolders = computed(() => recentSaveFolders.value.length > 0)

  /**
   * 最近文件夹数量
   */
  const recentFoldersCount = computed(() => recentSaveFolders.value.length)

  /**
   * 当前路径显示名称
   */
  const currentPathDisplayName = computed(() => {
    console.log('[DownloadCloudPathStore] 当前选中的云盘:', currentCloudPath.value)
    return currentCloudPath.value?.name || defaultSaveFolder.value?.name || '云盘'
  })

  /**
   * 转换为 CloudPathDetailInfo 格式的最近文件夹列表
   * 用于传递给弹窗组件
   */
  const cloudPathInfoList = computed((): CloudPathDetailInfo[] => {
    return recentSaveFolders.value.map(folder => ({
      id: folder.id,
      name: folder.name,
      canDelete: true, // 最近保存的文件夹都可以删除
    }))
  })

  // ===== Actions =====

  /**
   * 获取最近保存的文件夹列表
   */
  const getRecentSaveFolders = async (): Promise<IRecentDownloadPathData[]> => {
    try {
      isLoading.value = true
      console.log('[DownloadCloudPathStore] 获取最近保存文件夹...')

      const response = await ThunderPanClientSDK.getInstance().getRecentSaveFolders()
      
      if (response.success && response.data) {
        recentSaveFolders.value = response.data
        console.log('[DownloadCloudPathStore] 获取最近保存文件夹成功:', response.data)
        return response.data
      } else {
        console.error('[DownloadCloudPathStore] 获取最近保存文件夹失败:', response.error)
        return []
      }
    } catch (error) {
      console.error('[DownloadCloudPathStore] 获取最近保存文件夹异常:', error)
      return []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 设置最近保存的文件夹列表
   */
  const setRecentSaveFolders = async (folders: IRecentDownloadPathData[]): Promise<void> => {
    try {
      isLoading.value = true
      console.log('[DownloadCloudPathStore] 设置最近保存文件夹:', folders)

      const response = await ThunderPanClientSDK.getInstance().setRecentSaveFolders(folders)
      
      if (response.success) {
        recentSaveFolders.value = [...folders]
        console.log('[DownloadCloudPathStore] 设置最近保存文件夹成功')
      } else {
        console.error('[DownloadCloudPathStore] 设置最近保存文件夹失败:', response.error)
      }
    } catch (error) {
      console.error('[DownloadCloudPathStore] 设置最近保存文件夹异常:', error)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取默认保存文件夹
   */
  const getRecentSaveDefaultFolder = async (): Promise<IRecentDownloadPathData | null> => {
    try {

      const response = await ThunderPanClientSDK.getInstance().getRecentSaveDefaultFolder()

      if (response.success && response.data) {
        defaultSaveFolder.value = response.data
        return response.data
      } else {
        console.error('[DownloadCloudPathStore] 获取默认保存文件夹失败:', response.error)
        return null
      }
    } catch (error) {
      console.error('[DownloadCloudPathStore] 获取默认保存文件夹异常:', error)
      return null
    }
  }

  /**
   * 设置默认保存文件夹
   */
  const setRecentSaveDefaultFolder = async (defaultPath: IRecentDownloadPathData): Promise<void> => {
    try {
      console.log('[DownloadCloudPathStore] 设置默认保存文件夹:', defaultPath)

      // 获取是否使用最近使用路径
      const isLastUsePath = await config.getValue('ThunderPanPlugin', 'lastUsePath', false) as boolean;
      if (isLastUsePath) {
        // 如果使用最近使用路径，则设置为最近使用路径
        const response = await ThunderPanClientSDK.getInstance().setRecentSaveDefaultFolder(defaultPath)
        
        if (response.success) {
          defaultSaveFolder.value = { ...defaultPath }
          console.log('[DownloadCloudPathStore] 设置默认保存文件夹成功')
        } else {
          console.error('[DownloadCloudPathStore] 设置默认保存文件夹失败:', response.error)
        }
      }

    } catch (error) {
      console.error('[DownloadCloudPathStore] 设置默认保存文件夹异常:', error)
    }
  }

  /**
   * 清空最近保存文件夹
   */
  const clearRecentSaveFolder = async (): Promise<void> => {
    try {
      isLoading.value = true
      console.log('[DownloadCloudPathStore] 清空最近保存文件夹...')

      const response = await ThunderPanClientSDK.getInstance().clearRecentSaveFolder()
      
      if (response.success) {
        recentSaveFolders.value = []
        console.log('[DownloadCloudPathStore] 清空最近保存文件夹成功')
      } else {
        console.error('[DownloadCloudPathStore] 清空最近保存文件夹失败:', response.error)
      }
    } catch (error) {
      console.error('[DownloadCloudPathStore] 清空最近保存文件夹异常:', error)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 删除指定的最近保存文件夹
   */
  const deleteRecentSaveFolders = async (folderIds: string[]): Promise<void> => {
    try {
      isLoading.value = true
      console.log('[DownloadCloudPathStore] 删除最近保存文件夹:', folderIds)

      const response = await ThunderPanClientSDK.getInstance().deleteRecentSaveFolders(folderIds)
      
      if (response.success) {
        // 从本地状态中移除已删除的文件夹
        recentSaveFolders.value = recentSaveFolders.value.filter(
          folder => !folderIds.includes(folder.id)
        )
        console.log('[DownloadCloudPathStore] 删除最近保存文件夹成功')
      } else {
        console.error('[DownloadCloudPathStore] 删除最近保存文件夹失败:', response.error)
      }
    } catch (error) {
      console.error('[DownloadCloudPathStore] 删除最近保存文件夹异常:', error)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 设置当前选中的云盘路径
   */
  const setCurrentCloudPath = (cloudPath: IRecentDownloadPathData) => {
    console.log('[DownloadCloudPathStore] 设置当前云盘路径:', cloudPath)
    currentCloudPath.value = { ...cloudPath }
  }

  /**
   * 清除当前选中的云盘路径
   */
  const clearCurrentCloudPath = () => {
    console.log('[DownloadCloudPathStore] 清除当前云盘路径')
    currentCloudPath.value = null
  }

  /**
   * 获取当前选中的云盘路径
   */
  const getCurrentCloudPath = (): IRecentDownloadPathData | null => {
    return currentCloudPath.value || defaultSaveFolder.value
  }

  /**
   * 添加路径到最近保存文件夹历史记录
   */
  const addToRecentHistory = async (cloudPath: IRecentDownloadPathData): Promise<void> => {
    try {
      console.log('[DownloadCloudPathStore] 添加到最近历史记录:', cloudPath)

      // 检查是否已存在
      const existingIndex = recentSaveFolders.value.findIndex(folder => folder.id === cloudPath.id)
      
      if (existingIndex > -1) {
        // 如果存在，移除旧的记录
        recentSaveFolders.value.splice(existingIndex, 1)
      }

      // 添加到最前面
      recentSaveFolders.value.unshift({ ...cloudPath })

      // 限制历史记录数量（最多保存10个）
      if (recentSaveFolders.value.length > 10) {
        recentSaveFolders.value = recentSaveFolders.value.slice(0, 10)
      }

      // 保存到后端
      await setRecentSaveFolders(recentSaveFolders.value)
    } catch (error) {
      console.error('[DownloadCloudPathStore] 添加到最近历史记录失败:', error)
    }
  }

  /**
   * 初始化云盘路径信息
   */
  const initializeCloudPathInfo = async (): Promise<void> => {
    if (isInitialized.value) {
      console.log('[DownloadCloudPathStore] 已初始化，跳过重复初始化')
      return
    }

    try {
      isLoading.value = true
      console.log('[DownloadCloudPathStore] 开始初始化云盘路径信息...')

      // 并行获取最近文件夹列表和默认文件夹
      const [folders, defaultFolder] = await Promise.all([
        getRecentSaveFolders(),
        getRecentSaveDefaultFolder()
      ])

      // 如果没有默认文件夹，设置一个默认值
      if (!defaultFolder) {
        const defaultPath: IRecentDownloadPathData = {
          id: '',
          name: '我的云盘',
          fullFolderpathStr: '',
        }
        await setRecentSaveDefaultFolder(defaultPath)
      }

      // 设置当前路径为默认路径
      if (defaultSaveFolder.value) {
        setCurrentCloudPath(defaultSaveFolder.value)
      }

      isInitialized.value = true
      console.log('[DownloadCloudPathStore] 云盘路径信息初始化完成')
    } catch (error) {
      console.error('[DownloadCloudPathStore] 初始化云盘路径信息失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 刷新云盘路径信息
   */
  const refreshCloudPathInfo = async (): Promise<void> => {
    try {
      console.log('[DownloadCloudPathStore] 刷新云盘路径信息...')
      
      // 重新获取最新数据
      await Promise.all([
        getRecentSaveFolders(),
        getRecentSaveDefaultFolder()
      ])

      console.log('[DownloadCloudPathStore] 云盘路径信息刷新完成')
    } catch (error) {
      console.error('[DownloadCloudPathStore] 刷新云盘路径信息失败:', error)
    }
  }

  /**
   * 重置 store 状态
   */
  const resetStore = () => {
    console.log('[DownloadCloudPathStore] 重置 store 状态')
    recentSaveFolders.value = []
    defaultSaveFolder.value = null
    currentCloudPath.value = null
    isLoading.value = false
    isInitialized.value = false
  }

  // ===== 返回 store 接口 =====
  return {
    // 状态
    recentSaveFolders,
    defaultSaveFolder,
    currentCloudPath,
    isLoading,
    isInitialized,

    // 计算属性
    hasRecentFolders,
    recentFoldersCount,
    currentPathDisplayName,
    cloudPathInfoList,

    // Actions
    getRecentSaveFolders,
    setRecentSaveFolders,
    getRecentSaveDefaultFolder,
    setRecentSaveDefaultFolder,
    clearRecentSaveFolder,
    deleteRecentSaveFolders,
    setCurrentCloudPath,
    clearCurrentCloudPath,
    getCurrentCloudPath,
    addToRecentHistory,
    initializeCloudPathInfo,
    refreshCloudPathInfo,
    resetStore,
  }
}) 