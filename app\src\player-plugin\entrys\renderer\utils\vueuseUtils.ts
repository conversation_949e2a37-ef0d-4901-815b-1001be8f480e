
// onClickOutside 按需添加 ignore 配置，防止类似 modal 弹窗的点击触发当前组件的 onClickOutside
// 例子：login弹窗(class xmp-auth)+全屏mask, 加 class xmp-unClickOutside, 弹窗里的 onClickOutside genOnClickOutsideIgnore(['xmp-auth'])。
// 效果：当前弹窗的 xmp-unClickOutside 会触发 onClickOutside，点其他的 xmp-unClickOutside 不会触发
// 目的：不挡住自己的modal，能挡别人的modal
export function genOnClickOutsideIgnore(notSelector?: string[], unClickOutsideCls = '.xmp-unClickOutside') {
  if (notSelector?.length) {
    const res = [`${unClickOutsideCls}${notSelector.map(selector => `:not(${selector})`).join('')}`]
    // console.log('res', res)
    return res
  } else {
    return [unClickOutsideCls]
  }
}