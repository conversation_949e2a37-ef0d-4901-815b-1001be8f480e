import { ref } from 'vue'

import dayjs from 'dayjs'
import { defineStore } from 'pinia'

import { XLBTTaskSubFileSchedulerType } from '@root/common/task/base'
import { IGetCurrentUserQuotasResponse } from '@root/thunder-pan-plugin/src/utils/business-helper'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'

/**
 * Create Task Store
 * 管理创建任务的状态和逻辑
 */
export const useCreateTaskStore = defineStore('createTask', () => {
  // ===== 状态管理 =====

  /**
   * 是否启用列表顺序下载
   * 对应 NewBtTaskInfo 的 subFileScheduler 字段
   */
  const enableSequentialDownload = ref<boolean>(false)

  /**
   * 是否合并任务组
   */
  const isMergeTask = ref<boolean>(false)

  /**
   * 任务组名称
   */
  const taskGroupName = ref<string>(`任务组_${dayjs().format('YYYYMMDDHHMM')}`)

  /**
   * 云盘空间信息
   */
  const currentUserCloudAddQuotas = ref<IGetCurrentUserQuotasResponse>({
    limit: 0,
    usage: 0,
    surplus: 0
  })

  // ===== Actions =====

  /**
   * 获取当前是否启用列表顺序下载
   */
  const getEnableSequentialDownload = () => {
    return enableSequentialDownload.value
  }

  /**
   * 设置是否启用列表顺序下载
   */
  const setEnableSequentialDownload = (enabled: boolean) => {
    enableSequentialDownload.value = enabled
  }

  /**
   * 切换列表顺序下载状态
   */
  const toggleSequentialDownload = () => {
    enableSequentialDownload.value = !enableSequentialDownload.value
  }

  /**
   * 获取对应的 XLBTTaskSubFileSchedulerType 值
   */
  const getSubFileSchedulerType = (): XLBTTaskSubFileSchedulerType => {
    return enableSequentialDownload.value
      ? XLBTTaskSubFileSchedulerType.XL_BTTaskSubFileSequnecialScheduler
      : XLBTTaskSubFileSchedulerType.XL_BTTaskSubFileDefaultScheduler
  }

  /**
   * 重置enableSequentialDownload到初始值
   */
  const resetEnableSequentialDownload = () => {
    enableSequentialDownload.value = false
  }

  /**
   * 获取当前是否合并任务组
   */
  const getIsMergeTask = () => {
    return isMergeTask.value
  }

  /**
   * 设置是否合并任务组
   */
  const setIsMergeTask = (merge: boolean) => {
    isMergeTask.value = merge
  }

  /**
   * 获取当前任务组名称
   */
  const getTaskGroupName = () => {
    return taskGroupName.value
  }

  /**
   * 设置任务组名称
   */
  const setTaskGroupName = (name: string) => {
    taskGroupName.value = decodeURIComponent(name)
  }

  /**
   * 重置任务组名称到初始值
   */
  const resetTaskGroupName = () => {
    taskGroupName.value = `任务组_${dayjs().format('YYYYMMDDHHMM')}`
  }

  /**
   * 重置所有变量到初始值
   */
  const resetAll = () => {
    enableSequentialDownload.value = false
    isMergeTask.value = false
    taskGroupName.value = `任务组_${dayjs().format('YYYYMMDDHHMM')}`
  }

  /**
   * 设置云盘空间信息
   */
  const setCurrentUserCloudAddQuotas = async () => {
    const res = await ThunderPanClientSDK.getInstance().getCurrentUserCloudAddQuotas()
    if (res.success) {
      currentUserCloudAddQuotas.value = res.data!
    }
  }

  /**
   * 获取云盘空间信息
   */
  const getCurrentUserCloudAddQuotas = () => {
    return currentUserCloudAddQuotas.value
  }

  // ===== 返回状态和方法 =====
  return {
    // 状态
    enableSequentialDownload,
    isMergeTask,
    taskGroupName,
    currentUserCloudAddQuotas,

    // Actions
    getEnableSequentialDownload,
    setEnableSequentialDownload,
    toggleSequentialDownload,
    getSubFileSchedulerType,
    resetEnableSequentialDownload,
    getIsMergeTask,
    setIsMergeTask,
    getTaskGroupName,
    setTaskGroupName,
    resetTaskGroupName,
    resetAll,
    setCurrentUserCloudAddQuotas,
    getCurrentUserCloudAddQuotas,
  }
})
