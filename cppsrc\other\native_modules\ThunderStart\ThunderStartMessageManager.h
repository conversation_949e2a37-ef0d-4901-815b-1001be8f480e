#pragma once

#define XL_WM_COMMANDQUIT						(WM_USER + 0x301)
#define XL_WINDOW_THUNDERSTARTCOMMAND_NAME		L"XLStartCommandWindow"

class ThunderStartMessageManager : 
	public CWindowImpl<ThunderStartMessageManager>
{
public:
	ThunderStartMessageManager(void);
	virtual ~ThunderStartMessageManager(void);

	static ThunderStartMessageManager& Instance()
	{
		static ThunderStartMessageManager s_instance;
		return s_instance;
	}

	bool Init();

	DECLARE_WND_CLASS(XL_WINDOW_THUNDERSTARTCOMMAND_NAME)

BEGIN_MSG_MAP(ThunderStartMessageManager)
	MESSAGE_HANDLER(XL_WM_COMMANDQUIT, OnQuitCommand)
END_MSG_MAP()

private:
	LRESULT OnQuitCommand(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/);
	void ChangeWindowMessageFilter(UINT message, DWORD dwFlag);
	HWND m_hwndCommand;
};
