# function: SU<PERSON><PERSON>ELIST
MACRO(SUBFILELIST result curdir abs)
  FILE(GLOB children LIST_DIRECTORIES false RELATIVE ${curdir} ${curdir}/*)
  SET(filelist "")
  FOREACH(child ${children})
    if (${abs})
		LIST(APPEND filelist ${curdir}/${child})
	else (${abs})
		LIST(APPEND filelist ${child})
	endif (${abs})
  ENDFOREACH()
  SET(${result} ${filelist})
ENDMACRO()

# function: SUB_FILEWE_LIST
# description: sub file name withou extension 
MACRO(SUB_FILEWE_LIST result curdir)
  FILE(GLOB children LIST_DIRECTORIES false RELATIVE ${curdir} ${curdir}/*)
  SET(file_we_list "")
  FOREACH(child ${children})
	get_filename_component(file_we ${child} NAME_WE)
    LIST(APPEND file_we_list ${file_we})
  ENDFOREACH()
  SET(${result} ${file_we_list})
ENDMACRO()