/// <reference path="../impl/thunder-client-api.d.ts" />
import {
    LinkHubCallApiProxyImplWithIpcServer,
    getLinkHubCallApiProxy
} from '../linkhub-call-api-impl'

export class Link<PERSON>ub<PERSON>elper implements ThunderClientAPI.biz.ILinkHubPresenter {
    private apiProxy: LinkHubCallApiProxyImplWithIpcServer;
    private static instance: LinkHubHelper | null = null;

    private constructor() {
        this.apiProxy = getLinkHubCallApiProxy();
    }

    public static getInstance(): LinkHubHelper {
        if (!LinkHubHelper.instance) {
            if (global.LinkHubHelperClientInstance) {
                LinkHubHelper.instance = global.LinkHubHelperClientInstance;
            } else {
                LinkHubHelper.instance = new LinkHubHelper();
                global.LinkHubHelperClientInstance = LinkHubHelper.instance;
            }
        }
        return LinkHubHelper.instance!;
    }
    public async syncLinksToServer(param: ThunderClientAPI.dataStruct.dataModals.SyncLinksToServerParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.SyncLinksToServerResult> {
        let info = await this.apiProxy.CallApi('LinkHubHelperSyncLinksToServer', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.SyncLinksToServerResult;
        }
        return {} as any;
    }
    public async getLinks(param: ThunderClientAPI.dataStruct.dataModals.GetLinkRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.GetLinkRecordsResult> {
        let info = await this.apiProxy.CallApi('LinkHubHelperGetLinks', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.GetLinkRecordsResult;
        }
        return {} as any;
    }
    public async searchLinks(param: ThunderClientAPI.dataStruct.dataModals.SearchLinkRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.SearchLinkRecordsResult> {
        let info = await this.apiProxy.CallApi('LinkHubHelperSearchLinks', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.SearchLinkRecordsResult;
        }
        return {} as any;
    }
    public async saveLink(param: ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordResult> {
        let info = await this.apiProxy.CallApi('LinkHubHelperSaveLink', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordResult;
        }
        return {} as any;
    }
    public async saveLinks(param: ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordsResult> {
        let info = await this.apiProxy.CallApi('LinkHubHelperSaveLinks', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordsResult;
        }
        return {} as any;
    }
    async savePlaybackRecord(param: ThunderClientAPI.dataStruct.dataModals.InsertPlaybackRecordParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.InsertPlaybackRecordResult> {
        let info = await this.apiProxy.CallApi('LinkHubHelperSavePlaybackRecord', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.InsertPlaybackRecordResult;
        }
        return {} as any;
    }
    async getPlaybackRecords(param: ThunderClientAPI.dataStruct.dataModals.GetPlaybackRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.GetPlaybackRecordsResult> {
        let info = await this.apiProxy.CallApi('LinkHubHelperGetPlaybackRecords', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.GetPlaybackRecordsResult;
        }
        return {} as any;
    }

    async getAssociateCloudFile(param: ThunderClientAPI.dataStruct.dataModals.GetAssociateCloudFileParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.GetAssociateCloudFileResult> {
        let info = await this.apiProxy.CallApi('LinkHubHelperGetAssociateCloudFile', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.GetAssociateCloudFileResult;
        }
        return {} as any;
    }
}