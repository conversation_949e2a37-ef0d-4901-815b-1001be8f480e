APlayerV[0][00000006][15:38:17.904][0][08384]   a_kernel::load_config@ load config failed, load C:\xunlei\project\thunder_2025\bin\Release\logs\aplayer.txt failed, try load more.
APlayerV[0][00000007][15:38:17.904][0][08384]   a_kernel::load_config@ load config failed, load C:\xunlei\project\thunder_2025\bin\Release\logs\aplayer.txt.txt failed
APlayerV[1][00000008][15:38:17.904][0][08384]   a_player::set_config@ interface - set_config, audio.normalize = 1
APlayerV[1][00000009][15:38:17.904][0][08384]   a_player::set_callback@ interface - set_callback, callback_function = 0000000048206e20, callback_user = 00000000490ee5c0
APlayerV[1][00000010][15:38:18.668][0][32464]   a_event::fire_event@ fire event, event_id = AEVENT_TICK, param1 = 0, param2 = 0, param_str=nullptr
APlayerV[1][00000011][15:38:19.608][0][32464]   a_event::fire_event@ fire event, event_id = AEVENT_TICK, param1 = 0, param2 = 0, param_str=nullptr
APlayerV[1][00000012][15:38:20.546][0][32464]   a_event::fire_event@ fire event, event_id = AEVENT_TICK, param1 = 0, param2 = 0, param_str=nullptr
APlayerV[1][00000013][15:38:21.489][0][32464]   a_event::fire_event@ fire event, event_id = AEVENT_TICK, param1 = 0, param2 = 0, param_str=nullptr
APlayerV[1][00000014][15:38:22.430][0][32464]   a_event::fire_event@ fire event, event_id = AEVENT_TICK, param1 = 0, param2 = 0, param_str=nullptr
APlayerV[1][00000015][15:38:23.375][0][32464]   a_event::fire_event@ fire event, event_id = AEVENT_TICK, param1 = 0, param2 = 0, param_str=nullptr
APlayerV[1][00000016][15:38:24.306][0][32464]   a_event::fire_event@ fire event, event_id = AEVENT_TICK, param1 = 0, param2 = 0, param_str=nullptr
