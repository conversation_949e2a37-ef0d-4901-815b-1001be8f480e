import * as BaseType from '../base'
import { AplayerSubTitleManager } from './subtitle-manager';

export class AplayerMedia {
    private nativeObj: any;
    private subTitleManager: AplayerSubTitleManager;
    constructor(obj: any) {
        this.nativeObj = obj;
        this.subTitleManager = new AplayerSubTitleManager(this.nativeObj.getSubtitleManager());
    }

    public getSubtitleManager(): AplayerSubTitleManager {
        return this.subTitleManager;
    }

    public getName(): string {
        return this.nativeObj.getName();
    }

    public getAttribute(): BaseType.MediaAttribute {
        return this.nativeObj.getAttribute();
    }

    public getMediaWidth(): number {
        return this.nativeObj.getMediaWidth();
    }

    public getMediaHeight(): number {
        return this.nativeObj.getMediaHeight();
    }

    public isTaskLocalPlay(): boolean {
        return this.nativeObj.isTaskLocalPlay();
    }

    public isPanPlay(): boolean {
        return this.nativeObj.isPanPlay();
    }

    public isTaskPlay(): boolean {
        return this.nativeObj.isTaskPlay();
    }

    public getType(): BaseType.MediaType {
        return this.nativeObj.getType();
    }

    public attachAudioTrackPreparedEvent(cb: () => void): number {
        return this.nativeObj.attachAudioTrackPreparedEvent(cb);
    }

    public detachAudioTrackPreparedEvent(cookie: number): void {
        this.nativeObj.detachAudioTrackPreparedEvent(cookie);
    }

    public getAudioTrackList(): string[] {
        return this.nativeObj.getAudioTrackList();
    }

    public attachAudioTrackSelectChangeEvent(cb: (index: number) => void): number {
        return this.nativeObj.attachAudioTrackSelectChangeEvent(cb);
    }

    public detachAudioTrackSelectChangeEvent(cookie: number): void {
        return this.nativeObj.detachAudioTrackSelectChangeEvent(cookie);
    }

    public getAudioTrackSelectedIndex(): number {
        return this.nativeObj.getAudioTrackSelectedIndex();
    }

    public switchAudioTrack(index: number): void {
        this.nativeObj.switchAudioTrack(index);
    }

    public isShowRatio(): boolean {
        return this.nativeObj.isShowRatio() === 1;
    }

    public attachRatioPreparedEvent(cb: () => void): number {
        return this.nativeObj.attachRatioPreparedEvent(cb);
    }

    public detachRatioPreparedEvent(cookie: number): number {
        return this.nativeObj.detachRatioPreparedEvent(cookie);
    }

    public getRatioList(): BaseType.RatioItem[] {
        return this.nativeObj.getRatioList();
    }

    public getRatioSelectedId(): string {
        return this.nativeObj.getRatioSelectedId();
    }

    public switchRatio(id: string): void {
        this.nativeObj.switchRatio(id);
    }

    public isChangeRatio(): boolean {
        return this.nativeObj.isChangeRatio() === 1;
    }

    public attachProgressChangedEvent(cb: (progress: number) => void): number {
        return this.nativeObj.attachProgressChangedEvent(cb);
    }

    public detachProgressChangedEvent(cookie: number): void {
        this.nativeObj.detachProgressChangedEvent(cookie);
    }

    public attachProgressSetToLatestEvent(cb: (nPos: number) => void): number {
        return this.nativeObj.attachProgressSetToLatestEvent(cb);
    }

    public detachProgressSetToLatestEvent(cookie: number): void {
        this.nativeObj.detachProgressSetToLatestEvent(cookie);
    }

    public getPlayProgress(): number {
        return this.nativeObj.getPlayProgress();
    }

    public getDuration(): number {
        return this.nativeObj.getDuration();
    }

    public progressMoveTo(pos: number): void {
        this.nativeObj.progressMoveTo(pos);
    }

    public attachPlayStateChangeEvent(cb: (state: BaseType.MediaState) => void): number {
        return this.nativeObj.attachPlayStateChangeEvent(cb);
    }

    public detachPlayStateChangeEvent(cookie: number): void {
        this.nativeObj.detachPlayStateChangeEvent(cookie);
    }

    public getMediaState(): BaseType.MediaState {
        return this.nativeObj.getMediaState();
    }

    public getMediaErrorInfo(): BaseType.PlayErrInfo {
        return this.nativeObj.getMediaErrorInfo();
    }

    public attachPlayBufferEvent(cb: (buffer: boolean, local: boolean, type: BaseType.MediaType, speed: number) => void): number {
        return this.nativeObj.attachPlayBufferEvent(cb);
    }

    public detachPlayBufferEvent(cookie: number): void {
        this.nativeObj.detachPlayBufferEvent(cookie);
    }
}