// stdafx.h : include file for standard system include files,
// or project specific include files that are used frequently, but
// are changed infrequently
//

#pragma once

#include "targetver.h"

#pragma warning(disable:4005)

#define WIN32_LEAN_AND_MEAN             // Exclude rarely-used stuff from Windows headers
// Windows Header Files:
#include <windows.h>

// C RunTime Header Files
#include <stdlib.h>
#include <malloc.h>
#include <memory.h>
#include <tchar.h>

#include <string>
#include <vector>

#include "UIlib.h"
using namespace DuiLib;

#ifdef _DEBUG
#pragma comment(lib, "Duilib_md.lib")
#pragma comment(lib, "7zUncompress_md.lib")
#pragma comment(lib, "ThunderInstallInfo_md.lib")
#else
#pragma comment(lib, "DuiLib.lib")
#pragma comment(lib, "7zUncompress.lib")
#pragma comment(lib, "ThunderInstallInfo.lib")
#endif

#ifndef tstring
#ifdef _UNICODE
typedef std::wstring tstring;
#else
typedef std::string tstring;
#endif // _UNICODE
#endif // !tstring

#include "etwlogger.h"
#include "log_utils.h"

// TODO: reference additional headers your program requires here
