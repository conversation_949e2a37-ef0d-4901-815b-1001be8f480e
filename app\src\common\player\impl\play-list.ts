import * as BaseType from '../base'

export class AplayerPlayList {
    private nativeObj: any;
    constructor(obj: any) {
        this.nativeObj = obj;
    }

    public attachPlayListPreparedEvent(cb: () => void): number {
        return this.nativeObj.attachPlayListPreparedEvent(cb);
    }

    public detachPlayListPreparedEvent(cookie: number): void {
        this.nativeObj.detachPlayListPreparedEvent(cookie);
    }

    public attachPlayListSelectChangeEvent(cb: (id: string) => void): number {
        return this.nativeObj.attachPlayListSelectChangeEvent(cb);
    }

    public detachPlayListSelectChangeEvent(cookie: number): void {
        this.nativeObj.detachPlayListSelectChangeEvent(cookie);
    }

    public attachPlayListItemDeleteEvent(cb: (id: string) => void): number {
        return this.nativeObj.attachPlayListItemDeleteEvent(cb);
    }

    public detachPlayListItemDeleteEvent(cookie: number): void {
        this.nativeObj.detachPlayListItemDeleteEvent(cookie);
    }

    public attachPlayListItemAddEvent(cb: (bSucc: boolean, item?: BaseType.PlayListItem) => void): number {
        return this.nativeObj.attachPlayListItemAddEvent(cb);
    }

    public detachPlayListItemAddEvent(cookie: number): void {
        this.nativeObj.detachPlayListItemAddEvent(cookie);
    }

    public getPlayList(): BaseType.PlayListItem[] {
        return this.nativeObj.getPlayList();
    }

    public deletePlayListItem(id: string): void {
        this.nativeObj.deletePlayListItem(id);
    }

    public playItem(id: string): void {
        this.nativeObj.playItem(id);
    }

    public playNext(): void {
        this.nativeObj.playNext();
    }

    public playPrev(): void {
        this.nativeObj.playPrev();
    }

    public getPlayingItemId(): string {
        return this.nativeObj.getPlayingItemId();
    }

    public addLocalItem(name: string, filePath: string): void {
        this.nativeObj.addLocalItem(name, filePath);
    }

    public clear(): void {
        this.nativeObj.clear();
    }

    public getItemMediaInfo(id: string, cb: (info: BaseType.PlayListItemMediaInfo) => void): void {
        this.nativeObj.getItemMediaInfo(id, cb);
    }

    public isNextLocalPlay(cb: (bLocal: boolean) => void): void {
        this.nativeObj.isNextLocalPlay(cb);
    }

    public isPrevLocalPlay(cb: (bLocal: boolean) => void): void {
        this.nativeObj.isPrevLocalPlay(cb);
    }

    public isLocalPlay(id: string, cb: (bLocal: boolean) => void): void {
        this.nativeObj.isLocalPlay(id, cb);
    }
}