import { createVNode, render, nextTick } from 'vue'
import MessageBoxConstructor from './message-box.vue'

export interface MessageBoxOptions {
  title?: string
  message?: string | object | Function
  dangerouslyUseHTMLString?: boolean
  type?: string
  icon?: string | object
  closeIcon?: string | object
  customClass?: string
  customStyle?: object
  modalClass?: string
  showClose?: boolean
  beforeClose?: Function
  distinguishCancelAndClose?: boolean
  showCancelButton?: boolean
  showConfirmButton?: boolean
  cancelButtonText?: string
  confirmButtonText?: string
  cancelButtonClass?: string
  confirmButtonClass?: string
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  callback?: Function
  cancelButtonLoading?: boolean
  confirmButtonLoading?: boolean
  cancelButtonLoadingIcon?: string | object
  confirmButtonLoadingIcon?: string | object
  appendTo?: string
  modal?: boolean
}

function showMessageBox(options: MessageBoxOptions) {
  return new Promise((resolve, reject) => {
    const container = document.createElement('div')
    const vnode = createVNode(MessageBoxConstructor, {
      ...options,
      onConfirm: () => {
        render(null, container)
        resolve('confirm')
      },
      onCancel: () => {
        render(null, container)
        reject('cancel')
      },
      onClose: () => {
        render(null, container)
        if (options.distinguishCancelAndClose) {
          reject('close')
        } else {
          reject('cancel')
        }
      }
    })
    render(vnode, container)
    // 使用 nextTick 确保 DOM 渲染完成
    nextTick(() => {
      document.body.appendChild(container)
    })
  })
}

const MessageBox = {
  alert(message: string | object | Function, title = '', options: MessageBoxOptions = {}) {
    // 如果message是object，则直接当options
    if (typeof message === 'object') {
      options = message
      message = ''
    }
    return showMessageBox({
      ...options,
      title: title || options.title,
      message: message || options.message,
      showConfirmButton: true,
      closeOnClickModal: false,
      closeOnPressEscape: false,
    })
  },
  confirm(message: string | object | Function, title = '', options: MessageBoxOptions = {}) {
    if (typeof message === 'object') {
      options = message
      message = ''
    }
    return showMessageBox({
      ...options,
      title: title || options.title,
      message: message || options.message,
      showConfirmButton: true,
      closeOnClickModal: true,
      closeOnPressEscape: true,
    })
  },
}

export default MessageBox 