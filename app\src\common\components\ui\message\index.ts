import { AppContext, ComponentInternalInstance, createVNode, isVNode, render, shallowReactive, VNode } from 'vue'
import { isFunction } from '@vue/shared'
import { withInstallFunction } from '../../utils/vue-install'

import MessageConstructor from './message.vue'

let seed = 1

export interface MessageHandler {
  close: () => void
  updateType: (type: MessageType) => void
  updateMessage: (message: string) => void
}

export type MessageContext = {
  id: string
  vnode: VNode
  handler: MessageHandler
  vm: ComponentInternalInstance
  props: any
}

export type MessageType = 'info' | 'success' | 'warning' | 'error' | 'loading';

export interface IMessageOptions {
  message: string
  duration?: number
  type?: MessageType
  showTypeIcon?: boolean
  rightTextButton?: string
  onClose?: () => void
  onRightTextButtonClick?: () => void
}

export type MessageFn = {
  (options?: IMessageOptions, appContext?: null | AppContext): MessageHand<PERSON>
  closeAll(type?: MessageType): void
}
export type MessageTypedFn = (
  options?: IMessageOptions,
  appContext?: null | AppContext
) => MessageHandler

export interface Message extends MessageFn {
  success: MessageTypedFn
  warning: MessageTypedFn
  info: MessageTypedFn
  error: MessageTypedFn
}

export const instances: any[] = shallowReactive([])

export const getInstance = (id: string) => {
  const idx = instances.findIndex((instance) => instance.id === id)
  const current = instances[idx]
  let prev: MessageContext | undefined
  if (idx > 0) {
    prev = instances[idx - 1]
  }
  return { current, prev }
}

export const getLastOffset = (id: string): number => {
  const { prev } = getInstance(id)
  if (!prev) return 0
  return prev.vm.exposed!.bottom.value
}

export const getOffsetOrSpace = (id: string, offset: number) => {
  const idx = instances.findIndex((instance) => instance.id === id)
  return idx > 0 ? 16 : offset
}

const closeMessage = (instance: MessageContext) => {
  const idx = instances.indexOf(instance)
  if (idx === -1) return

  instances.splice(idx, 1)
  const { handler } = instance
  handler.close()
}

export function closeAll(type?: MessageType): void {
  for (const instance of instances) {
    if (!type || type === instance.props.type) {
      instance.handler.close()
    }
  }
}

const createMessage = (options: IMessageOptions, context?: AppContext | null): MessageContext => {

  const id = `thunder_message_${seed++}`

  const container = document.createElement('div')

  const props = {
    ...options,
    id,
    onClose: () => {
      if (options.onClose) {
        options.onClose()
      }
      closeMessage(instance)
    },

    onDestroy: () => {
      render(null, container)
    },
  }

  const vnode = createVNode(
    MessageConstructor,
    props,
    isFunction(props.message) || isVNode(props.message)
      ? {
          default: isFunction(props.message)
            ? props.message
            : () => props.message,
        }
      : null
  )
  vnode.appContext = context || message._context

  render(vnode, container)

  document.body.appendChild(container.firstElementChild!)


  const vm = vnode.component!

  const handler = {
    close: () => {
      vm.exposed!.visible.value = false
    },
    updateType: (type: MessageType) => {
      vm.exposed!.updateInnerType(type)
    },
    updateMessage: (message: string) => {
      vm.exposed!.updateInnerMessage(message)
    }
  }

  const instance = {
    id,
    vnode,
    vm,
    handler,
    props: (vnode.component as any).props,
  }

  return instance
}

export function message (options: IMessageOptions, context?: AppContext | null) {
  const instance = createMessage(options, context)

  instances.push(instance)
  return instance.handler
}

['success', 'info', 'warning', 'error'].forEach((type) => {
  message[type] = (options = {}, appContext?: AppContext | null) => {
    let _options: any = {}
    if (typeof options === 'string' || typeof options === 'number') {
      _options.message = String(options)
    } else {
      _options = options
    }

    return message({ ..._options, type } as any, appContext)
  }
})

message.closeAll = closeAll

message._context = null

export const XMPMessage = withInstallFunction(message, '$message')
export default XMPMessage
