import * as BaseType from '../base'
import {CategoryView} from './category-view'

export class Category {
  private nativeCategory: any;
  private views: Map<BaseType.CategoryViewID, CategoryView> = new Map();
  // ������
  private id: number;

  constructor(id: number, nativeCategory: any) {
    this.id = id;
    this.nativeCategory = nativeCategory;
  }

  // 获取分类里面的view信息，想起查看CategoryViewID[下载、已完成、删除]
  public getCategoryViewFromId(viewId: BaseType.CategoryViewID): CategoryView | undefined {
    if (this.views.has(viewId)) {
      return this.views.get(viewId);
    }

    let view = this.nativeCategory.getCategoryViewFromId(viewId);
    if (view) {
      this.views.set(viewId, new CategoryView(view));
    }
    return this.views.get(viewId);
  }

  public getId(): number {
    return this.id;
  }

  public getName(): string {
    return this.nativeCategory.getName();
  }

  public getDesc(): string {
    return this.nativeCategory.getDesc();
  }

  public getUserName(): string {
    return this.nativeCategory.getUserName();
  }

  // 当前分类里面正在下载的任务的个数
  public getDownloadingTaskCount(): number {
    return this.nativeCategory.getDownloadingTaskCount();
  }

  // 当前分类里面正在下载的所有任务的总速度
  public getDownloadTotalSpeed(): number {
    return this.nativeCategory.getDownloadTotalSpeed();
  }

  // 当前分类里面正在下载的任务的id信息
  public getDownloadingTaskIds(): number[] {
    return this.nativeCategory.getDownloadingTaskIds();
  }
}