/// <reference path="../impl/thunder-client-api.d.ts" />
import {
    LinkHubCallApiProxyImplWithIpcServer,
    getLinkHubCallApiProxy
} from '../linkhub-call-api-impl'

export class PlaybackRecordHelper implements ThunderClientAPI.biz.IPlaybackRecordPresenter {
    private apiProxy: LinkHubCallApiProxyImplWithIpcServer;
    private static instance: PlaybackRecordHelper | null = null;

    private constructor() {
        this.apiProxy = getLinkHubCallApiProxy();
    }

    public static getInstance(): PlaybackRecordHelper {
        if (!PlaybackRecordHelper.instance) {
            if (global.PlaybackRecordHelperClientInstance) {
                PlaybackRecordHelper.instance = global.PlaybackRecordHelperClientInstance;
            } else {
                PlaybackRecordHelper.instance = new PlaybackRecordHelper();
                global.PlaybackRecordHelperClientInstance = PlaybackRecordHelper.instance;
            }
        }
        return PlaybackRecordHelper.instance!;
    }

    public async loadPlaybackRecords(param: ThunderClientAPI.dataStruct.dataModals.LoadPlaybackRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.LoadPlaybackRecordsResult> {
        let info = await this.apiProxy.CallApi('PlaybackRecordGetPlaybackRecordList', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.LoadPlaybackRecordsResult;
        }
        return {} as any;
    }

    public async removePlaybackRecords(param: ThunderClientAPI.dataStruct.dataModals.RemovePlaybackRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.RemovePlaybackRecordsResult> {
        let info = await this.apiProxy.CallApi('PlaybackRecordRemovePlaybackRecords', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.RemovePlaybackRecordsResult;
        }
        return {} as any;
    }

    public async fetchFirstPage(param: ThunderClientAPI.dataStruct.common.FetchFirstPageParam)
        : Promise<ThunderClientAPI.dataStruct.common.FetchFirstPageResult> {
        let info = await this.apiProxy.CallApi('PlaybackRecordFetchFirstPage', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.common.FetchFirstPageResult;
        }
        return {} as any;
    }

    public async fetchNextPage(param: ThunderClientAPI.dataStruct.common.FetchNextPageParam)
        : Promise<ThunderClientAPI.dataStruct.common.FetchNextPageResult> {
        let info = await this.apiProxy.CallApi('PlaybackRecordFetchNextPage', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.common.FetchNextPageResult;
        }
        return {} as any;
    }

    public async removeAllPlaybackRecords(param: ThunderClientAPI.dataStruct.dataModals.RemoveAllPlaybackRecordsParam)
        : Promise<any> {
        let info = await this.apiProxy.CallApi('PlaybackRecordRemoveAllPlaybackRecords', param);
        if (info.bSucc) {
            return info.result as any;
        }
        return {} as any;
    }
    public async getPlaybackRecordDetail(param: {url: string, file_index: number}): Promise<any> {
        let info = await this.apiProxy.CallApi('PlaybackRecordGetDetail', param);
        if (info.bSucc) {
            return info.result as any;
        }
        return {} as any;
    }
}