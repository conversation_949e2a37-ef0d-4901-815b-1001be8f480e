declare namespace ThunderClientAPI {
    namespace dataStruct {
        namespace common {
            interface ErrorInfo {
                result: number
                message: string
            }
            // 暂定
            type SortOrderType = 'asc' | 'desc' | 'default' | 'none'
            type SortKeyType = 'name' | 'size' | 'time'
            interface SortOrder {
                sortOrderKey: SortKeyType // 排序的key, eg: "name", "size", "time"
                sortOrderType: SortOrderType // 排序的方式, eg: "asc", "desc"
            }
            interface ListFilter {
                filterKey: string // 过滤的key, eg: "name", "size", "time"
                filterValue: string // 过滤的值, eg: "xxx", ">100", "<100"
            }
            interface Range {
                beginIndex: number
                count: number
            }
            interface PageRange {
                range: Range
                sortOrder?: SortOrder // 请求列表是需要指定的排序方式
                listFilter?: ListFilter // 过滤条件
            }
            type DataSourceType = 'ALL_LINKS' | 'TREE_FILE_LIST' | 'LABEL' | 'MEDIA_TYPE'
            interface DataSourceConfig {
                dataSourceType: DataSourceType

                // ALL_LINKS类型则不需要指定parentLinkId
                // 如果是SUB_FILE_LIST类型，dataSrcName则填parentLinkId
                // 如果是LABEL类型，则dataSrcName填label标签key（待定）
                dataSourceKey?: string
                reserved?: string // 保留字段
            }
            interface FetchFirstPageParam {
                dataSrcConfig?: DataSourceConfig
                firstLoadCount: number
                reload: boolean
            }
            interface FetchFirstPageResult {
                totalCount: number
                hasLoadFinished: boolean
                error: common.ErrorInfo
            }

            interface FetchNextPageParam {
                dataSrcConfig?: DataSourceConfig
                loadCount: number
                reload: boolean
            }
            interface FetchNextPageResult {
                hasLoadFinished: boolean
                error: common.ErrorInfo
            }

        }

        namespace dataModals {
            type MediaType = 'MEDIA_UNKNOWN' | 'MEDIA_VIDEO' | 'MEDIA_AUDIO' | 'MEDIA_IMAGE' | 'MEDIA_ARCHIVE' | 'MEDIA_TEXT' | 'MEDIA_INSTALLER' | 'MEDIA_FONT' | 'MEDIA_SUBTITLE'
            type UrlType = 'magnet' | 'http' | 'ftp' | 'ed2k' | 'thunder'
            type AuditState = 'AUDIT_UNKNOWN' | 'AUDIT_ACCEPTED' | 'AUDIT_REJECTED'
            type ActionType = 'ACTION_UNKNOWN' | 'ACTION_DOWNLOAD' | 'ACTION_CLOUD_DOWNLOAD' | 'ACTION_TODO' | 'ACTION_PLAY'
            type LinkStatus = 'STATUS_UNKNOWN' | 'STATUS_NORMAL' | 'STATUS_DEFERRED'
            interface AuditInfo {
                status: AuditState,
                message: string,
            }
            interface LinkRecord {
                url_hash: string
                url: string
                id?: string // 服务端索引id
                url_type: UrlType
                parentid: string
                file_index: number
                gcid: string
                name: string
                child_count: number
                selected_count: number
                files_size: number
                size: number // int64_t 映射为 number
                is_dir: boolean
                add_time: number // int64_t 映射为 number，产品认为的添加时间
                actions: ActionType[]
                icon_link: string
                thumbnail_link: string
                media_type: MediaType
                file_ext: string
                audit: AuditInfo
                params: { [key: string]: string }
                status: LinkStatus

                // 本地缓存扩展字段
                can_video_play: boolean
                localfile_path: string
                fileList: FileItem[]
                filesAppended?: FileItem[] // 修改时，添加的子文件
                filesRemovedIndexs?: string[] // 修改时，删除的子文件索引列表
                sync_state: number // 当前记录的同步状态，本地缓存，还是已经从服务端会不回来
            }
            interface LinkInfo {
                url: string
                url_hash?: string // 修改Link时填写，如果不填写则自动生成
                url_type: UrlType
                name: string // 链接UI显示名称
                gcid: string
                size: number // 对应资源文件总大小
                create_time: number // int64_t 映射为 number
                file_ext: string // 文件扩展名
                actions: ActionType[] // 来源: ACTION_DOWNLOAD: 下载, ACTION_CLOUD_DOWNLOAD: 云添加, ACTION_TODO: 稍后, ACTION_PLAY: 播放
                local_saved_path: string // 文件保存路径

                // 包含子文件夹的链接信息
                is_folder: boolean
                total_count: number // 子文件总数量
                selected_count: number // 已选中的子文件数量
                selected_size: number // 已选中的子文件总大小
                fileList: FileItem[]
                filesAppended?: FileItem[] // 修改时，添加的子文件
                filesRemovedIndexs?: string[] // 修改时，删除的子文件索引列表
            }

            interface LinkSearchInfo {
                LinkID: string;
                UserID: string;
                ParentID: string;
                IsDir: number;
                Url: string;        // 链接UI显示名称
                IconLink: string;
                Name: string;
                Source: number;
                Size: number;       // int64_t 映射为 number
                MediaType: number;
                CreateAt: number;   // int64_t 映射为 number
                UpdateAt: number;   // int64_t 映射为 number
                HighLightWords: string[]
                Audit: {
                    Status: number;
                    Message: string;
                }
            }

            type LabelType = 'LABEL_UNKNOWN' | 'LABEL_LINK_STAT' | 'LABEL_LINK'
            interface LabelInfo {
                key: string
                name: string
                value: string
                type: LabelType
            }
            interface LabelList {
                labels: LabelInfo[]
            }
            interface FileItem {
                url_hash: string
                file_index: number // BT文件索引
                file_ext: string // 文件扩展名
                file_name: string // 文件UI显示名称
                file_path: string // BT文件路径
                local_saved_path: string
                gcid: string
                file_size: number
            }

            interface SyncLinksToServerParam {
                userData: string
            }
            interface SyncLinksToServerResult {
                userData: string
                syncTaskId: string
                error: common.ErrorInfo
            }
            interface GetLabelsParam {
                ignoreEvent?: boolean
            }
            interface GetLabelsResult {
                labels?: LabelList
                error: common.ErrorInfo
            }
            interface LoadLinkRecordsParam {
                dataSrcConfig?: DataSourceConfig
                rangeInfo: common.PageRange
                reload: boolean
            }
            interface LoadLinkRecordsResult {
                rangeInfo: common.PageRange
                records?: { links: LinkRecord[] },
                error: common.ErrorInfo
            }

            interface GetLinkRecordsConfig {
                urls?: string[]
                linkIds?: string[]
                ids?: string[]
                dataSrcConfig?: DataSourceConfig
                withPlaybackInfo?: boolean
            }
            // 无分页获取
            interface GetLinkRecordsParam {
                config: GetLinkRecordsConfig
                limitCount?: number
                userData?: string
                reload: boolean
            }
            interface GetLinkRecordsResult {
                records?: { links: LinkRecord[] },
                userData: string
                error: common.ErrorInfo
            }

            interface InsertLinkRecordParam {
                link: LinkInfo
                ignoreEvent?: boolean
            }
            interface InsertLinkRecordResult {
                linkId: string
                error: common.ErrorInfo
            }
            interface InsertLinkRecordsParam {
                links: LinkInfo[]
                ignoreEvent?: boolean
            }
            interface InsertLinkRecordsResult {
                error: common.ErrorInfo
            }

            interface UpdateLinkRecordParam {
                link: LinkRecord
                ignoreEvent?: boolean
            }
            interface UpdateLinkRecordResult {
                linkId: string
                error: common.ErrorInfo
            }

            interface RemoveLinkRecordsParam {
                linkIds: string[]
                ignoreEvent?: boolean
            }
            interface RemoveLinkRecordsResult {
                error: common.ErrorInfo
            }

            interface SearchLinkRecordsParam {
                keyword: string
                rangeInfo: common.PageRange
                reload: boolean
            }
            interface SearchLinkRecordsResult {
                hasMore: boolean
                rangeInfo: common.PageRange
                searchList?: { Links: LinkSearchInfo[] },
                error: common.ErrorInfo
            }

            interface VideoPlayProgress {
                total_length: string
                played_length: string
                percentage: string
            }

            interface VideoPlayDeviceInfo {
                id: string
                client_id: string
                icon_link: string
                name: string
            }
            enum DeviceType {
                PC = 0,
                MAC = 1,
                IOS = 2,
                ANDROID = 3,
                IPAD = 4,
                TV = 5,
                UNKNOWN = 6,
            }
            interface PlaybackRecord {
                id: string // 服务端索引id
                record_id: string
                url: string
                url_hash: string
                file_index: number
                resource_type: string
                resource_id: string
                name: string
                current_progress_time: number // int64_t
                video_total_time: number // int64_t
                progress: VideoPlayProgress
                update_time: string // int64_t
                gcid: string
                media_type: MediaType
                icon_link: string
                thumbnail_link: string
                device: VideoPlayDeviceInfo
                audit_state: string
                params: { [key: string]: string }
                space: string
                file_id: string
                source: string
                // 本地缓存扩展字段
                device_type: DeviceType
                sync_state: number // 当前记录的同步状态，本地缓存，还是已经从服务端会不回来
            }

            interface PlaybackInfo {
                url: string
                url_hash: string
                file_index: number
                resource_type: string
                resource_id: string
                name: string
                update_time: string // int64_t
                gcid: string
                // 本地缓存扩展字段
                current_progress_time: number // int64_t
                video_total_time: number // int64_t
                device_type: DeviceType
            }
            interface LoadPlaybackRecordsParam {
                rangeInfo: common.PageRange
                reload: boolean
            }
            interface LoadPlaybackRecordsResult {
                rangeInfo: common.PageRange
                records: { arrayRecords: PlaybackRecord[] }
                error: common.ErrorInfo
            }
            interface GetPlaybackRecordsParam {
                recKey: {
                    url?: string,
                    fileInfo?: { url: string, fileIndex?: number },
                    panInfo?: { fileId: string, space?: string }
                }
            }
            interface GetPlaybackRecordsResult {
                records: PlaybackRecord[]
                error: common.ErrorInfo
            }
            interface InsertPlaybackRecordParam {
                info: PlaybackInfo
                ignoreEvent?: boolean
            }
            interface InsertPlaybackRecordResult {
                error: common.ErrorInfo
            }
            interface RemovePlaybackRecordsParam {
                recordIds: string[]
                ignoreEvent?: boolean
            }
            interface RemovePlaybackRecordsResult {
                error: common.ErrorInfo
            }
            interface RemoveAllPlaybackRecordsParam {
                ignoreEvent?: boolean
            }
            interface RemoveAllPlaybackRecordsResult {
                error: common.ErrorInfo
            }
            
            interface GetAssociateCloudFileParam {
                url: string;
                fileIndex: number
            }
            interface GetAssociateCloudFileResult {
                url: string;
                params: { [key: string]: string }
                error: common.ErrorInfo
            }
        }
        namespace event {

            interface LinkHubEventDetail_DataSouceChanged {
                reason: string
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_TotalCountChanged {
                parentLinkId: string
                totalCount: number
                hasFinished: boolean
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_SyncToServerStatus {
                syncTaskId: string
                taskCount: number
                syncedCount: number
                failedCount: number
                hasFinished: number
                errInfo: common.ErrorInfo
            }
            enum TriggerType {
                LOCAL = 0,
                SERVER = 1,
                UNKNOWN = 2,
            }
            interface LinkRecordEventDetail_RecordAdded {
                linkId: string
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_RecordUpdated {
                linkId: string
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_RecordRemoved {
                linkId: string
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }
            interface LinkRecordEventDetail_LabelInfoChanged {
                labels: dataModals.LabelInfo[]
                errInfo: common.ErrorInfo
            }



            interface PlaybackRecordEventDetail_TotalCountChanged {
                totalCount: number
                hasFinished: boolean
                errInfo: common.ErrorInfo
            }
            interface PlaybackRecordEventDetail_SyncToServerStatus {
                syncTaskId: string
                taskCount: number
                syncedCount: number
                failedCount: number
                hasFinished: number
                errInfo: common.ErrorInfo
            }
            interface PlaybackRecordEventDetail_RecordAdded {
                recordId: string
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }
            interface PlaybackRecordEventDetail_RecordUpdated {
                recordId: string
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }
            interface PlaybackRecordEventDetail_RecordRemoved {
                recordIds: string[]
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }
            interface PlaybackRecordEventDetail_RecordAllCleared {
                trigger: TriggerType
                errInfo: common.ErrorInfo
            }

        }
    }
    namespace auth {
        interface UserAccountInfo {
            userId: string
            result: boolean
            error: dataStruct.common.ErrorInfo
        }
        interface IVerifyAccountResult {
            userId: string
            result: boolean
            error: dataStruct.common.ErrorInfo
        }
    }
    namespace app {
        interface IThunderClient {
            getClientId(): string
            getBizProvider(): biz.IBizProvider
            getEventDispatcher(): base.IEventDispatcher
        }
    }

    namespace appConfig {
        interface IDevConfig {
            getItem(key: string): string
            setItem(key: string, value: string): void
        }
    }
    namespace base {
        interface Event<T extends keyof EventDetails> {
            /** 事件类型 */
            type: T

            /** 事件内容 */
            detail: EventDetails[T]
        }

        interface EventDetails {
            LinkHubEvent_DataSouceChanged: dataStruct.event.LinkHubEventDetail_DataSouceChanged
            LinkRecordEvent_TotalCountChanged: dataStruct.event.LinkRecordEventDetail_TotalCountChanged
            LinkRecordEvent_SyncToServerStatus: dataStruct.event.LinkRecordEventDetail_SyncToServerStatus
            LinkRecordEvent_LabelInfoChanged: dataStruct.event.LinkRecordEventDetail_LabelInfoChanged
            LinkRecordEvent_RecordAdded: dataStruct.event.LinkRecordEventDetail_RecordAdded
            LinkRecordEvent_RecordUpdated: dataStruct.event.LinkRecordEventDetail_RecordUpdated
            LinkRecordEvent_RecordRemoved: dataStruct.event.LinkRecordEventDetail_RecordRemoved

            PlaybackRecordEvent_TotalCountChanged: dataStruct.event.PlaybackRecordEventDetail_TotalCountChanged
            //PlaybackRecordEvent_SyncToServerStatus: dataStruct.event.PlaybackRecordEventDetail_SyncToServerStatus
            PlaybackRecordEvent_RecordAdded: dataStruct.event.PlaybackRecordEventDetail_RecordAdded
            PlaybackRecordEvent_RecordUpdated: dataStruct.event.PlaybackRecordEventDetail_RecordUpdated
            PlaybackRecordEvent_RecordRemoved: dataStruct.event.PlaybackRecordEventDetail_RecordRemoved
            PlaybackRecordEvent_RecordAllCleared: dataStruct.event.PlaybackRecordEventDetail_RecordAllCleared
        }


        interface IEventDispatcher {
            attachEvent<T extends keyof ThunderClientAPI.base.EventDetails>(event: T, listener: any): void;
            detachEvent<T extends keyof ThunderClientAPI.base.EventDetails>(event: T, listener: any): void;
        }

    }
    namespace log {
        type ArgsType = DOMException | Error | boolean | number | string | null | undefined
        type Level = 'debug' | 'error' | 'info' | 'warn'
        interface INativeLogger {
            debug(...args: ArgsType[]): void
            error(...args: ArgsType[]): void
            info(...args: ArgsType[]): void
            warn(...args: ArgsType[]): void
        }
        function getNativeLogger(name: string): INativeLogger
    }

    namespace biz {
        interface ILinkListPresenter {
            fetchFirstPage(param: String<dataStruct.common.FetchFirstPageParam>): Promise<dataStruct.common.FetchFirstPageResult>
            fetchNextPage(param: String<dataStruct.common.FetchNextPageParam>): Promise<dataStruct.common.FetchNextPageResult>
            getLabels(param: String<dataStruct.dataModals.GetLabelsParam>): Promise<dataStruct.dataModals.GetLabelsResult>
            loadLinkRecords(param: String<dataStruct.dataModals.LoadLinkRecordsParam>): Promise<dataStruct.dataModals.LoadLinkRecordsResult>
            updateLinkRecord(param: String<dataStruct.dataModals.UpdateLinkRecordParam>): Promise<dataStruct.dataModals.UpdateLinkRecordResult>
            removeLinkRecords(param: String<dataStruct.dataModals.RemoveLinkRecordsParam>): Promise<dataStruct.dataModals.RemoveLinkRecordsResult>
        }
        interface IPlaybackRecordPresenter {
            fetchFirstPage(param: String<dataStruct.common.FetchFirstPageParam>): Promise<dataStruct.common.FetchFirstPageResult>
            fetchNextPage(param: String<dataStruct.common.FetchNextPageParam>): Promise<dataStruct.common.FetchNextPageResult>
            loadPlaybackRecords(param: String<dataStruct.dataModals.LoadPlaybackRecordsParam>): Promise<dataStruct.dataModals.LoadPlaybackRecordsResult>
            removePlaybackRecords(param: String<dataStruct.dataModals.RemovePlaybackRecordsParam>): Promise<dataStruct.dataModals.RemovePlaybackRecordsResult>
            removeAllPlaybackRecords(param: String<dataStruct.dataModals.RemoveAllPlaybackRecordsParam>): Promise<dataStruct.dataModals.RemoveAllPlaybackRecordsResult>
        }
        interface ILinkHubPresenter {
            syncLinksToServer(param: String<dataStruct.dataModals.SyncLinksToServerParam>): Promise<dataStruct.dataModals.SyncLinksToServerResult>
            saveLink(param: String<dataStruct.dataModals.InsertLinkRecordParam>): Promise<dataStruct.dataModals.InsertLinkRecordResult>
            saveLinks(param: String<dataStruct.dataModals.InsertLinkRecordsParam>): Promise<dataStruct.dataModals.InsertLinkRecordsResult>
            getLinks(param: String<dataStruct.dataModals.GetLinkRecordsParam>): Promise<dataStruct.dataModals.GetLinkRecordsResult>
            searchLinks(param: String<dataStruct.dataModals.SearchLinkRecordsParam>): Promise<dataStruct.dataModals.SearchLinkRecordsResult>
            savePlaybackRecord(param: String<dataStruct.dataModals.InsertPlaybackRecordParam>): Promise<dataStruct.dataModals.InsertPlaybackRecordResult> // 使用linkId获取跳转位置计算
            getPlaybackRecords(param: String<dataStruct.dataModals.GetPlaybackRecordsParam>): Promise<dataStruct.dataModals.GetPlaybackRecordsResult>
            getAssociateCloudFile(param: String<dataStruct.dataModals.GetAssociateCloudFileParam>): Promise<dataStruct.dataModals.GetAssociateCloudFileResult>
        }
        interface IBizProvider {
            getLinkHubPresenter(): ILinkHubPresenter
            getLinkRecordListPresenter(): ILinkListPresenter
            getPlaybackRecordPresenter(): IPlaybackRecordPresenter
        }
    }

}