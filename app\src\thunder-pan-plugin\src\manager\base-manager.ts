import jwt from 'jsonwebtoken'
import hex_md5 from 'blueimp-md5'
import { UserHelper } from '@/utils/user-helper'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { reactive } from 'vue'
import { ETabId, TabsManager } from './tabs-manager'
import { API_FILE, IPrivilegeData, TPrivilege } from '@root/common/thunder-pan-manager/pan-sdk/types'
import { safeJsonParse } from '@root/common/thunder-pan-manager/pan-sdk/utils/basic'
import { EFileSpaceFolderType } from '@root/common/thunder-pan-manager/pan-sdk/services/file'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { IPC_API_NAME } from '@root/common/thunder-pan-manager/common/ipc-define'
import { UserConfigTableManager } from '@/db/user-config'

export interface IBaseAbout {
  spaceUsage: number
  spaceLimit: number
  safeBoxSpaceUsage: number
  safeBoxSpaceLimit: number
  offlineTaskLimit: number
  offlineTaskUsage: number
}

export interface ICategoryData {
  file_total: number
  offline_total: number
  restore_total: number
}

export interface ISafeBoxFileInfo {
  id: string
  name: string
  space: string
}

export interface IISafeBoxInfo {
  hasInit: boolean
  token: string
  tokenExpiresAt: number
  verificationToken: string
}

export interface ILimit {
  limit: number | string
}

export interface IUSTData extends IPrivilegeData {
  expiration: string
  user_security_token: string
}

interface ISafeBoxTokenParseMeta {
  a: string
}
interface ISafeBoxTokenParse {
  iss: string
  aud: string
  exp: number
  iat: number
  nonce: string
  at_hash:string
  scope: string
  project_id: string
  meta: ISafeBoxTokenParseMeta
}

function getBaseAbout () {
  return {
    spaceUsage: 0,
    spaceLimit: 0,
    safeBoxSpaceUsage: 0,
    safeBoxSpaceLimit: 0,
    offlineTaskLimit: 0,
    offlineTaskUsage: 0,
  }
}

export class BaseManager {
  private static _instance: BaseManager

  static getInstance () {
    if (BaseManager._instance) {
      return BaseManager._instance
    } else {
      BaseManager._instance = new BaseManager()
      return BaseManager._instance
    }
  }

  private about: IBaseAbout = reactive<IBaseAbout>(getBaseAbout())
  private category: ICategoryData = reactive<ICategoryData>({
    file_total: 0,
    offline_total: 0,
    restore_total: 0,
  })
  private safeBoxInfo: IISafeBoxInfo = reactive({
    hasInit: false,
    token: '',
    tokenExpiresAt: 0,
    verificationToken: '',
  })
  private safeBoxFileInfo: ISafeBoxFileInfo = reactive<ISafeBoxFileInfo>({ id: '', name: '超级保险箱', space: 'SPACE_SAFE' })
  private privilege: TPrivilege = reactive({
    UPLOAD_FOLDER: null,
    CREATE_OFFLINE_TASK_LIMIT: null,
    ONLINE_PLAY_RESOLUTION: null,
    DECOMPRESS_FILE_SIZE_LIMIT: null,
    UPLOAD_FLOW_SIZE_LIMIT: null,
    SPACE_SIZE_LIMIT: null,
    TRASH_STORE_DURATION: null,
    FILE_INVALID_DURATION: null,
    USER_SECURITY_TOKEN: null,
    OFFLINE_SUB_FILE_COUNT_LIMIT: null,
  })

  init () {
    this._registerIPC()
  }

  async getDataInit () {
    await UserHelper.waitUserSignin()

    this.getBasicAbout()
    this.getSafeBoxAbout()
    this.getCategoryFileCount()
    this.getSafeBoxFileInfoService()
    this.getSafeBoxInitStatus()
    this.getUSTPrivilegeData()

    const res = await Promise.all([
      this.getSpacePrivilegeData(),
      this.getRecyclePrivilegeData(),
      this.getCreateTaskPrivilegeData(),
      this.getDecompressPrivilegeData(),
      this.getOfflineSubFileCountPrivilegeData(),
    ])
    this.privilege.SPACE_SIZE_LIMIT = res[0]
    this.privilege.TRASH_STORE_DURATION = res[1]
    this.privilege.CREATE_OFFLINE_TASK_LIMIT = res[2]
    this.privilege.DECOMPRESS_FILE_SIZE_LIMIT = res[3]
    this.privilege.OFFLINE_SUB_FILE_COUNT_LIMIT = res[4]
  }

  reset () {
    this.about.spaceUsage = 0
    this.about.spaceLimit = 0
    this.about.safeBoxSpaceUsage = 0
    this.about.safeBoxSpaceLimit = 0
    this.about.offlineTaskLimit = 0
    this.about.offlineTaskUsage = 0
    this.category.file_total = 0
    this.category.offline_total = 0
    this.category.restore_total = 0
    TabsManager.getInstance().updateTabCount(ETabId.ALL, this.category.file_total)
    TabsManager.getInstance().updateTabCount(ETabId.CLOUD_ADD, this.category.offline_total)
    TabsManager.getInstance().updateTabCount(ETabId.TRANSFER_FILE, this.category.restore_total)
  }

  async getBase () {
    this.getBasicAbout()
    this.getSafeBoxAbout()
    this.getCategoryFileCount()
  }

  getAbout () {
    return this.about
  }

  getCategory () {
    return this.category
  }

  getSafeBoxInfo () {
    return this.safeBoxInfo
  }

  getSafeBoxFileInfo () {
    return this.safeBoxFileInfo
  }

  getPrivilege () {
    return this.privilege
  }

  isSafeBoxExist () {
    return this.safeBoxFileInfo.id !== ''
  }

  async getBasicAbout () {
    const res = await ThunderPanClientSDK.getInstance().getAbout({
      params: {
        with_quotas: 'CREATE_OFFLINE_TASK_LIMIT'
      }
    })

    if (res.success && res.data) {
      // 云盘空间使用情况
      const spaceQuota = res.data!.quota!
      this.about.spaceUsage = Number(spaceQuota.usage)
      this.about.spaceLimit = Number(spaceQuota.limit)
      // 云添加使用情况
      this.about.offlineTaskLimit = Number(res.data?.quotas?.['CREATE_OFFLINE_TASK_LIMIT']?.limit)
      this.about.offlineTaskUsage = Number(res.data?.quotas?.['CREATE_OFFLINE_TASK_LIMIT']?.usage)
    }
  }

  async getSafeBoxAbout () {
    const res = await ThunderPanClientSDK.getInstance().getAbout({
      params: {
        space: 'SPACE_SAFE'
      }
    })

    if (res.success && res.data) {
      // 云盘保险箱空间使用情况
      const safeBoxSpaceQuota = res.data!.quota!
      this.about.safeBoxSpaceUsage = Number(safeBoxSpaceQuota.usage)
      this.about.safeBoxSpaceLimit = Number(safeBoxSpaceQuota.limit)
    }
  }

  async getRecyclePrivilegeData (): Promise<IPrivilegeData> {
    const res = await ThunderPanClientSDK.getInstance().getPrivilege('TRASH_STORE_DURATION');

    if (res.success) {
      return this.parsePrivilegeRes(res.data!)
    } else {
      return {
        user: '10',
        platinum: '15',
        superV: '80',
        superVYear: '80'
      }
    }
  }

  async getSpacePrivilegeData (): Promise<IPrivilegeData> {
    const res = await ThunderPanClientSDK.getInstance().getPrivilege('SPACE_SIZE_LIMIT');

    if (res.success) {
      return this.parsePrivilegeRes(res.data!)
    } else {
      return {
        user: '1099511627776',
        platinum: '3298534883328',
        superV: '6597069766656',
        superVYear: '13194139533312'
      }
    }
  }

  async getCreateTaskPrivilegeData (): Promise<IPrivilegeData> {
    const res = await ThunderPanClientSDK.getInstance().getPrivilege('CREATE_OFFLINE_TASK_LIMIT');

    if (res.success) {
      return this.parsePrivilegeRes(res.data!)
    } else {
      return {
        user: '3',
        platinum: '100',
        superV: '500',
        superVYear: '1000'
      }
    }
  }

  async getDecompressPrivilegeData (): Promise<IPrivilegeData> {
    const res = await ThunderPanClientSDK.getInstance().getPrivilege('DECOMPRESS_FILE_SIZE_LIMIT');

    if (res.success) {
      return this.parsePrivilegeRes(res.data!)
    } else {
      return {
        user: '0',
        platinum: '8589934592',
        superV: '17179869184',
        superVYear: '34359738368'
      }
    }
  }

  async getOfflineSubFileCountPrivilegeData (): Promise<IPrivilegeData> {
    const res = await ThunderPanClientSDK.getInstance().getPrivilege('OFFLINE_SUB_FILE_COUNT_LIMIT');

    if (res.success) {
      return this.parsePrivilegeRes(res.data!)
    } else {
      return {
        user: '1000',
        platinum: '2000',
        superV: '5000',
        superVYear: '5000'
      }
    }
  }

  async getCategoryFileCount () {
    const res = await ThunderPanClientSDK.getInstance().getAllCategoryFileCount()

    if (res.success && res.data) {
      this.category.file_total = Number(res.data.file_total)
      this.category.offline_total = Number(res.data.offline_total)
      this.category.restore_total = Number(res.data.restore_total)

      TabsManager.getInstance().updateTabCount(ETabId.ALL, this.category.file_total)
      TabsManager.getInstance().updateTabCount(ETabId.CLOUD_ADD, this.category.offline_total)
      TabsManager.getInstance().updateTabCount(ETabId.TRANSFER_FILE, this.category.restore_total)
    }
  }

  async getSafeBoxFileInfoService () {
    const res = await ThunderPanClientSDK.getInstance().getFileBySpace('SPACE_SAFE', EFileSpaceFolderType.SAFE)

    if (res.success && res.data) {
      this.safeBoxFileInfo.id = res.data.file_id
      this.safeBoxFileInfo.name = res.data.file_name ?? '超级保险箱'
      this.safeBoxFileInfo.space = res.data.space
    }
  }

  async getSafeBoxInitStatus () {
    const res = await ThunderPanClientSDK.getInstance().checkSafeHasInit();

    if (res.success) {
      this.safeBoxInfo.hasInit = res.data!.has_init!;
    }
  }

  async checkSafeBoxPassword (password: string) {
    const res = await ThunderPanClientSDK.getInstance().checkPassword({
      params: {
        password: hex_md5(password)
      },
    });

    if (res.success) {
      UserConfigTableManager.getInstance().setHash(password)
      this.safeBoxInfo.token = res.data!.token!;

      const decodedToken: ISafeBoxTokenParse = jwt.decode(this.safeBoxInfo.token);
      this.safeBoxInfo.tokenExpiresAt = (decodedToken?.exp ?? 0) * 1000; // 转为毫秒时间戳
    }
    return res;
  }

  async initSafeBoxPassword (password: string) {
    const res = await ThunderPanClientSDK.getInstance().initPassword({
      params: {
        password: hex_md5(password)
      },
    });

    if (res.success) {
      // 初始化密码成功，自动登录
      await this.checkSafeBoxPassword(password);
      this.safeBoxInfo.hasInit = true;
    }
    return res;
  }

  async resetSafeBoxPassword (password: string) {
    const res = await ThunderPanClientSDK.getInstance().resetPassword({
      params: {
        password: hex_md5(password),
        verification_token: this.safeBoxInfo.verificationToken,
      },
    });

    if (res.success) {
      // 重置密码成功，自动登录
      await this.checkSafeBoxPassword(password);
    }
    return res;
  }

  setSafeBoxVerificationToken (token: string) {
    this.safeBoxInfo.verificationToken = token;
  }

  getSafeBoxTokenIsExpires () {
    return !this.safeBoxInfo.token || Date.now() > this.safeBoxInfo.tokenExpiresAt
  }

  handleUSTRefreshTimer () {
    // 单位秒
    const UST = this.privilege.USER_SECURITY_TOKEN as IUSTData;
    const expiration = Number(UST?.expiration ?? 86400);

    // 在 USER_SECURITY_TOKEN 过期前 1 分钟重新请求刷新 token
    setTimeout(async () => {
      await this.getUSTPrivilegeData();
      this.handleUSTRefreshTimer();
    }, (expiration - 60) * 1000);
  }

  // 缩略图服务相关的 token
  async getUSTPrivilegeData () {
    const res = await ThunderPanClientSDK.getInstance().getPrivilege('USER_SECURITY_TOKEN');

    if (res.success && res.data) {
      this.privilege.USER_SECURITY_TOKEN = res.data.data as IUSTData;
      this.handleUSTRefreshTimer();
    }
  }

  private parsePrivilegeRes (res: API_FILE.DriveCheckPrivilegeResponse): IPrivilegeData {
    const user = safeJsonParse(res.data!['user']) as ILimit
    const platinum = safeJsonParse(res.data!['vip.platinum']) as ILimit
    const superV = safeJsonParse(res.data!['vip.super']) as ILimit
    const superVYear = safeJsonParse(res.data!['vip.super.year']) as ILimit
    // 不一定存在的字段
    const free = safeJsonParse(res.data!['free']) as ILimit
    const superV1 = safeJsonParse(res.data!['vip.super.v1']) as ILimit
    const superV2 = safeJsonParse(res.data!['vip.super.v2']) as ILimit
    const superV3 = safeJsonParse(res.data!['vip.super.v3']) as ILimit
    const superV4 = safeJsonParse(res.data!['vip.super.v4']) as ILimit
    const superV5 = safeJsonParse(res.data!['vip.super.v5']) as ILimit
    const superV6 = safeJsonParse(res.data!['vip.super.v6']) as ILimit
    const superV7 = safeJsonParse(res.data!['vip.super.v7']) as ILimit
    const superV8 = safeJsonParse(res.data!['vip.super.v8']) as ILimit
    const superV9 = safeJsonParse(res.data!['vip.super.v9']) as ILimit
    const superV10 = safeJsonParse(res.data!['vip.super.v10']) as ILimit

    return {
      user: user.limit,
      platinum: platinum.limit,
      superV: superV.limit,
      superVYear: superVYear.limit,
      free: free?.limit,
      superV1: superV1?.limit,
      superV2: superV2?.limit,
      superV3: superV3?.limit,
      superV4: superV4?.limit,
      superV5: superV5?.limit,
      superV6: superV6?.limit,
      superV7: superV7?.limit,
      superV8: superV8?.limit,
      superV9: superV9?.limit,
      superV10: superV10?.limit,
    }
  }

  private _registerIPC () {
    // 与主进程通一个 ipc context，直接注册
    client.registerFunctions({
      [IPC_API_NAME.GET_SAFE_BOX_TOKEN]: (_ctx: unknown) => {
        return this.getSafeBoxInfo().token
      },
    })
  }
}
