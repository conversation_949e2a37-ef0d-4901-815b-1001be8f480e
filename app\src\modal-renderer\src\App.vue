<script setup lang="ts">
// ========================= IMPORTS =========================
import { IpcRendererEvent, ipcRenderer } from 'electron'
import { onMounted, onBeforeUnmount, ref, defineAsyncComponent, watch } from 'vue'
import * as PopUpTypes from '@root/common/pop-up/types'
import { config } from '@root/common/config/config'
import { PopUpNS } from '@root/common/pop-up'

// ========================= TYPE DECLARATIONS =========================
// 声明全局类型以解决 TypeScript 错误
declare global {
  interface Window {
    __popupWindowId__: string
  }
}

// ========================= CONSTANTS & CONFIGS =========================
// 异步组件导入
const CreateTask = defineAsyncComponent(() => import('./views/create-task/create.vue'))
const PreCreateTask = defineAsyncComponent(() => import('./views/create-task/pre-new-task-entry.vue'))
// const PreNewLinkTask = defineAsyncComponent(() => import('./views/create-task/pre-new-link-task.vue'))
const DuplicateTask = defineAsyncComponent(() => import('./views/create-task/duplicate-task.vue'))
const DriveSpaceDialog = defineAsyncComponent(() => import('./views/create-task/disk-space-dialog/disk-space-dialog.vue'))
const Confirm = defineAsyncComponent(() => import('./views/confirm.vue'))
const Login = defineAsyncComponent(() => import('./views/login-register/index.vue'))
const Setting = defineAsyncComponent(() => import('./views/setting/index.vue'))
const LimitSpeedSetting = defineAsyncComponent(() => import('./views/setting/limit-speed.vue'))
const AddProxy = defineAsyncComponent(() => import('./views/setting/add-proxy.vue'))
const BrowserConfigGuide = defineAsyncComponent(() => import('./views/browser-config-guide/index.vue'))
const DownloadTaskMessage = defineAsyncComponent(() => import('./views/download/download-task-message.vue'))

const ThunderPanFetchBack = defineAsyncComponent(
  () => import('./views/thunder-pan/fetch-back.vue')
)
const ThunderPanPathSelector = defineAsyncComponent(
  () => import('./views/thunder-pan/path-selector.vue')
)
const DriveList = defineAsyncComponent(
  () => import('./components/new-task/drive-list/drive-list.vue')
)
const TaskSetting = defineAsyncComponent(
  () => import('./components/new-task/task-setting/task-setting.vue')
)
const DownloadCloudPath = defineAsyncComponent(
  () => import('./components/new-task/download-cloud-path/download-cloud-path.vue')
)
/** 继续添加面板 */
const KeepAdd = defineAsyncComponent(
  () => import('./views/keep-add/index.vue')
)

// 组件映射表
const componentsMap = {
  'create-task': CreateTask,
  // 自动拉起创建面板
  'pre-create-task': PreCreateTask,
  // 'pre-new-link-task': PreNewLinkTask,
  confirm: Confirm,
  login: Login,
  setting: Setting,
  'limit-speed-setting': LimitSpeedSetting,
  'add-proxy': AddProxy,
  'thunder-pan_fetch-back': ThunderPanFetchBack,
  'thunder-pan_path-selector': ThunderPanPathSelector,
  'drivelist': DriveList,
  'task-setting': TaskSetting,
  'download-cloud-path': DownloadCloudPath,
  'keep-add': KeepAdd,
  'duplicate-task': DuplicateTask,
  'browser-config-guide': BrowserConfigGuide,
  'download-task-message': DownloadTaskMessage,
  'drive-space-dialog': DriveSpaceDialog,
}

// ========================= REACTIVE VARIABLES =========================
const componetName = ref('') // TODO: 调试用，默认显示创建任务组件
const options = ref({})

// ========================= UTILITY FUNCTIONS =========================
/**
 * 将驼峰命名转换为短横线命名
 * @param str 驼峰命名字符串
 * @returns 短横线命名字符串
 */
function camelToKebab(str: string): string {
  return str
    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
    .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2')
    .toLowerCase()
}

// ========================= EVENT HANDLERS =========================
/**
 * 关闭当前弹窗
 */
const handleClose = async (params?: { scene?: string }): Promise<void> => {
  try {
    // 检查是否传递了scene参数
    if (!params?.scene) {
      console.warn('⚠️ 关闭弹窗缺少scene参数，不执行关闭操作')
      return
    }

    console.log('🔄 弹窗关闭场景:', params.scene)

    const currentWindow = PopUpNS.getCurrentWindow()
    await currentWindow.close()
    console.log('✅ 弹窗已关闭，场景:', params.scene)
  } catch (error) {
    console.error('❌ 关闭弹窗失败:', error)
  }
}

// ========================= IPC HANDLERS =========================
/**
 * 更新弹窗组件
 * @param event IPC 事件
 * @param args 组件参数
 */
async function updatePopUpComponent(
  event: IpcRendererEvent,
  args: {
    componentsName: string
    title: string
    dialogConf: Electron.BrowserViewConstructorOptions
    options: any
  }
): Promise<void> {
  try {
    console.log('🔄 更新弹窗组件:', args)

    // 更新组件名称和选项
    componetName.value = camelToKebab(args.componentsName)
    options.value = args.options

    // 判断args.options里是否有taskData，如果有且长度大于0，展示AutoCreateTask组件
    // if (
    //   args.options.taskData &&
    //   Array.isArray(args.options.taskData) &&
    //   args.options.taskData.length > 0
    // ) {
    //   console.log('🔄 任务数据:', args.options.taskData)
    //   console.log('📋 任务数据长度:', args.options.taskData.length)

    //   // 如果有taskData且长度大于0，展示AutoCreateTask组件
    //   componetName.value = 'pre-create-task'
    //   console.log('🎯 切换到 AutoCreateTask 组件')
    // }

    // 打印组件详细信息
    console.log('🎯 组件详情:', {
      componentName: componetName.value,
      originalComponentName: args.componentsName,
      title: args.title,
      props: args.options,
      propsKeys: Object.keys(args.options || {}),
      propsCount: Object.keys(args.options || {}).length,
    })

    // 设置窗口标题
    document.title = args?.title ?? ''
    const currentWindow = PopUpNS.getCurrentWindow()
    await currentWindow.setTitle(document.title)
  } catch (error) {
    console.error('❌ 更新弹窗组件失败:', error)
  }
}

/**
 * 销毁弹窗组件
 * @param event IPC 事件
 */
function destroyPopUpCompnent(event: IpcRendererEvent): void {
  console.log('🗑️ 销毁组件:', componetName.value)
  console.log('🗑️ 清除前的 Props:', options.value)

  // 重置状态
  componetName.value = ''
  options.value = {}
  document.title = ''

  console.log('✅ 组件已销毁')
}

// ========================= WATCHERS =========================
// 监听 options 变化并打印组件 props
watch(
  options,
  newOptions => {
    console.log('📋 组件 Props 更新:', {
      component: componetName.value,
      props: newOptions,
    })
  },
  { deep: true }
)

// 监听组件名称变化
watch(componetName, newComponentName => {
  console.log('🔄 当前组件:', newComponentName)
  if (newComponentName && options.value) {
    console.log('📋 当前组件 Props:', options.value)
  }
})

// ========================= LIFECYCLE HOOKS =========================
onMounted(() => {
  console.log('🚀 App 组件已挂载, 弹窗ID:', window.__popupWindowId__)
  console.log('🚀 初始组件状态:', {
    componentName: componetName.value,
    props: options.value,
  })

    // 设置全局配置
    ; (window as any).__config__ = config

  // 注册 IPC 监听器
  ipcRenderer.on('popup-wnd-notify-' + window.__popupWindowId__, updatePopUpComponent)
  ipcRenderer.on('popup-wnd-destroy-' + window.__popupWindowId__, destroyPopUpCompnent)
})

onBeforeUnmount(() => {
  console.log('🔄 App 组件即将卸载, 清理监听器')

  // 移除 IPC 监听器
  ipcRenderer.off('popup-wnd-notify-' + window.__popupWindowId__, updatePopUpComponent)
  ipcRenderer.off('popup-wnd-destroy-' + window.__popupWindowId__, destroyPopUpCompnent)
})

// ========================= COMMENTED CODE (FOR REFERENCE) =========================
// 注释掉的方法保留供参考
// const handleOk = async () => {
//   const currentWindow = PopUpNS.getCurrentWindow()
//   await currentWindow.close(PopUpTypes.Action.OK, 'hello world')
// }

// const handleCancel = async () => {
//   const currentWindow = PopUpNS.getCurrentWindow()
//   await currentWindow.close(PopUpTypes.Action.Cancel)
// }
</script>

<template>

  <div class="app-container">

    <component :is="componentsMap[componetName]" :options="options" @close="handleClose" />

  </div>

</template>

<style lang="scss" src="../../main-renderer/src/assets/css/xl-icon.scss"></style>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  width: 100%;
}
</style>
