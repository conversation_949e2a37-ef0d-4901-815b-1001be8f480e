/**
 * AlertDialog 组件的样式变体配置
 * 可以为不同类型的对话框提供不同的视觉风格
 */

export const alertDialogVariants = {
  // 信息对话框 - 蓝色主题
  info: {
    title: 'info-title',
    action: 'info-action',
    cancel: 'info-cancel',
  },
  // 成功对话框 - 绿色主题
  success: {
    title: 'success-title',
    action: 'success-action',
    cancel: 'success-cancel',
  },
  // 警告对话框 - 橙色主题
  warning: {
    title: 'warning-title',
    action: 'warning-action',
    cancel: 'warning-cancel',
  },
  // 错误对话框 - 红色主题
  error: {
    title: 'error-title',
    action: 'error-action',
    cancel: 'error-cancel',
  },
  // Thunder对话框 - 迅雷蓝主题
  thunder: {
    title: 'thunder-title',
    action: 'thunder-action',
    cancel: 'thunder-cancel',
  },
}

// CSS类需要在对应的CSS中定义
export default alertDialogVariants
