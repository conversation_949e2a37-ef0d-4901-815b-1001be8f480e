#include "StdAfx.h"
#include "SyncHttpsClient.h"

string CSyncHttpsClient::ms_strCAFilePath("");

CSyncHttpsClient::CSyncHttpsClient(int nBufSize)
#ifdef _PRODUCTRELEASE
	: m_https(nBufSize, true, ms_strCAFilePath)
#else
	: m_https(nBufSize, false, ms_strCAFilePath)
#endif
{
	
}

CSyncHttpsClient::~CSyncHttpsClient(void)
{

}

int CSyncHttpsClient::Init()
{
	char szModulePath[MAX_PATH] = {0};
	GetModuleFileNameA(NULL, szModulePath, MAX_PATH);

	char szCAFilePath[MAX_PATH] = {0};
	PathCombineA(szCAFilePath, szModulePath, "..\\cacert.pem");

	ms_strCAFilePath = szCAFilePath;

	return CSyncHttps::Init();
}

void CSyncHttpsClient::UnInit()
{
	CSyncHttps::UnInit();
}

bool CSyncHttpsClient::GetData(const string& strUrl, const string& strHeader)
{
	bool bRet = m_https.GetData(m_nRequestID, strUrl, strHeader);
	return bRet;
}

bool CSyncHttpsClient::GetData(const string& strUrl, std::vector<string> strHeaders)
{
	bool bRet = m_https.GetData(m_nRequestID, strUrl, strHeaders);
	return bRet;
}

bool CSyncHttpsClient::PostData(const string& strUrl, const string& strHeader, const string& strPostData)
{
	bool bRet = m_https.PostData(m_nRequestID, strUrl, strHeader, strPostData);
	return bRet;
}

bool CSyncHttpsClient::PostData(const string& strUrl, std::vector<string> strHeaders, const string& strPostData)
{
	bool bRet = m_https.PostData(m_nRequestID, strUrl, strHeaders, strPostData);
	return bRet;
}

bool CSyncHttpsClient::PostData(const string& strUrl, const string& strHeader, const byte* pPostData, int nPostDataSize)
{
	bool bRet = m_https.PostData(m_nRequestID, strUrl, strHeader, pPostData, nPostDataSize);
	return bRet;
}


