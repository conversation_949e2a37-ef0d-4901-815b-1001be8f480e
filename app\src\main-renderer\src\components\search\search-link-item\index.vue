<template>
  <div 
    class="search-link-item" 
    :class="{ 
      'search-link-item-disabled': disabled,
      'search-link-item-focused': focused,
      'search-item-focused': focused,
    }"
    @click="handleClick"
  >
    
    <div class="search-link-item-left">
      <div :class="['search-link-item-left-url', { 'disabled': disabled }]" v-if="link.Url">
        <img :src="link.IconLink" class="search-link-item-left-url-icon" v-tooltip="getLinkTypeTip()" />
        <span class="search-link-item-left-url-text">{{ link.Url }}</span>
      </div>
      <span :class="['search-link-item-left-title', { 'disabled': disabled }]" v-tooltip="{ content: link.Name, maxWidth: '340px', appendTo: 'parent' }" v-html="highlightText(link.Name)"></span>
      <span :class="['search-link-item-left-info', { 'disabled': disabled }]">
        {{ itemInfo }}
        <i v-if="disabled" class="xl-icon-general-details-m" v-tooltip="'此链接不可用'"></i>
      </span>
    </div>
    
    <div class="search-link-item-right">
      <button class="search-link-item-locate-btn" @click.stop="handleLocate" v-tooltip="'定位链接'">
          <i class="xl-icon-general-location-l"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ThunderUtil } from '@root/common/utils'
import { LinkHubHelper } from '@root/common/link-hub/client/link-hub-helper'
import { message } from '@root/common/components/ui/message'
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'
import dayjs from 'dayjs'
import { LinkFile } from '../types'

const LINK_ICON_MAP = {
  magnet: {
    tip: '磁力链'
  },
  http: {
    tip: '网页链接'
  },
  https: {
    tip: '网页链接'
  },
  ftp: {
    tip: '网页链接'
  },
  ed2k: {
    tip: '电驴链接'
  },
  thunder: {
    tip: '迅雷云盘链接'
  }
}

const props = defineProps<{
  link: LinkFile
  focused: boolean
  highLightWords?: string[]
}>()

const emit = defineEmits<{
  select: [link: LinkFile]
  locate: [link: LinkFile]
  mouseenter: []
}>()

const disabled = computed(() => {
  // 这里可以根据实际情况判断链接是否可用
  return props.link?.Audit?.Status === 2
})

const itemInfo = computed(() => {
  if (disabled.value) {
    return props.link?.Audit?.Message || '审核不通过'
  }
  // 格式化显示信息：添加时间 + 文件大小 + (如果是文件夹则显示文件数量)
  const info = `${formatAddTime(props.link?.CreateAt)} 添加 · ${ThunderUtil.bytesToSize(props.link?.Size, 2)}`
  
  return info
})

const formatAddTime = (addTime: number) => {
  if (!addTime) return '未知时间'
  
  const now = dayjs();
  const date = dayjs(addTime);
  if (now.diff(date, 'second') < 60) {
    return '刚刚';
  }
  if (now.isSame(date, 'day')) {
    return `今天 ${date.format('HH:mm')}`;
  }
  return date.format('YYYY-MM-DD HH:mm');
}

// 高亮搜索文本
const highlightText = (text: string) => {
  if (!props.highLightWords?.length || !text) return text
  
  // 对每个搜索词做一次全局高亮
  return props.highLightWords.reduce((str, word) => {
    if (!word) return str;
    const escaped = word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escaped})`, 'gi');
    return str.replace(regex, '<span class="highlight">$1</span>');
  }, text);
}

const getLinkTypeTip = () => {
  // 从URL判断链接类型
  const url = props.link.Url || '';
  if (url.startsWith('magnet:')) return LINK_ICON_MAP.magnet.tip;
  if (url.startsWith('ed2k:')) return LINK_ICON_MAP.ed2k.tip;
  if (url.startsWith('thunder:')) return LINK_ICON_MAP.thunder.tip;
  if (url.startsWith('http:')) return LINK_ICON_MAP.http.tip;
  if (url.startsWith('https:')) return LINK_ICON_MAP.https.tip;
  if (url.startsWith('ftp:')) return LINK_ICON_MAP.ftp.tip;
  
  // 默认返回HTTP链接提示
  return LINK_ICON_MAP.http.tip;
}


const fetchLinkDetail = async (linkId: string) => {
  try {
    // 获取父链接详情
    const res = await LinkHubHelper.getInstance().getLinks({
      config: {
        ids: [linkId],
        withPlaybackInfo: true,
      },
      limitCount: 1,
      userData: '',
      reload: false,
    })
    return res?.records?.links?.[0]
  } catch (error) {
    console.error('fetchLinkDetail error', error)
    return null
  }
}

async function handleClick() {
  if (disabled.value) {
    message({ message: '文件已失效，无法打开', type: 'warning' })
    return
  }
  if (props.link.IsDir === 1) {
    emit('locate', props.link)
    return
  } else {
    const linkDetail = await fetchLinkDetail(props.link.LinkID)
    if (linkDetail) {
      ConsumeManagerNs.consumeLink(linkDetail)
    }
    emit('select', props.link)
  }
}

const handleLocate = () => {
  // 定位链接的逻辑
  emit('locate', props.link)
}
</script>

<style lang="scss" scoped>
.search-link-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: var(--border-radius-M2, 10px);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
  height: 86px;


  &:hover,
  &-focused {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
  }

  .xl-icon-general-details-m {
    color: var(--functional-error-default, #FF4D4F);
  }

  &-left {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
    min-width: 0;

    &-url {
      display: flex;
      align-items: center;
      height: 16px;

      &-icon {
        width: 16px;
        height: 16px;
      }

      &-text {
        color: var(--font-font-3, #86909C);
        font-size: 11px;
        margin: 0 4px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &-icon {
        width: 16px;
        height: 16px;
        margin-left: 4px;
      }

      &.disabled {
        .search-link-item-left-url-text {
          color: var(--font-font-4, #C9CDD4);
        }
        .search-link-item-left-url-icon {
          opacity: 0.5;
        }
      }
    }

    &-title {
      font-size: 13px;
      line-height: 20px;
      color: var(--font-font-1, #272E3B);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: fit-content;
      max-width: 100%;

      :deep(.highlight) {
        color: var(--primary-primary-default, #226DF5);
      }

      &.disabled {
        color: var(--font-font-4, #C9CDD4);

        :deep(.highlight) {
          color: var(--primary-primary-disabled, #97C4FB);
        }
      }
    }

    &-info {
      display: flex;
      align-items: center;
      font-size: 12px;
      line-height: 18px;
      color: var(--font-font-3, #86909C);

      &.disabled {
        color: var(--functional-error-default, #FF4D4F);
      }
    }
  }

  &-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
  }

  &-locate-btn {
    width: 20px;
    height: 20px;
    display: none;
    align-items: center;
    justify-content: center;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    &:hover {
      cursor: pointer;
    }

    i {
      font-size: 20px;
      color: var(--font-font-2, #4e5769);
    }

    &:hover i {
      color: var(--primary-primary-default, #226DF5);
    }
  }

  &:hover,
  &-focused {
    .search-link-item-locate-btn {
      display: flex;
    }
  }
}
</style> 