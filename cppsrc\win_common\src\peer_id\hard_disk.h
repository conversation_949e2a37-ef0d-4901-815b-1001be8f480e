#ifndef _HARD_DISK_3743D696_BE86_4E71_9D16_4DFA679C7B6D_H_
#define _HARD_DISK_3743D696_BE86_4E71_9D16_4DFA679C7B6D_H_


#include <string>

class hard_disk
{
public:
	static bool get_serial_number( std::string& serial_number );
	static bool get_volume_serial(std::string& result);

private:
	static bool read_physical_drive_in_nt_with_admin_rights( std::string& serial_number );
	static bool read_ide_drive_as_scsi_drive_in_nt( std::string& serial_number );
	static bool read_physical_drive_in_nt_with_zero_rights( std::string& serial_number );
	static bool read_physical_drive_in_nt_using_smart( std::string& serial_number );
	static bool read_drive_ports_in_win_9x( std::string& serial_number );

private:
	hard_disk() {};
	~hard_disk() {};
};

#endif // _HARD_DISK_3743D696_BE86_4E71_9D16_4DFA679C7B6D_H_
