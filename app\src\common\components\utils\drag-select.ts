import { DomHelper } from '@root/common/components/utils/dom-helper'

export interface DST_DragPoint {
  x: number
  y: number
}

export interface DST_DragPadding {
  top?: number
  right?: number
  bottom?: number
  left?: number
}

export type DST_DragFunction = (selectArea: HTMLElement, picked?: HTMLElement[] ) => void

/**
 * 实现在指定区域内拖拽框选
 */
export default class DragSelect {
  private enable: boolean = true                          // 是否启用
  private startPoint!: DST_DragPoint | null               // 鼠标点击的起始位置
  private selectArea!: HTMLElement | null                 // 框选浮层的元素
  private basicRect!: DOMRect                             // 指定触发区域元素的 Rect 信息
  private basicClass: string                              // 指定的触发区域的 className
  private basicElement!: HTMLElement                      // 指定的触发区域的元素
  private detectClass!: string                            // 需要判断是否在区域内的元素
  private scrollClass!: string                            // 内容区（滚动区域）的元素（用于执行自动滚动）
  private areaPadding?: DST_DragPadding                   // 浮层计算的偏移量
  private onDragging?: DST_DragFunction                   // 拖拽过程中的回调
  private onDragEnd?: DST_DragFunction                    // 拖拽结束后的回调

  private dragging: boolean = false                       // 是否正在拖动
  private scrollDelta: number = 0                         // 滚动差值
  private scrollRAFId!: number                            // 检测函数 RAFId（自动滚动）
  private scrollElement!: HTMLElement                     // 滚动区域的元素
  private scrollStartScrollTop: number = 0                // 滚动区域的起始 scrollTop

  /**
   * @param basicClass 指定区域元素
   * @param detectClass 需要判断是否在区域内的元素（在 basicClass 内）
   * @param scrollClass 内容区（滚动区域）的元素（用于执行自动滚动，在 basicClass 内）
   * @param onDragging 拖拽过程中的回调
   * @param onDragEnd 拖拽结束后的回调
   * @param areaPadding 浮层计算的偏移量
   */
  constructor (
    basicClass: string,
    detectClass: string,
    scrollClass: string,
    onDragging?: DST_DragFunction,
    onDragEnd?: DST_DragFunction,
    areaPadding?: DST_DragPadding
  ) {
    this.basicClass = basicClass
    this.detectClass = detectClass
    this.scrollClass = scrollClass
    this.onDragging = onDragging
    this.onDragEnd = onDragEnd
    this.areaPadding = areaPadding || {}

    window.addEventListener("mousedown", this.handleMouseDown.bind(this))
    window.addEventListener("mousemove", this.handleMouseMove.bind(this))
    window.addEventListener("mouseup", this.handleMouseUp.bind(this))
  }

  private handleMouseDown (event: MouseEvent) {
    // 禁用则不处理
    if (event.button !== 0 || !this.enable || DomHelper.eventContainClass(event, 'disable-drag-select')) return
    // 查找在可视范围内的元素
    document.body.querySelectorAll(this.basicClass).forEach(el => {
      if (el.clientHeight) this.basicElement = el as any
    })
    // 鼠标点击不在指定的元素则不处理
    if (!this.basicElement || !DomHelper.eventContainClass(event, this.basicClass.replace(/\.|\#/g, ''))) return

    this.scrollElement = this.basicElement.querySelector(this.scrollClass) as HTMLDivElement
    if (!this.scrollElement) return

    this.basicRect = this.basicElement.getBoundingClientRect()
    this.scrollStartScrollTop = this.scrollElement.scrollTop
    this.startPoint = {
      x: event.clientX,
      y: event.clientY,
    }

    this.selectArea = document.createElement("div")
    this.selectArea.className = 'td-drag-area'
    this.selectArea.style.display = 'none'        // 防止创建元素时的 UI 抖动

    document.body.appendChild(this.selectArea)
  }

  private handleMouseMove (event: MouseEvent) {
    if (!this.startPoint || !this.scrollElement) return
    // 添加一个自动滚动的监测
    if (!this.dragging) {
      this.scrollRAFId = window.requestAnimationFrame(this.autoScroll.bind(this))
      this.dragging = true
    }
    // 鼠标的位移方向
    const xDirection = event.clientX > this.startPoint.x ? 'right' : 'left'
    const yDirection = event.clientY > this.startPoint.y ? 'down' : 'up'
    // 基准范围
    const basicTop = this.basicRect.top + (this.areaPadding?.top ?? 0)
    const basicRight = this.basicRect.right - (this.areaPadding?.right ?? 0)
    const basicBottom = this.basicRect.bottom - (this.areaPadding?.bottom ?? 0)
    const basicLeft = this.basicRect.left + (this.areaPadding?.left ?? 0)
    // 限制框选区域的范围
    let _x = event.clientX
    if (xDirection === 'left') {
      _x = _x < basicLeft ? basicLeft : _x
    } else {
      _x = _x > basicRight ? basicRight : _x
    }

    let _y = event.clientY
    if (yDirection === 'down') {
      if (_y > basicBottom) {
        this.scrollDelta = _y - basicBottom   // 向下时，自动滚动的差值
        _y = basicBottom
      } else {
        this.scrollDelta = 0
      }
    } else {
      if (_y < basicTop) {
        this.scrollDelta = _y - basicTop      // 向上时，自动滚动的差值
        _y = basicTop
      } else {
        this.scrollDelta = 0
      }
    }

    // 重新计算 y 轴起点
    let startY = this.startPoint.y
    const relative = this.scrollStartScrollTop - this.scrollElement.scrollTop
    startY = startY + relative

    const currentPoint = { x: _x, y: _y, }
    // 计算样式
    const width = currentPoint.x - this.startPoint.x
    const height = currentPoint.y - startY
    const left = width < 0 ? currentPoint.x : this.startPoint.x
    const top = height < 0 ? currentPoint.y : startY
    // 设置样式
    this.selectArea!.style.width = Math.abs(width) + 'px'
    this.selectArea!.style.height = Math.abs(height) + 'px'
    this.selectArea!.style.left = left + 'px'
    this.selectArea!.style.top = top + 'px'
    this.selectArea!.style.display = 'block'
    // 执行回调
    if (this.onDragging) {
      this.onDragging(this.selectArea!)
    }
  }

  private handleMouseUp () {
    if (!this.startPoint || !this.scrollElement) return
    // 移除自动滚动监测
    if(this.dragging) {
      window.cancelAnimationFrame(this.scrollRAFId)
      this.dragging = false
      this.scrollDelta = 0
    }
    // 回调 起点，终点，框选区域的元素
    if (this.onDragEnd) {
      const picked = this.getDetectElementsInArea()
      this.onDragEnd(this.selectArea!, picked)
    }
    // 移除框选元素
    this.startPoint = null
    this.selectArea = null
    document.body.querySelectorAll('.td-drag-area').forEach(el => {
      el.parentNode?.removeChild(el)
    })
  }

  private getDetectElementsInArea () {
    const picked: HTMLElement[] = []
    const items = this.basicElement.querySelectorAll(this.detectClass) as NodeListOf<HTMLDivElement>
    // 判断元素是否在框选区域内
    items.forEach(el => {
      if (this.isTargetInArea(el, this.selectArea!)) picked.push(el)
    })
    return picked
  }

  private autoScroll () {
    this.scrollElement?.scrollTo({ top: this.scrollElement.scrollTop + this.scrollDelta })
    this.scrollRAFId = window.requestAnimationFrame(this.autoScroll.bind(this))
  }

  /**
   * 更新功能是否启用
   * @param enable 是否启用
   */
  updateEnable (enable: boolean) {
    this.enable = enable
  }

  /**
   * 判断指定元素是否在框选区域内
   * @param target 指定元素
   * @param selectArea 框选元素
   * @returns true / false
   */
  isTargetInArea (target: HTMLElement, selectArea: HTMLElement) {
    if (!selectArea || !target) return

    const areaRect = selectArea.getBoundingClientRect()
    const targetRect = target.getBoundingClientRect()
    // 元素区域计算，两个元素是否存在重合（距离为 0 即算为重合）
    const zx = Math.abs(targetRect.left + targetRect.right - (areaRect.left + areaRect.right))
    const x = targetRect.width + areaRect.width
    const zy = Math.abs(targetRect.top + targetRect.bottom - (areaRect.top + areaRect.bottom))
    const y = targetRect.height + areaRect.height

    return zx <= x && zy <= y
  }

  /**
   * 移除相关事件监听
   */
  disconnect () {
    window.removeEventListener("mousedown", this.handleMouseDown.bind(this))
    window.removeEventListener("mousemove", this.handleMouseMove.bind(this))
    window.removeEventListener("mouseup", this.handleMouseUp.bind(this))
  }

  /**
   * 计算虚拟列表中被选中的元素起始与结尾 index
   * @param picked 选中的元素列表
   * @param selectArea 选择框元素
   * @param itemHeight 列表中子项的高度
   * @returns { startIndex, lastIndex }
   */
  static getDragSelectIndex (picked: HTMLElement[], selectArea: HTMLElement, itemHeight: number) {
    if (!selectArea || !picked.length) return {}

    const rect = selectArea.getBoundingClientRect()
    const first = picked[0]
    const last = picked[picked.length - 1]

    const rect_1 = first.getBoundingClientRect()
    const rect_2 = last.getBoundingClientRect()

    let startIndex = Number(first.dataset.index)
    let lastIndex = Number(last.dataset.index)

    // 在可视区域内最下面的元素的 top - 一个子项高度，要大于框选元素的 top 时，说明有元素在框选之外，
    // 按 top 的 差值 除以 一个子项高度，并向上取整，得出最上面的的 index
    if (rect_1.top - itemHeight > rect.top) {
      startIndex = startIndex - Math.ceil((rect_1.top - rect.top) / itemHeight)
    }
    // 在可视区域内最下面的元素的 bottom + 一个子项高度，要小于框选元素的 bottom 时，说明有元素在框选之外，
    // 按 bottom 的 差值 除以 一个子项高度，并向上取整，得出最下面的的 index
    if (rect_2.bottom + itemHeight < rect.bottom) {
      lastIndex = lastIndex + Math.ceil((rect.bottom - rect_2.bottom) / itemHeight)
    }

    return { startIndex, lastIndex }
  }
}
