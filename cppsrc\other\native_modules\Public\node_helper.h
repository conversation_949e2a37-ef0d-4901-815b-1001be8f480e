#pragma once
#pragma warning(push)
#pragma warning(disable: 4100)
#pragma warning(disable: 26454)
#pragma warning(disable: 4996)
#pragma warning(disable: 28251)
#pragma warning(disable: 26812)
#include <node.h>
#include <v8.h>
#include <uv.h>
#include <node_object_wrap.h>
#pragma warning(pop)

#pragma warning(disable: 4505)
namespace NodeHelper {

	template<class T = v8::String>
	v8::Local<T> GetV8ObjectKeyValue(v8::Isolate* isolate, v8::Local<v8::Object> object, const char* key) {
		return object->Get(isolate->GetCurrentContext(), v8::String::NewFromUtf8(isolate, key).ToLocalChecked()).ToLocalChecked().As<T>();
	}

	template<class T = v8::String>
	void SetV8ObjectKeyValue(v8::Isolate* isolate, v8::Local<v8::Object> object, const char* key, v8::Local<v8::Value> value) {
		object->Set(isolate->GetCurrentContext(), v8::String::NewFromUtf8(isolate, key).ToLocalChecked(), value);
	}

	static unsigned int GetV8Uint32Value(v8::Isolate* isolate, v8::Local<v8::Number> number)
	{
		return number->Uint32Value(isolate->GetCurrentContext()).ToChecked();
	}

	static unsigned int GetV8Uint32Value(v8::Isolate* isolate, v8::Local<v8::Value> number)
	{
		return number->Uint32Value(isolate->GetCurrentContext()).ToChecked();
	}

	static int GetV8int32Value(v8::Isolate* isolate, v8::Local<v8::Value> number)
	{
		return number->Int32Value(isolate->GetCurrentContext()).ToChecked();
	}

	static int64_t GetV8IntegerValue(v8::Isolate* isolate, v8::Local<v8::Value> number)
	{
		return number->IntegerValue(isolate->GetCurrentContext()).ToChecked();
	}

	static double GetV8NumberValue(v8::Isolate* isolate, v8::Local<v8::Value> number)
	{
		return number->NumberValue(isolate->GetCurrentContext()).ToChecked();
	}

	static void V8WriteUtf8(v8::Isolate* isolate, const v8::Local<v8::Value>& value, char* buffer, int buflen)
	{
		value->ToString(isolate->GetCurrentContext()).ToLocalChecked()->WriteUtf8(isolate, buffer, buflen);
	}

	static void V8WriteUtf8(v8::Isolate* isolate, v8::Local<v8::String>& value, char* buffer, int buflen)
	{
		value->ToString(isolate->GetCurrentContext()).ToLocalChecked()->WriteUtf8(isolate, buffer, buflen);
	}

	static void V8Write(v8::Isolate* isolate, const v8::Local<v8::Value>& value, uint16_t* buffer, int start =0, int buflen = -1)
	{
		value->ToString(isolate->GetCurrentContext()).ToLocalChecked()->Write(isolate, buffer, start, buflen);
	}

	static void WriteOneByte(v8::Isolate* isolate, const v8::Local<v8::Value>& value, uint8_t* buffer, int start = 0, int buflen = -1)
	{
		value->ToString(isolate->GetCurrentContext()).ToLocalChecked()->WriteOneByte(isolate, buffer, start, buflen);
	}

	static v8::Local<v8::String> NewFromUtf8(v8::Isolate* isolate, const char* buffer)
	{
		return v8::String::NewFromUtf8(isolate, buffer).ToLocalChecked();
	}

	static v8::Local<v8::String> NewFromTwoByte(v8::Isolate* isolate, const uint16_t* buffer)
	{
		return v8::String::NewFromTwoByte(isolate, buffer).ToLocalChecked();
	}

	static v8::Local<v8::String> NewFromOneByte(v8::Isolate* isolate, const uint8_t* buffer)
	{
		return v8::String::NewFromOneByte(isolate, buffer).ToLocalChecked();
	}
}
#pragma warning(default: 4505)