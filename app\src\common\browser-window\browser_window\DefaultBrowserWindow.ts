import type {
  SimpleBrowserWindowEvent,
  EventListener,
  ISimpleBrowserWindow,
} from '@xbase/electron_common_kit'
import { MainProcessBrowserWindow } from './MainProcessBrowserWindow'
import { RendererProcessBrowserWindow } from './RendererProcessBrowserWindow'
import type { BrowserWindowConstructorOptions } from 'electron'
import type { TBrowserWindowExtraConfig } from './type'

export class DefaultBrowserWindow implements ISimpleBrowserWindow {
  private _window: ISimpleBrowserWindow

  constructor(
    options?: BrowserWindowConstructorOptions,
    extraConfig?: TBrowserWindowExtraConfig,
  ) {
    if (process.type === 'browser') {
      this._window = new MainProcessBrowserWindow(options, extraConfig)
    } else {
      this._window = new RendererProcessBrowserWindow(options, extraConfig)
    }
  }

  // MainProcessBrowserWindow: main on browserWindow event
  // RendererProcessBrowserWindow: RendererProcessBrowserWindow.on
  on(event: SimpleBrowserWindowEvent, listener: EventListener<any[]>): void {
    this._window.on(event, listener)
  }
  once(event: SimpleBrowserWindowEvent, listener: EventListener<any[]>): void {
    this._window.once(event, listener)
  }
  off(event: SimpleBrowserWindowEvent, listener: EventListener<any[]>): void {
    this._window.off(event, listener)
  }

  async loadURL(url: string): Promise<void> {
    return await this._window.loadURL(url)
  }

  async getURL(): Promise<string | undefined> {
    return await this._window.getURL()
  }

  async destroy(): Promise<void> {
    return await this._window.destroy()
  }

  async isDestroyed(): Promise<boolean> {
    return await this._window.isDestroyed()
  }
}
