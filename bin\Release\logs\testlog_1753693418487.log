[2025-07-28 17:03:38.487][37624:11852][error][xl::dk::CommonThunderStorage::Init]: CommonThunderStorage, begin to open taskdb, path=C:\xunlei\project\thunder_2025\bin\profiles\NewTaskDb.dat (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:30)
[2025-07-28 17:03:38.488][37624:11852][info][xl::dk::DkProxyImpl::Init]: XDLInterface::Load nRet=0, appkey=xzcGMuWE1QX1A7MA^^SDK==26bfbf7a346bcf2ac776d5b7e1cb1c66 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:30)
[2025-07-28 17:03:38.488][37624:33952][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE TaskBase(TaskId BIGINT PRIMARY KEY,Type INT,Status INT,StatusChangeTime BIGINT,SavePath NVARCHAR,TotalReceiveSize BIGINT,TotalSendSize BIGINT,TotalReceiveValidSize BIGINT,TotalUploadSize BIGINT,CreationTime BIGINT,FileCreated INT,CompletionTime BIGINT,DownloadingPeriod BIGINT,RemovingToRecycleTime BIGINT,FailureErrorCode INT,Url NVARCHAR,ReferenceUrl NVARCHAR,ResourceSize BIGINT,Name NVARCHAR,Cid NVARCHAR,Gcid NVARCHAR,Description NVARCHAR,CategoryId INT,ResourceQueryCid NVARCHAR,CreationRequestType INT,StartMode INT,NamingType INT,StatisticsReferenceUrl NVARCHAR,UserRead INT,FileSafetyFlag INT,Playable INT,BlockInfo BLOB,OpenOnComplete INT,SpecialType INT,Proxy BLOB,OriginReceiveSize BIGINT,P2pReceiveSize BIGINT,P2sReceiveSize BIGINT,OfflineReceiveSize BIGINT,VipReceiveSize BIGINT,VipResourceEnableNecessary INT,ConsumedVipSize BIGINT,Forbidden INT,OptionalChannelDataSize BLOB,OwnerProductId INT,UserData Text,UrlCodePage INT,ReferenceUrlCodePage INT,StatisticsReferenceUrlCodePage INT,GroupTaskId BIGINT,DownloadSubTask INT,TagValue INT,InnerNatReceiveSize BIGINT,AdditionFlag INT,ProductInfo NVARCHAR,Origin NVARCHAR,FreeDcdnReceiveSize BIGINT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-07-28 17:03:38.489][37624:33952][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE Category(CategoryId INT PRIMARY KEY,Name NVARCHAR,Description NVARCHAR,UserName NVARCHAR,Password NVARCHAR) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-07-28 17:03:38.489][37624:33952][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE P2spTask(TaskId BIGINT PRIMARY KEY,Cookie NVARCHAR,UseOriginResourceOnly INT,OriginResourceThreadCount INT,FileNameFixed INT,DisplayUrl NVARCHAR,UserAgent NVARCHAR) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-07-28 17:03:38.489][37624:33952][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE BtTask(TaskId BIGINT PRIMARY KEY,SeedFile NVARCHAR,InfoId NVARCHAR,DownloadFileOrderData BLOB,Tracker NVARCHAR,DisplayName NVARCHAR) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-07-28 17:03:38.489][37624:33952][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE BtFile(BtFileId INTEGER PRIMARY KEY,BtTaskId BIGINT,Status INT,FileIndex INT,Visible INT,Download INT,FilePath NVARCHAR,FileName NVARCHAR,FileSize BIGINT,FileOffset BIGINT,ReceivedSize BIGINT,Cid NVARCHAR,Gcid NVARCHAR,OptionalChannelDataSize BLOB,VideoHeadFirstTime INT,VideoHeadFirstStatus INT,FailureErrorCode INT,UserRead INT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-07-28 17:03:38.489][37624:33952][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=ALTER TABLE BtFile ADD COLUMN UserRead INT DEFAULT 0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-07-28 17:03:38.489][37624:33952][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE EmuleTask(TaskId BIGINT PRIMARY KEY,FileHash NVARCHAR,ConfigureFilePath NVARCHAR,VideoHeadFirstTime INT,VideoHeadFirstStatus INT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-07-28 17:03:38.489][37624:33952][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE GroupTask(TaskId BIGINT PRIMARY KEY,SubTaskOrder BLOB,DownloadInOrder INT,DownloadingSubTaskCount INT,SmartMoveTimeout INT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-07-28 17:03:38.693][37624:11852][info][xl::dk::DkProxyImpl::Init]: XL_Init nRet=0, appkey=xzcGMuWE1QX1A7MA^^SDK==26bfbf7a346bcf2ac776d5b7e1cb1c66 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:40)
[2025-07-28 17:03:38.693][37624:11852][info][xl::dk::CategoryManager::Load]: CategoryManager::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\CategoryManager.cpp:10)
[2025-07-28 17:03:39.020][37624:11852][info][xl::dk::TaskManager::LoadTask]: TaskManager begin load taskbase from storage (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:126)
[2025-07-28 17:03:39.038][37624:11852][info][xl::dk::TaskManager::LoadTask]: TaskManager end load taskbase from storage (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:129)
[2025-07-28 17:03:39.038][37624:11852][info][xl::dk::TaskManager::LoadTask]: load finish, task count=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:231)
[2025-07-28 17:03:40.033][37624:11852][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:613)
[2025-07-28 17:03:41.035][37624:11852][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:613)
[2025-07-28 17:03:42.036][37624:11852][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:613)
[2025-07-28 17:03:43.036][37624:11852][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:613)
[2025-07-28 17:03:44.037][37624:11852][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:613)
[2025-07-28 17:03:45.037][37624:11852][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:613)
[2025-07-28 17:03:46.037][37624:11852][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:613)
[2025-07-28 17:03:47.038][37624:11852][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:613)
[2025-07-28 17:03:48.039][37624:11852][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:613)
