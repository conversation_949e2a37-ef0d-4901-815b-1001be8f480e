/**
 * 二级弹框的类型定义
 */
export enum Action {
  Unknown,
  OK,
  Cancel,
  Close,
}

export interface ResolvePayload {
  action: Action;
  [prop: string]: string | number | boolean | Object;
}

/** confirm 类型 */
export enum ConfirmType {
  Info = 'info',
  Warning = 'warning',
  Error = 'error',
  Success = 'success'
}

export enum RelatePosType {
  Custom = 0,  // 自定义位置
  CenterParent = 1, // 基于父窗口居中
  CenterScreen = 2, // 基于屏幕居中
  RightBottom = 3, // 右下角
}

export interface ConfirmOptions {
  /** confirm 标题 */
  title: string;
  /** confirm 类型 */
  type: ConfirmType;
  /** 内容 */
  content?: string;
  /** 确认按钮文本 */
  okText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮是否可见 */
  okVisible?: boolean;
  /** 取消按钮是否可见 */
  cancelVisible?: boolean;
  /** 窗口宽 */
  windowWidth?: number;
  /** 窗口高 */
  windowHeight?: number;
  /** 父窗口id */
  parentId?: number;
}
