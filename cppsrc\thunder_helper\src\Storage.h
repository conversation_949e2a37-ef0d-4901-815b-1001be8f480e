#ifndef XL_FF067F3A_5594_4515_8F55_FA1143E9A947
#define XL_FF067F3A_5594_4515_8F55_FA1143E9A947

#include <xlcommon.h>
#include <thread>
#include <map>
#include <list>
#ifdef WIN32
#include <js_native_api.h>
#include <js_native_api_types.h>
#elif defined(__APPLE__) && defined(__MACH__)
#include <node_api.h>
#include <node.h>
#include <v8.h>
#endif
#include <AddonOpt.h>
#include "./sqlite/sqlite3.h"
#include "./StorageValue.h"

struct SqliteStorageCommand {
	std::function<void()> run;
	std::function<void()> cb;
};

class SqliteStorage {
public:
	SqliteStorage(const std::string& strDb, std::shared_ptr<xl::thread::ThreadAffinity> main);
	SqliteStorage() = delete;

	xl::coroutine::AsyncTask<bool> Execute(std::string&& strSql, bool bHigh);
	xl::coroutine::AsyncTask<bool> Query(std::string&& strSql, std::vector<std::map<std::string, StorageValue*>>& values, bool bHigh);

private:
	void Load();
	AsyncTask<void> LoopRunTask();

private:
	std::shared_ptr<xl::thread::ThreadAffinity> m_mainAffinity{ nullptr };
	std::shared_ptr<xl::thread::ThreadAffinity> m_threadAffinity{ nullptr };
	std::thread m_t;
	std::string m_strDb;
	xl::coroutine::CoroLock m_loadLock{ true };
	sqlite3* m_pDataBase{ nullptr };
	uv_loop_t m_loop;

	std::list< SqliteStorageCommand> m_commands;
	bool m_bRunLoop{ false };
};

class SqliteStorageAddon {
public:
	static void Init(napi_env env, napi_value exports);

private:
	static napi_value Execute(napi_env env, napi_callback_info info);
	static napi_value Query(napi_env env, napi_callback_info info);
	static napi_value JSConstructor(napi_env env, napi_callback_info info);
};

#endif