<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  /** 加载器大小 */
  size?: 'small' | 'medium' | 'large' | number
  /** 加载器颜色 */
  color?: string
  /** 是否显示文字 */
  text?: string
  /** 是否全屏显示 */
  fullscreen?: boolean
  /** 背景遮罩 */
  mask?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  color: 'var(--primary-primary-default, #226DF5)',
  text: '',
  fullscreen: false,
  mask: false
})

// 计算样式
const loadingStyle = computed(() => {
  const sizeMap = {
    small: '16px',
    medium: '24px',
    large: '32px'
  }

  const size = typeof props.size === 'number'
    ? `${props.size}px`
    : sizeMap[props.size]

  return {
    width: size,
    height: size,
    color: props.color
  }
})

const loadingIconStyle = computed(() => {
  const sizeMap = {
    small: '16px',
    medium: '24px',
    large: '32px'
  }

  const size = typeof props.size === 'number'
    ? `${props.size}px`
    : sizeMap[props.size]

  return {
    fontSize: size
  }
})

const containerClass = computed(() => ({
  'loading-spinner': true,
  'loading-spinner-fullscreen': props.fullscreen,
  'loading-spinner-mask': props.mask
}))



</script>

<template>
  <div :class="containerClass" :style="loadingStyle">
    <div class="loading-spinner-icon">
      <i class="xl-icon-general-loading-l" :style="loadingIconStyle"></i>
    </div>
    <div v-if="text" class="loading-spinner-text">{{ text }}</div>
  </div>
</template>

<style lang="scss" scoped>
.loading-spinner {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: v-bind(color);

  &-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background-color: var(--primary-primary-background-default, #E8F4FF);
  }

  &-icon {
    i {
      display: inline-block;
      animation: loading-spinner-rotate 1s linear infinite;
    }
  }

  &-mask {
    background-color: var(--background-background-mask, #272E3B33);
  }

  &-text {
    margin-top: 8px;
    font-size: 14px;
    color: var(--font-font-3, #86909C);
  }
}

@keyframes loading-spinner-rotate {
  100% {
    transform: rotate(360deg);
  }
}
</style>
