import { clipboard } from 'electron'
import { MediaType } from '@root/common/player/base'
import { AplayerStack } from '@root/common/player/client/aplayer-stack'
import { API_FILE, API_SHARE } from '@root/common/thunder-pan-manager/pan-sdk/types'
import { checkCanUnzip, extendDriveFile, getMediaType, getPreviewType, isPassAuditFilter, isPassComplete, isPassSpace, Phase } from '@root/common/thunder-pan-manager/pan-sdk/utils/drive'
import { EDriveFileOperation, FileNameForbidReg, FileNameForbidRegString, SYSTEM_FOLDER_TYPE_LIST } from '@root/common/thunder-pan-manager/pan-sdk/utils/file'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import usePromptDialog from '@root/common/components/ui/Dialog/usePromptDialog'
import { useDateFormat, useNow } from '@vueuse/core'
import { BaseManager } from '../manager/base-manager'
import { PanBusinessHelper } from './business-helper'
import { CreateConfirmDialog } from '@/components/delete-confirm-dialog'
import { CreateShareDialog } from '@/components/share-dialog'
import { DriveFileManager } from '@/manager/drive-file-manager'
import { TransferFileManager } from '@/manager/transfer-file-manager'
import { config } from '@root/common/config/config'
import { RetrievalTaskHelper } from './retrieval-task-helper'
import { CreateSafeBoxDialog } from '@/components/safe-box-dialog'
import { useUserStore } from '@/store/user-store'
import { useTransferRouterStore } from '@/store/transfer-router-store'
import { GlobalEventHelper } from './global-event-helper'
import { IPayloadData } from '@/manager/message-center-manager'
import { MainRenderUIHelper } from '@root/common/main-renderer-ui-helper'
import { Logger } from "@root/common/logger"
import { useDriveRouterStore } from '@/store/drive-router-store'

const logger = new Logger({ tag: 'PanFileOperationHelper' })

export type Operation = 'unzip' | 'analyzeTorrent' | 'project' | 'download' | 'share' | 'trash' | 'restore' | 'move' | 'copy' | 'rename' | 'delete' | 'open' | 'report' | 'appeal' | 'moveToSafe' | 'moveOutSafe' | 'showInMobile'

export type OperationAvailability = {
  [operationKey in Operation]?: boolean
}

export interface IFileOperationOptions {
  actionFrom: string
  isInSafeBoxFolder?: boolean
  isInPrivilegeFolder?: boolean
  handleOpen?: (files: API_FILE.DriveFile) => void
}

export interface IGetFileOperationsOptions {
  isInSafeBoxFolder?: boolean
}

export interface IMoveResultHandlerParams {
  task_id: string
  from: string
  files: API_FILE.DriveFile[]
  space: string
  successMessage: string
}

export interface IDeleteUIFileOptions {
  refreshBase?: boolean
  isMoveFile?: boolean
}

export class FileOperationHelper {
  private static _instance: FileOperationHelper

  static getInstance () {
    if (FileOperationHelper._instance) {
      return FileOperationHelper._instance
    } else {
      FileOperationHelper._instance = new FileOperationHelper()
      return FileOperationHelper._instance
    }
  }

  public getFileOperations (files: extendDriveFile[], options: IGetFileOperationsOptions = {}): OperationAvailability {
    if (files.length === 0) return {}
    // 单选文件工具栏的操作功能，或文件列表的快捷操作按钮
    if (files.length === 1) {
      return this.getSingleFileOperation(files[0], options)
    }
    // 多选文件时，工具栏的操作功能
    return this.getMultipleFilesOperation(files, options)
  }

  // eslint-disable-next-line complexity
  private getSingleFileOperation (file: extendDriveFile, options: IGetFileOperationsOptions = {}): OperationAvailability {
    // 【保险箱】、【流畅播】、【享特权】文件夹不展示快捷操作
    if (['SAFE', 'FAVORITE', 'KOC_RES'].includes(file.folder_type!)) return {}

    // 标识为系统文件夹，仅支持【分享】与【下载】
    const isSystemFolder = SYSTEM_FOLDER_TYPE_LIST.includes(file.folder_type!)
    if (isSystemFolder) {
      return {
        share: true,
        download: true
      }
    }

    // 其他文件
    const isInSafe = options.isInSafeBoxFolder
    const isFolder = file.kind === 'drive#folder'
    const isTrashed = Boolean(file.trashed) === true

    const isCloudAddingTask = file.__kind__ === 'drive#task'
    const isUploadTask = file.phase === 'PHASE_TYPE_PENDING' && !file.__kind__
    const isTask = isCloudAddingTask || isUploadTask

    const isAuditPass = file.kind === 'drive#folder' || isPassAuditFilter(file)
    const isReadonly = Boolean(file.writable) === false
    const isVideo = getMediaType(file.file_extension, file.mime_type, file.file_category) === 'video'

    const unzipCondition = {} // TODO: 在线解压
    const canUnzip = checkCanUnzip(unzipCondition!, file.file_extension, file.mime_type, file.file_category)
    const isUnzipEnable = false // TODO: 在线解压
    const isSafeInDrive = BaseManager.getInstance().isSafeBoxExist()
    const isSafeShow = Boolean(true && isSafeInDrive) // TODO: 保险箱

    // 这里多了点重复判断，但是比起一长串if-else这样的写法比较清晰。
    const operationConditionMap: OperationAvailability = {
      analyzeTorrent: isAuditPass && !isTask && file.file_extension === '.torrent',
      trash: isAuditPass && !isTrashed && !isReadonly,
      report: !isFolder,
      appeal: !isAuditPass,
      move: isAuditPass && !isTrashed && !isReadonly,
      copy: isAuditPass && !isTrashed,
      share: isAuditPass && !isInSafe && !isTask && !isTrashed,
      download: isAuditPass && !isTask && !isTrashed,
      project: file.audit?.status === 'STATUS_OK' && !isTask && isVideo, // 不用isAuditPass是因为需要审核通过才能投屏
      delete: isTrashed || isInSafe || !isAuditPass,
      open: isFolder,
      rename: isAuditPass && !isTrashed && !isReadonly,
      restore: isTrashed && isAuditPass,
      unzip: !isTask && canUnzip && isUnzipEnable,
      moveOutSafe: isSafeShow && isAuditPass && !isTrashed && isInSafe && !isReadonly,
      moveToSafe: isSafeShow && isAuditPass && !isTrashed && !isInSafe && !isReadonly,
      showInMobile: !isTrashed
    }
    return operationConditionMap
  }

  // eslint-disable-next-line complexity
  private getMultipleFilesOperation (files: API_FILE.DriveFile[], options: IGetFileOperationsOptions = {}): OperationAvailability {
    const isTrashed = Boolean(files[0].trashed) === true
    const isReadonly = Boolean(files[0].writable) === false
    const isInSafe = options.isInSafeBoxFolder
    const isSafeInDrive = BaseManager.getInstance().isSafeBoxExist()
    const isSafeShow = Boolean(true && isSafeInDrive) // TODO: 保险箱

    const canMoveSafe = !isReadonly && isSafeShow
    if (!isTrashed) {
      return {
        download: true,
        share: !isInSafe,
        trash: !isTrashed,
        delete: isTrashed,
        move: !isReadonly,
        copy: true,
        moveToSafe: canMoveSafe && !isInSafe,
        moveOutSafe: canMoveSafe && isInSafe
      }
    }
    return {
      restore: true,
      delete: true
    }
  }

  /**
   * 关于保险箱之类的一切行为前置校验
   */
  checkBeforeConsumeSafeBox (): Promise<boolean> {
    return new Promise(resolve => {
      const isExpires = BaseManager.getInstance().getSafeBoxTokenIsExpires()
      // 如果保险箱 token 过期则弹窗重新输入密码
      if (isExpires) {
        const { userStoreState } = useUserStore()
        const { hasInit } = BaseManager.getInstance().getSafeBoxInfo()

        const dialog = CreateSafeBoxDialog({
          showType: hasInit ? 'reset' : 'init',
          phoneNumber: userStoreState.curUser.mobile!,
          onResolve: () => {
            resolve(true)
            dialog.close()
          },
          onClose: () => {
            resolve(false)
          }
        })
      } else {
        resolve(true)
      }
    })
  }

  async consumeFile (fileId: string, space: string = '') {
    if (space === 'SPACE_SAFE') {
      const pass = await this.checkBeforeConsumeSafeBox()
      if (!pass) return
    }

    const res = await ThunderPanClientSDK.getInstance().getFileInfo(fileId, {
      params: {
        space,
        usage: 'CONSUME',
      },
      headers: {
        'space-authorization': space === 'SPACE_SAFE' ? BaseManager.getInstance().getSafeBoxInfo().token : '',
      }
    })

    logger.log('consumeFile getFileInfo response', fileId, space, res)
    if (!res.success || !res.data) {
      window.__VueGlobalProperties__.$message({
        message: res.error.error_description || '获取文件信息失败',
        type: 'error'
      })
      return
    }

    const file = res.data
    const previewType = getPreviewType({
      extString: file.file_extension,
      mimeType: file.mime_type,
      category: file.file_category
    })

    logger.log('consumeFile previewType', previewType)
    if (previewType === 'picture') {
      if (!isPassAuditFilter(file)) {
        // 申述
        window.__VueGlobalProperties__.$message({
          message: file.audit?.message!,
          type: 'error'
        })
        return
      } else {
        // 图片预览
      }
    } else if (previewType === 'media') {
      return this.playMedia(file)
    } else if (previewType === 'unzip') {

    } else {

    }

    window.__VueGlobalProperties__.$message({
      message: '暂不支持该类型，请先下载',
      type: 'info'
    })
  }

  handleFilesOperationWithType (type: string, files: API_FILE.DriveFile[], options: IFileOperationOptions = { actionFrom: 'all' }) {
    logger.log('handleFilesOperationWithType', type, files, options)

    const firstFile = files[0]
    switch (type) {
      case EDriveFileOperation.OPEN: {
        if (options.handleOpen) {
          options.handleOpen(firstFile)
        }
        return
      }
      case EDriveFileOperation.DOWNLOAD: {
        this.batchCreateDownloadTaskWithFiles(files)
        return
      }
      case EDriveFileOperation.DELETE: {
        this.batchDeleteWithFiles(files, options)
        return
      }
      case EDriveFileOperation.PLAY: {
        this.playMedia(firstFile)
        return
      }
      case EDriveFileOperation.DLNA_PLAY: {
        this.playMedia(firstFile)
        return
      }
      case EDriveFileOperation.MOVE: {
        this.batchMoveFiles(files, options)
        return
      }
      case EDriveFileOperation.COPY: {
        this.batchCopyFiles(files, options)
        return
      }
      case EDriveFileOperation.SHARE: {
        this.batchCreateShare(files)
        return
      }
      case EDriveFileOperation.RENAME: {
        this.renameFile(firstFile, options.actionFrom)
        return
      }
      case EDriveFileOperation.MOVE_TO_SAFE: {
        this.moveInFilesToSafeBox(files, options.actionFrom)
        return
      }
      case EDriveFileOperation.MOVE_OUT_SAFE: {
        this.moveOutFilesFromSafeBox(files)
        return
      }
      case EDriveFileOperation.OPEN_FOLDER: {
        PanBusinessHelper.getInstance().openDirectory(firstFile.id!, { fileSpace: firstFile.space! })
        return
      }
      case EDriveFileOperation.DECOMPRESS: {
        break
      }
      case EDriveFileOperation.REPORT: {
        break
      }
      case EDriveFileOperation.APPEAL: {
        break
      }
      case EDriveFileOperation.ANALYZE_TORRENT: {
        break
      }
    }

    window.__VueGlobalProperties__.$message({
      message: '尚未支持该功能，敬请期待',
      type: 'info'
    })
  }

  playMedia (file: API_FILE.DriveFile, openMediaFrom?: string) {
    logger.log('playMedia', file)
    AplayerStack.GetInstance().openMedia({
      name: file.name!,
      gcid: file.hash!,
      playUrl: '',
      playFrom: '',
      zipPlay: 0,
      dlnaPlay: 0,
      mediaType: MediaType.MtPan,
      pan: {
        panFileId: file.id!,
        panRatioId: '',
        panSpace: file.space,
      }
    })
  }

  async moveInFilesToSafeBox (files: API_FILE.DriveFile[], from: string = 'all') {
    const fromIds = files.map(file => file.id!)
    const moveRes = await ThunderPanClientSDK.getInstance().batchMoveFiles({
      params: {
        ids: fromIds,
        to: {
          parent_id: BaseManager.getInstance().getSafeBoxFileInfo().id,
          space: 'SPACE_SAFE',
        },
        space: '',
      },
    })

    if (moveRes.success) {
      this._moveResultHandler({
        task_id: moveRes.data?.task_id!,
        from: from,
        files: files,
        space: 'SPACE_SAFE',
        successMessage: '已成功移入文件'
      })
    } else {
      window.__VueGlobalProperties__.$message({
        message: moveRes.error?.error_description || '移入文件失败，请稍后再试',
        type: 'error'
      })
    }
  }

  async moveOutFilesFromSafeBox (files: API_FILE.DriveFile[]) {
    const fromIds = files.map(file => file.id!)
    const moveRes = await ThunderPanClientSDK.getInstance().batchMoveFiles({
      params: {
        ids: fromIds,
        to: {
          parent_id: '',
          space: '',
        },
        space: 'SPACE_SAFE',
      },
      headers: {
        'space-authorization': BaseManager.getInstance().getSafeBoxInfo().token,
      }
    })

    if (moveRes.success) {
      this._moveResultHandler({
        task_id: moveRes.data?.task_id!,
        from: 'all',
        files: files,
        space: 'SPACE_SAFE',
        successMessage: '已成功移出文件'
      })
    } else {
      window.__VueGlobalProperties__.$message({
        message: moveRes.error?.error_description || '移出文件失败，请稍后再试',
        type: 'error'
      })
    }
  }

  async batchMoveFiles (files: API_FILE.DriveFile[], options: IFileOperationOptions) {
    if (options.isInSafeBoxFolder) {
      const pass = await this.checkBeforeConsumeSafeBox()
      if (!pass) return
    }

    const currentSpace = options.isInSafeBoxFolder ? 'SPACE_SAFE' : '';
    const res = await PanBusinessHelper.getInstance().openPathSelector({
      rootFile: this._getPathSelectorRootFile(options.isInSafeBoxFolder!)
    })

    if (res) {
      const fromIds = files.map(file => file.id!)
      const moveRes = await ThunderPanClientSDK.getInstance().batchMoveFiles({
        params: {
          ids: fromIds,
          to: {
            parent_id: res.selectedItem.id,
            space: currentSpace
          },
          space: currentSpace,
        },
        headers: {
          'space-authorization': options.isInSafeBoxFolder ? BaseManager.getInstance().getSafeBoxInfo().token : '',
        }
      })

      if (moveRes.success) {
        this._moveResultHandler({
          task_id: moveRes.data?.task_id!,
          from: options.actionFrom,
          files: files,
          space: currentSpace,
          successMessage: '移动成功'
        })
      } else {
        window.__VueGlobalProperties__.$message({
          message: moveRes.error?.error_description || '移动文件失败，请稍后再试',
          type: 'error'
        })
      }
    }
  }

  async batchCopyFiles (files: API_FILE.DriveFile[], options: IFileOperationOptions) {
    if (options.isInSafeBoxFolder) {
      const pass = await this.checkBeforeConsumeSafeBox()
      if (!pass) return
    }

    const currentSpace = options.isInSafeBoxFolder ? 'SPACE_SAFE' : '';
    const res = await PanBusinessHelper.getInstance().openPathSelector({
      rootFile: this._getPathSelectorRootFile(options.isInSafeBoxFolder!)
    })

    if (res) {
      const fromIds = files.map(file => file.id!)
      const copyRes = await ThunderPanClientSDK.getInstance().batchCopyFiles({
        params: {
          ids: fromIds,
          to: {
            parent_id: res.selectedItem.id,
            space: currentSpace
          },
          space: currentSpace,
        },
        headers: {
          'space-authorization': options.isInSafeBoxFolder ? BaseManager.getInstance().getSafeBoxInfo().token : '',
        }
      })

      if (copyRes.success) {
        const resultHandler = (messageData: IPayloadData) => {
          if (copyRes.data?.task_id === messageData.id) {
            if (messageData.phase === Phase.COMPLETE) {
              window.__VueGlobalProperties__.$message({
                message: '复制成功',
                type: 'success'
              })
              GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.DRIVE_LIST_CLEAN_PICKED)
              GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.TRANSFER_LIST_CLEAN_PICKED)
            } else {
              this._onCopyFileError(messageData, currentSpace)
            }
            GlobalEventHelper.getInstance().off(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_COPY, resultHandler)
          }
        }
        GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_COPY, resultHandler)
      } else {
        window.__VueGlobalProperties__.$message({
          message: copyRes.error?.error_description || '复制文件失败，请稍后再试',
          type: 'error'
        })
      }
    }
  }

  async batchDeleteWithFiles (files: API_FILE.DriveFile[], options: IFileOperationOptions = { actionFrom: 'all' }) {
    const duration = PanBusinessHelper.getInstance().getCurrentUserTrashPrivilegeDuration()
    const isUseDeleteFiles = options.isInSafeBoxFolder || options.isInPrivilegeFolder
    let bodyText = `回收站将自动保留 ${duration} 天，同时回收站文件将占用您的云盘空间`
    let leftButtonText = '从云盘彻底删除'

    if (options.isInSafeBoxFolder) {
      bodyText = '保险箱文件将直接删除，不会存放回收站'
      leftButtonText = ''
    } else if (options.isInPrivilegeFolder) {
      bodyText = '享特权文件删除后不可在回收站恢复，确认删除吗？'
      leftButtonText = ''
    }

    const dialog = CreateConfirmDialog({
      headerTitle: '确定删除所选文件/文件夹?',
      bodyText: bodyText,
      leftButtonText: leftButtonText,
      confirmButtonText: '确认删除',
      leftButtonVariant: 'ghost',
      confirmButtonVariant: 'warning',
      onConfirm: async () => {
        let res
        if (isUseDeleteFiles) {
          // 保险箱文件 headers
          let headers = {}
          if (options.isInSafeBoxFolder) {
            headers = {
              'space-authorization': BaseManager.getInstance().getSafeBoxInfo().token,
            }
          }

          res = await ThunderPanClientSDK.getInstance().batchDeleteFiles({
            params: {
              ids: files.map(file => file.id!),
              space: options.isInSafeBoxFolder ? 'SPACE_SAFE' : ''
            },
            headers,
          })
        } else {
          res = await ThunderPanClientSDK.getInstance().batchTrashFiles({
            params: {
              ids: files.map(file => file.id!)
            }
          })
        }

        if (res.success) {
          this._deleteUIFile(options.actionFrom, files, { refreshBase: true })
          window.__VueGlobalProperties__.$message({
            message: isUseDeleteFiles ? '删除成功' : '删除至回收站',
            type: 'success',
            rightTextButton: isUseDeleteFiles ? '' : '查看',
            onRightTextButtonClick: () => {
              // 跳转回收站
              MainRenderUIHelper.getInstance().navigateToPath('/trash', {
                tab: 'cloud'
              })
            }
          })
        } else {
          window.__VueGlobalProperties__.$message({
            message: res.error.error_description || '删除失败，请稍后再试',
            type: 'success'
          })
        }
        dialog.close()
      },
      onLeftButtonClick: async () => {
        const res = await ThunderPanClientSDK.getInstance().batchDeleteFiles({
          params: {
            ids: files.map(file => file.id!)
          }
        })

        if (res.success) {
          this._deleteUIFile(options.actionFrom, files, { refreshBase: true })
          window.__VueGlobalProperties__.$message({
            message: '彻底删除成功',
            type: 'success'
          })
        } else {
          window.__VueGlobalProperties__.$message({
            message: res.error.error_description || '彻底删除失败，请稍后再试',
            type: 'success'
          })
        }
        dialog.close()
      }
    })
  }

  async batchCreateDownloadTaskWithFiles (files: API_FILE.DriveFile[]) {
    logger.log('batchCreateDownloadTaskWithFiles', files)
    // 过滤出正常可下载文件
    const filteredFiles = files.filter(isPassAuditFilter).filter(isPassComplete).filter(isPassSpace)
    if (!filteredFiles.length) {
      window.__VueGlobalProperties__.$message({
        message: '所选没有可下载文件',
        type: 'error'
      })
      return
    }

    const isUseDefault = await config.getValue('ThunderPanPlugin', 'useDefault', false)
    logger.log('batchCreateDownloadTaskWithFiles isUseDefault', isUseDefault)
    // 直接下载，不弹窗
    if (isUseDefault) {
      const res = await RetrievalTaskHelper.getInstance().batchCreateDownloadTaskWithFiles(files)

      logger.log('batchCreateDownloadTaskWithFiles isUseDefault response', res)
      if (res.repeatTaskAction === 'ignore') {
        // 重复任务忽略不处理
      } else if (res.taskIds.length) {
        let message = '已创建下载任务'
        // 部分成功
        if (res.taskIds.length && (res.overlimitList.length || res.errorList.length)) {
          const failCount = res.overlimitList.length + res.errorList.length
          const firstFailFile = res.overlimitList[0] || res.errorList[0]
          const successCount = res.taskIds.length - failCount
          message = `已创建${successCount}个下载任务，${firstFailFile.name}等${failCount}个失败`
        }
        window.__VueGlobalProperties__.$message({
          message: message,
          type: 'success'
        })
      } else {
        const isAllOverlimit = !!res.overlimitList.length && res.overlimitList.length === res.taskIds.length
        const isAllError = !!res.errorList.length && res.errorList.length === res.taskIds.length

        let message = '创建下载任务失败'
        if (isAllOverlimit) {
          message = '当前文件夹文件数量超过限制，创建下载任务失败'
        } else if (isAllError) {
          message = '获取当前文件夹文件信息失败'
        }

        window.__VueGlobalProperties__.$message({
          message: message,
          type: 'error'
        })
      }
    } else {
      const res = await PanBusinessHelper.getInstance().openFetchBackDialog(files)

      logger.log('batchCreateDownloadTaskWithFiles FetchBackDialog response', res)
      // 失败的提示在 fetch-back 组件内提示
      if (res.success) {
        let message = '已创建下载任务'
        // 部分成功
        if (res.data.taskIds.length && (res.data.overlimitList.length || res.data.errorList.length)) {
          const failCount = res.data.overlimitList.length + res.data.errorList.length
          const firstFailFile = res.data.overlimitList[0] || res.data.errorList[0]
          const successCount = res.data.taskIds.length - failCount
          message = `已创建${successCount}个下载任务，${firstFailFile.name}等${failCount}个失败`
        }

        window.__VueGlobalProperties__.$message({
          message: message,
          type: 'success'
        })
      }
    }
    GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.DRIVE_LIST_CLEAN_PICKED)
    GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.TRANSFER_LIST_CLEAN_PICKED)
  }

  async batchCreateShare (files: API_FILE.DriveFile[]) {
    const ids = files.map(f => f.id!)
    const res = await ThunderPanClientSDK.getInstance().createShare({
      params: {
        file_ids: ids,
        share_to: 'copy',
        title: '云盘资源分享',
        restore_limit: '-1',
        expiration_days: '-1',
        params: {
          subscribe_push: String(false)
        }
      }
    })

    if (res.success && res.data) {
      CreateShareDialog({
        files: files,
        preShareResult: res.data
      })
      GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.SHARE_LIST_REFRESH)
      GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.DRIVE_LIST_CLEAN_PICKED)
      GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.TRANSFER_LIST_CLEAN_PICKED)
    } else {
      window.__VueGlobalProperties__.$message({
        message: res.error.error_description || '分享失败',
        type: 'error'
      })
    }
  }

  /**
   * 复制分享链接
   * @param item 分享数据
   * @param tailText 尾巴
   */
  copyShareData (item: API_SHARE.DriveShareMgrData, tailText: string) {
    try {
      const { share_url, pass_code, title, file_num } = item
      const titleStr = Number(file_num ?? 0) > 1 ? `${title}等` : title
      clipboard.writeText(`分享文件：${titleStr}\n链接：${share_url}\n${pass_code === '' ? '' : `提取码：${pass_code}\n`}${tailText}`)

      window.__VueGlobalProperties__.$message({
        message: '复制链接成功',
        type: 'success'
      })
    } catch (error: any) {
      window.__VueGlobalProperties__.$message({
        message: '复制链接失败',
        type: 'error'
      })
    }
  }

  /**
   * 批量复制分享链接
   * @param item 分享数据
   * @param tailText 尾巴
   */
  batchCopyShareData (items: API_SHARE.DriveShareMgrData[], tailText: string) {
    try {
      let nSucc: number = 0;
      let nFailed: number = 0;
      let text: string = '';
      for (let i = 0; i < items.length; i++) {
        if (items[i]?.title) {
          if (nSucc >= 1) {
            text += `\n\n`;
          }
          const { share_url, pass_code, title, file_num } = items[i]
          const titleStr = Number(file_num ?? 0) > 1 ? `${title}等` : title
          text += `分享文件：${titleStr}\n链接：${share_url}\n${pass_code === '' ? '' : `提取码：${pass_code}\n`}${tailText}`
          nSucc++;
        } else {
          nFailed++;
        }
        if (nSucc >= 1000) {
          break;
        }
      }
      if (text.length > 0) {
        clipboard.writeText(text)
      }
      let message: string = `复制成功${nSucc}项`;
      if (nFailed > 0) {
        // 有失效连接
        message += `，${nFailed}项失效链接未被复制`;
      }
      if (nSucc >= 1000 && nSucc + nFailed !== items.length) {
        message += `，最多支持复制前1000项`
      }

      window.__VueGlobalProperties__.$message({
        message,
        type: 'success'
      })
    } catch (error: any) {
      window.__VueGlobalProperties__.$message({
        message: '复制链接失败',
        type: 'error'
      })
    }
  }

  /**
   * 批量取消分享
   * @param ids 分享 id 数组
   */
  async batchCancelShare (ids: string[]) {
    const res = await ThunderPanClientSDK.getInstance().batchDeleteShare({ params: { ids: ids } })

    if (res.success) {
      window.__VueGlobalProperties__.$message({
        message: '已取消分享',
        type: 'success'
      })
      return ids
    } else {
      window.__VueGlobalProperties__.$message({
        message: res.error.error_description || '取消分享失败',
        type: 'error'
      })
      return []
    }
  }

  async renameFile (file: API_FILE.DriveFile, from: string = 'all') {
    const promptDialog = usePromptDialog()
    const result = await promptDialog.prompt({
      title: '重命名',
      placeholder: '请输入文件名称',
      defaultValue: file.name,
      modal: true,
      draggable: false,
      disableHeaderDraggable: false,
      validateOnInput: true, // 启用实时验证
      onChange: (value, event) => {
      },
      validator: (newName) => {
        if (!newName.length) {
          return { valid: false, message: `文件名称不能为空，请输入名称` }
        }
        if (newName === file.name) {
          return { valid: false, message: `文件名称修改前后一致` }
        }
        if (FileNameForbidReg.test(newName)) {
          return { valid: false, message: `名称不能包含${FileNameForbidRegString}等特殊字符` }
        }

        return { valid: true, message: '' }
      },
      onConfirm: async (newName) => {
        const newFileInfo: API_FILE.DriveFile = {
          id: file.id,
          name: newName
        }
        const res = await ThunderPanClientSDK.getInstance().batchUpdate({
          params: {
            files: [ newFileInfo ],
            space: file.space,
          },
          headers: {
            'space-authorization': file.space === 'SPACE_SAFE' ? BaseManager.getInstance().getSafeBoxInfo().token : '',
          },
        })

        if (res.success && res.data && res.data.files) {
          this._updateUIFile(from, res.data.files[0])
          window.__VueGlobalProperties__.$message({
            message: '重命名成功',
            type: 'success'
          })
          return true
        } else {
          window.__VueGlobalProperties__.$message({
            message: res.error.error_description || '重命名失败',
            type: 'error'
          })
        }
        return false
      }
    } as any)
  }

  async createFolder (parentId: string, space: string = '', from: string = 'all') {
    const nowDate = useDateFormat(useNow(), 'YYYYMMDD-HHmmss').value
    const promptDialog = usePromptDialog()
    const result = await promptDialog.prompt({
      title: '新建文件夹',
      placeholder: '请输入文件夹名称',
      defaultValue: `新建文件夹-${nowDate}`,
      modal: true,
      draggable: false,
      disableHeaderDraggable: false,
      validateOnInput: true, // 启用实时验证
      onChange: (value, event) => {
      },
      validator: (newName) => {
        if (!newName.length) {
          return { valid: false, message: `文件夹名称不能为空，请输入名称` }
        }
        if (FileNameForbidReg.test(newName)) {
          return { valid: false, message: `名称不能包含${FileNameForbidRegString}等特殊字符` }
        }

        return { valid: true, message: '' }
      },
      onConfirm: async (newName) => {
        let headers = {}
        if (space === 'SPACE_SAFE') {
          headers['space-authorization'] = BaseManager.getInstance().getSafeBoxInfo().token;
        }
        const res = await ThunderPanClientSDK.getInstance().createFolder(parentId, newName, {
          params: {
            space: space
          },
          headers,
        })

        if (res.success && res.data && res.data.file) {
          this._appendUIFile(from, res.data.file)
          window.__VueGlobalProperties__.$message({
            message: '创建文件夹成功',
            type: 'success'
          })
          return true
        } else {
          window.__VueGlobalProperties__.$message({
            message: res.error.error_description || '创建文件夹失败',
            type: 'error'
          })
        }
        return false
      }
    } as any)
  }

  private _moveResultHandler (params: IMoveResultHandlerParams) {
    const resultHandler = (messageData: IPayloadData) => {
      if (params.task_id === messageData.id) {
        // 移动任务成功
        if (messageData.phase === Phase.COMPLETE) {
          this._deleteUIFile(params.from, params.files, { isMoveFile: true })
          window.__VueGlobalProperties__.$message({
            message: params.successMessage,
            type: 'success',
          })
        } else {
          this._onMoveFileError(messageData, params.space)
        }
        // 移除监听
        GlobalEventHelper.getInstance().off(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_MOVE, resultHandler)
      }
    }
    // 监听 MQTT 推送
    GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_MOVE, resultHandler)
  }

  private _onMoveFileError (msgData: IPayloadData, currentSpace: string) {
    let message = '移动失败';
    const msgParams = msgData.params;
    const { isYearlySuper } = useUserStore()

    if (msgParams?.error_detail === 'all_files_invalid') {
      message = `选中文件都已失效，无法移动`;
    } else if (msgParams?.error_detail === 'operating_file_count_exceeded') {
      message = `所选文件数过多，无法批量移动`;
    } else if (msgParams?.error_detail === 'file_space_not_enough') {
      const isSafeBox = currentSpace === 'SPACE_SAFE';
      if (isYearlySuper) {
        message = isSafeBox ? '超级保险箱空间不足，请清理后重试' : '云盘空间不足，请清理后重试';
      } else {
        // ThunderPanManager.openPayPageForSpace({
        //   enough: false,
        //   isSafeBox: isSafeBox,
        //   aidFrom: isSafeBox ? 'safebox' : 'move',
        // });
        // return;
      }
    }

    window.__VueGlobalProperties__.$message({
      message: message,
      type: 'error'
    })
  }

  private _onCopyFileError (msgData: IPayloadData, currentSpace: string) {
    let message = '复制失败';
    const msgParams = msgData.params;
    const { isYearlySuper } = useUserStore()

    if (msgParams?.error_detail === 'file_space_not_enough') {
      const isSafeBox = currentSpace === 'SPACE_SAFE';
      if (isYearlySuper) {
        message = isSafeBox ? '超级保险箱空间不足，请清理后重试' : '云盘空间不足，请清理后重试';
      } else {
        // ThunderPanManager.openPayPageForSpace({
        //   enough: false,
        //   isSafeBox: isSafeBox,
        //   aidFrom: isSafeBox ? 'safebox' : 'duplicate',
        // });
        // return;
      }
    }

    window.__VueGlobalProperties__.$message({
      message: message,
      type: 'error'
    })
  }

  private _getPathSelectorRootFile (isInSafeBoxFolder: boolean) {
    const currentSpace = isInSafeBoxFolder ? 'SPACE_SAFE' : '';
    const baseRootFile = {
      id: isInSafeBoxFolder ? BaseManager.getInstance().getSafeBoxFileInfo().id : '',
      name: isInSafeBoxFolder ? BaseManager.getInstance().getSafeBoxFileInfo().name : '我的云盘',
      space: currentSpace,
    }
    return baseRootFile
  }

  private _appendUIFile (from: string, newFile: API_FILE.DriveFile) {
    const { currentParentFile: transferParentFile } = useTransferRouterStore()
    DriveFileManager.getInstance().cleanPicked()
    TransferFileManager.getInstance().cleanPicked()
    // 添加到云盘列表
    DriveFileManager.getInstance().appendFiles([ newFile ])
    // 默认选中该文件夹
    DriveFileManager.getInstance().togglePickedId(newFile.id!)
    // 驱动云盘文件列表滚动条
    GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.DRIVE_LIST_PICK_OR_HIGHLIGHT_FILE, newFile, false, true)
    // 如果【转存】视图处于（非根目录）同一个 parentId 下，则也添加进去
    if (transferParentFile.value.id !== '' && newFile.parent_id === transferParentFile.value.id) {
      TransferFileManager.getInstance().appendFiles([ newFile ])
    }
    // 更新基础信息
    BaseManager.getInstance().getBase()
  }

  private _updateUIFile (from: string, newFile: API_FILE.DriveFile) {
    DriveFileManager.getInstance().cleanPicked()
    TransferFileManager.getInstance().cleanPicked()
    // 可能处于同一个文件夹内，两个视图都可以尝试更新
    DriveFileManager.getInstance().updateFile(newFile)
    TransferFileManager.getInstance().updateFile(newFile)
  }

  private _deleteUIFile (from: string, files: API_FILE.DriveFile[], options: IDeleteUIFileOptions = {}) {
    const { currentParentFile: transferParentFile } = useTransferRouterStore()
    const ids = files.map(file => file.id!)
    DriveFileManager.getInstance().cleanPicked()
    TransferFileManager.getInstance().cleanPicked()
    // 可能处于同一个文件夹内，两个视图都可以尝试删除
    DriveFileManager.getInstance().batchDeleteFile(ids)
    // 如果是转存文件列表下移动文件则不执行删除
    if (!options.isMoveFile || transferParentFile.value.id !== '') {
      TransferFileManager.getInstance().batchDeleteFile(ids)
    }
    // 更新基础信息
    if (options.refreshBase) {
      BaseManager.getInstance().getBase()
    }
  }
}
