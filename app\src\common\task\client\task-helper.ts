import path from 'node:path'
import { FileExtNS } from "@root/common/file-ext";
import { FileOperationHelper } from "@root/common//helper/file-operation-helper";
import { ThunderNewTaskHelperNS } from "./new-task-helper";
import { ParseUrlFileNameNS } from "./parse-helper";
import * as DownloadKernel from '../base';
import { TaskManager } from "@root/common/task/client/task-manager";
import { AplayerStack } from "@root/common/player/client/aplayer-stack";
import { MediaType } from "@root/common/player/base";
import { BtTaskHelper } from "./bt-helper";
import { URLHelperNS } from "./url-helper";
import { Logger } from '@root/common/logger';
import { TOpenMediaFrom } from '@root/common/player/base';

const logger = new Logger({ tag: 'TaskHelper' })

export interface ICreateTaskCommonExtParams {
  savePath: string
  clickFrom: string
  openMediaFrom: TOpenMediaFrom
}

export interface ITaskPlayParams {
  id: number
  name: string
  gcid: string
  playFrom: string
  fileIndex: number
}

export interface IGetFileInfoFromMagnetResponse {
  btFileInfo: DownloadKernel.BtTaskInfo | null
  torrentSavePath: string
}

export enum ETaskResponse {
  SUCCESS = 0,
  NO_PLAY_DATA,
  PARSE_ERROR,
  PARSE_MAGNET_ERROR,
}

export class TaskHelper {

  private static _instance: TaskHelper;

  static getInstance() {
    if (TaskHelper._instance) return TaskHelper._instance;
    TaskHelper._instance = new TaskHelper();
    return TaskHelper._instance;
  }

  async createTask(text: string, ext: ICreateTaskCommonExtParams) {
    try {
      const categoryList = await this.getTaskBaseListFromText(text, ext.clickFrom)

      logger.log('解析结果', categoryList)
      // P2p 任务
      if (categoryList.p2pTaskList.length) {
        return this.createP2pTask(categoryList.p2pTaskList, ext)
      }
      // Bt 任务
      if (categoryList.magnetTaskList.length) {
        return this.createBtTask(categoryList.magnetTaskList, ext)
      }

      return ETaskResponse.NO_PLAY_DATA
    } catch (err) {
      logger.warn('解析出错', err)
      return ETaskResponse.PARSE_ERROR
    }
  }

  async getTaskBaseListFromText(text: string, clickFrom?: string) {
    let p2pTaskList: ParseUrlFileNameNS.IP2pPreCreateTaskItem[] = [];
    let magnetTaskList: ParseUrlFileNameNS.IMagnetPreCreateTaskItem[] = [];
    const res = await ParseUrlFileNameNS.getContentParseResult(text, clickFrom);

    p2pTaskList = res.p2pTaskList;
    magnetTaskList = res.magnetTaskList;

    return {
      p2pTaskList,
      magnetTaskList,
    }
  }

  private play(task: ITaskPlayParams, ext: ICreateTaskCommonExtParams) {
    const mediaInfo = {
      name: task.name,
      gcid: task.gcid,
      playUrl: '',
      playFrom: task.playFrom,
      zipPlay: 0,
      dlnaPlay: 0,
      mediaType: MediaType.MtDownload,
      task: {
        taskId: task.id,
        fileIndex: task.fileIndex,
      }
    }
    logger.log('播放 mediaInfo', mediaInfo)
    return AplayerStack.GetInstance().openMedia(mediaInfo, ext.openMediaFrom)
  }

  private async createP2pTask(p2pTaskList: ParseUrlFileNameNS.IP2pPreCreateTaskItem[], ext: ICreateTaskCommonExtParams) {
    let ret = ETaskResponse.NO_PLAY_DATA

    do {
      try {
        const taskInfoList = await this.getP2pTaskInfoList(p2pTaskList, ext.clickFrom)

        logger.log('p2p 链接解析结果', taskInfoList)
        if (taskInfoList.length) {
          const task = taskInfoList[0];
          const taskExt = '.' + FileOperationHelper.getSuffix(task.data?.fileName!);
          const supportPlay = FileExtNS.isAudioExt(taskExt.toLowerCase()) || FileExtNS.isVideoExt(taskExt.toLowerCase());

          if (supportPlay) {
            // 开始下载与播放
            const taskSet = await this.getP2pTaskSet([task], ext);
            logger.log('p2p 任务准备数据', taskSet)
            // 校验是否重复任务
            const repeatResult = await TaskManager.GetInstance().findRepeatTask(taskSet)

            logger.log('p2p 重复任务校验', repeatResult)
            let playInfo: ITaskPlayParams | undefined
            if (repeatResult[0]) {
              playInfo = {
                id: repeatResult[0].taskId,
                name: task.data?.fileName!,
                gcid: task.data?.fileHash!,
                playFrom: ext.clickFrom,
                fileIndex: repeatResult[0].index,
              }
            } else {
              const taskBase = await TaskManager.GetInstance().createTask(taskSet[0])
              logger.log('p2p 下载任务', taskBase)

              if (taskBase && taskBase.taskId) {
                const isTaskSupportPlay = await taskBase.isSupportPlay()
                logger.log('p2p 任务是否支持播放', isTaskSupportPlay)
                // 不支持播放则直接删除当前任务
                if (!isTaskSupportPlay) {
                  await taskBase.deleteTask(true)
                  break;
                }

                await taskBase.startTask();
                playInfo = {
                  id: taskBase.taskId,
                  name: task.data?.fileName!,
                  gcid: task.data?.fileHash!,
                  playFrom: ext.clickFrom,
                  fileIndex: -1,
                }
              }
            }

            logger.log('p2p 播放数据', playInfo)
            if (playInfo) {
              this.play(playInfo, ext)
              ret = ETaskResponse.SUCCESS
            }
          }
        }
      } catch (err) {
        logger.warn('p2p 解析出错', err)
        ret = ETaskResponse.PARSE_ERROR
      }
    } while (0)

    return ret
  }

  private async createBtTask(magnetTaskList: ParseUrlFileNameNS.IMagnetPreCreateTaskItem[], ext: ICreateTaskCommonExtParams) {
    let ret = ETaskResponse.NO_PLAY_DATA

    do {
      try {
        const magnetRes = await this.getFileInfoFromMagnet(magnetTaskList[0], ext)

        logger.log('bt 链接解析结果', magnetRes)
        if (magnetRes?.btFileInfo) {
          const fileInfo = magnetRes.btFileInfo
          const originUrl = (magnetTaskList[0].url.length > 0 && magnetTaskList[0].url.indexOf('magnet:') === 0)
            ? magnetTaskList[0].url
            : await URLHelperNS.fixMagnetUrl(fileInfo.infoId)

          const taskInfo: DownloadKernel.NewTaskInfo = {
            background: false,
            categoryId: DownloadKernel.DefaultCategoryId,
            taskType: DownloadKernel.TaskType.Bt,
            taskBaseInfo: {
              savePath: ext.savePath,
              taskName: fileInfo.title,
              origin: ThunderNewTaskHelperNS.getNewTaskTrackFrom(ext.clickFrom, true),
            }
          };
          const btInfo: DownloadKernel.NewBtTaskInfo = {
            seedFile: magnetRes.torrentSavePath,
            displayName: fileInfo.title,
            fileRealIndexLists: fileInfo.fileLists.map(file => file.realIndex), // 当前默认全选
            fileLists: Array.from(fileInfo.fileLists),
            infoId: fileInfo.infoId,
            tracker: fileInfo.trackerUrls.length ? fileInfo.trackerUrls[0] : '',
            origin: originUrl,
          };

          const taskSet: DownloadKernel.NewTaskSet = {
            taskInfo,
            btInfo,
          }
          logger.log('bt 任务准备数据', taskSet)
          // 校验是否重复任务
          const repeatResult = await TaskManager.GetInstance().findRepeatTask([taskSet])

          logger.log('bt 重复任务校验', repeatResult)
          let playInfo: ITaskPlayParams | undefined
          if (repeatResult.length) {
            playInfo = {
              id: repeatResult[0].taskId,
              name: taskInfo.taskBaseInfo.taskName,
              gcid: '',
              playFrom: ext.clickFrom,
              fileIndex: repeatResult[0].index,
            }
          } else {
            const taskBase = await TaskManager.GetInstance().createTask(taskSet);
            logger.log('bt 下载任务', taskBase)

            if (taskBase && taskBase.taskId) {
              const isTaskSupportPlay = await taskBase.isSupportPlay()
              logger.log('bt 任务是否支持播放', isTaskSupportPlay)
              // 不支持播放则直接删除当前任务
              if (!isTaskSupportPlay) {
                await taskBase.deleteTask(true)
                break;
              }

              await taskBase.startTask();
              playInfo = {
                id: taskBase.taskId,
                name: taskInfo.taskBaseInfo.taskName,
                gcid: '',
                playFrom: ext.clickFrom,
                fileIndex: -1,
              }
            }
          }

          logger.log('bt 播放数据', playInfo)
          if (playInfo) {
            this.play(playInfo, ext)
            ret = ETaskResponse.SUCCESS
          }
        } else {
          ret = ETaskResponse.PARSE_MAGNET_ERROR
        }
      } catch (err) {
        logger.warn('bt 解析出错', err)
        ret = ETaskResponse.PARSE_ERROR
      }
    } while (0)

    return ret
  }

  private async getFileInfoFromMagnet(magnetTask: ParseUrlFileNameNS.IMagnetPreCreateTaskItem, ext: ICreateTaskCommonExtParams): Promise<IGetFileInfoFromMagnetResponse | null> {
    const magnetInfo = await ParseUrlFileNameNS.parseMagnetUrl(magnetTask.url);
    const taskInfo: DownloadKernel.NewTaskInfo = {
      background: true,   // 后台下载，不进入下载列表中
      categoryId: DownloadKernel.DefaultCategoryId,
      taskType: DownloadKernel.TaskType.Magnet,
      taskBaseInfo: {
        savePath: ext.savePath,
        taskName: magnetInfo.infoHash + '.torrent',
        origin: ThunderNewTaskHelperNS.getNewTaskTrackFrom(ext.clickFrom, true),
      }
    };
    const magnetTaskInfo: DownloadKernel.NewMagnetTaskInfo = {
      url: magnetInfo.url || magnetTask.url,
      torrentFilePath: ext.savePath,
    };
    const taskSet = { taskInfo, magnetInfo: magnetTaskInfo }
    logger.log('bt 种子任务准备数据', taskSet)

    const task = await TaskManager.GetInstance().createTask(taskSet);
    logger.log('bt 种子下载任务', task)

    if (task && task.taskId) {
      return new Promise((resolve) => {
        task.startTask();
        // 一分钟超时
        const downloadTimeoutTimer = setTimeout(() => {
          logger.log('bt 种子下载超时')
          resolve(null)
        }, 60 * 1000)

        const torrentSavePath = path.join(taskInfo.taskBaseInfo.savePath, taskInfo.taskBaseInfo.taskName);
        const statusChangeHandler = async (task, oldStatus, newStatus) => {
          if (task.taskId === task.taskId) {
            logger.log('bt 种子下载状态变更', oldStatus, newStatus)
            if (newStatus === DownloadKernel.TaskStatus.Succeeded) {
              // 种子下载成功
              const btFileInfo = await BtTaskHelper.getBtFileInfoAw(torrentSavePath);

              resolve({
                btFileInfo,
                torrentSavePath,
              })
              clearTimeout(downloadTimeoutTimer)
              task.deleteTask(false);
              TaskManager.GetInstance().detachTaskStatusChangeEvent(statusChangeHandler)
            } else if (newStatus == DownloadKernel.TaskStatus.Failed) {
              // 种子下载失败
              resolve(null);
              clearTimeout(downloadTimeoutTimer)
              TaskManager.GetInstance().detachTaskStatusChangeEvent(statusChangeHandler)
            }
          }
        }

        TaskManager.GetInstance().attachTaskStatusChangeEvent(statusChangeHandler)
      })
    } else {
      return null
    }
  }

  private async getP2pTaskInfoList(p2pTaskList: ParseUrlFileNameNS.IP2pPreCreateTaskItem[], clickFrom?: string) {
    const infoList: ThunderNewTaskHelperNS.ISingleTaskUI[] = [];

    for (let i = 0; i < p2pTaskList.length; i++) {
      const task = p2pTaskList[i];
      const item: ThunderNewTaskHelperNS.ISingleTaskUI = await ThunderNewTaskHelperNS.constructTaskByUrl(
        task.url,
        clickFrom || '',
        task.type,
        task.birdKey
      );
      infoList.push(item);
    }

    return infoList;
  }

  private async getP2pTaskSet(p2pSelectedList: ThunderNewTaskHelperNS.ISingleTaskUI[], ext: ICreateTaskCommonExtParams) {
    let preNewTaskSet: DownloadKernel.NewTaskSet[] = [];

    for (let i = 0; i < p2pSelectedList.length; i++) {
      const taskFile = p2pSelectedList[i];
      const taskInfo: DownloadKernel.NewTaskInfo = {
        background: false,
        categoryId: DownloadKernel.DefaultCategoryId,
        taskType: taskFile.taskType!,
        taskBaseInfo: {
          savePath: ext.savePath,
          taskName: taskFile.data!.fileName!,
          fileSize: taskFile.data?.fileSize || 0,
          gcid: taskFile.data?.gcid || '',
          origin: ThunderNewTaskHelperNS.getNewTaskTrackFrom(ext.clickFrom, false),
        }
      };
      // p2p 任务
      let p2spInfo: DownloadKernel.NewP2spTaskInfo | undefined;
      // 电驴 ed2k 相关任务
      let emuleInfo: DownloadKernel.NewEmuleTaskInfo | undefined;

      if (taskFile.taskType === DownloadKernel.TaskType.P2sp) {
        p2spInfo = {
          url: taskFile.data!.url,
        };
      } else if (taskFile.taskType === DownloadKernel.TaskType.Emule) {
        emuleInfo = {
          url: taskFile.data!.url,
        };
      }

      preNewTaskSet.push({
        taskInfo,
        p2spInfo,
        emuleInfo,
      })
    }

    return preNewTaskSet
  }
}
