import * as path from 'path'
import requireNodeFile from '@root/common/require-node-file';
import {GetXxxNodePath, GetProfilesPath} from '@root/common/xxx-node-path';
import { TaskManager } from '@root/common/task/impl/task-manager';
import * as TaskBaseType from '@root/common/task/base';
import { BtTask } from 'task/impl/bt-task';
import { server } from '@xunlei/node-net-ipc/dist/ipc-server';

export class DownloadFileRecord {
  private static instance: DownloadFileRecord;
  private nativeManager: any;


  constructor() {
    
  }

  public init() {
    const thunderHelper: any = requireNodeFile(path.join(GetXxxNodePath(), 'thunder_helper.node'));
    this.nativeManager = new thunderHelper.NativeDownloadFileRecord(GetProfilesPath(), 'FileRecord.dat');
    server.registerFunctions({
        DownloadFileRecordQuery: async (c: any, context: any, url: string, index: number) => {
            let p = await this.query(url, index);
            return p;
        },
    });
    TaskManager.GetInstance().attachTaskStatusChangeEvent(async (taskId: number, eOld: TaskBaseType.TaskStatus, eNew: TaskBaseType.TaskStatus) => {
        if (eNew !== TaskBaseType.TaskStatus.Succeeded) {
            return;
        }

        let task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) {
            return;
        }
        let taskBase = task.getTaskBase();
        if (taskBase.background || taskBase.isPanTask) {
            return;
        }

        if (taskBase.taskType === TaskBaseType.TaskType.Emule || taskBase.taskType === TaskBaseType.TaskType.P2sp) {
            this.nativeManager.upsert(taskBase.url!, -1, path.join(taskBase.savePath!, taskBase.taskName!));
        }
    });
    TaskManager.GetInstance().attachTaskDetailChangeEvent(async (taskId: number, flags: TaskBaseType.TaskDetailChangedFlags) => {
        if (!(flags & TaskBaseType.TaskDetailChangedFlags.TaskName) && !(flags & TaskBaseType.TaskDetailChangedFlags.SavePath)) {
            return;
        }
        let task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) {
            return;
        }
        let taskBase = task.getTaskBase();
        if (taskBase.background ||  taskBase.isPanTask || (taskBase.taskStatus !== TaskBaseType.TaskStatus.Succeeded)) {
            return;
        }

        if (taskBase.taskType === TaskBaseType.TaskType.Emule || taskBase.taskType === TaskBaseType.TaskType.P2sp) {
            this.nativeManager.upsert(taskBase.url!, -1, path.join(taskBase.savePath!, taskBase.taskName!));
        } else if (taskBase.taskType === TaskBaseType.TaskType.Bt) {
            if (!taskBase.url || taskBase.url.length === 0) {
                return;
            }

            let btTask = task.toExtra<BtTask>();
            let btFileInfos = await btTask.getBtFileInfos();
            for (let btFileInfo of btFileInfos) {
                if (btFileInfo.fileStatus === TaskBaseType.BtSubFileStatus.Complete) {
                    let strSavePath = path.join(taskBase.savePath!, taskBase.taskName!, btFileInfo!.filePath, btFileInfo.fileName);
                    this.nativeManager.upsert(taskBase.url!, btFileInfo.realIndex, strSavePath);
                }
            }
        }
    });
    TaskManager.GetInstance().attachGroupSubTaskStatusChangeEvent(async (taskId: number, groupTaskId: number, eOld: TaskBaseType.TaskStatus, eNew: TaskBaseType.TaskStatus) => {
        if (eNew !== TaskBaseType.TaskStatus.Succeeded) {
            return;
        }
        let groupTask = await TaskManager.GetInstance().findTaskById(groupTaskId);
        if (!groupTask || groupTask.isPanTask() || groupTask.isBackground()) {
            return;
        }
        let task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) {
            return;
        }
        let taskBase = task.getTaskBase();
        if (taskBase.taskType === TaskBaseType.TaskType.Emule || taskBase.taskType === TaskBaseType.TaskType.P2sp) {
            this.nativeManager.upsert(taskBase.url!, -1, path.join(taskBase.savePath!, taskBase.taskName!));
        }
    });
    TaskManager.GetInstance().attachGroupSubTaskDetailChangeEvent(async (taskId: number, groupTaskId: number, flags: TaskBaseType.TaskDetailChangedFlags) => {
        if (!(flags & TaskBaseType.TaskDetailChangedFlags.TaskName) && !(flags & TaskBaseType.TaskDetailChangedFlags.SavePath)) {
            return;
        }
        let groupTask = await TaskManager.GetInstance().findTaskById(groupTaskId);
        if (!groupTask || groupTask.isPanTask() || groupTask.isBackground()) {
            return;
        }
        let task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) {
            return;
        }
        let taskBase = task.getTaskBase();
        if (taskBase.taskStatus !== TaskBaseType.TaskStatus.Succeeded) {
            return;
        }
        if (taskBase.taskType === TaskBaseType.TaskType.Emule || taskBase.taskType === TaskBaseType.TaskType.P2sp) {
            this.nativeManager.upsert(taskBase.url!, -1, path.join(taskBase.savePath!, taskBase.taskName!));
        }
    });
    TaskManager.GetInstance().attachBtSubTaskStatusChangeEvent(async (taskId: number, fileIndex: number, eOld: TaskBaseType.BtSubFileStatus, eNew: TaskBaseType.BtSubFileStatus) => {
        if (eNew !== TaskBaseType.BtSubFileStatus.Complete) {
            return;
        }

        let task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) {
            return;
        }
        let taskBase = task.getTaskBase();
        if (taskBase.background || !taskBase.url || taskBase.url.length === 0) {
            return;
        }

        let btTask = task.toExtra<BtTask>();
        let btFileInfo = await btTask.getBtFileInfoByIndex(fileIndex);
        if (!btFileInfo) {
            return;
        }

        let strSavePath = path.join(taskBase.savePath!, taskBase.taskName!, btFileInfo!.filePath, btFileInfo.fileName);
        this.nativeManager.upsert(taskBase.url!, fileIndex, strSavePath);
    });
    TaskManager.GetInstance().attachBtSubTaskDetailChangeEvent(async (taskId: number, fileIndex: number, flags: TaskBaseType.BtSubFileDetailChangeFlags) => {
        if (!(flags & TaskBaseType.BtSubFileDetailChangeFlags.FileName) && !(flags & TaskBaseType.BtSubFileDetailChangeFlags.FilePath)) {
            return;
        }
        let task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) {
            return;
        }
        let taskBase = task.getTaskBase();
        if (taskBase.background || !taskBase.url || taskBase.url.length === 0) {
            return;
        }

        let btTask = task.toExtra<BtTask>();
        let btFileInfo = await btTask.getBtFileInfoByIndex(fileIndex);
        if (!btFileInfo || btFileInfo.fileStatus !== TaskBaseType.BtSubFileStatus.Complete) {
            return;
        }
        let strSavePath = path.join(taskBase.savePath!, taskBase.taskName!, btFileInfo!.filePath, btFileInfo.fileName);
        this.nativeManager.upsert(taskBase.url!, fileIndex, strSavePath);
    });
  }

  public static GetInstance(): DownloadFileRecord {
    if (!DownloadFileRecord.instance) {
      if (global.DownloadFileRecordImplInstance) {
        DownloadFileRecord.instance =  global.DownloadFileRecordImplInstance;
      } else {
        DownloadFileRecord.instance = new DownloadFileRecord();
        global.DownloadFileRecordImplInstance = DownloadFileRecord.instance;
      }
    }
    return DownloadFileRecord.instance;
  }

  public async query(url: string, index: number): Promise<string> {
    return await new Promise((v) => {
        this.nativeManager.query(url, index, (p: string) => {
            v(p);
        });
    });
  }
}
