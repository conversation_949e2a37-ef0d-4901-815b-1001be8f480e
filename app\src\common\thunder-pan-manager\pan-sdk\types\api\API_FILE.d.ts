export namespace API_FILE {
    /**
     * 应用信息(用户侧)
     */
    export interface DriveApp {
        /**
         * 应用ID，每个应用拥有一个唯一ID
         */
        id?: string;
        /**
         * 应用名称
         */
        name: string;
        /**
         * 可以使用此应用打开的文件类型，包括mime_type或者文件后缀, 满足任意一个条件即可
         */
        access?: string[];
        /**
         * 应用入口链接
         */
        link?: string;
        /**
         * 引导链接，用于引导开通会员，或者展示应用错误信息
         * 1. 当用户的会员等级无法使用该应用时，通过该链接跳转到会员开通页面
         * 2. 当应用不可用时（例如超过在线解压文件大小上限），通过该链接跳转错误提示页面
         */
        redirect_link?: string;
        /**
         * 在应用中打开当前文件所要求的会员类型(已废弃)
         */
        vip_types?: string[];
        /**
         * 用于判断是否需要更高的会员等级使用该应用
         * false表示允许用户打开该应用
         * true表示需要更高的会员身份才能打开该应用（所需会员等级在vip_types中列出）
         */
        need_more_quota?: boolean;
        /**
         * 应用图标
         */
        icon_link?: string;
        /**
         * 是否默认打开的应用，true表示进入应用列表时默认选择该应用
         */
        is_default?: boolean;
        /**
         * app自定义配置，app可通过接口查询自己的配置
         */
        params?: {
            [name: string]: string;
        };
        /**
         * app属于哪个品类
         */
        category_ids?: string[];
        /**
         * 广告位类型,0:不是广告位 1:是广告位 (其余枚举值用来预留后续的扩展需求,比如顶部广告位/底部广告位等)
         */
        ad_scene_type?: number; // int32
        /**
         * space 访问空间
         */
        space?: string;
        /**
         * 其它类型的链接
         * 遵循 mime_type => link
         * 例如:
         * "text/csv": "https://example.com/link?id=xxxx&format=csv",
         * "application/zip": "https://example.com/link?&format=zip",
         */
        links?: {
            [name: string]: DriveLink;
        };
    }
    /**
     * ApplyForPrivilegeRequest 申请特权请求
     */
    export interface DriveApplyForPrivilegeRequest {
        /**
         * privilege 申请特权的类型
         */
        privilege?: string;
        /**
         * meta 提交申请附加内容
         */
        meta?: {
            [name: string]: string;
        };
        /**
         * code 邀请码
         */
        code?: string;
        /**
         * space 空间
         */
        space?: string;
    }
    /**
     * ApplyForPrivilegeResponse 申请特权结果
     */
    export interface DriveApplyForPrivilegeResponse {
        /**
         * result 申请结果
         */
        result?: DriveApplyForPrivilegeResponseResult;
        /**
         * message 结果说明
         */
        message?: string;
        /**
         * is_grant_updated 授权是否更新
         */
        is_grant_updated?: boolean;
        /**
         * data 申请权限详细信息，例如：
         * - createTime: 申请权限时间
         * - expireTime: 权限到期时间
         * - nowTime: 当前时间
         */
        data?: {
            [name: string]: string;
        };
    }
    /**
     * - REJECTED: 拒绝申请
     *  - REVIEWING: 申请完成,待审核
     *  - ACCEPTED: 申通通过,授予特权
     */
    export type DriveApplyForPrivilegeResponseResult = "REJECTED" | "REVIEWING" | "ACCEPTED";
    /**
     * Audio 音频信息
     */
    export interface DriveAudio {
        /**
         * 专辑
         */
        album?: string;
        /**
         * 艺人
         */
        artist?: string;
        /**
         * 原专辑艺人
         */
        album_artist?: string;
        /**
         * 作曲者
         */
        composer?: string;
        /**
         * 归类
         */
        grouping?: string;
        /**
         * 类型，风格
         */
        genre?: string;
        /**
         * 年份
         */
        year?: string;
        /**
         * 时长
         */
        duration?: string;
        /**
         * 轨道
         */
        track?: string;
        /**
         * 采样率
         */
        sample_rate?: string;
        /**
         * 比特率
         */
        bitrate?: string;
        /**
         * 音频声道
         */
        channels?: string;
        /**
         * 歌词地址
         */
        lyric_address?: string;
        /**
         * 编解码器
         */
        codec?: string;
        /**
         * 扩展信息
         */
        ext?: string;
        /**
         * 更新时间
         */
        update_time?: string;
    }
    /**
     * 审核状态
     */
    export interface DriveAudit {
        /**
         * 审核状态
         */
        status?: DriveAuditStatus;
        /**
         * message 审核状态说明，用于客户端展现。
         */
        message?: string;
        /**
         * 审核状态简短说明
         */
        title?: string;
    }
    /**
     * 风控过滤结果
     * - STATUS_UNKNOW: 未知
     *  - STATUS_OK: 允许
     *  - STATUS_SENSITIVE_RESOURCE: 敏感资源
     *  - STATUS_SENSITIVE_WORD: 文件名包含敏感词
     *  - STATUS_INVALID_RESOURCE: 无效资源
     */
    export type DriveAuditStatus = "STATUS_UNKNOW" | "STATUS_OK" | "STATUS_SENSITIVE_RESOURCE" | "STATUS_SENSITIVE_WORD" | "STATUS_INVALID_RESOURCE";
    /**
     * BatchCopyFileRequest 批量拷贝文件请求
     */
    export interface DriveBatchCopyFilesRequest {
        /**
         * 文件ID列表，建议一次最多拷贝100个文件
         */
        ids?: string[];
        /**
         * to 拷贝目标地址
         */
        to?: DriveBatchCopyFilesRequestTo;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * To 目标对象
     */
    export interface DriveBatchCopyFilesRequestTo {
        /**
         * parent_id 关联文件夹
         */
        parent_id?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * BatchCopyFileResponse  批量拷贝响应，返回相关task_id
     */
    export interface DriveBatchCopyFilesResponse {
        task_id?: string;
        /**
         * 空间
         */
        space?: string;
    }
    /**
     * BatchDeleteRequest 批量删除文件
     */
    export interface DriveBatchDeleteFilesRequest {
        /**
         * 文件ID列表，建议一次最多删除100个文件
         */
        ids?: string[];
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 扩展信息
         * 海外传入with=only_delete_uncompleted, 用于删除上传中或上传失败的文件
         */
        with?: string[];
    }
    /**
     * BatchDeleteResponse 批量删除文件响应
     */
    export interface DriveBatchDeleteFilesResponse {
        /**
         * 任务ID
         */
        task_id?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * BatchGetObjectIDsResponse 批量获取对象唯一id响应
     */
    export interface DriveBatchGetObjectIDsResponse {
        /**
         * id列表
         */
        ids?: string[];
    }
    /**
     * BatchGetPrivilegesResponse 用户特权批量检查结果
     */
    export interface DriveBatchGetPrivilegesResponse {
        /**
         * result字典 {privilege: PrivilegeData}, 权限对应的数据详细, 比如会员信息、云解压压缩包大小信息、上传流量信息、云盘空间大小信息、回收站时长信息
         */
        result?: {
            [name: string]: DrivePrivilegeData;
        };
    }
    /**
     * BatchMoveFileRequest 批量移动文件请求
     */
    export interface DriveBatchMoveFilesRequest {
        /**
         * 文件ID列表，建议一次最多移动100个文件
         */
        ids?: string[];
        /**
         * to 移动目标地址
         */
        to?: DriveBatchMoveFilesRequestTo;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * To 目标对象
     */
    export interface DriveBatchMoveFilesRequestTo {
        /**
         * parent_id 关联文件夹
         */
        parent_id?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    export interface DriveBatchMoveFilesResponse {
        task_id?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * BatchTrashRequest 批量移入回收站
     */
    export interface DriveBatchTrashFilesRequest {
        /**
         * 文件ID列表，建议一次最多移入100个文件
         */
        ids?: string[];
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * BatchTrashResponse 批量移入回收站响应
     */
    export interface DriveBatchTrashFilesResponse {
        /**
         * 任务ID
         */
        task_id?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * BatchUntrashRequest 批量还原回收站文件
     */
    export interface DriveBatchUntrashFilesRequest {
        /**
         * 文件ID列表，建议一次最多还原100个文件
         */
        ids?: string[];
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * BatchUntrashResponse 批量还原回收站文件响应
     */
    export interface DriveBatchUntrashFilesResponse {
        /**
         * 任务ID
         */
        task_id?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * BatchUpdateFilesRequest 批量更新文件信息
     */
    export interface DriveBatchUpdateFilesRequest {
        /**
         * file 需要更新的信息
         */
        files?: DriveFile[];
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * BatchUpdateFilesResponse 批量更新文件信息返回
     */
    export interface DriveBatchUpdateFilesResponse {
        /**
         * file 需要更新的信息
         */
        files?: DriveFile[];
    }
    /**
     * CheckImportDownloadResponse 检查导入下载响应
     */
    export interface DriveCheckImportDownloadResponse {
        /**
         * is_import 是否导入
         */
        is_import?: boolean;
        /**
         * can_load_records 可导入记录数
         */
        can_load_records?: number; // int32
    }
    /**
     * CheckPrivilegeResponse 用户特权检查结果
     */
    export interface DriveCheckPrivilegeResponse {
        /**
         * result 申请结果
         */
        result?: DriveCheckPrivilegeResponseResult;
        /**
         * message 结果说明
         */
        message?: string;
        /**
         * redirect_uri 重定向地址,使用前需要检查是否为空
         */
        redirect_uri?: string;
        /**
         * data 权限对应的数据详细,比如会员信息
         */
        data?: {
            [name: string]: string;
        };
    }
    /**
     * - REJECTED: 用户无资格申请或使用此特权
     *  - UNSUBMITTED: 用户有资格但需要用户主动提交申请
     *  - REVIEWING: 用户已提交申请, 待管理员审核
     *  - ACCEPTED: 已授权
     */
    export type DriveCheckPrivilegeResponseResult = "REJECTED" | "UNSUBMITTED" | "REVIEWING" | "ACCEPTED";
    /**
     * 合辑信息
     */
    export interface DriveCollection {
        /**
         * 类型 video/audio/image
         */
        type?: string;
        /**
         * 文件数量
         */
        count?: number; // int32
        /**
         * 排列顺序(从大到小排列)
         */
        order?: string; // int64
    }
    export interface DriveConfirmPreCreateFileRequest {
        task_id?: string;
        file_id?: string;
    }
    /**
     * To 目标对象
     */
    export interface DriveCopyFileRequestTo {
        /**
         * parent_id 关联文件夹
         */
        parent_id?: string;
        /**
         * name 目标文件名
         */
        name?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * UploadForm 使用form上传，响应里面会返回header、params、url等
     */
    export interface DriveCreateFileAdminRequestUploadForm {
    }
    /**
     * UploadResumable 断点上传类型，不同版本会有不同的实现方法
     */
    export interface DriveCreateFileAdminRequestUploadResumable {
        /**
         * provider 上传提供方
         */
        provider?: DriveProvider;
    }
    /**
     * UploadUrl 链接上传类型
     */
    export interface DriveCreateFileAdminRequestUploadUrl {
        /**
         * url 上传文件的地址，比如：资源链接
         */
        url?: string;
        /**
         * files 如果链接存在多文件，该变量用于筛选需要上传的文件
         */
        files?: string[];
    }
    /**
     * CreateFileAdminResponse 创建文件admin response
     */
    export interface DriveCreateFileAdminResponse {
        /**
         * upload_type 上传
         */
        upload_type?: DriveUploadType;
        /**
         * form 表单上传类型
         */
        form?: DriveCreateFileAdminResponseUploadForm;
        /**
         * url 链接上传类型
         */
        url?: DriveCreateFileAdminResponseUploadUrl;
        /**
         * resumable 断点续传
         */
        resumable?: DriveCreateFileAdminResponseUploadResumable;
        /**
         * file 创建的文件信息
         */
        file?: DriveFile;
        /**
         * task 该次请求对应的task信息
         */
        task?: DriveTask;
    }
    /**
     * UploadForm 表单上传类型，上传表单需要的参数。
     */
    export interface DriveCreateFileAdminResponseUploadForm {
        /**
         * kind 固定值
         */
        kind?: string;
        /**
         * url 提交链接
         */
        url?: string;
        /**
         * method form提交http method
         */
        method?: string;
        /**
         * multi_parts 表单附加属性
         */
        multi_parts?: {
            [name: string]: string;
        };
        /**
         * headers 提交请求时需要带上的header
         */
        headers?: {
            [name: string]: string;
        };
    }
    /**
     * UploadResumable 断点续传类型
     */
    export interface DriveCreateFileAdminResponseUploadResumable {
        kind?: string;
        /**
         * provider 实现类型
         */
        provider?: DriveProvider;
        /**
         * params 客户端上传需要的参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * UploadUrl 链接上传类型
     */
    export interface DriveCreateFileAdminResponseUploadUrl {
        kind?: string;
    }
    /**
     * CreateFileRequest 预创建文件请求
     */
    export interface DriveCreateFileRequest {
        /**
         * kind 文件或文件夹 drive#file drive#folder
         */
        kind?: string;
        /**
         * parent_id 上传路径id
         */
        parent_id?: string;
        /**
         * name 文件名，对于离线任务选填，其他则必填, 填写的名字必须是规范的文件名，不能包含非法字符
         */
        name?: string;
        /**
         * hash 文件哈希 gcid
         */
        hash?: string;
        /**
         * size 文件大小
         */
        size?: string; // int64
        /**
         * upload_type 文件上传类型
         */
        upload_type?: DriveUploadType;
        /**
         * url 使用url上传
         */
        url?: DriveCreateFileRequestUploadUrl;
        /**
         * form 默认使用form上传
         */
        form?: DriveCreateFileRequestUploadForm;
        /**
         * resumable 断点续传类型
         */
        resumable?: DriveCreateFileRequestUploadResumable;
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 创建文件夹出现重名时返回已存在的文件夹
         */
        ignore_duplicated_name?: boolean;
        /**
         * 文件id
         */
        id?: string;
        /**
         * 创建文件的扩展参数
         * 创建文件请求中， 增加"params": { "require_links": "true" } ，表示需要直接返回播放链接(边添加边播模式)
         * 返回File结构为空或者File中Media信息为空, 都表示不可边添加边播, 具体可以分为:
         *   - 非单视频任务, 单视频不可秒传任务, 不返回File结构
         *   - 审核不通过的任务, 返回File结构, File结构中包含审核结果
         *   - 其它错误, 不返回File结构
         *
         * 流畅播相关参数
         * - from: 标识场景来源是 新建面板流畅播(from="FLUENT_PLAY"), 无此场景不用传
         * - cookie: 浏览器cookie
         * - is_private：是否私密空间
         *
         * 云添加/流畅播 审核相关参数
         * 当云添加/流畅播的链接来源于网页时，需要传递以下参数，用于审核
         * - web_title: 链接来源页面的标题
         * - referer: 链接来源页面的地址
         * - played: 如果链接在浏览器的网页播的播放器中播放过，则played="1"，否则played="0"
         * - scene: 云添加/流畅播场景，取值如下
         *   - web_play 网页播
         *   - smart_spot_panel 嗅探面板
         *   - smart_spot_panel_player 嗅探面板(来自播放器)
         *   - browser_url_panel 浏览器新建面板
         *   - turl_panel bt面板
         *   - browser_turl_panel 浏览器bt面板
         *   - clipboard 剪贴板
         *   - url_panel 新建面板
         *   - birdkey_page 口令识别页
         *   - smart_spot 智能识别气泡
         *   - download_list 下载列表
         *   - download_record 下载记录
         *   - qrcode 二维码扫描
         *   - url_player 链接播放
         */
        params?: {
            [name: string]: string;
        };
        /**
         * 文件夹类型
         */
        folder_type?: string;
        /**
         * 是否不创建文件
         *   - 云添加仅提交采集不创建文件
         */
        dry_run?: boolean;
        /**
         * 任务是否需要去重
         *  - "true": 如果任务已存在直接返回任务信息, 否则创建任务
         *  - "false": 不管任务存在与否, 都重新创建任务
         */
        need_dedup?: boolean;
        /**
         * path 文件创建路径
         */
        path?: string;
    }
    /**
     * UploadForm 使用form上传，响应里面会返回header、params、url等
     */
    export interface DriveCreateFileRequestUploadForm {
    }
    /**
     * UploadResumable 断点上传类型，不同版本会有不同的实现方法
     */
    export interface DriveCreateFileRequestUploadResumable {
        /**
         * provider 上传提供方
         */
        provider?: DriveProvider;
    }
    /**
     * UploadUrl 链接上传类型
     */
    export interface DriveCreateFileRequestUploadUrl {
        /**
         * url 上传文件的地址，比如：资源链接
         */
        url?: string;
        /**
         * files 如果链接存在多文件，该变量用于筛选需要上传的文件
         */
        files?: string[];
    }
    /**
     * CreateFileResponse 预创建文件的响应，客户端需要根据upload_type来判断具体的上传内容
     */
    export interface DriveCreateFileResponse {
        /**
         * upload_type 上传
         */
        upload_type?: DriveUploadType;
        /**
         * form 表单上传类型
         */
        form?: DriveCreateFileResponseUploadForm;
        /**
         * url 链接上传类型
         */
        url?: DriveCreateFileResponseUploadUrl;
        /**
         * resumable 断点续传
         */
        resumable?: DriveCreateFileResponseUploadResumable;
        /**
         * file 创建的文件信息
         */
        file?: DriveFile;
        /**
         * task 该次请求对应的task信息
         */
        task?: DriveTask;
    }
    /**
     * UploadForm 表单上传类型，上传表单需要的参数。
     */
    export interface DriveCreateFileResponseUploadForm {
        /**
         * kind 固定值
         */
        kind?: string;
        /**
         * url 提交链接
         */
        url?: string;
        /**
         * method form提交http method
         */
        method?: string;
        /**
         * multi_parts 表单附加属性
         */
        multi_parts?: {
            [name: string]: string;
        };
        /**
         * headers 提交请求时需要带上的header
         */
        headers?: {
            [name: string]: string;
        };
    }
    /**
     * UploadResumable 断点续传类型
     */
    export interface DriveCreateFileResponseUploadResumable {
        kind?: string;
        /**
         * provider 实现类型
         */
        provider?: DriveProvider;
        /**
         * params 客户端上传需要的参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * UploadUrl 链接上传类型
     */
    export interface DriveCreateFileResponseUploadUrl {
        kind?: string;
    }
    /**
     * CreatePrivilegeCodeRequest 创建邀请码请求
     */
    export interface DriveCreatePrivilegeCodeRequest {
        /**
         * 特权名称
         */
        privilege?: string;
        /**
         * 自定义邀请码 批量(count>1)创建不允许自定义
         */
        code?: string;
        /**
         * 可使用次数
         */
        usable_times?: number; // int32
        /**
         * 创建个数
         */
        count?: number; // int32
        /**
         * 其他参数
         */
        params?: {
            [name: string]: string;
        };
        /**
         * space 空间
         */
        space?: string;
    }
    /**
     * CreatePrivilegeCodeResponse 创建邀请码响应
     */
    export interface DriveCreatePrivilegeCodeResponse {
        /**
         * 邀请码列表
         */
        codes?: DrivePrivilegeCode[];
    }
    /**
     * DeleteFileResponse 删除文件响应
     */
    export interface DriveDeleteFileResponse {
    }
    /**
     * DeletePrivilegeCodeResponse 删除邀请码响应
     */
    export interface DriveDeletePrivilegeCodeResponse {
    }
    export interface DriveDeleteSpaceResponse {
        deleted_uids?: string /* int64 */ [];
    }
    /**
     * DisposalFileStatus 处置文件状态
     * - STATUSNORMAL: 正常，可消费，可分享
     *  - STATUSCONSUMABLE: 可消费，不可分享
     *  - STATUSNOTCONSUMABLE: 不可消费，不可分享
     */
    export type DriveDisposalFileStatus = "STATUSNORMAL" | "STATUSCONSUMABLE" | "STATUSNOTCONSUMABLE";
    /**
     * DisposalFilesRequest 文件处置请求
     */
    export interface DriveDisposalFilesRequest {
        /**
         * userid 用户ID
         */
        userid?: string;
        /**
         * space 文件空间
         */
        space?: string;
        /**
         * infos 信息
         */
        infos?: DriveDisposalFilesRequestInfo[];
    }
    /**
     * Info 信息
     */
    export interface DriveDisposalFilesRequestInfo {
        /**
         * file_id 文件ID
         */
        file_id?: string;
        /**
         * disposal_status 文件处置状态
         */
        disposal_status?: DriveDisposalFileStatus;
    }
    /**
     * DisposalFilesResponse 文件处置响应
     */
    export interface DriveDisposalFilesResponse {
    }
    /**
     * EmptyTrashResponse 清除回收站响应
     */
    export interface DriveEmptyTrashResponse {
        task_id?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * File 文件信息
     */
    export interface DriveFile {
        /**
         * the kind of the file, 可选值： drive#file和drive#folder
         */
        kind?: string;
        /**
         * The id of the file
         */
        id?: string;
        /**
         * The parent_id of the file
         */
        parent_id?: string;
        /**
         * The name of the file
         */
        name?: string;
        /**
         * The owner user_id of the file
         */
        user_id?: string;
        /**
         * The size of the file's content in bytes.
         */
        size?: string; // int64
        /**
         * The revision of the file
         */
        revision?: string; // int64
        /**
         * The final component of fullFileExtension. This is only available for files with binary content in Google Drive.
         */
        file_extension?: string;
        /**
         * The MIME type of the file. https://tool.oschina.net/commons
         */
        mime_type?: string;
        /**
         * Whether the user has starred the file.
         */
        starred?: boolean;
        /**
         * A link for downloading the content of the file in a browser.
         */
        web_content_link?: string;
        /**
         * 创建时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        created_time?: string;
        /**
         * 系统修改时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        modified_time?: string;
        /**
         * A static, unauthenticated link to the file's icon.
         */
        icon_link?: string;
        /**
         * A short-lived link to the file's thumbnail, if available. Typically lasts on the order of hours. Only populated when the requesting app can access the file's content.
         */
        thumbnail_link?: string;
        /**
         * file md5Checksum
         */
        md5_checksum?: string;
        /**
         * file hash gcid
         */
        hash?: string;
        /**
         * 其它类型的链接
         * 遵循 mime_type => link
         * 例如:
         * "text/csv": "https://example.com/link?id=xxxx&format=csv",
         * "application/zip": "https://example.com/link?&format=zip",
         */
        links?: {
            [name: string]: DriveLink;
        };
        /**
         * 状态阶段
         */
        phase?: DrivePhaseType;
        /**
         * 审核状态
         */
        audit?: DriveAudit;
        /**
         * medias 媒体信息
         */
        medias?: DriveMedia[];
        /**
         * 是否是回收站文件
         */
        trashed?: boolean;
        /**
         * 回收站文件预定删除时间
         */
        delete_time?: string;
        /**
         * 文件原始url
         */
        original_url?: string;
        /**
         * params 扩展参数
         */
        params?: {
            [name: string]: string;
        };
        /**
         * BT文件索引
         */
        original_file_index?: number; // int64
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 支持的app
         */
        apps?: DriveApp[];
        /**
         * 文件是否可写
         */
        writable?: boolean;
        /**
         * 文件夹类型
         */
        folder_type?: string;
        /**
         * 合辑信息
         */
        collection?: DriveCollection;
        /**
         * The sort_name of the file
         */
        sort_name?: string;
        /**
         * 用户修改时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        user_modified_time?: string;
        /**
         * 拼写文件名，不包含文件后缀
         */
        spell_name?: DriveWordSpell[];
        /**
         * 文件类型
         * video,text,image,audio,archive,font,subtitle,installer,other
         */
        file_category?: DriveFileCategory;
        /**
         * 文件的标签
         */
        tags?: DriveTag[];
        /**
         * 文件涉及到的事件
         */
        reference_events?: DriveReferenceEvent[];
        /**
         * 引用的资源 如:任务/子任务/...
         */
        reference_resource?: ProtobufAny;
    }
    /**
     * FileCategory 文件类型
     * - OTHER: 其他文件类型
     *  - VIDEO: Video视频文件类型
     *  - TEXT: Text文本文件类型
     *  - IMAGE: Image图像文件类型
     *  - AUDIO: Audio音频文件类型
     *  - ARCHIVE: Archive归档文件类型
     *  - FONT: Font字体文件类型
     *  - SUBTITLE: Subtitle字幕文件类型
     *  - INSTALLER: Installer安装包文件类型
     */
    export type DriveFileCategory = "OTHER" | "VIDEO" | "TEXT" | "IMAGE" | "AUDIO" | "ARCHIVE" | "FONT" | "SUBTITLE" | "INSTALLER";
    /**
     * FileLabel 文件标签
     */
    export interface DriveFileLabel {
        /**
         * 标签id
         */
        id?: string;
        /**
         * 标签名
         */
        name?: string;
        /**
         * 描述
         */
        desc?: string;
        /**
         * 标签图案
         */
        icon_link?: string;
        /**
         * 状态
         */
        status?: DriveFileLabelStatus;
    }
    /**
     * - NORMAL: 正常
     *  - EXPIRED: 已过期
     */
    export type DriveFileLabelStatus = "NORMAL" | "EXPIRED";
    /**
     * FileOrder 文件排序类型
     * - DEFAULT_ORDER: 默认排序，由服务端决定
     *  - ID_DESC: 文件ID倒序
     *  - TYPE_DESC: 文件类型排序：默认文件夹 > 普通文件夹 > 文件 > id倒序排列
     *  - MODIFY_TIME_ASC: 文件修改时间正序
     *  - MODIFY_TIME_DESC: 文件修改时间倒序
     *  - SIZE_ASC: 文件大小正序
     *  - SIZE_DESC: 文件大小倒序
     *  - CREATED_TIME_ASC: 文件创建时间升序
     *  - CREATED_TIME_DESC: 文件创建时间降序
     *  - NAME_ASC: 文件名正序
     *  - NAME_DESC: 文件名倒序
     */
    export type DriveFileOrder = "DEFAULT_ORDER" | "ID_DESC" | "TYPE_DESC" | "MODIFY_TIME_ASC" | "MODIFY_TIME_DESC" | "SIZE_ASC" | "SIZE_DESC" | "CREATED_TIME_ASC" | "CREATED_TIME_DESC" | "NAME_ASC" | "NAME_DESC";
    export interface DriveFileTags {
        /**
         * 文件id
         */
        file_id?: string;
        /**
         * 标题
         */
        title?: string;
        /**
         * 标签详情
         */
        details?: DriveFileLabel[];
    }
    /**
     * 文件用途
     * - ALL: 拉取文件全部信息
     *  - FETCH: 文件取回
     * 用于文件取回，不支持播放
     *  - PLAY: 文件播放
     * 用于媒体播放，不支持文件取回
     *  - CACHE: 文件预缓存
     * 客户端取medias列表里的default资源做预缓存，该资源链接与普通播放链接限速策略不同
     * medias列表为空表示禁止当前客户端预缓存
     *  - CACHE_ALL: 缓存文件全部信息，可用于取回和播放
     *  - PROJECTION: 投屏
     *  - CONSUME: 文件消费
     *  - DISPLAY: 仅用于文件展示, 类似文件列表接口, 不返回取回链接、播放链接和云app信息
     */
    export type DriveFileUsage = "ALL" | "FETCH" | "PLAY" | "CACHE" | "CACHE_ALL" | "PROJECTION" | "CONSUME" | "DISPLAY";
    /**
     * GetAboutAdminResponse 获取网盘信息管理后台接口响应体
     */
    export interface DriveGetAboutAdminResponse {
        /**
         * kind 固定值：drive#about
         */
        kind?: string;
        /**
         * 限额信息
         */
        quota?: DriveQuota;
        /**
         * quota信息过期时间
         */
        expires_at?: string;
    }
    /**
     * GetAboutResponse 获取网盘信息响应
     */
    export interface DriveGetAboutResponse {
        /**
         * kind 固定值：drive#about
         */
        kind?: string;
        /**
         * 限额信息
         */
        quota?: DriveQuota;
        /**
         * quota信息过期时间
         */
        expires_at?: string;
        /**
         * 额度字典 {name: Quota}
         */
        quotas?: {
            [name: string]: DriveQuota;
        };
    }
    /**
     * GetFileAncestorsAdminResponse 获取文件祖先admin响应
     */
    export interface DriveGetFileAncestorsAdminResponse {
        /**
         * files 文件信息
         */
        ancestors?: DriveFile[];
    }
    export interface DriveGetFileLabelResponse {
        /**
         * 标签文案
         */
        text?: string;
        /**
         * 文件标签列表
         */
        file_tags?: DriveFileTags[];
    }
    export interface DriveGetLinkResponse {
        /**
         * 云播链接
         */
        link?: DriveLink;
        /**
         * 审核状态
         */
        audit?: DriveAudit;
    }
    /**
     * GetPrivilegeCodeRecordsResponse 查询特权邀请码记录响应
     */
    export interface DriveGetPrivilegeCodeRecordsResponse {
        /**
         * 邀请码信息
         */
        records?: DrivePrivilegeCodeRecord[];
    }
    /**
     * GetPrivilegeCodeResponse 查询特权邀请码响应
     */
    export interface DriveGetPrivilegeCodeResponse {
        /**
         * 邀请码信息
         */
        code?: DrivePrivilegeCode;
        /**
         * 用户信息
         */
        user_info?: DriveUserInfo;
    }
    /**
     * 图片尺寸
     * - SIZE_DEFAULT: 默认 具体行为由服务端定义
     *  - SIZE_SMALL: 小图
     *  - SIZE_MEDIUM: 中图
     *  - SIZE_LARGE: 大图
     *  - SIZE_BIG: 兼容TV端视频缩略图 width*height=720*406
     */
    export type DriveImageSize = "SIZE_DEFAULT" | "SIZE_SMALL" | "SIZE_MEDIUM" | "SIZE_LARGE" | "SIZE_BIG";
    /**
     * ImportDownloadRecordRequest 导入下载记录请求
     */
    export interface DriveImportDownloadRecordRequest {
        /**
         * space 空间
         */
        space?: string;
    }
    /**
     * ImportDownloadRecordResponse 导入下载记录响应
     */
    export interface DriveImportDownloadRecordResponse {
    }
    /**
     * Link 下载/播放链接
     */
    export interface DriveLink {
        /**
         * url 下载/播放链接
         */
        url?: string;
        /**
         * token 加速token
         */
        token?: string;
        /**
         * expire 过期时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        expire?: string;
        /**
         * type 链接类型 wan=广域网 lan=局域网
         */
        type?: string;
    }
    /**
     * ListFilesAdminResponse 文件列表admin
     */
    export interface DriveListFilesAdminResponse {
        /**
         * files 文件信息
         */
        files?: DriveFile[];
        /**
         * next_page_token
         */
        next_page_token?: string;
    }
    /**
     * ListFileResponse 列举文件响应
     */
    export interface DriveListFilesResponse {
        /**
         * kind 固定值：drive#fileList
         */
        kind?: string;
        /**
         * next_page_token 偏移量
         */
        next_page_token?: string;
        /**
         * 下次请求的文件类型
         */
        folder_type?: string;
        /**
         * files 搜索结果
         */
        files?: DriveFile[];
        /**
         * version 用户云盘文件版本。服务端通过云盘文件列表接口下发给客户端，客户端需要将这个值保存在本地，并且在每次请求云盘文件增量更新时带上该值
         */
        version?: string;
        /**
         * version_outdated 客户端上报的云盘文件版本是否已过时。当服务端下发 version_outdated=true 时，客户端需要做一次文件全量同步
         */
        version_outdated?: boolean;
        /**
         * 客户端和服务器文件同步完成的时间(目前仅标签同步完成会返回这个值)
         * 可以做为下次增量同步传入的起始时间
         */
        sync_time?: string;
    }
    /**
     * ListPrivilegeCodeRecordsResponse 查询邀请码使用记录响应
     */
    export interface DriveListPrivilegeCodeRecordsResponse {
        /**
         * 下一页页码
         */
        next_page_token?: string;
        /**
         * 使用记录列表
         */
        records?: DrivePrivilegeCodeRecord[];
    }
    /**
     * ListPrivilegeCodesResponse 查询邀请码列表响应
     */
    export interface DriveListPrivilegeCodesResponse {
        /**
         * 下一页页码
         */
        next_page_token?: string;
        /**
         * 邀请码列表
         */
        codes?: DrivePrivilegeCode[];
        /**
         * 剩余额度
         */
        quota?: number; // int32
        /**
         * total 总数
         */
        total?: number; // int32
    }
    /**
     * Media 媒体信息
     * 分辨率判断
     * 清晰度|范围（pixel=高X宽）| 备注
     *
     * 8K|pixel>=20736000|20736000=(8K+4K)/2
     *
     * 4K|5990400<=pixel<20736000|5990400=(4K+2K)/2
     *
     * 2K|2880000<=pixel<5990400|2880000=(2K+1080)/2
     *
     * 1080P|1497600<=pixel<2880000|1497600=(1080+720)/2
     *
     * 720P|664800<=pixel<1497600|664800=(720+480)/2
     *
     * 480P|319680<=pixel<664800 | 319680=(480+360)/2
     *
     * 360P|pixel<319680
     */
    export interface DriveMedia {
        /**
         * media_id 媒体ID。后续根据该ID来取播放链接
         */
        media_id?: string;
        /**
         * media_name 清晰度名称，用于清晰度列表展示：流畅 480P、高清 720P、超清1080P、无损
         */
        media_name?: string;
        /**
         * video 视频信息，包括分辨率、码率等信息
         */
        video?: DriveVideo;
        /**
         * link 播放链接信息
         */
        link?: DriveLink;
        /**
         * need_more_quota 用于判断用户是否有资格播放该清晰度的资源，值为true时表示需要更高的会员身份（所需会员等级在vip_types中列出），实际能否播放还需进一步判断is_visible
         */
        need_more_quota?: boolean;
        /**
         * vip_types 当need_more_quota=true时，标识该资源需要的会员身份
         */
        vip_types?: string[];
        /**
         * redirect_link 导购链接。目前，当need_more_quota=true时，则该链接跳转到购买vip链接
         */
        redirect_link?: string;
        /**
         * icon_link 清晰度图片
         */
        icon_link?: string;
        /**
         * is_default 是否默认播放，进入播放列表默认选择该清晰度
         */
        is_default?: boolean;
        /**
         * priority 播放优先级，级别越高越优先播放
         */
        priority?: number; // int32
        /**
         * is_origin 是否是原始画质视频
         */
        is_origin?: boolean;
        /**
         * resolution_name 分辨率名称。例如：4K、2K、1080P，用于展现原始画质的清晰度。
         */
        resolution_name?: string;
        /**
         * is_visible 该条目是否对用户可见。当is_visible=false时，清晰度列表需要隐藏该条目。
         */
        is_visible?: boolean;
        /**
         * category 视频清晰度类别，例如：原始画质、转码画质、HDR画质
         */
        category?: string;
        /**
         * 音频
         */
        audio?: DriveAudio;
    }
    /**
     * To 目标对象
     */
    export interface DriveMoveFileRequestTo {
        /**
         * parent_id 关联文件夹
         */
        parent_id?: string;
        /**
         * name 目标文件名
         */
        name?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * MoveFileResponse 移动文件响应
     */
    export interface DriveMoveFileResponse {
        task_id?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * PhaseType 文件阶段类型
     * - PHASE_TYPE_PENDING: 等待中
     *  - PHASE_TYPE_RUNNING: 进行中
     *  - PHASE_TYPE_ERROR: 失败
     *  - PHASE_TYPE_COMPLETE: 完成
     *  - PHASE_TYPE_PAUSED: 暂停
     */
    export type DrivePhaseType = "PHASE_TYPE_UNKNOW" | "PHASE_TYPE_PENDING" | "PHASE_TYPE_RUNNING" | "PHASE_TYPE_ERROR" | "PHASE_TYPE_COMPLETE" | "PHASE_TYPE_PAUSED";
    /**
     * 特权邀请码
     */
    export interface DrivePrivilegeCode {
        /**
         * ID
         */
        id?: string;
        /**
         * 创建者uid
         */
        owner?: string;
        /**
         * 特权名称
         */
        privilege?: string;
        /**
         * 邀请码
         */
        code?: string;
        /**
         * 邀请码过期时间
         * 日期时间格式使用RFC 3339格式精确到毫秒
         * 例如：2006-01-02T15:04:05.999Z07:00
         */
        expires_at?: string;
        /**
         * 可使用次数
         */
        usable_times?: number; // int32
        /**
         * 已使用次数
         */
        used_times?: number; // int32
        /**
         * 状态
         */
        status?: DrivePrivilegeCodeStatus;
        /**
         * 其他参数
         */
        params?: {
            [name: string]: string;
        };
        /**
         * 特权有效时间，单位秒
         */
        privilege_valid_seconds?: string; // int64
    }
    /**
     * PrivilegeCodeRecord 邀请码操作记录
     */
    export interface DrivePrivilegeCodeRecord {
        /**
         * 邀请码
         */
        code?: string;
        /**
         * 使用者uid
         */
        user?: string;
        /**
         * 记录创建时间
         * 日期时间格式使用RFC 3339格式精确到毫秒
         * 例如：2006-01-02T15:04:05.999Z07:00
         */
        create_time?: string;
        /**
         * user_info 用户信息
         */
        user_info?: DriveUserInfo;
    }
    /**
     * - AVAILABLE: 可使用
     *  - USEDUP: 次数已用完
     *  - DELETED: 已删除
     *  - EXPIRED: 已过期
     */
    export type DrivePrivilegeCodeStatus = "AVAILABLE" | "USEDUP" | "DELETED" | "EXPIRED";
    export interface DrivePrivilegeData {
        /**
         * data字典 {VIPType: 由json数组序列化而成的字符串}。如下例子是上传流量大小特权：
         * user、vip.platinum、vip.super、vip.super.year分别代表普通用户、白金、超会、年费超会
         * 每个会员身份对应的val是序列化后的json数组，json结构中的limit代表流量限制(单位：字节)，period代表时间。{\"limit\":1073741824,\"period\":\"day\"}代表每天最大上传流量为1073741824字节
         * "UPLOAD_FLOW_SIZE_LIMIT": {
         * "data": {
         * "user": "[{\"limit\":1073741824,\"period\":\"day\"},{\"limit\":10737418240,\"period\":\"month\"}]",
         * "vip.platinum": "[{\"limit\":21474836480,\"period\":\"day\"},{\"limit\":107374182400,\"period\":\"month\"}]",
         * "vip.super": "[{\"limit\":128849018880,\"period\":\"day\"},{\"limit\":536870912000,\"period\":\"month\"}]",
         * "vip.super.year": "[{\"limit\":257698037760,\"period\":\"day\"},{\"limit\":1099511627776,\"period\":\"month\"}]"
         * }
         * }
         */
        data?: {
            [name: string]: string;
        };
    }
    /**
     * - PROVIDER_ALIYUN: 阿里云OSS上传。
     * 需要集成对应端的SDK。
     */
    export type DriveProvider = "PROVIDER_UNKNOWN" | "PROVIDER_ALIYUN";
    /**
     * QrivilegeFluentPlayRequest 特权流畅播请求
     */
    export interface DriveQrivilegeFluentPlayRequest {
        /**
         * kind 文件或文件夹 drive#file drive#folder
         */
        kind?: string;
        /**
         * parent_id 上传路径id
         */
        parent_id?: string;
        /**
         * name 文件名，对于离线任务选填，其他则必填, 填写的名字必须是规范的文件名，不能包含非法字符
         */
        name?: string;
        /**
         * hash 文件哈希 gcid
         */
        hash?: string;
        /**
         * size 文件大小
         */
        size?: string; // int64
        /**
         * upload_type 文件上传类型
         */
        upload_type?: DriveUploadType;
        /**
         * url 使用url上传
         */
        url?: DriveQrivilegeFluentPlayRequestUploadUrl;
        /**
         * form 默认使用form上传
         */
        form?: DriveQrivilegeFluentPlayRequestUploadForm;
        /**
         * resumable 断点续传类型
         */
        resumable?: DriveQrivilegeFluentPlayRequestUploadResumable;
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 创建文件夹出现重名时返回已存在的文件夹
         */
        ignore_duplicated_name?: boolean;
        /**
         * 文件id
         */
        id?: string;
        /**
         * 创建文件的扩展参数
         * 创建文件请求中， 增加"params": { "require_links": "true" } ，表示需要直接返回播放链接(边添加边播模式)
         * 返回File结构为空或者File中Media信息为空, 都表示不可边添加边播, 具体可以分为:
         *   - 非单视频任务, 单视频不可秒传任务, 不返回File结构
         *   - 审核不通过的任务, 返回File结构, File结构中包含审核结果
         *   - 其它错误, 不返回File结构
         *
         * 流畅播相关参数
         * - from: 标识场景来源是 新建面板流畅播(from="FLUENT_PLAY"), 无此场景不用传
         * - cookie: 浏览器cookie
         *
         * 云添加/流畅播 审核相关参数
         * 当云添加/流畅播的链接来源于网页时，需要传递以下参数，用于审核
         * - web_title: 链接来源页面的标题
         * - referer: 链接来源页面的地址
         * - played: 如果链接在浏览器的网页播的播放器中播放过，则played="1"，否则played="0"
         * - scene: 云添加/流畅播场景，取值如下
         *   - web_play 网页播
         *   - smart_spot_panel 嗅探面板
         *   - browser_url_panel 浏览器新建面板
         *   - turl_panel bt面板
         *   - browser_turl_panel 浏览器bt面板
         *   - clipboard 剪贴板
         *   - url_panel 新建面板
         *   - birdkey_page 口令识别页
         *   - smart_spot 智能识别气泡
         *   - download_list 下载列表
         *   - download_record 下载记录
         *   - qrcode 二维码扫描
         *   - url_player 链接播放
         */
        params?: {
            [name: string]: string;
        };
        /**
         * 文件夹类型
         */
        folder_type?: string;
        /**
         * 是否不创建文件
         *   - 云添加仅提交采集不创建文件
         */
        dry_run?: boolean;
        /**
         * 任务是否需要去重
         *  - "true": 如果任务已存在直接返回任务信息, 否则创建任务
         *  - "false": 不管任务存在与否, 都重新创建任务
         */
        need_dedup?: boolean;
        /**
         * path 文件创建路径
         */
        path?: string;
    }
    /**
     * UploadForm 使用form上传，响应里面会返回header、params、url等
     */
    export interface DriveQrivilegeFluentPlayRequestUploadForm {
    }
    /**
     * UploadResumable 断点上传类型，不同版本会有不同的实现方法
     */
    export interface DriveQrivilegeFluentPlayRequestUploadResumable {
        /**
         * provider 上传提供方
         */
        provider?: DriveProvider;
    }
    /**
     * UploadUrl 链接上传类型
     */
    export interface DriveQrivilegeFluentPlayRequestUploadUrl {
        /**
         * url 上传文件的地址，比如：资源链接
         */
        url?: string;
        /**
         * files 如果链接存在多文件，该变量用于筛选需要上传的文件
         */
        files?: string[];
    }
    /**
     * QrivilegeFluentPlayResponse 特权流畅播响应
     */
    export interface DriveQrivilegeFluentPlayResponse {
        /**
         * upload_type 上传
         */
        upload_type?: DriveUploadType;
        /**
         * form 表单上传类型
         */
        form?: DriveQrivilegeFluentPlayResponseUploadForm;
        /**
         * url 链接上传类型
         */
        url?: DriveQrivilegeFluentPlayResponseUploadUrl;
        /**
         * resumable 断点续传
         */
        resumable?: DriveQrivilegeFluentPlayResponseUploadResumable;
        /**
         * file 创建的文件信息
         */
        file?: DriveFile;
        /**
         * task 该次请求对应的task信息
         */
        task?: DriveTask;
    }
    /**
     * UploadForm 表单上传类型，上传表单需要的参数。
     */
    export interface DriveQrivilegeFluentPlayResponseUploadForm {
        /**
         * kind 固定值
         */
        kind?: string;
        /**
         * url 提交链接
         */
        url?: string;
        /**
         * method form提交http method
         */
        method?: string;
        /**
         * multi_parts 表单附加属性
         */
        multi_parts?: {
            [name: string]: string;
        };
        /**
         * headers 提交请求时需要带上的header
         */
        headers?: {
            [name: string]: string;
        };
    }
    /**
     * UploadResumable 断点续传类型
     */
    export interface DriveQrivilegeFluentPlayResponseUploadResumable {
        kind?: string;
        /**
         * provider 实现类型
         */
        provider?: DriveProvider;
        /**
         * params 客户端上传需要的参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * UploadUrl 链接上传类型
     */
    export interface DriveQrivilegeFluentPlayResponseUploadUrl {
        kind?: string;
    }
    /**
     * Quota 限额
     */
    export interface DriveQuota {
        /**
         * kind 固定值：drive#quota
         */
        kind?: string;
        /**
         * 空间总大小 byte
         */
        limit?: string; // int64
        /**
         * 已经使用大小 byte
         */
        usage?: string; // int64
        /**
         * 回收站使用大小
         */
        usage_in_trash?: string; // int64
        /**
         * 播放次数限额
         */
        play_times_limit?: string; // int64
        /**
         * 已播放次数
         */
        play_times_usage?: string; // int64
        /**
         * 空间容量是否无限制
         */
        is_unlimited?: boolean;
        /**
         * 提示升级的会员类型
         */
        upgrade_type?: string;
    }
    /**
     * （文件列表的）引用事件信息
     */
    export interface DriveReferenceEvent {
        /**
         * kind 固定值：drive#event
         */
        kind?: string;
        /**
         * type 事件类型
         */
        type?: DriveType;
        /**
         * type_name 事件类型描述。前端直接使用该字段展现
         */
        type_name?: string;
        /**
         * source 事件源，比如是上传、离线添加
         */
        source?: string;
        /**
         * subject 事件主题
         */
        subject?: string;
        /**
         * id 事件id
         */
        id?: string;
        /**
         * device 发生该事件的设备
         */
        device?: string;
        /**
         * created_time 事件发生时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        created_time?: string;
        /**
         * icon_url 封面地址
         */
        icon_url?: string;
        /**
         * params 事件内容，json格式
         * play_seconds 客户端上报的播放时间，单位：秒
         * play_duration 客户端上报的视频时长，单位：秒
         * audio_track 客户端上报的音轨索引，索引下标从0开始，0代表第一个音轨，形如："audio_track":"0"
         * subtitle 客户端上报的字幕，内嵌字幕上报索引，索引下标从0开始，0代表第一个内嵌字幕，形如："subtitle":"2"。在线字幕报gcid，形如"subtitle":"30467D13ACA17FFD76086108B2635C88F9D400C0"
         * media_id 客户端上报的文件gcid，值的是取medias里面的media_id字段，形如："media_id":"3D136857102A551559EA90781DBCFC88BE2BC54A"
         */
        params?: {
            [name: string]: string;
        };
        /**
         * updated_time 事件更新时间
         */
        updated_time?: string;
        /**
         * 事件标签
         */
        label?: string;
        /**
         * 进度
         */
        progress?: number; // int32
    }
    /**
     * RefreshSpaceAdminRequest 空间刷新管理接口请求体
     */
    export interface DriveRefreshSpaceAdminRequest {
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 用户id
         */
        user_id?: string;
        /**
         * 刷新空间扩展参数
         * - "zombie": 清理僵尸文件，默认开启
         * - "folder": 刷新文件夹，默认关闭
         */
        flags?: {
            [name: string]: string;
        };
    }
    /**
     * RefreshSpaceAdminResponse 空间刷新管理接口响应体
     */
    export interface DriveRefreshSpaceAdminResponse {
        /**
         * kind 固定值：drive#about
         */
        kind?: string;
        /**
         * 限额信息
         */
        quota?: DriveQuota;
    }
    /**
     * SearchFilesRequest 搜索文件请求
     */
    export interface DriveSearchFilesRequest {
        /**
         * 资源链接
         */
        url?: string;
        /**
         * 目录id
         */
        parent_id?: string;
        /**
         * 查询数量限制
         */
        limit?: number; // int32
        /**
         * 文件夹类型
         */
        folder_type?: string;
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 文件gcid
         */
        gcid?: string;
        /**
         * bt对应的文件索引(bt查询需用url+file_index)
         */
        file_index?: string;
        /**
         * 扩展参数
         * 云添加/流畅播 审核相关参数
         * 当云添加/流畅播的链接来源于网页时，需要传递以下参数，用于审核
         * - web_title: 链接来源页面的标题
         * - referer: 链接来源页面的地址
         * - played: 如果链接在浏览器的网页播的播放器中播放过，则played="1"，否则played="0"
         * - scene: 云添加/流畅播场景，取值如下
         *   - web_play 网页播
         *   - smart_spot_panel 嗅探面板
         *   - browser_url_panel 浏览器新建面板
         *   - turl_panel bt面板
         *   - browser_turl_panel 浏览器bt面板
         *   - clipboard 剪贴板
         *   - url_panel 新建面板
         *   - birdkey_page 口令识别页
         *   - smart_spot 智能识别气泡
         *   - download_list 下载列表
         *   - download_record 下载记录
         *   - qrcode 二维码扫描
         *   - url_player 链接播放
         * - pretake: 预取数据，取值"1"表示需要预取数据，不传或传其他值表示不需要预取数据
         * - x_trace_id: 客户端生成的用于跟踪请求的唯一id
         */
        "params[string]"?: {
            [name: string]: any;
        };
        /**
         * 文件名
         */
        file_name?: string;
        /**
         * 指定要查询的扩展信息
         * **task 查询文件关联的任务信息**
         */
        with?: string[];
    }
    /**
     * SearchFilesResponse 搜索文件响应
     */
    export interface DriveSearchFilesResponse {
        /**
         * 搜索结果
         */
        files?: DriveFile[];
        /**
         * 偏移量
         */
        next_page_token?: string;
        /**
         * 请求参数url不为空时返回该url的资源是否存在
         */
        resource_existed?: boolean;
        /**
         * 不能识别的资源
         */
        unrecognized?: boolean;
    }
    /**
     * StarFilesRequest 对文件加星标请求
     */
    export interface DriveStarFilesRequest {
        /**
         * 文件ID列表，建议一次最多100个
         */
        ids?: string[];
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * StarFilesResponse 对文件加星标返回
     */
    export interface DriveStarFilesResponse {
        /**
         * 星标成功的文件ID列表
         */
        ids?: string[];
    }
    /**
     * StatPrivilegeCodeResponse 邀请码统计响应
     */
    export interface DriveStatPrivilegeCodeResponse {
        /**
         * 下一页页码
         */
        next_page_token?: string;
        /**
         * 邀请码统计
         */
        records?: StatPrivilegeCodeResponseRecord[];
    }
    /**
     * Tag 文件的标签
     */
    export interface DriveTag {
        /**
         * 标签的id
         */
        id?: string;
        /**
         * 标签名
         */
        name?: string;
        /**
         * 标签类型, 0:系统定义的 1:用户定义的
         */
        type?: number; // int32
        /**
         * 标签图标
         */
        icon_link?: string;
    }
    /**
     * Task 任务。比如离线任务
     */
    export interface DriveTask {
        /**
         * kind 状态类型 固定值：drive#task
         */
        kind?: string;
        /**
         * id  任务id
         */
        id?: string;
        /**
         * name 任务名称。例如：xxxx种子离线下载
         */
        name?: string;
        /**
         * type 任务类型。例如，
         * - offline 离线任务
         * - transcoding 转码任务
         */
        type?: string;
        /**
         * user_id 所属用户
         */
        user_id?: string;
        /**
         * statuses 任务状态。如果是BT文件，那么status会有多个（列举bt种子内的文件并创建离线任务）。
         */
        statuses?: DriveTaskStatus[];
        /**
         * status_size 子状态个数
         */
        status_size?: number; // int32
        /**
         * params 任务内容
         */
        params?: {
            [name: string]: string;
        };
        /**
         * file_id 任务对应的文件ID。如果是BT文件，那么task对应的file应该是文件夹。
         */
        file_id?: string;
        /**
         * file_name 任务对应的文件名。
         */
        file_name?: string;
        /**
         * file_size 文件大小
         */
        file_size?: string; // int64
        /**
         * message 任务状态说明，用于客户端展现
         */
        message?: string;
        /**
         * created_time 任务创建时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        created_time?: string;
        /**
         * updated_time 任务完成时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        updated_time?: string;
        /**
         * third_task_id 三方任务ID
         */
        third_task_id?: string;
        /**
         * phase 运行阶段
         */
        phase?: DrivePhaseType;
        /**
         * progress 进度 % 百分比
         */
        progress?: number; // float
        /**
         * icon_link 任务图标
         */
        icon_link?: string;
        /**
         * callback 任务完成之后的通知地址（给用户）。
         * 例如：可以是mqtt的地址、https地址等
         */
        callback?: string;
        /**
         * 引用的资源
         */
        reference_resource?: ProtobufAny;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * Status 状态
     */
    export interface DriveTaskStatus {
        /**
         * kind 固定值：drive#status
         */
        kind?: string;
        /**
         * id 状态id
         */
        id?: string;
        /**
         * file_id 归属的文件id
         */
        file_id?: string;
        /**
         * name 状态名称: 离线下载、解压等
         */
        name?: string;
        /**
         * icon_link 图标
         */
        icon_link?: string;
        /**
         * progress 进度 % 百分比
         */
        progress?: number; // float
        /**
         * file_name bt任务内子文件对应的文件名。
         * 如果包含文件夹，那么file_name的形式是：/foo/bar/name.mov
         */
        file_name?: string;
        /**
         * file_size 文件大小
         */
        file_size?: string; // int64
        /**
         * phase 运行阶段
         */
        phase?: DrivePhaseType;
        /**
         * message 任务状态说明，用于客户端展现。
         * 如果是bt文件，这里就是单个任务的状态信息。
         */
        message?: string;
        /**
         * created_time 状态创建时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        created_time?: string;
        /**
         * updated_time 状态完成时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        updated_time?: string;
        /**
         * params 响应参数
         * 如果是离线任务，那么这里面包含所有子文件信息
         */
        params?: {
            [name: string]: string;
        };
        /**
         * 引用的资源
         */
        reference_resource?: ProtobufAny;
    }
    /**
     * Type 事件类型
     * - TYPE_PLAY: TYPE_PLAY 播放视频或音频
     *  - TYPE_VIEW: TYPE_VIEW 查看文件内容
     *  - TYPE_CREATE: TYPE_UPDATE 创建文件。
     * 可能是离线下载或者解压等动作创建的文件。
     *  - TYPE_UPDATE: TYPE_UPDATE 更新文件
     *  - TYPE_DOWNLOAD: TYPE_DOWNLOAD 下载文件
     *  - TYPE_DELETE: TYPE_DELETE 删除文件
     *  - TYPE_UPLOAD: TYPE_UPLOAD 上传文件
     *  - TYPE_RESTORE: TYPE_RESTORE 转存文件(夹)
     */
    export type DriveType = "TYPE_UNKNOWN" | "TYPE_PLAY" | "TYPE_VIEW" | "TYPE_CREATE" | "TYPE_UPDATE" | "TYPE_DOWNLOAD" | "TYPE_DELETE" | "TYPE_UPLOAD" | "TYPE_RESTORE";
    /**
     * UnstarFilesRequest 取消对文件星标请求
     */
    export interface DriveUnstarFilesRequest {
        /**
         * 文件ID列表，建议一次最多100个
         */
        ids?: string[];
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * UnstarFilesResponse 取消对文件加星标返回
     */
    export interface DriveUnstarFilesResponse {
    }
    /**
     * UpdatePrivilegeCodeResponse 更新邀请码响应
     */
    export interface DriveUpdatePrivilegeCodeResponse {
        /**
         * 邀请码
         */
        code?: DrivePrivilegeCode;
    }
    /**
     * - UPLOAD_TYPE_URL: 使用url上传。
     * 上传请求和响应内url对象不允许为空。
     *  - UPLOAD_TYPE_FORM: 使用form上传。该类型必须是所有端的支持的，因为某些情况下可能会降级为该类型。
     * 上传请求和响应内form对象不允许为空。
     *  - UPLOAD_TYPE_RESUMABLE: 断点续传，需要跟具体云厂商的实现绑定。
     * 上传请求和响应内form对象不允许为空。
     */
    export type DriveUploadType = "UPLOAD_TYPE_UNKNOWN" | "UPLOAD_TYPE_URL" | "UPLOAD_TYPE_FORM" | "UPLOAD_TYPE_RESUMABLE";
    /**
     * UserInfo 用户信息
     */
    export interface DriveUserInfo {
        /**
         * user_id 用户id
         */
        user_id?: string;
        /**
         * portrait_url 用户头像
         */
        portrait_url?: string;
        /**
         * nickname 昵称
         */
        nickname?: string;
        /**
         * avatar 用户头像
         */
        avatar?: string;
    }
    /**
     * Video 视频信息
     */
    export interface DriveVideo {
        /**
         * height 高
         */
        height?: number; // int64
        /**
         * width 宽
         */
        width?: number; // int64
        /**
         * duration 视频时长
         */
        duration?: number; // int64
        /**
         * bit_rate 码率
         */
        bit_rate?: number; // int64
        /**
         * frame_rate 帧率
         */
        frame_rate?: number; // int64
        /**
         * video_codec 编码格式
         */
        video_codec?: string;
        /**
         * audio_codec 音频编码格式
         */
        audio_codec?: string;
        /**
         * video_type 视频类型
         */
        video_type?: string;
        /**
         * hdr_type 视频HDR类型
         */
        hdr_type?: string;
        /**
         * storage_type 存储类型  0:默认存储类型 1:边转边播存储类型
         */
        storage_type?: number; // int32
    }
    /**
     * WordSpell 单词拼写
     */
    export interface DriveWordSpell {
        /**
         * 单词，例如："长度123"
         * 此字段仅用于兼容TV端旧版逻辑，其他端请使用字段：split_word
         */
        word?: string;
        /**
         * 首字母，例如：“长度123” 返回的首字母是["C,Z", "D", "123"]
         */
        acronym?: string[];
        /**
         * 全拼，例如：“长度123”返回的全拼是["CHANG,ZHANG", "DU,DUO", "123"]
         */
        complete?: string[];
        /**
         * 切分后的单词，例如：例如：“长度123”返回的是["长", "度", "123"]
         */
        split_word?: string[];
    }
    /**
     * CopyFileRequest 拷贝文件请求
     */
    export interface FilesCopyFileBody {
        /**
         * to 拷贝目标地址
         */
        to?: DriveCopyFileRequestTo;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * MoveFileRequest 移动文件请求
     */
    export interface FilesMoveFileBody {
        /**
         * to 移动目标地址
         */
        to?: DriveMoveFileRequestTo;
        /**
         * 文件空间
         */
        space?: string;
    }
    export interface GooglerpcStatus {
        code?: number; // int32
        message?: string;
        details?: ProtobufAny[];
    }
    /**
     * `Any` contains an arbitrary serialized protocol buffer message along with a
     * URL that describes the type of the serialized message.
     *
     * Protobuf library provides support to pack/unpack Any values in the form
     * of utility functions or additional generated methods of the Any type.
     *
     * Example 1: Pack and unpack a message in C++.
     *
     *     Foo foo = ...;
     *     Any any;
     *     any.PackFrom(foo);
     *     ...
     *     if (any.UnpackTo(&foo)) {
     *       ...
     *     }
     *
     * Example 2: Pack and unpack a message in Java.
     *
     *     Foo foo = ...;
     *     Any any = Any.pack(foo);
     *     ...
     *     if (any.is(Foo.class)) {
     *       foo = any.unpack(Foo.class);
     *     }
     *
     * Example 3: Pack and unpack a message in Python.
     *
     *     foo = Foo(...)
     *     any = Any()
     *     any.Pack(foo)
     *     ...
     *     if any.Is(Foo.DESCRIPTOR):
     *       any.Unpack(foo)
     *       ...
     *
     * Example 4: Pack and unpack a message in Go
     *
     *      foo := &pb.Foo{...}
     *      any, err := anypb.New(foo)
     *      if err != nil {
     *        ...
     *      }
     *      ...
     *      foo := &pb.Foo{}
     *      if err := any.UnmarshalTo(foo); err != nil {
     *        ...
     *      }
     *
     * The pack methods provided by protobuf library will by default use
     * 'type.googleapis.com/full.type.name' as the type URL and the unpack
     * methods only use the fully qualified type name after the last '/'
     * in the type URL, for example "foo.bar.com/x/y.z" will yield type
     * name "y.z".
     *
     *
     * JSON
     *
     * The JSON representation of an `Any` value uses the regular
     * representation of the deserialized, embedded message, with an
     * additional field `@type` which contains the type URL. Example:
     *
     *     package google.profile;
     *     message Person {
     *       string first_name = 1;
     *       string last_name = 2;
     *     }
     *
     *     {
     *       "@type": "type.googleapis.com/google.profile.Person",
     *       "firstName": <string>,
     *       "lastName": <string>
     *     }
     *
     * If the embedded message type is well-known and has a custom JSON
     * representation, that representation will be embedded adding a field
     * `value` which holds the custom JSON in addition to the `@type`
     * field. Example (for message [google.protobuf.Duration][]):
     *
     *     {
     *       "@type": "type.googleapis.com/google.protobuf.Duration",
     *       "value": "1.212s"
     *     }
     */
    export interface ProtobufAny {
        [name: string]: any;
        /**
         * A URL/resource name that uniquely identifies the type of the serialized
         * protocol buffer message. This string must contain at least
         * one "/" character. The last segment of the URL's path must represent
         * the fully qualified name of the type (as in
         * `path/google.protobuf.Duration`). The name should be in a canonical form
         * (e.g., leading "." is not accepted).
         *
         * In practice, teams usually precompile into the binary all types that they
         * expect it to use in the context of Any. However, for URLs which use the
         * scheme `http`, `https`, or no scheme, one can optionally set up a type
         * server that maps type URLs to message definitions as follows:
         *
         * * If no scheme is provided, `https` is assumed.
         * * An HTTP GET on the URL must yield a [google.protobuf.Type][]
         *   value in binary format, or produce an error.
         * * Applications are allowed to cache lookup results based on the
         *   URL, or have them precompiled into a binary to avoid any
         *   lookup. Therefore, binary compatibility needs to be preserved
         *   on changes to types. (Use versioned type names to manage
         *   breaking changes.)
         *
         * Note: this functionality is not currently available in the official
         * protobuf release, and it is not used for type URLs beginning with
         * type.googleapis.com.
         *
         * Schemes other than `http`, `https` (or the empty scheme) might be
         * used with implementation specific semantics.
         */
        "@type"?: string;
    }
    /**
     * `NullValue` is a singleton enumeration to represent the null value for the
     * `Value` type union.
     *
     *  The JSON representation for `NullValue` is JSON `null`.
     *
     *  - NULL_VALUE: Null value.
     */
    export type ProtobufNullValue = "NULL_VALUE";
    /**
     * - ORDERBY_DEFAULT: 默认排序
     *  - ORDERBY_USE_TIMES_ASC: 按使用量升序
     *  - ORDERBY_USE_TIMES_DESC: 按使用量降序
     */
    export type StatPrivilegeCodeRequestOrderby = "ORDERBY_DEFAULT" | "ORDERBY_USE_TIMES_ASC" | "ORDERBY_USE_TIMES_DESC";
    /**
     * 使用统计记录
     */
    export interface StatPrivilegeCodeResponseRecord {
        /**
         * 日期
         */
        date?: string;
        /**
         * 使用次数
         */
        used_times?: number; // int32
    }
}
