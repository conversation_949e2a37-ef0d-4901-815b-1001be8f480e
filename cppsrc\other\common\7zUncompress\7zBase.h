#include <stdio.h>
#include <string.h>

#include "C/7z.h"
#include "C/7zAlloc.h"
#include "C/7zCrc.h"
#include "C/7zFile.h"
#include "C/7zVersion.h"
#include "7zMem.h"

#ifndef USE_WINDOWS_FILE
/* for mkdir */
#ifdef _WIN32
#include <direct.h>
#else
#include <sys/stat.h>
#include <errno.h>
#endif
#endif

#ifdef __cplusplus
extern "C" {  
#endif

int Buf_EnsureSize(CBuf *dest, size_t size);
#ifndef _WIN32
Bool Utf16_To_Utf8(Byte *dest, size_t *destLen, const UInt16 *src, size_t srcLen);
SRes Utf16_To_Utf8Buf(CBuf *dest, const UInt16 *src, size_t srcLen);
#endif
SRes Utf16_To_Char(CBuf *buf, const UInt16 *s, int fileMode);
WRes MyCreateDir(const UInt16 *name);
WRes OutFile_OpenUtf16(CSzFile *p, const UInt16 *name);
SRes PrintString(const UInt16 *s);
void UInt64ToStr(UInt64 value, char *s);
char *UIntToStr(char *s, unsigned value, int numDigits);
void ConvertFileTimeToString(const CNtfsFileTime *ft, char *s);
void PrintError(char *sz);
#ifdef USE_WINDOWS_FILE
void GetAttribString(UInt32 wa, Bool isDir, char *s);
#else
void GetAttribString(UInt32, Bool, char *s);
#endif

#ifdef __cplusplus
}
#endif