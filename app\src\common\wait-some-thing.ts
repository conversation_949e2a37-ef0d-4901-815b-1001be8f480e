export class WaitSomeThing {
    private bDone: boolean = false;
    private cbs: any[] = [];

    public async waitDone(): Promise<void> {
        if (!this.bDone) {
            await new Promise((v)=> {
                this.cbs.push(()=> {
                    v(true);
                });
            })
        }
    }

    public done(): void {
        this.bDone = true;
        let temp = this.cbs;
        this.cbs = [];
        temp.forEach((cb) => {
            cb();
        });
    }

    public isDone(): boolean {
        return this.bDone;
    }

    public reset(): void {
        this.bDone = false;
    }
}