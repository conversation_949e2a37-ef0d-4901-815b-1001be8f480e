const fs = require('fs')
const path = require('path')
const axios = require('axios')
const md5 = require('blueimp-md5')

const getPubKeyTimeStamp = parseInt(Date.now() / 1000)

// windows xbase 配置信息
const xbaseConfigWin = {
  algVersion: '1',
  clientId: 'XW-G4v1H72tgfJym',
  packageName: 'ThunderPanPlugin',
  signKey: 'n&Ng)QE3EZ'
}

// MacOS xbase 配置信息
const xbaseConfigMac = {
  algVersion: '1',
  clientId: 'Y_Rjlpdf8tXHrP6u',
  packageName: 'ThunderPanPlugin',
  signKey: 'ff22e3635b'
}

function getXbaseSDKConfig (type) {
  switch (type) {
    case 'win':
      return xbaseConfigWin;
    case 'mac':
      return xbaseConfigMac;
    default:
      return xbaseConfigWin;
  }
}

function getPubKeys (type, appVersion) {
  const xbaseConfig = getXbaseSDKConfig(type);

  const clientId = xbaseConfig.clientId;
  const packageName = xbaseConfig.packageName;
  const algVersion = xbaseConfig.algVersion;
  const signKey = xbaseConfig.signKey;
  const signStr = `${clientId}${appVersion}${packageName}${getPubKeyTimeStamp}${algVersion}${signKey}`;
  const sign = md5(signStr);
  // 请求参数
  let query = `client_id=${clientId}&client_version=${appVersion}&package_name=${packageName}`;
  query += `&timestamp=${getPubKeyTimeStamp}&alg_version=${algVersion}&sign=${sign}`;
  // 请求链接
  const url = `http://risk-account-ssl.office.k8s.xunlei.cn/amaze/v1.0/algorithms?${query}`;

  console.log('[panScript] getPubKeys request url', url);
  return axios.get(url)
    .then(res => {
      const algorithms = res.data.algorithms;
      console.log('[panScript] getPubKeys response', algorithms);
      return algorithms;
    })
    .catch(err => {
      const errData = err.response.data;
      console.log('[panScript] getPubKeys error', errData);
      return Promise.reject(errData)
    })
}

function runScript (appVersion, targetPath) {
  try {
    if (!targetPath) {
      targetPath = path.join(process.cwd(), 'src/common/thunder-pan-manager/common/pub-keys.ts');
    }
    if (!appVersion) {
      const pkgContent = fs.readFileSync(path.join(process.cwd(), 'package.json'), 'utf-8');
      const pkg = JSON.parse(pkgContent);
      appVersion = pkg.version;
    }

    fs.chmodSync(targetPath, '0755');

    const getAllPubKeys = Promise.all([
      getPubKeys('win', appVersion),
      getPubKeys('mac', appVersion),
    ]);

    getAllPubKeys.then(res => {
      const WinPubKeys = JSON.stringify(res[0], null, '\t');
      const MacPubKeys = JSON.stringify(res[1], null, '\t');

      const pubKeysContent = `/**
 * Automatic Generate
 * 自动生成不需要手动修改
 */

interface IPubKey {
  alg: string
  salt: string
}

const WinPubKeys: IPubKey[] = ${WinPubKeys};
const MacPubKeys: IPubKey[] = ${MacPubKeys};

export function getPubKeys () {
  return process.platform === 'darwin' ? MacPubKeys : WinPubKeys;
}
`;

      fs.writeFileSync(targetPath, pubKeysContent, 'utf-8');
    });
  } catch (error) {
    console.log('[panScript] runScript error', error)
    process.exit(1)
  }
}

runScript('*******');
