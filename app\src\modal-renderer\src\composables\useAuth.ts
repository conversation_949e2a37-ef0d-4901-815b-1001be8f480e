import { useUserStore } from '@root/modal-renderer/src/stores/user'
import { PopUpNS } from '@root/common/pop-up'

/**
 * 认证相关的组合式函数
 * 封装登录状态检查、登录弹窗显示等逻辑
 * 
 * @example
 * ```typescript
 * // 在组件中使用
 * import { useAuth } from '@root/modal-renderer/src/composables/useAuth'
 * import { useUserStore } from '@root/modal-renderer/src/stores/user'
 * 
 * const { requireAuth, initializeAuth, cleanupAuth, showLoginDialog } = useAuth()
 * const userStore = useUserStore()
 * 
 * // 新增：标记是否在组件中显示了登录弹窗
 * const hasShownLoginDialog = ref(false)
 * 
 * onMounted(async () => {
 *   // 初始化认证状态
 *   await initializeAuth()
 *   
 *   // 检查登录状态，如果未登录则显示登录弹窗
 *   const isLoggedIn = await requireAuth()
 *   if (!isLoggedIn) {
 *     console.log('用户未登录，弹出登录弹窗')
 *     // 标记已经在组件中显示了登录弹窗
 *     hasShownLoginDialog.value = true
 *     showLoginDialog()
 *     // 注意：这里不需要等待，因为登录弹窗是异步的
 *     // 用户登录成功后，会通过事件监听器自动更新状态
 *     return
 *   }
 *   
 *   console.log('用户已登录，继续初始化')
 *   // 继续组件的初始化逻辑
 *   initializeComponentAfterLogin()
 * })
 * 
 * // 监听登录状态变化，区分两种场景
 * watch(() => userStore.isLogged, (newStatus, oldStatus) => {
 *   if (newStatus && !oldStatus) {
 *     // 登录状态从 false 变为 true，说明用户刚刚登录成功
 *     if (hasShownLoginDialog.value) {
 *       // 场景2：在组件中显示了登录弹窗后登录成功
 *       console.log('场景2：用户在组件中登录成功，继续初始化')
 *       hasShownLoginDialog.value = false // 重置标志
 *       initializeComponentAfterLogin()
 *     } else {
 *       // 场景1：打开组件时用户已经登录
 *       console.log('场景1：用户打开组件时已经登录')
 *       // 这种情况在 onMounted 中已经处理了，这里不需要重复处理
 *     }
 *   }
 * })
 * 
 * function initializeComponentAfterLogin() {
 *   // 在这里添加需要在用户登录后执行的初始化逻辑
 *   // 例如：加载用户数据、初始化任务列表等
 * }
 * 
 * onUnmounted(() => {
 *   // 清理认证相关资源
 *   cleanupAuth()
 * })
 * ```
 * 
 * @description
 * 这个组合式函数基于 useUserStore 实现，提供了以下功能：
 * 1. 自动检查用户登录状态
 * 2. 未登录时自动显示登录弹窗
 * 3. 手动弹出登录弹窗
 * 4. 监听用户登录/退出事件
 * 5. 用户退出时自动关闭所有弹窗
 * 6. 提供资源清理功能
 * 
 * 注意：要监听用户登录成功事件，建议直接使用 userStore.isLogged：
 * ```typescript
 * import { useUserStore } from '@root/modal-renderer/src/stores/user'
 * const userStore = useUserStore()
 * 
 * // 区分两种登录场景：
 * // 1. 打开组件时用户已经登录
 * // 2. 在组件中显示登录弹窗后用户才登录成功
 * const hasShownLoginDialog = ref(false)
 * 
 * watch(() => userStore.isLogged, (newStatus, oldStatus) => {
 *   if (newStatus && !oldStatus) {
 *     if (hasShownLoginDialog.value) {
 *       console.log('场景2：用户在组件中登录成功')
 *       hasShownLoginDialog.value = false
 *       // 继续初始化逻辑
 *     } else {
 *       console.log('场景1：用户打开组件时已经登录')
 *       // 这种情况在 onMounted 中已经处理了
 *     }
 *   }
 * })
 * ```
 */
export function useAuth() {
  const userStore = useUserStore()

  /**
   * 检查登录状态，如果未登录则显示登录弹窗
   * @returns Promise<boolean> 返回是否已登录
   */
  const requireAuth = async (): Promise<boolean> => {
    const isLoggedIn = await userStore.getLoginStatus()
    
    if (!isLoggedIn) {
      console.log('[useAuth] 用户未登录，显示登录弹窗')
      // 显示登录弹窗
      PopUpNS.showLoginDlg()
      return false
    }
    
    console.log('[useAuth] 用户已登录')
    return true
  }

  /**
   * 弹出登录弹窗
   * @description 手动触发登录弹窗显示
   */
  const showLoginDialog = (): void => {
    console.log('[useAuth] 手动弹出登录弹窗')
    PopUpNS.showLoginDlg()
  }

  /**
   * 用户登出
   * @description 调用用户store的登出方法，并关闭所有弹窗
   */
  const logout = (): void => {
    console.log('[useAuth] 用户登出')
    userStore.onUserSignOut()
    // 关闭所有弹窗的逻辑在 userStore 的 closeAllDialogs 中处理
  }

  /**
   * 初始化认证状态
   * @description 初始化用户状态和事件监听
   * @returns Promise<void>
   */
  const initializeAuth = async (): Promise<void> => {
    console.log('[useAuth] 初始化认证状态')
    await userStore.initializeUserState()
  }

  /**
   * 清理认证相关资源
   * @description 清理事件监听器等资源
   */
  const cleanupAuth = (): void => {
    console.log('[useAuth] 清理认证资源')
    userStore.cleanupEventListeners()
  }

  return {
    // 状态
    isLogged: userStore.isLogged,
    isCheckingLoginStatus: userStore.isCheckingLoginStatus,
    
    // 方法
    requireAuth,
    showLoginDialog,
    logout,
    initializeAuth,
    cleanupAuth,
    
    // 直接暴露 userStore 的方法，方便使用
    getLoginStatus: userStore.getLoginStatus,
    forceRefreshLoginStatus: userStore.forceRefreshLoginStatus,
    onUserSignIn: userStore.onUserSignIn,
    onUserSignOut: userStore.onUserSignOut,
  }
} 