import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { API_FILE, IExtendDriveFile } from '@root/common/thunder-pan-manager/pan-sdk/types'
import { SortFunctionMapType, SortOrderType } from '@root/common/thunder-pan-manager/pan-sdk/utils/file'
import { reactive } from 'vue'
import { UserHelper } from '../utils/user-helper'
import { FilterManager } from './filter-manager'

export interface TypeDriveSortInfo {
  type: SortFunctionMapType
  order: SortOrderType
}

export interface IGetFileListOptions {
  forceSync?: boolean
  silent?: boolean
}

export interface IGetCurrentFileListOptions {
  reset?: boolean
}

export type TFileHeaderSortType = 'name' | 'size' | 'time'

export interface IFileStoreCurrentData {
  isFetching: boolean
  fileList: IExtendDriveFile[]
  fileListPageToken: string
  fileListFolderType: string
  pickedIds: string[]
  sortInfo: TypeDriveSortInfo
}

function genDefaultCurrentData (): IFileStoreCurrentData {
  return {
    isFetching: false,
    fileList: [],
    fileListPageToken: '',
    fileListFolderType: 'FOLDER_TYPE',
    pickedIds: [],
    sortInfo: {
      type: 'modified_time',
      order: SortOrderType.DESC
    },
  }
}

/**
 * 云盘全部文件管理器
 */
export class DriveFileManager {
  private static _instance: DriveFileManager

  static getInstance () {
    if (DriveFileManager._instance) {
      return DriveFileManager._instance
    } else {
      DriveFileManager._instance = new DriveFileManager()
      return DriveFileManager._instance
    }
  }

  private current = reactive<IFileStoreCurrentData>(genDefaultCurrentData());
  private sortInfoMap: Map<string, TypeDriveSortInfo> = new Map()
  // 需要被过滤的文件（主要是从其他地方跳转云盘后，当前列表不存的文件，需要动态插入一个，在后续拉文件的过程中需要过滤掉）
  private filterFile: API_FILE.DriveFile | undefined = undefined

  reset () {
    this.current = reactive<IFileStoreCurrentData>(genDefaultCurrentData())
  }

  getCurrentData () {
    return this.current
  }

  getSortInfoByKey (key: string) {
    const sortInfo = this.sortInfoMap.get(key) || {
      type: 'modified_time',
      order: SortOrderType.DESC
    }
    this.current.sortInfo = sortInfo

    return sortInfo
  }

  updateSortInfoByKey (key: string, sortInfo: TypeDriveSortInfo) {
    this.sortInfoMap.delete(key)
    this.sortInfoMap.set(key, sortInfo)
  }

  private _getSortOrder (key: string) {
    const currentSortInfo = this.getSortInfoByKey(key)

    switch (currentSortInfo.type) {
      case 'name': {
        return currentSortInfo.order === SortOrderType.DESC ? 'NAME_DESC' : 'NAME_ASC'
      }
      case 'size': {
        return currentSortInfo.order === SortOrderType.DESC ? 'SIZE_DESC' : 'SIZE_ASC'
      }
      case 'modified_time': {
        return currentSortInfo.order === SortOrderType.DESC ? 'MODIFY_TIME_DESC' : 'MODIFY_TIME_ASC'
      }
      default: {
        return 'MODIFY_TIME_DESC'
      }
    }
  }

  async getCurrentFileList (parentId: string = '', options: IGetCurrentFileListOptions = {}) {
    if (this.current.isFetching) return
    // 重置数据
    if (options.reset) {
      this.cleanFilterFile()
      this.current.fileList = []
      this.current.fileListPageToken = ''
      this.current.fileListFolderType = 'FOLDER_TYPE'
    }
    this.current.isFetching = true

    try {
      await UserHelper.waitUserSignin()
      const key = `all_${parentId}`

      const customFilter = FilterManager.getInstance().getCurrentFilterForService(key)
      const res = await ThunderPanClientSDK.getInstance().getFiles({
        params: {
          parent_id: parentId,
          page_token: this.current.fileListPageToken,
          order: this._getSortOrder(key),
          folder_type: this.current.fileListFolderType,
          filters: {
            phase: {
              eq: "PHASE_TYPE_COMPLETE"
            },
            trashed: {
              eq: false
            },
            ...customFilter,
          }
        }
      })

      if (res.success && res.data && res.data.files) {
        let files = res.data.files
        // 过滤动态添加的文件
        if (this.filterFile) {
          files = files.filter(file => { return this.filterFile?.id !== file.id })
        }

        this.current.fileList.push(...files)
        this.current.fileListPageToken = res.data.next_page_token ?? ''
        this.current.fileListFolderType = res.data.folder_type ?? 'FOLDER_TYPE'
      }
    } catch (err) {

    } finally {
      this.current.isFetching = false
    }
  }

  appendFiles (files: API_FILE.DriveFile[]) {
    this.current.fileList.unshift(...files)
  }

  updateFile (newFileData: API_FILE.DriveFile) {
    this.current.fileList.forEach(file => {
      if (file.id === newFileData.id) {
        Object.assign(file, newFileData)
      }
    })
  }

  batchDeleteFile (ids: string[]) {
    this.current.fileList = this.current.fileList.filter(file => { return !ids.includes(file.id!) })
  }

  cleanCurrentPageToken () {
    this.current.fileListPageToken = ''
  }

  cleanPicked () {
    this.current.pickedIds = []
  }

  setPickedIds (ids: string[]) {
    this.current.pickedIds = ids
  }

  togglePickedId (fileId: string, forceCurrent?: boolean) {
    // 强制选中当前指定项
    if (forceCurrent) {
      this.current.pickedIds = [fileId]
      return
    }

    if (!this.current.pickedIds.includes(fileId)) {
      this.current.pickedIds.push(fileId)
    } else {
      const index = this.current.pickedIds.indexOf(fileId)
      if (index > -1) {
        this.current.pickedIds.splice(index, 1)
      }
    }
  }

  // 设置需要过滤的文件（主要是从其他地方跳转云盘后，当前列表不存的文件，需要动态插入一个，在后续拉文件的过程中需要过滤掉）
  setFilterFile (file: API_FILE.DriveFile) {
    this.filterFile = file
  }

  cleanFilterFile () {
    this.filterFile = undefined
  }
}
