import * as path from 'path';
import { B<PERSON>erWindow, Rectangle } from 'electron';
import { Uflag, CmdShow, WindowMessage, GWCmd } from '@root/common/window-define';
import { ThunderHelper } from '@root/common/thunder-helper';

const shadowWindows: Map<number, ShadowWindow> = new Map();

type CRect = { left: number; top: number; right: number; bottom: number };

function getDpiFactor(wnd: BrowserWindow): number;
function getDpiFactor(wnd: number): number;
function getDpiFactor(wnd: BrowserWindow | number): number {
  let dpiFactor: number = 1.0;
  if (ThunderHelper.getDPIAwareSupport()) {
    if (typeof wnd === 'number') {
      dpiFactor = ThunderHelper.getMonitorDPIFactor(wnd);
    } else {
      const hwnd: number = wnd.getNativeWindowHandle()?.readUIntLE(0, 4);
      dpiFactor = ThunderHelper.getMonitorDPIFactor(hwnd);
    }
  } else {
    dpiFactor = ThunderHelper.getSysDPIFactor();
  }
  return dpiFactor;
}

class ShadowWindow {
  private win: BrowserWindow | null = null;
  private winId: number | undefined = undefined; // 记录窗口id，防止窗口销毁id无法获取
  private winHandle: number = 0; // 记录窗口句柄，防止窗口销毁阴影窗口的映射无法解除
  private shadowHandle: number = 0;
  private area: number = 16;

  private closeHandler: ((event: Event) => void) | null = null;
  private resizeHandler: Function | null = null;
  private moveHandler: Function | null = null;
  private focusHandler: Function | null = null;
  private blurHandler: Function | null = null;
  private showHandler: Function | null = null;
  private hideHandler: Function | null = null;

  private getParentWindowRect(): Rectangle | null {
    const border: number = this.area / 2;
    const mainRect: Rectangle = ThunderHelper.getWindowRect(this.win!);
    if (!mainRect) {
      return null;
    }
    const dpi: number = getDpiFactor(this.win!);

    const rect: Rectangle = { x: 0, y: 0, width: 0, height: 0 };
    rect.x = Math.floor(mainRect.x - border * dpi);
    rect.y = Math.floor(mainRect.y - border * dpi);
    rect.width = Math.floor(mainRect.width + border * 2 * dpi);
    rect.height = Math.floor(mainRect.height + border * 2 * dpi);

    return rect;
  }

  private updateShadowWindowPos(redraw?: boolean): void {
    const parentRect: Rectangle | null = this.getParentWindowRect();
    const oldShadowRect: Rectangle = ThunderHelper.getWindowRect(this.shadowHandle);
    if (!parentRect || !oldShadowRect) {
      return;
    }
    if (
      oldShadowRect.x !== parentRect.x ||
      oldShadowRect.y !== parentRect.y ||
      oldShadowRect.width !== parentRect.width ||
      oldShadowRect.height !== parentRect.height
    ) {
      ThunderHelper.setWindowPos(
        this.shadowHandle,
        this.win!,
        parentRect.x,
        parentRect.y,
        parentRect.width,
        parentRect.height,
        Uflag.SWP_NOACTIVATE
      );
      redraw = true;
    } else {
      ThunderHelper.setWindowPos(
        this.shadowHandle,
        this.win!,
        0,
        0,
        0,
        0,
        Uflag.SWP_NOSIZE |
          Uflag.SWP_NOMOVE |
          Uflag.SWP_NOACTIVATE |
          Uflag.SWP_NOSENDCHANGING |
          Uflag.SWP_NOOWNERZORDER |
          Uflag.SWP_NOREDRAW
      );
    }
    if (redraw) {
      const dpi: number = getDpiFactor(this.win!);
      // logger.information('drawShadowWindow dpi', dpi);
      ThunderHelper.drawShadowWindow(this.win!, dpi * 100);
    }
  }

  private onWindowClose(): void {
    detachShadowWindow(this.winId!);
    this.winId = undefined;
    this.winHandle = 0;
  }

  private onWindowResize(): void {
    this.updateShadowWindowPos(true);
  }

  private onWindowMove(): void {
    this.updateShadowWindowPos();
  }

  private onWindowFocus(): void {
    this.updateShadowWindowPos();
  }

  private onWindowBlur(): void {
    this.updateShadowWindowPos();
  }

  private onWindowShow(): void {
    this.updateShadowWindowPos();
    ThunderHelper.showWindow(this.shadowHandle, CmdShow.SW_SHOWNOACTIVATE);
  }

  private onWindowHide(): void {
    ThunderHelper.hideWindow(this.shadowHandle);
  }

  private onWindowPosChanged(): void {
    if (this.shadowHandle && this.win && !this.win.isDestroyed() && this.win.isVisible()) {
      const prev: number = ThunderHelper.getNextWindow(this.shadowHandle, GWCmd.GW_HWNDPREV)!;
      if (prev !== this.winHandle) {
        this.updateShadowWindowPos();
      }
    }
  }

  public attach(
    win: BrowserWindow,
    parent: BrowserWindow | null,
    replaceWndProc: boolean,
    roundRect: boolean,
    area: number,
    bkgImage: string,
    corner?: CRect
  ): number {
    do {
      if (!win || win.isDestroyed()) {
        break;
      }
      this.win = win;
      this.winId = win.id;
      this.winHandle = win.getNativeWindowHandle().readUIntLE(0, 4);
      const dpi: number = getDpiFactor(this.win!);
      this.shadowHandle = ThunderHelper.attachShadowWindow(
        this.win!,
        parent,
        {
          shadow_size: area,
          replace_wndproc: replaceWndProc,
          round_rect: roundRect,
          dpi_factor: dpi * 100,
          image: bkgImage,
          rect: corner,
        }
      );
      if (!this.shadowHandle) {
        break;
      }

      ThunderHelper.setShadowWindowResizable(this.win!, win.isResizable());

      this.closeHandler = this.onWindowClose.bind(this);
      win.on('closed', this.closeHandler);
      if (replaceWndProc) {
        // 替换窗口过程不需要走下面逻辑
        break;
      }

      this.updateShadowWindowPos(true);

      if (!win.isVisible()) {
        ThunderHelper.hideWindow(this.shadowHandle);
      }
      this.resizeHandler = this.onWindowResize.bind(this);
      this.moveHandler = this.onWindowMove.bind(this);
      this.focusHandler = this.onWindowFocus.bind(this);
      this.blurHandler = this.onWindowBlur.bind(this);
      this.showHandler = this.onWindowShow.bind(this);
      this.hideHandler = this.onWindowHide.bind(this);

      win.on('resize', this.resizeHandler);
      win.on('move', this.moveHandler);
      win.on('focus', this.focusHandler);
      win.on('blur', this.blurHandler);
      win.on('show', this.showHandler);
      win.on('hide', this.hideHandler);

      win.hookWindowMessage(WindowMessage.WM_WINDOWPOSCHANGED, this.onWindowPosChanged.bind(this));
    } while (false);
    return this.shadowHandle;
  }

  public detach(): void {
    // logger.information('detach win.id:', this.winId);
    if (this.win && !this.win.isDestroyed()) {
      if (this.closeHandler) {
        this.win.removeListener('close', this.closeHandler);
        this.closeHandler = null;
      }
      if (this.resizeHandler) {
        this.win.removeListener('resize', this.resizeHandler);
        this.resizeHandler = null;
      }
      if (this.moveHandler) {
        this.win.removeListener('move', this.moveHandler);
        this.moveHandler = null;
      }
      if (this.focusHandler) {
        this.win.removeListener('focus', this.focusHandler);
        this.focusHandler = null;
      }
      if (this.blurHandler) {
        this.win.removeListener('blur', this.blurHandler);
        this.blurHandler = null;
      }
      if (this.showHandler) {
        this.win.removeListener('show', this.showHandler);
        this.showHandler = null;
      }
      if (this.hideHandler) {
        this.win.removeListener('hide', this.hideHandler);
        this.hideHandler = null;
      }
      this.win.unhookWindowMessage(WindowMessage.WM_WINDOWPOSCHANGED);
    }
    ThunderHelper.destroyShadowWindow(this.winHandle);
  }
}

function doAttchShadowWindow(
  win: BrowserWindow,
  parent: BrowserWindow | null,
  replaceWndProc: boolean = true,
  roundRect: boolean = true,
  area: number = 16,
  bkgImage: string = '',
  corner?: CRect
): number {
  let ret: number = 0;
  do {
    if (!win || win.isDestroyed()) {
      // logger.warning('win is already destroyed!');
      break;
    }
    let shadowWindow: ShadowWindow | undefined = shadowWindows.get(win.id);
    if (shadowWindow) {
      // logger.warning('shadowWindow is already attached! win id:', win.id);
      break;
    }
    shadowWindow = new ShadowWindow();
    shadowWindows.set(win.id, shadowWindow);
    ret = shadowWindow.attach(win, parent, replaceWndProc, roundRect, area, bkgImage, corner);
  } while (false);
  return ret;
}

export async function attachShadowWindow(
  win: BrowserWindow,
  parent?: BrowserWindow | null,
  replaceWndProc: boolean = true,
  roundRect: boolean = true,
  area: number = 16
): Promise<void> {
  setTimeout(() => {
    do {
      if (win.isDestroyed()) {
        break;
      }
      const iconDir = path.join(process.execPath, '../icon')
      const bkgImage = roundRect ? path.join(iconDir, 'shadow-corner.png') : path.join(iconDir, 'shadow.png')
      const corner: CRect = {
        left: 17,
        top: 17,
        right: 17,
        bottom: 17
      };
      doAttchShadowWindow(win, parent as BrowserWindow | null, replaceWndProc, roundRect, area, bkgImage, corner);
    } while (0);
  }, 50);
}

export function detachShadowWindow(winId: number): void {
  do {
    const shadowWindow: ShadowWindow = shadowWindows.get(winId)!;
    if (!shadowWindow) {
      break;
    }
    shadowWindow.detach();
    shadowWindows.delete(winId);
  } while (false);
}

export function detachAllShadowWindow(): void {
  shadowWindows.forEach((shadowWindow: ShadowWindow) => {
    shadowWindow.detach();
  });
  shadowWindows.clear();
}

export function escapeCloseFlicker(win: BrowserWindow): void {
  do {
    if (!win) {
      break;
    }

    const parentWindow: BrowserWindow = win.getParentWindow()!;
    if (!parentWindow) {
      break;
    }

    win.once('close', () => {
      if (!parentWindow.isDestroyed()) {
        parentWindow.focus();
      }
    });
  } while (0);
}
