import { ref, watch } from 'vue'
import { AccountHelper } from '@root/common/account/impl/accountHelper'
import { ApplicationManager } from '@root/common/thunder-pan-manager/manager/application'
import { AccountHelperEventKey, UserInfo } from '@root/common/account/account-type'
import { IResponseHistory, IHistoryTask, IResponseUpdateHistory, IResult } from './type'
import { env } from '@root/common/env'
import { requestFn, getMd5, changeListParams, getRandomNum, concatSearchParamsToUrl, hexMD5 } from './utils'


export const downloadHistory = () => {

  const limit = 15
  const beginHistory = ref(0)
  const beginCollection = ref(0)
  const deviceId = ref('')
  const userId = ref('')
  const appVersionCode = ref('')
  const historyList = ref<IHistoryTask[]>([])
  const collectionList = ref<IHistoryTask[]>([])
  const loading = ref(false)
  const collectionTotal = ref(0)
  const historyTotal = ref(0)
  const session = ref('')

  const baseUrl = {
    prod: 'https://api-zone-lixian-vip-ssl.xunlei.com',
    test: 'https://xllixian.office.k8s.xunlei.cn'
  }[env]

  AccountHelper.getInstance().attachEvent(AccountHelperEventKey.USER_INFO_CHANGE, userInfoAlter)

  async function initData () {
    const userInfo = await AccountHelper.getInstance().getUserInfo()
    userId.value = userInfo?.sub ?? ''
    deviceId.value = await AccountHelper.getInstance().getDeviceID()
    // TODO: versionCode
    appVersionCode.value = ApplicationManager.getCurrentDeviceClientVersion()
    session.value = (await AccountHelper.getInstance().getOldAccountSessionInfo()).sessionId || ''
    // 初始化数据
    beginHistory.value = 0
    beginCollection.value = 0
    historyList.value = []
    collectionList.value = []
    loading.value = false
    collectionTotal.value = 0
    historyTotal.value = 0

    await getHistory()
    // await getCollection()
  }

  function unInitData () {
    console.log('>>>>> 解绑事件')
    AccountHelper.getInstance().detachEvent(AccountHelperEventKey.USER_INFO_CHANGE, userInfoAlter)
  }

  function userInfoAlter (info) {
    console.log('>>>>>>>>>>>>>> info', info)
    initData()
  }

  async function request (options) {
    try {
      return await requestFn(options, userId.value, session.value)
    } catch (error) {
      console.log('>>>>>>>>>>>> error', error)
    }
    return null
  }

  function initCollectionList () {
    beginCollection.value = 0
    collectionList.value = []
    collectionTotal.value = 0
    getCollection()
  }

  /** 获取历史收藏数据 */
  async function getCollection () {
    const common = getCommonData('GET')
    const data = {
      begin: beginCollection.value,
      num: limit,
      filetype: 0,
      folder_id: 0,
      ...common
    }
    console.log('> params', data)
    loading.value = true
    const res = await request({
      method: 'GET',
      url: `${baseUrl}/collections/${userId.value}`,
      data,
    }) as IResponseHistory
    console.log('>>>>>>>>>> getCollection', res)
    if (res && res.result === 0) {
      const list = changeListParams(res?.task_info ?? [])
      console.log('>>>>>>>>>> list', list)
      if (!beginCollection.value) {
        collectionList.value = list
      } else {
        collectionList.value.push(...list)
      }
      beginCollection.value = collectionList.value.length
      collectionTotal.value = res.total
    }
    loading.value = false
  }

  /** 获取历史数据 */
  async function getHistory () {
    const common = getCommonData('GET')
    const data = {
      begin: beginHistory.value,
      num: limit,
      filetype: 0,
      folder_id: 0,
      ...common
    }
    console.log('> params', data)
    loading.value = true
    const res = await request({
      method: 'GET',
      url: `${baseUrl}/tasks/${userId.value}`,
      data,
    }) as IResponseHistory
    console.log('>>>>>>>>>> getHistory', res)
    if (res && res.result === 0) {
      const list = changeListParams(res?.task_info ?? [])
      if (!beginHistory.value) {
        historyList.value = list
      } else {
        historyList.value.push(...list)
      }
      beginHistory.value = historyList.value.length
      historyTotal.value = res.total
    }
    console.log('>>>>>>>>>>>>> historyList.value', historyList.value, beginHistory.value)
    loading.value = false
  }

  function updateHistoryList (list: IHistoryTask[]) {
    list.forEach(item => {
      const index = historyList.value.findIndex(i => i.task_id === item.task_id)
      console.log('>>>>>>>>>>>>>> index', index)

      if (index !== -1) {
        historyList.value[index].collection_id = item.collection_id
        console.log('> historyList.value[index]', historyList.value[index]) 
      }
    })
  }

  /** 收藏资源 */
  async function collectedFiles (data:{taskIds: number[], folder_id?: number, record_type?: number}):Promise<IResult> {
    let { taskIds, folder_id=10000, record_type=0 } = data
    if (!Array.isArray(taskIds)) {
      taskIds = [taskIds]
    }
    const taskInfos = taskIds.map((taskId) => {
      return {
        task_id: taskId,
        folder_id: folder_id,
        record_type: record_type
      }
    })
    const common = getCommonData('POST')
    const url = `${baseUrl}/collections/${userId.value}`
    const newUrl = concatSearchParamsToUrl(url, common)
    console.log('>>>>>>>>>>> taskInfos', taskInfos)
    const res = await request({
      method: 'POST',
      url: newUrl,
      data: {
        task_info: taskInfos
      },
    }) as IResponseUpdateHistory
    console.log('>>>>>>>>>>>>>>>>>> res', res)
    if (res && res.result === 0) {
      // 调整对应的资源数据
      updateHistoryList(res.task_info || [])
      return {
        result: 0,
        message: '标记成功'
      }
    }
    return {
      result: -1,
      message: '标记失败'
    }
  }

  function updateCollectionList (collectIds: number[], isHistoryTab: boolean) {
    // 历史记录： 更改状态   收藏记录： 移除
    if (!isHistoryTab) {
      collectionList.value = collectionList.value.filter(item => !collectIds.includes(item.collection_id))
      beginCollection.value = collectionList.value.length
      collectionTotal.value = collectionTotal.value - 1 < 0 ? 0 : collectionTotal.value - 1
    } 
    collectIds.forEach(id => {
      const index = historyList.value.findIndex(item => item.collection_id === id)
      if (index !== -1) {
        historyList.value[index].collection_id = 0
      }
    })
  }

  /** 取消收藏资源 */
  async function unCollectedFiles (collectIds: number[] | number, isHistoryTab: boolean) {
    if (!Array.isArray(collectIds)) {
      collectIds = [collectIds]
    }
    const paramStr = collectIds.join(',')
    const common = getCommonData('DELETE')
    const url = `${baseUrl}/collections/${userId.value}/${paramStr}`
    const newUrl = concatSearchParamsToUrl(url, common)
    const res = await request({
      method: 'DELETE',
      url: newUrl,
      data: {},
    }) as IResponseUpdateHistory
    console.log('>>>>>>>>>>>>>>>>>> res', res)
    if (res && res.result === 0) {
      // 调整对应的资源数据
      updateCollectionList(collectIds, isHistoryTab)
      return {
        result: 0,
        message: '取消标记成功'
      }
    }
    return {
      result: -1,
      message: '取消标记失败'
    }
  }

    /** 重命名 */
  async function renameHistory (task: IHistoryTask, name: string) {
    const common = getCommonData('PUT')
    const id = task.collection_id ? task.collection_id : task.task_id
    const url = `${baseUrl}/${task.collection_id ? 'collections' : 'tasks'}/${userId.value}/${id}`
    const newUrl = concatSearchParamsToUrl(url, common)
    const res = await request({
      method: 'PUT',
      url: newUrl,
      data: {task_info: {filename: name}},
      headers: {
        'Content-Type': 'application/json'
      },
    }) as IResponseUpdateHistory
    if (res && res.result === 0) {
      upDateFileName(task, name)
      return {
        result: 0,
        message: '重命名成功'
      }
    }
    return {
      result: -1,
      message: '重命名失败'
    }
  }

  function upDateFileName(task: IHistoryTask, name: string) {
    const index = historyList.value.findIndex(item => item.task_id === task.task_id)
    if (index !== -1) {
      historyList.value[index].filename = name
    }
    const collectionInx = collectionList.value.findIndex(item => item.task_id === task.task_id)
    if (collectionInx !== -1) {
      collectionList.value[collectionInx].filename = name
    }
  }

  /** 删除历史记录 */
  async function delHistoryTask (taskIds: number[]) {
    if (!Array.isArray(taskIds)) {
      taskIds = [taskIds]
    }
    const paramStr = taskIds.join(',')
    const common = getCommonData('DELETE')
    const url = `${baseUrl}/tasks/${userId.value}/${paramStr}`
    const newUrl = concatSearchParamsToUrl(url, common)
    const res = await request({
      method: 'DELETE',
      url: newUrl,
      data: common,
      headers: {
        'Content-Type': 'application/json'
      },
    }) as IResponseUpdateHistory
    if (res && res.result === 0) {
      updateDelHistoryList(taskIds)
      return {
        result: 0,
        message: '删除下载记录成功'
      }
    }
    return {
      result: -1,
      message: '删除下载记录失败'
    }
  }

  function updateDelHistoryList (taskIds: number[]) {
    historyList.value = historyList.value.filter(item => !taskIds.includes(item.task_id))
    beginHistory.value = historyList.value.length
    historyTotal.value = historyTotal.value - 1 < 0 ? 0 : historyTotal.value - 1
  }

  /** 清空历史记录 */
  async function clearHistory () {
    const common = getCommonData('POST')
    const res = await request({
      method: 'POST',
      url: `${baseUrl}/tasks:deleteAll`,
      data: {
        ...common,
        uid: userId.value,
      },
      headers: {
        'Content-Type': 'application/json'
      },
    }) as IResponseUpdateHistory
    if (res && res.result === 0) {
      historyList.value = []
      beginHistory.value = 0
      historyTotal.value = 0
      return {
        result: 0,
        message: '清空下载记录成功'
      }
    }
    return {
      result: -1,
      message: '清空下载记录失败'
    }
  }

  function getCommonData(method) {
    // wiki地址：http://wiki.xunlei.cn/pages/viewpage.action?pageId=20873184
    const secret = "!@#$%^&*()QAZ"
    const commonData = {
      device_id: deviceId.value,
      client_name: "xl_pc_web",
      client_version: '0', // appVersion.value,
      ts: Date.now()+'',
      r: getRandomNum(1, 100)+"",
      key: ''
    }
    const { client_name, client_version, ts, r } = commonData
    const str = secret+method.toUpperCase()+client_name+client_version+ts+r
    // commonData.key = hexMD5(str)
    commonData.key = getMd5(str)
    return commonData
  }


  return {
    initData,
    getCollection,
    getHistory,
    collectedFiles,
    unCollectedFiles,
    delHistoryTask,
    clearHistory,
    historyList,
    collectionList,
    loading,
    collectionTotal,
    historyTotal,
    initCollectionList,
    unInitData,
    renameHistory
  }
}