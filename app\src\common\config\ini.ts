import { IConfigSectionMap, IConfigKeyMap } from '@root/common/config/types';

const n: string = '\n';
const rn: string = '\r\n';
const regexSection: RegExp = /^\s*\[(.+?)\]\s*$/;
const regexParam: RegExp = /^\s*([^#].*?)\s*=\s*(.*?)\s*$/;

export namespace IniNS {
  export function stringify(sections: IConfigSectionMap): string {
    let ret: string = '';
    // 遍历所以section
    for (const section in sections) {
      const keyMap: IConfigKeyMap = sections[section];
      if (!keyMap) {
        break;
      }

      const secString: string = '[' + section + ']' + rn;
      ret += secString;

      // 遍历每个section下的param
      for (const key in keyMap) {
        const value: string = keyMap[key] as string;
        // 这里不能这样判断，因为有可能value的值是0
        // if (!value) {
        //   break;
        // }
        if (value === undefined || value === null) {
          break;
        }
        const param: string = key + '=' + value + rn;
        ret += param;
      }
    }
    return ret + n;
  }

  export function parse(buffer: string): IConfigSectionMap {
    const data: IConfigSectionMap = {};
    const lines: string[] = buffer.split(/\r\n|\r|\n/);
    let sections: IConfigKeyMap = {};
    let keys: RegExpMatchArray | null = null;
    for (let i: number = 0; i < lines.length; ++i) {
      const line: string = lines[i];
      keys = line.match(regexSection);
      if (keys) {
        data[keys[1]] = sections = data[keys[1]] || {};
      } else if (sections) {
        keys = line.match(regexParam);
        if (keys) {
          sections[keys[1]] = keys[2];
        }
      }
    }

    return data;
  }
}