<script setup lang="ts">
import { ref } from 'vue'
import { AccountHelper } from '@root/common/account/client/accountHelper';
import {
  IGetVerificationResponseData,
} from '@xbase/electron_auth_kit'
import { PopUpNS } from '@root/common/pop-up';
import LoadingSpinner from '@root/common/components/ui/loading-spinner/index.vue'

const emit = defineEmits<{
  (e: 'toLogin'): void
}>()

// 注册相关状态
const phoneNumber = ref('')
const password = ref('')
const confirmPassword = ref('')
const countryCode = ref('+86')

// 获取验证码
const verificationCode = ref('')
const countdown = ref(0)
const countdownTimer = ref<number | null>(null)
const verificationInfo = ref<IGetVerificationResponseData>()

// 注册填写错误信息
const errorMessage = ref('')

const isLoading = ref(false)

const handleUpdateCountryCode = (value: string) => {
  countryCode.value = value
}

const handleUpdatePhoneNumber = (value: string) => {
  phoneNumber.value = value
}

const handleUpdateVerificationCode = (value: string) => {
  verificationCode.value = value
}

const handleGetVerificationCode = async () => {
  console.log('handleGetVerificationCode', phoneNumber.value)
  if (countdown.value > 0) {
    return
  }

  if (!phoneNumber.value) {
    errorMessage.value = '手机号不能为空'
    return
  }

  if (!/^1[3-9]\d{9}$/.test(phoneNumber.value)) {
    errorMessage.value = '手机格式不正确'
    return
  }


  try {
    verificationInfo.value = await AccountHelper.getInstance().sendSignUpVerificationWithPhoneNumber(`${countryCode.value} ${phoneNumber.value}`)
    console.log('handleGetVerificationCode res', verificationInfo.value)
    // 开始倒计时
    countdown.value = 60 // 设置60秒倒计时
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
    }
    countdownTimer.value = window.setInterval(() => {
      if (countdown.value > 0) {
        countdown.value--
      } else {
        if (countdownTimer.value) {
          clearInterval(countdownTimer.value)
          countdownTimer.value = null
        }
      }
    }, 1000)
  } catch (error) {
    console.error('Failed to send verification code:', error)
    errorMessage.value = '发送验证码失败，请稍后重试'
  }
}

const handleRegister = async () => {
  // 正在注册，直接返回
  if (isLoading.value) return

  if (!phoneNumber.value) {
    errorMessage.value = '手机号不能为空'
    return
  }

  if (!password.value) {
    errorMessage.value = '密码不能为空'
    return
  }

  if (!confirmPassword.value) {
    errorMessage.value = '请再次输入密码'
    return
  }

  if (password.value !== confirmPassword.value) {
    errorMessage.value = '两次输入密码不一致'
    return
  }

  if (!verificationCode.value) {
    errorMessage.value = '验证码不能为空'
    return
  }

  if (!verificationInfo.value?.verification_id) {
    errorMessage.value = '请先获取短信验证码'
    return
  }

  errorMessage.value = ''
  isLoading.value = true

  try {

    console.log('注册参数', `${countryCode.value} ${phoneNumber.value}`, password.value, verificationCode.value, verificationInfo.value?.verification_id)

    const res = await AccountHelper.getInstance().signUp({
      phoneNumber: `${countryCode.value} ${phoneNumber.value}`,
      password: password.value,
      verificationCode: verificationCode.value,
      verification_id: verificationInfo.value?.verification_id ?? '',
    })

    console.log('注册成功', res)

    const currentWindow = PopUpNS.getCurrentWindow();
    await currentWindow.close();

  } catch (e) {
    console.log('注册失败', JSON.stringify(e))
    const errorDesc: string = await AccountHelper.getInstance().translateResponseError(e as any);
    errorMessage.value = errorDesc
  } finally {
    isLoading.value = false
  }
}

const handleUserAgreement = () => { }

const handlePrivacyAgreement = () => { }

</script>


<template>
  <div class="register-container">
    <div class="register-container-header draggable">
      <h1>欢迎注册迅雷</h1>
    </div>
    <div class="register-container-body">
      <div class="register-container-body-input">
        <xl-input placeholder="请输入手机号" type="phone" v-model="phoneNumber" @update:countryCode="handleUpdateCountryCode"
          :countryCodes="[{ code: '+86', name: '中国大陆' }]" @update:modelValue="handleUpdatePhoneNumber" />

        <xl-input type="password" v-model="password" placeholder="密码8-20位，包含字母/数字/字符2种组合" />

        <xl-input type="password" v-model="confirmPassword" placeholder="再次确认输入密码" />

        <xl-input placeholder="请输入验证码" v-model="verificationCode" @update:modelValue="handleUpdateVerificationCode">
          <template #right>
            <span class="register-container-input-right-text"
              :class="{ 'register-container-text-click': countdown === 0 }" @click="handleGetVerificationCode">
              {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
            </span>
          </template>
        </xl-input>
      </div>
    </div>

    <div class="register-container-button">
      <div class="register-container-button-error" v-if="errorMessage">
        <i class="xl-icon-tips-warning"></i>
        <span>{{ errorMessage }}</span>
      </div>
      <Button style="width: 100%;" @click="handleRegister" variant="primary" size="lg" :disable="isLoading">
        <LoadingSpinner v-if="isLoading" size="small" color="var(--font-font-light, #FFFFFF)" />
        <span v-else>注册</span>
      </Button>
    </div>

    <div class="register-container-wrapper">
      <span class="register-container-text">已有账号,<span class="register-container-text-link"
          @click="emit('toLogin')">直接登录</span></span>
    </div>

    <div class="register-footer">
      <span>注册即表示同意</span>
      <span class="register-footer-text-link register-container-text-click" @click="handleUserAgreement">《用户协议》</span>
      <span>及</span>
      <span class="register-footer-text-link register-container-text-click"
        @click="handlePrivacyAgreement">《隐私协议》</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.register-container {
  padding: 0 32px;

  &-header {
    height: 64px;
    position: relative;
    margin-bottom: 8px;
    margin-right: 16px;

    h1 {
      font-size: 24px;
      line-height: 32px;
      padding: 27px 32px 5px 0;
      color: var(--font-font-1, #272E3B);
      margin: 0;
    }
  }

  &-body {
    margin-top: 18px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &-body-input {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &-input-right-text {
    color: var(--font-font-2, #4E5769);
    font-size: 14px;
    line-height: 22px;

    &:not(.register-container-text-click) {
      cursor: not-allowed;
      color: var(--font-font-4, #C9CDD4);
    }
  }

  &-button {
    padding-top: 32px;
    position: relative;
    width: 100%;

    &-error {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--functional-error-default, #FF4D4F);
      font-size: 11px;
      line-height: 14px;
      position: absolute;
      top: 9px;
      left: 0;
    }
  }

  &-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 12px;
  }

  &-text {
    font-size: 13px;
    line-height: 22px;
    color: var(--font-font-3, #86909C);
  }

  &-text-click {
    cursor: pointer;

    &:hover {
      color: var(--primary-primary-hover, #488BF7);
    }
  }

  &-text-link {
    color: var(--primary-primary-default, #226DF5);
    cursor: pointer;
  }
}

.register-footer {
  position: absolute;
  bottom: 12px;
  left: 32px;
  width: 100%;
  display: flex;
  align-items: center;

  color: #949BA5;
  font-size: 12px;
  line-height: 20px;

  &-text-link {
    color: #6A707C;
  }
}
</style>
