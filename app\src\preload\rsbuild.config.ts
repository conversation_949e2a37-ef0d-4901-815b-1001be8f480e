import { defineConfig } from '@rsbuild/core'
import path from 'node:path'

export default defineConfig({
  output: {
    cleanDistPath: false,
    sourceMap: true,
    distPath: {
      root: path.join(process.cwd(), '../../dist/preload'),
      js: '.',
    },
    filename: {
      js: 'index.js',
    },
  },
  resolve: {
    alias: {
      '@root': path.join(process.cwd(), '../../src'),
      '@': path.join(process.cwd(), 'src'),
    },
  },
  tools: {
    rspack: {
      target: 'electron-preload',
      entry: {
        preload: path.join(__dirname, 'index.ts'),
      },
    },
  },
})
