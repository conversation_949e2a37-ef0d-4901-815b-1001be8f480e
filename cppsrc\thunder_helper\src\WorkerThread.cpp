#include "./WorkerThread.h"

WorkerThread::WorkerThread() {
	m_pool = std::make_shared<xl::multithread::ThreadPool>(4);
}

void WorkerThread::Init(std::shared_ptr<xl::thread::ThreadAffinity> threadAffinity) {
	m_pMainThreadAffinity = threadAffinity;
}

xl::coroutine::CommonAwaitable<void> WorkerThread::ResumeOnMainUIThread() {
	return m_pMainThreadAffinity->SwitchToAffinityThread();
}

xl::coroutine::CommonAwaitable<void> WorkerThread::ResumeOnThreadPool() {
    auto awaitCb = [this](CommonAwaitable<void>::_CB&& cb) {
        m_pool->AddTask(std::move(cb));
    };
    return xl::coroutine::CommonAwaitable<void>{awaitCb};
}