import { <PERSON>k<PERSON><PERSON><PERSON> } from '@root/common/task/client/dk-helper';
import { TaskUtilHelper } from '@root/common/helper/task-util-helper';
import { DkThunderHelper } from '@root/common/task/client/dk-thunder-helper';
import { URLHelperNS } from '@root/common/task/client/url-helper';
import * as DownloadKernel from '@root/common/task/base';
import { Logger } from '@root/common/logger';
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper';

// 后缀名列表来源于配置面板－监视设置－下载类型
const downloadExtNameList: string = `.asf;.avi;.exe;.iso;.mp3;.mpeg;.mpg;.mpga;.ra;.rar;.rm;.rmvb;.tar;.wma;.wmp;.wmv;
  .mov;.zip;.3gp;.chm;.mdf;.torrent;.jar;.msi;.arj;.bin;.dll;.psd;.hqx;.sit;.lzh;.gz;.tgz;.xlsx;.xls;.doc;.docx;.ppt;
  .pptx;.flv;.swf;.mkv;.tp;.ts;.flac;.ape;.wav;.aac;.txt;.dat;.7z;.ttf;.bat;.xv;.xvx;.pdf;.mp4;.apk;.ipa;.epub;.mobi;
  .deb;.sisx;.cab;.pxl;.dmg;.msu;`;

// 百度百科：常用文件扩展名
const usualExtNameList: string = `.a;.a3m;.a3w;.a4m;.a4p;.a4w;.a5w;.aam;.aas;.abf;.abk;.abs;.ace;.acm;.acp;.act;.ad;
  .ada;.adb;.adf;.adi;.adm;.adp;.adr;.ads;.af2;.af3;.afm;.ai;.aif;.aifc;.aiff;.aim;.ais;.akw;.alb;.all;.ams;.anc;.ani;
  .ans;.ant;.api;.aps;.ari;.arj;.art;.asa;.asc;.asd;.ase;.asf;.asm;.aso;.asp;.asv;.asx;.atw;.au;.avb;.avi;.avr;.avs;
  .awd;.awr;.axx;.bak;.bas;.bat;.bdf;.bgl;.bi;.bif;.biff;.bin;.bk;.bk$;.bks;.bmk;.bmp;.book;.brx;.bsp;.btm;.bud;.bun;
  .bw;.bwv;.c;.cab;.cad;.cal;.cap;.cas;.cb;.cc;.ccb;.cch;.cco;.cct;.cda;.cdf;.cdi;.cdm;.cdr;.cdt;.cdx;.cfg;.cgi;.cgm;
  .chk;.chm;.chr;.cif;.cil;.class;.cll;.clp;.cls;.cmf;.cmv;.cmx;.cnf;.cnm;.cnt;.cod;.com;.cpl;.cpo;.cpp;.cpr;.cpt;.cpx;
  .crd;.crp;.crt;.csc;.csp;.cst;.csv;.ctl;.cur;.cv;.cxx;.dat;.db;.dbc;.dbf;.dbx;.dcm;.dcs;.dct;.dcu;.dcx;.dem;.der;.dewf;
  .dib;.dic;.dif;.dig;.dir;.diz;.dlg;.dll;.dls;.dmd;.dmf;.doc;.dot;.draw;.drv;.drw;.dsf;.dsg;.dsm;.dsp;.dsq;.dsw;.dtd;
  .dun;.dv;.dxf;.dxr;.eda;.edd;.emd;.emf;.eml;.ephtml;.eps;.exe;.fav;.fax;.fcd;.fdf;.ffa;.ffk;.ffl;.ffo;.fif;.fla;.flc;
  .fm;.fml;.fng;.fnk;.fon;.fot;.frt;.frx;.ftg;.fts;.gal;.gdb;.gdm;.gem;.gen;.getright;.gfi;.gfx;.gho;.gif;.gim;.gix;.gkh;
  .gks;.gl;.gna;.gnt;.gnx;.gra;.grf;.grp;.hcom;.hgl;.hlp;.hpj;.hpp;.hst;.ht;.htm;.html;.htt;.htx;.icb;.icc;.icl;.icm;.ico;
  .idd;.idf;.idq;.idx;.iff;.iges;.igf;.ilbm;.ima;.inf;.ini;.inrs;.ins;.int;.iqy;.iso;.ist;.isu;.iwc;.j62;.jar;.java;.jbf;
  .jff;.jfif;.jif;.jmp;.jpe;.jpeg;.jpg;.js;.jsp;.jtf;.k25;.kar;.kdc;.key;.kfx;.kiz;.kkw;.kmp;.kqp;.lab;.lbm;.lbt;.lbx;.ldb;
  .ldl;.leg;.lft;.lgo;.lha;.lib;.lin;.lis;.llx;.lnk;.log;.lst;.lu;.lyr;.lzh;.lzs;.m1v;.m3u;.mad;.maf;.mam;.map;.maq;.mar;.mat;
  .mb1;.mbx;.mcr;.mdb;.mde;.mdl;.mdn;.mdw;.mdz;.mic;.mid;.mim;.mime;.mli;.mme;.mng;.mnu;.mod;.mov;.mp2;.mp3;.mpa;.mpe;.mpeg;
  .mpg;.mpp;.mpr;.msg;.msi;.msn;.msp;.mst;.mtm;.nan;.nap;.ncb;.ncd;.ncf;.nff;.nft;.nil;.nist;.nls;.nlu;.ntx;.nwc;.nws;.obj;
  .ocx;.ods;.ofn;.oft;.olb;.ole;.oogl;.opo;.p65;.pab;.part;.pas;.pbd;.pbl;.pbm;.pbr;.pcd;.pcl;.pcm;.pdd;.pdf;.pfm;.pgl;.pgm;
  .ph;.php;.php3;.phtml;.pic;.pjt;.pjx;.pkg;.pli;.png;.pot;.ppa;.ppf;.ppm;.pps;.ppt;.prf;.prg;.prj;.prn;.prt;.psd;.psp;.pst;
  .pwz;.qic;.qif;.qlb;.qry;.qtp;.qtx;.qw;.ra;.ram;.rar;.rdf;.reg;.rep;.res;.rft;.rgb;.rm;.rmd;.rpt;.rtf;.rul;.rvp;.s;.sav;.sbl;
  .scc;.scf;.scp;.scr;.sct;.scx;.sdt;.sdv;.sdx;.sep;.sfd;.sfi;.sfr;.sfx;.sgi;.sgml;.shg;.shtml;.shw;.sig;.ska;.skl;.sl;.spl;
  .sqc;.sqr;.str;.swa;.swf;.sys;.syw;.taz;.tga;.theme;.thn;.tif;.tiff;.tig;.tlb;.tmp;.tol;.tpl;.trm;.trn;.ttf;.txt;.txw;.udf;
  .ult;.url;.use;.uwf;.vbp;.vbp;.vbw;.vbw;.vbx;.vbx;.vct;.vcx;.vda;.vda;.vir;.vir;.viv;.vqf;.vsd;.vsd;.vsl;.vsl;.vss;.vss;.vst;
  .vst;.vsw;.vsw;.vxd;.vxd;.w3l;.wab;.wad;.wav;.wbk;.wcm;.wdb;.wfm;.wfn;.wil;.wiz;.wll;.wmf;.wow;.wp;.wpd;.wpf;.wpg;.wps;.wpt;.wr1;
  .wrk;.wrl;.wrz;.x;.x16;.x32;.xar;.xbm;.xi;.xla;.xlb;.xlc;.xld;.xlk;.xll;.xlm;.xls;.xlt;.xlv;.xlw;.xnk;.xpm;.xwd;.xwf;.yal;.z;.zap;.zip;`;

export namespace ParseUrlFileNameNS {

  export interface IUrlInfo {
    schema?: string;
    userName?: string;
    password?: string;
    hostName?: string;
    port?: number;
    fullPath?: string;
    codePage?: number;
  }

  export interface IEd2kUrlInfo {
    fileSize?: number;
    fileName?: string;
    fileHash?: string;
  }

  export function isDownloadFileExtName(ext: string): boolean {
    let is: boolean = false;
    do {
      if (ext === undefined || ext === null) {
        break;
      }
      if (ext === '' || ext === '.') {
        break;
      }
      if (downloadExtNameList.indexOf(ext) > -1) {
        is = true;
        break;
      }
    } while (0);
    return is;
  }

  export function isIllegalFileName(fileName: string): boolean {
    let good: boolean = false;
    do {
      if (fileName === undefined || fileName === null) {
        break;
      }
      if (fileName === '') {
        break;
      }
      if (fileName.match(/[\/\\"<>\?\*|]/)) {
        break;
      }
      good = true;
    } while (0);
    return good;
  }

  export function isGoodFileExtName(ext: string): boolean {
    let good: boolean = false;
    do {
      if (ext === undefined || ext === null) {
        break;
      }
      if (ext === '' || ext === '.') {
        break;
      }
      const fileExtType: TaskUtilHelper.FileExtType = TaskUtilHelper.getTaskFileType(undefined, ext);
      if (
        fileExtType === TaskUtilHelper.FileExtType.Video ||
        fileExtType === TaskUtilHelper.FileExtType.Music ||
        fileExtType === TaskUtilHelper.FileExtType.Pic
      ) {
        good = true;
        break;
      }
      good = isDownloadFileExtName(ext);
    } while (0);
    return good;
  }

  /**
   * @description: 判断文件名是否为图片、音视频、常见下载后缀名
   */
  export function isGoodFileName(fileName: string): boolean {
    let good: boolean = false;
    const parsed = DkThunderHelper.parsePath(fileName);
    good = isIllegalFileName(parsed.name) && isGoodFileExtName(parsed.ext);
    return good;
  }

  /**
   * @description : 解析URLPath，对形如"pagefile?k1=v1&k2=v2&k3=v3"
   * 如果vn中有合适的文件名，则返回；否则，如果pagefile是合适的文件名，则返回；否则，返回空
   */
  export function parseFileNameFromDynamicUrlPath(urlPath: string): string {
    let ret: string = '';
    do {
      if (urlPath === undefined || urlPath === null) {
        break;
      }
      const urlPathParsed = parseDynamicUrlPath(urlPath);
      if (urlPathParsed.args !== undefined) {
        const argsObj = parseDynamicUrlArgs(urlPathParsed.args);
        const keys = Object.keys(argsObj)
        for (let i = 0; i < keys.length; i++) {
          const key = keys[i];
          const value: string = Reflect.get(argsObj, key);
          if (isGoodFileName(value)) {
            ret = value;
            break;
          }
        }
      }

      if (urlPathParsed.pageFileName !== undefined) {
        if (isGoodFileName(urlPathParsed.pageFileName)) {
          ret = urlPathParsed.pageFileName;
        }
      }
    } while (0);
    return ret;
  }

  export async function getFileNameListFromUrlPath(urlPath: string) {
    const names: string[] = [];
    do {
      if (urlPath === undefined || urlPath === null) {
        break;
      }

      const fileNameDynamic: string = parseFileNameFromDynamicUrlPath(urlPath);
      if (fileNameDynamic !== '') {
        if (!names.includes(fileNameDynamic)) {
          const parsed: string = await parseFileNameFromP2spUrlPath(fileNameDynamic);
          names.push(parsed);
        }
      }

      const fileName: string = await parseFileNameFromP2spUrlPath(urlPath);
      if (!names.includes(fileName)) {
        names.push(fileName);
      }
    } while (0);
    return names;
  }

  export async function getNameFromUrl(url: string): Promise<string> {
    let ret: string = 'index.html';
    const taskType: DownloadKernel.TaskType = await DkThunderHelper.getTaskTypeFromUrl(url);
    if (taskType === DownloadKernel.TaskType.P2sp) {
      const urlInfo: IUrlInfo = await parseUrl(url);
      if (typeof urlInfo === 'object' && Object.keys(urlInfo).length && urlInfo.fullPath) {
        const fileNameList: string[] = await getFileNameListFromUrlPath(urlInfo.fullPath);
        if (fileNameList.length > 0) {
          ret = fileNameList[0];
        }
      } else {
        // 兼容ipv6的文件名解析
        const regResults = /:\/\/.*?\[.+?\].*(\/.+)/.exec(url);
        if (regResults && regResults[1]) {
          const fileNameList: string[] = await getFileNameListFromUrlPath(regResults[1]);
          if (fileNameList.length > 0) {
            ret = fileNameList[0];
          }
        }
      }
    } else if (taskType === DownloadKernel.TaskType.Emule) {
      const ed2kInfo: IEd2kUrlInfo = await parseEd2kUrl(url);
      ret = ed2kInfo.fileName!;
    }
    return ret.replace(/[*?/\\:|<>\"]/g, '_');
  }

  /**
   * @description: 解析形如"k1=v1&k2=v2&k3=v3"为table{k1=v1, k2=v2, k3=v3}
   */
  export function parseDynamicUrlArgs(args: string) {
    const obj: object = new Object();
    do {
      if (args === undefined || args === null) {
        break;
      }

      // /([^&=?\s]+)=+([^&\s]*)/g
      const regEx: RegExp = /([^&=?]+)=([^&]*)/g;
      while (regEx.exec(args)) {
        Reflect.set(obj, RegExp.$1, RegExp.$2);
      }
    } while (0);
    return obj;
  }

  /**
   * @description: 解析形如"pagefile?k1=v1&k2=v2&k3=v3"为两个字符串"pagefile","k1=v1&k2=v2&k3=v3"
   */
  interface IParseDynamicUrlPathRes {
    pageFileName: string | undefined
    args: string | undefined
  }
  export function parseDynamicUrlPath(urlPath: string): IParseDynamicUrlPathRes {
    const ret: IParseDynamicUrlPathRes = { pageFileName: undefined, args: undefined };
    do {
      if (urlPath === undefined || urlPath === null) {
        break;
      }
      if (urlPath.match(/[\/]?([^?]*)\?([^\s]*)/)) {
        ret.pageFileName = RegExp.$1;
        ret.args = RegExp.$2;
      } else {
        ret.pageFileName = urlPath;
        ret.args = '';
      }
    } while (0);

    return ret;
  }

  export interface IP2pPreCreateTaskItem {
    url: string
    type: DownloadKernel.TaskType
    birdKey?: string
    sourceUrl: string
  }

  export interface IMagnetPreCreateTaskItem extends ThunderNewTaskHelperNS.INewTaskData {
    url: string
    clickFrom?: string
    sourceUrl: string
  }

  export interface IGetContentParseResultResponse {
    p2pTaskList: IP2pPreCreateTaskItem[]
    magnetTaskList: IMagnetPreCreateTaskItem[]
    shareTaskList: IShareTaskItem[]
  }


  export interface IShareTaskItem {
    url: string
    share_id: string
    pass_code: string
    sourceUrl: string
  }

  const logger = new Logger({ tag: 'ParserHelper' })

  export function getQueryString(url: string, key: string): string {
    let value: string = '';
    do {
      let urlObj: URL | null = null;
      try {
        urlObj = new URL(url);
      } catch (error) {
        //
      }
      if (!urlObj) {
        break;
      }
      value = urlObj.searchParams.get(key) ?? '';
    } while (0);
    return value;
  }

  export function setQueryString(url: string, query: { [prop: string]: string | number }): string {
    let newUrl: string = url;
    do {
      let urlObj: URL | null = null;
      try {
        urlObj = new URL(url);
      } catch (error) {
        //
      }
      if (!urlObj) {
        break;
      }
      for (let k in query) {
        urlObj.searchParams.set(k, String(query[k]));
      }
      newUrl = urlObj.href;
    } while (0);
    return newUrl;
  }

  export function processYunpanShareData(url: string, sourceUrl: string): IShareTaskItem | null {
    if (url.indexOf('https://pan.xunlei.com/s/') === 0) {
      let passCode: string = getQueryString(url, 'pass_code');
      if (!passCode) {
        passCode = url.match(/pwd=(\w+)/)?.[1] ?? '';
      }
      const shareID = url.match(/\/s\/([\w|-]+)(#|\?|$)/)?.[1] ?? ''; // 匹配 /s/ 和 #、?、字符串结尾中间的字符串
      return {
        url: url,
        share_id: shareID,
        pass_code: passCode,
        sourceUrl,
      }
    }
    return null
  }

  function isRegResultValidHttpSplitter(text: string, regResult: RegExpExecArray, lastIndex: number): boolean {
    let validProtocal: boolean = true;
    const resultProtocal: string = regResult[0];
    const tag: string = '&tr=';
    if (lastIndex - resultProtocal.length >= 3 && resultProtocal.indexOf('http') === 0) {
      if (text.substr(lastIndex - resultProtocal.length - tag.length, tag.length) === tag) {
        validProtocal = false;
      }
    }
    return validProtocal;
  }


  function splitTextByProtocal(text: string): string[] {
    const protocalUrlText: string[] = [];
    const reg: RegExp = /(https?|ftp|thunder|ed2k|magnet):/g;
    let regResult: RegExpExecArray | null = reg.exec(text);
    let protocalIndex: number = -1;
    // 由于v8版本太旧，不支持高级的零宽断言正则，这里使用字符串匹配
    while (regResult) {
      if (isRegResultValidHttpSplitter(text, regResult, reg.lastIndex)) {
        // 如果是http/https协议，如果前面是&tr=，就不分割，有可能是磁力链的参数
        const lastProtocalIndex: number = protocalIndex;
        const resultProtocal: string = regResult[0];
        protocalIndex = reg.lastIndex - resultProtocal.length;
        if (lastProtocalIndex >= 0) {
          protocalUrlText.push(text.substr(lastProtocalIndex, protocalIndex - lastProtocalIndex));
        }
      }
      regResult = reg.exec(text);
      if (!regResult) {
        protocalUrlText.push(text.substr(protocalIndex));
      }
    }
    return protocalUrlText;
  }

  const ed2kPrefix: string = 'ed2k://';
  const magnetPrefix: string = 'magnet:?';
  const thunderPrefix: string = 'thunder://';
  function isEd2kProtocal(url: string): boolean {
    let ret: boolean = false;
    if (url) {
      ret = url.indexOf(ed2kPrefix) === 0;
    }
    return ret;
  }

  function isMagnetProtocal(url: string): boolean {
    let ret: boolean = false;
    if (url) {
      ret = url.indexOf(magnetPrefix) === 0;
    }
    return ret;
  }

  function isTraditionProtocal(url: string): boolean {
    const urlPrefix: string[] = ['http://', 'https://', 'ftp://'];
    let ret: boolean = false;
    if (url) {
      for (let i: number = 0; i < urlPrefix.length; ++i) {
        const prefix: string = urlPrefix[i];
        if (url.indexOf(prefix) === 0) {
          ret = true;
          break;
        }
      }
    }

    return ret;
  }

  function trimMagnetValidUrl(url: string): string {
    let urlOut: string = url;
    // 此段代码翻译自 bt_magnet_url_parser.cpp 183行 bool bt_magnet_url_parser::parse_magnet_url()函数
    const magnetUrlInfo: string = url.substr(magnetPrefix.length);
    const urlInfos: string[] = magnetUrlInfo.split('&');
    for (let i: number = 0; i < urlInfos.length; ++i) {
      let index: number = urlInfos[i].indexOf('=');
      if (index < 0) {
        continue;
      }
      const tag: string = urlInfos[i].substr(0, index);
      if (tag === 'xt' || tag.indexOf('xt.') === 0) {
        // 现在只识别urn::btih:的形式，如果不是，则返回false
        const tagValue: string = 'urn:btih:';
        const tagEnd: string = urlInfos[i].substr(index + 1, tagValue.length);
        if (tagEnd !== tagValue) {
          break;
        }
        let base32InfoHash: string = urlInfos[i].substr(index + tagValue.length + 1);
        if (base32InfoHash.length <= 32) {
          // 小于32位的一定不合法，直接返回，刚好32位的当作合法，直接返回
          break;
        }
        // 判断前40个[0-9,a-z]字符，当合法，取不到，再判断前32个[0-9,a-z]
        let reg: RegExp = /[0-9,A-Z,a-z]{40}/;
        if (reg.test(base32InfoHash)) {
          if (base32InfoHash.length > 40 && base32InfoHash[40] !== '&') {
            logger.log('reg test 40');
            base32InfoHash = base32InfoHash.substr(0, 40);
            index = url.indexOf(base32InfoHash);
            if (index >= 0) {
              urlOut = url.substr(0, index + base32InfoHash.length);
            }
          }
          logger.log('urlOut', urlOut, url);
          break;
        } else {
          reg = /[0-9,A-Z,a-z]{32}/;
          if (reg.test(base32InfoHash)) {
            if (base32InfoHash[32] !== '&') {
              logger.log('reg test 32');
              base32InfoHash = base32InfoHash.substr(0, 32);
              index = url.indexOf(base32InfoHash);
              if (index >= 0) {
                urlOut = url.substr(0, index + base32InfoHash.length);
              }
            }
            logger.log('urlOut', urlOut, url);
            break;
          }
        }
      }
    }
    return urlOut;
  }

  function trimThunderValidUrl(url: string): string {
    let urlOut: string = url;
    if (url) {
      const reg: RegExp = /[^A-Za-z0-9=\/\+]/g;
      reg.lastIndex = thunderPrefix.length;
      const regResult: RegExpExecArray | null = reg.exec(url);
      if (regResult) {
        urlOut = url.substr(0, reg.lastIndex - 1);
        logger.log('urlOut', urlOut, url);
      }
    }
    return urlOut;
  }

  function trimEd2kValidUrl(url: string): string {
    let urlOut: string = url;
    if (url) {
      const reg: RegExp = /\|\//g;
      reg.lastIndex = ed2kPrefix.length;
      const regResult: RegExpExecArray | null = reg.exec(url);
      if (regResult) {
        urlOut = url.substr(0, reg.lastIndex);
        logger.log('urlOut', urlOut, url);
      }
    }
    return urlOut;
  }

   function trimRightValidUrl(url: string): string {
    let urlOut: string = url;
    if (url) {
      const reg: RegExp = /[ ,\r,\n]/g;
      const regResult: RegExpExecArray | null = reg.exec(url);
      if (regResult) {
        urlOut = url.substr(0, reg.lastIndex - 1);
        logger.log('urlOut', urlOut, url);
      }
    }
    return urlOut;
  }

  function trimTranditionValidUrl(url: string): string {
    let urlOut: string = url;
    return urlOut;
  }

  // 1、如果只有一个协议，只处理magnet/ed2k/thunder，其他截掉左边，右边不处理
  // 2、分割协议如果http/https前有&tr=，不分割
  // 3、多个协议，每个协议的右侧以空格、换行后都截掉。
  async function trimValidUrls(text: string): Promise<string[]> {
    const validUrls: string[] = [];
    if (text) {
      // 以协议为分割出url段
      const protocalUrlText: string[] = splitTextByProtocal(text);
      logger.log('splitTextByProtocal', protocalUrlText);
      if (protocalUrlText.length === 1) {
        let validUrl: string = protocalUrlText[0];
        if (isMagnetProtocal(validUrl)) {
          validUrl = trimMagnetValidUrl(validUrl);
        } else if (await isThunderPrivateUrl(validUrl)) {
          validUrl = trimThunderValidUrl(validUrl);
        } else if (isEd2kProtocal(validUrl)) {
          validUrl = trimEd2kValidUrl(validUrl);
        } else {
          const matcher: RegExpMatchArray | null = validUrl.trim().match(/([\S]+)/);
          if (matcher && matcher[0]) {
            validUrl = matcher[0];
          }
        }
        validUrls.push(validUrl);
      } else {
        protocalUrlText.forEach(
          async (validUrl: string) => {
            validUrl = validUrl.trim();
            // 先截掉空格、换行后的所有字符
            validUrl = trimRightValidUrl(validUrl);
            // 再针对不同的协议使用不同的截断方式
            if (isMagnetProtocal(validUrl)) {
              validUrl = trimMagnetValidUrl(validUrl);
            } else if (await isThunderPrivateUrl(validUrl)) {
              validUrl = trimThunderValidUrl(validUrl);
            } else if (isEd2kProtocal(validUrl)) {
              validUrl = trimEd2kValidUrl(validUrl);
            } else if (isTraditionProtocal(validUrl)) {
              validUrl = trimTranditionValidUrl(validUrl);
            }
            logger.log('validUrl', validUrl);
            validUrls.push(validUrl);
          }
        );
      }
    }
    return validUrls;
  }

  export async function parseYunpanShare(textList: string[]): Promise<string[]> {
    const panShareLinks: { [url: string]: boolean } = {};
    const urlMap: { [url: string]: boolean } = {};
    for (let i: number = 0; i < textList.length; ++i) {
      const url: string = textList[i];
      if (!url) {
        continue;
      }
      const validUrls: string[] = await trimValidUrls(url);
      for (const url of validUrls) {
        urlMap[url] = true;
      }
    }

    const validUrls: string[] = Object.keys(urlMap);
    if (validUrls?.length) {
      const text: string = textList.join(' ');
      for (let i: number = 0; i < validUrls.length && i < 50; i++) {
        // http://jira.xunlei.cn/browse/XLCLOUD-27272 最多打开50个分享链接
        let url: string = validUrls[i].split('#')[0]; // remove the part after '#' in url.
        if (url.indexOf('https://pan.xunlei.com/s/') === 0) {
          if (getQueryString(url, 'pass_code')) {
            logger.log('auto share completely url', url);
            panShareLinks[url] = true;
            continue;
          }

          // 在其后搜索
          const index: number = text.indexOf(url);
          const position: number = index + url.length;
          let subText: string = text.substring(position);
          if (i !== validUrls.length - 1) {
            const nextUrl: string = validUrls[i + 1];
            const subEnd: number = text.indexOf(nextUrl, position);
            if (subEnd !== -1) {
              subText = text.substring(position, subEnd);
            }
          }

          let results: string[] | null = /提取码.*?([a-zA-Z0-9]+)/.exec(subText);
          if (!results) {
            results = /.*?([a-zA-Z0-9]+)/.exec(subText);
          }
          logger.log('auto share', results);
          if (results && results[1] && results[1].length === 4) {
            const passCode: string = results[1];
            logger.log('auto share', url, passCode);
            const isFastAccess = /pwd=(\w+)/.test(url);
            if (!isFastAccess) { // 不重复设置passcode
              url = setQueryString(url, { pass_code: passCode });
            }
            logger.log('auto share open url', url);
            panShareLinks[url] = true;
          } else {
            panShareLinks[url] = true;
          }
        }
      }
    }

    return Object.keys(panShareLinks);
  }

  /**
   * 校验是否可以下载（从剪切板中获取的文案，进行解析，如果可以下载则进行处理（如弹窗），如果不能下载则不处理）
   * @param content
   */
  export async function getContentParseResult(content: string, clickFrom: string = 'download', isMagnetCode: boolean = true): Promise<IGetContentParseResultResponse> {
    const allText = content.trim();
    // 重置
    const p2pTaskList: IP2pPreCreateTaskItem[] = [];
    const magnetTaskList: IMagnetPreCreateTaskItem[] = [];
    const shareTaskList: IShareTaskItem[] = [];

    if (allText === '') {
      return {
        p2pTaskList,
        magnetTaskList,
        shareTaskList,
      };
    }
    // 按不可见字符切分链接
    const lines = allText.split(/[\f\n\r]/);
    // 去重相同的链接
    const handledLineMap: Map<string, boolean> = new Map();

    for (let i = 0; i < lines.length; i++) {
      let url = lines[i].trim();
      const sourceUrl = url
      // 空的直接跳过
      if (!url) continue;
      // 已经处理过的直接跳过
      if (handledLineMap.get(url)) continue;
      // 如果符合迅雷私有协议，则解析出来
      if (await isThunderPrivateUrl(url)) {
        url = await parseThunderPrivateUrl(url);
      }
      try {
        const shareUrl = (await parseYunpanShare([url]))[0]
        if (shareUrl) {
          // 分享链接处理
          const shareData = processYunpanShareData(shareUrl, sourceUrl)
          if (shareData) {
            shareTaskList.push(shareData)
          }
        }
      } catch (e) {
      }
      // 迅雷口令
      let birdKey: string | undefined = undefined;
      const match = URLHelperNS.isBirdKey(url);
      if (match && match.length) {
        birdKey = url;
      }
      // 如果是种子特征码，尝试补齐 magnet 协议
      if (isMagnetCode) {
        if (URLHelperNS.isMagnetCode(url)) {
          url = await URLHelperNS.fixMagnetUrl(url);
        }
      }
      // 无法识别的 url 的直接跳过
      if (!URLHelperNS.isUrlValid(url)) continue;
      // 不支持的 url 的直接跳过
      if (!URLHelperNS.isSupportUrl(url)) continue;

      // 处理过的链接设置好状态
      handledLineMap.set(url, true);

      const taskType: DownloadKernel.TaskType = await DkThunderHelper.getTaskTypeFromUrl(url);
      logger.log('taskType', taskType)
      if (taskType === DownloadKernel.TaskType.P2sp || taskType === DownloadKernel.TaskType.Emule) {
        // P2P 任务
        p2pTaskList.push({ url: url, type: taskType, birdKey: birdKey, sourceUrl });
      } else {
        // BT 任务
        magnetTaskList.push({ url: url, birdkeyChars: birdKey, clickFrom: clickFrom, sourceUrl });
      }
    }

    return {
      p2pTaskList,
      magnetTaskList,
      shareTaskList,
    }
  }

  export async function parseUrl(url: string) {
    return await DkHelper.parseP2spUrl(url);
  }

  export async function parseEd2kUrl(url: string) {
    return await DkHelper.parserEd2kLink(url);
  }

  export async function parseMagnetUrl(url: string) {
    return await DkHelper.parseMagnetUrl(url);
  }

  export async function parseFileNameFromP2spUrlPath(url: string) {
    return await DkHelper.parseFileNameFromP2spUrlPath(url);
  }

  export async function parseBtTaskInfo(filePath: string) {
    return await DkHelper.parseBtTaskInfo(filePath);
  }

  export async function isThunderPrivateUrl(url: string) {
    return await DkHelper.isThunderPrivateUrl(url);
  }

  export async function parseThunderPrivateUrl(url: string) {
    let ret: string = url;
    if (await isThunderPrivateUrl(url)) {
      ret = await DkHelper.parseThunderPrivateUrl(url);
    }
    return ret;
  }
}