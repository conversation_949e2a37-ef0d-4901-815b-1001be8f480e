import { <PERSON>w<PERSON><PERSON><PERSON><PERSON> } from "@root/common/player/dwm-helper"
import { ImageRatioType } from "@root/common/player/base"

export const Player_UI_Const = {
  Player_Ani_Fade_Second: DwmHelper.getInstance().getIsDwm() ? 0 : 0.3,
  Player_Ani_Fade_Menu_Second: DwmHelper.getInstance().getIsDwm() ? 0 : 0.5,

  Popover_Hide_Delay_Second: DwmHelper.getInstance().getIsDwm() ? 0 : 0.3,

  Player_CommonNotice_Hide_Ms: 3000,
}

export const Player_Speed_Config_List = [
  {
    id: 'play_speed_0.5',
    icon: require('@root/common/assets/img/player/ic_player_speed0.5_30.svg'),
  },
  {
    id: 'play_speed_0.75',
    icon: require('@root/common/assets/img/player/ic_player_speed0.75_30.svg'),
  },
  {
    id: 'play_speed_1',
    icon: require('@root/common/assets/img/player/ic_player_speed_30.svg'),
  },
  {
    id: 'play_speed_1.25',
    icon: require('@root/common/assets/img/player/ic_player_speed1.25_30.svg'),
  },
  {
    id: 'play_speed_1.5',
    icon: require('@root/common/assets/img/player/ic_player_speed1.5_30.svg'),
    tag: require('@root/common/assets/img/player/ic-quality-free-time.svg'),
  },
  {
    id: 'play_speed_2',
    icon: require('@root/common/assets/img/player/ic_player_speed2.0_30.svg'),
    tag: require('@root/common/assets/img/player/ic-quality-free-time.svg'),
  },
  {
    id: 'play_speed_3',
    icon: require('@root/common/assets/img/player/ic_player_speed3.0_30.svg'),
    tag: require('@root/common/assets/img/player/ic-quality-free-time.svg'),
  },
  {
    id: 'play_speed_4',
    icon: require('@root/common/assets/img/player/ic_player_speed4.0_30.svg'),
    tag: require('@root/common/assets/img/player/ic-quality-free-time.svg'),
  },
]

export const Player_Volume_Config_List = [
  {
    checkVolumeMatch: (volume: number) => volume >= 50 && volume <= 100,
    icon: require('@root/common/assets/img/player/ic_player_volbig_30.svg'),
  },
  {
    checkVolumeMatch: (volume: number) => volume >= 25 && volume < 50,
    icon: require('@root/common/assets/img/player/ic_player_volmed_30.svg'),
  },
  {
    checkVolumeMatch: (volume: number) => volume > 0 && volume < 25,
    icon: require('@root/common/assets/img/player/ic_player_volsmall_30.svg'),
  },
  {
    checkVolumeMatch: (volume: number) => volume === 0,
    icon: require('@root/common/assets/img/player/ic_player_volmute_30.svg'),
  },
]
export function getVolumeConfig(volume: number) {
  return Player_Volume_Config_List.find((config) =>
    config.checkVolumeMatch(volume),
  )
}

export const Player_Ratio_Config_List = [
  {
    resolutionName: '4K',
    icon: require('@root/common/assets/img/player/ic_player_4K_30.svg'),
    // tag: require('@root/common/assets/img/player/ic-quality-free-time.svg'), // 服务端下发
  },
  {
    resolutionName: '1080P',
    icon: require('@root/common/assets/img/player/ic_player_1080p_30.svg'),
    // tag: require('@root/common/assets/img/player/ic-quality-free-time.svg'),
  },
  {
    resolutionName: '720P',
    icon: require('@root/common/assets/img/player/ic_player_720p_30.svg'),
    // tag: require('@root/common/assets/img/player/ic-quality-free-time.svg'),
  },
  {
    resolutionName: '480P',
    icon: require('@root/common/assets/img/player/ic_player_480p_30.svg'),
  },
  {
    resolutionName: '360P',
    icon: require('@root/common/assets/img/player/ic_player_360p_30.svg'),
  },
]

export const Player_Subtitles_FontSize_Config_List = [
  {
    label: '最小',
    value: 80,
  },
  {
    label: '较小',
    value: 90,
  },
  {
    label: '适中',
    value: 100,
  },
  {
    label: '较大',
    value: 120,
  },
  {
    label: '最大',
    value: 150,
  },
]

export enum Player_Subtitles_PositionMode {
  default = 'default',
  custom = 'custom',
}
export const Player_Subtitles_PositionMode_Config_List = [
  {
    label: '默认位置',
    value: Player_Subtitles_PositionMode.default,
  },
  {
    label: '自定义位置',
    value: Player_Subtitles_PositionMode.custom,
  },
]

export function genSubtitlesDefaultConfig() {
  return {
    timing: 0,
    fontSize: 100, // 适中
    positionMode: Player_Subtitles_PositionMode.default,
    position: [100], // todo bug, 默认位置-1000, 切到自定义位置时在设置100
  }
}

export type TSubtitlesConfig = ReturnType<typeof genSubtitlesDefaultConfig>

export function genPlayViewDefaultConfig() {
  return {
    imageRatio: ImageRatioType.Origin,
    rorate: 0,
    flip_horizontal: false,
    flip_vertical: false,
  }
}

export type TVideoMenuName =
  | 'screen'
  | 'sound'
  | 'color'
export type TVideoMenuClickId =
  | 'restore_set' // 恢复默认设置
  // 画面调节菜单
  | 'original' // 原始比例
  | 'cover_screen' // 铺满全屏
  | '16:9' // 16:9
  | '4:3' // 4:3
  | '21:9' // 21:9
  | 'turn_left' // 向左旋转
  | 'turn_right' // 向右旋转
  | 'flip_horizontal' // 水平翻转
  | 'flip_vertical' // 垂直翻转

export const Player_PlayView_Aspect_Config_List: Array<{
  label: string,
  value: ImageRatioType,
  width: number
  click_id: TVideoMenuClickId,
}> = [
    {
      label: '原始比例',
      value: ImageRatioType.Origin,
      width: 78,
      click_id: 'original',
    },
    {
      label: '铺满全屏',
      value: ImageRatioType.Stretch,
      width: 78,
      click_id: 'cover_screen',
    },
    {
      label: '16:9',
      value: ImageRatioType.Type_16_9,
      width: 52,
      click_id: '16:9',
    },
    {
      label: '4:3',
      value: ImageRatioType.Type_4_3,
      width: 52,
      click_id: '4:3',
    },
    {
      label: '21:9',
      value: ImageRatioType.Type_21_9,
      width: 52,
      click_id: '21:9',
    },
  ]

export type TPlayviewRotateConfig = {
  label: string,
  icon: string,
  value: string,
  click_id: TVideoMenuClickId,
}
export const Player_PlayView_Rotate_Config_List: Array<TPlayviewRotateConfig> = [
  {
    label: '向左旋转',
    icon: require('@root/common/assets/img/player/ic_player_rotateleft_30.svg'),
    value: 'turn_left',
    click_id: 'turn_left',
  },
  {
    label: '向右旋转',
    icon: require('@root/common/assets/img/player/ic_player_rotateright_30.svg'),
    value: 'turn_right',
    click_id: 'turn_right',
  },
  {
    label: '水平翻转',
    icon: require('@root/common/assets/img/player/ic_horizontal_30.svg'),
    value: 'flip_horizontal',
    click_id: 'flip_horizontal',
  },
  {
    label: '垂直翻转',
    icon: require('@root/common/assets/img/player/ic_vertical_30.svg'),
    value: 'flip_vertical',
    click_id: 'flip_vertical',
  },
]

export type TPlayerAction =
  'togglePlay' |
  'stop' |
  'progressRewind' | // 快进
  'progressBackward' | // 快退
  'prevEpisode' | // 上一集
  'nextEpisode'  // 下一集