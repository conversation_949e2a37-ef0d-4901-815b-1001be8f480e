const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
/**
 * AES加密的配置 
 * 1.密钥 
 * 2.偏移向量 
 * 3.算法模式ECB(这样才能分段解密)
 */

/**
 * @param data Buffer类型
* AES_128_CBC 加密 
* 128位 
* return base64
*/
function encryption(data, key) {
  if (typeof data === 'string') {
    data = Buffer.from(data);
  }
  var cipherChunks = [];
  var cipher = crypto.createCipheriv('aes-128-ecb', key, Buffer.from([]));
  cipher.setAutoPadding(false);
  let mod = data.length % 16;
  // 用空格补全data到16的倍数
  if (mod !== 0) {
    let addspace = '';
    for (let i = 0; i < (16 - mod); i++) {
      addspace += ' ';
    }
    data = Buffer.concat([data, Buffer.from(addspace)]);
    // console.log('#### ', data.length);
  }

  cipherChunks.push(cipher.update(data));
  cipherChunks.push(cipher.final());
  return Buffer.concat(cipherChunks);
}


/**
* 解密
* return utf8
*/
function decryption(data, key){
  var cipherChunks = [];
  var decipher = crypto.createDecipheriv('aes-128-ecb', key, Buffer.from([]));
  decipher.setAutoPadding(false);
  cipherChunks.push(decipher.update(data));
  cipherChunks.push(decipher.final());
  return Buffer.concat(cipherChunks);
}

/**
 * 加密文件
 */
function encryptFile(filename, outputfile, key) {
  file = fs.readFileSync(filename);
  let value = encryption(file, key);
  console.log('## encryptFile', filename, ' key:', key, ' outputfile:', outputfile);
  fs.writeFileSync(outputfile, value);
  let dec_value = decryption(value, key);
  console.log('## try decrypt value: ', file.toString('utf-8').slice(0, 10), dec_value.toString('utf-8').slice(0, 10));
}

// 从文件加载key:
function loadKey(file) {
  // key实际上就是PEM编码的字符串:
  return fs.readFileSync(path.resolve(__dirname, file), 'utf8');
}

/**
 * 解密文件
 */
function decryptFile(filename, outputfile, key) {
  file = fs.readFileSync(filename);
  console.log('## decryptFile', filename, ' key:', key)
  let value = decryption(file, key);
  fs.writeFileSync(outputfile, value);
}

/**
 * 加密头部, 使用RSA_PKCS1_PADDING
 * 加密后的长度固定为256
 */
function encryptHeader(data) {
  // let prvKey = loadKey('./rsa-prv.pem')
  let prvKey = '-----BEGIN RSA PRIVATE KEY-----\r\n' + 
  'MIIEowIBAAKCAQEAtMFIFQe+rBqT7yU6qyouJaI7PIJKqgIbJvoUq/9PTo1eWJVh\r\n'+
  'LEfOCAm6QQUUeV/KWAns4UyF4GbSabSvMp6v9ON6WPnQdAsj0Y0Hekenl2K5YBA/\r\n'+
  'WzAZJ2NUH1ACloQGlO5fxhAZ90cjNXCd2ZXB5JoWNbcuqSBAR0Jh0ACyfFuxB12x\r\n'+
  'MSvaB3HR48UBT8KkQvwc2Bqf2HqLH0OxLDwePqeKsLPtHWndK+VljcsWVTnaCbjD\r\n'+
  'EuVWoBiHQpSAgr68qHC5mIi1DivZ+2GnJiqRPzlaH0zQBD5H4X3GzSmEUOEmtVCo\r\n'+
  'VY+zIA6MLeG5lrTBhmQogXBpTEh/bNNfPqQzqQIDAQABAoIBAB0nzAOwYx4Jj3EU\r\n'+
  'lTkErbreRyXEclynyE9qKSlxHg6ymNQ7DBiDYIVFZGiWdC37uHyo5S+WE0YV77yj\r\n'+
  'K1tD42fhxVtSuD/viBCF6f3cTvkJMJOBK2b/BB+pUulWE4TJuPIO3R3qfPW44MH+\r\n'+
  '70coc8T24bw6VulqzVNxLqvJYQDT19aXTKLFLvxszNfJpwFOy5rsjeiOAny6kWYf\r\n'+
  '8YKzBjL/do41ZWNObNKicznQZMYOY8r8MoFRnb1RtQ9uNbZIUec3NE3aJauqFI8s\r\n'+
  'uRE6yGuaLc3ck05tRdW4v50UVanSmSd03GQc+4PWdG0fCg8s0pEN56O5SzQwP2UH\r\n'+
  'o5tZagECgYEA5eqiqzB/91x7rVmFrfteD5hEzfAOl04uoi7z87eeuJIh8lP4YD7M\r\n'+
  'Neel9zQwld33D5mOXfvjBMWKxMI8vRwO+HVW5NlpxIjj7g+XzWyTYy3xr1Lwz7Z4\r\n'+
  'nYeyh+quftzywI07lZ4Sb7fqYkbvSrRWuUoMKODu3mHltd+x/GHj4sECgYEAyULe\r\n'+
  'i2nZ4Tlph/xe+36uUv8iImSATJKaCBC0r6kOa9lIje69QVfmbEWlUPGl928etfct\r\n'+
  'PZp1As9zkmDgzhGow1z+eixBwE8KWyT7kAJLUOHrupY/vFN5LmIP5K4HAMA7uaHF\r\n'+
  '+8JtKvnbXEKMpNXAxZ4AEsR+kNAZa1b0qnLZUukCgYEAlcAz34ihcL4eGBSdC2sU\r\n'+
  'ItgsyG25MzfLC/vFDVLl4JXd4nZq6lU8sUeGQ/MMSjDewJxlrDVp8iuwbOOOqCXk\r\n'+
  'YlQbGse5Kjd3LhGd8EDt5sWAC4/cJY5WFVTTJc/ng93phxz2WEcy/u0tjoFhTqkT\r\n'+
  'a3AdZ2rKarIc6k8fIKGpyoECgYBOcQCZ57SiwqSt+p/ud45sDMJ6Ex+qOI1OTWyO\r\n'+
  '9l4HjNVpdD5RJHhLhM2NAdIE4Utf8qmwU+39HWvXBmk4B8v18N3CMlOiJ2xK39wo\r\n'+
  'YayHJragr4TB2urlOBUntOhH06Szkii/GiVAVce4aru0VxdeLlmq9ui5elId8vND\r\n'+
  'J6YqWQKBgCyWgBUyoLePEY/MfD/HNSlaBkWRCcb2wwzlsTNOcyff2JAKWBvZEa+X\r\n'+
  'INz5zHj2J/PL9Q3ZpDrsHU8WZ2FrAUvmEydYrgYsVaZ+ophAySVDHRtTCmYEFQyC\r\n'+
  'msnJwlwTPjBLWwYY1uD2Nd6KSlUEI47RyText0hkKRm9zclnWWK2\r\n'+
  '-----END RSA PRIVATE KEY-----\r\n';
  // 使用私钥加密:
  let enc_by_prv = crypto.privateEncrypt({ key: prvKey, padding: crypto.RSA_PKCS1_PADDING }, data);
  return enc_by_prv;
}

/**
 * 解密头部, 使用RSA_PKCS1_PADDING
 */
function decryptHeader(data) {
  // let pubKey = loadKey('./rsa-pub.pem')
  let pubKey = '-----BEGIN PUBLIC KEY-----\r\n'+
  'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMFIFQe+rBqT7yU6qyou\r\n'+
  'JaI7PIJKqgIbJvoUq/9PTo1eWJVhLEfOCAm6QQUUeV/KWAns4UyF4GbSabSvMp6v\r\n'+
  '9ON6WPnQdAsj0Y0Hekenl2K5YBA/WzAZJ2NUH1ACloQGlO5fxhAZ90cjNXCd2ZXB\r\n'+
  '5JoWNbcuqSBAR0Jh0ACyfFuxB12xMSvaB3HR48UBT8KkQvwc2Bqf2HqLH0OxLDwe\r\n'+
  'PqeKsLPtHWndK+VljcsWVTnaCbjDEuVWoBiHQpSAgr68qHC5mIi1DivZ+2GnJiqR\r\n'+
  'PzlaH0zQBD5H4X3GzSmEUOEmtVCoVY+zIA6MLeG5lrTBhmQogXBpTEh/bNNfPqQz\r\n'+
  'qQIDAQAB\r\n'+
  '-----END PUBLIC KEY-----';
  let dec_by_pub = crypto.publicDecrypt({ key: pubKey, padding: crypto.RSA_PKCS1_PADDING }, data);
  return dec_by_pub;
}

/**
 * 对header里面的files的字符串计算并比较md5, 杜绝篡改
 */
function calculateMd5(str) {
  let md5 = crypto.createHash('md5');
  md5.update(str);
  return md5.digest("hex");
}

module.exports.encryptFile = encryptFile;
module.exports.decryptFile = decryptFile;
module.exports.encryption = encryption;
module.exports.decryption = decryption;
module.exports.decryptHeader = decryptHeader;
module.exports.encryptHeader = encryptHeader;
module.exports.calculateMd5 = calculateMd5;