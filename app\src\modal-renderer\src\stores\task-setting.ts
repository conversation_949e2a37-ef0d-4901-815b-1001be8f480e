import { computed, ref } from 'vue'

import { defineStore } from 'pinia'

import type { TaskSettings } from '@root/modal-renderer/types/new-task.type'

/**
 * Task Settings Store
 * 专门管理任务设置的状态和逻辑
 */
export const useTaskSettingStore = defineStore('taskSetting', () => {
  // ===== 状态管理 =====

  /**
   * 任务设置组件的显示状态
   */
  const taskSettingVisible = ref(false)

  /**
   * 默认任务设置
   */
  const defaultTaskSettings = ref<TaskSettings>({
    downloadUrl: '',
    openAfterDownload: false,
    downloadFromOriginalOnly: false,
    threadCount: 5,
    loginFtpServer: false,
    ftpUsername: '',
    ftpPassword: '',
    customTaskName: '',
  })

  /**
   * 任务设置映射表 - 存储每个URL的自定义设置
   */
  const taskSettingsMap = ref<Map<string, TaskSettings>>(new Map())

  // ===== 计算属性 =====

  /**
   * 是否有自定义设置的任务
   */
  const hasCustomSettings = computed(() => taskSettingsMap.value.size > 0)

  /**
   * 所有任务设置的摘要信息
   */
  const taskSettingsSummary = computed(() => {
    const summary: Array<{ url: string; settings: TaskSettings }> = []

    taskSettingsMap.value.forEach((settings, url) => {
      summary.push({
        url,
        settings: { ...settings },
      })
    })

    return summary
  })

  // ===== Actions =====

  /**
   * 显示任务设置弹窗
   */
  const showTaskSetting = () => {
    taskSettingVisible.value = true
  }

  /**
   * 隐藏任务设置弹窗
   */
  const hideTaskSetting = () => {
    taskSettingVisible.value = false
  }

  /**
   * 获取任务设置
   * @param url 任务URL
   * @returns 任务设置
   */
  const getTaskSettings = (url: string): TaskSettings => {
    const customSettings = taskSettingsMap.value.get(url)
    if (customSettings) {
      return { ...customSettings }
    }
    // 返回默认设置的副本
    return { ...defaultTaskSettings.value }
  }

  /**
   * 设置任务设置
   * @param url 任务URL
   * @param settings 设置内容
   */
  const setTaskSettings = (url: string, settings: TaskSettings): void => {
    taskSettingsMap.value.set(url, { ...settings })
    console.log(`任务 ${url} 设置已更新:`, settings)
  }

  /**
   * 清除指定任务的设置
   * @param url 任务URL
   */
  const clearTaskSettings = (url: string): void => {
    taskSettingsMap.value.delete(url)
    console.log(`任务 ${url} 的设置已清除`)
  }

  /**
   * 清除所有任务设置
   */
  const clearAllTaskSettings = (): void => {
    taskSettingsMap.value.clear()
    console.log('所有任务设置已清除')
  }

  /**
   * 设置默认任务设置
   * @param settings 默认设置
   */
  const setDefaultTaskSettings = (settings: TaskSettings): void => {
    defaultTaskSettings.value = { ...settings }
    console.log('默认任务设置已更新:', settings)
  }

  /**
   * 获取默认任务设置
   * @returns 默认任务设置
   */
  const getDefaultTaskSettings = (): TaskSettings => {
    return { ...defaultTaskSettings.value }
  }

  /**
   * 获取任务的自定义名称
   * @param url 任务URL
   * @returns 自定义任务名称，如果没有则返回空字符串
   */
  const getCustomTaskName = (url: string): string => {
    const settings = taskSettingsMap.value.get(url)
    return settings?.customTaskName || ''
  }

  /**
   * 设置任务的自定义名称
   * @param url 任务URL
   * @param customName 自定义名称
   */
  const setCustomTaskName = (url: string, customName: string): void => {
    const currentSettings = getTaskSettings(url)
    const updatedSettings = {
      ...currentSettings,
      customTaskName: decodeURIComponent(customName)
    }
    setTaskSettings(url, updatedSettings)
    console.log(`任务 ${url} 的自定义名称已设置为: ${customName}`)
  }

  /**
   * 清除任务的自定义名称
   * @param url 任务URL
   */
  const clearCustomTaskName = (url: string): void => {
    const currentSettings = getTaskSettings(url)
    const updatedSettings = {
      ...currentSettings,
      customTaskName: ''
    }
    setTaskSettings(url, updatedSettings)
    console.log(`任务 ${url} 的自定义名称已清除`)
  }

  /**
   * 应用设置到所有任务（将默认设置应用到所有任务并清除个性化设置）
   * @param settings 要应用的设置
   */
  const applySettingsToAllTasks = (settings: TaskSettings): void => {
    // 更新默认设置
    setDefaultTaskSettings(settings)

    // 清除所有个性化设置，让所有任务都使用新的默认设置
    clearAllTaskSettings()

    console.log('设置已应用到所有任务:', settings)
  }

  /**
   * 处理任务设置取消事件
   */
  const handleTaskSettingCancel = () => {
    console.log('TaskSetting 取消')
    hideTaskSetting()
  }

  /**
   * 处理应用到所有任务事件
   * @param settings 要应用的设置
   */
  const handleTaskSettingApplyAll = (settings: TaskSettings) => {
    console.log('TaskSetting 应用到所有任务:', settings)

    // 应用设置到所有任务
    applySettingsToAllTasks(settings)

    // 隐藏设置弹窗
    hideTaskSetting()
  }

  /**
   * 重置 Store 状态
   */
  const resetStore = () => {
    taskSettingVisible.value = false

    // 重置为默认的任务设置
    defaultTaskSettings.value = {
      downloadUrl: '',
      openAfterDownload: false,
      downloadFromOriginalOnly: false,
      threadCount: 5,
      loginFtpServer: false,
      ftpUsername: '',
      ftpPassword: '',
      customTaskName: '',
    }

    taskSettingsMap.value.clear()

    console.log('TaskSetting Store 已重置')
  }

  return {
    // 状态
    taskSettingVisible,
    defaultTaskSettings,
    taskSettingsMap,

    // 计算属性
    hasCustomSettings,
    taskSettingsSummary,

    // 方法
    showTaskSetting,
    hideTaskSetting,
    getTaskSettings,
    setTaskSettings,
    clearTaskSettings,
    clearAllTaskSettings,
    setDefaultTaskSettings,
    getDefaultTaskSettings,
    getCustomTaskName,
    setCustomTaskName,
    clearCustomTaskName,
    applySettingsToAllTasks,
    handleTaskSettingCancel,
    handleTaskSettingApplyAll,
    resetStore,
  }
})
