<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useThrottleFn, useDebounceFn } from '@vueuse/core'
import dayjs from 'dayjs'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import PlayItem from './components/PlayItem.vue'
import Empty from './components/Empty.vue'
import Loading from '@root/common/components/ui/loading/index.vue'
import { useUserStore } from '@/stores/user'
import { useRoute } from 'vue-router'
import { useKeepAliveHooks } from '@/hooks/useKeepAliveHooks'
import { requestHelper } from '@/utils/request'
import { env } from '@root/common/env'
import { PlaybackRecordHelper } from '@root/common/link-hub/impl/playback-record-helper'
import { EventDispatcher } from '@root/common/link-hub/client/event-dispatcher'
import MessageBox from '@root/common/components/ui/message-box'
import Message from '@root/common/components/ui/message'
import { AplayerStack } from '@root/common/player/client/aplayer-stack'
// import { AplayerMedia } from '@root/common/player/client/aplayer-media';

defineOptions({
  name: 'recent_play'
})

const route = useRoute()
const isHidden = ref(false)

const pageSize = ref(30)

const userStore = useUserStore();

const isLogin = computed(() => {
  return !!userStore?.userInfo?.id
})

const playbackRecords = ref<ThunderClientAPI.dataStruct.dataModals.PlaybackRecord[]>([])

function groupRecordsByDate(records: any[]) {
  const groups: { title: string, items: ThunderClientAPI.dataStruct.dataModals.PlaybackRecord[] }[] = []
  const now = dayjs()
  const today = now.startOf('day')
  const yesterday = now.subtract(1, 'day').startOf('day')
  const todayGroup: any[] = []
  const yesterdayGroup: any[] = []
  const earlierGroup: any[] = []
  
  records?.forEach(item => {
    if (!item || !item.update_time) return
    
    // 将时间戳字符串转换为数字
    const timestamp = Number(item.update_time)
    if (isNaN(timestamp)) return
    
    const itemDate = dayjs(timestamp)
    
    // 判断是否是今天（从今天00:00:00到现在）
    if (itemDate.isSame(today, 'day')) {
      todayGroup.push(item)
    } 
    // 判断是否是昨天（昨天00:00:00到23:59:59）
    else if (itemDate.isSame(yesterday, 'day')) {
      yesterdayGroup.push(item)
    } 
    // 其他时间归为更早
    else {
      earlierGroup.push(item)
    }
  })
  
  if (todayGroup.length) groups.push({ title: '今天', items: todayGroup })
  if (yesterdayGroup.length) groups.push({ title: '昨天', items: yesterdayGroup })
  if (earlierGroup.length) groups.push({ title: '更早', items: earlierGroup })
  return groups
}

const recentPlayGroups = computed(() => groupRecordsByDate(playbackRecords.value))
const hasMore = ref(true)
const loading = ref(false)

// 在 data 部分新增状态
const nextPageToken = ref('')

async function fetchPlaybackRecords(isReload = false) {
  try {
    // 重置分页状态
    if (isReload) {
      nextPageToken.value = ''
    }

    const params = {
      limit: pageSize.value,
      next_page_token: nextPageToken.value
    }

    const res = await PlaybackRecordHelper.getInstance().getRecordsFromServer(params)
    console.log('[recentplay] fetchPlaybackRecords res:', res)
    
    // 处理返回数据
    if (res?.records) {
      if (isReload) {
        // 首次加载或强制刷新时替换数据
        playbackRecords.value = res.records.events || []
      } else {
        // 加载更多时追加数据
        playbackRecords.value.push(...res.records.events || [])
      }
      
      // 更新分页标记
      nextPageToken.value = res.records.next_page_token || ''
      hasMore.value = !!nextPageToken.value
    }

    return res
  } catch (err) {
    console.error('[recentplay] fetchPlaybackRecords error:', err)
    throw err
  }
}

async function initRecentPlay(forceReload = false) {
  if (loading.value) return
  
  loading.value = true
  try {
    await fetchPlaybackRecords(forceReload)
  } catch (err) {
    console.error('[recentplay] init error:', err)
  } finally {
    loading.value = false
  }
}

// 用户登录状态变化时重新加载数据
watch(isLogin, () => {
  console.log('[recentplay] 用户登录状态变化，重新加载数据')
  // 延迟加载，等待登录态变化生效
  nextTick(() => {
    initRecentPlay(true)
  })
})

async function loadMore() {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  try {
    await fetchPlaybackRecords()
  } catch (err) {
    console.error('[recentplay] loadMore error:', err)
  } finally {
    loading.value = false
  }
}

// 新增滚动容器引用和位置状态
const scrollContainer = ref<HTMLElement>()
const scrollPosition = ref(0)

// 新增状态管理
const needsRefresh = ref(false) // 标记是否需要刷新
const isAtTop = ref(true) // 标记当前是否在顶部
const isActive = ref(true) // 标记页面是否活跃

// 修改滚动处理函数
function handleScroll(e: Event) {
  const el = e.target as HTMLElement
  const scrollTop = el.scrollTop

  if (scrollContainer.value) {
    scrollPosition.value = scrollContainer.value.scrollTop
  }

  // 更新是否在顶部状态（增加5px缓冲）
  isAtTop.value = scrollTop <= 5
  
  // 如果滚动到顶部且需要刷新
  if (isAtTop.value && needsRefresh.value) {
    doRefresh()
    return
  }
  const isNearBottom = el.scrollHeight - scrollTop - el.clientHeight < 50
  // 原有加载更多逻辑
  if (isNearBottom && !loading.value && hasMore.value) {
    loadMore()
  }
  
}

// 统一刷新方法
const doRefresh = useDebounceFn(() => {
  console.log('[recentplay] 执行延迟刷新')
  initRecentPlay(true)
  needsRefresh.value = false
}, 500)

// 修改事件处理函数
const handleRefreshEvent = () => {
  if (!isActive.value) {
    // 页面不活跃时直接刷新
    scrollPosition.value = 0
    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = 0
    }
    doRefresh()
  } else if (isAtTop.value) {
    // 如果在顶部直接刷新
    doRefresh()
  } else {
    // 否则标记需要刷新
    needsRefresh.value = true
    console.log('[recentplay] 页面不在顶部，已标记需要刷新')
  }
}

// 统一使用一个处理函数
const handlePlaybackRecordEvent = useDebounceFn(() => {
  handleRefreshEvent()
}, 1000)

// 播放记录删除事件处理
// const handlePlaybackRecordRemoved = useDebounceFn((obj: ThunderClientAPI.dataStruct.event.PlaybackRecordEventDetail_RecordRemoved) => {
//   console.log('[recentplay] 监听到播放记录删除事件:', obj)
//   // 重新加载数据
//   initRecentPlay(true)
// }, 2000)

// 播放记录新增事件处理
// const handlePlaybackRecordAdded = useDebounceFn((obj: ThunderClientAPI.dataStruct.event.PlaybackRecordEventDetail_RecordAdded) => {
//   console.log('[recentplay] 监听到播放记录新增事件:', obj)
//   // 重新加载数据，获取最新记录
//   initRecentPlay(true)
// }, 2000)

// 播放记录更新事件处理
// const handlePlaybackRecordUpdated = useDebounceFn((obj: ThunderClientAPI.dataStruct.event.PlaybackRecordEventDetail_RecordUpdated) => {
//   console.log('[recentplay] 监听到播放记录更新事件:', obj)
//   // 重新加载数据，获取更新后的记录
//   initRecentPlay(true)
// }, 2000)

// 播放记录总数变化事件处理
// const handlePlaybackRecordTotalCountChanged = (obj: ThunderClientAPI.dataStruct.event.PlaybackRecordEventDetail_TotalCountChanged) => {
//   console.log('[recentplay] 监听到播放记录总数变化:', obj)
//   // 当总数变化时，重新加载数据
//   initRecentPlay(true)
// }

// const handleMediaClosedEvent = (m: AplayerMedia) => {
//   console.log('[recentplay] 播放关闭事件:', m)
//   handleRefreshEvent()
// }

// 删除播放记录
async function handleRemove(recordId: string) {
  // 从播放记录列表中删除对应的记录
  playbackRecords.value = playbackRecords.value.filter(item => item.record_id !== recordId)
}
  

// 清除所有播放记录
async function handleClearAll() {
  if (!playbackRecords.value.length) {
    Message({ message: '暂无播放记录！', type: 'info' })
    return
  }
  
  try {
    await MessageBox.confirm(
      isLogin.value ? '所有设备上的播放记录将同步清空' : '',
      '确认清除全部播放记录？',
      {
        type: 'warning',
        showCancelButton: true,
        showConfirmButton: true,
        confirmButtonText: '清除',
        cancelButtonText: '取消',
      }
    )
    
    console.log('[recentplay] 开始清除所有播放记录')
    // const res = await PlaybackRecordHelper.getInstance().removeAllPlaybackRecords({
    //   ignoreEvent: false
    // })
    const baseUrl =  env === 'prod' ? 'https://api-shoulei-ssl.xunlei.com'  :  'http://test.api-shoulei-ssl.xunlei.com'
    const res = await requestHelper.request({
      url: `${baseUrl}/mediahub/v1/events:clear`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      withCredentials: true,
    })
    console.log('[recentplay] 清除所有记录成功:', res)
    
    // 清除后重新初始化，需要强制刷新
    initRecentPlay(true)
  } catch (e) {
    console.log('[recentplay] 用户取消清除操作或操作失败')
  }
}

// 初始化页面和事件监听
const initPageAndEvents = () => {
  console.log('[recentplay] 初始化页面和事件监听')
  
  // 初始化播放记录数据
  initRecentPlay(true)
  
  // 添加事件监听
  EventDispatcher.getInstance().attachEvent('PlaybackRecordEvent_RecordRemoved', handlePlaybackRecordEvent)
  EventDispatcher.getInstance().attachEvent('PlaybackRecordEvent_RecordAdded', handlePlaybackRecordEvent)
  EventDispatcher.getInstance().attachEvent('PlaybackRecordEvent_RecordUpdated', handlePlaybackRecordEvent)
  // EventDispatcher.getInstance().attachEvent('PlaybackRecordEvent_TotalCountChanged', handlePlaybackRecordTotalCountChanged)

  AplayerStack.GetInstance().attachMediaClosedEvent(handlePlaybackRecordEvent)
}

// 清理事件监听
const cleanupEvents = () => {
  console.log('[recentplay] 清理事件监听')
  
  EventDispatcher.getInstance().detachEvent('PlaybackRecordEvent_RecordRemoved', handlePlaybackRecordEvent)
  EventDispatcher.getInstance().detachEvent('PlaybackRecordEvent_RecordAdded', handlePlaybackRecordEvent)
  EventDispatcher.getInstance().detachEvent('PlaybackRecordEvent_RecordUpdated', handlePlaybackRecordEvent)
  // EventDispatcher.getInstance().detachEvent('PlaybackRecordEvent_TotalCountChanged', handlePlaybackRecordTotalCountChanged)

  AplayerStack.GetInstance().detachMediaCloseEvent(handlePlaybackRecordEvent)
}

// 使用 useKeepAliveHooks 监听页面激活和失活
useKeepAliveHooks(route, {
  onMounted: () => {
    console.log('[recentplay] 页面首次挂载')
    initPageAndEvents()
  },
  onActivated: (route) => {
    isActive.value = true
    console.log('[recentplay] 页面重新激活，刷新播放记录')
    nextTick(() => {
      if (scrollContainer.value && scrollPosition.value > 10) {
        scrollContainer.value.scrollTop = scrollPosition.value
        console.log('[recentplay] 恢复滚动位置:', scrollPosition.value)
      } else {
        initRecentPlay(true)
      }
    })
    // 重新激活时强制刷新数据
    // initRecentPlay(true)
  },
  onDeactivated: (route) => {
    isActive.value = false
    console.log('[recentplay] 页面被失活，保存当前状态')
    // 可以在这里保存滚动位置等状态
  },
  onUnmounted: () => {
    console.log('[recentplay] 页面卸载，清理事件监听')
    cleanupEvents()
  }
})

</script>

<template>
  <div class="recent-play">
    <div class="recent-play-header">
      <span class="recent-play-title">
        最近播放
        <i :class="!isHidden ? 'xl-icon-general-eye-m' : 'xl-icon-general-eyeinvisible-m'"
          @click="isHidden = !isHidden"></i>
      </span>
      <DropdownMenu :items="[{
        label: '清除所有播放记录',
        key: 'clear-all',
        icon: 'xl-icon-general-clearout-l'
      }]" :align="'end'" @select="handleClearAll">
        <Button :is-icon="true" variant="ghost">
          <i class="xl-icon-general-more-l"></i>
        </Button>
      </DropdownMenu>
    </div>
    <div class="recent-play-content" ref="scrollContainer" data-scroll-container @scroll="handleScroll">
      <div v-if="!isHidden" class="recent-play-group">
        <template v-for="group in recentPlayGroups" :key="group.title">
          <div class="recent-play-group-title">{{ group.title }}</div>
          <PlayItem v-for="item in group.items" :key="item.id" :item="item" @remove="handleRemove" />
        </template>
        <template v-if="recentPlayGroups.length > 0">
          <div v-if="loading" class="recent-play-loading">加载中...</div>
          <div v-else-if="!hasMore" class="recent-play-nomore">没有更多了</div>
        </template>
        <template v-else>
          <Loading v-if="loading" />
          <Empty v-else type="empty" :is-login="isLogin" />
        </template>
      </div>
      <Empty v-else type="hidden" :is-login="false" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.recent-play {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--background-background-container, #fff);

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 40px;
  }

  &-title {
    font-size: 26px;
    font-weight: 700;
    color: var(--font-font-1, #272E3B);
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      color: var(--font-font-2, #4E5769);
      cursor: pointer;
    }
  }

  &-content {
    flex: 1;
    min-height: 0;
    overflow: auto;
    margin: 22px 0;
    padding: 0 22px;
  }

  &-group {
    display: flex;
    flex-direction: column;
    &-title {
      position: sticky;
      top: 0;
      z-index: 2;
      background: var(--background-background-container, #fff);
      padding-left: 18px;
      line-height: 40px;
      text-align: left;
      font-size: 12px;
      color: var(--font-font-3, #898E97);
    }
  }

  &-loading, &-nomore {
    text-align: center;
    color: #aaa;
    padding: 16px 0;
    font-size: 14px;
  }
}
</style>