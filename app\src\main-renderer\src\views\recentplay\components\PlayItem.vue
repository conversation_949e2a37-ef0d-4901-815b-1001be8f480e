<script setup lang="ts">
import type { PropType } from 'vue'
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import ContextMenu from '@root/common/components/ui/context-menu/index.vue'
import { defineProps, defineEmits, computed, ref } from 'vue'
import dayjs from 'dayjs'
import { PlaybackRecordHelper } from '@root/common/link-hub/client/playback-record-helper'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper';
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'

const emit = defineEmits<{
  (e: 'play'): void
  (e: 'download'): void
  (e: 'remove', recordId: string): void
}>()

// const playDiviceMap = {
//   'pc': 'device-pc',
//   '电脑': 'device-pc',
//   'cloud': 'device-cloud',
//   'remote': 'device-remotedevices',
//   'tv': 'device-tv',
//   'TV': 'device-tv',
//   '电视': 'device-tv',
//   'ipad': 'device-ipad',
//   '平板': 'device-ipad',
//   'mobile': 'device-mobile',
//   '手机': 'device-mobile',
// }

const props = defineProps({
  item: {
    type: Object as PropType<ThunderClientAPI.dataStruct.dataModals.PlaybackRecord>,
    required: true
  }
})

// 图片加载状态
const imageError = ref(false)

// 判断是否应该显示默认图标
const shouldShowDefaultIcon = computed(() => {
  const thumbnail = props.item.thumbnail_link
  return !thumbnail || thumbnail === 'default' || imageError.value
})

// 播放进度
const progress = computed(() => {
  return Math.ceil(Number(props.item.current_progress_time || 0) / Number(props.item.video_total_time || 1) * 100) || Number(props.item.progress?.percentage || 0)
})

// 处理图片加载错误
function handleImageError() {
  imageError.value = true
}

function formatDisplayTime(time: string | number) {
  // 处理13位时间戳字符串
  const timestamp: number = Number(time)
  const d = dayjs(timestamp)
  const now = dayjs()
  if (d.isSame(now, 'day')) {
    return `今天 ${d.format('HH:mm')}`
  } else if (d.isSame(now.subtract(1, 'day'), 'day')) {
    return `昨天 ${d.format('HH:mm')}`
  } else if (d.isSame(now, 'year')) {
    return d.format('MM-DD HH:mm')
  } else {
    return d.format('YYYY-MM-DD HH:mm')
  }
}

function formatDuration(time: number | string) {
  // ms时间
  if (time) {
    const seconds = Number(time || 0);
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = Math.floor(seconds % 60);

    if (h === 0) {
      return [m, s].map(v => v.toString().padStart(2, '0')).join(':');
    }
    return [h, m, s].map(v => v.toString().padStart(2, '0')).join(':');
  }

  return '00:00';
}

// 点击播放
async function handlePlay() {
  try {
    ConsumeManagerNs.consumePlaybackInfo({
      ...props.item,
    })
    emit('play')
  } catch (error) {
    console.error('播放失败:', error)
  }
}

// 下载功能
async function handleDownload() {
  try {
    if (!props.item.params?.url) {
      console.warn('下载链接为空，无法下载')
      return
    }

    ThunderNewTaskHelperNS.showPreCreateTaskWindow([{
      url: props?.item.params?.url
    }]);

    // 这里可以调用下载API，暂时使用console.log模拟
    console.log('开始下载:', props.item.name, props.item.params?.url)
    emit('download')
  } catch (error) {
    console.error('下载失败:', error)
  }
}

// 清除记录
async function handleRemove() {
  try {
    const helper = PlaybackRecordHelper.getInstance()
    const result = await helper.removePlaybackRecords({
      recordIds: [props.item.id],
      ignoreEvent: false
    })

    if (result && result.error.result == 0) {
      console.log('清除记录成功:', props.item.id)
      emit('remove', props.item.id)
    } else {
      console.error('清除记录失败')
    }
  } catch (error) {
    console.error('清除记录失败:', error)
    // 可以添加用户提示
  }
}

// 右键菜单项
const contextMenuItems = computed(() => {
  const items: any[] = []

  // 如果有URL，显示下载选项
  if (props.item.params?.url) {
    items.push({
      key: 'download',
      label: '下载',
      icon: 'xl-icon-general-download-l"'
    })
  }

  // 清除记录选项
  items.push({
    key: 'remove',
    label: '清除记录',
    icon: 'xl-icon-general-clearout-m',
  })

  return items
})

// 处理右键菜单选择
function handleContextMenuSelect(key: string) {
  switch (key) {
    case 'download':
      handleDownload()
      break
    case 'remove':
      handleRemove()
      break
  }
}

// function getSavedTags(item: any) {
//   if (item.sync_state === 2) return ['本地', '云盘']
//   if (item.sync_state === 1) return ['云盘']
//   return ['本地']
// }

</script>

<template>
  <ContextMenu :items="contextMenuItems" @select="handleContextMenuSelect" contentClass="play-item-context-menu">
    <div class="play-item" @dblclick="handlePlay">
      <div class="play-item-left">
        <div class="play-item-left-img-wrap" @click.stop="handlePlay">
          <!-- 默认图标 -->
          <div v-if="shouldShowDefaultIcon" class="play-item-left-default-icon">
            <i class="file-icon-type file-type-video"></i>
          </div>
          <!-- 真实图片 -->
          <img v-else :src="item.thumbnail_link" @error="handleImageError" />
          <div class="play-item-left-progress-bar">
            <div class="play-item-left-progress-inner" :style="{ width: progress + '%' }"></div>
          </div>
        </div>
        <div class="play-item-left-time">
          <span>{{ formatDuration(item.current_progress_time || item.progress?.played_length || 0) }}</span>
        </div>
        <div class="play-item-left-play-icon" @click.stop="handlePlay">
          <i class="xl-icon-general-play-m" v-tooltip="'播放'"></i>
        </div>
        <!-- <div class="play-item-left-bt-tips">
          <Tooltip :side-offset="10">
            <template #trigger>
              <i class="xl-icon-general-episode-m"></i>
            </template>
<template #content>
              bt文件集合
            </template>
</Tooltip>
</div> -->
      </div>
      <div class="play-item-right">
        <Tooltip :side-offset="10" :max-width="398" align="center" as-child :trigger-by-pointer="true"
          :delay-duration="1000" contentClass="play-item-right-title-tooltip">
          <template #trigger>
            <div @click.stop="handlePlay" class="play-item-right-title">
              {{ item.name || '--' }}
            </div>
          </template>
          <template #content>
            {{ item.name || '--' }}
          </template>
        </Tooltip>

        <div class="play-item-right-info">
          <div class="play-item-right-info-play">
            <!-- <i :class="`xl-icon-${playDiviceMap[item.device?.client_id || 'pc']}`"></i> -->
            <img :src="item.device?.icon_link" alt="device-icon" class="play-item-right-info-play-icon">
            <span>{{ formatDisplayTime(item.update_time) }} · 已看{{ progress + '%' }}</span>
          </div>
          <!-- <div class="play-item-right-info-save">
            <span>已存至</span>
            <div v-for="tag in getSavedTags(item)" :key="tag" class="play-item-right-info-tag">
              {{ tag }}
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </ContextMenu>
</template>

<style scoped lang="scss">
.play-item {
  display: flex;
  width: 100%;
  height: 104px;
  z-index: 2;
  padding: 12px 12px 12px 18px;
  border-radius: var(--border-radius-L, 12px);

  &-left {
    width: 142px;
    height: 80px;
    position: relative;
    margin-right: 18px;
    flex-shrink: 0;

    &-img-wrap {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
      cursor: pointer;
      border-radius: var(--border-radius-M, 8px);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    &:hover {
      .play-item-left-play-icon {
        cursor: pointer;
        visibility: visible;
      }
    }

    &-default-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      border-radius: var(--border-radius-M, 8px);
      background: #EFF6FE;

      i {
        width: 40px;
        height: 40px;
      }
    }

    &-progress-bar {
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      height: 4px;
      overflow: hidden;
    }

    &-progress-inner {
      height: 100%;
      background: var(--primary-primary-default, #226DF5);
      border-radius: 2px;
      transition: width 0.3s;
    }

    &-time {
      position: absolute;
      bottom: 4px;
      right: 4px;
      display: flex;
      height: 18px;
      padding: 4px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      border-radius: var(--border-radius-S, 6px);
      background: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80));
      color: var(--font-font-light, #FFF);
      font-size: 12px;
      font-weight: 500;
    }

    &-play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 32px;
      height: 32px;
      border-radius: 100%;
      background: url('@root/common/assets/img/glass.svg') no-repeat center center / 100% 100%;
      backdrop-filter: blur(5px);
      fill: rgba(255, 255, 255, 0.22);
      display: flex;
      align-items: center;
      justify-content: center;
      visibility: hidden;
      transition: visibility 0.1s ease-in-out;

      i {
        color: var(--font-font-light, #FFF);
      }
    }

    &-bt-tips {
      position: absolute;
      top: 4px;
      right: 4px;
      border-radius: var(--border-radius-S, 6px);
      background: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80));
      height: 22px;
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      visibility: hidden;
      transition: visibility 0.1s ease-in-out;

      i {
        color: var(--font-font-light, #FFF);
      }
    }

    &:hover {
      .play-item-left-bt-tips {
        visibility: visible;
      }
    }
  }

  &-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;

    &-info {
      display: flex;
      align-items: center;
      gap: 12px;

      color: var(--font-font-3, #898E97);
      font-size: 12px;
      line-height: 20px;
      font-weight: 400;

      &-play,
      &-save {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      &-play-icon {
        width: 14px;
        height: 14px;
        object-fit: contain;
      }

      &-tag {
        border-radius: 3px;
        border: 0.5px solid var(--font-font-3, #86909C);
        display: flex;
        height: 14px;
        align-items: center;
        justify-content: center;
        padding: 0 3px;
        color: var(--font-font-3, #86909C);
        font-size: 10px;
        line-height: 12px;
      }
    }
  }
}
</style>

<style lang="scss">
.play-item-context-menu {
  border-radius: 10px;
  border: 1px solid var(--border-border-2, #E5E6EB);
  background: var(--background-background-elevated, #FFF);
  /* boxShadow 2 */
  box-shadow: 0px 3px 16px 0px rgba(0, 0, 0, 0.05), 0px 12px 45px 2px rgba(0, 0, 0, 0.08);
  z-index: 99;
}

.play-item-right-title-tooltip {
  z-index: 99;
}

.play-item-right-title {
  word-break: break-all;
  color: var(--font-font-1, #272E3B);
  text-align: left;
  font-size: 13px;
  font-weight: 400;
  line-height: 22px;
  cursor: pointer;

  @include lineClamp(2);

  &:hover {
    color: var(--primary-primary-default, #226DF5);
  }
}
</style>