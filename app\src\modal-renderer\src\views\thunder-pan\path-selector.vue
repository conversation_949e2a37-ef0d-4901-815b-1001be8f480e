<script lang="ts" setup>
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import Button from '@root/common/components/ui/button/index.vue'
import Tree from '@root/common/components/ui/tree/tree.vue'

import { computed, nextTick, onMounted, reactive, ref, unref, useTemplateRef } from 'vue';
import { PopUpNS } from '@root/common/pop-up';
import * as PopUpTypes from '@root/common/pop-up/types';
import { config } from '@root/common/config/config';
import { API_FILE } from '@root/common/thunder-pan-manager/pan-sdk/types';
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client';
import { FileNameForbidReg, FileNameForbidRegString, SYSTEM_FOLDER_TYPE_LIST } from '@root/common/thunder-pan-manager/pan-sdk/utils/file';
import { useDateFormat, useNow } from '@vueuse/core';
import { sleep } from '@root/common/thunder-pan-manager/pan-sdk/utils/basic';

export interface IFileTreeRootFileSet {
  id?: string
  name?: string
  space?: string
}

export interface IPathSelectorPropsOptions {
  title?: string
  rootFile: IFileTreeRootFileSet
  enableNewFolder?: boolean
  cancelButtonText?: string
  confirmButtonText?: string
}

export interface ITreePathData {
  id: string
  name: string
}

export interface IPathSelectorNodeItem {
  id: string
  label: string
  type: string
  expanded?: boolean
  expandable?: boolean
  loading?: boolean
  hasChildren: boolean
  children: IPathSelectorNodeItem[]
  _originFile: API_FILE.DriveFile
  _editable?: boolean
  _parentPath: ITreePathData[]
  _alreadyGotChildren: boolean
}

const props = defineProps<{ options: IPathSelectorPropsOptions }>()

const scrollContentRef = ref()
const dialogVisible = ref(true)
const selectedItem = ref<IPathSelectorNodeItem>()
const pathData = reactive<{
  list: IPathSelectorNodeItem[]
}>({
  list: []
})
const isCreatingFolder = ref(false)
const isConfirmCreatingFolder = ref(false)
const createFolderNewName = ref('')
const createFolderInput = useTemplateRef('$createFolderInput')
let createFolderCallback: null | Function = null

const titleText = computed(() => props.options.title ?? '选择云盘保存路径')
const cancelButtonText = computed(() => props.options.cancelButtonText ?? '取消')
const confirmButtonText = computed(() => props.options.confirmButtonText ?? '确认')

async function handleClose () {
  const currentWindow = PopUpNS.getCurrentWindow();
  await currentWindow.close(PopUpTypes.Action.Close);
}

async function handleConfirm () {
  if (isConfirmCreatingFolder.value) {
    createFolderCallback = () => {
      handleConfirmClose()
    }
  } else {
    handleConfirmClose()
  }
}

async function handleConfirmClose () {
  if (!selectedItem.value) return

  const currentWindow = PopUpNS.getCurrentWindow();
  const response = {
    path: selectedItem.value._parentPath,
    selectedItem: {
      id: selectedItem.value.id,
      name: selectedItem.value.label,
    }
  }
  await currentWindow.close(PopUpTypes.Action.OK, response)
}

async function handleCancel () {
  const currentWindow = PopUpNS.getCurrentWindow();
  await currentWindow.close(PopUpTypes.Action.Cancel);
}

async function handleCreateFolder () {
  if (!selectedItem.value || isCreatingFolder.value || isConfirmCreatingFolder.value) return
  isCreatingFolder.value = true

  const nowDate = useDateFormat(useNow(), 'YYYYMMDD-HHmmss').value
  const newFolderId = `new_folder_${Date.now()}`
  const newFolderName = `新建文件夹-${nowDate}`

  selectedItem.value.children.unshift({
    id: newFolderId,
    label: newFolderName,
    type: 'folder',
    expanded: false,
    expandable: false,
    children: [],
    hasChildren: false,
    _originFile: {},
    _editable: true,
    _parentPath: [ ...selectedItem.value._parentPath, { id: selectedItem.value.id, name: selectedItem.value.label } ],
    _alreadyGotChildren: false,
  })
  selectedItem.value.loading = false
  selectedItem.value.expanded = true
  selectedItem.value.expandable = true
  selectedItem.value.hasChildren = true
  selectedItem.value._alreadyGotChildren = true
  createFolderNewName.value = newFolderName

  await nextTick()
  if (createFolderInput.value) {
    createFolderInput.value.focus()
    createFolderInput.value.select()
  }
}

function handleCreateFolderNameChange (event: Event) {
  createFolderNewName.value = (event.target as HTMLInputElement).value
}

// 监听输入框失焦、回车事件
async function createFolderConfirm () {
  const newName = unref(createFolderNewName)

  if (!newName) {
    window.__VueGlobalProperties__.$message({
      message: '文件夹名称不能为空，请输入名称',
      type: 'warning'
    })
    return
  }
  if (FileNameForbidReg.test(newName)) {
    window.__VueGlobalProperties__.$message({
      message: `名称不能包含${FileNameForbidRegString}等特殊字符`,
      type: 'warning'
    })
    return
  }
  if (isConfirmCreatingFolder.value) return
  isConfirmCreatingFolder.value = true

  const parent = selectedItem.value!
  const res = await ThunderPanClientSDK.getInstance().createFolder(parent.id, newName, {
    params: {
      space: parent._originFile.space ?? ''
    }
  })

  if (res.success && res.data && res.data.file) {
    const targetNode = parent.children[0]

    targetNode.id = res.data.file.id!
    targetNode.label = res.data.file.name!
    targetNode._originFile = res.data.file
    targetNode._editable = false
    isCreatingFolder.value = false
    ThunderPanClientSDK.getInstance().appendNewFilesToDrive(res.data.file.parent_id!, [ res.data.file ])
    // 选中当前文件夹
    await nextTick()
    selectedItem.value = targetNode

    if (createFolderCallback) {
      createFolderCallback()
    }
  } else {
    window.__VueGlobalProperties__.$message({
      message: res.error.error_description || '创建文件夹失败，请稍后再试',
      type: 'error'
    })
  }

  await sleep(1000)
  createFolderNewName.value = ''
  isConfirmCreatingFolder.value = false
}

function handleDialogUpdateOpen (isOpen: boolean) {
  dialogVisible.value = isOpen
  if (!isOpen) {
    handleClose()
  }
}

function handleTreeItemClick (node: IPathSelectorNodeItem) {
  console.log('handleTreeItemClick', node)
  // 正在创建文件夹，阻止选中其他文件夹
  if (isCreatingFolder.value || isConfirmCreatingFolder.value) return

  selectedItem.value = node
}

async function handleTreeItemExpand (node: IPathSelectorNodeItem) {
  console.log('handleTreeItemExpand', node)
  // 已经有子节点则不处理
  if (node._alreadyGotChildren || node.loading) return
  node.loading = true

  try {
    const childrenList = await getCurrentFolderChildren(node._originFile, [ ...node._parentPath, { id: node.id, name: node.label } ])

    if (childrenList.length) {
      node.children = childrenList
      node.expanded = true
    } else {
      // 当前节点没有子文件夹
      node.expandable = false
      node.hasChildren = false
    }
    node.loading = false
    node._alreadyGotChildren = true
  } catch (err) {
    node.loading = false
  } finally {
    await nextTick()
    scrollContentRef.value?.ps.update()
  }
}

async function getCurrentFolderChildren (parentFile: API_FILE.DriveFile, parentPath: ITreePathData[]) {
  const list: IPathSelectorNodeItem[] = []
  const isInSafeBoxFolder = parentFile.space === 'SPACE_SAFE'
  const res = await ThunderPanClientSDK.getInstance().getFolderList(parentFile.id!, parentFile.space!, '', {
    headers: {
      'space-authorization': isInSafeBoxFolder ? (await ThunderPanClientSDK.getInstance().getSafeBoxToken()).data : '',
    }
  })

  if (res.success && res.data && res.data.files) {
    res.data.files.map((file, index) => {
      // 过滤【保险箱、流畅播】文件夹
      if (['SAFE', 'FAVORITE'].includes(file.folder_type!)) return;
      // 默认排序
      let order = index;
      const sysIdx = SYSTEM_FOLDER_TYPE_LIST.indexOf(file.folder_type!);
      if (sysIdx > -1) {
        order = Number.MIN_SAFE_INTEGER + sysIdx;
      }

      return {
        ...file,
        __order__: order,
      }
    })
    .filter(file => !!file)
    .sort((a, b) => a.__order__! - b.__order__!)
    .forEach(file => {
      list.push({
        id: file.id!,
        label: file.name!,
        type: 'folder',
        expanded: false,
        expandable: true,
        children: [],
        hasChildren: true,
        _originFile: file,
        _parentPath: parentPath,
        _alreadyGotChildren: false,
      })
    })
  }

  return list
}

onMounted(async () => {
  (window as any).__config__ = config;

  pathData.list = [
    {
      id: props.options.rootFile.id!,
      label: props.options.rootFile.name!,
      type: 'folder',
      expanded: true,
      expandable: true,
      children: [],
      hasChildren: true,
      _originFile: props.options.rootFile,
      _parentPath: [],
      _alreadyGotChildren: false,
    }
  ]
  handleTreeItemExpand(pathData.list[0])
  selectedItem.value = pathData.list[0]
})
</script>

<template>
  <Dialog
    :open="dialogVisible"
    :title="titleText"
    :show-title-icon="false"
    :preventDefaultClose="true"
    @close.stop="handleClose"
    @update:open="handleDialogUpdateOpen"
  >
    <div class="path-selector-container none-draggable">
      <PerfectScrollbar ref="scrollContentRef" class="path-selector-wrapper" :options="{ swipeEasing: false, suppressScrollX: true }">
        <Tree
          :data="pathData.list"
          :selected-node="selectedItem"
          @node-click="handleTreeItemClick"
          @node-expanded="handleTreeItemExpand"
        >
          <!-- <template #icon="{ node }">
            <img v-if="node.icon" :src="node.icon" alt="">
            <i v-else class="file-icon-type file-type-folder"></i>
          </template> -->

          <template #node="{ node }">
            <div class="path-selector-node">
              <div class="node-icon">
                <img v-if="node.icon" :src="node.icon" alt="">
                <i v-else class="file-icon-type file-type-folder"></i>
              </div>

              <template v-if="node._editable">
                <input
                  ref="$createFolderInput"
                  class="node-input"
                  type="text"
                  :value="node.label"
                  @change="handleCreateFolderNameChange"
                  @blur="createFolderConfirm"
                  @keyup.enter="createFolderConfirm"
                />
              </template>

              <span v-else class="node-title">{{ node.label }}</span>
            </div>
          </template>
        </Tree>
      </PerfectScrollbar>
    </div>

    <template #actions>
      <div class="path-selector-actions none-draggable">
        <Button
          size="lg"
          variant="secondary"
          :class="{
            'new-folder-btn': true,
            'is-visible': options.enableNewFolder
          }"
          @click="handleCreateFolder"
        >
          新建文件夹
        </Button>

        <div class="right-actions">
          <Button size="lg" variant="default" @click="handleConfirm">{{ confirmButtonText }}</Button>
          <Button size="lg" variant="secondary" @click.stop="handleCancel">{{ cancelButtonText }}</Button>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<style>
.dialog-content {
  width: 100% !important;
  height: 100% !important;
  -webkit-app-region: drag;
}
</style>

<style lang="scss" scoped>
.path-selector-container {
  margin: 20px 0;
  height: 320px;
  padding: 16px;
  border-radius: var(--border-radius-L);
  border: 1px solid var(--border-border-2);

  .path-selector-wrapper {
    height: 100%;
  }

  /* 调整树形控件中图标的大小 */
  :deep(.td-tree-node__image-icon .file-icon-type) {
    width: 40px;
    height: 40px;
    zoom: 0.5;
    vertical-align: middle;
  }

  .lazy-node {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .loading-text {
    color: var(--font-font-3);
    font-size: 12px;
  }
}

.path-selector-node {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;

  .node-icon {
    img, i {
      width: 40px;
      height: 40px;
      zoom: 0.5;
      vertical-align: middle;
    }
  }

  .node-title {
    -webkit-line-clamp: 1;
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    white-space: pre-wrap;
  }

  .node-input {
    width: 456px;
    height: 28px;
    border: 1px solid var(--primary-primary-default);
    border-radius: var(--border-radius-S);
    padding: 3px 4px;
  }
}

.path-selector-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .new-folder-btn {
    visibility: hidden;

    &.is-visible {
      visibility: visible;
    }
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    button {
      min-width: 100px;
    }
  }
}
</style>
