<script setup lang="ts">
import Button from '@root/common/components/ui/button/index.vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'

export interface IToolbarOperationType {
  key: string
  icon?: string;
  text: string;
  disabled?: boolean;
}

export interface IMenuItem {
  label: string
  icon?: string
  disabled?: boolean
  children?: IMenuItem[]
  hasSeparator?: boolean
  key: string
  rightText?: string
  checkbox?: boolean
  checked?: boolean
}

const props = withDefaults(defineProps<{
  isShow: boolean
  primaryOperation?: IToolbarOperationType[]
  dropdownMenuList?: IMenuItem[]
}>(), {
  isShow: false
})

const emit = defineEmits<{
  (e: 'menuItemClick', key: string): void
}>()

function handleItemClick (key: string) {
  emit('menuItemClick', key)
}
</script>

<template>
  <div v-if="isShow" class="toolbar">
    <Button
      v-for="btn in primaryOperation"
      size="sm"
      variant="ghost"
      :key="btn.text"
      :disabled="btn.disabled"
      @click="handleItemClick(btn.key)"
    >
      <i :class="btn.icon"></i>
      {{ btn.text }}
    </Button>
  </div>

  <div v-if="isShow && dropdownMenuList && dropdownMenuList.length" class="toolbar-more">
    <DropdownMenu :items="dropdownMenuList" @select="handleItemClick">
      <Button variant="outline" size="sm" :is-icon="true">
        <i class="xl-icon-more"></i>
      </Button>
    </DropdownMenu>
  </div>
</template>

<style lang="scss" scoped>
.toolbar {
  border: 1px solid var(--border-border-2);
  border-radius: var(--border-radius-M);
  margin-left: 8px;
}

.toolbar-more {
  margin-left: 8px;
}
</style>
