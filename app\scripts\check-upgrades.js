#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 推荐的最新稳定版本
const RECOMMENDED_VERSIONS = {
  node: '22.15.0',
  npm: '10.8.0'
};

function compareVersions(a, b) {
  const aParts = a.replace(/^v/, '').split('.').map(Number);
  const bParts = b.replace(/^v/, '').split('.').map(Number);
  
  for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
    const numA = aParts[i] || 0;
    const numB = bParts[i] || 0;
    if (numA > numB) return 1;
    if (numA < numB) return -1;
  }
  return 0;
}

function isVersionOlder(current, recommended) {
  return compareVersions(current, recommended) < 0;
}

const currentNodeVersion = process.version.replace(/^v/, '');
const currentNpmVersion = process.env.npm_version || 'unknown';

console.log('🔍 检查版本升级建议...\n');

console.log('📊 当前版本状态:');
console.log(`Node.js: v${currentNodeVersion}`);
console.log(`npm: ${currentNpmVersion !== 'unknown' ? 'v' + currentNpmVersion : '未知'}\n`);

console.log('🎯 推荐的最新稳定版本:');
console.log(`Node.js: v${RECOMMENDED_VERSIONS.node} (LTS)`);
console.log(`npm: v${RECOMMENDED_VERSIONS.npm}\n`);

let needsUpgrade = false;

// 检查 Node.js
if (isVersionOlder(currentNodeVersion, RECOMMENDED_VERSIONS.node)) {
  console.log('🔄 Node.js 升级建议:');
  console.log('  推荐升级到最新的 LTS 版本以获得更好的性能和安全性');
  console.log('  下载地址: https://nodejs.org/');
  console.log('  或使用 nvm: nvm install node --lts\n');
  needsUpgrade = true;
} else {
  console.log('✅ Node.js 版本已是最新稳定版本\n');
}

// 检查 npm
if (currentNpmVersion !== 'unknown' && isVersionOlder(currentNpmVersion, RECOMMENDED_VERSIONS.npm)) {
  console.log('🔄 npm 升级建议:');
  console.log('  升级命令: npm install -g npm@latest');
  console.log('  升级后可享受更快的安装速度和更好的依赖解析\n');
  needsUpgrade = true;
} else if (currentNpmVersion !== 'unknown') {
  console.log('✅ npm 版本已是最新稳定版本\n');
}

if (!needsUpgrade) {
  console.log('🎉 你的开发环境已经是最新的稳定版本!');
} else {
  console.log('💡 提示: 升级后请重新运行 npm install 确保所有依赖正确安装');
}

console.log('\n📝 版本信息:');
console.log('  - Node.js 18.x LTS (Active LTS) 支持到 2025-04-30');
console.log('  - Node.js 20.x LTS (Active LTS) 支持到 2026-04-30'); 
console.log('  - Node.js 22.x LTS (Current LTS) 支持到 2027-04-30'); 