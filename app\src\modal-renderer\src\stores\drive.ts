import { ref } from 'vue'
import { defineStore } from 'pinia'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import type { IGetCurrentUserQuotasResponse } from '@root/thunder-pan-plugin/src/utils/business-helper'

/**
 * Drive Store
 * 专门管理云盘相关状态和操作
 */
export const useDriveStore = defineStore('drive', () => {
  // ===== 状态管理 =====

  /**
   * 用户云盘配额信息
   */
  const quotas = ref<IGetCurrentUserQuotasResponse | null>(null)

  /**
   * 是否正在加载配额信息
   */
  const isLoadingQuotas = ref<boolean>(false)

  // ===== Actions =====

  /**
   * 获取用户云盘配额信息
   */
  const getQuotas = async (): Promise<IGetCurrentUserQuotasResponse | null> => {
    if (isLoadingQuotas.value) {
      console.log('[DriveStore] 正在加载配额信息，跳过重复请求')
      return quotas.value
    }

    try {
      isLoadingQuotas.value = true
      console.log('[DriveStore] 开始获取用户云盘配额信息')
      
      const response = await ThunderPanClientSDK.getInstance().getCurrentUserDriveQuotas()
      
      if (response.success && response.data) {
        quotas.value = response.data
        console.log('[DriveStore] 成功获取云盘配额信息:', response.data)
        return response.data
      } else {
        console.error('[DriveStore] 获取云盘配额信息失败:', response.error)
        return null
      }
    } catch (error) {
      console.error('[DriveStore] 获取云盘配额信息异常:', error)
      return null
    } finally {
      isLoadingQuotas.value = false
    }
  }

  /**
   * 清空配额信息缓存
   */
  const clearQuotas = () => {
    console.log('[DriveStore] 清空配额信息缓存')
    quotas.value = null
  }

  /**
   * 获取当前缓存的配额信息
   */
  const getCurrentQuotas = (): IGetCurrentUserQuotasResponse | null => {
    return quotas.value
  }

  // ===== 返回值 =====
  return {
    // 状态
    quotas,
    isLoadingQuotas,
    
    // Actions
    getQuotas,
    clearQuotas,
    getCurrentQuotas,
  }
}) 