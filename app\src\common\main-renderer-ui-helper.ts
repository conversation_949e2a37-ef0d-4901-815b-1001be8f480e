import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { mainRendererContext } from '@xunlei/node-net-ipc/dist/ipc-base';
import { reactive } from 'vue'
import { RouteHistoryManager_Channels } from '@root/common/constant';
import { IMessageOptions } from 'components/ui/message';

export interface IHeaderQuickFunctionIconData {
  enable?: boolean
}

export interface IHeaderQuickFunctionIconStatusSetting {
  forward?: IHeaderQuickFunctionIconData
  backward?: IHeaderQuickFunctionIconData
  refresh?: IHeaderQuickFunctionIconData
}

export enum EMainRenderUIHelperEventKey {
  FORWARD = 'MainRenderUIHelper_trigger_forward',
  BACKWARD = 'MainRenderUIHelper_trigger_backward',
  REFRESH = 'MainRenderUIHelper_trigger_refresh',
}

export enum EMainRenderUIHelperApiKey {
  SET_STATUS = 'SetHeaderQuickFunctionIconStatus',
  SHOW_TOAST = 'MainRenderer_ShowToast'
}

export class MainRenderUIHelper {
  private static _instance: MainRenderUIHelper

  static getInstance () {
    if (MainRenderUIHelper._instance) {
      return MainRenderUIHelper._instance
    } else {
      MainRenderUIHelper._instance = new MainRenderUIHelper()
      return MainRenderUIHelper._instance
    }
  }

  forward: IHeaderQuickFunctionIconData = reactive<IHeaderQuickFunctionIconData>({ enable: false })
  backward: IHeaderQuickFunctionIconData = reactive<IHeaderQuickFunctionIconData>({ enable: false })
  refresh: IHeaderQuickFunctionIconData = reactive<IHeaderQuickFunctionIconData>({ enable: true })

  init () {
    this._registerIPC()
  }

  reset () {
    client.unRegisterFunctions([EMainRenderUIHelperApiKey.SET_STATUS])
    this.forward.enable = false
    this.backward.enable = false
    this.refresh.enable = true
  }

  handleForward () {
    if (this.forward.enable) {
      console.log('触发前进事件')
      client.broadcastEvent(EMainRenderUIHelperEventKey.FORWARD)
    } else {
      console.log('前进按钮已禁用')
    }
  }

  handleBackward () {
    if (this.backward.enable) {
      console.log('触发后退事件')
      client.broadcastEvent(EMainRenderUIHelperEventKey.BACKWARD)
    } else {
      console.log('后退按钮已禁用')
    }
  }

  handleRefresh () {
    if (this.refresh.enable) {
      console.log('触发刷新事件')
      client.broadcastEvent(EMainRenderUIHelperEventKey.REFRESH)
    } else {
      console.log('刷新按钮已禁用')
    }
  }

  updateQuickFunctionIconStatus (setting: IHeaderQuickFunctionIconStatusSetting) {
    if (setting.forward !== undefined) {
      this.forward.enable = setting.forward.enable
    }
    if (setting.backward !== undefined) {
      this.backward.enable = setting.backward.enable
    }
    if (setting.refresh !== undefined) {
      this.refresh.enable = setting.refresh.enable
    }
  }

  setHeaderStatusRemote (setting: IHeaderQuickFunctionIconStatusSetting) {
    return client.callRemoteClientFunction(mainRendererContext, EMainRenderUIHelperApiKey.SET_STATUS, setting)
  }

  navigateToPath (path: string, query: Record<string, string>) {
    return client.callRemoteClientFunction(mainRendererContext, RouteHistoryManager_Channels.navigateToPath, path, query)
  }

  showToast (options: IMessageOptions) {
    window.__VueGlobalProperties__.$message(options)
  }

  // 获取当前按钮状态
  getCurrentStatus(): IHeaderQuickFunctionIconStatusSetting {
    return {
      forward: { enable: this.forward.enable },
      backward: { enable: this.backward.enable },
      refresh: { enable: this.refresh.enable }
    }
  }

  private _registerIPC () {
    client.registerFunctions({
      [EMainRenderUIHelperApiKey.SET_STATUS]: (ctx: unknown, setting: IHeaderQuickFunctionIconStatusSetting) => {
        console.log('收到远程状态更新:', setting)
        return this.updateQuickFunctionIconStatus(setting)
      },
      [EMainRenderUIHelperApiKey.SHOW_TOAST]: (ctx: unknown, options: IMessageOptions) => {
        return this.showToast(options)
      },
    })
  }
}
