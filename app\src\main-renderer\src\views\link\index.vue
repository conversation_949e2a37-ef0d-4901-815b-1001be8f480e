<script setup lang="ts">
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import { LinkRecordHelper } from '@root/common/link-hub/client/link-record-helper'
import { EventDispatcher } from '@root/common/link-hub/client/event-dispatcher'
import LinkItem from './components/LinkItem.vue'
import Empty from './components/Empty.vue'
import { ref, computed, useTemplateRef, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useKeepAliveHooks } from '@/hooks/useKeepAliveHooks'
import { LottieAnimation } from 'lottie-web-vue'
import { PopUpNS } from '@root/common/pop-up'
import Loading from '@root/common/components/ui/loading/index.vue'
import LoadingSpinner from '@root/common/components/ui/loading-spinner/index.vue'
import { useInfiniteScroll } from '@vueuse/core'
import { useUserStore } from '@/stores/user'
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'
import dayjs from 'dayjs'
import { LinkHubHelper } from '@root/common/link-hub/impl/link-hub-helper'
import { RecycleScroller } from 'vue-virtual-scroller'

defineOptions({
  name: 'link'
})

const userStore = useUserStore();
const el = useTemplateRef<RecycleScroller>('el')

const router = useRouter()
const route = useRoute()

const activeTab = ref('all')
const tabs = ref<ThunderClientAPI.dataStruct.dataModals.LabelInfo[]>([
  {
    key: 'all',
    name: '全部链接',
    value: '0',
    type: 'LABEL_LINK_STAT',
  },
])

// 高亮项
const highlightedLinkId = ref<string>('')

// Sync status states
const syncingStatus = ref<'not_start' | 'syncing' | 'success' | 'failed'>('not_start')
const syncedCount = ref(0)
const pendingCount = ref(0)
const syncedTime = ref<string>('')

// 分页相关状态
const page = ref(1)
const pageSize = ref(30)
const loading = ref(false)
const initialLoading = ref(true)
const totalLinkCount = ref<Record<string, number>>({ 'all': 0 })

const linkList = ref<ThunderClientAPI.dataStruct.dataModals.LinkRecord[]>([])
const curLink = ref<ThunderClientAPI.dataStruct.dataModals.LinkRecord>()


const isLoggedIn = computed(() => {
  return !!userStore?.getUid
})

const hasMore = computed(() => {
  if (linkList.value.length === 0) return true

  return linkList.value.length < totalLinkCount.value[activeTab.value]
})

// 获取数据的方法
const fetchData = async (pageNum: number) => {
  // todo: 临时方案，上层重新刷新label。by weiliang
  if (activeTab.value !== 'all') {
    await LinkRecordHelper.getInstance().fetchFirstPage({
      dataSrcConfig: { dataSourceType: 'LABEL', dataSourceKey: activeTab.value },
      firstLoadCount: pageSize.value,
      reload: true
    })
  }

  const beginIndex = (pageNum - 1) * pageSize.value

  const data = await LinkRecordHelper.getInstance().loadLinkRecords({
    dataSrcConfig: {
      dataSourceType: activeTab.value === 'all' ? 'ALL_LINKS' : 'LABEL',
      dataSourceKey: activeTab.value === 'all' ? '' : activeTab.value
    },
    rangeInfo: {
      range: { beginIndex, count: pageSize.value },
    },
    reload: false,
  })

  return data?.records?.links || []
}

const loadMore = async () => {
  console.log('loadMore begin!!!!!!!!!!!!!!!!!!!!!!!!!!', loading.value, hasMore.value)
  if (loading.value || !hasMore.value) return
  loading.value = true
  try {
    const linkData = await fetchData(page.value)

    console.log('从客户端获取的linkData', linkData)

    if (linkData.length > 0) {
      const existingIds = new Set(linkList.value.map(item => item.id))
      const newItems = linkData.filter(item => !existingIds.has(item.id))
      linkList.value = [...linkList.value, ...newItems]
      page.value++
    }

  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
    initialLoading.value = false
  }
}

// Computed sync status text
const syncStatus = computed(() => {
  if (!isLoggedIn.value) {

    return {
      text: `共 ${pendingCount.value} 项未同步`,
      icon: 'ic_unsync'
    }
  }

  if (syncingStatus.value === 'syncing') {
    return {
      text: `同步中, ${syncedCount.value} 项完成，剩余 ${pendingCount.value} 项`,
    }
  }

  if (syncingStatus.value === 'failed') {
    let text = ''
    if (syncedCount.value === 0) {
      text = '抱歉同步失败，请稍后点击重试'
    } else {
      text = `${syncedCount.value} 项已同步，剩余 ${pendingCount.value} 项同步失败，请稍后点击重试`
    }
    return {
      text,
      icon: 'ic_unsync'
    }
  }

  if (syncingStatus.value === 'success') {
    return {
      text: `共 ${syncedCount.value} 项，已同步到本账号`,
      icon: 'ic_synced'
    }
  }


  return {
    text: '未开启链接同步',
    icon: 'ic_unsync'
  }

})

const syncTipContent = computed(() => {
  if (syncingStatus.value === 'syncing') {
    return {
      title: '同步中',
      content: '提示：链接关联的【下载记录】与【播放记录】会同步至你的账号，支持多设备使用'
    }
  }

  if (syncingStatus.value === 'success') {
    return {
      title: `上次同步 今天 ${syncedTime.value}`,
      content: '提示：链接关联的【下载记录】与【播放记录】会同步至你的账号，支持多设备使用'
    }
  }

  if (syncingStatus.value === 'failed') {
    let text = ''
    if (syncedCount.value === 0) {
      text = '抱歉同步失败，请稍后点击重试'
    } else {
      text = `${syncedCount.value} 项已同步，剩余 ${pendingCount.value} 项同步失败，请稍后点击重试`
    }
    return {
      title: text,
      content: '提示：链接关联的【下载记录】与【播放记录】会同步至你的账号，支持多设备使用'
    }
  }

  return {}

})

const handleTotalCountChanged = (obj: ThunderClientAPI.dataStruct.event.LinkRecordEventDetail_TotalCountChanged) => {
  console.log('LinkRecordEvent_TotalCountChanged', obj)
  if (obj.parentLinkId === '' || obj.parentLinkId === activeTab.value) {
    totalLinkCount.value[obj.parentLinkId || 'all'] = obj?.totalCount ?? 0
  }

}

const handleSyncToServerStatus = (obj: ThunderClientAPI.dataStruct.event.LinkRecordEventDetail_SyncToServerStatus) => {
  console.log('LinkRecordEvent_SyncToServerStatus', obj)
  const { failedCount, syncedCount: syncedCountValue, hasFinished, taskCount } = obj
  if (hasFinished) {
    if (failedCount && failedCount > 0) {
      syncingStatus.value = 'failed'
    } else {
      syncingStatus.value = 'success'
      syncedTime.value = dayjs().format('YYYY-MM-DD HH:mm')
    }

    syncedCount.value = syncedCountValue

  } else {
    if (isLoggedIn.value) {
      syncingStatus.value = 'syncing'
    } else {
      syncingStatus.value = 'not_start'
    }

    syncedCount.value = syncedCountValue
    pendingCount.value = taskCount - syncedCountValue
  }
}

const reloadLinkList = async () => {
  // 重置分页状态
  page.value = 1
  linkList.value = []
  initialLoading.value = true

  // 获取第一页数据
  await fetchFirstPageLink()

}

const fetchFirstPageLink = async () => {
  try {
    const res = await LinkRecordHelper.getInstance().fetchFirstPage({
      dataSrcConfig: { dataSourceType: 'ALL_LINKS' },
      firstLoadCount: pageSize.value,
      reload: false,
    })
    console.log('fetchFirstPageLink res', res)

    // 加载第一页数据
    await loadMore()

    // 如果初始加载完成，且有高亮ID但未找到对应项，尝试通过API获取
    if (highlightedLinkId.value && linkList.value.length > 0) {
      // 检查当前列表中是否已经有这个链接
      handleLocateLink(highlightedLinkId.value)
    }

    return res
  } catch (error) {
    console.error('fetchFirstPageLink error', error)
    initialLoading.value = false
    return null
  }
}


const handleDataSouceChanged = (obj: ThunderClientAPI.dataStruct.event.LinkHubEventDetail_DataSouceChanged) => {
  console.log('LinkHubEvent_DataSouceChanged', obj)
  reloadLinkList()
}

const handleRecordAdded = (obj: ThunderClientAPI.dataStruct.event.LinkRecordEventDetail_RecordAdded) => {
  console.log('LinkRecordEvent_RecordAdded', obj)
  reloadLinkList()
}
const handleLabelInfoChanged = (obj: ThunderClientAPI.dataStruct.event.LinkRecordEventDetail_LabelInfoChanged) => {
  console.log('LinkRecordEvent_LabelInfoChanged', obj)

  if (obj?.errInfo?.result === 0 && !obj?.errInfo?.message) {
    tabs.value = obj.labels
  }
}

const initPage = async () => {
  console.log('linkhub onMounted!!!!!!!!!!!!!!!!!!!!!!!!!!', '开始请求第一页链接数据')

  await reloadLinkList()

  EventDispatcher.getInstance().attachEvent('LinkRecordEvent_TotalCountChanged', handleTotalCountChanged)
  EventDispatcher.getInstance().attachEvent('LinkRecordEvent_SyncToServerStatus', handleSyncToServerStatus)
  EventDispatcher.getInstance().attachEvent('LinkHubEvent_DataSouceChanged', handleDataSouceChanged)
  EventDispatcher.getInstance().attachEvent('LinkRecordEvent_RecordAdded', handleRecordAdded)
  EventDispatcher.getInstance().attachEvent('LinkRecordEvent_LabelInfoChanged', handleLabelInfoChanged)
}


// 使用 useKeepAliveHooks 监听页面生命周期
useKeepAliveHooks(route, {
  onActivated: (route) => {
    console.log('linkhub onActivated!!!!!!!!!!!!!!!!!!!!!!!!!!')
    initPage()
  },
  onMounted: () => {
    console.log('linkhub onMounted!!!!!!!!!!!!!!!!!!!!!!!!!!')
    initPage()
  },
  // Clean up interval when component is unmounted
  onUnmounted: () => {
    console.log('linkhub onUnmounted!!!!!!!!!!!!!!!!!!!!!!!!!!')

    EventDispatcher.getInstance().detachEvent('LinkRecordEvent_TotalCountChanged', handleTotalCountChanged)
    EventDispatcher.getInstance().detachEvent('LinkRecordEvent_SyncToServerStatus', handleSyncToServerStatus)
    EventDispatcher.getInstance().detachEvent('LinkHubEvent_DataSouceChanged', handleDataSouceChanged)
    EventDispatcher.getInstance().detachEvent('LinkRecordEvent_RecordAdded', handleRecordAdded)
    EventDispatcher.getInstance().detachEvent('LinkRecordEvent_LabelInfoChanged', handleLabelInfoChanged)
  }

})

const handlePlay = (link: ThunderClientAPI.dataStruct.dataModals.LinkRecord) => {
  ConsumeManagerNs.consumeLink(link)
}

const handleSelectLink = (link: ThunderClientAPI.dataStruct.dataModals.LinkRecord) => {
  curLink.value = link
  console.log('handleSelectLink', link)
  if (link.is_dir) {
    // 多文件链接打开二级页面
    router.push(`/link/${link.id}`)
  } else {
    // 单文件，如果是视频，拉起播放器，如果是非视频，打开文件, 否则提示
    // showTipsDialog.value = true
    handlePlay(link)
  }
}

const handleUpdateStatus = async (link: ThunderClientAPI.dataStruct.dataModals.LinkRecord, status: ThunderClientAPI.dataStruct.dataModals.LinkStatus) => {
  console.log('handleUpdateStatus', link, status)

  await LinkRecordHelper.getInstance().updateLinkRecord({
    link: {
      ...link,
      status: status
    }
  })

  linkList.value = linkList.value.map(item => {
    if (item.url_hash === link.url_hash) {
      return { ...item, status: status }
    }
    return item
  })
}

const handleDeleteSuccess = async (link: ThunderClientAPI.dataStruct.dataModals.LinkRecord) => {
  linkList.value = linkList.value.filter(item => item.url_hash !== link.url_hash)
}

const handleRenameSuccess = async (newName: string, curLink: ThunderClientAPI.dataStruct.dataModals.LinkRecord) => {
  linkList.value = linkList.value.map(item => {
    if (item.url_hash === curLink.url_hash) {
      return { ...item, name: newName }
    }
    return item
  })
}

const handleToLogin = () => {
  PopUpNS.showLoginDlg();
}

const handleSyncClick = () => {
  if (!isLoggedIn.value) {
    handleToLogin()
  }
  LinkHubHelper.getInstance().syncLinksToServer({ userData: '' }).then((result) => {
    console.log('开始同步链接到服务器:', result)

    // 如果同步任务id为空，则表示本次没有同步
    if (!result.syncTaskId) {
      syncingStatus.value = 'success'
      syncedTime.value = dayjs().format('YYYY-MM-DD HH:mm')
      syncedCount.value = 0
      pendingCount.value = 0
    }

  }).catch((error) => {
    console.error('同步链接到服务器失败', error)
  })
}

const handleTabClick = (key: string) => {
  activeTab.value = key
  reloadLinkList()
}

useInfiniteScroll(
  el,
  () => {
    // load more
    console.log('loadMore begin!!!!!!!!!!!!!!!!!!!!!!!!!!', page.value)
    loadMore()
  },
  {
    distance: 10,
    canLoadMore: () => {
      return hasMore.value && !loading.value && totalLinkCount.value[activeTab.value] > pageSize.value
    },
  }
)

// 处理从搜索结果跳转过来的链接定位
const handleLocateLink = async (linkId: string) => {

  // 检查当前列表中是否已经有这个链接
  const existingLinkIndex = linkList.value.findIndex(link =>
    link.id === linkId
  )
  console.log('handleLocateLink1', linkId, existingLinkIndex)

  if (existingLinkIndex !== -1) {
    // 如果已经在列表中，滚动到该项
    setTimeout(() => {
      if (el.value) {
        el.value.scrollToItem(existingLinkIndex)
      }
    }, 200)
  } else {
    // 如果不在列表中，通过API获取该链接
    try {
      const response = await LinkHubHelper.getInstance().getLinks({
        config: {
          ids: [linkId],
          withPlaybackInfo: true,
        },
        limitCount: 1,
        userData: '',
        reload: false,
      })
      console.log('handleLocateLink response', response)

      // 检查响应中是否有链接数据
      if (response && response.records && response.records.links && response.records.links.length > 0) {
        // 将获取到的链接放在列表最前面
        const targetLink = response.records.links[0]

        // 将链接插入到列表最前面
        linkList.value = [targetLink, ...linkList.value]

        // 滚动到顶部的新项
        nextTick(() => {
          if (el.value) {
            el.value.scrollToItem(0)
          }
        })
      }
    } catch (error) {
      console.error('获取链接详情失败:', error)
    }
  }
}

// 简化监听route.query.linkId的逻辑
watch(() => route.query.linkId, (newLinkId) => {
  if (newLinkId) {
    console.log('route.query.linkId changed', newLinkId, linkList.value?.length)
    // 3. 保存高亮ID
    highlightedLinkId.value = newLinkId as string
    // 若是没有数据，则交由初次获取数据那里去定位处理
    if (!initialLoading.value) {
      handleLocateLink(newLinkId as string)
    }
  } else {
    highlightedLinkId.value = ''
  }
}, { immediate: true })
</script>

<template>
  <div class="link-container">
    <div v-if="route.path === '/link'" class="link-list-container">
      <div class="link-list-header">
        <div class="link-list-header-title">
          全部链接
        </div>
        <Tooltip :align="'start'" :disabled="!isLoggedIn || syncingStatus === 'not_start'">
          <template #trigger>
            <div class="link-list-header-sync" @click="handleSyncClick">
              <inline-svg v-if="syncStatus?.icon && syncingStatus !== 'syncing'"
                :src="require(`@root/common/assets/img/${syncStatus.icon}.svg`)" />

              <LottieAnimation v-if="syncingStatus === 'syncing'" class="link-list-header-sync-icon"
                :animation-data="require('@root/common/assets/lottie/ic-syncing.json')" :auto-play="true"
                :loop="true" />

              <span>{{ syncStatus?.text }}</span>
            </div>
          </template>
          <template #content>
            <div class="link-list-sync-tips">
              <span class="link-list-sync-tips-title">{{ syncTipContent.title }}</span>
              <span class="link-list-sync-tips-info">{{ syncTipContent.content }}</span>
            </div>
          </template>
        </Tooltip>
      </div>
      <!-- <div class="link-list-guest-tips" v-if="!isLoggedIn">
        <div class="link-list-guest-tips-content">
          <span>
            您当前处于访客状态，登录同步其他设备链接，激活更多功能和服务
            <a @click="handleToLogin">立即登录</a>
          </span>
          <i class="xl-icon-close" />
        </div>
      </div> -->
      <div class="link-list-tabs">
        <Button v-for="tab in tabs" :key="tab.key" :variant="activeTab === tab.key ? 'default' : 'secondary'" size="sm"
          @click="handleTabClick(tab.key)">
          {{ `${tab.name} · ${tab.value}` }}
        </Button>
      </div>

      <Loading v-if="initialLoading" />

      <div class="link-list-content">
        <RecycleScroller ref="el" v-if="!initialLoading" class="link-list-wrapper" key-field="url_hash"
          :items="linkList" :item-size="94" data-scroll-container>

          <template v-slot="{ item }">
            <LinkItem @update-status="(status) => handleUpdateStatus(item, status)"
              @select-link="() => handleSelectLink(item)" @delete-success="() => handleDeleteSuccess(item)"
              @rename-success="(newName) => handleRenameSuccess(newName, item)"
              @open-link="() => handleSelectLink(item)" @play-link="() => handlePlay(item)" :key="item.url_hash"
              :link="item" :highlighted="!!highlightedLinkId && highlightedLinkId === item.id" />
          </template>

          <template #empty>
            <Empty type="linkList" v-if="linkList.length === 0" />
          </template>

          <template #after>
            <div class="link-list-loading" v-if="loading">
              <LoadingSpinner />
            </div>
          </template>
        </RecycleScroller>

      </div>
    </div>
    <router-view v-slot="{ Component }">
      <component :is="Component" :link-data="curLink"
        @rename-success="(newName: string) => handleRenameSuccess(newName, curLink!)"
        @delete-success="handleDeleteSuccess" />
    </router-view>
  </div>

</template>

<style lang="scss" scoped>
.link-container {
  width: 100%;
  height: 100%;
  background-color: var(--background-background-container, #FFFFFF);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.link-list {
  &-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    overflow: hidden;
  }

  &-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 32px;
    width: 100%;
  }

  &-header {
    display: flex;
    align-items: flex-end;
    padding: 11px 40px 11px 36px;
    gap: 8px;
    height: 64px;

    &-title {
      font-size: 26px;
      font-weight: 700;
      color: var(--font-font-1, #272E3B);
    }

    &-sync {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px;
      height: 28px;
      font-size: 14px;
      color: var(--font-font-2, #636F88);
      border-radius: var(--border-radius-M, 8px);
      font-size: 12px;
      color: var(--font-font-3, #86909C);
      cursor: pointer;

      &-icon {
        width: 20px;
        height: 20px;
      }

      &:hover {
        background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
      }

      i {
        font-size: 12px;
      }
    }
  }

  &-tabs {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 40px;
    height: 48px;
  }

  &-sync-tips {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 4px;
    width: 220px;
    box-sizing: border-box;

    &-title {
      color: var(--font-font-1, #272E3B);
      font-size: 13px;
      line-height: 22px;
    }

    &-info {
      color: var(--font-font-3, #86909C);
      font-size: 11px;
      line-height: 14px;
    }
  }

  &-guest-tips {
    padding: 0 40px 8px 40px;

    &-content {
      display: flex;
      align-items: center;
      height: 40px;
      border-radius: var(--border-radius-M, 8px);
      background: var(--button-button-lead-default, rgba(34, 109, 245, 0.10));
      color: var(--font-font-1, #272E3B);
      font-size: 13px;
      padding: 0 12px;
      justify-content: space-between;

      a {
        color: var(--primary-primary-default, #226DF5);
        margin-left: 8px;
        cursor: pointer;
      }

      i {
        font-size: 12px;
        color: var(--font-font-3, #86909C);
        cursor: pointer;
      }
    }
  }

  &-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    position: relative;
  }

  &-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 8px 25px;
    overflow-y: auto;
  }
}
</style>
