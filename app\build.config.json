{"productName": "ElectronRsbuildTemplateVue", "appId": "ElectronRsbuildTemplateVue", "icon": "assets/icons/1024x1024.png", "copyright": "Copyright © 2023 by <PERSON>", "files": ["dist"], "directories": {"output": "release", "buildResources": "assets"}, "asar": false, "nsis": {"artifactName": "${productName}-${version}.${ext}", "perMachine": true, "oneClick": false, "warningsAsErrors": false, "deleteAppDataOnUninstall": true, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "win": {"target": ["zip", "nsis"]}, "mac": {"target": {"target": "default", "arch": ["arm64", "x64"]}, "type": "distribution", "hardenedRuntime": true, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist", "gatekeeperAssess": false}, "dmg": {"contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "extraResources": ["./assets/**"], "publish": [{"provider": "github", "owner": "RyanProMax", "repo": "electron-react-rspack"}]}