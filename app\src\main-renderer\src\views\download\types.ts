import * as BaseType from '@root/common/task/base'

export interface IDialogInfo { title: string, confirmText: string, icon: string, tip: string }

export enum IDialogStatus {
  Fail,
  Lost,
  Recycle,
  Delete,
  RetryDownload, // 重新下载
}

/** 数据过滤 */
export enum ISourceFilter {
  all = -1,
  Unkown,
  Video,
  Software,
  Doc,
  Music,
  Pic,
  Zip,
  Bt,
  pdf,
  taskGroup,
  loseFile,
  yunpan,
  nas,
  wky
}

/** 旧数据 */
export enum SortType {
  Default = -10,
  ByTime_Remain = 14, // 剩余时间
  ByTime_Creation = 17, // 创建日期
  ByTime_Completion = 19, // 完成时间
  ByType = 16,
  ByName = 1,
  BySize = 9,
  ByProgress = 18,
  ByState = 10,
  ByTime = 99
}

export interface ISortMap {
  list: { val: string; label: string }[]
  title: string
  key: number
}

export enum ErrorType {
  Download = 'download', // 下载
  Speed = 'speed', // 会员加速服务器
  Dcdn = 'dcdn' // DCDN加速
}

  // 错误码
  export enum TaskError {
    Unkown = 0, // 未知错误

    // TaskErrorTask
    Create, // 任务创建失败(独立进程)
    InvaldParam, // 任务参数错误
    InvaldLink, // 任务链接失效
    InvaldConfig, // 任务配置文件错误
    Timeout, // 任务超时
    VerifyData, // 任务校验失败
    Forbidden, // 任务被禁止下载
    RangeDispatch, // 多线程加速出错
    FilePathOverRanging, // 文件路径超出系统限制

    // TaskErrorDISK                  = 200+
    FileCreate = 201, // 文件创建失败
    FileWrite, // 文件写入失败
    FileRead, // 文件读取失败
    FileRename, // 文件重命名失败
    FileFull, // 磁盘空间不足

    FileOccupied = 211, // 无法创建文件(文件被占用)
    FileAccessDenied, // 无法创建文件(权限不足)

    // TaskErrorP2sp                  = 400+

    // TaskErrorEmule                 = 500+

    // TaskErrorBt                    = 600+
    BtUploadExist = 601, // BT任务已存在

    // Forbindden                        = 700+
    ForbinddenResource = 701, // 敏感资源
    ForbinddenAccount, // 账号异常无法下载
    ForbinddenArea, // 所在区域无法下载
    ForbinddenCopyright, // 应版权方要求无法下载
    ForbinddenReaction,
    ForbinddenPorn,

    DownloadSDKCrash = 10001, // 下载引擎未启动
    torrentFileNotExist = 10002, // 种子文件不存在

    DownloadSDKMissing = 65662, // 下载引擎丢失  载库构造错误码的实现：(((1 & 0x000000FF) << 16) | (126 & 0x0000FFFF)) : LoadLibrary dll不存在的错误码是126；
  }