import { GetXxxNodePath, GetProfilesPath } from '@root/common/xxx-node-path'
import requireNodeFile from '@root/common/require-node-file'
import path from 'node:path'
import { Logger } from '@root/common/logger';
import { UserConfigTableManager } from './user-config';

const logger = new Logger({ tag: 'ThunderPanDataBase' })

export class ThunderPanDataBase {
  private static _instance: ThunderPanDataBase;

  static getInstance() {
    if (ThunderPanDataBase._instance) return ThunderPanDataBase._instance;
    ThunderPanDataBase._instance = new ThunderPanDataBase();
    return ThunderPanDataBase._instance;
  }

  private db: any

  init() {
    const thunderHelper: any = requireNodeFile(path.join(GetXxxNodePath(), 'thunder_helper.node'));
    this.db = new thunderHelper.NativeSqliteStorage(path.join(GetProfilesPath(), './ThunderPan.dat'));
  }

  execute(execSQL: string): Promise<boolean> {
    logger.debug('执行SQL start', execSQL)
    return new Promise(resolve => {
      this.db.execute(execSQL, (success: boolean) => {
        // logger.log('执行SQL res', execSQL, success)
        resolve(success)
      })
    })
  }

  query(querySQL: string): Promise<{ success: boolean, items: any[] }> {
    logger.debug('查询SQL start', querySQL)
    return new Promise(resolve => {
      this.db.query(querySQL, (success: boolean, items: any[]) => {
        // logger.log('查询SQL res', querySQL, success, items)
        resolve({
          success,
          items
        })
      }, true)
    })
  }

  async initLocalSql() {
    const tableExist = await UserConfigTableManager.getInstance().exist()
    logger.log('数据库表是否存在', tableExist)
    if (!tableExist) await UserConfigTableManager.getInstance().create()
  }
}
