import type { AplayerMedia } from '@root/common/player/client/aplayer-media'
import { createGlobalState } from '@vueuse/core'
import { readonly, ref } from 'vue'

export type TMediaControlGlobal = {
}

export type TPlayerControlGlobal = {
  mediaControlMap: Map<AplayerMedia['id'], TMediaControlGlobal>
  isAppLaunchFirstMediaPlay: boolean
}

export const usePlayerControlGlobal = createGlobalState(() => {
  const playerControlGlobal_ref = ref<TPlayerControlGlobal>({
    mediaControlMap: new Map(),
    isAppLaunchFirstMediaPlay: true,
  })
  const playerControlGlobal = readonly(playerControlGlobal_ref)
  const playerControlGlobalAction = {
    // setLastPlayNoticeNoShow(
    //   id: AplayerMedia['id'],
    //   lastPlayNoticeNoShow: boolean = true,
    // ) {
    //   if (playerControlGlobal_ref.value.mediaControlMap.has(id)) {
    //     playerControlGlobal_ref.value.mediaControlMap.get(
    //       id,
    //     )!.lastPlayNoticeNoShow = lastPlayNoticeNoShow
    //   } else {
    //     playerControlGlobal_ref.value.mediaControlMap.set(id, {
    //       lastPlayNoticeNoShow,
    //     })
    //   }
    // },
    set<TKey extends keyof TPlayerControlGlobal>(key: TKey, val: TPlayerControlGlobal[TKey]) {
      if (playerControlGlobal_ref.value[key]) {
        playerControlGlobal_ref.value[key] = val
      }
      playerControlGlobal_ref.value[key] = val
    }
  }

  return { playerControlGlobal, playerControlGlobalAction }
})
