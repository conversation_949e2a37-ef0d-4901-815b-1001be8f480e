import { computed, type ComputedRef } from 'vue'
import * as BaseType from '@root/common/task/base'
import { AplayerStack } from '@root/common/player/client/aplayer-stack'
import * as PlayerBaseType from '@root/common/player/base'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import XMPMessage from '@root/common/components/ui/message/index'
import { LinkSaver } from '@root/modal-renderer/src/utils/link-saver'
import { LinkSaveScene, type LinkSaveOptions } from '@root/modal-renderer/types/link-saver.type'

import type {
  TaskFileSelectionMap,
  IUrlDataMap,
  IUrlWithTypeArray,
  TaskExtDataMap,
} from '@root/modal-renderer/types/new-task.type'

/**
 * 播放任务参数接口
 */
export interface HandlePlayTasksParams {
  /** 选中的文件索引映射 */
  checkedFileIndexes: TaskFileSelectionMap
  /** 任务数据映射 */
  dataMap: IUrlDataMap
  /** 所有URL及其类型数组 */
  allUrlsWithType: IUrlWithTypeArray
  /** 场景编号，用于标识不同使用场景 */
  scene?: string
  /** 扩展数据选项 */
  optionsExtData?: TaskExtDataMap
}

/**
 * 播放任务相关的组合式函数
 * 封装播放按钮的disabled状态判断和播放任务处理逻辑
 */
export function usePlayTasks() {
  /**
   * 判断播放按钮是否应该被禁用
   * @param checkedFileIndexes 选中的文件索引映射
   * @param dataMap 任务数据映射
   * @returns 是否禁用播放按钮
   */
  const isPlayButtonDisabled = (
    checkedFileIndexes: TaskFileSelectionMap,
    dataMap: IUrlDataMap
  ): boolean => {
    // 检查是否有选中的视频或音频文件
    let hasSelectedVideoOrAudio = false

    // 遍历所有任务的选中文件
    for (const [taskId, selectionInfo] of Object.entries(checkedFileIndexes)) {
      if (!selectionInfo?.fileIndexes?.length) continue

      // 获取该任务的详细信息
      const taskData = dataMap[taskId]
      if (!taskData) continue

      // 检查该任务是否有文件列表（主要针对磁力链任务）
      if (taskData.detail?.fileLists && Array.isArray(taskData.detail.fileLists)) {
        // 检查选中的文件中是否有视频或音频文件
        for (const fileIndex of selectionInfo.fileIndexes) {
          const file = taskData.detail.fileLists[fileIndex]
          if (!file?.fileName) continue

          try {
            // 使用TaskUtilHelper判断文件类型
            const fileType = TaskUtilHelper.getTaskFileType(file.fileName)
            
            if (fileType === TaskUtilHelper.FileExtType.Video || 
                fileType === TaskUtilHelper.FileExtType.Music) {
              hasSelectedVideoOrAudio = true
              break
            }
          } catch (error) {
            console.error('判断选中文件类型时出错:', error, 'fileName:', file.fileName)
          }
        }
      } else if (taskData.fileName) {
        // 对于单文件任务（如P2SP、Emule），直接检查文件名
        try {
          const fileType = TaskUtilHelper.getTaskFileType(taskData.fileName)
          
          if (fileType === TaskUtilHelper.FileExtType.Video || 
              fileType === TaskUtilHelper.FileExtType.Music) {
            hasSelectedVideoOrAudio = true
            break
          }
        } catch (error) {
          console.error('判断选中文件类型时出错:', error, 'fileName:', taskData.fileName)
        }
      }

      // 如果已经找到视频或音频文件，提前退出循环
      if (hasSelectedVideoOrAudio) break
    }

    return !hasSelectedVideoOrAudio
  }

  /**
   * 创建计算属性版本的isPlayButtonDisabled
   * @param checkedFileIndexes 响应式的选中文件索引映射
   * @param dataMap 响应式的任务数据映射
   * @returns 计算属性
   */
  const createIsPlayButtonDisabledComputed = (
    checkedFileIndexes: () => TaskFileSelectionMap,
    dataMap: () => IUrlDataMap
  ): ComputedRef<boolean> => {
    return computed(() => {
      return isPlayButtonDisabled(checkedFileIndexes(), dataMap())
    })
  }

  /**
   * 处理播放任务
   * @param params 播放任务参数
   */
  const handlePlayTasks = (params: HandlePlayTasksParams): void => {
    const {
      checkedFileIndexes,
      dataMap,
      allUrlsWithType,
      scene = 'default',
      optionsExtData
    } = params

    console.log(`[usePlayTasks] 点击播放按钮，场景: ${scene}, 开始收集选中的音视频文件`)

    const links: any[] = []
    let hasValidMediaFiles = false

    // 遍历所有任务的选中文件
    for (const [taskUrl, selectionInfo] of Object.entries(checkedFileIndexes)) {
      if (!selectionInfo?.fileIndexes?.length) {
        console.log(`任务 ${taskUrl} 没有选中的文件，跳过`)
        continue
      }

      console.log(`处理任务 ${taskUrl} 的选中文件:`, selectionInfo.fileIndexes)

      // 获取该任务的详细信息和类型
      const taskData = dataMap[taskUrl]
      if (!taskData) {
        console.warn(`找不到任务数据: ${taskUrl}`)
        continue
      }

      // 查找对应的任务类型
      const urlWithType = allUrlsWithType.find(item => item.url === taskUrl)
      if (!urlWithType) {
        console.warn(`找不到任务类型信息: ${taskUrl}`)
        continue
      }

      const { taskType } = urlWithType

      // 根据任务类型处理选中的文件
      switch (taskType) {
        case BaseType.TaskType.Magnet:
          // 磁力链任务处理
          if (taskData.detail?.fileLists && Array.isArray(taskData.detail.fileLists)) {
            const selectedMediaFiles: PlayerBaseType.LinkSubFileInfo[] = []
            
            for (const fileIndex of selectionInfo.fileIndexes) {
              const file = taskData.detail.fileLists[fileIndex]
              if (!file?.fileName) continue

              try {
                // 使用TaskUtilHelper判断文件类型
                const fileType = TaskUtilHelper.getTaskFileType(file.fileName)
                
                if (fileType === TaskUtilHelper.FileExtType.Video || 
                    fileType === TaskUtilHelper.FileExtType.Music) {
                  selectedMediaFiles.push({index: fileIndex, name: decodeURIComponent(file?.fileName)})
                  hasValidMediaFiles = true
                  console.log(`磁力链文件 ${file.fileName} 是音视频文件，索引: ${fileIndex}`)
                }
              } catch (error) {
                console.error('判断磁力链文件类型时出错:', error, 'fileName:', file.fileName)
              }
            }

            // 如果有音视频文件，添加到播放链接
            if (selectedMediaFiles.length > 0) {
              links.push({
                url: taskUrl,
                name: decodeURIComponent(taskData.fileName || taskData.title || '磁力任务'),
                index: -1,
                subFileInfos: selectedMediaFiles,
              })
              console.log(`添加磁力链播放链接:`, {
                url: taskUrl,
                name: decodeURIComponent(taskData.fileName || taskData.title),
                subFileCount: selectedMediaFiles.length
              })
            }
          }
          break

        case BaseType.TaskType.P2sp:
          // P2SP任务处理（单文件）
          if (taskData.fileName) {
            try {
              const fileType = TaskUtilHelper.getTaskFileType(taskData.fileName)
              
              if (fileType === TaskUtilHelper.FileExtType.Video || 
                  fileType === TaskUtilHelper.FileExtType.Music) {
                links.push({
                  url: taskUrl,
                  name: decodeURIComponent(taskData.fileName),
                  index: -1,
                  subFileInfos: [], // P2SP单文件不需要子文件索引
                })
                hasValidMediaFiles = true
                console.log(`添加P2SP播放链接:`, {
                  url: taskUrl,
                  name: decodeURIComponent(taskData.fileName)
                })
              }
            } catch (error) {
              console.error('判断P2SP文件类型时出错:', error, 'fileName:', taskData.fileName)
            }
          }
          break

        case BaseType.TaskType.Emule:
          // Emule任务处理（单文件）
          if (taskData.fileName) {
            try {
              const fileType = TaskUtilHelper.getTaskFileType(taskData.fileName)
              
              if (fileType === TaskUtilHelper.FileExtType.Video || 
                  fileType === TaskUtilHelper.FileExtType.Music) {
                links.push({
                  url: taskUrl,
                  name: decodeURIComponent(taskData.fileName),
                  index: -1,
                  subFileInfos: [], // Emule单文件不需要子文件索引
                })
                hasValidMediaFiles = true
                console.log(`添加Emule播放链接:`, {
                  url: taskUrl,
                  name: decodeURIComponent(taskData.fileName)
                })
              }
            } catch (error) {
              console.error('判断Emule文件类型时出错:', error, 'fileName:', taskData.fileName)
            }
          }
          break

        default:
          console.warn('不支持的任务类型用于播放:', taskType, taskUrl)
          break
      }
    }

    // 检查是否有有效的媒体文件
    if (!hasValidMediaFiles || links.length === 0) {
      console.warn(`[usePlayTasks] 场景: ${scene}, 没有找到可播放的音视频文件`)
      XMPMessage({
        message: '没有找到可播放的音视频文件',
        type: 'warning',
      })
      return
    }

    console.log(`[usePlayTasks] 场景: ${scene}, 构造的播放链接数组:`, links)

    // 调用播放器播放
    try {
      AplayerStack.GetInstance().openMedia({
        name: links.length > 1 ? '批量播放' : decodeURIComponent(links[0].name),
        gcid: '',
        playUrl: '',
        playFrom: 'create-task',
        zipPlay: 0,
        dlnaPlay: 0,
        mediaType: PlayerBaseType.MediaType.MtUniversal,
        universal: {
          links,
          index: 0, // 从第一个开始播放
        },
      })
      
      console.log(`[usePlayTasks] 场景: ${scene}, 成功启动播放器，共 ${links.length} 个媒体文件`)

      // 播放成功后，上报播放的链接到 LinkSaver
      try {
        const linkSaver = LinkSaver.getInstance()

        // 构建用于上报的 URL 数组和数据映射
        const playedUrlsWithType = links.map(link => {
          // 从 allUrlsWithType 找到对应的类型信息
          const urlWithType = allUrlsWithType.find(item => item.url === link.url)
          return {
            url: link.url,
            taskType: urlWithType?.taskType || BaseType.TaskType.P2sp, // 默认为 P2SP
          }
        })

        // 构建数据映射
        const playedDataMap = links.reduce((map, link) => {
          const originalData = dataMap[link.url]
          if (originalData) {
            map[link.url] = {
              ...originalData,
              // 添加播放相关的元数据
              playedAt: Date.now(),
              playedFrom: scene,
            }
          }
          return map
        }, {} as any)

        const options: LinkSaveOptions = {
          scene: LinkSaveScene.AUTO_CREATE, // 使用自动创建场景，表示播放触发的自动上报
          actions: ['ACTION_PLAY'], // 播放操作
          status: 'STATUS_NORMAL', 
          ignoreEvent: false,
          customData: {
            sourceScene: scene,
            component: 'usePlayTasks',
            action: 'play',
            playedLinksCount: links.length,
            timestamp: Date.now(),
          },
        }

        // 异步上报，不影响播放体验
        linkSaver.savePendingAllTasks({
          allUrlsWithType: playedUrlsWithType,
          dataMap: playedDataMap,
          urlExtraDataMap: {},
          checkedFileIndexes: checkedFileIndexes,
          options,
          optionsExtData
        })

        console.log(`[usePlayTasks] 场景: ${scene}, 已上报 ${links.length} 个播放链接到 LinkSaver`)
      } catch (reportError) {
        // 上报失败不影响播放功能
        console.error(`[usePlayTasks] 场景: ${scene}, 上报播放链接失败:`, reportError)
      }
    } catch (error) {
      console.error(`[usePlayTasks] 场景: ${scene}, 启动播放器失败:`, error)
      XMPMessage({
        message: '启动播放器失败',
        type: 'error',
      })
    }
  }

  return {
    isPlayButtonDisabled,
    createIsPlayButtonDisabledComputed,
    handlePlayTasks,
  }
} 