#include "StdAfx.h"
#include "PatchManager.h"
#include "utility.h"
#include "xl_lib/filesystem/file_utility.h"

PatchManager::PatchManager(void)
:m_bInit(false),
m_bBeginEnum(false),
m_bHaveUpdate(false),
m_InstalledPatchRoot(NULL),
m_bPatchMonopolize(false),
m_bPatchInfoDirty(false)
{
}

PatchManager::~PatchManager(void)
{
}

void PatchManager::Init(const wchar_t* ProductName, const wchar_t* ProductVer, const wchar_t* ProductPathMD5)
{
    if (m_bInit)
    {
        return;
    }
    m_ProductName = ProductName;
    m_ProductVer = ProductVer;
    m_ProductPathMD5 = ProductPathMD5;
    
    LoadInstalledPatchInfo();
    m_bInit = true;
}

void PatchManager::UnInit()
{
    if (!m_bInit)
    {
        return;
    }
    SavePatchInfo();
    m_InstalledPatchRoot->release();
    m_bInit = false;
}

void PatchManager::SavePatchInfo()
{
    if (m_bPatchInfoDirty)
    {
        std::wstring path;
        utility::GetPatchInfoFilePath(m_ProductName.c_str(), m_ProductVer.c_str(), m_ProductPathMD5.c_str(), path);
        m_InstalledPatchRoot->write_utf8_xml_file(path.c_str());
        m_bPatchInfoDirty = false;
    }
}

void PatchManager::AddPatchToInstallList(const char* PatchId)
{
    if (!m_bInit)
    {
        return;
    }
    if (m_PackInfos.find(PatchId) != m_PackInfos.end())
    {
        if (!IsPatchInstalled(PatchId))
        {
            m_InstallList.push_back(PatchId);
        }
    }
}

bool PatchManager::IsPatchInstalled(const char* id)
{
    if (!m_bInit)
    {
        return false;
    }
    for (size_t i = 0; i < m_InstalledPatchId.size(); ++i)
    {
        if (strcmp(m_InstalledPatchId[i].name.c_str(), id) == 0)
        {
            return true;
        }
    }
    return false;
}

void PatchManager::RemovePatchFromInstallList(const char* PatchId)
{
    if (!m_bInit)
    {
        return;
    }
    m_InstallList.remove(PatchId);
}

void PatchManager::RemoveAllPatchFromInstallList()
{
    if (!m_bInit)
    {
        return;
    }
    m_InstallList.clear();
}

void PatchManager::InsertPatchInfo(const char* id, PATCH_TYPE type, const char* param, const char* url, const char* httpurl, const char* desc, const char* md5, unsigned long filesize)
{
    if (!m_bInit)
    {
        return;
    }
    PatchInfo info;
    info.type = type;
    info.param = param;
    info.url = url;
    info.httpurl = httpurl;
    info.desc = desc;
    info.md5 = md5;
    info.filesize = filesize;

    std::string szId = id;

    m_PackInfos.insert(std::map<std::string, PatchInfo>::value_type(szId, info));
}

void PatchManager::SetUpdateInfo(const char* name, const char* newver, const char* param, const char* url, const char* httpurl, const char* desc, const char* md5, unsigned long filesize)
{
    if (!m_bInit)
    {
        return;
    }
    NewUpdateInfo info;
    info.name = name;
    info.newver = newver;
    info.param = param;
    info.url = url;
    info.httpurl = httpurl;
    info.desc = desc;
    info.md5 = md5;
    info.filesize = filesize;

    m_UpdateInfo = info;
    m_bHaveUpdate = true;
}

const char* PatchManager::GetNewUpdateVer()
{
    return m_UpdateInfo.newver.c_str();
}

const char* PatchManager::GetNewUpdateDesc()
{
    return m_UpdateInfo.desc.c_str();
}

NewUpdateInfo* PatchManager::GetUpdateInfo()
{
    if (!m_bInit)
    {
        return NULL;
    }
    if (m_bHaveUpdate)
    {
        return &m_UpdateInfo;
    }
    return NULL;
}

bool PatchManager::GetPatchInfo(const char* id, PatchInfo* & pInfo)
{
    if (!m_bInit)
    {
        return false;
    }
    pInfo = NULL;
    std::map<std::string, PatchInfo>::iterator it = m_PackInfos.find(id);
    if (it == m_PackInfos.end())
    {
        return false;
    }
    pInfo = &(it->second);
    return true;
}

void PatchManager::BeginEnumInstallList()
{
    if (!m_bInit)
    {
        return;
    }
    m_InstallListIterator = m_InstallList.begin();
    m_bBeginEnum = true;
}

bool PatchManager::InstallListHasNext(std::string& id)
{
    if (!m_bInit)
    {
        return false;
    }
    id = "";
    if (m_bBeginEnum)
    {
        if (m_InstallListIterator != m_InstallList.end())
        {
            id = *m_InstallListIterator;
            ++m_InstallListIterator;
            return true;
        }
    }
    return false;
}

size_t PatchManager::GetInstalledPatchCount()
{
    if (!m_bInit)
    {
        return 0;
    }
    return m_InstalledPatchId.size();
}

bool PatchManager::GetInstalledPatchId(size_t index, std::string& PatchId, bool& InstallRet)
{
    if (!m_bInit)
    {
        return false;
    }
    if (index < 0 || index >= m_InstalledPatchId.size())
    {
        return false;
    }
    InstalledPatchInfo info = m_InstalledPatchId[index];
    PatchId = info.name;
    InstallRet = info.InstallRet;
    return true;
}

void PatchManager::LoadInstalledPatchInfo()
{
    std::wstring PatchInfoFile;
    utility::GetPatchInfoFilePath(m_ProductName.c_str(), m_ProductVer.c_str(), m_ProductPathMD5.c_str(), PatchInfoFile);

    m_InstalledPatchRoot = new xml_element("InstalledPatch");

    xml_dom_parser parser;
    if (parser.load_from_file(PatchInfoFile.c_str()))
    {
        xml_element* pResult = parser.get_result();
        if (!pResult)
        {
            return;
        }
        if (strcmp(pResult->get_name(), "InstalledPatch") == 0)
        {
            m_InstalledPatchRoot->release();
            m_InstalledPatchRoot = pResult;
            for (size_t i = 0; i < m_InstalledPatchRoot->get_child_element_count(); ++i)
            {
                xml_element* pPatch = m_InstalledPatchRoot->get_child_element(i);
                InstalledPatchInfo info;
                info.name = pPatch->get_name();
                info.InstallRet = (_stricmp(pPatch->get_attribute_value("ret"), "success") == 0)?true:false;
                m_InstalledPatchId.push_back(info);
                pPatch->release();
            }
        }
    }
}

void PatchManager::SetThisPatchId(const char* id)
{
    m_nowPatchId = id;
}

void PatchManager::GetThisPatchId(std::string& id)
{
    id = m_nowPatchId;
}

void PatchManager::AddInstalledPatchId(const char* id, const char* InstallTime, bool installResult)
{
    if (!m_bInit)
    {
        return;
    }
    xml_element* patch = new xml_element("");
    patch->set_name(id);
    patch->set_attribute("time", InstallTime);
    if (installResult)
    {
        patch->set_attribute("ret", "success");
    }
    else
    {
        patch->set_attribute("ret", "fail");
    }
    
    m_InstalledPatchRoot->add_child_element(patch);
    patch->release();
    m_bPatchInfoDirty = true;

    InstalledPatchInfo info;
    info.name = id;
    info.InstallRet = installResult;
    m_InstalledPatchId.push_back(info);
}

void PatchManager::SetPatchMonopolize(bool monopolize)
{
    m_bPatchMonopolize = monopolize;
}

bool PatchManager::GetPatchMonopolize()
{
    return m_bPatchMonopolize;
}