/**
 * @description: 对一些数据类型做定义
 */

// 本地数据版本号，旧版本为undefined，当前为1.0.0
export type LOCAL_DATA_VERSION = '1.0.0';

/**
 * @description: sdk2.0本地登录数据accountdata.dat格式
 */
export interface IOldLocalData {
  version: undefined; // 本地数据版本号，旧版本为undefined，当前为1.0.0
  users?: {
    uName?: string;
    xl_autologin?: true | false | 1 | 0 | '1' | '0',
    loginkey?: string;
    userid?: string;
    usernick?: string;
    show?: boolean;
    nearlyThirdType?: string;
  }[];
  xbaseLogouInfo?: { needRunLogout: boolean };
  logoutUrls?: {
    clientId: string;
    url: string;
  };
  deviceid?: { id: string };
  // lastLoginInfo: {};
}

/**
 * @description: 上一次登录数据
 */
export interface ILastLoginData {
  userName: string; // 登录用户名
  autoLogin?: boolean; // 是否自动登录
  userId?: string; // sdkDataVersion 1.0.1中新增字段
  mode?: LoginMode; // 上次登录方式
}

/**
 * @description: 客户端内oauth授权退出信息数据
 */
export interface IOauthLogoutInfo {
  needRunLogout: boolean; // 是否需要执行退出。
  signOutURI?: string;
}

/**
 * @description: 登录历史数据
 */
export interface ILoginHistory {
  userName: string; // 登录用户名。
  autoLogin?: boolean; // 是否自动登录。
  userId?: string; // (兼容旧版本账号登录，有login key时才会有)
  mode?: LoginMode;
}

export interface ILoginStatExtra {
  route: number[]; // 各时间节点
  from: string;
}

/**
 * @description: sdk3.0本地文件数据
 */
export interface ILocalData {
  version: LOCAL_DATA_VERSION; // 版本号，目前为1.0.0
  loginHistoryList?: ILoginHistory[];
  lastLoginData?: ILastLoginData;
  sdkData: { [key: string]: string };
  sdkDataVersion: string;
  oauthLogoutInfo?: IOauthLogoutInfo;
}

export interface IUserInitConfig {
  apiOrigins: string[];
  clientId: string;
  clientSecret: string;
  appId: string;
  noHostAppName: string;
  appVersion: string;
  osVersion: string;
  appKey: string;
  deviceId: string;
  accountDirPath: string;
}

export interface IBusinessRequestConfig {
  apiOrigins: string[];
  appId: string;
  appName: string;
  appVersion: string;
  deviceSign: string;
  osVersion: string;
}

export interface ILoginByLoginKeyRequest {
  userId: string;
  loginKey: string;
}

export interface ILoginByLoginKeyResponse {
  error: string;
  errorCode: string;
  error_description: string;
  loginKey: string;
  sessionID: string;
  userID: string;
}

export interface IGetAllUserInfoRequest {
  userId: string;
  sessionId: string;
}

export enum EVipType {
  DownloadVip = 0,  // 下载会员
  YunpanVip         // 云盘会员
}

export interface IUserVipInfo {
  expireDate: string;
  isAutoDeduct: string;
  isRemind: string;
  isVip: string;
  isYear: string;
  payId: string;
  payName: string;
  register: string;
  suplusDay: number;
  vasType: string;
  vasid: string;
  vipDayGrow: string;
  vipGrow: string;
  vipLevel: string;
  icon: {
    general: string;
    small: string;
  };
  detail: {
    isvip: number; // =1是会员，其他非会员
    vas_type: string; // 身份 "5"
    start: string; // 起始时间 "2022-10-24T11:56:41+08:00"
    end: string; // 过期时间 "2022-11-24T11:56:41+08:00"
    surplus_day: number; // 剩余天数 31
  }[];
}

// 旧接口对应的完整用户信息  http://xluser./login/login3/#26-getuserinfo
export interface IAllUserInfo {
  account: string;
  birthday: string;
  city: string;
  country: string;
  error: string;
  errorCode: string;
  error_description: string;
  imgURL: string;
  isAutoDeduct: boolean;
  isCompressed: string;
  isSetPassWord: string;
  isSpecialNum: string;
  isSubAccount: string;
  mobile: string;
  nickName: string;
  order: string;
  personalSign: string;
  platformVersion: string;
  protocolVersion: string;
  province: string;
  rank: string;
  registerDate: string;
  role: string;
  sequenceNo: string;
  sex: string;
  todayScore: string;
  userID: string;
  userNewNo: string;
  vipList: IUserVipInfo[];
}

export interface IOldUserInfo extends IAllUserInfo {
  deviceid: string;
  sessionid: string;
  sessionID: string;
  secureKey: string;
  loginkey: string;
  loginKey: string;
  userid: string;
  usrname: string;
  userName: string;
  uName: string;
  usernick: string;
  usernewno: string;
}

export interface IGetAllUserInfoResponse extends IAllUserInfo {
  error: string;
  errorCode: string;
  error_description: string;
}

export interface IAccessToken2SessionInfoResponse {
  sessionid?: string;
  user_id?: number;
  loginkey?: string;
  secure_key?: string;
}

export interface IAuthClientResponse {
  code: string;
  scope: string;
  state: string;
  expires_in: number;
}

export enum LoginWndCloseType {
  Unknown = 'unknown',
  Close = 'close',
  Success = 'suc',
  Forget = 'forget',
  Third = 'third',
  Modify = 'modify'
}

export enum LoginFailedErrorNo {
  LoginTimeout = 0, // 0 为未执行自动登陆；在重连的情况下： 0 为未执行自动登陆
  AutoLoginFailed = 1, // 1 为自动登陆失败；在重连的情况下： 1 为请求登陆接口返回失败
  LoginKeyFailed, // 2 为历史账号的loginkey登陆失败
  AccountLoginFailed, // 3 为账号密码登陆失败
  VerifationFailed, // 4 为短信登陆失败
}

export enum LoginMode {
  Account = 'account',
  Phone = 'phone',
  Third = 'third',
  // QQ = 'qq',
  // Wechat = 'weixin',
  // Weibo = 'weibo',
  Register = 'register',
  Code = 'qr',
  Other = 'other'
}

export interface ICountryPhoneOptions {
  countryCode: string;
  countryNumber: string;
  countryName?: string;
}

export type ClientStatus = 'UNAVAILABLE' |
  'CONNECTED' |
  'DISCONNECTED' |
  'DISCONNECTING' |
  'RECONNECTING' |
  'UNKNOWN';

export const thirdRedirectURI = 'https://4DC9584B-76B6-45D8-B05E-4D7F9D4F2359.xunlei.com/';
export const thirdLoginTabName: string = 'WEB_SDK_THIRD_LOGIN_39bba395-37a6-4d9c-a20e-3f020ba6866f';
