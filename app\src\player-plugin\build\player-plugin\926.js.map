{"version": 3, "file": "926.js", "sources": ["webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/api/callbacks-registry.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/api/get-electron-binding.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/api/id-generator.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/api/ipc-cmd-handler-mgr.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/api/ipc-cmd.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/api/object-meta.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/api/objects-registry.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/api/slogger.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/api/util.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/index.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/lib/async-remote-server.js", "webpack://player-plugin/../../node_modules/@xunlei/async-remote/dist/lib/async-remote.js", "webpack://player-plugin/../../node_modules/dotenv/lib/main.js", "webpack://player-plugin/../../node_modules/@swc/helpers/esm/_define_property.js"], "sourcesContent": ["\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nclass CallbacksRegistry {\r\n    constructor() {\r\n        this.nextId = 0;\r\n        this.callbacks = {};\r\n        this.callbackIds = new WeakMap();\r\n        this.locationInfo = new WeakMap();\r\n        this.nextId = 0;\r\n        this.callbacks = {};\r\n    }\r\n    add(callback) {\r\n        let id = this.callbackIds.get(callback);\r\n        if (id != null) {\r\n            return id;\r\n        }\r\n        id = this.nextId += 1;\r\n        this.callbacks[id] = callback;\r\n        this.callbackIds.set(callback, id);\r\n        const regexp = /at (.*)/gi;\r\n        const stackString = new Error().stack;\r\n        if (!stackString) {\r\n            return id;\r\n        }\r\n        let filenameAndLine;\r\n        let match;\r\n        while ((match = regexp.exec(stackString)) !== null) {\r\n            const location = match[1];\r\n            if (location.includes(\"native\")) {\r\n                continue;\r\n            }\r\n            if (location.includes(\"electron.asar\")) {\r\n                continue;\r\n            }\r\n            if (location.includes('(<anonymous>)')) {\r\n                continue;\r\n            }\r\n            if (location.includes('callbacks-registry.js')) {\r\n                continue;\r\n            }\r\n            const ref = /([^/^)]*)\\)?$/gi.exec(location);\r\n            if (ref)\r\n                filenameAndLine = ref[1];\r\n            break;\r\n        }\r\n        this.locationInfo.set(callback, filenameAndLine);\r\n        return id;\r\n    }\r\n    get(id) {\r\n        return this.callbacks[id] || function () { };\r\n    }\r\n    getLocation(callback) {\r\n        return this.locationInfo.get(callback);\r\n    }\r\n    apply(id, ...args) {\r\n        return this.get(id).apply(global, ...args);\r\n    }\r\n    remove(id) {\r\n        const callback = this.callbacks[id];\r\n        if (callback) {\r\n            this.callbackIds.delete(callback);\r\n            delete this.callbacks[id];\r\n        }\r\n    }\r\n}\r\nexports.default = CallbacksRegistry;\r\n//# sourceMappingURL=callbacks-registry.js.map", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.getElectronBinding = void 0;\r\nconst getElectronBinding = (name) => {\r\n    if (process._linkedBinding) {\r\n        return process._linkedBinding('electron_common_' + name);\r\n    }\r\n    else if (process.electronBinding) {\r\n        return process.electronBinding(name);\r\n    }\r\n    else {\r\n        return null;\r\n    }\r\n};\r\nexports.getElectronBinding = getElectronBinding;\r\n//# sourceMappingURL=get-electron-binding.js.map", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nlet nextSN = 0;\r\nfunction makeId(prefix) {\r\n    if (prefix) {\r\n        return prefix.concat('.').concat(String(++nextSN));\r\n    }\r\n    else {\r\n        return String(++nextSN);\r\n    }\r\n}\r\nexports.default = makeId;\r\n//# sourceMappingURL=id-generator.js.map", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst sl = require(\"./slogger\");\r\nvar IPCMsgHandlerMgr;\r\n(function (IPCMsgHandlerMgr) {\r\n    let storage = {};\r\n    function add(id, handler, thisArg) {\r\n        storage[id] = { func: handler, thisArg: thisArg };\r\n    }\r\n    IPCMsgHandlerMgr.add = add;\r\n    function invoke(id, ...args) {\r\n        let handler = storage[id];\r\n        if (handler) {\r\n            if (handler.thisArg) {\r\n                handler.func.apply(handler.thisArg, ...args);\r\n            }\r\n            else {\r\n                handler.func(...args);\r\n            }\r\n        }\r\n        else {\r\n            sl.error(`Cannot invoke function by unrecognize id. ${id}`);\r\n        }\r\n        return IPCMsgHandlerMgr;\r\n    }\r\n    IPCMsgHandlerMgr.invoke = invoke;\r\n    function remove(id) {\r\n        delete storage[id];\r\n    }\r\n    IPCMsgHandlerMgr.remove = remove;\r\n})(IPCMsgHandlerMgr || (IPCMsgHandlerMgr = {}));\r\nexports.default = IPCMsgHandlerMgr;\r\n//# sourceMappingURL=ipc-cmd-handler-mgr.js.map", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar AsyncRemoteIPCCommand;\r\n(function (AsyncRemoteIPCCommand) {\r\n    let browser;\r\n    (function (browser) {\r\n        browser.require = \"AR_BROWSER_REQUIRE\";\r\n        browser.builtIn = \"AR_BROWSER_GET_BUILTIN\";\r\n        browser.global = \"AR_BROWSER_GET_GLOBAL\";\r\n        browser.functionCall = \"AR_BROWSER_FUNCTION_CALL\";\r\n        browser.construct = \"AR_BROWSER_CONSTRUCTOR\";\r\n        browser.memberConstruct = \"AR_BROWSER_MEMBER_CONSTRUCTOR\";\r\n        browser.memberCall = \"AR_BROWSER_MEMBER_CALL\";\r\n        browser.memberSet = \"AR_BROWSER_MEMBER_SET\";\r\n        browser.memberGet = \"AR_BROWSER_MEMBER_GET\";\r\n        browser.currentWindow = \"AR_BROWSER_CURRENT_WINDOW\";\r\n        browser.currentWebContents = \"AR_BROWSER_CURRENT_WEB_CONTENTS\";\r\n        browser.clientWebContents = \"AR_BROWSER_CLIENT_WEB_CONTENTS\";\r\n        browser.webContents = \"AR_BROWSER_WEB_CONTENTS\";\r\n        browser.sync = \"AR_BROWSER_SYNC\";\r\n        browser.contextRelease = \"AR_BROWSER_CONTEXT_RELEASE\";\r\n        browser.BROWSER_WRONG_CONTEXT_ERROR = \"AR_BROWSER_WRONG_CONTEXT_ERROR\";\r\n        browser.BROWSER_DEREFERENCE = 'AR_BROWSER_DEREFERENCE';\r\n    })(browser = AsyncRemoteIPCCommand.browser || (AsyncRemoteIPCCommand.browser = {}));\r\n    let renderer;\r\n    (function (renderer) {\r\n        renderer.requireReturn = \"AR_RENDERER_REQUIRE_RETURN\";\r\n        renderer.getBuiltInReturn = \"AR_RENDERER_BUILTIN_RETURN\";\r\n        renderer.getGlobalReturn = \"AR_RENDERER_GLOBAL_RETURN\";\r\n        renderer.functionCallReturn = \"AR_RENDERER_FUNCTION_CALL_RETURN\";\r\n        renderer.memberConstructReturn = \"AR_RENDERER_MEMBER_CONSTRUCTOR_RETURN\";\r\n        renderer.constructReturn = \"AR_RENDERER_CONSTRUCTOR_RETURN\";\r\n        renderer.memberCallReturn = \"AR_RENDERER_MEMBER_CALL_RETURN\";\r\n        renderer.memberSetReturn = \"AR_RENDERER_MEMBER_SET_RETURN\";\r\n        renderer.memberGetReturn = \"AR_RENDERER_MEMBER_GET_RETURN\";\r\n        renderer.currentWindowReturn = \"AR_BROWSER_CURRENT_WINDOW_RETURN\";\r\n        renderer.currentWebContentsReturn = \"AR_RENDERER_CURRENT_WEB_CONTENTS_RETURN\";\r\n        renderer.clientWebContentsReturn = \"AR_RENDERER_CLIENT_WEB_CONTENTS_RETURN\";\r\n        renderer.webContentsReturn = \"AR_RENDERER_WEB_CONTENTS_RETURN\";\r\n        renderer.syncReturn = \"AR_RENDERER_SYNC_RETURN\";\r\n        renderer.callback = \"AR_RENDERER_CALLBACK\";\r\n        renderer.RENDERER_RELEASE_CALLBACK = 'AR_RENDERER_RELEASE_CALLBACK';\r\n    })(renderer = AsyncRemoteIPCCommand.renderer || (AsyncRemoteIPCCommand.renderer = {}));\r\n})(AsyncRemoteIPCCommand || (AsyncRemoteIPCCommand = {}));\r\nexports.default = AsyncRemoteIPCCommand;\r\n//# sourceMappingURL=ipc-cmd.js.map", "\"use strict\";\r\nvar ObjectMetaNS;\r\n(function (ObjectMetaNS) {\r\n    ObjectMetaNS.propertiesOfFunction = [\r\n        'length',\r\n        'name',\r\n        'arguments',\r\n        'caller',\r\n        'prototype',\r\n        'apply',\r\n        'bind',\r\n        'call',\r\n        'toString'\r\n    ];\r\n})(ObjectMetaNS || (ObjectMetaNS = {}));\r\nmodule.exports = ObjectMetaNS;\r\n//# sourceMappingURL=object-meta.js.map", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst sl = require(\"./slogger\");\r\nconst getOwnerKey = (webContents, contextId) => {\r\n    return `${webContents.id}-${contextId}`;\r\n};\r\nclass ObjectsRegistry {\r\n    constructor() {\r\n        this.nextId = 0;\r\n        this.storage = {};\r\n        this.owners = {};\r\n        this.electronIds = new WeakMap();\r\n    }\r\n    add(webContents, contextId, obj) {\r\n        const id = this.saveToStorage(obj);\r\n        const ownerKey = getOwnerKey(webContents, contextId);\r\n        let owner = this.owners[ownerKey];\r\n        if (!owner) {\r\n            owner = this.owners[ownerKey] = new Map();\r\n            this.registerDeleteListener(webContents, contextId);\r\n        }\r\n        if (!owner.has(id)) {\r\n            owner.set(id, 0);\r\n            this.storage[id].count++;\r\n        }\r\n        owner.set(id, owner.get(id) + 1);\r\n        return id;\r\n    }\r\n    getIdOfObejct(obj) {\r\n        let id = this.electronIds.get(obj);\r\n        return id;\r\n    }\r\n    get(id) {\r\n        const pointer = this.storage[id];\r\n        if (pointer != null)\r\n            return pointer.object;\r\n    }\r\n    remove(webContents, contextId, id) {\r\n        const ownerKey = getOwnerKey(webContents, contextId);\r\n        const owner = this.owners[ownerKey];\r\n        if (owner && owner.has(id)) {\r\n            const newRefCount = owner.get(id) - 1;\r\n            if (newRefCount <= 0) {\r\n                owner.delete(id);\r\n                this.dereference(id);\r\n            }\r\n            else {\r\n                owner.set(id, newRefCount);\r\n            }\r\n        }\r\n    }\r\n    clear(webContents, contextId) {\r\n        const ownerKey = getOwnerKey(webContents, contextId);\r\n        const owner = this.owners[ownerKey];\r\n        if (!owner)\r\n            return;\r\n        for (const id of owner.keys())\r\n            this.dereference(id);\r\n        delete this.owners[ownerKey];\r\n    }\r\n    saveToStorage(object) {\r\n        let id = this.electronIds.get(object);\r\n        if (!id) {\r\n            id = ++this.nextId;\r\n            this.storage[id] = {\r\n                count: 0,\r\n                object: object\r\n            };\r\n            this.electronIds.set(object, id);\r\n        }\r\n        return id;\r\n    }\r\n    dereference(id) {\r\n        const pointer = this.storage[id];\r\n        if (pointer == null) {\r\n            return;\r\n        }\r\n        pointer.count -= 1;\r\n        if (pointer.count === 0) {\r\n            this.electronIds.delete(pointer.object);\r\n            delete this.storage[id];\r\n        }\r\n    }\r\n    registerDeleteListener(webContents, contextId) {\r\n        const processHostId = contextId.split('-')[0];\r\n        const listener = (_, deletedProcessHostId) => {\r\n            if (deletedProcessHostId &&\r\n                deletedProcessHostId.toString() === processHostId) {\r\n                sl.info(\"render-view-deleted: processid=\" + processHostId);\r\n                webContents.removeListener('render-view-deleted', listener);\r\n                this.clear(webContents, contextId);\r\n            }\r\n        };\r\n        webContents.on('render-view-deleted', listener);\r\n    }\r\n}\r\nexports.default = ObjectsRegistry;\r\n//# sourceMappingURL=objects-registry.js.map", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.assert = exports.log = exports.error = exports.warn = exports.info = exports.trace = exports.timeEnd = exports.time = exports.traceback = void 0;\r\nconst path = require(\"path\");\r\nlet myConsole = console;\r\nlet prefix;\r\nif (process.type === \"renderer\") {\r\n    prefix = \"[Renderer] [async-remote]:\";\r\n}\r\nelse if (process.type === \"browser\") {\r\n    prefix = \"[Browser] [async-remote]:\";\r\n}\r\nelse {\r\n    prefix = `[${process.type}] [async-remote]`;\r\n}\r\nfunction stackInfo(n = 5) {\r\n    let stackReg = /at\\s+(.*)\\s+\\((.*):(\\d*):(\\d*)\\)/i;\r\n    let stackReg2 = /at\\s+()(.*):(\\d*):(\\d*)/i;\r\n    let stacklist = new Error().stack.split(\"\\n\").slice(n + 1);\r\n    stacklist.shift();\r\n    let infoArray = [];\r\n    stacklist.forEach((value, index) => {\r\n        let sp = stackReg.exec(value) || stackReg2.exec(value);\r\n        let data = {};\r\n        if (sp && sp.length === 5) {\r\n            data.method = sp[1];\r\n            data.path = sp[2];\r\n            data.line = sp[3];\r\n            data.pos = sp[4];\r\n            data.file = path.basename(data.path);\r\n            infoArray.push(data);\r\n        }\r\n    });\r\n    return infoArray;\r\n}\r\nlet traceback = function (depth = 5) {\r\n    return stackInfo(depth)\r\n        .map((info) => {\r\n        return info.method + \"@(\" + info.file + \")\";\r\n    })\r\n        .join(\" <= \");\r\n};\r\nexports.traceback = traceback;\r\nlet time = function (...args) {\r\n    myConsole.time(...args);\r\n};\r\nexports.time = time;\r\nlet timeEnd = function (...args) {\r\n    myConsole.timeEnd(...args);\r\n};\r\nexports.timeEnd = timeEnd;\r\nlet trace = function (...args) {\r\n    let stacks = stackInfo();\r\n    let method = \"\";\r\n    if (stacks[0] && stacks[0].method) {\r\n        method = method;\r\n    }\r\n    myConsole.trace(prefix, ...args);\r\n};\r\nexports.trace = trace;\r\nlet info = function (...args) {\r\n    let stacks = stackInfo();\r\n    let method = \"anonymous\";\r\n    if (stacks[0] && stacks[0].method) {\r\n        method = method;\r\n    }\r\n    myConsole.info(prefix, \"[\" + method + \"]\", args.join(\",\"));\r\n};\r\nexports.info = info;\r\nlet warn = function (...args) {\r\n    let stacks = stackInfo();\r\n    let method = \"\";\r\n    if (stacks[0] && stacks[0].method) {\r\n        method = method;\r\n    }\r\n    myConsole.warn(\"<WARN>\" + prefix, \"[\" + method + \"]\", args.join(\",\"));\r\n};\r\nexports.warn = warn;\r\nlet error = function (...args) {\r\n    let stacks = stackInfo();\r\n    let method = \"\";\r\n    if (stacks[0] && stacks[0].method) {\r\n        method = method;\r\n    }\r\n    myConsole.error(\"<ERROR>\" + prefix, \"[\" + method + \"]\", args.join(\",\"));\r\n};\r\nexports.error = error;\r\nlet log = function (...args) {\r\n    myConsole.log(prefix, ...args);\r\n};\r\nexports.log = log;\r\nlet assert = function (cond, msg) {\r\n    if (!cond) {\r\n        throw new Error(\"assert failed! msg:\" + msg ? msg : \"\");\r\n    }\r\n};\r\nexports.assert = assert;\r\nif (!process.env.DEBUG_ASYNC_REMOTE) {\r\n    let placeHolder = function () {\r\n        return;\r\n    };\r\n    exports.traceback = placeHolder;\r\n    exports.time = placeHolder;\r\n    exports.timeEnd = placeHolder;\r\n    exports.trace = placeHolder;\r\n    exports.info = placeHolder;\r\n    exports.warn = placeHolder;\r\n    exports.error = placeHolder;\r\n    exports.log = placeHolder;\r\n    exports.assert = placeHolder;\r\n}\r\nelse {\r\n}\r\n//# sourceMappingURL=slogger.js.map", "\"use strict\";\r\nconst electron_1 = require(\"electron\");\r\nvar AsyncRemoteUtil;\r\n(function (AsyncRemoteUtil) {\r\n    function getRemoteObjectName(remoteObj) {\r\n        let ret = typeof remoteObj;\r\n        if (\"function\" === ret) {\r\n            ret = remoteObj.name;\r\n        }\r\n        else if (\"object\" === ret) {\r\n            let ret = remoteObj.name;\r\n            if (\"string\" !== typeof ret) {\r\n                let con = remoteObj.constructor;\r\n                ret = con ? con.name : Object.toString.call(remoteObj);\r\n            }\r\n        }\r\n        return ret;\r\n    }\r\n    AsyncRemoteUtil.getRemoteObjectName = getRemoteObjectName;\r\n    function isPromise(val) {\r\n        return (val &&\r\n            val.then &&\r\n            val.then instanceof Function &&\r\n            val.constructor &&\r\n            val.constructor.reject &&\r\n            val.constructor.reject instanceof Function &&\r\n            val.constructor.resolve &&\r\n            val.constructor.resolve instanceof Function);\r\n    }\r\n    AsyncRemoteUtil.isPromise = isPromise;\r\n    const serializableTypes = [\r\n        <PERSON><PERSON><PERSON>,\r\n        Number,\r\n        String,\r\n        Date,\r\n        Error,\r\n        RegExp,\r\n        A<PERSON>yBuffer\r\n    ];\r\n    function isSerializableObject(value) {\r\n        return value === null || ArrayBuffer.isView(value) || serializableTypes.some(type => value instanceof type);\r\n    }\r\n    AsyncRemoteUtil.isSerializableObject = isSerializableObject;\r\n    const objectMap = function (source, mapper) {\r\n        const sourceEntries = Object.entries(source);\r\n        const targetEntries = sourceEntries.map(([key, val]) => [key, mapper(val)]);\r\n        return Object.fromEntries(targetEntries);\r\n    };\r\n    function serializeNativeImage(image) {\r\n        const representations = [];\r\n        const scaleFactors = image.getScaleFactors();\r\n        if (scaleFactors.length === 1) {\r\n            const scaleFactor = scaleFactors[0];\r\n            const size = image.getSize(scaleFactor);\r\n            const buffer = image.toBitmap({ scaleFactor });\r\n            const dataURL = image.toDataURL({ scaleFactor });\r\n            representations.push({ scaleFactor, size, buffer, dataURL });\r\n        }\r\n        else {\r\n            for (const scaleFactor of scaleFactors) {\r\n                const size = image.getSize(scaleFactor);\r\n                const dataURL = image.toDataURL({ scaleFactor });\r\n                representations.push({ scaleFactor, size, dataURL });\r\n            }\r\n        }\r\n        return { __ELECTRON_SERIALIZED_NativeImage__: true, representations };\r\n    }\r\n    function deserializeNativeImage(value) {\r\n        const image = electron_1.nativeImage.createEmpty();\r\n        if (value.representations.length === 1) {\r\n            const { buffer, size, scaleFactor, dataURL } = value.representations[0];\r\n            const { width, height } = size;\r\n            image.addRepresentation({ buffer, scaleFactor, width, height, dataURL });\r\n        }\r\n        else {\r\n            for (const rep of value.representations) {\r\n                const { dataURL, size, scaleFactor } = rep;\r\n                const { width, height } = size;\r\n                image.addRepresentation({ dataURL, scaleFactor, width, height });\r\n            }\r\n        }\r\n        return image;\r\n    }\r\n    function serialize(value) {\r\n        if (value && value.constructor && value.constructor.name === 'NativeImage') {\r\n            return serializeNativeImage(value);\r\n        }\r\n        if (Array.isArray(value)) {\r\n            return value.map(serialize);\r\n        }\r\n        else if (isSerializableObject(value)) {\r\n            return value;\r\n        }\r\n        else if (value instanceof Object) {\r\n            return objectMap(value, serialize);\r\n        }\r\n        else {\r\n            return value;\r\n        }\r\n    }\r\n    AsyncRemoteUtil.serialize = serialize;\r\n    function deserialize(value) {\r\n        if (value && value.__ELECTRON_SERIALIZED_NativeImage__) {\r\n            return deserializeNativeImage(value);\r\n        }\r\n        else if (Array.isArray(value)) {\r\n            return value.map(deserialize);\r\n        }\r\n        else if (isSerializableObject(value)) {\r\n            return value;\r\n        }\r\n        else if (value instanceof Object) {\r\n            return objectMap(value, deserialize);\r\n        }\r\n        else {\r\n            return value;\r\n        }\r\n    }\r\n    AsyncRemoteUtil.deserialize = deserialize;\r\n})(AsyncRemoteUtil || (AsyncRemoteUtil = {}));\r\nmodule.exports = AsyncRemoteUtil;\r\n//# sourceMappingURL=util.js.map", "\"use strict\";\r\nconst sl = require(\"./api/slogger\");\r\nif (process.type === \"renderer\") {\r\n    sl.info(\"client running\");\r\n    if (!global.__xdasAsyncRemoteExports) {\r\n        let exportsObj = {};\r\n        global.__xdasAsyncRemoteExports = exportsObj;\r\n        let client = require(\"./lib/async-remote\");\r\n        exportsObj.require = client.remoteRequire;\r\n        exportsObj.getCurrentWebContents = client.getCurrentWebContents;\r\n        exportsObj.getCurrentWindow = client.getCurrentWindow;\r\n        exportsObj.Interest = client.Interest;\r\n        exportsObj.global = new Proxy({}, {\r\n            get: (target, p, receiver) => {\r\n                return client.getGlobal(p);\r\n            },\r\n        });\r\n        exportsObj.electron = new Proxy({}, {\r\n            get: (target, p, receiver) => {\r\n                return client.getBuiltin(p);\r\n            },\r\n        });\r\n        Object.defineProperty(exportsObj, \"currentWindow\", {\r\n            get: () => {\r\n                return client.getCurrentWindow();\r\n            },\r\n        });\r\n        Object.defineProperty(exportsObj, \"currentWebContents\", {\r\n            get: () => {\r\n                return client.getCurrentWebContents();\r\n            },\r\n        });\r\n        Object.defineProperty(exportsObj, \"process\", {\r\n            get: () => {\r\n                return client.getGlobal(\"process\");\r\n            },\r\n        });\r\n        Object.defineProperty(exportsObj, \"webContents\", {\r\n            get: () => {\r\n                return client.getWebContents();\r\n            },\r\n        });\r\n    }\r\n}\r\nelse if (process.type === \"browser\") {\r\n    sl.info(\"server running\");\r\n    if (!global.__xdasAsyncRemoteExports) {\r\n        let exportsObj = {};\r\n        global.__xdasAsyncRemoteExports = exportsObj;\r\n        const server = require(\"./lib/async-remote-server\");\r\n        server.startServer();\r\n        exportsObj.getObjectRegistry = server.getObjectRegistry;\r\n    }\r\n}\r\nmodule.exports = global.__xdasAsyncRemoteExports;\r\n//# sourceMappingURL=index.js.map", "\"use strict\";\r\nconst events_1 = require(\"events\");\r\nconst electron = require(\"electron\");\r\nconst objects_registry_1 = require(\"../api/objects-registry\");\r\nconst ipc_cmd_1 = require(\"../api/ipc-cmd\");\r\nconst object_meta_1 = require(\"../api/object-meta\");\r\nconst sl = require(\"../api/slogger\");\r\nconst get_electron_binding_1 = require(\"../api/get-electron-binding\");\r\nconst util_1 = require(\"../api/util\");\r\nconst ipc = electron.ipcMain;\r\nconst v8Util = (0, get_electron_binding_1.getElectronBinding)(\"v8_util\");\r\nconst rendererFunctionCache = new Map();\r\nconst finalizationRegistry = new FinalizationRegistry((fi) => {\r\n    const mapKey = fi.id[0] + '~' + fi.id[1];\r\n    const ref = rendererFunctionCache.get(mapKey);\r\n    if (ref !== undefined && ref.deref() === undefined) {\r\n        rendererFunctionCache.delete(mapKey);\r\n        if (!fi.webContents.isDestroyed()) {\r\n            try {\r\n                fi.webContents.sendToFrame(fi.frameId, ipc_cmd_1.default.renderer.RENDERER_RELEASE_CALLBACK, fi.id[0], fi.id[1]);\r\n            }\r\n            catch (error) {\r\n                console.warn(`sendToFrame() failed: ${error}`);\r\n            }\r\n        }\r\n    }\r\n});\r\nfunction getCachedRendererFunction(id) {\r\n    const mapKey = id[0] + '~' + id[1];\r\n    const ref = rendererFunctionCache.get(mapKey);\r\n    if (ref !== undefined) {\r\n        const deref = ref.deref();\r\n        if (deref !== undefined)\r\n            return deref;\r\n    }\r\n    return undefined;\r\n}\r\nfunction setCachedRendererFunction(id, wc, frameId, value) {\r\n    const wr = new WeakRef(value);\r\n    const mapKey = id[0] + '~' + id[1];\r\n    rendererFunctionCache.set(mapKey, wr);\r\n    finalizationRegistry.register(value, {\r\n        id,\r\n        webContents: wc,\r\n        frameId\r\n    });\r\n    return value;\r\n}\r\nconst locationInfo = new WeakMap();\r\nconst objectsRegistry = new objects_registry_1.default();\r\nfunction getObjectMembers(sender, contextId, object, visited, root) {\r\n    let names = Object.getOwnPropertyNames(object);\r\n    if (typeof object === \"function\") {\r\n        names = names.filter(function (name) {\r\n            return !String.prototype.includes.call(object_meta_1.propertiesOfFunction, name);\r\n        });\r\n    }\r\n    return names.map((name) => {\r\n        let untouchable = false;\r\n        let valueFromRoot = false;\r\n        let value;\r\n        try {\r\n            value = object[name];\r\n        }\r\n        catch (error) {\r\n            untouchable = true;\r\n            sl.warn('untouchable ' + name + ' ' + error);\r\n        }\r\n        if (untouchable) {\r\n            try {\r\n                value = root.value[name];\r\n                untouchable = false;\r\n                valueFromRoot = true;\r\n            }\r\n            catch (error) {\r\n                sl.error('property ' + name + ' untouchable, event try root[name]');\r\n            }\r\n        }\r\n        if (untouchable) {\r\n            return null;\r\n        }\r\n        const descriptor = Object.getOwnPropertyDescriptor(object, name);\r\n        if (!descriptor) {\r\n            return null;\r\n        }\r\n        let type;\r\n        let writable = false;\r\n        if (descriptor.get === undefined && typeof object[name] === 'function') {\r\n            type = 'method';\r\n        }\r\n        else {\r\n            if (descriptor.set || descriptor.writable)\r\n                writable = true;\r\n            type = 'get';\r\n        }\r\n        value = valueToMeta(sender, contextId, value, false, visited, root);\r\n        return { name, enumerable: descriptor.enumerable, writable, type, value, configurable: valueFromRoot ? true : false };\r\n    });\r\n}\r\nconst getObjectPrototype = function (sender, contextId, object, visited, root) {\r\n    const proto = Object.getPrototypeOf(object);\r\n    if (proto === null || proto === Object.prototype)\r\n        return null;\r\n    return {\r\n        members: getObjectMembers(sender, contextId, proto, visited, root),\r\n        proto: getObjectPrototype(sender, contextId, proto, visited, root)\r\n    };\r\n};\r\nconst valueToMeta = function (sender, contextId, value, optimizeSimpleObject = false, visited = null, root = null) {\r\n    visited === null ? (visited = {}) : (visited = visited);\r\n    let type;\r\n    switch (typeof value) {\r\n        case 'object':\r\n            if (value instanceof Buffer) {\r\n                type = 'buffer';\r\n            }\r\n            else if (value && value.constructor && value.constructor.name === 'NativeImage') {\r\n                type = 'nativeimage';\r\n            }\r\n            else if (Array.isArray(value)) {\r\n                type = 'array';\r\n            }\r\n            else if (value instanceof Error) {\r\n                type = 'error';\r\n            }\r\n            else if ((0, util_1.isSerializableObject)(value)) {\r\n                type = 'value';\r\n            }\r\n            else if ((0, util_1.isPromise)(value)) {\r\n                type = 'promise';\r\n            }\r\n            else if (Object.prototype.hasOwnProperty.call(value, 'callee') && value.length != null) {\r\n                type = 'array';\r\n            }\r\n            else if (optimizeSimpleObject && v8Util.getHiddenValue(value, 'simple')) {\r\n                type = 'value';\r\n            }\r\n            else {\r\n                type = 'object';\r\n            }\r\n            break;\r\n        case 'function':\r\n            type = 'function';\r\n            break;\r\n        default:\r\n            type = 'value';\r\n            break;\r\n    }\r\n    do {\r\n        if (type === 'object' || type === 'function') {\r\n            const regId = objectsRegistry.getIdOfObejct(value);\r\n            if (regId) {\r\n                if (visited[regId]) {\r\n                    objectsRegistry.add(sender, contextId, value);\r\n                    return { type, refId: regId, id: regId };\r\n                }\r\n            }\r\n        }\r\n    } while (0);\r\n    if (type === 'array') {\r\n        return {\r\n            type,\r\n            members: value.map((el) => valueToMeta(sender, contextId, el, optimizeSimpleObject, visited, root))\r\n        };\r\n    }\r\n    else if (type === 'nativeimage') {\r\n        return { type, value: (0, util_1.serialize)(value) };\r\n    }\r\n    else if (type === 'object' || type === 'function') {\r\n        const registerId = objectsRegistry.add(sender, contextId, value);\r\n        visited[registerId] = true;\r\n        const meta = {\r\n            refId: undefined,\r\n            type,\r\n            name: value.constructor ? value.constructor.name : '',\r\n            id: registerId,\r\n            members: undefined,\r\n            proto: undefined,\r\n        };\r\n        if (root === null) {\r\n            root = { value: value, meta: meta };\r\n        }\r\n        meta.members = getObjectMembers(sender, contextId, value, visited, root);\r\n        meta.proto = getObjectPrototype(sender, contextId, value, visited, root);\r\n        return meta;\r\n    }\r\n    else if (type === 'buffer') {\r\n        return { type, value };\r\n    }\r\n    else if (type === 'promise') {\r\n        value.then(function () { }, function () { });\r\n        return {\r\n            type,\r\n            then: valueToMeta(sender, contextId, function (onFulfilled, onRejected) {\r\n                value.then(onFulfilled, onRejected);\r\n            })\r\n        };\r\n    }\r\n    else if (type === 'error') {\r\n        return {\r\n            type,\r\n            value,\r\n            members: Object.keys(value).map(name => ({\r\n                name,\r\n                value: valueToMeta(sender, contextId, value[name])\r\n            }))\r\n        };\r\n    }\r\n    else {\r\n        return {\r\n            type: 'value',\r\n            value\r\n        };\r\n    }\r\n};\r\nconst removeRemoteListenersAndLogWarning = (sender, callIntoRenderer) => {\r\n    const location = locationInfo.get(callIntoRenderer);\r\n    let message = 'Attempting to call a function in a renderer window that has been closed or released.' +\r\n        `\\nFunction provided here: ${location}`;\r\n    if (sender instanceof events_1.EventEmitter) {\r\n        const remoteEvents = sender.eventNames().filter((eventName) => {\r\n            return sender.listeners(eventName).includes(callIntoRenderer);\r\n        });\r\n        if (remoteEvents.length > 0) {\r\n            message += `\\nRemote event names: ${remoteEvents.join(', ')}`;\r\n            remoteEvents.forEach((eventName) => {\r\n                sender.removeListener(eventName, callIntoRenderer);\r\n            });\r\n        }\r\n    }\r\n    console.warn(message);\r\n};\r\nconst fakeConstructor = (constructor, name) => new Proxy(Object, {\r\n    get(target, prop, receiver) {\r\n        if (prop === 'name') {\r\n            return name;\r\n        }\r\n        else {\r\n            return Reflect.get(target, prop, receiver);\r\n        }\r\n    }\r\n});\r\nfunction unwrapArgs(sender, frameId, contextId, args) {\r\n    const metaToValue = function (meta) {\r\n        switch (meta.type) {\r\n            case 'nativeimage':\r\n                return (0, util_1.deserialize)(meta.value);\r\n            case \"value\":\r\n                return meta.value;\r\n            case \"remote-object\":\r\n                return objectsRegistry.get(meta.id);\r\n            case \"array\":\r\n                return unwrapArgs(sender, frameId, contextId, meta.value);\r\n            case \"buffer\":\r\n                return Buffer.from(meta.value);\r\n            case \"date\":\r\n                return new Date(meta.value);\r\n            case \"promise\":\r\n                return Promise.resolve({\r\n                    then: metaToValue(meta.then),\r\n                });\r\n            case \"object\":\r\n            case \"interest\": {\r\n                const ret = meta.name !== 'Object' ? Object.create({\r\n                    constructor: fakeConstructor(Object, meta.name)\r\n                }) : {};\r\n                for (const { name, value } of meta.members) {\r\n                    ret[name] = metaToValue(value);\r\n                }\r\n                return ret;\r\n            }\r\n            case \"function-with-return-value\": {\r\n                const returnValue = metaToValue(meta.value);\r\n                return function () {\r\n                    return returnValue;\r\n                };\r\n            }\r\n            case \"function\": {\r\n                const objectId = [contextId, meta.id];\r\n                sl.info(\"renderer function id:\" + objectId);\r\n                const cachedFunction = getCachedRendererFunction(objectId);\r\n                if (cachedFunction !== undefined) {\r\n                    return cachedFunction;\r\n                }\r\n                const callIntoRenderer = function (...args) {\r\n                    sl.info(\"[CALLBACK] args\", args);\r\n                    let succeed = false;\r\n                    if (!sender.isDestroyed()) {\r\n                        try {\r\n                            let serializeMeta = valueToMeta(sender, contextId, args);\r\n                            if (serializeMeta) {\r\n                                serializeMeta = JSON.parse(JSON.stringify(serializeMeta));\r\n                            }\r\n                            succeed = sender.sendToFrame(frameId, ipc_cmd_1.default.renderer.callback, contextId, meta.id, serializeMeta) !== false;\r\n                        }\r\n                        catch (error) {\r\n                            console.warn(`sendToFrame() failed: ${error}`);\r\n                        }\r\n                    }\r\n                    if (!succeed) {\r\n                        removeRemoteListenersAndLogWarning(this, callIntoRenderer);\r\n                    }\r\n                };\r\n                locationInfo.set(callIntoRenderer, meta.location);\r\n                Object.defineProperty(callIntoRenderer, 'length', { value: meta.length });\r\n                setCachedRendererFunction(objectId, sender, frameId, callIntoRenderer);\r\n                return callIntoRenderer;\r\n            }\r\n            default:\r\n                throw new TypeError(`Unknown type: ${meta.type}`);\r\n        }\r\n    };\r\n    return args.map(metaToValue);\r\n}\r\nfunction exceptionToMeta(error) {\r\n    return {\r\n        type: \"exception\",\r\n        value: {\r\n            type: \"error\",\r\n            value: error,\r\n            members: [],\r\n        }\r\n    };\r\n}\r\nfunction throwRPCError(message) {\r\n    const error = new Error(message);\r\n    Object.defineProperty(error, \"code\", { value: \"EBADRPC\" });\r\n    Object.defineProperty(error, \"errno\", { value: -72 });\r\n    throw error;\r\n}\r\nvar AsyncRemoteServer;\r\n(function (AsyncRemoteServer) {\r\n    const sendResponse = (event, channel, ...args) => {\r\n        const sender = event.sender;\r\n        if (args[1]) {\r\n            args[1] = JSON.parse(JSON.stringify(args[1]));\r\n        }\r\n        if (!sender.isDestroyed()) {\r\n            sender.send(channel, ...args);\r\n        }\r\n        else {\r\n            sl.warn(\"webcontext is destroyed.\");\r\n        }\r\n    };\r\n    function startServer() {\r\n        ipc.on(ipc_cmd_1.default.browser.require, (event, contextId, ipcId, moduleName) => {\r\n            sl.info(`[REQUIRE] moduleName=${moduleName} `);\r\n            if (process.mainModule) {\r\n                let mod = process.mainModule.require(moduleName);\r\n                let meta = valueToMeta(event.sender, contextId, mod);\r\n                sendResponse(event, ipc_cmd_1.default.renderer.requireReturn, ipcId, meta);\r\n            }\r\n            else {\r\n                let mainModule = module;\r\n                while (mainModule.parent) {\r\n                    mainModule = mainModule.parent;\r\n                }\r\n                let mod = mainModule.require(moduleName);\r\n                let meta = valueToMeta(event.sender, contextId, mod);\r\n                sendResponse(event, ipc_cmd_1.default.renderer.requireReturn, ipcId, meta);\r\n            }\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.builtIn, (event, contextId, ipcId, p) => {\r\n            sl.info(`[BUILTIN]: property=${p} contextId=${contextId}`);\r\n            let ref = electron;\r\n            let obj = ref[p];\r\n            let meta = valueToMeta(event.sender, contextId, obj);\r\n            sl.info(`[BUILTIN]: returns remoteId:${meta.id}, type: ${typeof obj}`);\r\n            sendResponse(event, ipc_cmd_1.default.renderer.getBuiltInReturn, ipcId, meta);\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.global, (event, contextId, ipcId, p) => {\r\n            sl.info(`[GLOBAL]: proerty:${p} contextId=${contextId}`);\r\n            let obj = global[p];\r\n            let meta;\r\n            meta = valueToMeta(event.sender, contextId, obj);\r\n            sl.info(`[GLOBAL]: returns remoteid=${meta.id}, obj=` + typeof obj);\r\n            sendResponse(event, ipc_cmd_1.default.renderer.getGlobalReturn, ipcId, meta);\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.currentWindow, (event, contextId, ipcId, p) => {\r\n            sl.info(`[CURRENT_WINDOW]: property=${p} contextId=${contextId}`);\r\n            let getOwnerBrowserWindow = event.sender\r\n                .getOwnerBrowserWindow;\r\n            let obj = getOwnerBrowserWindow.call(event.sender);\r\n            let meta = valueToMeta(event.sender, contextId, obj);\r\n            sl.info(`[CURRENT_WINDOW]: returns remoteid=${meta.id}, obj=` + obj);\r\n            sendResponse(event, ipc_cmd_1.default.renderer.currentWindowReturn, ipcId, meta);\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.currentWebContents, (event, contextId, ipcId, p) => {\r\n            sendResponse(event, ipc_cmd_1.default.renderer.currentWebContentsReturn, ipcId, valueToMeta(event.sender, contextId, event.sender));\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.webContents, (event, contextId, ipcId, p) => {\r\n            sl.info(`[WebContents]: proerty:${p} contextId=${contextId}`);\r\n            let obj = electron.webContents;\r\n            let meta;\r\n            meta = valueToMeta(event.sender, contextId, obj);\r\n            sl.info(`[WebContents]: returns remoteid=${meta.id}, obj=` + typeof obj);\r\n            sendResponse(event, ipc_cmd_1.default.renderer.webContentsReturn, ipcId, meta);\r\n        });\r\n        const attachInstanceEvent = (inst, interestEvent) => {\r\n            const doAttachEvents = (ls, useOnce) => {\r\n                if (ls) {\r\n                    let names = Object.getOwnPropertyNames(ls);\r\n                    names.forEach((name) => {\r\n                        if (useOnce) {\r\n                            inst.once(name, ls[name]);\r\n                        }\r\n                        else {\r\n                            inst.on(name, ls[name]);\r\n                        }\r\n                    });\r\n                }\r\n            };\r\n            if (interestEvent.on) {\r\n                doAttachEvents(interestEvent.on, false);\r\n            }\r\n            if (interestEvent.once) {\r\n                doAttachEvents(interestEvent.once, true);\r\n            }\r\n        };\r\n        ipc.on(ipc_cmd_1.default.browser.construct, (event, contextId, ipcId, remoteId, args) => {\r\n            let retMeta;\r\n            let interestEvent = null;\r\n            try {\r\n                sl.info(`[CONSTRUCTOR]: remoteId=${remoteId} `);\r\n                let lastMeta = args.length > 0 ? args[args.length - 1] : null;\r\n                args = unwrapArgs(event.sender, event.frameId, contextId, args);\r\n                let constructor = objectsRegistry.get(remoteId);\r\n                if (constructor == null) {\r\n                    throwRPCError(`Cannot call constructor on missing remote object ${remoteId}`);\r\n                }\r\n                if (lastMeta && lastMeta.type === \"interest\") {\r\n                    interestEvent = args.pop();\r\n                }\r\n                let newObj = new (Function.prototype.bind.apply(constructor, [\r\n                    null,\r\n                    ...args,\r\n                ]))();\r\n                if (newObj && interestEvent) {\r\n                    attachInstanceEvent(newObj, interestEvent);\r\n                }\r\n                retMeta = valueToMeta(event.sender, contextId, newObj);\r\n                sl.info(`[CONSTRUCTOR]: returns remoteId =${retMeta.id} name=${constructor.name} `);\r\n            }\r\n            catch (error) {\r\n                retMeta = exceptionToMeta(error);\r\n            }\r\n            finally {\r\n                sendResponse(event, ipc_cmd_1.default.renderer.constructReturn, ipcId, retMeta);\r\n            }\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.functionCall, function (event, contextId, ipcId, thisArgRemoteId, remoteId, args) {\r\n            let retMeta;\r\n            try {\r\n                sl.info(`[FUNCTION_CALL]: remoteId=${remoteId}`);\r\n                args = unwrapArgs(event.sender, event.frameId, contextId, args);\r\n                let func = objectsRegistry.get(remoteId);\r\n                if (func == null) {\r\n                    sl.error(`Cannot call function on missing remote object ${remoteId}`);\r\n                    retMeta = valueToMeta(event.sender, contextId, undefined);\r\n                }\r\n                else {\r\n                    let thisArg = thisArgRemoteId ? objectsRegistry.get(thisArgRemoteId) : global;\r\n                    retMeta = valueToMeta(event.sender, contextId, func.apply(thisArg, args), true);\r\n                }\r\n                sl.info(`[FUNCTION_CALL]: name=${func.name}`);\r\n            }\r\n            catch (error) {\r\n                retMeta = exceptionToMeta(error);\r\n            }\r\n            finally {\r\n                sendResponse(event, ipc_cmd_1.default.renderer.functionCallReturn, ipcId, retMeta);\r\n            }\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.memberCall, function (event, contextId, ipcId, thisArgRemoteId, remoteId, method, args) {\r\n            sl.info(`[MEMBER_CALL]: remoteId=${remoteId}, method=${method}, args count=${args.length}`);\r\n            let retMeta;\r\n            try {\r\n                args = unwrapArgs(event.sender, event.frameId, contextId, args);\r\n                let obj = objectsRegistry.get(remoteId);\r\n                if (obj == null) {\r\n                    throwRPCError(`Cannot call function '${method}' on missing remote object ${remoteId}`);\r\n                }\r\n                let thisArg = thisArgRemoteId ? objectsRegistry.get(thisArgRemoteId) : obj;\r\n                retMeta = valueToMeta(event.sender, contextId, obj[method].apply(thisArg, args), true);\r\n            }\r\n            catch (error) {\r\n                retMeta = exceptionToMeta(error);\r\n            }\r\n            finally {\r\n                sendResponse(event, ipc_cmd_1.default.renderer.memberCallReturn, ipcId, retMeta);\r\n            }\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.memberGet, function (event, contextId, ipcId, remoteId, p) {\r\n            let retMeta;\r\n            try {\r\n                sl.info(`[MEMBER_GET]: remoteId=${remoteId}, property=`, p);\r\n                let obj = objectsRegistry.get(remoteId);\r\n                if (obj == null) {\r\n                    throwRPCError(`Cannot get property '${Object.toString.call(p)}' on missing remote object ${remoteId}`);\r\n                }\r\n                let value = obj[p];\r\n                retMeta = valueToMeta(event.sender, contextId, value);\r\n            }\r\n            catch (error) {\r\n                retMeta = exceptionToMeta(error);\r\n            }\r\n            finally {\r\n                sendResponse(event, ipc_cmd_1.default.renderer.memberGetReturn, ipcId, retMeta);\r\n            }\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.memberSet, function (event, contextId, ipcId, remoteId, p, args) {\r\n            try {\r\n                sl.info(`[MEMBER_SET]: remoteId=${remoteId}, property=` + p);\r\n                args = unwrapArgs(event.sender, event.frameId, contextId, args);\r\n                let obj = objectsRegistry.get(remoteId);\r\n                if (obj == null) {\r\n                    throwRPCError(`Cannot set property '${Object.toString.call(p)}' on missing remote object ${remoteId}`);\r\n                }\r\n                obj[p] = args[0];\r\n                sendResponse(event, ipc_cmd_1.default.renderer.memberSetReturn, ipcId, {\r\n                    type: \"value\",\r\n                    value: true,\r\n                });\r\n            }\r\n            catch (error) {\r\n                sendResponse(event, ipc_cmd_1.default.renderer.memberSetReturn, ipcId, exceptionToMeta(error));\r\n            }\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.memberConstruct, function (event, contextId, ipcId, remoteId, method, args) {\r\n            let retMeta;\r\n            let interestEvent = null;\r\n            try {\r\n                sl.info(`[MEMBER_CONSTRUCTOR]: regId=${remoteId}, method=${method}`);\r\n                let lastMeta = args.length > 0 ? args[args.length - 1] : null;\r\n                args = unwrapArgs(event.sender, event.frameId, contextId, args);\r\n                let object = objectsRegistry.get(remoteId);\r\n                if (object == null) {\r\n                    throwRPCError(`Cannot call constructor '${method}' on missing remote object ${remoteId}`);\r\n                }\r\n                if (lastMeta && lastMeta.type === \"interest\") {\r\n                    interestEvent = args.pop();\r\n                }\r\n                let constructor = object[method];\r\n                let newObj = new (Function.prototype.bind.apply(constructor, [\r\n                    null,\r\n                    ...args,\r\n                ]))();\r\n                if (newObj && interestEvent) {\r\n                    attachInstanceEvent(newObj, interestEvent);\r\n                }\r\n                retMeta = valueToMeta(event.sender, contextId, newObj);\r\n            }\r\n            catch (error) {\r\n                retMeta = exceptionToMeta(error);\r\n            }\r\n            finally {\r\n                sendResponse(event, ipc_cmd_1.default.renderer.memberConstructReturn, ipcId, retMeta);\r\n            }\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.sync, function (event, contextId, ipcId, remoteId) {\r\n            let obj = objectsRegistry.get(remoteId);\r\n            sendResponse(event, ipc_cmd_1.default.renderer.syncReturn, ipcId, valueToMeta(event.sender, contextId, obj));\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.BROWSER_DEREFERENCE, (event, contextId, id) => {\r\n            objectsRegistry.remove(event.sender, contextId, id);\r\n        });\r\n        ipc.on(ipc_cmd_1.default.browser.contextRelease, (e, contextId) => {\r\n            objectsRegistry.clear(e.sender.id, contextId);\r\n        });\r\n    }\r\n    AsyncRemoteServer.startServer = startServer;\r\n    function getObjectRegistry() {\r\n        return objectsRegistry;\r\n    }\r\n    AsyncRemoteServer.getObjectRegistry = getObjectRegistry;\r\n})(AsyncRemoteServer || (AsyncRemoteServer = {}));\r\nmodule.exports = AsyncRemoteServer;\r\n//# sourceMappingURL=async-remote-server.js.map", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.createFunctionWithReturnValue = exports.getWebContents = exports.getCurrentWebContents = exports.getCurrentWindow = exports.getGlobal = exports.getBuiltin = exports.remoteRequire = exports.Interest = void 0;\r\nconst electron = require(\"electron\");\r\nconst buffer_1 = require(\"buffer\");\r\nconst callbacks_registry_1 = require(\"../api/callbacks-registry\");\r\nconst ipc_cmd_handler_mgr_1 = require(\"../api/ipc-cmd-handler-mgr\");\r\nconst ipc_cmd_1 = require(\"../api/ipc-cmd\");\r\nconst id_generator_1 = require(\"../api/id-generator\");\r\nconst sl = require(\"../api/slogger\");\r\nconst util_1 = require(\"../api/util\");\r\nconst get_electron_binding_1 = require(\"../api/get-electron-binding\");\r\nconst v8Util = (0, get_electron_binding_1.getElectronBinding)('v8_util');\r\nconst ipc = electron.ipcRenderer;\r\nconst callbacksRegistry = new callbacks_registry_1.default();\r\nconst remoteObjectCache = new Map();\r\nconst finalizationRegistry = new FinalizationRegistry((id) => {\r\n    const ref = remoteObjectCache.get(id);\r\n    if (ref !== undefined && ref.deref() === undefined) {\r\n        remoteObjectCache.delete(id);\r\n        ipc.send(ipc_cmd_1.default.browser.BROWSER_DEREFERENCE, contextId, id, 0);\r\n    }\r\n});\r\nconst electronIds = new WeakMap();\r\nconst isReturnValue = new WeakSet();\r\nfunction getCachedRemoteObject(id) {\r\n    const ref = remoteObjectCache.get(id);\r\n    if (ref !== undefined) {\r\n        const deref = ref.deref();\r\n        if (deref !== undefined)\r\n            return deref;\r\n    }\r\n}\r\nfunction setCachedRemoteObject(id, value) {\r\n    const wr = new WeakRef(value);\r\n    remoteObjectCache.set(id, wr);\r\n    finalizationRegistry.register(value, id);\r\n    return value;\r\n}\r\nfunction getContextId() {\r\n    if (v8Util) {\r\n        return v8Util.getHiddenValue(global, 'contextId');\r\n    }\r\n    else {\r\n        throw new Error('Electron >=v13.0.0-beta.6 required to support sandboxed renderers');\r\n    }\r\n}\r\nfunction setRemoteId(remoteObj, id) {\r\n    v8Util.setHiddenValue(remoteObj, '__remote_id__', id);\r\n}\r\nfunction getRemoteId(remoteObj) {\r\n    return v8Util.getHiddenValue(remoteObj, '__remote_id__');\r\n}\r\nconst contextId = process.contextId || getContextId();\r\nclass Interest {\r\n    constructor(events) {\r\n        if (typeof events === \"object\") {\r\n            this.on =\r\n                typeof events.on === \"object\" ? events.on : {};\r\n            this.once =\r\n                typeof events.once === \"object\" ? events.once : {};\r\n        }\r\n        else {\r\n            this.on = {};\r\n            this.once = {};\r\n        }\r\n        if (!this.check()) {\r\n            throw new Error(\"unexpected param\");\r\n        }\r\n    }\r\n    check() {\r\n        let ret = true;\r\n        do {\r\n            let names = Object.getOwnPropertyNames(this.on);\r\n            names.forEach((name) => {\r\n                let v = this.on[name];\r\n                if (typeof v !== \"function\") {\r\n                    ret = false;\r\n                }\r\n            });\r\n            if (!ret)\r\n                break;\r\n            names = Object.getOwnPropertyNames(this.once);\r\n            names.forEach((name) => {\r\n                let v = this.once[name];\r\n                if (typeof v !== \"function\") {\r\n                    ret = false;\r\n                }\r\n            });\r\n        } while (false);\r\n        return ret;\r\n    }\r\n}\r\nexports.Interest = Interest;\r\nfunction wrapArgs(args, visited = new Set()) {\r\n    const valueToMeta = (value) => {\r\n        if (visited.has(value)) {\r\n            return { type: \"value\", value: null };\r\n        }\r\n        if (value && value.constructor && value.constructor.name === 'NativeImage') {\r\n            return { type: 'nativeimage', value: (0, util_1.serialize)(value) };\r\n        }\r\n        else if (Array.isArray(value)) {\r\n            visited.add(value);\r\n            let meta = {\r\n                type: \"array\",\r\n                value: wrapArgs(value, visited),\r\n            };\r\n            visited.delete(value);\r\n            return meta;\r\n        }\r\n        else if (value instanceof buffer_1.Buffer) {\r\n            return { type: \"buffer\", value };\r\n        }\r\n        else if (value instanceof Date) {\r\n            return { type: \"date\", value: value.getTime() };\r\n        }\r\n        else if (value != null && typeof value === \"object\") {\r\n            if ((0, util_1.isPromise)(value)) {\r\n                return {\r\n                    type: \"promise\",\r\n                    then: valueToMeta(function (onFulfilled, onRejected) {\r\n                        value.then(onFulfilled, onRejected);\r\n                    }),\r\n                };\r\n            }\r\n            else if (electronIds.has(value)) {\r\n                return {\r\n                    type: 'remote-object',\r\n                    id: electronIds.get(value)\r\n                };\r\n            }\r\n            let meta = {\r\n                type: value instanceof Interest ? \"interest\" : \"object\",\r\n                name: value.constructor ? value.constructor.name : \"\",\r\n                members: [],\r\n            };\r\n            visited.add(value);\r\n            for (let prop in value) {\r\n                meta.members.push({\r\n                    name: prop,\r\n                    value: valueToMeta(value[prop]),\r\n                });\r\n            }\r\n            visited.delete(value);\r\n            return meta;\r\n        }\r\n        else if (typeof value === 'function' && isReturnValue.has(value)) {\r\n            return {\r\n                type: 'function-with-return-value',\r\n                value: valueToMeta(value())\r\n            };\r\n        }\r\n        else if (typeof value === \"function\") {\r\n            let rendererId = callbacksRegistry.add(value);\r\n            return {\r\n                type: \"function\",\r\n                id: rendererId,\r\n                location: callbacksRegistry.getLocation(value),\r\n                length: value.length,\r\n            };\r\n        }\r\n        else {\r\n            return { type: \"value\", value: value };\r\n        }\r\n    };\r\n    let argsMetas = args.map(valueToMeta);\r\n    return argsMetas;\r\n}\r\nfunction setObjectMembers(ref, object, metaId, members, overwrite) {\r\n    if (!Array.isArray(members))\r\n        return;\r\n    for (const member of members) {\r\n        if (!member) {\r\n            continue;\r\n        }\r\n        if (Object.prototype.hasOwnProperty.call(object, member.name) && !overwrite)\r\n            continue;\r\n        const descriptor = { enumerable: member.enumerable };\r\n        let value;\r\n        if (member.type === 'method') {\r\n            if (member.value) {\r\n                if (member.value.refId) {\r\n                    value = getCachedRemoteObject(member.value.refId);\r\n                    if (value === null) {\r\n                        throw new Error('member refId pointer to null ' + member.value.refId + 'name:' + member.name);\r\n                    }\r\n                }\r\n                else {\r\n                    value = metaToValue(member.value, member.name, metaId);\r\n                }\r\n            }\r\n            descriptor.get = () => {\r\n                return value;\r\n            };\r\n            descriptor.set = (newValue) => {\r\n                value = newValue;\r\n                return value;\r\n            };\r\n            descriptor.configurable = true;\r\n        }\r\n        else if (member.type === 'get') {\r\n            let value;\r\n            descriptor.get = () => value;\r\n            if (member.writable) {\r\n                descriptor.set = (newValue) => {\r\n                    value = newValue;\r\n                };\r\n            }\r\n            value = metaToValue(member.value);\r\n        }\r\n        Object.defineProperty(object, member.name, descriptor);\r\n    }\r\n}\r\nfunction setObjectPrototype(ref, object, metaId, descriptor, isMethod = false) {\r\n    if (descriptor === null)\r\n        return;\r\n    let proto = {};\r\n    if (isMethod) {\r\n        const temp = function () {\r\n        };\r\n        proto = temp;\r\n    }\r\n    setObjectMembers(ref, proto, metaId, descriptor.members);\r\n    setObjectPrototype(ref, proto, metaId, descriptor.proto);\r\n    Object.setPrototypeOf(object, proto);\r\n}\r\nfunction proxyFunctionProperties(metaId, name) {\r\n    const remoteMemberFunction = function (...args) {\r\n        throw new Error(\"Should Never com to a remoteMemberFunction PlaceHolder\");\r\n    };\r\n    Object.defineProperty(remoteMemberFunction, \"name\", {\r\n        value: name,\r\n        writable: false,\r\n        enumerable: true,\r\n    });\r\n    const ret = new Proxy(remoteMemberFunction, {\r\n        apply: (target, thisArg, argsArray) => {\r\n            return new Promise((resolve, reject) => {\r\n                let thisArgRemoteId = getRemoteId(thisArg);\r\n                if (!thisArgRemoteId) {\r\n                    thisArgRemoteId = getRemoteId(thisArg.__remoteObj_);\r\n                }\r\n                let command = ipc_cmd_1.default.browser.memberCall;\r\n                let ipcId = (0, id_generator_1.default)(command);\r\n                ipc.send(command, contextId, ipcId, thisArgRemoteId, metaId, name, wrapArgs(argsArray));\r\n                ipc_cmd_handler_mgr_1.default.add(ipcId, (meta) => {\r\n                    try {\r\n                        resolve(metaToValue(meta));\r\n                    }\r\n                    catch (e) {\r\n                        reject(e);\r\n                    }\r\n                });\r\n            });\r\n        },\r\n        construct: (target, argsArray, newTarget) => {\r\n            return new Promise((resolve, reject) => {\r\n                let command = ipc_cmd_1.default.browser.memberConstruct;\r\n                let ipcId = (0, id_generator_1.default)(command);\r\n                ipc.send(command, contextId, ipcId, metaId, name, wrapArgs(argsArray));\r\n                ipc_cmd_handler_mgr_1.default.add(ipcId, (meta) => {\r\n                    try {\r\n                        let v = metaToValue(meta);\r\n                        resolve(v);\r\n                    }\r\n                    catch (e) {\r\n                        reject(e);\r\n                    }\r\n                });\r\n            });\r\n        },\r\n    });\r\n    setRemoteId(remoteMemberFunction, metaId);\r\n    return ret;\r\n}\r\nfunction makeNonEnumerableMembers(remoteObj, metaId) {\r\n    let __set = function (p, newValue) {\r\n        let type = typeof newValue;\r\n        if (\"function\" === type) {\r\n            throw new Error(\"set a function to a remote member is dangerous\");\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            let command = ipc_cmd_1.default.browser.memberSet;\r\n            let ipcId = (0, id_generator_1.default)(command);\r\n            const newMeta = wrapArgs([newValue]);\r\n            ipc.send(command, contextId, ipcId, metaId, p, newMeta);\r\n            ipc_cmd_handler_mgr_1.default.add(ipcId, (meta) => {\r\n                try {\r\n                    let bOk = metaToValue(meta);\r\n                    remoteObj[p] = newValue;\r\n                    resolve(bOk);\r\n                }\r\n                catch (e) {\r\n                    reject(e);\r\n                }\r\n            });\r\n        });\r\n    };\r\n    let __get = function (p) {\r\n        return new Promise((resolve, reject) => {\r\n            let command = ipc_cmd_1.default.browser.memberGet;\r\n            let ipcId = (0, id_generator_1.default)(command);\r\n            ipc.send(command, contextId, ipcId, metaId, p);\r\n            ipc_cmd_handler_mgr_1.default.add(ipcId, (meta) => {\r\n                try {\r\n                    const remoteValue = metaToValue(meta);\r\n                    remoteObj[p] = remoteValue;\r\n                    resolve(remoteValue);\r\n                }\r\n                catch (e) {\r\n                    reject(e);\r\n                }\r\n            });\r\n        });\r\n    };\r\n    let __sync = function () {\r\n        return new Promise((resolve, reject) => {\r\n            let command = ipc_cmd_1.default.browser.sync;\r\n            let ipcId = (0, id_generator_1.default)(command);\r\n            ipc.send(command, contextId, ipcId, metaId);\r\n            ipc_cmd_handler_mgr_1.default.add(ipcId, (meta) => {\r\n                try {\r\n                    let newRemoteObj = metaToValue(meta);\r\n                    resolve(newRemoteObj);\r\n                }\r\n                catch (e) {\r\n                    reject(e);\r\n                }\r\n            });\r\n        });\r\n    };\r\n    Object.defineProperties(remoteObj, {\r\n        __set: {\r\n            enumerable: false,\r\n            writable: false,\r\n            value: __set,\r\n        },\r\n        __get: {\r\n            enumerable: false,\r\n            writable: false,\r\n            value: __get,\r\n        },\r\n        __sync: {\r\n            enumerable: false,\r\n            writable: false,\r\n            value: __sync,\r\n        },\r\n    });\r\n}\r\nfunction metaToValue(meta, name, metaId) {\r\n    if (!meta)\r\n        return {};\r\n    if (meta.type === 'value') {\r\n        return meta.value;\r\n    }\r\n    else if (meta.type === 'array') {\r\n        return meta.members.map((member) => metaToValue(member));\r\n    }\r\n    else if (meta.type === 'nativeimage') {\r\n        return (0, util_1.deserialize)(meta.value);\r\n    }\r\n    else if (meta.type === 'buffer') {\r\n        return buffer_1.Buffer.from(meta.value);\r\n    }\r\n    else if (meta.type === 'promise') {\r\n        return Promise.resolve({ then: metaToValue(meta.then) });\r\n    }\r\n    else if (meta.type === 'error') {\r\n        return metaToError(meta);\r\n    }\r\n    else if (meta.type === 'exception') {\r\n        if (meta.value.type === 'error') {\r\n            return metaToError(meta.value);\r\n        }\r\n        else {\r\n            return new Error(`Unexpected value type in exception: ${meta.value.type}`);\r\n        }\r\n    }\r\n    else {\r\n        let ret;\r\n        let refId = meta.refId;\r\n        if (refId) {\r\n            const cached = getCachedRemoteObject(refId);\r\n            if (cached) {\r\n                cached;\r\n                return cached;\r\n            }\r\n            else {\r\n                sl.warn('[metaToValue] refId pointer to null ' + refId);\r\n                if (meta.type === 'function') {\r\n                    return () => {\r\n                        return;\r\n                    };\r\n                }\r\n                else {\r\n                    return {};\r\n                }\r\n            }\r\n        }\r\n        if ('id' in meta) {\r\n            const cached = getCachedRemoteObject(meta.id);\r\n            if (cached !== undefined) {\r\n                ret = cached;\r\n                return ret;\r\n            }\r\n        }\r\n        meta = meta;\r\n        if (meta.type === 'function') {\r\n            if (name) {\r\n                ret = proxyFunctionProperties(metaId, name);\r\n            }\r\n            else {\r\n                const remoteFunction = function (...args) {\r\n                    throw new Error(\"Should Never com to a remoteFunction PlaceHolder\");\r\n                };\r\n                setRemoteId(remoteFunction, meta.id);\r\n                ret = new Proxy(remoteFunction, {\r\n                    apply: (target, thisArg, argsArray) => {\r\n                        return new Promise((resolve, reject) => {\r\n                            let thisArgRemoteId = getRemoteId(thisArg);\r\n                            let command = ipc_cmd_1.default.browser.functionCall;\r\n                            let ipcId = (0, id_generator_1.default)(command);\r\n                            ipc.send(command, contextId, ipcId, thisArgRemoteId, meta.id, wrapArgs(argsArray));\r\n                            ipc_cmd_handler_mgr_1.default.add(ipcId, (meta) => {\r\n                                try {\r\n                                    resolve(metaToValue(meta));\r\n                                }\r\n                                catch (e) {\r\n                                    reject(e);\r\n                                }\r\n                            });\r\n                        });\r\n                    },\r\n                    construct: (target, argsArray, newTarget) => {\r\n                        return new Promise((resolve, reject) => {\r\n                            let command = ipc_cmd_1.default.browser.construct;\r\n                            let ipcId = (0, id_generator_1.default)(command);\r\n                            ipc.send(command, contextId, ipcId, meta.id, wrapArgs(argsArray));\r\n                            ipc_cmd_handler_mgr_1.default.add(ipcId, (meta1) => {\r\n                                try {\r\n                                    let v = metaToValue(meta1);\r\n                                    resolve(v);\r\n                                }\r\n                                catch (e) {\r\n                                    reject(e);\r\n                                }\r\n                            });\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        }\r\n        else {\r\n            ret = {};\r\n        }\r\n        makeNonEnumerableMembers(ret, meta.id);\r\n        setObjectMembers(ret, ret, meta.id, meta.members);\r\n        setObjectPrototype(ret, ret, meta.id, meta.proto, meta.type === 'function');\r\n        setRemoteId(ret, meta.id);\r\n        if (ret.constructor) {\r\n            Object.defineProperty(ret.constructor, 'name', { value: meta.name });\r\n        }\r\n        electronIds.set(ret, meta.id);\r\n        setCachedRemoteObject(meta.id, ret);\r\n        return ret;\r\n    }\r\n}\r\nclass RemoteModule {\r\n    getChannel(delegateWhat) {\r\n        let channel = \"\";\r\n        switch (delegateWhat) {\r\n            case \"module\":\r\n                channel = ipc_cmd_1.default.browser.require;\r\n                break;\r\n            case \"builtin\":\r\n                channel = ipc_cmd_1.default.browser.builtIn;\r\n                break;\r\n            case \"global\":\r\n                channel = ipc_cmd_1.default.browser.global;\r\n                break;\r\n            case \"current_window\":\r\n                channel = ipc_cmd_1.default.browser.currentWindow;\r\n                break;\r\n            case \"current_web_contents\":\r\n                channel = ipc_cmd_1.default.browser.currentWebContents;\r\n                break;\r\n            case \"client_web_contents\":\r\n                channel = ipc_cmd_1.default.browser.clientWebContents;\r\n                break;\r\n            case \"web_contents\":\r\n                channel = ipc_cmd_1.default.browser.webContents;\r\n                break;\r\n        }\r\n        return channel;\r\n    }\r\n    constructor(...args) {\r\n        this.__resolved_ = false;\r\n        this.__promise_ = null;\r\n        this.__remoteObj_ = null;\r\n        this.__metaId_ = undefined;\r\n        this.__what_ = \"\";\r\n        this.__name_ = \"\";\r\n        let argv0Type = typeof arguments[0];\r\n        if (\"string\" === argv0Type) {\r\n            let what = arguments[0];\r\n            let name = arguments[1];\r\n            this.__what_ = what;\r\n            this.__name_ = name ? name : what;\r\n            this.__resolved_ = false;\r\n            this.__remoteObj_ = null;\r\n            this.__promise_ = new Promise((resolve, reject) => {\r\n                let channel = this.getChannel(what);\r\n                let ipcId = (0, id_generator_1.default)(channel);\r\n                ipc.send(channel, contextId, ipcId, name);\r\n                ipc_cmd_handler_mgr_1.default.add(ipcId, (meta) => {\r\n                    try {\r\n                        let realObj = metaToValue(meta);\r\n                        this.__remoteObj_ = realObj;\r\n                        this.__resolved_ = true;\r\n                        this.__metaId_ = meta.id;\r\n                        resolve(realObj);\r\n                    }\r\n                    catch (e) {\r\n                        reject(e);\r\n                    }\r\n                });\r\n            });\r\n        }\r\n        else {\r\n            this.__remoteObj_ = arguments[0];\r\n            this.__resolved_ = true;\r\n            this.__promise_ = null;\r\n            this.__metaId_ = undefined;\r\n        }\r\n    }\r\n    __resolve() {\r\n        let promise = this.__promise_;\r\n        if (promise !== null) {\r\n        }\r\n        else {\r\n            if (this.__resolved_) {\r\n                promise = new Promise((resolve, reject) => {\r\n                    resolve(this.__remoteObj_);\r\n                });\r\n                this.__promise_ = promise;\r\n            }\r\n            else {\r\n                throw Error(\"missing the promise for ayncnomously get remote object\");\r\n            }\r\n        }\r\n        return promise;\r\n    }\r\n    __isResolved() {\r\n        return this.__resolved_;\r\n    }\r\n}\r\nfunction hasSenderId(input) {\r\n    return typeof input.senderId === \"number\";\r\n}\r\nfunction metaToError(meta) {\r\n    const obj = meta.value;\r\n    for (const { name, value } of meta.members) {\r\n        obj[name] = metaToValue(value);\r\n    }\r\n    return obj;\r\n}\r\nfunction handleMessage(channel, handler) {\r\n    ipc.on(channel, (event, passedContextId, id, ...args) => {\r\n        if (hasSenderId(event)) {\r\n            if (event.senderId !== 0 && event.senderId !== undefined) {\r\n                console.error(`Message ${channel} sent by unexpected WebContents (${event.senderId})`);\r\n                return;\r\n            }\r\n        }\r\n        if (passedContextId === contextId) {\r\n            handler(id, ...args);\r\n        }\r\n        else {\r\n            ipc.send(ipc_cmd_1.default.browser.BROWSER_WRONG_CONTEXT_ERROR, contextId, passedContextId, id);\r\n        }\r\n    });\r\n}\r\nfunction defaultHandler(event, ipcId, meta) {\r\n    try {\r\n        ipc_cmd_handler_mgr_1.default.invoke(ipcId, meta).remove(ipcId);\r\n    }\r\n    finally {\r\n        ipc_cmd_handler_mgr_1.default.remove(ipcId);\r\n    }\r\n}\r\nipc.on(ipc_cmd_1.default.renderer.requireReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.getBuiltInReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.getGlobalReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.currentWindowReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.currentWebContentsReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.functionCallReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.constructReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.memberCallReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.memberSetReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.memberGetReturn, defaultHandler);\r\nipc.on(ipc_cmd_1.default.renderer.memberConstructReturn, defaultHandler);\r\nhandleMessage(ipc_cmd_1.default.renderer.callback, (callbackId, argsArray) => {\r\n    callbacksRegistry.apply(callbackId, metaToValue(argsArray));\r\n});\r\nipc.on(ipc_cmd_1.default.renderer.syncReturn, defaultHandler);\r\nhandleMessage(ipc_cmd_1.default.renderer.RENDERER_RELEASE_CALLBACK, (callbackId) => {\r\n    sl.info(\"[RELEASE_CALLBACK]: callbackId:\", callbackId);\r\n    callbacksRegistry.remove(callbackId);\r\n});\r\nprocess.on(\"exit\", () => {\r\n    ipc.send(ipc_cmd_1.default.browser.contextRelease, contextId);\r\n});\r\nconst apiMember = [\"__resolve\", \"__isResolved\"];\r\nconst internalMember = [\r\n    \"__promise_\",\r\n    \"__resolved_\",\r\n    \"__remoteObj_\",\r\n    \"__name_\",\r\n    \"__what_\",\r\n    \"__metaId_\",\r\n];\r\nconst assertResolved = (obj) => {\r\n    if (!obj.__isResolved()) {\r\n        throw Error(\"Can not access the property of a remote module which has not Resolved yet.\");\r\n    }\r\n};\r\nfunction createRemoteModuleHolder(remoteModule) {\r\n    const remoteModuleFunction = function () { };\r\n    Object.defineProperty(remoteModuleFunction, \"name\", {\r\n        value: remoteModule.__name_,\r\n    });\r\n    Object.defineProperty(remoteModuleFunction, \"what\", {\r\n        enumerable: false,\r\n        value: remoteModule.__what_,\r\n    });\r\n    let ret = new Proxy(remoteModuleFunction, {\r\n        getPrototypeOf: (target) => {\r\n            assertResolved(remoteModule);\r\n            return Reflect.getPrototypeOf(remoteModule.__remoteObj_);\r\n        },\r\n        setPrototypeOf: (target, v) => {\r\n            throw new Error(\"changing prototype of remote object is forbidden\");\r\n        },\r\n        isExtensible: (target) => {\r\n            assertResolved(remoteModule);\r\n            return Reflect.isExtensible(remoteModule.__remoteObj_);\r\n        },\r\n        preventExtensions: (target) => {\r\n            assertResolved(remoteModule);\r\n            return Reflect.preventExtensions(remoteModule);\r\n        },\r\n        getOwnPropertyDescriptor: (target, p) => {\r\n            assertResolved(remoteModule);\r\n            return Reflect.getOwnPropertyDescriptor(remoteModule.__remoteObj_, p);\r\n        },\r\n        has: (target, p) => {\r\n            assertResolved(remoteModule);\r\n            return Reflect.has(remoteModule.__remoteObj_, p);\r\n        },\r\n        deleteProperty: (target, p) => {\r\n            assertResolved(target);\r\n            return Reflect.deleteProperty(remoteModule.__remoteObj_, p);\r\n        },\r\n        defineProperty: (target, p, attributes) => {\r\n            assertResolved(remoteModule);\r\n            return Reflect.defineProperty(remoteModule.__remoteObj_, p, attributes);\r\n        },\r\n        get: (target, p, receiver) => {\r\n            if (typeof p === \"string\") {\r\n                if (String.prototype.includes.call(internalMember, p)) {\r\n                    let ref = remoteModule;\r\n                    return ref[p];\r\n                }\r\n                if (String.prototype.includes.call(apiMember, p)) {\r\n                    let ref = remoteModule;\r\n                    return ref[p];\r\n                }\r\n            }\r\n            assertResolved(remoteModule);\r\n            return Reflect.get(remoteModule.__remoteObj_, p);\r\n        },\r\n        set: (target, p, value, receiver) => {\r\n            assertResolved(remoteModule);\r\n            return Reflect.set(remoteModule.__remoteObj_, p, value, receiver);\r\n        },\r\n        ownKeys: (target) => {\r\n            assertResolved(remoteModule);\r\n            return Reflect.ownKeys(remoteModule.__remoteObj_);\r\n        },\r\n        apply: (target, thisArg, argArray) => {\r\n            assertResolved(remoteModule);\r\n            Reflect.apply(remoteModule.__remoteObj_, thisArg, argArray);\r\n        },\r\n        construct: (target, argArray, newTarget) => {\r\n            assertResolved(remoteModule);\r\n            if (typeof remoteModule.__remoteObj_ !== \"function\") {\r\n                throw Error(\"operator new ONLY used for function\");\r\n            }\r\n            return new Promise((resolve, reject) => {\r\n                let command = ipc_cmd_1.default.browser.construct;\r\n                let ipcId = (0, id_generator_1.default)(command);\r\n                let metaId = remoteModule.__metaId_;\r\n                ipc.send(command, contextId, ipcId, metaId, wrapArgs(argArray));\r\n                ipc_cmd_handler_mgr_1.default.add(ipcId, (meta) => {\r\n                    try {\r\n                        resolve(metaToValue(meta));\r\n                    }\r\n                    catch (e) {\r\n                        reject(e);\r\n                    }\r\n                });\r\n            });\r\n        },\r\n    });\r\n    const promise = remoteModule.__promise_;\r\n    promise.then((remoteObj) => {\r\n        const remoteType = typeof remoteObj;\r\n        if (remoteType === 'function' || remoteType === 'object') {\r\n            const id = getRemoteId(remoteObj);\r\n            if (id) {\r\n                setRemoteId(ret, id);\r\n            }\r\n        }\r\n    });\r\n    return ret;\r\n}\r\nfunction remoteRequire(module) {\r\n    return createRemoteModuleHolder(new RemoteModule(\"module\", module));\r\n}\r\nexports.remoteRequire = remoteRequire;\r\nfunction getBuiltin(p) {\r\n    return createRemoteModuleHolder(new RemoteModule(\"builtin\", p));\r\n}\r\nexports.getBuiltin = getBuiltin;\r\nfunction getGlobal(p) {\r\n    return createRemoteModuleHolder(new RemoteModule(\"global\", p));\r\n}\r\nexports.getGlobal = getGlobal;\r\nfunction getCurrentWindow() {\r\n    return createRemoteModuleHolder(new RemoteModule(\"current_window\"));\r\n}\r\nexports.getCurrentWindow = getCurrentWindow;\r\nfunction getCurrentWebContents() {\r\n    return createRemoteModuleHolder(new RemoteModule(\"current_web_contents\"));\r\n}\r\nexports.getCurrentWebContents = getCurrentWebContents;\r\nfunction getWebContents() {\r\n    return createRemoteModuleHolder(new RemoteModule(\"web_contents\"));\r\n}\r\nexports.getWebContents = getWebContents;\r\nfunction createFunctionWithReturnValue(returnValue) {\r\n    const func = () => returnValue;\r\n    isReturnValue.add(func);\r\n    return func;\r\n}\r\nexports.createFunctionWithReturnValue = createFunctionWithReturnValue;\r\n//# sourceMappingURL=async-remote.js.map", "/* @flow */\n/*::\n\ntype DotenvParseOptions = {\n  debug?: boolean\n}\n\n// keys and values from src\ntype DotenvParseOutput = { [string]: string }\n\ntype DotenvConfigOptions = {\n  path?: string, // path to .env file\n  encoding?: string, // encoding of .env file\n  debug?: string // turn on logging for debugging purposes\n}\n\ntype DotenvConfigOutput = {\n  parsed?: DotenvParseOutput,\n  error?: Error\n}\n\n*/\n\nconst fs = require('fs')\nconst path = require('path')\n\nfunction log (message /*: string */) {\n  console.log(`[dotenv][DEBUG] ${message}`)\n}\n\nconst NEWLINE = '\\n'\nconst RE_INI_KEY_VAL = /^\\s*([\\w.-]+)\\s*=\\s*(.*)?\\s*$/\nconst RE_NEWLINES = /\\\\n/g\nconst NEWLINES_MATCH = /\\r\\n|\\n|\\r/\n\n// Parses src into an Object\nfunction parse (src /*: string | Buffer */, options /*: ?DotenvParseOptions */) /*: DotenvParseOutput */ {\n  const debug = Boolean(options && options.debug)\n  const obj = {}\n\n  // convert Buffers before splitting into lines and processing\n  src.toString().split(NEWLINES_MATCH).forEach(function (line, idx) {\n    // matching \"KEY' and 'VAL' in 'KEY=VAL'\n    const keyValueArr = line.match(RE_INI_KEY_VAL)\n    // matched?\n    if (keyValueArr != null) {\n      const key = keyValueArr[1]\n      // default undefined or missing values to empty string\n      let val = (keyValueArr[2] || '')\n      const end = val.length - 1\n      const isDoubleQuoted = val[0] === '\"' && val[end] === '\"'\n      const isSingleQuoted = val[0] === \"'\" && val[end] === \"'\"\n\n      // if single or double quoted, remove quotes\n      if (isSingleQuoted || isDoubleQuoted) {\n        val = val.substring(1, end)\n\n        // if double quoted, expand newlines\n        if (isDoubleQuoted) {\n          val = val.replace(RE_NEWLINES, NEWLINE)\n        }\n      } else {\n        // remove surrounding whitespace\n        val = val.trim()\n      }\n\n      obj[key] = val\n    } else if (debug) {\n      log(`did not match key and value when parsing line ${idx + 1}: ${line}`)\n    }\n  })\n\n  return obj\n}\n\n// Populates process.env from .env file\nfunction config (options /*: ?DotenvConfigOptions */) /*: DotenvConfigOutput */ {\n  let dotenvPath = path.resolve(process.cwd(), '.env')\n  let encoding /*: string */ = 'utf8'\n  let debug = false\n\n  if (options) {\n    if (options.path != null) {\n      dotenvPath = options.path\n    }\n    if (options.encoding != null) {\n      encoding = options.encoding\n    }\n    if (options.debug != null) {\n      debug = true\n    }\n  }\n\n  try {\n    // specifying an encoding returns a string instead of a buffer\n    const parsed = parse(fs.readFileSync(dotenvPath, { encoding }), { debug })\n\n    Object.keys(parsed).forEach(function (key) {\n      if (!Object.prototype.hasOwnProperty.call(process.env, key)) {\n        process.env[key] = parsed[key]\n      } else if (debug) {\n        log(`\"${key}\" is already defined in \\`process.env\\` and will not be overwritten`)\n      }\n    })\n\n    return { parsed }\n  } catch (e) {\n    return { error: e }\n  }\n}\n\nmodule.exports.config = config\nmodule.exports.parse = parse\n", "function _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });\n    } else obj[key] = value;\n\n    return obj;\n}\nexport { _define_property as _ };\n"], "names": ["Object", "exports", "WeakMap", "callback", "filenameAndLine", "match", "id", "regexp", "stackString", "Error", "location", "ref", "args", "global", "name", "process", "nextSN", "prefix", "String", "IPCMsgHandlerMgr", "storage", "sl", "handler", "thisArg", "AsyncRemoteIPCCommand", "browser", "renderer", "ObjectMetaNS", "module", "getO<PERSON><PERSON><PERSON><PERSON>", "webContents", "contextId", "obj", "owner<PERSON><PERSON>", "owner", "Map", "pointer", "newRefCount", "object", "processHostId", "listener", "_", "deletedProcessHostId", "path", "myConsole", "console", "stackInfo", "n", "stackReg", "stackReg2", "stacklist", "infoArray", "value", "index", "sp", "data", "depth", "info", "stacks", "cond", "msg", "placeHolder", "AsyncRemoteUtil", "electron_1", "remoteObj", "ret", "con", "val", "Function", "serializableTypes", "Boolean", "Number", "Date", "RegExp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isSerializableObject", "type", "objectMap", "source", "mapper", "sourceEntries", "key", "serialize", "serializeNativeImage", "image", "representations", "scaleFactors", "scaleFactor", "size", "buffer", "dataURL", "Array", "deserialize", "deserializeNativeImage", "width", "height", "rep", "exportsObj", "client", "Proxy", "target", "p", "receiver", "server", "AsyncRemoteServer", "events_1", "electron", "objects_registry_1", "ipc_cmd_1", "object_meta_1", "get_electron_binding_1", "util_1", "ipc", "v8Util", "rendererFunctionCache", "finalizationRegistry", "FinalizationRegistry", "fi", "mapKey", "undefined", "error", "locationInfo", "objectsRegistry", "getObjectMembers", "sender", "visited", "root", "names", "untouchable", "valueFromRoot", "descriptor", "writable", "valueToMeta", "getObjectPrototype", "proto", "optimizeSimpleObject", "<PERSON><PERSON><PERSON>", "regId", "el", "registerId", "meta", "onFulfilled", "onRejected", "removeRemoteListenersAndLogWarning", "callIntoR<PERSON><PERSON>", "message", "remoteEvents", "eventName", "fakeConstructor", "constructor", "prop", "Reflect", "unwrapArgs", "frameId", "metaToValue", "Promise", "returnValue", "objectId", "cachedFunction", "getCached<PERSON><PERSON>erFunction", "deref", "succeed", "serializeMeta", "JSON", "setCached<PERSON><PERSON>erFunction", "wc", "wr", "WeakRef", "TypeError", "exceptionToMeta", "throwRPCError", "sendResponse", "event", "channel", "ipcId", "moduleName", "mod", "mainModule", "getOwnerBrowserWindow", "attachInstanceEvent", "inst", "interestEvent", "doAttachEvents", "ls", "useOnce", "remoteId", "retMeta", "lastMeta", "newObj", "thisArgRemoteId", "func", "method", "e", "buffer_1", "callbacks_registry_1", "ipc_cmd_handler_mgr_1", "id_generator_1", "callbacksRegistry", "remoteObjectCache", "electronIds", "isReturnValue", "WeakSet", "getCachedRemoteObject", "setRemoteId", "getRemoteId", "getContextId", "Interest", "events", "wrapArgs", "Set", "setObjectMembers", "metaId", "members", "overwrite", "member", "newValue", "metaToError", "refId", "cached", "proxyFunctionProperties", "remoteMemberFunction", "<PERSON>rg<PERSON><PERSON><PERSON><PERSON>", "resolve", "reject", "command", "newTarget", "v", "remoteFunction", "meta1", "newMeta", "bOk", "remoteValue", "newRemoteObj", "setObjectPrototype", "isMethod", "setCachedRemoteObject", "RemoteModule", "delegate<PERSON><PERSON>", "argv0Type", "arguments", "what", "realObj", "promise", "handleMessage", "passedContextId", "input", "defaultHandler", "callbackId", "apiMember", "internalMember", "assertResolved", "createRemoteModuleHolder", "remoteModule", "remoteModuleFunction", "attributes", "arg<PERSON><PERSON>y", "remoteType", "fs", "log", "RE_INI_KEY_VAL", "RE_NEWLINES", "NEWLINES_MATCH", "parse", "src", "options", "debug", "line", "idx", "keyV<PERSON>ueArr", "end", "isDoubleQuoted", "isSingleQuoted", "dotenvPath", "encoding", "parsed", "_define_property"], "mappings": "uHACAA,OAAO,cAAc,CAACC,EAAS,aAAc,CAAE,MAAO,EAAK,GAgE3DA,EAAA,OAAe,CA/Df,MACI,aAAc,CACV,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,SAAS,CAAG,CAAC,EAClB,IAAI,CAAC,WAAW,CAAG,IAAIC,QACvB,IAAI,CAAC,YAAY,CAAG,IAAIA,QACxB,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,SAAS,CAAG,CAAC,CACtB,CACA,IAAIC,CAAQ,CAAE,CACV,IAYIC,EACAC,EAbAC,EAAK,IAAI,CAAC,WAAW,CAAC,GAAG,CAACH,GAC9B,GAAIG,AAAM,MAANA,EACA,OAAOA,EAEXA,EAAK,IAAI,CAAC,MAAM,EAAI,EACpB,IAAI,CAAC,SAAS,CAACA,EAAG,CAAGH,EACrB,IAAI,CAAC,WAAW,CAAC,GAAG,CAACA,EAAUG,GAC/B,IAAMC,EAAS,YACTC,EAAc,AAAIC,QAAQ,KAAK,CACrC,GAAI,CAACD,EACD,OAAOF,EAIX,KAAO,AAAuC,OAAtCD,CAAAA,EAAQE,EAAO,IAAI,CAACC,EAAW,GAAa,CAChD,IAAME,EAAWL,CAAK,CAAC,EAAE,CACzB,GAAIK,EAAS,QAAQ,CAAC,WAGlBA,EAAS,QAAQ,CAAC,kBAGlBA,EAAS,QAAQ,CAAC,kBAGlBA,EAAS,QAAQ,CAAC,yBARlB,SAWJ,IAAMC,EAAM,kBAAkB,IAAI,CAACD,GAC/BC,GACAP,CAAAA,EAAkBO,CAAG,CAAC,EAAE,AAAD,EAC3B,KACJ,CAEA,OADA,IAAI,CAAC,YAAY,CAAC,GAAG,CAACR,EAAUC,GACzBE,CACX,CACA,IAAIA,CAAE,CAAE,CACJ,OAAO,IAAI,CAAC,SAAS,CAACA,EAAG,EAAI,WAAc,CAC/C,CACA,YAAYH,CAAQ,CAAE,CAClB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAACA,EACjC,CACA,MAAMG,CAAE,CAAE,GAAGM,CAAI,CAAE,CACf,OAAO,IAAI,CAAC,GAAG,CAACN,GAAI,KAAK,CAACO,UAAWD,EACzC,CACA,OAAON,CAAE,CAAE,CACP,IAAMH,EAAW,IAAI,CAAC,SAAS,CAACG,EAAG,CAC/BH,IACA,IAAI,CAAC,WAAW,CAAC,MAAM,CAACA,GACxB,OAAO,IAAI,CAAC,SAAS,CAACG,EAAG,CAEjC,CACJ,C,kCC/DAN,OAAO,cAAc,CAACC,EAAS,aAAc,CAAE,MAAO,EAAK,GAC3DA,EAAQ,kBAAkB,CAAG,KAAK,EAYlCA,EAAQ,kBAAkB,CAXC,AAACa,GACxB,AAAIC,QAAQ,cAAc,CACfA,QAAQ,cAAc,CAAC,mBAAqBD,GAE9CC,QAAQ,eAAe,CACrBA,QAAQ,eAAe,CAACD,GAGxB,I,kCCVfd,OAAO,cAAc,CAACC,EAAS,aAAc,CAAE,MAAO,EAAK,GAC3D,IAAIe,EAAS,CASbf,CAAAA,EAAA,OAAe,CARf,SAAgBgB,CAAM,SAClB,AAAIA,EACOA,EAAO,MAAM,CAAC,KAAK,MAAM,CAACC,OAAO,EAAEF,IAGnCE,OAAO,EAAEF,EAExB,C,wCCPIG,EACOA,MACHC,EAJRpB,OAAO,cAAc,CAACC,EAAS,aAAc,CAAE,MAAO,EAAK,GAC3D,IAAMoB,EAAK,EAAQ,MAGXD,EAAU,CAAC,EAIfD,CALOA,EA0BRA,GAAqBA,CAAAA,EAAmB,CAAC,IArBvB,GAAG,CAHpB,SAAab,CAAE,CAAEgB,CAAO,CAAEC,CAAO,EAC7BH,CAAO,CAACd,EAAG,CAAG,CAAE,KAAMgB,EAAS,QAASC,CAAQ,CACpD,EAiBAJ,EAAiB,MAAM,CAfvB,SAAgBb,CAAE,CAAE,GAAGM,CAAI,EACvB,IAAIU,EAAUF,CAAO,CAACd,EAAG,CAYzB,OAXIgB,EACIA,EAAQ,OAAO,CACfA,EAAQ,IAAI,CAAC,KAAK,CAACA,EAAQ,OAAO,IAAKV,GAGvCU,EAAQ,IAAI,IAAIV,GAIpBS,EAAG,KAAK,CAAC,CAAC,0CAA0C,EAAEf,EAAG,CAAC,EAEvDa,CACX,EAKAA,EAAiB,MAAM,CAHvB,SAAgBb,CAAE,EACd,OAAOc,CAAO,CAACd,EAAG,AACtB,EAGJL,EAAA,OAAe,CAAGkB,C,sCC7BdK,EACOA,EAEIC,EAoBAC,EAxBf1B,OAAO,cAAc,CAACC,EAAS,aAAc,CAAE,MAAO,EAAK,GAKnDwB,CADOA,EAkBED,CApBNA,EAwCRA,GAA0BA,CAAAA,EAAwB,CAAC,IApBf,OAAO,EAAKA,CAAAA,EAAsB,OAAO,CAAG,CAAC,IAjBpE,OAAO,CAAG,qBAClBC,EAAQ,OAAO,CAAG,yBAClBA,EAAQ,MAAM,CAAG,wBACjBA,EAAQ,YAAY,CAAG,2BACvBA,EAAQ,SAAS,CAAG,yBACpBA,EAAQ,eAAe,CAAG,gCAC1BA,EAAQ,UAAU,CAAG,yBACrBA,EAAQ,SAAS,CAAG,wBACpBA,EAAQ,SAAS,CAAG,wBACpBA,EAAQ,aAAa,CAAG,4BACxBA,EAAQ,kBAAkB,CAAG,kCAC7BA,EAAQ,iBAAiB,CAAG,iCAC5BA,EAAQ,WAAW,CAAG,0BACtBA,EAAQ,IAAI,CAAG,kBACfA,EAAQ,cAAc,CAAG,6BACzBA,EAAQ,2BAA2B,CAAG,iCACtCA,EAAQ,mBAAmB,CAAG,yBAI9BC,CADOA,EAiBGF,EAAsB,QAAQ,EAAKA,CAAAA,EAAsB,QAAQ,CAAG,CAAC,IAhBtE,aAAa,CAAG,6BACzBE,EAAS,gBAAgB,CAAG,6BAC5BA,EAAS,eAAe,CAAG,4BAC3BA,EAAS,kBAAkB,CAAG,mCAC9BA,EAAS,qBAAqB,CAAG,wCACjCA,EAAS,eAAe,CAAG,iCAC3BA,EAAS,gBAAgB,CAAG,iCAC5BA,EAAS,eAAe,CAAG,gCAC3BA,EAAS,eAAe,CAAG,gCAC3BA,EAAS,mBAAmB,CAAG,mCAC/BA,EAAS,wBAAwB,CAAG,0CACpCA,EAAS,uBAAuB,CAAG,yCACnCA,EAAS,iBAAiB,CAAG,kCAC7BA,EAAS,UAAU,CAAG,0BACtBA,EAAS,QAAQ,CAAG,uBACpBA,EAAS,yBAAyB,CAAG,+BAG7CzB,EAAA,OAAe,CAAGuB,C,gCC3ClB,IAAIG,CAEAA,CAWDA,CAAAA,GAAiBA,CAAAA,EAAe,CAAC,EAAC,EAXpB,oBAAoB,CAAG,CAChC,SACA,OACA,YACA,SACA,YACA,QACA,OACA,OACA,WACH,CAELC,EAAO,OAAO,CAAGD,C,oCCdjB3B,OAAO,cAAc,CAACC,EAAS,aAAc,CAAE,MAAO,EAAK,GAC3D,IAAMoB,EAAK,EAAQ,MACbQ,EAAc,CAACC,EAAaC,IACvB,CAAC,EAAED,EAAY,EAAE,CAAC,CAAC,EAAEC,EAAU,CAAC,AA4F3C9B,CAAAA,EAAA,OAAe,CA1Ff,MACI,aAAc,CACV,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,OAAO,CAAG,CAAC,EAChB,IAAI,CAAC,MAAM,CAAG,CAAC,EACf,IAAI,CAAC,WAAW,CAAG,IAAIC,OAC3B,CACA,IAAI4B,CAAW,CAAEC,CAAS,CAAEC,CAAG,CAAE,CAC7B,IAAM1B,EAAK,IAAI,CAAC,aAAa,CAAC0B,GACxBC,EAAWJ,EAAYC,EAAaC,GACtCG,EAAQ,IAAI,CAAC,MAAM,CAACD,EAAS,CAUjC,OATKC,IACDA,EAAQ,IAAI,CAAC,MAAM,CAACD,EAAS,CAAG,IAAIE,IACpC,IAAI,CAAC,sBAAsB,CAACL,EAAaC,IAEzC,CAACG,EAAM,GAAG,CAAC5B,KACX4B,EAAM,GAAG,CAAC5B,EAAI,GACd,IAAI,CAAC,OAAO,CAACA,EAAG,CAAC,KAAK,IAE1B4B,EAAM,GAAG,CAAC5B,EAAI4B,EAAM,GAAG,CAAC5B,GAAM,GACvBA,CACX,CACA,cAAc0B,CAAG,CAAE,CAEf,OADS,IAAI,CAAC,WAAW,CAAC,GAAG,CAACA,EAElC,CACA,IAAI1B,CAAE,CAAE,CACJ,IAAM8B,EAAU,IAAI,CAAC,OAAO,CAAC9B,EAAG,CAChC,GAAI8B,AAAW,MAAXA,EACA,OAAOA,EAAQ,MAAM,AAC7B,CACA,OAAON,CAAW,CAAEC,CAAS,CAAEzB,CAAE,CAAE,CAC/B,IAAM2B,EAAWJ,EAAYC,EAAaC,GACpCG,EAAQ,IAAI,CAAC,MAAM,CAACD,EAAS,CACnC,GAAIC,GAASA,EAAM,GAAG,CAAC5B,GAAK,CACxB,IAAM+B,EAAcH,EAAM,GAAG,CAAC5B,GAAM,CAChC+B,CAAAA,GAAe,GACfH,EAAM,MAAM,CAAC5B,GACb,IAAI,CAAC,WAAW,CAACA,IAGjB4B,EAAM,GAAG,CAAC5B,EAAI+B,EAEtB,CACJ,CACA,MAAMP,CAAW,CAAEC,CAAS,CAAE,CAC1B,IAAME,EAAWJ,EAAYC,EAAaC,GACpCG,EAAQ,IAAI,CAAC,MAAM,CAACD,EAAS,CACnC,GAAKC,GAEL,IAAK,IAAM5B,KAAM4B,EAAM,IAAI,GACvB,IAAI,CAAC,WAAW,CAAC5B,EACrB,QAAO,IAAI,CAAC,MAAM,CAAC2B,EAAS,CAChC,CACA,cAAcK,CAAM,CAAE,CAClB,IAAIhC,EAAK,IAAI,CAAC,WAAW,CAAC,GAAG,CAACgC,GAS9B,OARKhC,IACDA,EAAK,EAAE,IAAI,CAAC,MAAM,CAClB,IAAI,CAAC,OAAO,CAACA,EAAG,CAAG,CACf,MAAO,EACP,OAAQgC,CACZ,EACA,IAAI,CAAC,WAAW,CAAC,GAAG,CAACA,EAAQhC,IAE1BA,CACX,CACA,YAAYA,CAAE,CAAE,CACZ,IAAM8B,EAAU,IAAI,CAAC,OAAO,CAAC9B,EAAG,AACjB,OAAX8B,IAGJA,EAAQ,KAAK,EAAI,EACK,IAAlBA,EAAQ,KAAK,GACb,IAAI,CAAC,WAAW,CAAC,MAAM,CAACA,EAAQ,MAAM,EACtC,OAAO,IAAI,CAAC,OAAO,CAAC9B,EAAG,EAE/B,CACA,uBAAuBwB,CAAW,CAAEC,CAAS,CAAE,CAC3C,IAAMQ,EAAgBR,EAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CACvCS,EAAW,CAACC,EAAGC,KACbA,GACAA,EAAqB,QAAQ,KAAOH,IACpClB,EAAG,IAAI,CAAC,kCAAoCkB,GAC5CT,EAAY,cAAc,CAAC,sBAAuBU,GAClD,IAAI,CAAC,KAAK,CAACV,EAAaC,GAEhC,EACAD,EAAY,EAAE,CAAC,sBAAuBU,EAC1C,CACJ,C,wCC1FIvB,EAJJjB,OAAO,cAAc,CAACC,EAAS,aAAc,CAAE,MAAO,EAAK,GAC3DA,EAAQ,MAAM,CAAGA,EAAQ,GAAG,CAAGA,EAAQ,KAAK,CAAGA,EAAQ,IAAI,CAAGA,EAAQ,IAAI,CAAGA,EAAQ,KAAK,CAAGA,EAAQ,OAAO,CAAGA,EAAQ,IAAI,CAAGA,EAAQ,SAAS,CAAG,KAAK,EACvJ,IAAM0C,EAAO,EAAQ,MACjBC,EAAYC,QAWhB,SAASC,EAAUC,EAAI,CAAC,EACpB,IAAIC,EAAW,oCACXC,EAAY,2BACZC,EAAY,AAAIzC,QAAQ,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAACsC,EAAI,GACxDG,EAAU,KAAK,GACf,IAAIC,EAAY,EAAE,CAalB,OAZAD,EAAU,OAAO,CAAC,CAACE,EAAOC,KACtB,IAAIC,EAAKN,EAAS,IAAI,CAACI,IAAUH,EAAU,IAAI,CAACG,GAC5CG,EAAO,CAAC,EACRD,GAAMA,AAAc,IAAdA,EAAG,MAAM,GACfC,EAAK,MAAM,CAAGD,CAAE,CAAC,EAAE,CACnBC,EAAK,IAAI,CAAGD,CAAE,CAAC,EAAE,CACjBC,EAAK,IAAI,CAAGD,CAAE,CAAC,EAAE,CACjBC,EAAK,GAAG,CAAGD,CAAE,CAAC,EAAE,CAChBC,EAAK,IAAI,CAAGZ,EAAK,QAAQ,CAACY,EAAK,IAAI,EACnCJ,EAAU,IAAI,CAACI,GAEvB,GACOJ,CACX,CA+DA,GA1FIlC,EADAF,AAAiB,aAAjBA,QAAQ,IAAI,CACH,6BAEJA,AAAiB,YAAjBA,QAAQ,IAAI,CACR,4BAGA,CAAC,CAAC,EAAEA,QAAQ,IAAI,CAAC,gBAAgB,CAAC,CA6B/Cd,EAAQ,SAAS,CAPD,SAAUuD,EAAQ,CAAC,EAC/B,OAAOV,EAAUU,GACZ,GAAG,CAAC,AAACC,GACCA,EAAK,MAAM,CAAG,KAAOA,EAAK,IAAI,CAAG,KAEvC,IAAI,CAAC,OACd,EAKAxD,EAAQ,IAAI,CAHD,SAAU,GAAGW,CAAI,EACxBgC,EAAU,IAAI,IAAIhC,EACtB,EAKAX,EAAQ,OAAO,CAHD,SAAU,GAAGW,CAAI,EAC3BgC,EAAU,OAAO,IAAIhC,EACzB,EAUAX,EAAQ,KAAK,CARD,SAAU,GAAGW,CAAI,EACzB,IAAI8C,EAASZ,GAETY,CAAAA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC,MAAM,CAGjCd,EAAU,KAAK,CAAC3B,KAAWL,EAC/B,EAUAX,EAAQ,IAAI,CARD,SAAU,GAAGW,CAAI,EACxB,IAAI8C,EAASZ,GAETY,CAAAA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC,MAAM,CAGjCd,EAAU,IAAI,CAAC3B,EAAQ,cAAoBL,EAAK,IAAI,CAAC,KACzD,EAUAX,EAAQ,IAAI,CARD,SAAU,GAAGW,CAAI,EACxB,IAAI8C,EAASZ,GAETY,CAAAA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC,MAAM,CAGjCd,EAAU,IAAI,CAAC,SAAW3B,EAAQ,KAAoBL,EAAK,IAAI,CAAC,KACpE,EAUAX,EAAQ,KAAK,CARD,SAAU,GAAGW,CAAI,EACzB,IAAI8C,EAASZ,GAETY,CAAAA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC,MAAM,CAGjCd,EAAU,KAAK,CAAC,UAAY3B,EAAQ,KAAoBL,EAAK,IAAI,CAAC,KACtE,EAKAX,EAAQ,GAAG,CAHD,SAAU,GAAGW,CAAI,EACvBgC,EAAU,GAAG,CAAC3B,KAAWL,EAC7B,EAOAX,EAAQ,MAAM,CALD,SAAU0D,CAAI,CAAEC,CAAG,EAC5B,GAAI,CAACD,EACD,MAAM,AAAIlD,MAAoCmD,EAEtD,EAEI,CAAC7C,QAAQ,GAAG,CAAC,kBAAkB,CAAE,CACjC,IAAI8C,EAAc,WAElB,CACA5D,CAAAA,EAAQ,SAAS,CAAG4D,EACpB5D,EAAQ,IAAI,CAAG4D,EACf5D,EAAQ,OAAO,CAAG4D,EAClB5D,EAAQ,KAAK,CAAG4D,EAChB5D,EAAQ,IAAI,CAAG4D,EACf5D,EAAQ,IAAI,CAAG4D,EACf5D,EAAQ,KAAK,CAAG4D,EAChB5D,EAAQ,GAAG,CAAG4D,EACd5D,EAAQ,MAAM,CAAG4D,CACrB,C,wCC5GIC,EADJ,IAAMC,EAAa,EAAQ,OAE3B,AAAC,SAAUD,CAAe,EAetBA,EAAgB,mBAAmB,CAdnC,SAA6BE,CAAS,EAClC,IAAIC,EAAM,OAAOD,EACjB,GAAI,aAAeC,EACfA,EAAMD,EAAU,IAAI,MAEnB,GAAI,WAAaC,EAAK,CACvB,IAAIA,EAAMD,EAAU,IAAI,CACxB,GAAI,UAAa,OAAOC,EAAK,CACzB,IAAIC,EAAMF,EAAU,WAAW,CAC/BC,EAAMC,EAAMA,EAAI,IAAI,CAAGlE,OAAO,QAAQ,CAAC,IAAI,CAACgE,EAChD,CACJ,CACA,OAAOC,CACX,EAYAH,EAAgB,SAAS,CAVzB,SAAmBK,CAAG,EAClB,OAAQA,GACJA,EAAI,IAAI,EACRA,EAAI,IAAI,YAAYC,UACpBD,EAAI,WAAW,EACfA,EAAI,WAAW,CAAC,MAAM,EACtBA,EAAI,WAAW,CAAC,MAAM,YAAYC,UAClCD,EAAI,WAAW,CAAC,OAAO,EACvBA,EAAI,WAAW,CAAC,OAAO,YAAYC,QAC3C,EAEA,IAAMC,EAAoB,CACtBC,QACAC,OACArD,OACAsD,KACA/D,MACAgE,OACAC,YACH,CACD,SAASC,EAAqBvB,CAAK,EAC/B,OAAOA,AAAU,OAAVA,GAAkBsB,YAAY,MAAM,CAACtB,IAAUiB,EAAkB,IAAI,CAACO,GAAQxB,aAAiBwB,EAC1G,CACAd,EAAgB,oBAAoB,CAAGa,EACvC,IAAME,EAAY,SAAUC,CAAM,CAAEC,CAAM,EAGtC,OAAO/E,OAAO,WAAW,CADHgF,AADAhF,OAAO,OAAO,CAAC8E,GACD,GAAG,CAAC,CAAC,CAACG,EAAKd,EAAI,GAAK,CAACc,EAAKF,EAAOZ,GAAK,EAE9E,CAqDAL,CAAAA,EAAgB,SAAS,CAjBzB,SAASoB,EAAU9B,CAAK,SACpB,AAAIA,GAASA,EAAM,WAAW,EAAIA,AAA2B,gBAA3BA,EAAM,WAAW,CAAC,IAAI,CAC7C+B,AArCf,SAA8BC,CAAK,EAC/B,IAAMC,EAAkB,EAAE,CACpBC,EAAeF,EAAM,eAAe,GAC1C,GAAIE,AAAwB,IAAxBA,EAAa,MAAM,CAAQ,CAC3B,IAAMC,EAAcD,CAAY,CAAC,EAAE,CAC7BE,EAAOJ,EAAM,OAAO,CAACG,GACrBE,EAASL,EAAM,QAAQ,CAAC,CAAEG,YAAAA,CAAY,GACtCG,EAAUN,EAAM,SAAS,CAAC,CAAEG,YAAAA,CAAY,GAC9CF,EAAgB,IAAI,CAAC,CAAEE,YAAAA,EAAaC,KAAAA,EAAMC,OAAAA,EAAQC,QAAAA,CAAQ,EAC9D,MAEI,IAAK,IAAMH,KAAeD,EAAc,CACpC,IAAME,EAAOJ,EAAM,OAAO,CAACG,GACrBG,EAAUN,EAAM,SAAS,CAAC,CAAEG,YAAAA,CAAY,GAC9CF,EAAgB,IAAI,CAAC,CAAEE,YAAAA,EAAaC,KAAAA,EAAME,QAAAA,CAAQ,EACtD,CAEJ,MAAO,CAAE,oCAAqC,GAAML,gBAAAA,CAAgB,CACxE,EAmBoCjC,GAE5BuC,MAAM,OAAO,CAACvC,GACPA,EAAM,GAAG,CAAC8B,GAEZP,EAAqBvB,GACnBA,EAEFA,aAAiBpD,OACf6E,EAAUzB,EAAO8B,GAGjB9B,CAEf,EAmBAU,EAAgB,WAAW,CAjB3B,SAAS8B,EAAYxC,CAAK,SACtB,AAAIA,GAASA,EAAM,mCAAmC,CAC3CyC,AApCf,SAAgCzC,CAAK,EACjC,IAAMgC,EAAQrB,EAAW,WAAW,CAAC,WAAW,GAChD,GAAIX,AAAiC,IAAjCA,EAAM,eAAe,CAAC,MAAM,CAAQ,CACpC,GAAM,CAAEqC,OAAAA,CAAM,CAAED,KAAAA,CAAI,CAAED,YAAAA,CAAW,CAAEG,QAAAA,CAAO,CAAE,CAAGtC,EAAM,eAAe,CAAC,EAAE,CACjE,CAAE0C,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE,CAAGP,EAC1BJ,EAAM,iBAAiB,CAAC,CAAEK,OAAAA,EAAQF,YAAAA,EAAaO,MAAAA,EAAOC,OAAAA,EAAQL,QAAAA,CAAQ,EAC1E,MAEI,IAAK,IAAMM,KAAO5C,EAAM,eAAe,CAAE,CACrC,GAAM,CAAEsC,QAAAA,CAAO,CAAEF,KAAAA,CAAI,CAAED,YAAAA,CAAW,CAAE,CAAGS,EACjC,CAAEF,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE,CAAGP,EAC1BJ,EAAM,iBAAiB,CAAC,CAAEM,QAAAA,EAASH,YAAAA,EAAaO,MAAAA,EAAOC,OAAAA,CAAO,EAClE,CAEJ,OAAOX,CACX,EAqBsChC,GAEzBuC,MAAM,OAAO,CAACvC,GACZA,EAAM,GAAG,CAACwC,GAEZjB,EAAqBvB,GACnBA,EAEFA,aAAiBpD,OACf6E,EAAUzB,EAAOwC,GAGjBxC,CAEf,CAEJ,EAAGU,GAAoBA,CAAAA,EAAkB,CAAC,IAC1ClC,EAAO,OAAO,CAAGkC,C,oCCvHjB,IAAMzC,EAAK,EAAQ,MACnB,GAAIN,AAAiB,aAAjBA,QAAQ,IAAI,CAEZ,IADAM,EAAG,IAAI,CAAC,kBACJ,CAACR,OAAO,wBAAwB,CAAE,CAClC,IAAIoF,EAAa,CAAC,CAClBpF,CAAAA,OAAO,wBAAwB,CAAGoF,EAClC,IAAIC,EAAS,EAAQ,KACrBD,CAAAA,EAAW,OAAO,CAAGC,EAAO,aAAa,CACzCD,EAAW,qBAAqB,CAAGC,EAAO,qBAAqB,CAC/DD,EAAW,gBAAgB,CAAGC,EAAO,gBAAgB,CACrDD,EAAW,QAAQ,CAAGC,EAAO,QAAQ,CACrCD,EAAW,MAAM,CAAG,IAAIE,MAAM,CAAC,EAAG,CAC9B,IAAK,CAACC,EAAQC,EAAGC,IACNJ,EAAO,SAAS,CAACG,EAEhC,GACAJ,EAAW,QAAQ,CAAG,IAAIE,MAAM,CAAC,EAAG,CAChC,IAAK,CAACC,EAAQC,EAAGC,IACNJ,EAAO,UAAU,CAACG,EAEjC,GACArG,OAAO,cAAc,CAACiG,EAAY,gBAAiB,CAC/C,IAAK,IACMC,EAAO,gBAAgB,EAEtC,GACAlG,OAAO,cAAc,CAACiG,EAAY,qBAAsB,CACpD,IAAK,IACMC,EAAO,qBAAqB,EAE3C,GACAlG,OAAO,cAAc,CAACiG,EAAY,UAAW,CACzC,IAAK,IACMC,EAAO,SAAS,CAAC,UAEhC,GACAlG,OAAO,cAAc,CAACiG,EAAY,cAAe,CAC7C,IAAK,IACMC,EAAO,cAAc,EAEpC,EACJ,OAEC,GAAInF,AAAiB,YAAjBA,QAAQ,IAAI,GACjBM,EAAG,IAAI,CAAC,kBACJ,CAACR,OAAO,wBAAwB,EAAE,CAClC,IAAIoF,EAAa,CAAC,CAClBpF,CAAAA,OAAO,wBAAwB,CAAGoF,EAClC,IAAMM,EAAS,EAAQ,KACvBA,EAAO,WAAW,GAClBN,EAAW,iBAAiB,CAAGM,EAAO,iBAAiB,AAC3D,CAEJ3E,EAAO,OAAO,CAAGf,OAAO,wBAAwB,A,uCCoR5C2F,E,WAzUJ,IAAMC,EAAW,EAAQ,MACnBC,EAAW,EAAQ,MACnBC,EAAqB,EAAQ,MAC7BC,EAAY,EAAQ,MACpBC,EAAgB,EAAQ,MACxBxF,EAAK,EAAQ,MACbyF,EAAyB,EAAQ,MACjCC,EAAS,EAAQ,MACjBC,EAAMN,EAAS,OAAO,CACtBO,EAAS,AAAC,GAAGH,EAAuB,kBAAkB,AAAD,EAAG,WACxDI,EAAwB,IAAI/E,IAC5BgF,EAAuB,IAAIC,qBAAqB,AAACC,IACnD,IAAMC,EAASD,EAAG,EAAE,CAAC,EAAE,CAAG,IAAMA,EAAG,EAAE,CAAC,EAAE,CAClC1G,EAAMuG,EAAsB,GAAG,CAACI,GACtC,GAAI3G,AAAQ4G,KAAAA,IAAR5G,GAAqBA,AAAgB4G,KAAAA,IAAhB5G,EAAI,KAAK,KAC9BuG,EAAsB,MAAM,CAACI,GACzB,CAACD,EAAG,WAAW,CAAC,WAAW,IAC3B,GAAI,CACAA,EAAG,WAAW,CAAC,WAAW,CAACA,EAAG,OAAO,CAAET,EAAU,OAAO,CAAC,QAAQ,CAAC,yBAAyB,CAAES,EAAG,EAAE,CAAC,EAAE,CAAEA,EAAG,EAAE,CAAC,EAAE,CACnH,CACA,MAAOG,EAAO,CACV3E,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE2E,EAAM,CAAC,CACjD,CAGZ,GAsBMC,EAAe,IAAIvH,QACnBwH,EAAkB,IAAIf,EAAmB,OAAO,CACtD,SAASgB,EAAiBC,CAAM,CAAE7F,CAAS,CAAEO,CAAM,CAAEuF,CAAO,CAAEC,CAAI,EAC9D,IAAIC,EAAQ/H,OAAO,mBAAmB,CAACsC,GAMvC,MALsB,YAAlB,OAAOA,GACPyF,CAAAA,EAAQA,EAAM,MAAM,CAAC,SAAUjH,CAAI,EAC/B,MAAO,CAACI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC2F,EAAc,oBAAoB,CAAE/F,EAC/E,EAAC,EAEEiH,EAAM,GAAG,CAAC,AAACjH,IACd,IAEIsC,EAyBAwB,EA3BAoD,EAAc,GACdC,EAAgB,GAEpB,GAAI,CACA7E,EAAQd,CAAM,CAACxB,EAAK,AACxB,CACA,MAAO0G,EAAO,CACVQ,EAAc,GACd3G,EAAG,IAAI,CAAC,eAAiBP,EAAO,IAAM0G,EAC1C,CACA,GAAIQ,EACA,GAAI,CACA5E,EAAQ0E,EAAK,KAAK,CAAChH,EAAK,CACxBkH,EAAc,GACdC,EAAgB,EACpB,CACA,MAAOT,EAAO,CACVnG,EAAG,KAAK,CAAC,YAAcP,EAAO,qCAClC,CAEJ,GAAIkH,EACA,OAAO,KAEX,IAAME,EAAalI,OAAO,wBAAwB,CAACsC,EAAQxB,GAC3D,GAAI,CAACoH,EACD,OAAO,KAGX,IAAIC,EAAW,GAUf,OATID,AAAmBX,KAAAA,IAAnBW,EAAW,GAAG,EAAkB,AAAwB,YAAxB,OAAO5F,CAAM,CAACxB,EAAK,CACnD8D,EAAO,UAGHsD,CAAAA,EAAW,GAAG,EAAIA,EAAW,QAAQ,AAAD,GACpCC,CAAAA,EAAW,EAAG,EAClBvD,EAAO,OAEXxB,EAAQgF,EAAYR,EAAQ7F,EAAWqB,EAAO,GAAOyE,EAASC,GACvD,CAAEhH,KAAAA,EAAM,WAAYoH,EAAW,UAAU,CAAEC,SAAAA,EAAUvD,KAAAA,EAAMxB,MAAAA,EAAO,YAAY,GAAE6E,CAA6B,CACxH,EACJ,CACA,IAAMI,EAAqB,SAAUT,CAAM,CAAE7F,CAAS,CAAEO,CAAM,CAAEuF,CAAO,CAAEC,CAAI,EACzE,IAAMQ,EAAQtI,OAAO,cAAc,CAACsC,UACpC,AAAIgG,AAAU,OAAVA,GAAkBA,IAAUtI,OAAO,SAAS,CACrC,KACJ,CACH,QAAS2H,EAAiBC,EAAQ7F,EAAWuG,EAAOT,EAASC,GAC7D,MAAOO,EAAmBT,EAAQ7F,EAAWuG,EAAOT,EAASC,EACjE,CACJ,EACMM,EAAc,SAAUR,CAAM,CAAE7F,CAAS,CAAEqB,CAAK,CAAEmF,EAAuB,EAAK,CAAEV,EAAU,IAAI,CAAEC,EAAO,IAAI,MAEzGlD,EACJ,OAFoBiD,EAApBA,AAAY,OAAZA,EAA8B,CAAC,EAAgBA,EAEvC,OAAOzE,GACX,IAAK,SAEGwB,EADAxB,aAAiBoF,OACV,SAEFpF,GAASA,EAAM,WAAW,EAAIA,AAA2B,gBAA3BA,EAAM,WAAW,CAAC,IAAI,CAClD,cAEFuC,MAAM,OAAO,CAACvC,GACZ,QAEFA,aAAiB3C,MACf,QAEF,AAAC,GAAGsG,EAAO,oBAAoB,AAAD,EAAG3D,GAC/B,QAEF,AAAC,GAAG2D,EAAO,SAAS,AAAD,EAAG3D,GACpB,UAEFpD,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACoD,EAAO,WAAaA,AAAgB,MAAhBA,EAAM,MAAM,CACnE,QAEFmF,GAAwBtB,EAAO,cAAc,CAAC7D,EAAO,UACnD,QAGA,SAEX,KACJ,KAAK,WACDwB,EAAO,WACP,KACJ,SACIA,EAAO,OAEf,CACA,GACI,GAAIA,AAAS,WAATA,GAAqBA,AAAS,aAATA,EAAqB,CAC1C,IAAM6D,EAAQf,EAAgB,aAAa,CAACtE,GAC5C,GAAIqF,GACIZ,CAAO,CAACY,EAAM,CAEd,OADAf,EAAgB,GAAG,CAACE,EAAQ7F,EAAWqB,GAChC,CAAEwB,KAAAA,EAAM,MAAO6D,EAAO,GAAIA,CAAM,CAGnD,OACK,EAAG,CACZ,GAAI7D,AAAS,UAATA,EACA,MAAO,CACHA,KAAAA,EACA,QAASxB,EAAM,GAAG,CAAC,AAACsF,GAAON,EAAYR,EAAQ7F,EAAW2G,EAAIH,EAAsBV,EAASC,GACjG,EAEC,GAAIlD,AAAS,gBAATA,EACL,MAAO,CAAEA,KAAAA,EAAM,MAAO,AAAC,GAAGmC,EAAO,SAAS,AAAD,EAAG3D,EAAO,EAElD,GAAIwB,AAAS,WAATA,GAAqBA,AAAS,aAATA,EAAqB,CAC/C,IAAM+D,EAAajB,EAAgB,GAAG,CAACE,EAAQ7F,EAAWqB,EAC1DyE,CAAAA,CAAO,CAACc,EAAW,CAAG,GACtB,IAAMC,EAAO,CACT,MAAOrB,KAAAA,EACP3C,KAAAA,EACA,KAAMxB,EAAM,WAAW,CAAGA,EAAM,WAAW,CAAC,IAAI,CAAG,GACnD,GAAIuF,EACJ,QAASpB,KAAAA,EACT,MAAOA,KAAAA,CACX,EAMA,OALa,OAATO,GACAA,CAAAA,EAAO,CAAE,MAAO1E,EAAO,KAAMwF,CAAK,GAEtCA,EAAK,OAAO,CAAGjB,EAAiBC,EAAQ7F,EAAWqB,EAAOyE,EAASC,GACnEc,EAAK,KAAK,CAAGP,EAAmBT,EAAQ7F,EAAWqB,EAAOyE,EAASC,GAC5Dc,CACX,CACK,GAAIhE,AAAS,WAATA,EACL,MAAO,CAAEA,KAAAA,EAAMxB,MAAAA,CAAM,EAEpB,GAAIwB,AAAS,YAATA,EAEL,OADAxB,EAAM,IAAI,CAAC,WAAc,EAAG,WAAc,GACnC,CACHwB,KAAAA,EACA,KAAMwD,EAAYR,EAAQ7F,EAAW,SAAU8G,CAAW,CAAEC,CAAU,EAClE1F,EAAM,IAAI,CAACyF,EAAaC,EAC5B,EACJ,OAEC,GAAIlE,AAAS,UAATA,EACL,MAAO,CACHA,KAAAA,EACAxB,MAAAA,EACA,QAASpD,OAAO,IAAI,CAACoD,GAAO,GAAG,CAACtC,GAAS,EACrCA,KAAAA,EACA,MAAOsH,EAAYR,EAAQ7F,EAAWqB,CAAK,CAACtC,EAAK,CACrD,GACJ,OAGA,MAAO,CACH,KAAM,QACNsC,MAAAA,CACJ,CAER,EACM2F,EAAqC,CAACnB,EAAQoB,KAChD,IAAMtI,EAAW+G,EAAa,GAAG,CAACuB,GAC9BC,EACA,CAAC;AACoC,wBADV,EAAEvI,EAAS,CAAC,CAC3C,GAAIkH,aAAkBnB,EAAS,YAAY,CAAE,CACzC,IAAMyC,EAAetB,EAAO,UAAU,GAAG,MAAM,CAAC,AAACuB,GACtCvB,EAAO,SAAS,CAACuB,GAAW,QAAQ,CAACH,GAE5CE,CAAAA,EAAa,MAAM,CAAG,IACtBD,GAAW;AAAG,oBAAoB,EAAEC,EAAa,IAAI,CAAC,MAAM,CAAC,CAC7DA,EAAa,OAAO,CAAC,AAACC,IAClBvB,EAAO,cAAc,CAACuB,EAAWH,EACrC,GAER,CACAnG,QAAQ,IAAI,CAACoG,EACjB,EACMG,EAAkB,CAACC,EAAavI,IAAS,IAAIqF,MAAMnG,OAAQ,CAC7D,KAAIoG,EAAQkD,EAAMhD,IACd,AAAIgD,AAAS,SAATA,EACOxI,EAGAyI,QAAQ,GAAG,CAACnD,EAAQkD,EAAMhD,EAG7C,GACA,SAASkD,EAAW5B,CAAM,CAAE6B,CAAO,CAAE1H,CAAS,CAAEnB,CAAI,EAChD,IAAM8I,EAAc,SAAUd,CAAI,EAC9B,OAAQA,EAAK,IAAI,EACb,IAAK,cACD,MAAO,AAAC,GAAG7B,EAAO,WAAW,AAAD,EAAG6B,EAAK,KAAK,CAC7C,KAAK,QACD,OAAOA,EAAK,KAAK,AACrB,KAAK,gBACD,OAAOlB,EAAgB,GAAG,CAACkB,EAAK,EAAE,CACtC,KAAK,QACD,OAAOY,EAAW5B,EAAQ6B,EAAS1H,EAAW6G,EAAK,KAAK,CAC5D,KAAK,SACD,OAAOJ,OAAO,IAAI,CAACI,EAAK,KAAK,CACjC,KAAK,OACD,OAAO,IAAIpE,KAAKoE,EAAK,KAAK,CAC9B,KAAK,UACD,OAAOe,QAAQ,OAAO,CAAC,CACnB,KAAMD,EAAYd,EAAK,IAAI,CAC/B,EACJ,KAAK,SACL,IAAK,WAAY,CACb,IAAM3E,EAAM2E,AAAc,WAAdA,EAAK,IAAI,CAAgB5I,OAAO,MAAM,CAAC,CAC/C,YAAaoJ,EAAgBpJ,OAAQ4I,EAAK,IAAI,CAClD,GAAK,CAAC,EACN,IAAK,GAAM,CAAE9H,KAAAA,CAAI,CAAEsC,MAAAA,CAAK,CAAE,GAAIwF,EAAK,OAAO,CACtC3E,CAAG,CAACnD,EAAK,CAAG4I,EAAYtG,GAE5B,OAAOa,CACX,CACA,IAAK,6BAA8B,CAC/B,IAAM2F,EAAcF,EAAYd,EAAK,KAAK,EAC1C,OAAO,WACH,OAAOgB,CACX,CACJ,CACA,IAAK,WAAY,CACb,IAAMC,EAAW,CAAC9H,EAAW6G,EAAK,EAAE,CAAC,CACrCvH,EAAG,IAAI,CAAC,wBAA0BwI,GAClC,IAAMC,EAAiBC,AA7PvC,SAAmCzJ,CAAE,EACjC,IAAMgH,EAAShH,CAAE,CAAC,EAAE,CAAG,IAAMA,CAAE,CAAC,EAAE,CAC5BK,EAAMuG,EAAsB,GAAG,CAACI,GACtC,GAAI3G,AAAQ4G,KAAAA,IAAR5G,EAAmB,CACnB,IAAMqJ,EAAQrJ,EAAI,KAAK,GACvB,GAAIqJ,AAAUzC,KAAAA,IAAVyC,EACA,OAAOA,CACf,CAEJ,EAoPiEH,GACjD,GAAIC,AAAmBvC,KAAAA,IAAnBuC,EACA,OAAOA,EAEX,IAAMd,EAAmB,SAAU,GAAGpI,CAAI,EACtCS,EAAG,IAAI,CAAC,kBAAmBT,GAC3B,IAAIqJ,EAAU,GACd,GAAI,CAACrC,EAAO,WAAW,GACnB,GAAI,CACA,IAAIsC,EAAgB9B,EAAYR,EAAQ7F,EAAWnB,GAC/CsJ,GACAA,CAAAA,EAAgBC,KAAK,KAAK,CAACA,KAAK,SAAS,CAACD,GAAc,EAE5DD,EAAUrC,AAAwG,KAAxGA,EAAO,WAAW,CAAC6B,EAAS7C,EAAU,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAE7E,EAAW6G,EAAK,EAAE,CAAEsB,EACnG,CACA,MAAO1C,EAAO,CACV3E,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE2E,EAAM,CAAC,CACjD,CAECyC,GACDlB,EAAmC,IAAI,CAAEC,EAEjD,EAIA,OAHAvB,EAAa,GAAG,CAACuB,EAAkBJ,EAAK,QAAQ,EAChD5I,OAAO,cAAc,CAACgJ,EAAkB,SAAU,CAAE,MAAOJ,EAAK,MAAM,AAAC,IACvEwB,AA5QhB,SAAmC9J,CAAE,CAAE+J,CAAE,CAAEZ,CAAO,CAAErG,CAAK,EACrD,IAAMkH,EAAK,IAAIC,QAAQnH,GACjBkE,EAAShH,CAAE,CAAC,EAAE,CAAG,IAAMA,CAAE,CAAC,EAAE,CAClC4G,EAAsB,GAAG,CAACI,EAAQgD,GAClCnD,EAAqB,QAAQ,CAAC/D,EAAO,CACjC9C,GAAAA,EACA,YAAa+J,EACbZ,QAAAA,CACJ,EAEJ,EAkQ0CI,EAAUjC,EAAQ6B,EAAST,GAC9CA,CACX,CACA,QACI,MAAM,AAAIwB,UAAU,CAAC,cAAc,EAAE5B,EAAK,IAAI,CAAC,CAAC,CACxD,CACJ,EACA,OAAOhI,EAAK,GAAG,CAAC8I,EACpB,CACA,SAASe,EAAgBjD,CAAK,EAC1B,MAAO,CACH,KAAM,YACN,MAAO,CACH,KAAM,QACN,MAAOA,EACP,QAAS,EAAE,AACf,CACJ,CACJ,CACA,SAASkD,EAAczB,CAAO,EAC1B,IAAMzB,EAAQ,AAAI/G,MAAMwI,EAGxB,OAFAjJ,OAAO,cAAc,CAACwH,EAAO,OAAQ,CAAE,MAAO,SAAU,GACxDxH,OAAO,cAAc,CAACwH,EAAO,QAAS,CAAE,MAAO,GAAI,GAC7CA,CACV,EAEA,AAAC,SAAUhB,CAAiB,EACxB,IAAMmE,EAAe,CAACC,EAAOC,EAAS,GAAGjK,KACrC,IAAMgH,EAASgD,EAAM,MAAM,AACvBhK,CAAAA,CAAI,CAAC,EAAE,EACPA,CAAAA,CAAI,CAAC,EAAE,CAAGuJ,KAAK,KAAK,CAACA,KAAK,SAAS,CAACvJ,CAAI,CAAC,EAAE,EAAC,EAE3CgH,EAAO,WAAW,GAInBvG,EAAG,IAAI,CAAC,4BAHRuG,EAAO,IAAI,CAACiD,KAAYjK,EAKhC,CAmOA4F,CAAAA,EAAkB,WAAW,CAlO7B,WACIQ,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,OAAO,CAAE,CAACgE,EAAO7I,EAAW+I,EAAOC,KAEhE,GADA1J,EAAG,IAAI,CAAC,CAAC,qBAAqB,EAAE0J,EAAW,CAAC,CAAC,EACzChK,QAAQ,UAAU,CAAE,CACpB,IAAIiK,EAAMjK,QAAQ,UAAU,CAAC,OAAO,CAACgK,GACjCnC,EAAOR,EAAYwC,EAAM,MAAM,CAAE7I,EAAWiJ,GAChDL,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAEkE,EAAOlC,EACzE,KACK,CACD,IAAIqC,EAAarJ,EACjB,KAAOqJ,EAAW,MAAM,EACpBA,EAAaA,EAAW,MAAM,CAElC,IAAID,EAAMC,EAAW,OAAO,CAACF,GACzBnC,EAAOR,EAAYwC,EAAM,MAAM,CAAE7I,EAAWiJ,GAChDL,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAEkE,EAAOlC,EACzE,CACJ,GACA5B,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,OAAO,CAAE,CAACgE,EAAO7I,EAAW+I,EAAOzE,KAChEhF,EAAG,IAAI,CAAC,CAAC,oBAAoB,EAAEgF,EAAE,WAAW,EAAEtE,EAAU,CAAC,EAEzD,IAAIC,EAAMrB,AADA+F,CACG,CAACL,EAAE,CACZuC,EAAOR,EAAYwC,EAAM,MAAM,CAAE7I,EAAWC,GAChDX,EAAG,IAAI,CAAC,CAAC,4BAA4B,EAAEuH,EAAK,EAAE,CAAC,QAAQ,EAAE,OAAO5G,EAAI,CAAC,EACrE2I,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAEkE,EAAOlC,EAC5E,GACA5B,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,MAAM,CAAE,CAACgE,EAAO7I,EAAW+I,EAAOzE,SAG3DuC,EAFJvH,EAAG,IAAI,CAAC,CAAC,kBAAkB,EAAEgF,EAAE,WAAW,EAAEtE,EAAU,CAAC,EACvD,IAAIC,EAAMnB,MAAM,CAACwF,EAAE,CAEnBuC,EAAOR,EAAYwC,EAAM,MAAM,CAAE7I,EAAWC,GAC5CX,EAAG,IAAI,CAAC,CAAC,2BAA2B,EAAEuH,EAAK,EAAE,CAAC,MAAM,CAAC,CAAG,OAAO5G,GAC/D2I,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAEkE,EAAOlC,EAC3E,GACA5B,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,aAAa,CAAE,CAACgE,EAAO7I,EAAW+I,EAAOzE,KACtEhF,EAAG,IAAI,CAAC,CAAC,2BAA2B,EAAEgF,EAAE,WAAW,EAAEtE,EAAU,CAAC,EAGhE,IAAIC,EAAMkJ,AAFkBN,EAAM,MAAM,CACnC,qBAAqB,CACM,IAAI,CAACA,EAAM,MAAM,EAC7ChC,EAAOR,EAAYwC,EAAM,MAAM,CAAE7I,EAAWC,GAChDX,EAAG,IAAI,CAAC,CAAC,mCAAmC,EAAEuH,EAAK,EAAE,CAAC,MAAM,CAAC,CAAG5G,GAChE2I,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAEkE,EAAOlC,EAC/E,GACA5B,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAE,CAACgE,EAAO7I,EAAW+I,EAAOzE,KAC3EsE,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,wBAAwB,CAAEkE,EAAO1C,EAAYwC,EAAM,MAAM,CAAE7I,EAAW6I,EAAM,MAAM,EACrI,GACA5D,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,WAAW,CAAE,CAACgE,EAAO7I,EAAW+I,EAAOzE,SAGhEuC,EAFJvH,EAAG,IAAI,CAAC,CAAC,uBAAuB,EAAEgF,EAAE,WAAW,EAAEtE,EAAU,CAAC,EAC5D,IAAIC,EAAM0E,EAAS,WAAW,CAE9BkC,EAAOR,EAAYwC,EAAM,MAAM,CAAE7I,EAAWC,GAC5CX,EAAG,IAAI,CAAC,CAAC,gCAAgC,EAAEuH,EAAK,EAAE,CAAC,MAAM,CAAC,CAAG,OAAO5G,GACpE2I,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAEkE,EAAOlC,EAC7E,GACA,IAAMuC,EAAsB,CAACC,EAAMC,KAC/B,IAAMC,EAAiB,CAACC,EAAIC,KACpBD,GAEAxD,AADY/H,OAAO,mBAAmB,CAACuL,GACjC,OAAO,CAAC,AAACzK,IACP0K,EACAJ,EAAK,IAAI,CAACtK,EAAMyK,CAAE,CAACzK,EAAK,EAGxBsK,EAAK,EAAE,CAACtK,EAAMyK,CAAE,CAACzK,EAAK,CAE9B,EAER,CACIuK,CAAAA,EAAc,EAAE,EAChBC,EAAeD,EAAc,EAAE,CAAE,IAEjCA,EAAc,IAAI,EAClBC,EAAeD,EAAc,IAAI,CAAE,GAE3C,EACArE,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,SAAS,CAAE,CAACgE,EAAO7I,EAAW+I,EAAOW,EAAU7K,SACxE8K,EACJ,IAAIL,EAAgB,KACpB,GAAI,CACAhK,EAAG,IAAI,CAAC,CAAC,wBAAwB,EAAEoK,EAAS,CAAC,CAAC,EAC9C,IAAIE,EAAW/K,EAAK,MAAM,CAAG,EAAIA,CAAI,CAACA,EAAK,MAAM,CAAG,EAAE,CAAG,KACzDA,EAAO4I,EAAWoB,EAAM,MAAM,CAAEA,EAAM,OAAO,CAAE7I,EAAWnB,GAC1D,IAAIyI,EAAc3B,EAAgB,GAAG,CAAC+D,EACnB,OAAfpC,GACAqB,EAAc,CAAC,iDAAiD,EAAEe,EAAS,CAAC,EAE5EE,GAAYA,AAAkB,aAAlBA,EAAS,IAAI,EACzBN,CAAAA,EAAgBzK,EAAK,GAAG,EAAC,EAE7B,IAAIgL,EAAS,GAAKxH,CAAAA,SAAS,SAAS,CAAC,IAAI,CAAC,KAAK,CAACiF,EAAa,CACzD,QACGzI,EACN,GACGgL,GAAUP,GACVF,EAAoBS,EAAQP,GAEhCK,EAAUtD,EAAYwC,EAAM,MAAM,CAAE7I,EAAW6J,GAC/CvK,EAAG,IAAI,CAAC,CAAC,iCAAiC,EAAEqK,EAAQ,EAAE,CAAC,MAAM,EAAErC,EAAY,IAAI,CAAC,CAAC,CAAC,CACtF,CACA,MAAO7B,EAAO,CACVkE,EAAUjB,EAAgBjD,EAC9B,QACQ,CACJmD,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAEkE,EAAOY,EAC3E,CACJ,GACA1E,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,YAAY,CAAE,SAAUgE,CAAK,CAAE7I,CAAS,CAAE+I,CAAK,CAAEe,CAAe,CAAEJ,CAAQ,CAAE7K,CAAI,EAC7G,IAAI8K,EACJ,GAAI,CACArK,EAAG,IAAI,CAAC,CAAC,0BAA0B,EAAEoK,EAAS,CAAC,EAC/C7K,EAAO4I,EAAWoB,EAAM,MAAM,CAAEA,EAAM,OAAO,CAAE7I,EAAWnB,GAC1D,IAAIkL,EAAOpE,EAAgB,GAAG,CAAC+D,GAC/B,GAAIK,AAAQ,MAARA,EACAzK,EAAG,KAAK,CAAC,CAAC,8CAA8C,EAAEoK,EAAS,CAAC,EACpEC,EAAUtD,EAAYwC,EAAM,MAAM,CAAE7I,EAAWwF,KAAAA,OAE9C,CACD,IAAIhG,EAAUsK,EAAkBnE,EAAgB,GAAG,CAACmE,GAAmBhL,OACvE6K,EAAUtD,EAAYwC,EAAM,MAAM,CAAE7I,EAAW+J,EAAK,KAAK,CAACvK,EAASX,GAAO,GAC9E,CACAS,EAAG,IAAI,CAAC,CAAC,sBAAsB,EAAEyK,EAAK,IAAI,CAAC,CAAC,CAChD,CACA,MAAOtE,EAAO,CACVkE,EAAUjB,EAAgBjD,EAC9B,QACQ,CACJmD,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAEkE,EAAOY,EAC9E,CACJ,GACA1E,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,UAAU,CAAE,SAAUgE,CAAK,CAAE7I,CAAS,CAAE+I,CAAK,CAAEe,CAAe,CAAEJ,CAAQ,CAAEM,CAAM,CAAEnL,CAAI,MAE/G8K,EADJrK,EAAG,IAAI,CAAC,CAAC,wBAAwB,EAAEoK,EAAS,SAAS,EAAEM,EAAO,aAAa,EAAEnL,EAAK,MAAM,CAAC,CAAC,EAE1F,GAAI,CACAA,EAAO4I,EAAWoB,EAAM,MAAM,CAAEA,EAAM,OAAO,CAAE7I,EAAWnB,GAC1D,IAAIoB,EAAM0F,EAAgB,GAAG,CAAC+D,EACnB,OAAPzJ,GACA0I,EAAc,CAAC,sBAAsB,EAAEqB,EAAO,2BAA2B,EAAEN,EAAS,CAAC,EAEzF,IAAIlK,EAAUsK,EAAkBnE,EAAgB,GAAG,CAACmE,GAAmB7J,EACvE0J,EAAUtD,EAAYwC,EAAM,MAAM,CAAE7I,EAAWC,CAAG,CAAC+J,EAAO,CAAC,KAAK,CAACxK,EAASX,GAAO,GACrF,CACA,MAAO4G,EAAO,CACVkE,EAAUjB,EAAgBjD,EAC9B,QACQ,CACJmD,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAEkE,EAAOY,EAC5E,CACJ,GACA1E,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,SAAS,CAAE,SAAUgE,CAAK,CAAE7I,CAAS,CAAE+I,CAAK,CAAEW,CAAQ,CAAEpF,CAAC,EACtF,IAAIqF,EACJ,GAAI,CACArK,EAAG,IAAI,CAAC,CAAC,uBAAuB,EAAEoK,EAAS,WAAW,CAAC,CAAEpF,GACzD,IAAIrE,EAAM0F,EAAgB,GAAG,CAAC+D,EACnB,OAAPzJ,GACA0I,EAAc,CAAC,qBAAqB,EAAE1K,OAAO,QAAQ,CAAC,IAAI,CAACqG,GAAG,2BAA2B,EAAEoF,EAAS,CAAC,EAEzG,IAAIrI,EAAQpB,CAAG,CAACqE,EAAE,CAClBqF,EAAUtD,EAAYwC,EAAM,MAAM,CAAE7I,EAAWqB,EACnD,CACA,MAAOoE,EAAO,CACVkE,EAAUjB,EAAgBjD,EAC9B,QACQ,CACJmD,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAEkE,EAAOY,EAC3E,CACJ,GACA1E,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,SAAS,CAAE,SAAUgE,CAAK,CAAE7I,CAAS,CAAE+I,CAAK,CAAEW,CAAQ,CAAEpF,CAAC,CAAEzF,CAAI,EAC5F,GAAI,CACAS,EAAG,IAAI,CAAC,CAAC,uBAAuB,EAAEoK,EAAS,WAAW,CAAC,CAAGpF,GAC1DzF,EAAO4I,EAAWoB,EAAM,MAAM,CAAEA,EAAM,OAAO,CAAE7I,EAAWnB,GAC1D,IAAIoB,EAAM0F,EAAgB,GAAG,CAAC+D,EACnB,OAAPzJ,GACA0I,EAAc,CAAC,qBAAqB,EAAE1K,OAAO,QAAQ,CAAC,IAAI,CAACqG,GAAG,2BAA2B,EAAEoF,EAAS,CAAC,EAEzGzJ,CAAG,CAACqE,EAAE,CAAGzF,CAAI,CAAC,EAAE,CAChB+J,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAEkE,EAAO,CACnE,KAAM,QACN,MAAO,EACX,EACJ,CACA,MAAOtD,EAAO,CACVmD,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAEkE,EAAOL,EAAgBjD,GAC3F,CACJ,GACAR,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,eAAe,CAAE,SAAUgE,CAAK,CAAE7I,CAAS,CAAE+I,CAAK,CAAEW,CAAQ,CAAEM,CAAM,CAAEnL,CAAI,MACnG8K,EACJ,IAAIL,EAAgB,KACpB,GAAI,CACAhK,EAAG,IAAI,CAAC,CAAC,4BAA4B,EAAEoK,EAAS,SAAS,EAAEM,EAAO,CAAC,EACnE,IAAIJ,EAAW/K,EAAK,MAAM,CAAG,EAAIA,CAAI,CAACA,EAAK,MAAM,CAAG,EAAE,CAAG,KACzDA,EAAO4I,EAAWoB,EAAM,MAAM,CAAEA,EAAM,OAAO,CAAE7I,EAAWnB,GAC1D,IAAI0B,EAASoF,EAAgB,GAAG,CAAC+D,EACnB,OAAVnJ,GACAoI,EAAc,CAAC,yBAAyB,EAAEqB,EAAO,2BAA2B,EAAEN,EAAS,CAAC,EAExFE,GAAYA,AAAkB,aAAlBA,EAAS,IAAI,EACzBN,CAAAA,EAAgBzK,EAAK,GAAG,EAAC,EAE7B,IAAIyI,EAAc/G,CAAM,CAACyJ,EAAO,CAC5BH,EAAS,GAAKxH,CAAAA,SAAS,SAAS,CAAC,IAAI,CAAC,KAAK,CAACiF,EAAa,CACzD,QACGzI,EACN,GACGgL,GAAUP,GACVF,EAAoBS,EAAQP,GAEhCK,EAAUtD,EAAYwC,EAAM,MAAM,CAAE7I,EAAW6J,EACnD,CACA,MAAOpE,EAAO,CACVkE,EAAUjB,EAAgBjD,EAC9B,QACQ,CACJmD,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAEkE,EAAOY,EACjF,CACJ,GACA1E,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,IAAI,CAAE,SAAUgE,CAAK,CAAE7I,CAAS,CAAE+I,CAAK,CAAEW,CAAQ,EAC9E,IAAIzJ,EAAM0F,EAAgB,GAAG,CAAC+D,GAC9Bd,EAAaC,EAAOhE,EAAU,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAEkE,EAAO1C,EAAYwC,EAAM,MAAM,CAAE7I,EAAWC,GAC3G,GACAgF,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAE,CAACgE,EAAO7I,EAAWzB,KACrEoH,EAAgB,MAAM,CAACkD,EAAM,MAAM,CAAE7I,EAAWzB,EACpD,GACA0G,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,cAAc,CAAE,CAACoF,EAAGjK,KACjD2F,EAAgB,KAAK,CAACsE,EAAE,MAAM,CAAC,EAAE,CAAEjK,EACvC,EACJ,EAKAyE,EAAkB,iBAAiB,CAHnC,WACI,OAAOkB,CACX,CAEJ,EAAGlB,GAAsBA,CAAAA,EAAoB,CAAC,IAC9C5E,EAAO,OAAO,CAAG4E,C,oCC/jBjBxG,OAAO,cAAc,CAACC,EAAS,aAAc,CAAE,MAAO,EAAK,GAC3DA,EAAQ,6BAA6B,CAAGA,EAAQ,cAAc,CAAGA,EAAQ,qBAAqB,CAAGA,EAAQ,gBAAgB,CAAGA,EAAQ,SAAS,CAAGA,EAAQ,UAAU,CAAGA,EAAQ,aAAa,CAAGA,EAAQ,QAAQ,CAAG,KAAK,EACrN,IAAMyG,EAAW,EAAQ,MACnBuF,EAAW,EAAQ,MACnBC,EAAuB,EAAQ,MAC/BC,EAAwB,EAAQ,MAChCvF,EAAY,EAAQ,MACpBwF,EAAiB,EAAQ,MACzB/K,EAAK,EAAQ,MACb0F,EAAS,EAAQ,MAEjBE,EAAS,AAAC,GAAGH,AADY,EAAQ,MACG,kBAAkB,AAAD,EAAG,WACxDE,EAAMN,EAAS,WAAW,CAC1B2F,EAAoB,IAAIH,EAAqB,OAAO,CACpDI,EAAoB,IAAInK,IACxBgF,EAAuB,IAAIC,qBAAqB,AAAC9G,IACnD,IAAMK,EAAM2L,EAAkB,GAAG,CAAChM,EACtBiH,MAAAA,IAAR5G,GAAqBA,AAAgB4G,KAAAA,IAAhB5G,EAAI,KAAK,KAC9B2L,EAAkB,MAAM,CAAChM,GACzB0G,EAAI,IAAI,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAE7E,EAAWzB,EAAI,GAE/E,GACMiM,EAAc,IAAIrM,QAClBsM,EAAgB,IAAIC,QAC1B,SAASC,EAAsBpM,CAAE,EAC7B,IAAMK,EAAM2L,EAAkB,GAAG,CAAChM,GAClC,GAAIK,AAAQ4G,KAAAA,IAAR5G,EAAmB,CACnB,IAAMqJ,EAAQrJ,EAAI,KAAK,GACvB,GAAIqJ,AAAUzC,KAAAA,IAAVyC,EACA,OAAOA,CACf,CACJ,CAeA,SAAS2C,EAAY3I,CAAS,CAAE1D,CAAE,EAC9B2G,EAAO,cAAc,CAACjD,EAAW,gBAAiB1D,EACtD,CACA,SAASsM,EAAY5I,CAAS,EAC1B,OAAOiD,EAAO,cAAc,CAACjD,EAAW,gBAC5C,CACA,IAAMjC,EAAYhB,QAAQ,SAAS,EAAI8L,AAdvC,WACI,GAAI5F,EACA,OAAOA,EAAO,cAAc,CAACpG,OAAQ,YAGrC,OAAM,AAAIJ,MAAM,oEAExB,GAQA,OAAMqM,EACF,YAAYC,CAAM,CAAE,CAWhB,GAVI,AAAkB,UAAlB,OAAOA,GACP,IAAI,CAAC,EAAE,CACH,AAAqB,UAArB,OAAOA,EAAO,EAAE,CAAgBA,EAAO,EAAE,CAAG,CAAC,EACjD,IAAI,CAAC,IAAI,CACL,AAAuB,UAAvB,OAAOA,EAAO,IAAI,CAAgBA,EAAO,IAAI,CAAG,CAAC,IAGrD,IAAI,CAAC,EAAE,CAAG,CAAC,EACX,IAAI,CAAC,IAAI,CAAG,CAAC,GAEb,CAAC,IAAI,CAAC,KAAK,GACX,MAAM,AAAItM,MAAM,mBAExB,CACA,OAAQ,CACJ,IAAIwD,EAAM,GACV,EAAG,CACC,IAAI8D,EAAQ/H,OAAO,mBAAmB,CAAC,IAAI,CAAC,EAAE,EAO9C,GANA+H,EAAM,OAAO,CAAC,AAACjH,IAEM,YAAb,OADI,IAAI,CAAC,EAAE,CAACA,EAAK,EAEjBmD,CAAAA,EAAM,EAAI,CAElB,GACI,CAACA,EACD,MAEJ8D,AADAA,CAAAA,EAAQ/H,OAAO,mBAAmB,CAAC,IAAI,CAAC,IAAI,GACtC,OAAO,CAAC,AAACc,IAEM,YAAb,OADI,IAAI,CAAC,IAAI,CAACA,EAAK,EAEnBmD,CAAAA,EAAM,EAAI,CAElB,EACJ,OAAS,GAAO,CAChB,OAAOA,CACX,CACJ,CAEA,SAAS+I,EAASpM,CAAI,CAAEiH,EAAU,IAAIoF,GAAK,EACvC,IAAM7E,EAAc,AAAChF,IACjB,GAAIyE,EAAQ,GAAG,CAACzE,GACZ,MAAO,CAAE,KAAM,QAAS,MAAO,IAAK,EAExC,GAAIA,GAASA,EAAM,WAAW,EAAIA,AAA2B,gBAA3BA,EAAM,WAAW,CAAC,IAAI,CACpD,MAAO,CAAE,KAAM,cAAe,MAAO,AAAC,GAAG2D,EAAO,SAAS,AAAD,EAAG3D,EAAO,EAEjE,GAAIuC,MAAM,OAAO,CAACvC,GAAQ,CAC3ByE,EAAQ,GAAG,CAACzE,GACZ,IAAIwF,EAAO,CACP,KAAM,QACN,MAAOoE,EAAS5J,EAAOyE,EAC3B,EAEA,OADAA,EAAQ,MAAM,CAACzE,GACRwF,CACX,CACK,GAAIxF,aAAiB6I,EAAS,MAAM,CACrC,MAAO,CAAE,KAAM,SAAU7I,MAAAA,CAAM,EAE9B,GAAIA,aAAiBoB,KACtB,MAAO,CAAE,KAAM,OAAQ,MAAOpB,EAAM,OAAO,EAAG,EAE7C,GAAIA,AAAS,MAATA,GAAiB,AAAiB,UAAjB,OAAOA,EAAoB,CACjD,GAAI,AAAC,GAAG2D,EAAO,SAAS,AAAD,EAAG3D,GACtB,MAAO,CACH,KAAM,UACN,KAAMgF,EAAY,SAAUS,CAAW,CAAEC,CAAU,EAC/C1F,EAAM,IAAI,CAACyF,EAAaC,EAC5B,EACJ,EAEC,GAAIyD,EAAY,GAAG,CAACnJ,GACrB,MAAO,CACH,KAAM,gBACN,GAAImJ,EAAY,GAAG,CAACnJ,EACxB,EAEJ,IAAIwF,EAAO,CACP,KAAMxF,aAAiB0J,EAAW,WAAa,SAC/C,KAAM1J,EAAM,WAAW,CAAGA,EAAM,WAAW,CAAC,IAAI,CAAG,GACnD,QAAS,EAAE,AACf,EAEA,IAAK,IAAIkG,KADTzB,EAAQ,GAAG,CAACzE,GACKA,EACbwF,EAAK,OAAO,CAAC,IAAI,CAAC,CACd,KAAMU,EACN,MAAOlB,EAAYhF,CAAK,CAACkG,EAAK,CAClC,GAGJ,OADAzB,EAAQ,MAAM,CAACzE,GACRwF,CACX,MACK,GAAI,AAAiB,YAAjB,OAAOxF,GAAwBoJ,EAAc,GAAG,CAACpJ,GACtD,MAAO,CACH,KAAM,6BACN,MAAOgF,EAAYhF,IACvB,OAEC,GAAI,AAAiB,YAAjB,OAAOA,EAEZ,MAAO,CACH,KAAM,WACN,GAHaiJ,EAAkB,GAAG,CAACjJ,GAInC,SAAUiJ,EAAkB,WAAW,CAACjJ,GACxC,OAAQA,EAAM,MAAM,AACxB,OAGA,MAAO,CAAE,KAAM,QAAS,MAAOA,CAAM,CAE7C,EAEA,OADgBxC,EAAK,GAAG,CAACwH,EAE7B,CACA,SAAS8E,EAAiBvM,CAAG,CAAE2B,CAAM,CAAE6K,CAAM,CAAEC,CAAO,CAAEC,CAAS,EAC7D,GAAK1H,MAAM,OAAO,CAACyH,GAEnB,IAAK,IAAME,KAAUF,EAAS,KAOtBhK,EANJ,GAAI,CAACkK,GAGDtN,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACsC,EAAQgL,EAAO,IAAI,GAAK,CAACD,EAF9D,SAIJ,IAAMnF,EAAa,CAAE,WAAYoF,EAAO,UAAU,AAAC,EAEnD,GAAIA,AAAgB,WAAhBA,EAAO,IAAI,CAAe,CAC1B,GAAIA,EAAO,KAAK,EACZ,GAAIA,EAAO,KAAK,CAAC,KAAK,CAElB,IAAIlK,AAAU,OADdA,CAAAA,EAAQsJ,EAAsBY,EAAO,KAAK,CAAC,KAAK,GAE5C,MAAM,AAAI7M,MAAM,gCAAkC6M,EAAO,KAAK,CAAC,KAAK,CAAG,QAAUA,EAAO,IAAI,CAChG,MAGAlK,EAAQsG,EAAY4D,EAAO,KAAK,CAAEA,EAAO,IAAI,CAAEH,GAGvDjF,EAAW,GAAG,CAAG,IACN9E,EAEX8E,EAAW,GAAG,CAAG,AAACqF,GACdnK,EAAQmK,EAGZrF,EAAW,YAAY,CAAG,EAC9B,MACK,GAAIoF,AAAgB,QAAhBA,EAAO,IAAI,CAAY,CAC5B,IAAIlK,CACJ8E,CAAAA,EAAW,GAAG,CAAG,IAAM9E,EACnBkK,EAAO,QAAQ,EACfpF,CAAAA,EAAW,GAAG,CAAG,AAACqF,IACdnK,EAAQmK,CACZ,GAEJnK,EAAQsG,EAAY4D,EAAO,KAAK,CACpC,CACAtN,OAAO,cAAc,CAACsC,EAAQgL,EAAO,IAAI,CAAEpF,EAC/C,CACJ,CAyIA,SAASwB,EAAYd,CAAI,CAAE9H,CAAI,CAAEqM,CAAM,EACnC,GAAI,CAACvE,EACD,MAAO,CAAC,EACZ,GAAIA,AAAc,UAAdA,EAAK,IAAI,CACT,OAAOA,EAAK,KAAK,CAEhB,GAAIA,AAAc,UAAdA,EAAK,IAAI,CACd,OAAOA,EAAK,OAAO,CAAC,GAAG,CAAC,AAAC0E,GAAW5D,EAAY4D,IAE/C,GAAI1E,AAAc,gBAAdA,EAAK,IAAI,CACd,MAAO,AAAC,GAAG7B,EAAO,WAAW,AAAD,EAAG6B,EAAK,KAAK,CAmBxC,MAvGyB5E,EAAWmJ,MAwGjClJ,EAlBH,GAAI2E,AAAc,WAAdA,EAAK,IAAI,CACd,OAAOqD,EAAS,MAAM,CAAC,IAAI,CAACrD,EAAK,KAAK,EAErC,GAAIA,AAAc,YAAdA,EAAK,IAAI,CACd,OAAOe,QAAQ,OAAO,CAAC,CAAE,KAAMD,EAAYd,EAAK,IAAI,CAAE,GAErD,GAAIA,AAAc,UAAdA,EAAK,IAAI,CACd,OAAO4E,EAAY5E,GAElB,GAAIA,AAAc,cAAdA,EAAK,IAAI,OACd,AAAIA,AAAoB,UAApBA,EAAK,KAAK,CAAC,IAAI,CACR4E,EAAY5E,EAAK,KAAK,EAGtB,AAAInI,MAAM,CAAC,oCAAoC,EAAEmI,EAAK,KAAK,CAAC,IAAI,CAAC,CAAC,EAK7E,IAAI6E,EAAQ7E,EAAK,KAAK,CACtB,GAAI6E,EAAO,CACP,IAAMC,EAAShB,EAAsBe,UACrC,AAAIC,GAMA,EADArM,EAAG,IAAI,CAAC,uCAAyCoM,GAC7C7E,AAAc,aAAdA,EAAK,IAAI,EACF,KAEP,EAGO,CAAC,CACZ,CAER,CACA,GAAI,OAAQA,EAAM,CACd,IAAM8E,EAAShB,EAAsB9D,EAAK,EAAE,EAC5C,GAAI8E,AAAWnG,KAAAA,IAAXmG,EAEA,OADMA,CAGd,CAEA,GAAI9E,AAAc,aAAdA,EAAK,IAAI,EACT,GAAI9H,EACAmD,EAAM0J,AAvLtB,SAAiCR,CAAM,CAAErM,CAAI,EACzC,IAAM8M,EAAuB,SAAU,GAAGhN,CAAI,EAC1C,MAAM,AAAIH,MAAM,yDACpB,EACAT,OAAO,cAAc,CAAC4N,EAAsB,OAAQ,CAChD,MAAO9M,EACP,SAAU,GACV,WAAY,EAChB,GACA,IAAMmD,EAAM,IAAIkC,MAAMyH,EAAsB,CACxC,MAAO,CAACxH,EAAQ7E,EAASsM,IACd,IAAIlE,QAAQ,CAACmE,EAASC,KACzB,IAAIlC,EAAkBe,EAAYrL,GAC7BsK,GACDA,CAAAA,EAAkBe,EAAYrL,EAAQ,YAAY,GAEtD,IAAIyM,EAAUpH,EAAU,OAAO,CAAC,OAAO,CAAC,UAAU,CAC9CkE,EAAQ,AAAC,GAAGsB,EAAe,OAAO,AAAD,EAAG4B,GACxChH,EAAI,IAAI,CAACgH,EAASjM,EAAW+I,EAAOe,EAAiBsB,EAAQrM,EAAMkM,EAASa,IAC5E1B,EAAsB,OAAO,CAAC,GAAG,CAACrB,EAAO,AAAClC,IACtC,GAAI,CACAkF,EAAQpE,EAAYd,GACxB,CACA,MAAOoD,EAAG,CACN+B,EAAO/B,EACX,CACJ,EACJ,GAEJ,UAAW,CAAC5F,EAAQyH,EAAWI,IACpB,IAAItE,QAAQ,CAACmE,EAASC,KACzB,IAAIC,EAAUpH,EAAU,OAAO,CAAC,OAAO,CAAC,eAAe,CACnDkE,EAAQ,AAAC,GAAGsB,EAAe,OAAO,AAAD,EAAG4B,GACxChH,EAAI,IAAI,CAACgH,EAASjM,EAAW+I,EAAOqC,EAAQrM,EAAMkM,EAASa,IAC3D1B,EAAsB,OAAO,CAAC,GAAG,CAACrB,EAAO,AAAClC,IACtC,GAAI,CACA,IAAIsF,EAAIxE,EAAYd,GACpBkF,EAAQI,EACZ,CACA,MAAOlC,EAAG,CACN+B,EAAO/B,EACX,CACJ,EACJ,EAER,GAEA,OADAW,EAAYiB,EAAsBT,GAC3BlJ,CACX,EAuI8CkJ,EAAQrM,OAErC,CACD,IAAMqN,EAAiB,SAAU,GAAGvN,CAAI,EACpC,MAAM,AAAIH,MAAM,mDACpB,EACAkM,EAAYwB,EAAgBvF,EAAK,EAAE,EACnC3E,EAAM,IAAIkC,MAAMgI,EAAgB,CAC5B,MAAO,CAAC/H,EAAQ7E,EAASsM,IACd,IAAIlE,QAAQ,CAACmE,EAASC,KACzB,IAAIlC,EAAkBe,EAAYrL,GAC9ByM,EAAUpH,EAAU,OAAO,CAAC,OAAO,CAAC,YAAY,CAChDkE,EAAQ,AAAC,GAAGsB,EAAe,OAAO,AAAD,EAAG4B,GACxChH,EAAI,IAAI,CAACgH,EAASjM,EAAW+I,EAAOe,EAAiBjD,EAAK,EAAE,CAAEoE,EAASa,IACvE1B,EAAsB,OAAO,CAAC,GAAG,CAACrB,EAAO,AAAClC,IACtC,GAAI,CACAkF,EAAQpE,EAAYd,GACxB,CACA,MAAOoD,EAAG,CACN+B,EAAO/B,EACX,CACJ,EACJ,GAEJ,UAAW,CAAC5F,EAAQyH,EAAWI,IACpB,IAAItE,QAAQ,CAACmE,EAASC,KACzB,IAAIC,EAAUpH,EAAU,OAAO,CAAC,OAAO,CAAC,SAAS,CAC7CkE,EAAQ,AAAC,GAAGsB,EAAe,OAAO,AAAD,EAAG4B,GACxChH,EAAI,IAAI,CAACgH,EAASjM,EAAW+I,EAAOlC,EAAK,EAAE,CAAEoE,EAASa,IACtD1B,EAAsB,OAAO,CAAC,GAAG,CAACrB,EAAO,AAACsD,IACtC,GAAI,CACA,IAAIF,EAAIxE,EAAY0E,GACpBN,EAAQI,EACZ,CACA,MAAOlC,EAAG,CACN+B,EAAO/B,EACX,CACJ,EACJ,EAER,EACJ,OAGA/H,EAAM,CAAC,EAWX,OA7L0BD,EAoLDC,EApLYkJ,EAoLPvE,EAAK,EAAE,CA5HzC5I,OAAO,gBAAgB,CAACgE,EAAW,CAC/B,MAAO,CACH,WAAY,GACZ,SAAU,GACV,MA3DI,SAAUqC,CAAC,CAAEkH,CAAQ,EAE7B,GAAI,YADO,OAAOA,EAEd,MAAM,AAAI9M,MAAM,kDAEpB,OAAO,IAAIkJ,QAAQ,CAACmE,EAASC,KACzB,IAAIC,EAAUpH,EAAU,OAAO,CAAC,OAAO,CAAC,SAAS,CAC7CkE,EAAQ,AAAC,GAAGsB,EAAe,OAAO,AAAD,EAAG4B,GAClCK,EAAUrB,EAAS,CAACO,EAAS,EACnCvG,EAAI,IAAI,CAACgH,EAASjM,EAAW+I,EAAOqC,EAAQ9G,EAAGgI,GAC/ClC,EAAsB,OAAO,CAAC,GAAG,CAACrB,EAAO,AAAClC,IACtC,GAAI,CACA,IAAI0F,EAAM5E,EAAYd,EACtB5E,CAAAA,CAAS,CAACqC,EAAE,CAAGkH,EACfO,EAAQQ,EACZ,CACA,MAAOtC,EAAG,CACN+B,EAAO/B,EACX,CACJ,EACJ,EACJ,CAuCI,EACA,MAAO,CACH,WAAY,GACZ,SAAU,GACV,MA1CI,SAAU3F,CAAC,EACnB,OAAO,IAAIsD,QAAQ,CAACmE,EAASC,KACzB,IAAIC,EAAUpH,EAAU,OAAO,CAAC,OAAO,CAAC,SAAS,CAC7CkE,EAAQ,AAAC,GAAGsB,EAAe,OAAO,AAAD,EAAG4B,GACxChH,EAAI,IAAI,CAACgH,EAASjM,EAAW+I,EAAOqC,EAAQ9G,GAC5C8F,EAAsB,OAAO,CAAC,GAAG,CAACrB,EAAO,AAAClC,IACtC,GAAI,CACA,IAAM2F,EAAc7E,EAAYd,EAChC5E,CAAAA,CAAS,CAACqC,EAAE,CAAGkI,EACfT,EAAQS,EACZ,CACA,MAAOvC,EAAG,CACN+B,EAAO/B,EACX,CACJ,EACJ,EACJ,CA2BI,EACA,OAAQ,CACJ,WAAY,GACZ,SAAU,GACV,MA9BK,WACT,OAAO,IAAIrC,QAAQ,CAACmE,EAASC,KACzB,IAAIC,EAAUpH,EAAU,OAAO,CAAC,OAAO,CAAC,IAAI,CACxCkE,EAAQ,AAAC,GAAGsB,EAAe,OAAO,AAAD,EAAG4B,GACxChH,EAAI,IAAI,CAACgH,EAASjM,EAAW+I,EAAOqC,GACpChB,EAAsB,OAAO,CAAC,GAAG,CAACrB,EAAO,AAAClC,IACtC,GAAI,CACA,IAAI4F,EAAe9E,EAAYd,GAC/BkF,EAAQU,EACZ,CACA,MAAOxC,EAAG,CACN+B,EAAO/B,EACX,CACJ,EACJ,EACJ,CAgBI,CACJ,GA6GIkB,EAAiBjJ,EAAKA,EAAK2E,EAAK,EAAE,CAAEA,EAAK,OAAO,GAChD6F,AApPR,SAASA,EAAmB9N,CAAG,CAAE2B,CAAM,CAAE6K,CAAM,CAAEjF,CAAU,CAAEwG,EAAW,EAAK,EACzE,GAAIxG,AAAe,OAAfA,EACA,OACJ,IAAII,EAAQ,CAAC,EACToG,GAGApG,CAAAA,EAFa,WACb,CACW,EAEf4E,EAAiBvM,EAAK2H,EAAO6E,EAAQjF,EAAW,OAAO,EACvDuG,EAAmB9N,EAAK2H,EAAO6E,EAAQjF,EAAW,KAAK,EACvDlI,OAAO,cAAc,CAACsC,EAAQgG,EAClC,EAwO2BrE,EAAKA,EAAK2E,EAAK,EAAE,CAAEA,EAAK,KAAK,CAAEA,AAAc,aAAdA,EAAK,IAAI,EAC3D+D,EAAY1I,EAAK2E,EAAK,EAAE,EACpB3E,EAAI,WAAW,EACfjE,OAAO,cAAc,CAACiE,EAAI,WAAW,CAAE,OAAQ,CAAE,MAAO2E,EAAK,IAAI,AAAC,GAEtE2D,EAAY,GAAG,CAACtI,EAAK2E,EAAK,EAAE,GAC5B+F,AA/aR,SAA+BrO,CAAE,CAAE8C,CAAK,EACpC,IAAMkH,EAAK,IAAIC,QAAQnH,GACvBkJ,EAAkB,GAAG,CAAChM,EAAIgK,GAC1BnD,EAAqB,QAAQ,CAAC/D,EAAO9C,EAEzC,EA0a8BsI,EAAK,EAAE,CAAE3E,GACxBA,CACX,CACJ,CAtXAhE,EAAQ,QAAQ,CAAG6M,CAuXnB,OAAM8B,EACF,WAAWC,CAAY,CAAE,CACrB,IAAIhE,EAAU,GACd,OAAQgE,GACJ,IAAK,SACDhE,EAAUjE,EAAU,OAAO,CAAC,OAAO,CAAC,OAAO,CAC3C,KACJ,KAAK,UACDiE,EAAUjE,EAAU,OAAO,CAAC,OAAO,CAAC,OAAO,CAC3C,KACJ,KAAK,SACDiE,EAAUjE,EAAU,OAAO,CAAC,OAAO,CAAC,MAAM,CAC1C,KACJ,KAAK,iBACDiE,EAAUjE,EAAU,OAAO,CAAC,OAAO,CAAC,aAAa,CACjD,KACJ,KAAK,uBACDiE,EAAUjE,EAAU,OAAO,CAAC,OAAO,CAAC,kBAAkB,CACtD,KACJ,KAAK,sBACDiE,EAAUjE,EAAU,OAAO,CAAC,OAAO,CAAC,iBAAiB,CACrD,KACJ,KAAK,eACDiE,EAAUjE,EAAU,OAAO,CAAC,OAAO,CAAC,WAAW,AAEvD,CACA,OAAOiE,CACX,CACA,YAAY,GAAGjK,CAAI,CAAE,CACjB,IAAI,CAAC,WAAW,CAAG,GACnB,IAAI,CAAC,UAAU,CAAG,KAClB,IAAI,CAAC,YAAY,CAAG,KACpB,IAAI,CAAC,SAAS,CAAG2G,KAAAA,EACjB,IAAI,CAAC,OAAO,CAAG,GACf,IAAI,CAAC,OAAO,CAAG,GACf,IAAIuH,EAAY,OAAOC,SAAS,CAAC,EAAE,CACnC,GAAI,WAAaD,EAAW,CACxB,IAAIE,EAAOD,SAAS,CAAC,EAAE,CACnBjO,EAAOiO,SAAS,CAAC,EAAE,AACvB,KAAI,CAAC,OAAO,CAAGC,EACf,IAAI,CAAC,OAAO,CAAGlO,GAAckO,EAC7B,IAAI,CAAC,WAAW,CAAG,GACnB,IAAI,CAAC,YAAY,CAAG,KACpB,IAAI,CAAC,UAAU,CAAG,IAAIrF,QAAQ,CAACmE,EAASC,KACpC,IAAIlD,EAAU,IAAI,CAAC,UAAU,CAACmE,GAC1BlE,EAAQ,AAAC,GAAGsB,EAAe,OAAO,AAAD,EAAGvB,GACxC7D,EAAI,IAAI,CAAC6D,EAAS9I,EAAW+I,EAAOhK,GACpCqL,EAAsB,OAAO,CAAC,GAAG,CAACrB,EAAO,AAAClC,IACtC,GAAI,CACA,IAAIqG,EAAUvF,EAAYd,EAC1B,KAAI,CAAC,YAAY,CAAGqG,EACpB,IAAI,CAAC,WAAW,CAAG,GACnB,IAAI,CAAC,SAAS,CAAGrG,EAAK,EAAE,CACxBkF,EAAQmB,EACZ,CACA,MAAOjD,EAAG,CACN+B,EAAO/B,EACX,CACJ,EACJ,EACJ,MAEI,IAAI,CAAC,YAAY,CAAG+C,SAAS,CAAC,EAAE,CAChC,IAAI,CAAC,WAAW,CAAG,GACnB,IAAI,CAAC,UAAU,CAAG,KAClB,IAAI,CAAC,SAAS,CAAGxH,KAAAA,CAEzB,CACA,WAAY,CACR,IAAI2H,EAAU,IAAI,CAAC,UAAU,CAC7B,GAAIA,AAAY,OAAZA,QAGA,GAAI,IAAI,CAAC,WAAW,CAChBA,EAAU,IAAIvF,QAAQ,CAACmE,EAASC,KAC5BD,EAAQ,IAAI,CAAC,YAAY,CAC7B,GACA,IAAI,CAAC,UAAU,CAAGoB,OAGlB,MAAMzO,MAAM,0DAGpB,OAAOyO,CACX,CACA,cAAe,CACX,OAAO,IAAI,CAAC,WAAW,AAC3B,CACJ,CAIA,SAAS1B,EAAY5E,CAAI,EACrB,IAAM5G,EAAM4G,EAAK,KAAK,CACtB,IAAK,GAAM,CAAE9H,KAAAA,CAAI,CAAEsC,MAAAA,CAAK,CAAE,GAAIwF,EAAK,OAAO,CACtC5G,CAAG,CAAClB,EAAK,CAAG4I,EAAYtG,GAE5B,OAAOpB,CACX,CACA,SAASmN,EAActE,CAAO,CAAEvJ,CAAO,EACnC0F,EAAI,EAAE,CAAC6D,EAAS,CAACD,EAAOwE,EAAiB9O,EAAI,GAAGM,KAC5C,GAXG,AAA0B,UAA1B,OAAOyO,AAWMzE,EAXA,QAAQ,EAYhBA,AAAmB,IAAnBA,EAAM,QAAQ,EAAUA,AAAmBrD,KAAAA,IAAnBqD,EAAM,QAAQ,CAAgB,CACtD/H,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAEgI,EAAQ,iCAAiC,EAAED,EAAM,QAAQ,CAAC,CAAC,CAAC,EACrF,MACJ,CAEAwE,IAAoBrN,EACpBT,EAAQhB,KAAOM,GAGfoG,EAAI,IAAI,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAE7E,EAAWqN,EAAiB9O,EAEpG,EACJ,CACA,SAASgP,EAAe1E,CAAK,CAAEE,CAAK,CAAElC,CAAI,EACtC,GAAI,CACAuD,EAAsB,OAAO,CAAC,MAAM,CAACrB,EAAOlC,GAAM,MAAM,CAACkC,EAC7D,QACQ,CACJqB,EAAsB,OAAO,CAAC,MAAM,CAACrB,EACzC,CACJ,CACA9D,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAE0I,GACjDtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAE0I,GACpDtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAE0I,GACnDtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAE0I,GACvDtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,wBAAwB,CAAE0I,GAC5DtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAE0I,GACtDtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAE0I,GACnDtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAE0I,GACpDtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAE0I,GACnDtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAE0I,GACnDtI,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAE0I,GACzDH,EAAcvI,EAAU,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAE,CAAC2I,EAAY1B,KAC5DxB,EAAkB,KAAK,CAACkD,EAAY7F,EAAYmE,GACpD,GACA7G,EAAI,EAAE,CAACJ,EAAU,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAE0I,GAC9CH,EAAcvI,EAAU,OAAO,CAAC,QAAQ,CAAC,yBAAyB,CAAE,AAAC2I,IACjElO,EAAG,IAAI,CAAC,kCAAmCkO,GAC3ClD,EAAkB,MAAM,CAACkD,EAC7B,GACAxO,QAAQ,EAAE,CAAC,OAAQ,KACfiG,EAAI,IAAI,CAACJ,EAAU,OAAO,CAAC,OAAO,CAAC,cAAc,CAAE7E,EACvD,GACA,IAAMyN,EAAY,CAAC,YAAa,eAAe,CACzCC,EAAiB,CACnB,aACA,cACA,eACA,UACA,UACA,YACH,CACKC,EAAiB,AAAC1N,IACpB,GAAI,CAACA,EAAI,YAAY,GACjB,MAAMvB,MAAM,6EAEpB,EACA,SAASkP,EAAyBC,CAAY,EAC1C,IAAMC,EAAuB,WAAc,EAC3C7P,OAAO,cAAc,CAAC6P,EAAsB,OAAQ,CAChD,MAAOD,EAAa,OAAO,AAC/B,GACA5P,OAAO,cAAc,CAAC6P,EAAsB,OAAQ,CAChD,WAAY,GACZ,MAAOD,EAAa,OAAO,AAC/B,GACA,IAAI3L,EAAM,IAAIkC,MAAM0J,EAAsB,CACtC,eAAgB,AAACzJ,IACbsJ,EAAeE,GACRrG,QAAQ,cAAc,CAACqG,EAAa,YAAY,GAE3D,eAAgB,CAACxJ,EAAQ8H,KACrB,MAAM,AAAIzN,MAAM,mDACpB,EACA,aAAc,AAAC2F,IACXsJ,EAAeE,GACRrG,QAAQ,YAAY,CAACqG,EAAa,YAAY,GAEzD,kBAAmB,AAACxJ,IAChBsJ,EAAeE,GACRrG,QAAQ,iBAAiB,CAACqG,IAErC,yBAA0B,CAACxJ,EAAQC,KAC/BqJ,EAAeE,GACRrG,QAAQ,wBAAwB,CAACqG,EAAa,YAAY,CAAEvJ,IAEvE,IAAK,CAACD,EAAQC,KACVqJ,EAAeE,GACRrG,QAAQ,GAAG,CAACqG,EAAa,YAAY,CAAEvJ,IAElD,eAAgB,CAACD,EAAQC,KACrBqJ,EAAetJ,GACRmD,QAAQ,cAAc,CAACqG,EAAa,YAAY,CAAEvJ,IAE7D,eAAgB,CAACD,EAAQC,EAAGyJ,KACxBJ,EAAeE,GACRrG,QAAQ,cAAc,CAACqG,EAAa,YAAY,CAAEvJ,EAAGyJ,IAEhE,IAAK,CAAC1J,EAAQC,EAAGC,IACb,AAAI,AAAa,UAAb,OAAOD,IACHnF,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAACuO,EAAgBpJ,IAI/CnF,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAACsO,EAAWnJ,IAFnC1F,AADGiP,CACA,CAACvJ,EAAE,EAOrBqJ,EAAeE,GACRrG,QAAQ,GAAG,CAACqG,EAAa,YAAY,CAAEvJ,IAElD,IAAK,CAACD,EAAQC,EAAGjD,EAAOkD,KACpBoJ,EAAeE,GACRrG,QAAQ,GAAG,CAACqG,EAAa,YAAY,CAAEvJ,EAAGjD,EAAOkD,IAE5D,QAAS,AAACF,IACNsJ,EAAeE,GACRrG,QAAQ,OAAO,CAACqG,EAAa,YAAY,GAEpD,MAAO,CAACxJ,EAAQ7E,EAASwO,KACrBL,EAAeE,GACfrG,QAAQ,KAAK,CAACqG,EAAa,YAAY,CAAErO,EAASwO,EACtD,EACA,UAAW,CAAC3J,EAAQ2J,EAAU9B,KAE1B,GADAyB,EAAeE,GACX,AAAqC,YAArC,OAAOA,EAAa,YAAY,CAChC,MAAMnP,MAAM,uCAEhB,OAAO,IAAIkJ,QAAQ,CAACmE,EAASC,KACzB,IAAIC,EAAUpH,EAAU,OAAO,CAAC,OAAO,CAAC,SAAS,CAC7CkE,EAAQ,AAAC,GAAGsB,EAAe,OAAO,AAAD,EAAG4B,GACpCb,EAASyC,EAAa,SAAS,CACnC5I,EAAI,IAAI,CAACgH,EAASjM,EAAW+I,EAAOqC,EAAQH,EAAS+C,IACrD5D,EAAsB,OAAO,CAAC,GAAG,CAACrB,EAAO,AAAClC,IACtC,GAAI,CACAkF,EAAQpE,EAAYd,GACxB,CACA,MAAOoD,EAAG,CACN+B,EAAO/B,EACX,CACJ,EACJ,EACJ,CACJ,GAWA,OATAkD,AADgBU,EAAa,UAAU,CAC/B,IAAI,CAAC,AAAC5L,IACV,IAAMgM,EAAa,OAAOhM,EAC1B,GAAIgM,AAAe,aAAfA,GAA6BA,AAAe,WAAfA,EAAyB,CACtD,IAAM1P,EAAKsM,EAAY5I,GACnB1D,GACAqM,EAAY1I,EAAK3D,EAEzB,CACJ,GACO2D,CACX,CAIAhE,EAAQ,aAAa,CAHrB,SAAuB2B,CAAM,EACzB,OAAO+N,EAAyB,IAAIf,EAAa,SAAUhN,GAC/D,EAKA3B,EAAQ,UAAU,CAHlB,SAAoBoG,CAAC,EACjB,OAAOsJ,EAAyB,IAAIf,EAAa,UAAWvI,GAChE,EAKApG,EAAQ,SAAS,CAHjB,SAAmBoG,CAAC,EAChB,OAAOsJ,EAAyB,IAAIf,EAAa,SAAUvI,GAC/D,EAKApG,EAAQ,gBAAgB,CAHxB,WACI,OAAO0P,EAAyB,IAAIf,EAAa,kBACrD,EAKA3O,EAAQ,qBAAqB,CAH7B,WACI,OAAO0P,EAAyB,IAAIf,EAAa,wBACrD,EAKA3O,EAAQ,cAAc,CAHtB,WACI,OAAO0P,EAAyB,IAAIf,EAAa,gBACrD,EAOA3O,EAAQ,6BAA6B,CALrC,SAAuC2J,CAAW,EAC9C,IAAMkC,EAAO,IAAMlC,EAEnB,OADA4C,EAAc,GAAG,CAACV,GACXA,CACX,C,uBC5tBA,IAAMmE,EAAK,EAAQ,MACbtN,EAAO,EAAQ,MAErB,SAASuN,EAAKjH,CAAO,EACnBpG,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAEoG,EAAQ,CAAC,CAC1C,CAGA,IAAMkH,EAAiB,gCACjBC,EAAc,OACdC,EAAiB,aAGvB,SAASC,EAAOC,CAAG,CAAyBC,CAAO,EACjD,IAAMC,EAAQnM,CAAAA,CAAQkM,CAAAA,GAAWA,EAAQ,KAAK,AAAD,EACvCxO,EAAM,CAAC,EAkCb,OA/BAuO,EAAI,QAAQ,GAAG,KAAK,CAACF,GAAgB,OAAO,CAAC,SAAUK,CAAI,CAAEC,CAAG,EAE9D,IAAMC,EAAcF,EAAK,KAAK,CAACP,GAE/B,GAAIS,AAAe,MAAfA,EAAqB,CACvB,IAAM3L,EAAM2L,CAAW,CAAC,EAAE,CAEtBzM,EAAOyM,CAAW,CAAC,EAAE,EAAI,GACvBC,EAAM1M,EAAI,MAAM,CAAG,EACnB2M,EAAiB3M,AAAW,MAAXA,CAAG,CAAC,EAAE,EAAYA,AAAa,MAAbA,CAAG,CAAC0M,EAAI,AAI7CE,AAH8B,OAAX5M,CAAG,CAAC,EAAE,EAAYA,AAAa,MAAbA,CAAG,CAAC0M,EAAI,EAG3BC,GACpB3M,EAAMA,EAAI,SAAS,CAAC,EAAG0M,GAGnBC,GACF3M,CAAAA,EAAMA,EAAI,OAAO,CAACiM,EA7BZ,KA6BgC,GAIxCjM,EAAMA,EAAI,IAAI,GAGhBnC,CAAG,CAACiD,EAAI,CAAGd,CACb,MAAWsM,GACTP,EAAI,CAAC,8CAA8C,EAAES,EAAM,EAAE,EAAE,EAAED,EAAK,CAAC,CAE3E,GAEO1O,CACT,CAsCAJ,EAAO,OAAO,CAAC,MAAM,CAnCrB,SAAiB4O,CAAO,EACtB,IAAIQ,EAAarO,EAAK,OAAO,CAAC5B,QAAQ,GAAG,GAAI,QACzCkQ,EAAyB,OACzBR,EAAQ,GAERD,IACkB,MAAhBA,EAAQ,IAAI,EACdQ,CAAAA,EAAaR,EAAQ,IAAI,AAAD,EAEF,MAApBA,EAAQ,QAAQ,EAClBS,CAAAA,EAAWT,EAAQ,QAAQ,AAAD,EAEP,MAAjBA,EAAQ,KAAK,EACfC,CAAAA,EAAQ,EAAG,GAIf,GAAI,CAEF,IAAMS,EAASZ,EAAML,EAAG,YAAY,CAACe,EAAY,CAAEC,SAAAA,CAAS,GAAI,CAAER,MAAAA,CAAM,GAUxE,OARAzQ,OAAO,IAAI,CAACkR,GAAQ,OAAO,CAAC,SAAUjM,CAAG,EAClCjF,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAACe,QAAQ,GAAG,CAAEkE,GAE5CwL,GACTP,EAAI,CAAC,CAAC,EAAEjL,EAAI,mEAAmE,CAAC,EAFhFlE,QAAQ,GAAG,CAACkE,EAAI,CAAGiM,CAAM,CAACjM,EAAI,AAIlC,GAEO,CAAEiM,OAAAA,CAAO,CAClB,CAAE,MAAOlF,EAAG,CACV,MAAO,CAAE,MAAOA,CAAE,CACpB,CACF,EAGApK,EAAO,OAAO,CAAC,KAAK,CAAG0O,C,oCChHvB,SAASa,EAAiBnP,CAAG,CAAEiD,CAAG,CAAE7B,CAAK,EAKrC,OAJI6B,KAAOjD,EACPhC,OAAO,cAAc,CAACgC,EAAKiD,EAAK,CAAE,MAAO7B,EAAO,WAAY,GAAM,aAAc,GAAM,SAAU,EAAK,GAClGpB,CAAG,CAACiD,EAAI,CAAG7B,EAEXpB,CACX,C"}