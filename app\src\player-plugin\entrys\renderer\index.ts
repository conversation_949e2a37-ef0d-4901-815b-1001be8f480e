import './variable.css'
import { initialVueApp } from '@root/common/vue-app-initial'
import { createApp } from 'vue'
import App from './App.vue'

import { AplayerServer } from '@root/common/player/server/server'
import requireNodeFile from '@root/common/require-node-file'
import { GetLogsPath, GetPlayerControlAddonNodeName, GetProfilesPath, GetSdkPath, GetXxxNodePath } from '@root/common/xxx-node-path'
import path from 'path'

import { AplayerStack } from '@root/common/player/impl/aplayer-stack'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { MediaType, XlRequestHeaders } from '@root/common/player/base'
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios'
import { FileSystemAWNS } from '@root/common/fs-utilities'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import {ipc<PERSON><PERSON><PERSON>} from 'electron'
import * as TaskBaseType from '@root/common/task/base'
import {LinkHubHelper} from '@root/common/link-hub/client/link-hub-helper'
import {PlaybackRecordHelper} from '@root/common/link-hub/client/playback-record-helper'
import { TaskManager } from '@root/common/task/client/task-manager'
import { PopUpNS } from '@root/common/pop-up'
import { AccountHelper } from '@root/common/account/client/accountHelper'
import { AccountHelperEventKey } from '@root/common/account/account-type'
import { DownloadFileRecord } from '@root/common/download-file-record/client/download-file-record'

let clientName: string = 'player'
if (process.platform === 'darwin') {
  client.start({ name: clientName }, 'thunder', false, (...args) => {
    console.log('client callback', ...args)
  })
} else {
  client.start({ name: clientName })
}
TaskManager.GetInstance().init();
PopUpNS.enableDevTools();

const app = createApp(App)
initialVueApp(app)
app.mount('#app')

class PlayRecordCache {
  private r: ThunderClientAPI.dataStruct.dataModals.PlaybackRecord | null = null;
  private insertTime: number = 0;
  private getRecordInfo: {url: string, file_index: number} = {url: '', file_index: -1};

  public async getRecord(url: string, index?: number): Promise<ThunderClientAPI.dataStruct.dataModals.PlaybackRecord | null> {
    this.getRecordInfo = {url, file_index: index === undefined ? -1:index};
    let thisRequest = {url, file_index: index === undefined ? -1:index};
    console.log('PlayRecordCache getRecord, enter, thisRequest=', thisRequest);
    if (this.r && (((new Date()).getTime() - this.insertTime) < 10 * 1000) && this.isSameFile(this.r, this.getRecordInfo)) {
      console.log('PlayRecordCache getRecord, use cache', ',r=',this.r,',getRecordInfo=',this.getRecordInfo);
      return this.r;
    }

    try {
      console.log('PlayRecordCache getRecord, enter',(new Date()).getTime(),',url=',url,',index=',index);
      let ret = await LinkHubHelper.getInstance().getPlaybackRecords({
        recKey: {
          fileInfo: {
            url,
            fileIndex: index
          }
        }
      });
      console.log('PlayRecordCache getRecord ',(new Date()).getTime(),',ret=', ret);

      if (!this.isSameFile(this.getRecordInfo, thisRequest)) {
        console.log('PlayRecordCache getRecord, mayba pre request,cur=', this.getRecordInfo, ',thisRequest=', thisRequest);
        return null;
      }

      if (ret.error.result === 0 && this.isSameFile(ret.records[0], this.getRecordInfo)) {
        this.r = ret.records[0];
        this.insertTime = (new Date()).getTime();
      } else {
        console.log('PlayRecordCache getRecord, reuslt not 0 or this.isSameFile(ret.records[0], this.getRecordInfo) not true,cur=', this.getRecordInfo);
        this.r = null;
      }
    } catch(e) {
      this.r = null;
    }

    return this.r;
  }

  public async addRecord(url: string, index: number, name: string) {
    this.r = {} as any;
    this.r!.url = url,
    this.r!.file_index = index,
    this.r!.name = name;
    this.insertTime = (new Date()).getTime();
    console.log('PlayRecordCache addRecord,r=', this.r);
    
    try {
      LinkHubHelper.getInstance().savePlaybackRecord({
        info: {
          url: this.r!.url,
          url_hash: '',
          file_index: this.r!.file_index,
          resource_type: '',
          resource_id: '',
          name: this.r!.name,
          update_time: Date.now().toString(),
          gcid: '',
          current_progress_time: 0,
          video_total_time: 0,
          device_type: 0,
          status: 0,
        }
      });
    } catch(e) {

    }
  }

  public updatePlayInfo(pos: number, duration: number): void {
    if (this.r) {
      this.r.current_progress_time = pos / 1000;
      this.r.video_total_time = duration / 1000;

      try {
        console.log('PlayRecordCache updatePlayInfo r=', this.r)
        LinkHubHelper.getInstance().savePlaybackRecord({
          info: {
            url: this.r.url,
            url_hash: '',
            file_index: this.r.file_index,
            resource_type: this.r.resource_type ?? '',
            resource_id: this.r.resource_id ?? '',
            name: this.r.name,
            update_time: Date.now().toString(),
            gcid: this.r.gcid ?? '',
            current_progress_time: Math.max(0, Math.floor(this.r.current_progress_time)),
            video_total_time: Math.max(0, Math.floor(this.r.video_total_time)),
            device_type: this.r.device_type ?? 0,
            status: this.r.status ?? 0,
          }
        });
      } catch(e) {

      }
    }
  }

  async test() {
     let a = await PlaybackRecordHelper.getInstance().getPlaybackRecordDetail({url: this.r!.url, file_index: this.r!.file_index});
     console.log('PlayRecordCache getPlaybackRecordDetail ', a)
  }
  public getPlayInfo(): {pos: number, duration: number} {
    if (this.r) {
      return {pos: this.r.current_progress_time * 1000, duration: this.r.video_total_time * 1000};
    }

    return {pos: 0, duration: 0};
  }

  private isSameFile(left: {url: string, file_index: number}, right: {url: string, file_index: number}) {
    if (!left || !right) {
      return false;
    }

    if (left.url.slice(0, 6).toLowerCase() === 'magnet') {
      return (left.url === right.url) && (left.file_index === right.file_index);
    }

    return left.url === right.url;
  }
}

function parseDynamicUrlPath(urlPath: string): string {
  let args: string = '';
  do {
    if (urlPath === undefined || urlPath === null) {
      break;
    }
    // logger.information('parseDynamicUrlPath');
    if (urlPath.match(/[\/]?([^?]*)\?([^\s]*)/)) {
      args = RegExp.$2;
    } else {
      args = '';
    }
    // logger.information('parseDynamicUrlPath ret');
  } while (0);

  return args;
}
function parseDynamicUrlArgs(args: string): { [key: string]: any } {
  let obj: { [key: string]: any } = {};
  do {
    if (args === undefined || args === null) {
      break;
    }

    // /([^&=?\s]+)=+([^&\s]*)/g
    // logger.information('parseDynamicUrlArgs');
    let regEx: RegExp = /([^&=?]+)=([^&]*)/g;
    while (regEx.exec(args)) {
      obj[RegExp.$1] = RegExp.$2;
    }
    // logger.information('parseDynamicUrlArgs ret ', obj);
  } while (0);
  return obj;
}
function getUrlArgs(urlPath: string): { [key: string]: any } {
  let args: string = parseDynamicUrlPath(urlPath);
  let argsObj: any = parseDynamicUrlArgs(args);
  return argsObj;
}

async function InitPlayer() {
  let url: string = window.location.href;
  let argObj = getUrlArgs(url);
  console.log('logpath=', GetLogsPath(), ',argobj=', argObj);
  let parentWnd = Number(argObj['ph'])
  let floatWnd = Number(argObj['ch'])

  let playercontrol = requireNodeFile(
    path.join(GetXxxNodePath(), GetPlayerControlAddonNodeName()),
  )
  let thunderHelper = requireNodeFile(
    path.join(GetXxxNodePath(), 'thunder_helper.node')
  )
  let playerInitParam: any = {
    xmp: false,
    openPlayerLog: true,
    logDir: GetLogsPath(),
    codecPath: '',
    subtitleCachePath: path.join(GetProfilesPath(), 'subtitle'),
    peerid: thunderHelper.getPeerId(),
    version: '',
    versionCode: 10,
    configPath: path.join(GetProfilesPath(), 'player_config.json'),
    //dbDir: GetProfilesPath(),
    //dbName: 'player_record.db',
    panPlayCache: path.join(GetProfilesPath(), 'yun_play'),
    downloadServerDir: GetSdkPath()
  }

  playercontrol.initAddon(playerInitParam);
  AplayerServer.GetInstance().init();
  let playerWnd = await AplayerStack.GetInstance().createPlayerWnd(floatWnd, parentWnd);
  ipcRenderer.send('AplayerWndBind', playerWnd);
  AplayerStack.GetInstance().setMediaOpenHook(async (attr: any): Promise<void> => {
    ipcRenderer.send('AplayerWndShow');
  });
  AplayerStack.GetInstance().addCloseWndHook(() => {
    ipcRenderer.send('AplayerWndHide');
  });
  AplayerStack.GetInstance().attachQuitPlayEvent(() => {
    ipcRenderer.send('AplayerWndHide');
  });

  let updateUserInfo = async () => {
    let info = await AccountHelper.getInstance().getUserInfo();
    console.log('wsw===========info',info)
    playercontrol.updateUserInfo(info);
  };
  AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, () => {
    updateUserInfo();
  });
  AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_OUT, () => {
    updateUserInfo();
  });
  AccountHelper.getInstance().attachEvent(AccountHelperEventKey.USER_INFO_CHANGE, () => {
    updateUserInfo();
  });
  updateUserInfo();

  playercontrol.setGlobalHttpRequest(
    async (
      url: string,
      method: string,
      filePath: string,
      body: string,
      headers: XlRequestHeaders,
      cb: (res: string) => void,
    ) => {
      if (filePath && filePath.length > 0) {
        try {
          if (!headers) {
            headers = {}
          }
          // headers['user-agent'] = newUserAgent;
          let config: AxiosRequestConfig = {
            method,
            url,
            timeout: 10000,
            //headers,
          };
          if (filePath && filePath.length > 0) {
            config['responseType'] = 'arraybuffer';
          }
          const res: AxiosResponse = await axios.request(config)
          //console.log('request url=', url, ',res=', res)
          if (res.status === 200) {
            await FileSystemAWNS.writeFileAW(filePath, Buffer.from(res.data));
          }
          cb('')
        } catch (error) {
          console.error(
            'rcp Request error:',
            error ? JSON.stringify(error) : 'empty error!',
          )
          cb('')
        }
      } else {
        try {
          if (!headers) {
            headers = {}
          }
          //headers['user-agent'] = newUserAgent;
          let config: AxiosRequestConfig = {
            method,
            url,
            timeout: 10000,
            //headers,
          };
          const res: AxiosResponse = await axios.request(config)
          console.log('request url=', url, ',data=', res.data)
          if (res.status === 200) {
            let str = res.data
            if (typeof res.data === 'object') {
              str = JSON.stringify(res.data)
            }
            cb(str);
          }
        } catch (error) {
          console.error(
            'rcp Request error:',
            error ? JSON.stringify(error) : 'empty error!',
          )
          cb('');
        }
      }
    },
  )

  playercontrol.registerPanApi([
    {
      GetFileInfo: async (
        fileId: string,
        params: any,
        cb: (res: any) => void,
      ) => {
        let headers: any = {}
        if (params && params.space === 'SPACE_SAFE') {
          let info = await ThunderPanClientSDK.getInstance().getSafeBoxToken();
          if (info.success) {
            headers['space-authorization'] = info.data;
          }
        }
        let info = await ThunderPanClientSDK.getInstance().getFileInfo(fileId, {
          params,
          headers,
        })
        try {
          console.log(
            'PanBusiness',
            'GetFileInfo, res=',
            JSON.stringify(info ?? {}),
            ', params=',
            JSON.stringify(params ?? {}),
            ', fileId=',
            fileId,
          )
          cb(info)
        } catch (e) {
          console.log('GetFileInfo error', e)
        }
      },
    },
    {
      'GetSubtitleFilesInSameDirectory': async (fileId: string, space: string, filters: any, cb: ((res: any) => void)) => {
        let info: any = await ThunderPanClientSDK.getInstance().getAllFilesInDirectory({ fileId, space, filters });
        //console.log('GetSubtitleFilesInSameDirectory=', info);
        cb(info);
      }
    },
    {
      'SearchFileInSameDirectory': async (fileId: string, space: string,
        cb: ((res: any) => void)) => {
        let info: any = await ThunderPanClientSDK.getInstance().getSameFilesInDirectory({ fileId, space });
        //console.log("SearchFileInSameDirectory============fileId=", fileId, ",space=", space, ",info=", info);
        if (info && info.success && info.data) {
          cb(info.data);
        } else {
          cb([]);
        }
      }
    },
    {
      'ReportParam': async (data: any) => {
        ThunderPanClientSDK.getInstance().reportEvent( {
          params: data
        });
      }
    },
    {
      'GetParam': async (space: string, filtersParam: any,
        cb: ((res: any) => void)) => {
        let filters: string = '';
        if (filtersParam) {
          filters = JSON.stringify(filtersParam);
        }
        let params: any = {
          filters,
          space,
        };
        let res = await ThunderPanClientSDK.getInstance().getEvents( {
          params,
          headers: {
            readTimeout: 3000,
            connectTimeout: 3000,
          }
        });
        if (res.success && res.data && res.data['events']) {
          cb(res.data['events']);
        } else {
          cb([]);
        }
      }
    },
  ])
  playercontrol.addStatCallback((eventKey: string, value: string) => {
    // client.callRemoteClientFunction(mainRendererContext, MainToRenderer_Stat_Channel.playerStat, eventKey, value)
  });

  let g_recordCache: PlayRecordCache = new PlayRecordCache();
  playercontrol.registerUniversalApi([
    {
      'GetUrlPlayIdcInfo': async (url: string, index: number, cb: (succ: boolean, panInfo: { panFileId: string, panSpace: string, locaFilePath: string, fileName: string }) => void) => {
        let locaFilePath = await DownloadFileRecord.GetInstance().query(url, index);
        if (locaFilePath && locaFilePath.length > 0 && await FileSystemAWNS.existsAW(locaFilePath)) {
          cb(true, { panFileId: '', panSpace: '', locaFilePath, fileName: path.basename(locaFilePath) })
        } else {
          let r = await LinkHubHelper.getInstance().getAssociateCloudFile({url, fileIndex: index});
          console.log('wsw=================GetUrlPlayIdcInfo',r, ',index=', index, ',url=',url)
          if (r && (r as any).drive_file_info && (r as any).drive_file_info.file_id) {
            cb(true, { panFileId: (r as any).drive_file_info.file_id, panSpace: (r as any).drive_file_info.space, locaFilePath: '', fileName: '' });
          } else {
            cb(false, { panFileId: '', panSpace: '', locaFilePath: '', fileName: '' });
          }
        }
      }
    }, 
    {
      'GetMagnetRecentPlayIndex': async (url: string, index: number, cb: (succ: boolean, index: number) =>void) => {
        //TODO 服务器暂时不支持，
        cb(false, -1);
        // let r: ThunderClientAPI.dataStruct.dataModals.PlaybackRecord | null = await g_recordCache.getRecord(url, index);
        // if (r) {
        //   cb(true, r.file_index);
        // } else {
        //   cb(false, -1);
        // }
      }
    }
  ]);

  interface FindMediaIdInfo {
    mediaType: MediaType,
    name: string,
    gcid: string,
    task?: {
      taskId: number,
      fileIndex: number,
    }
    universal?: {
      url: string,
      fileIndex: number,
    }
  }
  
  playercontrol.registerMediaStorageOnlineApi([
    {
      'FindMediaId': async (info: FindMediaIdInfo, cb: (succ: boolean, id: number) => void) => {
        let url = ''
        let fileIndex: number = -1;
        if (info.mediaType === MediaType.MtDownload) {
          let task = await TaskManager.GetInstance().findTaskById(info.task!.taskId);
          url = await task!.getUrl();
          fileIndex = info.task!.fileIndex;
        } else {
          url = info.universal!.url;
          fileIndex = info.universal!.fileIndex;
        }

        let r: ThunderClientAPI.dataStruct.dataModals.PlaybackRecord | null = await g_recordCache.getRecord(url, fileIndex);
        if (r) {
          cb(true, 1);
        } else {
          cb(false, 0);
        }
      }
    },
    {
      'AddMedia': async (info: FindMediaIdInfo, cb: (succ: boolean, id: number) => void) => {
        let url = ''
        let fileIndex: number = -1;
        if (info.mediaType === MediaType.MtDownload) {
          let task = await TaskManager.GetInstance().findTaskById(info.task!.taskId);
          url = await task!.getUrl();
          fileIndex = info.task!.fileIndex;
        } else {
          url = info.universal!.url;
          fileIndex = info.universal!.fileIndex;
        }

        g_recordCache.addRecord(url, fileIndex, info.name);
        cb(true, 1);
      }
    }, 
    {
      'GetPlayInfo': async (mediaId: number, cb: (succ: boolean, info: any) =>void) => {
        cb(true, g_recordCache.getPlayInfo());
      }
    },
    {
      'UpdatePlayInfo': async (info: { mediaId: number, duration: number, pos: number }, cb: (succ: boolean, info: any) => void) => {
        g_recordCache.updatePlayInfo(info.pos, info.duration);

        cb(true, '');
      }
    }
  ]);
}
InitPlayer();