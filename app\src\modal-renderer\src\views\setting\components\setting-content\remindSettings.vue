<script setup lang="ts">
import { onMounted, ref } from 'vue';
import CheckboxGroup, { ICheckoutGroupOptions } from '@root/common/components/ui/checkbox-group/index.vue';
import { setSettingConfig, REMIND_SETTING_NAME_MAP, getSettingConfig } from '@root/modal-renderer/src/views/setting';

// 右下角弹窗提醒
const selectedRemindName = ref<string[]>([])
const remindOptions = ref<ICheckoutGroupOptions[]>([{
  label: '下载完成时提醒我',
  name: REMIND_SETTING_NAME_MAP.Finish,
  onChange: (checked: boolean, optionName: string) => {
    setSettingConfig(optionName, checked)
  },
  defaultValue: true
}, {
  label: '下载失败时提醒我',
  name: REMIND_SETTING_NAME_MAP.FailSuggest,
  onChange: (checked: boolean, optionName: string) => {
    setSettingConfig(optionName, checked)
  },
  defaultValue: true
}, {
  label: '设置静默下载时，创建任务后提醒我',
  name: REMIND_SETTING_NAME_MAP.OneKeyDownload,
  onChange: (checked: boolean, optionName: string) => {
    setSettingConfig(optionName, checked)
  },
  defaultValue: true
}, {
  label: '云添加或上传完成时提醒我',
  name: REMIND_SETTING_NAME_MAP.AddNetDiskMessageSuccess,
  onChange: (checked: boolean, optionName: string) => {
    setSettingConfig(optionName, checked)
  },
  defaultValue: true
}, {
  label: '云添加或上传失败时提醒我',
  name: REMIND_SETTING_NAME_MAP.AddNetDiskMessageFail,
  onChange: (checked: boolean, optionName: string) => {
    setSettingConfig(optionName, checked)
  },
  defaultValue: true
}, {
  label: '解压完成时提醒我',
  name: REMIND_SETTING_NAME_MAP.UnzipTipSuccess,
  onChange: (checked: boolean, optionName: string) => {
    setSettingConfig(optionName, checked)
  },
  defaultValue: true
}, {
  label: '解压失败时提醒我',
  name: REMIND_SETTING_NAME_MAP.UnzipTipFail,
  onChange: (checked: boolean, optionName: string) => {
    setSettingConfig(optionName, checked)
  },
  defaultValue: true
}, {
  label: '允许消息通知',
  name: REMIND_SETTING_NAME_MAP.NotifyAdvertisement,
  onChange: (checked: boolean, optionName: string) => {
    setSettingConfig(optionName, checked)
  },
  defaultValue: true
}])

// 声音提醒
const selectedSoundName = ref<string[]>([])
const selectSoundOptions = ref<ICheckoutGroupOptions[]>([{
  label: '下载完成后播放提示音',
  name: REMIND_SETTING_NAME_MAP.PlayWaveWhileFinish,
  onChange: (checked: boolean, optionName: string) => {
    setSettingConfig(optionName, checked)
  },
  defaultValue: true
}])

const isPlaySoundAfterDownloadComplete = ref(false)

const initDefaultValue = async () => {
  isPlaySoundAfterDownloadComplete.value = await getSettingConfig(REMIND_SETTING_NAME_MAP.Finish, false) as boolean

  selectedRemindName.value = await Promise.all(remindOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))

  selectedSoundName.value = await Promise.all(selectSoundOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
}

onMounted(() => {
  initDefaultValue()
})



</script>

<template>
  <CheckboxGroup title="右下角弹窗" :options="remindOptions" v-model="selectedRemindName" orientation="vertical" />
  <div class="settings-content-divider"></div>
  <CheckboxGroup title="声音提醒" :options="selectSoundOptions" v-model="selectedSoundName" orientation="vertical" />
  <div class="settings-content-divider"></div>

</template>

<style lang="scss">
.remind-settings-checkbox-label {
  color: var(--font-font-2, #4E5769);
}
</style>