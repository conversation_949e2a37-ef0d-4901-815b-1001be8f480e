import * as path from 'path';
import { GetXxxNodePath } from '@root/common/xxx-node-path';
import requireNodeFile from '@root/common/require-node-file';
import { EventEmitter } from 'events';
import { TaskManager } from '@root/common/task/impl/task-manager';
import * as TaskBaseType from '@root/common/task/base'
import { readonly, ref } from 'vue';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client'
import { mainRendererContext } from '@xunlei/node-net-ipc/dist/ipc-base';

const thunderHelper: any = requireNodeFile(path.join(GetXxxNodePath(), 'thunder_helper.node'));


export enum ScheduleTaskType {
  UnKnown = -1,
  ShutDown,
  Sleep,
  Exit,
  StartAll,
  StopAll
}

export enum ScheduleTaskEventType {
  Unknow = 'Unknow',
  ScheduleTaskCountChange = 'ScheduleTaskCountChange'
  ScheduleTaskEmit = 'ScheduleTaskEmit'
}

export interface IScheduleTask {
  hours: number;
  minutes: number;
  seconds: number;
  optType: ScheduleTaskType;
  id: number; // 内部用
}

export namespace ScheduleTaskNS  {
  let downloadCompleteTaskType: ScheduleTaskType = ScheduleTaskType.UnKnown;
  let timeFreeDownloadIntervalId: number = -1;
  let taskStatusCookie: number = 0;

  
  let maxScheduleTaskId: number = 1;
  let operations: IScheduleTask[] = [];
  let delayTaskTimerId: number = -1;
  const eventEmitter: EventEmitter = new EventEmitter();

  const IDLE_THRESHOLD_SECONDS = 300; // 5 minutes
  const IDLE_CHECK_INTERVAL_MS = 2000; // 2 seconds
  let isIdleDownloading = false;
  const isIdleDownloadFeatureOn = ref(false);

  export function getEventEmitter(): EventEmitter {
    return eventEmitter;
  }

  export function setDownloadCompleteTaskType(taskType: ScheduleTaskType): boolean {
    if (
        taskType !== ScheduleTaskType.ShutDown &&
        taskType !== ScheduleTaskType.Sleep &&
        taskType !== ScheduleTaskType.Exit
      ) {
        return false;
      }
      if (downloadCompleteTaskType === taskType) {
        return false;
      }

      // 取消之前的下载完成后任务
      cancelDownloadCompleteTask();
      downloadCompleteTaskType = taskType;
      startDownloadCompleteMonitor();

      return true;
  }

  export function getDownloadCompleteTaskType(): ScheduleTaskType {
    return downloadCompleteTaskType;
  }

  export function startDownloadCompleteMonitor(): void {
    taskStatusCookie = TaskManager.GetInstance().attachTaskStatusChangeEvent((taskId: number, eOld: TaskBaseType.TaskStatus, eNew: TaskBaseType.TaskStatus) => {
      if (eNew === TaskBaseType.TaskStatus.Failed || eNew === TaskBaseType.TaskStatus.Succeeded) {
        if (TaskManager.GetInstance().getDownloadQueueCount() === 0) {
          executePlayTask(downloadCompleteTaskType);
          cancelDownloadCompleteTask();
        }  
      }
    });
    return;
  }

  export function stopDownloadCompleteMonitor(): void {
    if (taskStatusCookie === 0) {
      return;
    }
    TaskManager.GetInstance().detachTaskStatusChangeEvent(taskStatusCookie);
    taskStatusCookie = 0;
  }

  export function cancelDownloadCompleteTask(): void {
    stopDownloadCompleteMonitor();
    downloadCompleteTaskType = ScheduleTaskType.UnKnown;
  }

  function executePlayTask(optType: ScheduleTaskType) {
    if (optType === ScheduleTaskType.Exit) {
      clientModule.callRemoteClientFunction(mainRendererContext, 'QuitThunder');
    } else if (optType === ScheduleTaskType.ShutDown) {
      thunderHelper.shutDownMachine();
    } else if (optType === ScheduleTaskType.Sleep) {
      thunderHelper.suspendMachine();
    } else if (optType === ScheduleTaskType.StartAll) {
      startAllTask();
    } else if (optType === ScheduleTaskType.StopAll) {
      pauseAllTask();
    }
  }

  export function addPlanTask(planTask: IScheduleTask): number {
    planTask.id = maxScheduleTaskId;
    maxScheduleTaskId++;
    operations.push(planTask);
    if (delayTaskTimerId === -1) {
      delayTaskTimerId = setInterval(() => {
        let beforeCount = operations.length;
        let executeId: Set<number> = new Set();
        for (let i = 0; i < operations.length; i++) {
          if (operations[i].seconds > 0) {
            operations[i].seconds -= 1;
          } else if (operations[i].minutes > 0) {
            operations[i].minutes -= 1;
            operations[i].seconds = 59;
          } else if (operations[i].hours > 0) {
            operations[i].hours -= 1;
            operations[i].minutes = 59;
            operations[i].seconds = 59;
          } 

          if (operations[i].hours === 0 && operations[i].minutes === 0 && operations[i].seconds === 0) {
            executeId.add(operations[i].id);
          }
        }
        for (let id of executeId) {
          for (let i = 0; i < operations.length; i++) {
            if (operations[i].id === id) {
              executePlayTask(operations[i].optType);
              operations.splice(i, 1);
              eventEmitter.emit(ScheduleTaskEventType.ScheduleTaskEmit, operations[i]);    
              break;
            }
          }
        }
        if (operations.length === 0 && delayTaskTimerId !== -1) {
          clearInterval(delayTaskTimerId);
          delayTaskTimerId = -1;
        }
        if (operations.length !== beforeCount) {
          eventEmitter.emit(ScheduleTaskEventType.ScheduleTaskCountChange, operations.length);
        }
      }, 1000) as any;
    }
    eventEmitter.emit(ScheduleTaskEventType.ScheduleTaskCountChange, operations.length);
    return planTask.id;
  }

  export function deletePlanTask(id: number) {
    for (let i = 0; i < operations.length; i++) {
      if (operations[i].id === id) {
        operations.splice(i, 1);
        eventEmitter.emit(ScheduleTaskEventType.ScheduleTaskCountChange, operations.length);
        break;
      }
    }
    if (operations.length === 0 && delayTaskTimerId !== -1) {
      clearInterval(delayTaskTimerId);
      delayTaskTimerId = -1;
    }
  } 

  export function getPlanTasks(): IScheduleTask[] {
    return operations;
  }

  export function getScheduleTaskCount(): number {
    return operations.length;
  }

  /** 空闲下载功能是否开启 */
  export function getIsIdleDownloadFeatureEnabled() {
    return readonly(isIdleDownloadFeatureOn);
  }

  /**
   * 开启空闲下载
   * 当系统空闲时间超过阈值时，自动开始所有下载任务。
   * 当系统恢复活动时，自动暂停所有下载任务。
   */
  export function startFreeDownLoad(): void {
    if (timeFreeDownloadIntervalId !== -1) {
      return; // 空闲下载已开启
    }
    isIdleDownloadFeatureOn.value = true;

    const checkIdleAndToggleDownload = () => {
      const freeTime = thunderHelper.getFreeTime();
      if (isIdleDownloading) {
        // 当前正在空闲下载，检查是否需要暂停
        if (freeTime < IDLE_THRESHOLD_SECONDS) {
          isIdleDownloading = false;
          pauseAllTask();
        }
      } else {
        // 当前未进行空闲下载，检查是否需要开始
        if (freeTime >= IDLE_THRESHOLD_SECONDS) {
          isIdleDownloading = true;
          startAllTask();
        }
      }
    };

    checkIdleAndToggleDownload(); // 立即执行一次检查
    timeFreeDownloadIntervalId = setInterval(checkIdleAndToggleDownload, IDLE_CHECK_INTERVAL_MS) as any;
  }

  // 关闭空闲下载
  export function closeFreeDownLoad(): void {
    if (timeFreeDownloadIntervalId !== -1) {
      clearInterval(timeFreeDownloadIntervalId);
      timeFreeDownloadIntervalId = -1;
    }
    isIdleDownloadFeatureOn.value = false;
    // 如果是通过空闲下载启动的任务，则暂停它们
    if (isIdleDownloading) {
      isIdleDownloading = false;
      pauseAllTask();
    }
  }

  async function startAllTask() {
    let category = await TaskManager.GetInstance().GetCategoryManager().getCategoryById(TaskBaseType.DefaultCategoryId);
    if (category) {
      let view = category.getCategoryViewFromId(TaskBaseType.CategoryViewID.Downloading);
      if (view) {
        view.startAllTask();
      }
    }
    category = await TaskManager.GetInstance().GetCategoryManager().getCurrentPanCategory();
    if (category) {
      let view = category.getCategoryViewFromId(TaskBaseType.CategoryViewID.Downloading);
      if (view) {
        view.startAllTask();
      }
    }
  }

  async function pauseAllTask() {
    let category = await TaskManager.GetInstance().GetCategoryManager().getCategoryById(TaskBaseType.DefaultCategoryId);
    if (category) {
      let view = category.getCategoryViewFromId(TaskBaseType.CategoryViewID.Downloading);
      if (view) {
        view.stopAllTask();
      }
    }
    category = await TaskManager.GetInstance().GetCategoryManager().getCurrentPanCategory();
    if (category) {
      let view = category.getCategoryViewFromId(TaskBaseType.CategoryViewID.Downloading);
      if (view) {
        view.stopAllTask();
      }
    }
  }
}
