#include "./const_string_id.h"

#include "../../config/config.h"

void const_string_id::Clear()
{
    m_constidmap.clear();
}

bool const_string_id::AddConfigFile(const wchar_t *lpPath)
{
    bool ret = false;
    xl::config::config config;
    if (config.load(lpPath))
    {
        int count = config.get_item_count(0);
        for (int i = 0; i < count; i++)
        {
            std::wstring key, value;
            config.get_item(0, i, key, value);
            int id = _wtoi(value.c_str());
            if (id != 0)
            {
                ret |= AddID(key.c_str(), id);
            }
        }
    }
    return ret;
    
}

bool const_string_id::AddID(const wchar_t *string, const int id)
{
    if (!IsStringExist(string))
    {
        m_constidmap.insert(std::make_pair(string, id));
        return true;
    }
    return false;
}

bool const_string_id::GetIDFromString(const wchar_t* string, int& id)
{
    id = 0;
    StringMapIterator it = m_constidmap.find(string);
    if (it != m_constidmap.end())
    {
        id = it->second;
        return true;
    }
    return false;
}

bool const_string_id::IsStringExist(const wchar_t* string)
{
    return (m_constidmap.find(string) != m_constidmap.end());
}