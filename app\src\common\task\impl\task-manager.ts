import * as BaseType from '../base'
import {DkHelper} from './dk-helper'
import {Task} from './task'
import {CategoryManager} from './category-manager'
import * as path from 'path'
import requireNodeFile from '@root/common/require-node-file';
import {GetXxxNodePath, GetDkAddonNodeName} from '@root/common/xxx-node-path'

const dk: any = requireNodeFile(path.join(GetXxxNodePath(), GetDkAddonNodeName()));

export class TaskManager {
  private static instance: TaskManager;

  private nativeTaskManager: any;
  //
  private categoryManager: CategoryManager | null = null;
  // ���񻺴�
  private taskCache: Map<number, Task> = new Map();
  // �Ƿ��Ѿ�load���
  private isLoaded: boolean = false;
  private tpPeerId: string = '';

  constructor() {
    this.nativeTaskManager = new dk.NativeTaskManager();
  }

  public static GetInstance(): TaskManager {
    if (!TaskManager.instance) {
      if (global.TaskManagerImplInstance) {
        TaskManager.instance =  global.TaskManagerImplInstance;
      } else {
        TaskManager.instance = new TaskManager();
        global.TaskManagerImplInstance = TaskManager.instance;
      }
    }
    return TaskManager.instance;
  }

  public GetCategoryManager(): CategoryManager {
    if (this.categoryManager) {
      return this.categoryManager;
    }
    this.categoryManager = new CategoryManager(this.nativeTaskManager.getCategoryManager());
    return this.categoryManager;
  }

  // 创建任务，详细见NewTaskSet
  public async createTask(info: BaseType.NewTaskSet): Promise<Task | undefined> {
    await this.waitLoadFinish();
    let taskId: number = 0;
    if (info.taskInfo.taskType === BaseType.TaskType.P2sp) {
      taskId = this.nativeTaskManager.createP2spTask(info.taskInfo, info.p2spInfo!);
    } else if (info.taskInfo.taskType === BaseType.TaskType.Bt) {
      taskId = this.nativeTaskManager.createBtTask(info.taskInfo, info.btInfo!);
    } else if (info.taskInfo.taskType === BaseType.TaskType.Group) {
      taskId = this.nativeTaskManager.createGroupTask(info.taskInfo, info.groupInfo!);
    } else if (info.taskInfo.taskType === BaseType.TaskType.Emule) {
      taskId = this.nativeTaskManager.createEmuleTask(info.taskInfo, info.emuleInfo!);
    } else if (info.taskInfo.taskType === BaseType.TaskType.Magnet) {
      taskId = this.nativeTaskManager.createMagnetTask(info.taskInfo, info.magnetInfo!);
    }
    return this.findTaskImpl(taskId);
  }

  public async checkRepeatAndCreateTask(info: BaseType.NewTaskSet): Promise<{task: Task|undefined, exist: boolean}> {
    await this.waitLoadFinish();
    let repeatInfo = this.findRepeatTaskImpl([info]);
    if (repeatInfo && repeatInfo.length > 0) {
      return { task: this.findTaskImpl(repeatInfo[0].taskId), exist: true };
    } else {
      let taskId: number = 0;
      if (info.taskInfo.taskType === BaseType.TaskType.P2sp) {
        taskId = this.nativeTaskManager.createP2spTask(info.taskInfo, info.p2spInfo!);
      } else if (info.taskInfo.taskType === BaseType.TaskType.Bt) {
        taskId = this.nativeTaskManager.createBtTask(info.taskInfo, info.btInfo!);
      } else if (info.taskInfo.taskType === BaseType.TaskType.Group) {
        taskId = this.nativeTaskManager.createGroupTask(info.taskInfo, info.groupInfo!);
      } else if (info.taskInfo.taskType === BaseType.TaskType.Emule) {
        taskId = this.nativeTaskManager.createEmuleTask(info.taskInfo, info.emuleInfo!);
      } else if (info.taskInfo.taskType === BaseType.TaskType.Magnet) {
        taskId = this.nativeTaskManager.createMagnetTask(info.taskInfo, info.magnetInfo!);
      }
      return { task: this.findTaskImpl(taskId), exist: false };
    }
  }

  public async createP2spTask(taskInfo: BaseType.NewTaskInfo, p2spInfo: BaseType.NewP2spTaskInfo): Promise<Task | undefined> {
    await this.waitLoadFinish();
    let taskId = this.nativeTaskManager.createP2spTask(taskInfo, p2spInfo);
    return this.findTaskImpl(taskId);
  }

  // ����������
  public async  createGroupTask(taskInfo: BaseType.NewTaskInfo, groupInfoList: BaseType.NewGroupTaskInfo[]): Promise<Task | undefined> {
    await this.waitLoadFinish();
    let taskId = this.nativeTaskManager.createGroupTask(taskInfo, groupInfoList);
    return this.findTaskImpl(taskId);
  }

  public async  createEmuleTask(taskInfo: BaseType.NewTaskInfo, emuleTaskInfo: BaseType.NewEmuleTaskInfo): Promise<Task | undefined> {
    await this.waitLoadFinish();
    let taskId = this.nativeTaskManager.createEmuleTask(taskInfo, emuleTaskInfo);
    return this.findTaskImpl(taskId);
  }

  public async createMagnetTask(taskInfo: BaseType.NewTaskInfo, magnetTaskInfo: BaseType.NewMagnetTaskInfo): Promise<Task | undefined> {
    await this.waitLoadFinish();
    let taskId = this.nativeTaskManager.createMagnetTask(taskInfo, magnetTaskInfo);
    return this.findTaskImpl(taskId);
  }

  public async createBtTask(taskInfo: BaseType.NewTaskInfo, btTaskInfo: BaseType.NewBtTaskInfo): Promise<Task | undefined> {
    await this.waitLoadFinish();
    let taskId = this.nativeTaskManager.createBtTask(taskInfo, btTaskInfo);
    return this.findTaskImpl(taskId);
  }

  // 查找重复任务，逻辑需要按2025的修改
  public async findRepeatTask(infos: BaseType.NewTaskSet[]): Promise<BaseType.FindRepeatTaskResultItem[]> {
    if (infos.length === 0) {
      return [];
    }

    await this.waitLoadFinish();
    return this.findRepeatTaskImpl(infos);
  }

  // 查找任务
  public async findTaskById(taskId: number): Promise<Task | undefined> {
    await this.waitLoadFinish();
    return this.findTaskImpl(taskId);
  }

  public async searchTask(key: string, isSearchPanTask: boolean): Promise<number[]> {
    if (!key || key.length === 0) {
      return [];
    }

    await this.waitLoadFinish();

    let inCategoryIds: number[] = [];
    inCategoryIds.push(-1);
    if (isSearchPanTask) {
      let category = await GetCategoryManager().getCurrentPanCategory();
      if (category) {
        inCategoryIds.push(category.getId());
      }
    }

    return await new Promise((v) => {
      this.nativeTaskManager.searchTask(key, inCategoryIds, (ids: number[]) => {
        v(ids);
      });
    });
  }

  public  getDownloadQueueCount(): number {
    return this.nativeTaskManager.getDownloadQueueCount();
  }

  // 批量暂停任务
  public batchStartTasks(taskIds: number[]) {
    this.nativeTaskManager.batchStartTasks(taskIds);
  }

  // 批量暂停任务
  public batchStopTasks(taskIds: number[], r: BaseType.TaskStopReason) {
    this.nativeTaskManager.batchStopTasks(taskIds, r);
  }

  // 批量删除任务
  public batchDeleteTasks(taskIds: number[], deleteFile: boolean): void {
    this.nativeTaskManager.batchDeleteTasks(taskIds, deleteFile);
  }

  // 批量删除任务到回收站
  public batchRecycleTasks(taskIds: number[]): void {
    this.nativeTaskManager.batchRecycleTasks(taskIds);
  }

  // 任务详情变更
  public attachTaskDetailChangeEvent(cb: (taskId: number, flags: BaseType.TaskDetailChangedFlags) => void): number {
    return this.nativeTaskManager.attachTaskDetailChangeEvent(cb);
  }

  public detachTaskDetailChangeEvent(cookie: number): void {
    this.nativeTaskManager.detachTaskDeletedEvent(cookie);
  }

  // 任务状态变更
  public attachTaskStatusChangeEvent(cb: (taskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => void): number {
    return this.nativeTaskManager.attachTaskStatusChangeEvent(cb);
  }

  public detachTaskStatusChangeEvent(cookie: number): void {
    this.nativeTaskManager.detachTaskStatusChangeEvent(cookie);
  }

  // 任务组子任务详情变化
  public attachGroupSubTaskDetailChangeEvent(cb: (taskId: number, groupTaskId: number,
    flags: BaseType.TaskDetailChangedFlags) => void): number {
    return this.nativeTaskManager.attachGroupSubTaskDetailChangeEvent(cb);
  }

  public detachGroupSubTaskDetailChangeEvent(cookie: number): void {
    this.nativeTaskManager.detachGroupSubTaskDetailChangeEvent(cookie);
  }

  // 任务组子任务状态变化
  public attachGroupSubTaskStatusChangeEvent(cb: (taskId: number, groupTaskId: number, eOld: BaseType.TaskStatus,
    eNew: BaseType.TaskStatus) => void): number {
    return this.nativeTaskManager.attachGroupSubTaskStatusChangeEvent(cb);
  }

  public detachGroupSubTaskStatusChangeEvent(cookie: number): void {
    this.nativeTaskManager.detachGroupSubTaskStatusChangeEvent(cookie);
  }

  // ���񳹵�ɾ��
  public attachTaskDeletedEvent(cb: (taskId: number, groupTaskId: number) => void): number {
    return this.nativeTaskManager.attachTaskDeletedEvent(cb);
  }

  public detachTaskDeletedEvent(cookie: number) {
    this.nativeTaskManager.detachTaskDeletedEvent(cookie);
  }

  // bt子文件变更
  public attachBtSubTaskDetailChangeEvent(cb: (taskId: number, fileIndex: number, flags: BaseType.BtSubFileDetailChangeFlags) => void): number {
    return this.nativeTaskManager.attachBtSubTaskDetailChangeEvent(cb);
  }

  public detachBtSubTaskDetailChangeEvent(cookie: number) {
    this.nativeTaskManager.detachBtSubTaskDetailChangeEvent(cookie);
  }

  // bt������״̬�仯
  public attachBtSubTaskStatusChangeEvent(cb: (taskId: number, fileIndex: number, eOld: BaseType.BtSubFileStatus, eNew: BaseType.BtSubFileStatus) => void): number {
    return this.nativeTaskManager.attachBtSubTaskStatusChangeEvent(cb);
  }

  public detachBtSubTaskStatusChangeEvent(cookie: number) {
    this.nativeTaskManager.detachBtSubTaskStatusChangeEvent(cookie);
  }

  public attachGlobaSpeedChangeEvent(cb: (downloadSpeed: number, uploadSpeed: number) => void) {
    return this.nativeTaskManager.attachGlobaSpeedChangeEvent(cb);
  }

  public detachGlobaSpeedChangeEvent(cookie: number) {
    this.nativeTaskManager.detachGlobaSpeedChangeEvent(cookie);
  }

  public attachAutoMoveTaskToTailEvent(cb: (taskId: number) => void) {
    return this.nativeTaskManager.attachAutoMoveTaskToTailEvent(cb);
  }

  public detachAutoMoveTaskToTailEvent(cookie: number) {
    this.nativeTaskManager.detachAutoMoveTaskToTailEvent(cookie);
  }

  public setAutoMoveLowSpeedTaskSwitch(open: boolean) {
    this.nativeTaskManager.setAutoMoveLowSpeedTaskSwitch(open);
  }

  public setAutoAddTaskSpeedLimit(speed: number) {
    this.nativeTaskManager.setAutoAddTaskSpeedLimit(speed);
  }

  public setNoWaitTaskSizeThreshold(size: number) {
    this.nativeTaskManager.setNoWaitTaskSizeThreshold(size);
  }

  public async waitLoadFinish(): Promise<void> {
    if (this.isLoaded) {
      return;
    }
    await new Promise((v: (p: boolean) => void) => {
      this.nativeTaskManager.waitLoadFinish(() => {
        this.isLoaded = true;
        v(true);
      });
    });
  }

  public updateMaxDownloadTaskCount(n: number): void {
    this.nativeTaskManager.updateMaxDownloadTaskCount(n);
  }

  public setGlobalExtInfo(info: string, append: boolean): void {
    this.nativeTaskManager.setGlobalExtInfo(info, append);
  }

  public setBeforePanTaskCreateDkTaskCallback(cb: (taskId: number, panFileId: string, done: () => void) => void): void {
    this.nativeTaskManager.setBeforePanTaskCreateDkTaskCallback(cb);
  }

  public async getTpPeerId(): Promise<string> {
    if (this.tpPeerId.length > 0) {
      return this.tpPeerId;
    }
    await this.waitLoadFinish();
    await new Promise((v: (p: boolean) => void) => {
      this.nativeTaskManager.getTpPeerId((id: string) => {
        this.tpPeerId = id;
        v(true);
      });
    });
    return this.tpPeerId;
  }

  public async setUserInfo(userId: string, jumpKey: string) {
    await this.waitLoadFinish();
    this.nativeTaskManager.setUserInfo(userId, jumpKey);
  }

  public async startDownloadAndPlay(taskId: number) {
    await this.waitLoadFinish();
    this.nativeTaskManager.startDownloadAndPlay(taskId);
  } 

  public async stopDownloadAndPlay() {
    await this.waitLoadFinish();
    this.nativeTaskManager.stopDownloadAndPlay();
  }

  public async setProxy(host: string,
          port: number,
          userName: string,
          passWord: string,
          proxyType: BaseType.ProxyType) {
    await this.waitLoadFinish();
    this.nativeTaskManager.setProxy(host, port, userName, passWord, proxyType);
  }

  private findTaskImpl(taskId: number): Task | undefined {
    if (taskId <= 0) {
      return undefined;
    }
    if (this.taskCache.has(taskId)) {
      return this.taskCache.get(taskId);
    }
    let realTask = this.nativeTaskManager.findTaskById(taskId);
    if (realTask) {
      let task = new Task(taskId, realTask);
      this.taskCache.set(taskId, task);
      return task;
    }
    return undefined;
  }

  private findRepeatTaskImpl(infos: BaseType.NewTaskSet[]): BaseType.FindRepeatTaskResultItem[] {
    let keys: string[] = [];
    infos.forEach((info) => {
      if (info.taskInfo.taskType === BaseType.TaskType.P2sp) {
        if (info.taskInfo.panFileId && info.taskInfo.panUserId) {
          keys.push(info.taskInfo.panUserId + '-' + info.taskInfo.panFileId);
        } else {
          keys.push(info.p2spInfo!.url);
        }
      } else if (info.taskInfo.taskType === BaseType.TaskType.Bt) {
        keys.push('BT-' + info.btInfo!.infoId);
      } else if (info.taskInfo.taskType === BaseType.TaskType.Group) {
        keys.push('');
      } else if (info.taskInfo.taskType === BaseType.TaskType.Emule) {
        let ed2kResult = DkHelper.parserEd2kLink(info.emuleInfo!.url);
        keys.push('Emule-' + ed2kResult.fileHash);
      } else if (info.taskInfo.taskType === BaseType.TaskType.Magnet) {
        keys.push(info.magnetInfo!.url);
      }
    });
    return this.nativeTaskManager.findRepeatTask(keys);
  }
}

export function GetTaskManager(): TaskManager {
  return TaskManager.GetInstance();
}

export function GetCategoryManager(): CategoryManager {
  return GetTaskManager().GetCategoryManager();
}