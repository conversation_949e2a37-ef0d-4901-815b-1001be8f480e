import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { AplayerStack } from '@root/common/player/client/aplayer-stack'


import { PopUpNS } from '@root/common/pop-up'
import { initialVueApp } from '@root/common/vue-app-initial'

import App from './App.vue'

const boxId: string = new URL(location.href)?.searchParams?.get('boxId')!
window.__popupWindowId__ = Number(boxId)
const clientIpcName = 'modal-renderer' + boxId
client.start({ name: clientIpcName })

const pinia = createPinia()
const app = createApp(App)
initialVueApp(app)
app.use(pinia)
app.mount('#modal-renderer-root')


AplayerStack.GetInstance().initApiProxy();
// 初始化当前窗口
PopUpNS.constructorCurrentWindow(Number(boxId))
PopUpNS.enableDevTools();

// import * as BaseType from '@root/common/task/base';
// // // 注意这里都是从client里面导入的
// import { TaskManager } from '@root/common/task/client/task-manager';
// import { DkHelper } from '@root/common/task/client/dk-helper';
// import { Task } from '@root/common/task/client/task';
// // 必须要初始化
// TaskManager.GetInstance().init();

// // demo
// // 注意这里的事件和main-renderer里面有一点区别，这里是task
// TaskManager.GetInstance().attachTaskStatusChangeEvent(async (task: Task, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
//     console.log('task status change，id=', task.getId(), ',old=', eOld, ',new=', eNew);
// });
// setTimeout(async () => {
//     let taskSavePath: string = 'D:\\work\\xl\\thunder_2025\\bin\\download';
//     let magnetTask = await TaskManager.GetInstance().createTask({
//         taskInfo: {
//             background: true, // 磁力换种子任务一般不显示在下载列表
//             taskType: BaseType.TaskType.Magnet,
//             taskBaseInfo: {
//                 savePath: taskSavePath,
//                 taskName: 'test.torrent'
//             }
//         },
//         magnetInfo: {
//             url: 'magnet:?xt=urn:btih:6594021d90991d9f527dfc6ae2b1d9b4092ef72e&dn=%e9%98%b3%e5%85%89%e7%94%b5%e5%bd%b1dygod.org.%e4%b8%8d%e8%af%b4%e8%af%9d%e7%9a%84%e7%88%b1.2025.HD.1080P.%e5%9b%bd%e8%af%ad%e4%b8%ad%e8%8b%b1%e5%8f%8c%e5%ad%97.mkv&tr=udp%3a%2f%2ftracker.opentrackr.org%3a1337%2fannounce&tr=udp%3a%2f%2fexodus.desync.com%3a6969%2fannounce',
//             torrentFilePath: taskSavePath
//         }
//     });
//     magnetTask!.start();

//     //等待种子下载完成后，解析bt种子【例子里面可以先保证种子存在】
//     let info = await DkHelper.parseBtTaskInfo(path.join(taskSavePath, 'test.torrent'));
//     console.log('bt info=', info);
//     let btTask = await TaskManager.GetInstance().createTask({
//         taskInfo: {
//             background: false,
//             taskType: BaseType.TaskType.Bt,
//             taskBaseInfo: {
//                 savePath: taskSavePath,
//                 taskName: info.title
//             }
//         },
//         btInfo: {
//             origin: 'magnet:?xt=urn:btih:6594021d90991d9f527dfc6ae2b1d9b4092ef72e&dn=%e9%98%b3%e5%85%89%e7%94%b5%e5%bd%b1dygod.org.%e4%b8%8d%e8%af%b4%e8%af%9d%e7%9a%84%e7%88%b1.2025.HD.1080P.%e5%9b%bd%e8%af%ad%e4%b8%ad%e8%8b%b1%e5%8f%8c%e5%ad%97.mkv&tr=udp%3a%2f%2ftracker.opentrackr.org%3a1337%2fannounce&tr=udp%3a%2f%2fexodus.desync.com%3a6969%2fannounce',
//             seedFile: path.join(taskSavePath, 'test.torrent'),
//             displayName: info.title,
//             fileRealIndexLists: [0],//选中的需要下载的bt文件的index
//             fileLists: info.fileLists,
//             infoId: info.infoId,
//             tracker: '',
//         }
//     });
//     btTask!.start();
// }, 5000);
