<template>
  <div class="dialog-examples">
    <h2>Dialog 组件使用示例</h2>

    <!-- 示例16: 编程式API调用 -->
    <div class="example-section">
      <h3>16. 编程式API调用</h3>
      <div style="display: flex; gap: 12px; flex-wrap: wrap">
        <button
          class="api-btn info"
          @click="showInfoDialog"
        >
          📘 Info API
        </button>
        <button
          class="api-btn success"
          @click="showSuccessDialog"
        >
          ✅ Success API
        </button>
        <button
          class="api-btn warning"
          @click="showWarningDialog"
        >
          ⚠️ Warning API
        </button>
        <button
          class="api-btn error"
          @click="showErrorDialog"
        >
          ❌ Error API
        </button>
        <button
          class="api-btn confirm"
          @click="showConfirmDialog"
        >
          ❓ Confirm API
        </button>
        <button
          class="api-btn thunder"
          @click="showCallbackDemo"
        >
          🔄 回调处理演示
        </button>
        <button
          class="api-btn rename"
          @click="showRenameDialog"
        >
          📝 重命名对话框
        </button>
        <button
          class="api-btn error"
          @click="showAdvancedPromptDialog"
        >
          🚀 高级Prompt方案
        </button>
        <button
          class="api-btn warning"
          @click="showRealtimeValidationDialog"
        >
          ⚡ 实时验证演示
        </button>
        <button
          class="api-btn info"
          @click="showFixedHeightDialog"
        >
          📐 固定高度模式
        </button>
        <button
          class="api-btn success"
          @click="showCustomHeightDialog"
        >
          📏 自定义输入框高度
        </button>
      </div>

      <!-- 回调演示的结果显示区域 -->
      <div
        class="callback-result"
        v-if="callbackResult"
        :class="callbackResult.type"
      >
        <div class="result-header">
          <span class="result-icon">{{ callbackResult.icon }}</span>
          <span class="result-title">{{ callbackResult.title }}</span>
          <button
            class="clear-btn"
            @click="clearCallbackResult"
          >
            ✕
          </button>
        </div>
        <div class="result-content">{{ callbackResult.message }}</div>
        <div class="result-timestamp">{{ callbackResult.timestamp }}</div>
      </div>
    </div>

    <!-- 示例18: 重命名对话框高级用法 -->
    <div class="example-section">
      <h3>18. 重命名对话框高级用法</h3>
      <div style="display: flex; gap: 12px; flex-wrap: wrap">
        <button
          class="api-btn rename"
          @click="showAdvancedRenameDialog"
        >
          📝 高级重命名对话框
        </button>
        <button
          class="api-btn warning"
          @click="showValidationRenameDialog"
        >
          ✅ 带验证的重命名
        </button>
      </div>
      <p style="margin-top: 12px; color: #666; font-size: 14px">
        演示带输入框、实时验证和自动聚焦的重命名对话框
      </p>
    </div>

    <!-- 示例19: Content传递方案对比 -->
    <div class="example-section">
      <h3>19. Content传递方案对比</h3>
      <div style="display: flex; gap: 12px; flex-wrap: wrap">
        <button
          class="api-btn info"
          @click="showHtmlStringDialog"
        >
          📄 HTML字符串方案（已修复）
        </button>
        <button
          class="api-btn warning"
          @click="showPromptDialog"
        >
          ⌨️ Prompt方案
        </button>
      </div>
      <p style="margin-top: 12px; color: #666; font-size: 14px">
        对比不同的content传递方式：HTML字符串(v-html) vs 专用方法
      </p>
    </div>

    <!-- 示例1: 默认带trigger的对话框 -->
    <div class="example-section">
      <h3>1. 默认带trigger的对话框</h3>
      <Dialog
        v-model:open="dialog1Open"
        title="确认操作"
        content="您确定要执行此操作吗？"
        :show-trigger="true"
        trigger-text="点击打开对话框"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
    </div>

    <!-- 示例2: 自定义trigger内容 -->
    <div class="example-section">
      <h3>2. 自定义trigger内容</h3>
      <Dialog
        v-model:open="dialog2Open"
        title="删除确认"
        content="此操作不可撤销，确定要删除吗？"
        variant="error"
        :show-trigger="true"
        @confirm="handleDelete"
        @cancel="handleCancel"
      >
        <template #trigger-content>
          <span style="display: flex; align-items: center; gap: 4px">🗑️ 删除项目</span>
        </template>
      </Dialog>
    </div>

    <!-- 示例3: 完全自定义trigger -->
    <div class="example-section">
      <h3>3. 完全自定义trigger</h3>
      <Dialog
        v-model:open="dialog3Open"
        title="自定义触发器"
        content="这是一个完全自定义trigger的示例"
        :show-trigger="true"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      >
        <template #trigger>
          <button
            class="custom-trigger-btn"
            @click="dialog3Open = true"
          >
            🎨 自定义样式按钮
          </button>
        </template>
      </Dialog>
    </div>

    <!-- 示例4: 不显示trigger，通过编程方式控制 -->
    <div class="example-section">
      <h3>4. 不显示trigger，通过编程方式控制</h3>
      <button
        class="manual-trigger"
        @click="dialog4Open = true"
      >
        手动打开对话框
      </button>
      <Dialog
        v-model:open="dialog4Open"
        title="编程控制"
        content="这个对话框通过外部按钮控制开关"
        :show-trigger="false"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
    </div>

    <!-- 示例5: 不同变体的trigger -->
    <div class="example-section">
      <h3>5. 不同变体的对话框</h3>
      <div style="display: flex; gap: 12px; flex-wrap: wrap">
        <Dialog
          v-model:open="dialogInfo"
          title="信息提示"
          content="这是一个信息提示"
          variant="info"
          trigger-text="信息"
          :show-trigger="true"
        />
        <Dialog
          v-model:open="dialogSuccess"
          title="成功提示"
          content="操作成功完成"
          variant="success"
          trigger-text="成功"
          :show-trigger="true"
        />
        <Dialog
          v-model:open="dialogWarning"
          title="警告提示"
          content="请注意这个警告"
          variant="warning"
          trigger-text="警告"
          :show-trigger="true"
        />
        <Dialog
          v-model:open="dialogError"
          title="错误提示"
          content="发生了一个错误"
          variant="error"
          trigger-text="错误"
          :show-trigger="true"
        />
        <Dialog
          v-model:open="dialogThunder"
          title="Thunder提示"
          content="这是Thunder主题的对话框"
          variant="thunder"
          trigger-text="Thunder"
          :show-trigger="true"
        />
      </div>
    </div>

    <!-- 示例6: 控制标题图标显示 -->
    <div class="example-section">
      <h3>6. 控制标题图标显示</h3>
      <div style="display: flex; gap: 12px; flex-wrap: wrap">
        <Dialog
          v-model:open="dialogWithIcon"
          title="带图标的标题"
          content="这个对话框显示标题图标"
          variant="success"
          trigger-text="显示图标"
          :show-trigger="true"
          :show-title-icon="true"
        />
        <Dialog
          v-model:open="dialogWithoutIcon"
          title="不带图标的标题"
          content="这个对话框不显示标题图标"
          variant="success"
          trigger-text="隐藏图标"
          :show-trigger="true"
          :show-title-icon="false"
        />
      </div>
    </div>

    <!-- 示例7: 自定义标题图标 -->
    <div class="example-section">
      <h3>7. 自定义标题图标</h3>
      <Dialog
        v-model:open="dialogCustomIcon"
        title="自定义图标"
        content="这个对话框使用自定义的标题图标"
        variant="thunder"
        trigger-text="自定义图标"
        :show-trigger="true"
        :show-title-icon="true"
      >
        <template #title-icon>
          <span style="font-size: 20px">🚀</span>
        </template>
      </Dialog>
    </div>

    <!-- 示例8: 加载状态 -->
    <div class="example-section">
      <h3>8. 加载状态</h3>
      <Dialog
        v-model:open="dialogLoading"
        title="提交数据"
        content="点击确认后将显示加载状态"
        variant="info"
        trigger-text="测试加载状态"
        :show-trigger="true"
        :loading="loadingDialog"
        @confirm="handleLoadingConfirm"
        @cancel="handleCancel"
      />
    </div>

    <!-- 示例9: 阻止默认关闭行为 -->
    <div class="example-section">
      <h3>9. 阻止默认关闭行为</h3>
      <Dialog
        v-model:open="dialogPreventClose"
        title="自定义关闭逻辑"
        content="这个对话框需要自定义关闭逻辑，点击确认或取消不会自动关闭"
        variant="warning"
        trigger-text="自定义关闭"
        :show-trigger="true"
        :prevent-default-close="true"
        @confirm="handlePreventCloseConfirm"
        @cancel="handlePreventCloseCancel"
      />
    </div>

    <!-- 示例10: 自定义内容插槽 -->
    <div class="example-section">
      <h3>10. 自定义内容插槽</h3>
      <Dialog
        v-model:open="dialogCustomContent"
        title="自定义内容"
        variant="info"
        trigger-text="自定义内容"
        :show-trigger="true"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      >
        <div class="custom-content">
          <p>这是自定义的内容区域</p>
          <ul>
            <li>支持HTML内容</li>
            <li>可以包含任意组件</li>
            <li>完全自定义样式</li>
          </ul>
          <div class="highlight-box">
            <strong>重要提示：</strong>
            这是一个高亮显示的信息框
          </div>
        </div>
      </Dialog>
    </div>

    <!-- 示例11: 自定义操作按钮 -->
    <div class="example-section">
      <h3>11. 自定义操作按钮</h3>
      <Dialog
        v-model:open="dialogCustomActions"
        title="自定义操作"
        content="这个对话框有自定义的操作按钮"
        variant="thunder"
        trigger-text="自定义操作"
        :show-trigger="true"
      >
        <template #actions>
          <div class="custom-actions">
            <button
              class="btn-secondary"
              @click="handleCustomAction('save')"
            >
              💾 保存草稿
            </button>
            <button
              class="btn-danger"
              @click="handleCustomAction('delete')"
            >
              🗑️ 删除
            </button>
            <button
              class="btn-primary"
              @click="handleCustomAction('publish')"
            >
              🚀 发布
            </button>
          </div>
        </template>
      </Dialog>
    </div>

    <!-- 示例12: 左侧操作区域 -->
    <div class="example-section">
      <h3>12. 左侧操作区域</h3>
      <Dialog
        v-model:open="dialogLeftAction"
        title="左侧操作"
        content="这个对话框在左侧有额外的操作按钮"
        variant="info"
        trigger-text="左侧操作"
        :show-trigger="true"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      >
        <template #left-action>
          <button
            class="btn-help"
            @click="showHelp"
          >
            ❓ 帮助
          </button>
        </template>
      </Dialog>
    </div>

    <!-- 示例13: 自定义确认按钮内容 -->
    <div class="example-section">
      <h3>13. 自定义确认按钮内容</h3>
      <Dialog
        v-model:open="dialogCustomConfirm"
        title="自定义确认按钮"
        content="确认按钮有自定义的内容和图标"
        variant="success"
        trigger-text="自定义确认按钮"
        :show-trigger="true"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      >
        <template #confirm-content>
          <span style="display: flex; align-items: center; gap: 4px">✨ 立即执行</span>
        </template>
      </Dialog>
    </div>

    <!-- 示例14: 隐藏取消按钮 -->
    <div class="example-section">
      <h3>14. 隐藏取消按钮</h3>
      <Dialog
        v-model:open="dialogNoCancel"
        title="只有确认按钮"
        content="这个对话框只显示确认按钮"
        variant="info"
        trigger-text="只有确认"
        :show-trigger="true"
        :show-cancel="false"
        @confirm="handleConfirm"
      />
    </div>

    <!-- 示例15: 隐藏关闭按钮 -->
    <div class="example-section">
      <h3>15. 隐藏右上角关闭按钮</h3>
      <Dialog
        v-model:open="dialogNoClose"
        title="无关闭按钮"
        content="这个对话框没有右上角的关闭按钮"
        variant="warning"
        trigger-text="无关闭按钮"
        :show-trigger="true"
        :show-close-button="false"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
    </div>

    <!-- 示例17: 自定义类名前缀 -->
    <div class="example-section">
      <h3>17. 自定义类名前缀</h3>
      <Dialog
        v-model:open="dialogCustomClass"
        title="自定义样式"
        content="这个对话框使用了自定义的类名前缀"
        variant="thunder"
        trigger-text="自定义样式"
        :show-trigger="true"
        class-prefix="my-custom"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Dialog from './Dialog.vue'
import { useAlertDialog } from './useAlertDialog.js'
import { usePromptDialog } from './usePromptDialog.js'

// 获取编程式API
const alertDialog = useAlertDialog()
const promptDialog = usePromptDialog()

// 对话框状态
const dialog1Open = ref(false)
const dialog2Open = ref(false)
const dialog3Open = ref(false)
const dialog4Open = ref(false)
const dialogInfo = ref(false)
const dialogSuccess = ref(false)
const dialogWarning = ref(false)
const dialogError = ref(false)
const dialogThunder = ref(false)
const dialogWithIcon = ref(false)
const dialogWithoutIcon = ref(false)
const dialogCustomIcon = ref(false)
const dialogLoading = ref(false)
const loadingDialog = ref(false)
const dialogPreventClose = ref(false)
const dialogCustomContent = ref(false)
const dialogCustomActions = ref(false)
const dialogLeftAction = ref(false)
const dialogCustomConfirm = ref(false)
const dialogNoCancel = ref(false)
const dialogNoClose = ref(false)
const dialogCustomClass = ref(false)

// 回调演示结果状态
const callbackResult = ref(null)

// 事件处理
const handleConfirm = () => {
  console.log('确认操作')
}

const handleCancel = () => {
  console.log('取消操作')
}

const handleDelete = () => {
  console.log('删除操作')
}

// 加载状态处理
const handleLoadingConfirm = () => {
  loadingDialog.value = true
  // 模拟异步操作
  setTimeout(() => {
    loadingDialog.value = false
    dialogLoading.value = false
    console.log('操作完成')
  }, 2000)
}

// 阻止默认关闭的处理
const handlePreventCloseConfirm = () => {
  console.log('自定义确认逻辑')
  // 这里可以添加自定义验证逻辑
  if (confirm('确定要关闭对话框吗？')) {
    dialogPreventClose.value = false
  }
}

const handlePreventCloseCancel = () => {
  console.log('自定义取消逻辑')
  // 这里可以添加自定义验证逻辑
  if (confirm('确定要取消吗？')) {
    dialogPreventClose.value = false
  }
}

// 自定义操作处理
const handleCustomAction = action => {
  console.log(`执行操作: ${action}`)
  dialogCustomActions.value = false
}

// 帮助按钮处理
const showHelp = () => {
  alert('这是帮助信息')
}

// 回调演示方法
const showCallbackDemo = async () => {
  console.log('🔄 开始回调演示...')

  try {
    const result = await alertDialog.confirm({
      title: '🔄 回调处理演示',
      content: `这是一个回调处理演示对话框。

• 点击"确认"按钮会返回 true
• 点击"取消"按钮会返回 false
• 点击右上角关闭按钮也会返回 false
• 按 ESC 键也会返回 false

点击后会在下方显示具体的回调结果和时间戳。`,
      variant: 'info',
      showCancel: true,
      confirmText: '确认',
      cancelText: '取消',
    })

    console.log('✅ Callback demo result:', result, typeof result)

    if (result === true) {
      console.log('✅ 用户点击了确认按钮')
      callbackResult.value = {
        type: 'success',
        icon: '✅',
        title: '用户确认操作',
        message: `alertDialog.confirm() 返回值: ${result} (${typeof result})`,
        timestamp: new Date().toLocaleString(),
      }
    } else if (result === false) {
      console.log('❌ 用户点击了取消或关闭')
      callbackResult.value = {
        type: 'cancel',
        icon: '❌',
        title: '用户取消操作',
        message: `alertDialog.confirm() 返回值: ${result} (${typeof result})`,
        timestamp: new Date().toLocaleString(),
      }
    } else {
      console.log('⚠️ 未预期的返回值:', result)
      callbackResult.value = {
        type: 'error',
        icon: '⚠️',
        title: '未预期的返回值',
        message: `alertDialog.confirm() 返回值: ${result} (${typeof result})`,
        timestamp: new Date().toLocaleString(),
      }
    }
  } catch (error) {
    console.error('❌ Callback demo error:', error)
    callbackResult.value = {
      type: 'error',
      icon: '⚠️',
      title: '操作异常',
      message: `发生错误: ${error.message}`,
      timestamp: new Date().toLocaleString(),
    }
  }
}

// 清除回调结果
const clearCallbackResult = () => {
  callbackResult.value = null
}

// 编程式API调用示例
const showInfoDialog = async () => {
  const result = await alertDialog.info({
    title: '信息提示',
    content: '这是通过编程式API调用的信息对话框',
  })
  console.log('Info dialog result:', result)
}

const showSuccessDialog = async () => {
  const result = await alertDialog.success({
    title: '成功提示',
    content: '操作成功完成！',
  })
  console.log('Success dialog result:', result)
}

const showWarningDialog = async () => {
  const result = await alertDialog.warning({
    title: '警告提示',
    content: '请注意这个重要警告！',
  })
  console.log('Warning dialog result:', result)
}

const showErrorDialog = async () => {
  const result = await alertDialog.error({
    title: '错误提示',
    content: '发生了一个错误，请重试！',
  })
  console.log('Error dialog result:', result)
}

const showConfirmDialog = async () => {
  const result = await alertDialog.confirm({
    title: '确认操作',
    content: '您确定要执行此操作吗？这个操作不可撤销。',
    variant: 'warning',
  })
  console.log('Confirm dialog result:', result)
  if (result) {
    alert('用户确认了操作')
  } else {
    alert('用户取消了操作')
  }
}

const showRenameDialog = async () => {
  // 创建一个包含输入框的HTML内容
  const inputHTML = `
    <div style="margin: 24px 0 16px 0;">
      <input 
        id="rename-input" 
        type="text" 
        placeholder="" 
        style="
          width: 100%;
          padding: 16px;
          border: 2px solid #4a90e2;
          border-radius: 8px;
          font-size: 16px;
          box-sizing: border-box;
          outline: none;
          transition: all 0.2s ease;
          background-color: #f8f9fa;
        "
        onFocus="this.style.borderColor='#4a90e2'; this.style.backgroundColor='#ffffff';"
        onBlur="this.style.borderColor='#4a90e2'; this.style.backgroundColor='#f8f9fa';"
        value="文件名示例"
      />
    </div>
  `

  const result = await alertDialog.open({
    title: '重命名',
    content: inputHTML,
    allowHtml: true,
    variant: 'thunder',
    showCancel: true,
    confirmText: '确定',
    cancelText: '取消',
    showTitleIcon: false,
  })

  // 在对话框打开后，自动聚焦并选中输入框内容
  setTimeout(() => {
    const inputElement = document.getElementById('rename-input')
    if (inputElement) {
      inputElement.focus()
      inputElement.select()
    }
  }, 100)

  if (result) {
    // 获取输入框的值
    const inputElement = document.getElementById('rename-input')
    const newName = inputElement ? inputElement.value : ''
    console.log('用户确认重命名，新名称:', newName)
    if (newName.trim()) {
      alert(`重命名成功！新名称: ${newName}`)
    } else {
      alert('请输入有效的名称')
    }
  } else {
    console.log('用户取消重命名')
  }
}

// 高级重命名对话框
const showAdvancedRenameDialog = async () => {
  const currentName = '我的重要文档.txt'

  const inputHTML = `
    <div style="margin: 20px 0;">
      <div style="margin-bottom: 12px; color: #666; font-size: 14px;">
        当前名称: <strong>${currentName}</strong>
      </div>
      <input 
        id="advanced-rename-input" 
        type="text" 
        style="
          width: 100%;
          padding: 14px;
          border: 2px solid #4a90e2;
          border-radius: 8px;
          font-size: 16px;
          box-sizing: border-box;
          outline: none;
          transition: all 0.3s ease;
          background-color: #f8f9fa;
          box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        "
        onFocus="this.style.borderColor='#007bff'; this.style.backgroundColor='#ffffff'; this.style.boxShadow='0 0 0 3px rgba(0,123,255,0.1)';"
        onBlur="this.style.borderColor='#4a90e2'; this.style.backgroundColor='#f8f9fa'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.05)';"
        value="${currentName}"
      />
      <div style="margin-top: 8px; color: #666; font-size: 12px;">
        💡 提示: 输入新的文件名，支持中文、英文和数字
      </div>
    </div>
  `

  const result = await alertDialog.open({
    title: '重命名文件',
    content: inputHTML,
    allowHtml: true,
    variant: 'thunder',
    showCancel: true,
    confirmText: '确定',
    cancelText: '取消',
    showTitleIcon: false,
  })

  setTimeout(() => {
    const inputElement = document.getElementById('advanced-rename-input')
    if (inputElement) {
      inputElement.focus()
      // 选中文件名部分（不包括扩展名）
      const lastDotIndex = inputElement.value.lastIndexOf('.')
      if (lastDotIndex > 0) {
        inputElement.setSelectionRange(0, lastDotIndex)
      } else {
        inputElement.select()
      }
    }
  }, 150)

  if (result) {
    const inputElement = document.getElementById('advanced-rename-input')
    const newName = inputElement ? inputElement.value.trim() : ''
    if (newName && newName !== currentName) {
      console.log(`文件重命名: "${currentName}" -> "${newName}"`)
      alert(`✅ 重命名成功！\n旧名称: ${currentName}\n新名称: ${newName}`)
    } else if (!newName) {
      alert('❌ 文件名不能为空')
    } else {
      alert('ℹ️ 文件名未改变')
    }
  }
}

// 带验证的重命名对话框
const showValidationRenameDialog = async () => {
  const currentName = '项目文档'

  const result = await alertDialog.open({
    title: '重命名（带验证）',
    content: `
      <div style="margin: 20px 0;">
        <input 
          id="validation-rename-input" 
          type="text" 
          style="
            width: 100%;
            padding: 14px;
            border: 2px solid #4a90e2;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            outline: none;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
          "
          value="${currentName}"
        />
        <div id="validation-message" style="margin-top: 8px; font-size: 12px; min-height: 16px; color: #28a745;">
          ✓ 文件名有效
        </div>
        <div style="margin-top: 12px; padding: 10px; background-color: #f0f8ff; border-radius: 4px; font-size: 12px; color: #666;">
          <strong>命名规则:</strong><br>
          • 不能包含特殊字符: &lt; &gt; : " / \\ | ? *<br>
          • 长度: 1-255 个字符<br>
          • 不能以空格开头或结尾
        </div>
      </div>
    `,
    allowHtml: true,
    variant: 'info',
    showCancel: true,
    confirmText: '确定',
    cancelText: '取消',
    showTitleIcon: false,
  })

  // 添加输入验证逻辑
  setTimeout(() => {
    const inputElement = document.getElementById('validation-rename-input')
    const messageEl = document.getElementById('validation-message')

    if (inputElement && messageEl) {
      inputElement.focus()
      inputElement.select()

      // 添加输入事件监听
      inputElement.addEventListener('input', function () {
        const value = this.value
        const forbiddenChars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
        let isValid = true
        let message = ''

        if (!value.trim()) {
          isValid = false
          message = '❌ 文件名不能为空'
        } else if (value.length > 255) {
          isValid = false
          message = '❌ 文件名过长（最多255个字符）'
        } else if (value.trim() !== value) {
          isValid = false
          message = '❌ 文件名不能以空格开头或结尾'
        } else {
          for (let char of forbiddenChars) {
            if (value.includes(char)) {
              isValid = false
              message = `❌ 文件名不能包含字符: ${char}`
              break
            }
          }
        }

        if (isValid) {
          message = '✓ 文件名有效'
          this.style.borderColor = '#28a745'
          this.style.backgroundColor = '#f8fff9'
          messageEl.style.color = '#28a745'
        } else {
          this.style.borderColor = '#dc3545'
          this.style.backgroundColor = '#fff8f8'
          messageEl.style.color = '#dc3545'
        }

        messageEl.textContent = message
      })
    }
  }, 150)

  if (result) {
    const inputElement = document.getElementById('validation-rename-input')
    const newName = inputElement ? inputElement.value.trim() : ''
    console.log(`验证重命名: "${currentName}" -> "${newName}"`)
    alert(`✅ 验证通过！新名称: ${newName}`)
  }
}

// ========== Content传递方案对比 ==========

// 方案1: HTML字符串方案（已修复）
const showHtmlStringDialog = async () => {
  const result = await alertDialog.open({
    title: 'HTML字符串方案',
    content: `
      <div style="color: red; margin-bottom: 12px;">✅ 这是HTML字符串，现在可以正常渲染了！</div>
      <input type="text" placeholder="这个输入框现在可以显示" style="width: 100%; padding: 8px; margin-bottom: 12px; border: 1px solid #ccc; border-radius: 4px;" />
      <p><strong>说明：</strong> 使用 allowHtml: true 来启用HTML渲染</p>
      <div style="background: #fff3cd; padding: 10px; border-radius: 4px; border-left: 4px solid #ffc107;">
        <strong>⚠️ 安全提示：</strong> 只对可信的HTML内容使用 allowHtml，避免XSS攻击
      </div>
    `,
    allowHtml: true,
    variant: 'info',
  })
  console.log('HTML字符串方案结果:', result)
}

// 方案2: Prompt方案（最简单）
const showPromptDialog = async () => {
  const result = await promptDialog.prompt({
    title: '重命名文件',
    placeholder: '请输入新的文件名',
    defaultValue: '我的文档.txt',
    hint: '💡 使用专用的prompt方法，自动处理输入框逻辑',
  })

  if (result !== false) {
    console.log('Prompt方案结果:', result)
    alert(`新文件名: ${result}`)
  } else {
    console.log('用户取消了输入')
  }
}

// 方案3: 高级Prompt方案（带验证）
const showAdvancedPromptDialog = async () => {
  const result = await promptDialog.prompt({
    title: '重命名（高级验证）',
    placeholder: '请输入新名称',
    defaultValue: '项目文档',
    currentValue: '旧文档名.txt',
    hint: '🔍 支持实时验证，名称不能超出3个字符，会立即显示错误状态',
    validateOnInput: true, // 启用实时验证
    onChange: (value, event) => {
      // 在输入变化时进行自定义验证
      console.log('输入内容变化:', value, '长度:', value.length)

      if (value.length > 3) {
        console.log('❌ 验证失败: 名称不能超出3个字符，当前长度:', value.length)
        // validateOnInput: true 会自动触发 validator 并立即显示红色错误状态
      } else {
        console.log('✅ 验证通过，当前长度:', value.length)
      }
    },
    onConfirm: async value => {
      console.log('确认按钮被点击，输入值:', value)
      console.log('开始异步处理，3秒后关闭对话框...')

      // 模拟异步操作，比如提交到服务器
      await new Promise(resolve => setTimeout(resolve, 3000))

      console.log('异步处理完成，对话框将关闭')
      return true // 返回 true 表示可以关闭对话框
    },
    validator: value => {
      if (!value.trim()) {
        return { valid: false, message: '❌ 文件名不能为空' }
      }

      if (value.length > 3) {
        return { valid: false, message: `❌ 名称不能超出3个字符，当前: ${value.length}个字符` }
      }

      return { valid: true, message: '' }
    },
  })

  if (result !== false) {
    console.log('高级Prompt方案结果:', result)
    alert(`✅ 验证通过！新名称: ${result}`)
  }
}

const showRealtimeValidationDialog = async () => {
  const result = await promptDialog.prompt({
    title: '实时验证演示',
    placeholder: '试试输入超过5个字符...',
    defaultValue: '',
    hint: '⚡ 输入时会立即显示验证结果',
    validateOnInput: true, // 启用实时验证
    onChange: (value, event) => {
      // 在输入变化时的额外处理
      console.log('输入内容变化:', value, '长度:', value.length)

      if (value.length > 5) {
        console.log('❌ 超出长度限制')
      } else if (value.includes(' ')) {
        console.log('❌ 包含空格')
      } else if (value.length > 0) {
        console.log('✅ 输入有效')
      }
    },
    validator: value => {
      if (!value.trim()) {
        return { valid: false, message: '⚠️ 请输入内容' }
      }

      if (value.includes(' ')) {
        return { valid: false, message: '❌ 不能包含空格' }
      }

      if (value.length > 5) {
        return { valid: false, message: `❌ 内容过长！最多5个字符，当前${value.length}个` }
      }

      if (value.length < 2) {
        return { valid: false, message: '⚠️ 至少需要2个字符' }
      }

      return { valid: true, message: '' }
    },
    onConfirm: async value => {
      console.log('确认输入:', value)
      // 模拟验证过程
      await new Promise(resolve => setTimeout(resolve, 1000))
      return true
    },
  })

  if (result !== false) {
    console.log('实时验证演示结果:', result)
    alert(`✅ 输入成功！内容: "${result}"`)
  } else {
    console.log('用户取消了输入')
  }
}

const showFixedHeightDialog = async () => {
  const result = await promptDialog.prompt({
    title: '固定高度模式演示',
    placeholder: '试试输入超过3个字符...',
    defaultValue: '',
    hint: '🔧 使用固定高度模式，错误信息区域始终占位，高度不会变化',
    validateOnInput: true,
    fixedHeight: true, // 启用固定高度模式
    onChange: (value, event) => {
      console.log('固定高度模式 - 输入变化:', value, '长度:', value.length)
    },
    validator: value => {
      if (!value.trim()) {
        return { valid: false, message: '⚠️ 请输入内容' }
      }

      if (value.length > 3) {
        return { valid: false, message: `❌ 超出限制！最多3个字符，当前${value.length}个` }
      }

      return { valid: true, message: '' }
    },
    onConfirm: async value => {
      console.log('固定高度模式确认:', value)
      await new Promise(resolve => setTimeout(resolve, 1000))
      return true
    },
  })

  if (result !== false) {
    console.log('固定高度模式结果:', result)
    alert(`✅ 输入成功！内容: "${result}"`)
  } else {
    console.log('用户取消了输入')
  }
}

const showCustomHeightDialog = async () => {
  const result = await promptDialog.prompt({
    title: '自定义输入框高度演示',
    placeholder: '这是一个100px高的文本区域，可以输入多行文本...',
    defaultValue: '',
    hint: '📏 输入框高度设置为100px，支持多行文本输入。使用 Ctrl+Enter 确认',
    validateOnInput: true,
    fixedHeight: true, // 启用固定高度模式
    inputType: 'textarea', // 使用文本区域
    inputStyle: {
      height: '100px',
      resize: 'vertical', // 允许垂直调整大小
      fontFamily: 'monospace', // 使用等宽字体
      lineHeight: '1.5',
    },
    onChange: (value, event) => {
      console.log(
        '自定义输入框高度 - 输入变化:',
        '字符数:',
        value.length,
        '行数:',
        value.split('\n').length
      )
    },
    validator: value => {
      if (!value.trim()) {
        return { valid: false, message: '⚠️ 请输入内容' }
      }

      const lines = value.split('\n').length
      if (lines > 20) {
        return { valid: false, message: `❌ 行数过多！最多20行，当前${lines}行` }
      }

      if (value.length > 1000) {
        return { valid: false, message: `❌ 内容过长！最多1000字符，当前${value.length}字符` }
      }

      return { valid: true, message: '' }
    },
    onConfirm: async value => {
      console.log('自定义输入框高度确认:', value)
      console.log('总字符数:', value.length, '总行数:', value.split('\n').length)
      await new Promise(resolve => setTimeout(resolve, 1000))
      return true
    },
  })

  if (result !== false) {
    console.log('自定义输入框高度结果:', result)
    const lines = result.split('\n').length
    alert(`✅ 输入成功！\n字符数: ${result.length}\n行数: ${lines}`)
  } else {
    console.log('用户取消了输入')
  }
}
</script>

<style scoped>
.dialog-examples {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.example-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 2px solid #007bff;
  color: #333;
}

.custom-trigger-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 25px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.custom-trigger-btn:hover {
  transform: translateY(-2px);
}

.manual-trigger {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.manual-trigger:hover {
  background-color: #0056b3;
}

.custom-content {
  text-align: left;
}

.custom-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.highlight-box {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  background-color: #fff3cd;
  color: #856404;
}

.custom-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 8px;
}

.btn-secondary {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #6c757d;
  color: white;
  cursor: pointer;
}

.btn-danger {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #dc3545;
  color: white;
  cursor: pointer;
}

.btn-primary {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.btn-help {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background-color: #17a2b8;
  color: white;
  font-size: 12px;
  cursor: pointer;
}

.api-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.api-btn.info {
  background-color: #17a2b8;
  color: white;
}

.api-btn.success {
  background-color: #28a745;
  color: white;
}

.api-btn.warning {
  background-color: #ffc107;
  color: #212529;
}

.api-btn.error {
  background-color: #dc3545;
  color: white;
}

.api-btn.confirm {
  background-color: #6f42c1;
  color: white;
}

.api-btn.rename {
  background-color: #fd7e14;
  color: white;
}

.api-btn.thunder {
  background-color: #007bff;
  color: white;
}

.api-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 回调结果显示区域样式 */
.callback-result {
  margin-top: 20px;
  padding: 16px;
  border-left: 4px solid;
  border-radius: 8px;
  background-color: #f8f9fa;
  animation: slideIn 0.3s ease-out;
}

.callback-result.success {
  border-left-color: #28a745;
  background-color: #d4edda;
}

.callback-result.cancel {
  border-left-color: #6c757d;
  background-color: #e2e3e5;
}

.callback-result.error {
  border-left-color: #dc3545;
  background-color: #f8d7da;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-icon {
  margin-right: 8px;
  font-size: 18px;
}

.result-title {
  flex: 1;
  color: #333;
  font-weight: 600;
}

.clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 4px;
  border: none;
  border-radius: 50%;
  background: none;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

.result-content {
  margin-bottom: 6px;
  padding: 8px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  color: #555;
  font-family: monospace;
}

.result-timestamp {
  color: #888;
  font-size: 12px;
  text-align: right;
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 自定义类名前缀示例样式 */
:deep(.my-custom-content) {
  border: 2px solid #ff6b6b;
}

:deep(.my-custom-title) {
  color: #ff6b6b !important;
}
</style>
