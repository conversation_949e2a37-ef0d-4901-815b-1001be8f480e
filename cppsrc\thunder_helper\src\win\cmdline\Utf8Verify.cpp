#include ".\utf8verify.h"
#include "../text/transcode.h"


using namespace Utf8Verify;

int Utf8Verify::ones_in_byte_header(const char c)
{
	unsigned char temp = c&0xFF;
	int count=0;
	while(temp&0x80)
	{
		count++;
		temp=(unsigned char)((unsigned int)temp)<<1;
	}

	return count;
}

bool Utf8Verify::verify(const char* _data, int _len)
{
	int c;    
	int i = 0;
	int num = 0;
	int header=0;
	while (i < _len)
	{
		c = _data[i] & 0xFF;//get a byte
		if(c==0)
		{
			return true;
		}

		header = ones_in_byte_header((char)c);
		if((header>0)&&(i+header-1>_len))
		{
			return false;
		}

		if(header>0)
		{           
			for(int j=1;j<=header-1;j++)
			{   
				if(i+j>=_len)
				{
					return false;
				}

				if((_data[i+j]&0xC0) !=0x80)
				{
					return false;
				}
			}
		}        

		num++;    
		if(header==0)
		{
			i++;
		}

		i+=header;
		header=0;
	}

	if(i>=_len)
	{
		return true;
	}
	else
	{
		return false;
	}
}

void Utf8Verify::TryTranslateUrl(std::wstring& strUrl)
{
	if (strUrl.empty())
	{
		return;
	}

	char szUrl[4096] = {0};
#pragma warning(push)
#pragma warning(disable: 4996)
	size_t n = wcstombs(szUrl, strUrl.c_str(), strUrl.size());
#pragma warning(pop)
	if (n>0 && n<4096)
	{
		std::wstring strTranslatedUrl;
		szUrl[n] = 0;
		if (Utf8Verify::verify(szUrl, (int)strlen(szUrl)))
		{
			xl::text::transcode::UTF8_to_Unicode(szUrl, (uint32_t)strlen(szUrl), strTranslatedUrl);
			strUrl = strTranslatedUrl;
		}		
	}	
}