
export namespace FileOperationHelper {

  export function getSuffix (filename: string=''): string {
    const matcher: RegExpMatchArray | null = filename.match(/\.([^.]+)$/)
    return matcher ? matcher[1] : ''
  }

  // 格式化大小显示
  export function formatSize (bytes: number): string {
    if (bytes === 0) return '0 B'

    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))

    return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + units[i]
  }
}
