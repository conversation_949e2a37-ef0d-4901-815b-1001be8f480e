#include "./aplayer_impl.h"
#include <xlcommon.h>

#define WM_USER_EVENT_MSG WM_USER + 1
struct AplayerEventData {
	void* pUserData;
	int nEventId;
	int param1;
	int param2;
	std::string strParam;
};

static HWND g_playerWnd = nullptr;
static HWND g_floatChildWnd = nullptr;
static HWND g_floatWnd = nullptr;
static bool g_bLButtonDown = false;
static AplayerImpl* g_pAPlayerImpl = nullptr;
static HCURSOR				g_cursor;
static bool g_bTrackMouse = false;


AplayerImpl::AplayerImpl() {
	g_pAPlayerImpl = this;    
}

LRESULT __stdcall AplayerImpl::PlayerWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
	switch (message)
	{
	case WM_USER_EVENT_MSG:
	{
		AplayerEventData* pData = (AplayerEventData*)lParam;
		((AplayerImpl*)(pData->pUserData))->OnEventMessage((void*)lParam);
		return 0;
	}
	case WM_MOUSEMOVE:
	case WM_LBUTTONDOWN:
	case WM_LBUTTONUP:
	case WM_LBUTTONDBLCLK:
	case WM_RBUTTONDOWN:
	case WM_RBUTTONUP:
	case WM_RBUTTONDBLCLK:
	case WM_MOUSEWHEEL:
	case WM_MOUSEHOVER:
	case WM_MOUSELEAVE:
	case WM_KEYDOWN:
	case WM_KEYUP:
	case WM_CHAR:
	case WM_SYSKEYDOWN:
	case WM_SYSKEYUP:
		if (message == WM_MOUSEMOVE) {
			::SetFocus(hWnd);
			if (!g_bTrackMouse) {
				TRACKMOUSEEVENT tme;
				tme.cbSize = sizeof(tme);
				tme.dwFlags = TME_LEAVE;
				tme.hwndTrack = hWnd;
				TrackMouseEvent(&tme);
				g_bTrackMouse = true;
			}
			SetCursor(g_cursor);
		}
		else if (message == WM_MOUSELEAVE) {
			g_bTrackMouse = false;
		}
		if (g_pAPlayerImpl) {
			g_pAPlayerImpl->OnWndMessage(message, wParam, lParam);
		}
		break;
	}
	return DefWindowProc(hWnd, message, wParam, lParam);
}

ATOM PlayerWndRegisterClass(HINSTANCE hInstance)
{
	WNDCLASSEXW wcex;

	wcex.cbSize = sizeof(WNDCLASSEXW);

	wcex.style = CS_HREDRAW | CS_VREDRAW | CS_DBLCLKS;
	wcex.lpfnWndProc = AplayerImpl::PlayerWndProc;
	wcex.cbClsExtra = 0;
	wcex.cbWndExtra = 0;
	wcex.hInstance = hInstance;
	wcex.hIcon = NULL;
	wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
	wcex.hbrBackground = CreateSolidBrush(RGB(0, 0, 0));
	wcex.lpszMenuName = NULL;
	wcex.lpszClassName = L"player_wnd_class";
	wcex.hIconSm = NULL;
	g_cursor = wcex.hCursor;

	return RegisterClassExW(&wcex);
}


HWND AplayerImpl::CreatePlayerWnd(HWND hParent) {
	auto hInst = GetModuleHandle(NULL);
	size_t argc = 2;
	auto n = PlayerWndRegisterClass(hInst);

	RECT rc = { 0 };
	GetWindowRect(hParent, &rc);

	auto nStyle = GetWindowLongPtr(hParent, GWL_STYLE);
	::SetWindowLongPtr(hParent, GWL_STYLE, nStyle | WS_CLIPCHILDREN);

	HWND hWnd = ::CreateWindow("player_wnd_class", "player_wnd", WS_CHILD,
		0, 0, rc.right - rc.left, rc.bottom - rc.top, hParent, nullptr, hInst, nullptr);
	::SetParent(hWnd, hParent);

	ShowWindow(hWnd, SW_SHOW);
	UpdateWindow(hWnd);

	return hWnd;
}

bool AplayerImpl::CreateAPlayer(const AplayerParam& param, HWND* pWnd) {
	g_floatWnd = param.hFloat;
	g_floatChildWnd = FindWindowEx(param.hFloat, NULL, "Chrome_RenderWidgetHostHWND", NULL);
	m_playerWnd = CreatePlayerWnd(param.hParent);
	XL_SPDLOG_INFO("CreateAPlayer player_wnd={:p}, parentWnd={:p}", (void*)m_playerWnd, (void*)param.hParent);
	*pWnd = m_playerWnd;

	m_pAplayer = aplayer_create();
	aplayer_set_view(m_pAplayer, m_playerWnd);

	if (param.bOpenLog) {
		aplayer_set_config(m_pAplayer, "log.enable", "1");
	}
	aplayer_set_config(m_pAplayer, "log.level", "2");
	if (!param.strCachePath.empty()) {
		aplayer_set_config(m_pAplayer, "network.transmitsavepath", param.strCachePath.c_str());
	}

	aplayer_set_config(m_pAplayer, "network.transmitlibpath", param.strSdkPath.c_str());
	aplayer_set_config(m_pAplayer, "network.transmitappkey", "xzcGMuTmV0RGlza19NOzA^SDK==d7c2e150bde4^bd74d3f24305^e1fa56");

	if (!param.strLogDir.empty()) {
		aplayer_set_config(m_pAplayer, "log.path", param.strLogDir.c_str());
	}

	if (param.bIsAudioNormalize)
	{
		aplayer_set_config(m_pAplayer, "audio.normalize", "1");
	}
	aplayer_set_callback(m_pAplayer, AplayerImpl::AplayerEventCallback, this);

	return true;
}

long AplayerImpl::Open(const char* szUrl)
{
	if (m_pAplayer) {
		auto nRet = aplayer_open(m_pAplayer, szUrl);
		XL_SPDLOG_INFO("aplayer_open, nRet={:d}, url={:s}", nRet, szUrl);
		return nRet;
	}
	XL_SPDLOG_INFO("aplayer_open, failed, player nullptr, url={:s}", szUrl);
	return -1;
}

long AplayerImpl::Close()
{
	if (m_pAplayer) {
		XL_SPDLOG_INFO("aplayer_close");
		aplayer_close(m_pAplayer);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_close, failed, player nullptr");
	return -1;
}

long AplayerImpl::Play()
{
	if (m_pAplayer) {
		XL_SPDLOG_INFO("aplayer_play");
		aplayer_play(m_pAplayer);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_play, failed, player nullptr");
	return -1;
}

long AplayerImpl::Pause()
{
	if (m_pAplayer) {
		XL_SPDLOG_INFO("aplayer_pause");
		aplayer_pause(m_pAplayer);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_pause, failed, player nullptr");
	return -1;
}

long AplayerImpl::GetVersion(std::string& strVersion)
{
	if (m_pAplayer) {
		XL_SPDLOG_INFO("AsyncGetVersion");
		strVersion = aplayer_get_version();
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_get_version, failed, player nullptr");
	return -1;
}

long AplayerImpl::SetCustomLogo(long logo)
{
	return 0;
}

long AplayerImpl::GetState(long* state)
{
	if (m_pAplayer)
	{
		aplayer_state nState = aplayer_get_state(m_pAplayer);
		XL_SPDLOG_INFO("aplayer_get_state, nState={:d}", (int)nState);
		*state = nState;
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_get_state, failed, player nullptr");
	return -1;
}

long AplayerImpl::GetDuration(long* duration)
{
	if (m_pAplayer) {
		*duration = aplayer_get_duration(m_pAplayer);
		XL_SPDLOG_INFO("aplayer_get_duration, nRet={:d}", *duration);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_get_duration, failed, player nullptr");
	return -1;
}

long AplayerImpl::GetPosition(long* position)
{
	if (m_pAplayer) {
		*position = aplayer_get_position(m_pAplayer);
		XL_SPDLOG_INFO("aplayer_get_position, nRet={:d}", *position);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_get_position, failed, player nullptr");
	return -1;
}

long AplayerImpl::SetPosition(long position, long* result)
{
	if (m_pAplayer) {
		auto nRet = aplayer_set_position(m_pAplayer, position);
		XL_SPDLOG_INFO("aplayer_set_position, position={:d}, nRet={:d}", position, nRet);
		*result = nRet;
		return 0;
	}
	*result = -1;
	XL_SPDLOG_INFO("aplayer_set_position, failed, player nullptr");
	return -1;
}

long AplayerImpl::GetVideoWidth(long* video_width)
{
	if (m_pAplayer) {
		*video_width = aplayer_get_video_width(m_pAplayer);
		XL_SPDLOG_INFO("aplayer_get_video_width, video_width={:d}", *video_width);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_get_video_width, failed, player nullptr");
	return -1;
}

long AplayerImpl::GetVideoHeight(long* video_height)
{
	if (m_pAplayer) {
		*video_height = aplayer_get_video_height(m_pAplayer);
		XL_SPDLOG_INFO("aplayer_get_video_height, video_height={:d}", *video_height);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_get_video_height, failed, player nullptr");
	return -1;
}

long AplayerImpl::GetVolume(long* volume)
{
	if (m_pAplayer) {
		*volume = aplayer_get_volume(m_pAplayer);
		XL_SPDLOG_INFO("aplayer_get_volume, value={:d}", *volume);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_get_volume, failed, player nullptr");
	return -1;
}

long AplayerImpl::SetVolume(long volume, long* result)
{
	if (m_pAplayer) {
		*result = aplayer_set_volume(m_pAplayer, (int)volume);
		XL_SPDLOG_INFO("aplayer_set_volume, code={:d}, volume:{:d}", *result, volume);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_set_volume, failed, player nullptr");
	return -1;
}

long AplayerImpl::IsSeeking(long* seeking)
{
	if (m_pAplayer) {
		*seeking = aplayer_is_seeking(m_pAplayer);
		XL_SPDLOG_INFO("aplayer_is_seeking, code={:d}", *seeking);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_is_seeking, failed, player nullptr");
	return -1;
}

long AplayerImpl::GetBufferProgress(long* buffer_progress)
{
	if (m_pAplayer) {
		*buffer_progress = aplayer_get_buffer_progress(m_pAplayer);
		XL_SPDLOG_INFO("aplayer_get_buffer_progress, code={:d}", *buffer_progress);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_get_buffer_progress, failed, player nullptr");
	return -1;
}

long AplayerImpl::GetConfig(const char* szConfigId, std::string& strValue)
{
	if (m_pAplayer) {
		auto p = aplayer_get_config(m_pAplayer, szConfigId);
		if (p) {
			strValue = p;
		}
		XL_SPDLOG_INFO("aplayer_get_config key={:s}, value={:s}", szConfigId, p ? p : "");
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_get_config, failed, player nullptr");
	return -1;
}

long AplayerImpl::SetConfig(const char* szConfigId, const char* szValue, long* result)
{
	if (m_pAplayer) {
		*result = aplayer_set_config(m_pAplayer, szConfigId, szValue);
		XL_SPDLOG_INFO("aplayer_set_config nRet={:d}, key={:s}, value={:s}", *result, szConfigId, szValue);
		return 0;
	}
	XL_SPDLOG_INFO("aplayer_set_config, failed, player nullptr");
	return -1;
}

void AplayerImpl::SetMainWnd(HWND main_wnd)
{
}

int AplayerImpl::AplayerEventCallback(void* user, int event_id, int param1, int param2,
	const char* param_str) {
	if (event_id == AEVENT_TICK) {
		return 0;
	}
	AplayerImpl* pThis = (AplayerImpl*)user;
	AplayerEventData* pData = new AplayerEventData();
	pData->pUserData = user;
	pData->nEventId = event_id;
	pData->param1 = param1;
	pData->param2 = param2;
	pData->strParam = param_str ? param_str : "";

	pThis->PostMessage(WM_USER_EVENT_MSG, (void*)pData);
	return 0;
}

void AplayerImpl::PostMessage(uint32_t nMsg, void* pData) {
	::PostMessage(m_playerWnd, nMsg, 0, (LPARAM)pData);
}

void AplayerImpl::OnEventMessage(void* pData) {
	AplayerEventData* pEventData = (AplayerEventData*)pData;
	XL_SPDLOG_INFO("OnEventMessage, id={:d}", pEventData->nEventId);
	auto pStream = new XLIPCStream;
	pStream->WriteInt(pEventData->nEventId);
	pStream->WriteInt(pEventData->param1);
	pStream->WriteInt(pEventData->param2);
	pStream->WriteUtf8(pEventData->strParam.c_str());
	m_conn->AsynCall("OnEvent", pStream);
	pStream->Release();
	delete pEventData;
}

void AplayerImpl::OnWndMessage(UINT message, WPARAM wParam, LPARAM lParam) {
	auto pStream = new XLIPCStream;
	pStream->WriteUint(message);
	pStream->WriteUint64(wParam);
	pStream->WriteInt64(lParam);
	m_conn->AsynCall("OnWndMessage", pStream);
	pStream->Release();
}