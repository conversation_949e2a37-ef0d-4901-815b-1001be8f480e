#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 查找所有包含 @electron/* 名称的 package.json 文件
const srcDir = path.join(__dirname, '..');
const electronPackages = [];

function findElectronPackages(dir) {
  const items = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const item of items) {
    if (item.isDirectory()) {
      const packageJsonPath = path.join(dir, item.name, 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        try {
          const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
          if (packageJson.name && packageJson.name.startsWith('@electron/')) {
            electronPackages.push({
              name: packageJson.name,
              path: path.join(dir, item.name)
            });
          }
        } catch (error) {
          // 忽略无效的 package.json 文件
        }
      }
    }
  }
}

findElectronPackages(srcDir);

console.log(`Found ${electronPackages.length} @electron/* packages:`);
electronPackages.forEach(pkg => console.log(`  - ${pkg.name} (${path.relative(process.cwd(), pkg.path)})`));

// 运行构建命令
for (const pkg of electronPackages) {
  console.log(`\nBuilding ${pkg.name}...`);
  try {
    execSync('npm run build', { 
      cwd: pkg.path, 
      stdio: 'inherit' 
    });
    console.log(`✓ ${pkg.name} built successfully`);
  } catch (error) {
    console.error(`✗ Failed to build ${pkg.name}`);
    process.exit(1);
  }
}

console.log('\nAll @electron/* packages built successfully!'); 