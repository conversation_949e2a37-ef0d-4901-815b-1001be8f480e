<script setup lang="ts">
import { ref, computed, onUnmounted, onMounted, watch, useTemplateRef } from 'vue'
import DetailTaskItem from './DetailTaskItem.vue'
import Detail from './Detail.vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
// import * as BaseType from '@root/common/task/base'
// import { taskExtraFunc } from '../../taskExtra'
// import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import { useTaskDetailStore } from '@/stores/taskDetail'
import { CreateCommonContextmenu } from '@root/common/components/ui/contextmenu'
import { generateDetailTaskContextMenu } from '../../utils'
import { IBranch, ILeaf } from '@/types/taskDetails'
import * as BaseType from '@root/common/task/base'
import { taskExtraFunc } from '@/common/task-extra'
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'
import DefaultPage from '../DefaultPage.vue'
import { PopUpNS } from '@root/common/pop-up'
import { RelatePosType } from '@root/common/pop-up/types';
import { ThunderHelper } from '@root/common/thunder-helper'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'


const props = withDefaults(defineProps<{
  show: boolean
}>(), {
  show: false,
})

const taskDetailStore = useTaskDetailStore()

const $rootElement = useTemplateRef<HTMLDivElement>('$rootElement')

const dropdownMenuList = ref([
  {
    key: 'all',
    label: '全部',
    checkbox: true,
    checked: true,
  },
  {
    key: 'downloading',
    label: '下载中',
    checkbox: true,
    checked: false,
  },
  {
    key: 'downFinish',
    label: '已完成',
    checkbox: true,
    checked: false,
  },
  {
    key: 'downFail',
    label: '下载失败',
    checkbox: true,
    checked: false,
  }
])

const detailTaskList = ref<HTMLElement | null>(null)
const showList = ref<(IBranch | ILeaf)[]>([])
const filterData = ref<{key:string, label:string}>({key: 'all', label: '全部',})
const sortTask = ref<any>({})

const isShowAll = computed(() => {
  return filterData.value.key === 'all'
})

const handleCheckboxSelect = (key: string) => {
  console.log('>>>>>>>>>>>>>>>>>>> key', key)
  dropdownMenuList.value.map(item => {
    if (item.key === key) {
      filterData.value.label = item.label
      filterData.value.key = key
      item.checked = true
    } else {
      item.checked = false
    }
    return item
  })
}

const handleFilterReset = () => {
  handleCheckboxSelect('all')
}


const emits = defineEmits<{ (event: 'update:show', value: any): void }>()

const tab = ref<'file' | 'detail'>('file')

const navList = ref<{
  path: string;
  label: string,
  type: 'bt' | 'branch' | 'btBranch'
  root?: boolean
}[]>([])

const isDetailTab = computed(() => {
  return tab.value === 'detail'
})

const taskInfo = computed(() => {
  return taskDetailStore.taskInfo
})
const branchMap = computed(() => {
  return taskDetailStore.taskSourceList
})
const addStatusTxt = computed(() => {
  if (navList.value.length && !isDetailTab.value) {
    return navList.value[navList.value.length - 1].label
  }
  return `已添加 ${taskDetailStore.downAddTotal}/${taskDetailStore.downloadAllTotal} 个文件`
})

const currentTaskIsDown = computed(() => {
  // 此处需要判断是否为Bt任务
  return taskDetailStore.taskStatus ? [BaseType.TaskStatus.Started, BaseType.TaskStatus.StartPending].includes(taskDetailStore.taskStatus) : false
})


const filterList = computed(() => {
  return showList.value.filter(item => {
    // 判断是否展示
    if (!((item.type === 'branch' && (item.data?.downloadTotal ?? 0) > 0) || ((item.type === 'leaf' || item.type === 'bt') && item.data?.isNeedDownload))) {
      return false
    }
    if (filterData.value.key === 'all') {return true}
    // console.log('>>>>>>>>>>>>>>>> ', item.type, item.data)
    if (filterData.value.key === 'downFinish') {
      console.log('>>>>>>>>>>>>>> item.data', item.data)
      return item.data?.isComplete
    }
    if (filterData.value.key === 'downFail') {
      return item.data?.status ? [BaseType.BtSubFileStatus.Failed].includes(item.data?.status) : false
    }
    if (filterData.value.key === 'downloading') {
      const fileStatus = item.data?.status
      if (item.data.taskId) {
        return fileStatus === BaseType.TaskStatus.Started
      } else if (item.type === 'branch') {
        return item.data?.isExistDown
      }
      return fileStatus ? [BaseType.BtSubFileStatus.Downloading, BaseType.BtSubFileStatus.Waiting].includes(fileStatus) : false
    }
    return true
  })
})

const isShowAddBtn = computed(() => {
  return (taskDetailStore.isBtTask || taskDetailStore.isGroupTask) && !taskDetailStore.isPanTask && !navList.value.length
})

const handleCloseDrawer = () => {
  emits('update:show', false)
}

const handleChangeTab = (val: 'file' | 'detail') => {
  tab.value = val
}

/** 跳转到顶部 */
const jumpToDetail = () => {
  detailTaskList.value?.scrollTo({ top: 0, behavior:'instant' })
}

/** 导航回退 */
const handleBack = () => {
  navList.value.splice(navList.value.length-1, 1)
  const val = navList.value[navList.value.length-1]
  const path = val?.path
  const type = val?.type ?? ''
  if (typeof path === 'undefined') {
    if (taskDetailStore.isGroupTask) {
      showList.value = taskDetailStore.taskTreeData[''].children
      taskDetailStore.clearBTTaskSourceList()
    } else {
      showList.value = taskDetailStore.taskTreeData[''].children
    }
    navList.value = []
    return
  }
  if (type === 'branch') {
    showList.value = taskDetailStore.taskTreeData[path].children
  } else if (type === 'btBranch') {
    showList.value = taskDetailStore.btTaskTreeData[path].children
  } if (type === 'bt') {
    showList.value = taskDetailStore.taskTreeData[''].children
    navList.value = []
    taskDetailStore.clearBTTaskSourceList()
  }
  jumpToDetail()
}

const handleClickItem = async (item: IBranch | ILeaf) => {
  console.log('>>>>>> item', item)
  if (item.type === 'leaf') { return }

  const lastNav = navList.value[navList.value.length-1]
  let type: 'branch' | 'btBranch' = ['bt', 'btBranch'].includes(lastNav?.type) ? 'btBranch' : 'branch'

  // 判断了为IBranch类型，如何减少判断属性是否存在
  if (item.type === 'branch') {
    navList.value.push({ path: item.key, label: item.name, type })
    if ('children' in item ) {
      showList.value = item.children
      jumpToDetail()
    }

  } else if (item.type === 'bt') {
    await taskDetailStore.insertBtTask(item.data?.taskId)
    navList.value.push({ path: item.name, label: item.name, type: 'bt' }, { path: '', label: item.name, type: 'btBranch' })
    showList.value = taskDetailStore.btTaskTreeData[''].children
    jumpToDetail()
  }
}

// 排序
watch(filterList, (newVal) => {
  // 进行分类
  const sortData = newVal.sort((a, b) => {
    return ThunderHelper.compareStr((a.data.fileName || ''), (b.data.fileName ||''))
  })
  let folderList: (IBranch | ILeaf)[] = []
  let videoList: (IBranch | ILeaf)[] = []
  // let audioList: (IBranch | ILeaf)[] = []
  // let imageList: (IBranch | ILeaf)[] = []
  // let zipList: (IBranch | ILeaf)[] = []
  let otherList: (IBranch | ILeaf)[] = []
  // console.log('>>>>>>>>>>>>>> sortData', sortData)
  sortData.forEach(item => {
    let type = 'folder'
    if (!(item.type === 'branch' || item.type === 'bt')) {
      type = TaskUtilHelper.getFileType(item.data.fileName)
    }

    switch (type) {
      case 'folder':
        folderList.push(item)
        break
      case 'video':
        videoList.push(item)
        break
      // case 'audio':
      //   audioList.push(item)
      //   break
      // case 'image':
      //   imageList.push(item)
      //   break
      // case 'zip':
      //   zipList.push(item)
      //   break
      default:
        otherList.push(item)
        break
    }
  })
  sortTask.value = [
    {
      label: '文件夹',
      isShow: folderList.length,
      list: folderList
    },
    {
      label: '视频',
      isShow: videoList.length,
      list: videoList
    },
    {
      label: '其他',
      isShow: otherList.length,
      list: otherList
    }
  ]
})

const handleContextmenu = (event: MouseEvent, file: IBranch | ILeaf) => {
  console.log('handleContextmenu', file)
  const isAllowDel = taskDetailStore.downAddTotal > (file.data.downloadTotal || 1)
  const menuList = generateDetailTaskContextMenu(file, isAllowDel)
  if (!(menuList[0] && menuList[0].length)) { return }
  CreateCommonContextmenu({
    menuList,
    parentElement: $rootElement.value!,
    clickPosition: {
      x: event.clientX,
      y: event.clientY,
    },
    onClose: () => {

    },
    onMenuItemClick: (item) => {
      handleFileOperation(item.key, file)
    }
  })

}

const handleBranchLeaf = (branch: IBranch | ILeaf): number[] => {
  let items: number[] = []
  if (branch.type === 'leaf' || branch.type === 'bt') {
    const id = branch.data?.taskId ? branch.data.taskId : branch.data?.index
    if (id) {
      items.push(id)
    }
    return items
  }
  for (const child of (branch?.children || [])) {
    if (branch.type === 'branch') {
      const res = handleBranchLeaf(child)
      items.push(...res)
    } else if (branch.type === 'leaf' || branch.type === 'bt') {
      const id = branch.data?.taskId ? branch.data.taskId : branch.data.index
      console.log('>>>>>>> id', id, items)
      if (id) {
        items.push(id)
      }
      return items
    }
  }
  return items
}

/** 其实这里是取消下载 */
const deleteSubTask = (file: IBranch | ILeaf) => {
  // 判断当前是否为最后一个文件
  // 是 删除最外层的任务 同时关闭详情窗口
  console.log('>>>>>>>>>>>>>>>>>>> file', file)
  // 判断文件是否为任务组
  if (taskDetailStore.isGroupTask) {
    const leafs = handleBranchLeaf(file)
    console.log('>>>>>>>>>>>>>> leafs', leafs)
    // 判断是否在处理bt子任务
    if (taskDetailStore.isInsertBtTask) {
      taskExtraFunc.groupCancelBtSubTask(taskDetailStore.currentTaskId, [{taskId: taskDetailStore.currentViewTaskId, indexs: leafs}])
    } else {
      taskExtraFunc.groupCancelSubTask(taskDetailStore.currentTaskId, leafs)
    }
  } else {
    const BTSubLeafs = handleBranchLeaf(file)
    console.log('> bt leafs', BTSubLeafs)
    file.data.index && taskExtraFunc.cancelBtSubTask(taskDetailStore.currentTaskId, BTSubLeafs)
  }
}

/** 彻底删除，等于取消下载+删除已下载的文件 */
const deleteSubTaskCompletely = (file: IBranch | ILeaf) => {
  // 判断当前是否为最后一个文件
  // 是 删除最外层的任务 同时关闭详情窗口
  if (taskDetailStore.isGroupTask) {
    const leafs = handleBranchLeaf(file)
    console.log('>>>>>>>>>>>>>> leafs', leafs)
    if (taskDetailStore.isInsertBtTask) {// 判断是否在处理bt子任务
      taskExtraFunc.groupDeleteBtSubTask(taskDetailStore.currentTaskId, [{taskId: taskDetailStore.currentViewTaskId, indexs: leafs}])
    } else {
      taskExtraFunc.groupDeleteSubTask(taskDetailStore.currentTaskId, leafs)
    }

  } else {
    const BTSubLeafs = handleBranchLeaf(file)
    console.log('> bt leafs', BTSubLeafs)
    taskExtraFunc.deleteBtSubTask(taskDetailStore.currentTaskId, BTSubLeafs)
  }
}

/** 下载任务 */
const downloadSubTask = (file: IBranch | ILeaf) => {

}

const handleFileOperation = (key: string, file: IBranch | ILeaf) => {
  console.log('handleFileOperation', key, file)
  const isTask = file.data.taskId
  const isBranch = file.type === 'branch'
  switch (key) {
    case 'download':
      console.log('handleFileOperation download')
      // if (isBranch) {
      //   taskExtraFunc.groupDownloadSubTask(taskDetailStore.currentViewTaskId, handleBranchLeaf(file))
      // } else if (isTask) {
      //   taskExtraFunc.groupDownloadSubTask(taskDetailStore.currentViewTaskId, [file.data.taskId])
      // }
      break
    case 'delete':
      deleteSubTask(file)
      break
    case 'shiftDelete':
      deleteSubTaskCompletely(file)
      break
    case 'play':
      console.log('handleFileOperation play')
      const realIndex = file.data?.index ?? -1
      const taskId = taskInfo.value.taskId
      customFile(file.data.taskId || taskId, realIndex)
      break
    case 'retryDownload':
      console.log('handleFileOperation retryDownload')
      // if (isBranch) {
      //   const leafs = handleBranchLeaf(file)
      //   console.log('>>>>>>>>>>> leafs', leafs)
      //   if (leafs.length) {
      //     taskExtraFunc.groupDownloadSubTask(taskDetailStore.currentViewTaskId, leafs)
      //   }
      // } else if (isTask) {
      //   taskExtraFunc.groupDownloadSubTask(taskDetailStore.currentViewTaskId, [file.data.taskId])
      // }
      break
    // case 'pause':
    //   if (isTask) {
    //     // taskExtraFunc.stopTaskById(file.data.taskId, BaseType.TaskStopReason.Manual)
    //     console.log('>>>>>>>>>>>> 暂无此功能')
    //   }
    //   console.log('handleFileOperation pause')
    //   break

  }
}

/** 播放 */
const customFile = (id: number, index=-1) => {
  console.log('>>>> 播放handlePlay', id)
  ConsumeManagerNs.consumeTask(id, index)
}

/** 继续添加 */
const handleKeepAdd = async () => {
  const res = await PopUpNS.popup('KeepAdd', {
    taskId: taskDetailStore.currentTaskId,
    relatePos: RelatePosType.CenterParent,
    windowWidth: 680,
    windowHeight: 592,
  }, { modal: true, resizable: false });
  console.log('>>>>>>>>>>>>>> handleKeepAdd', res)
}

onMounted(() => {
  if (taskDetailStore.singleTask) {
    tab.value = 'detail'
  }
  console.log('>>>>>>>>>>>>>>>>> taskDetailStore.taskTreeData', taskDetailStore.taskTreeData)
  showList.value = taskDetailStore.taskTreeData['']?.children ?? []
})

onUnmounted(() => {
  taskDetailStore.unInit()
})

</script>

<template>
  <div :class="$style.taskSideDrawer" ref="$rootElement">
    <div :class="$style.mask" @click="handleCloseDrawer"></div>
    <div :class="$style.contentWrapper">
      <div :class="$style.header">
        <div :class="$style.title">
          {{ taskInfo?.taskName || '未命名任务' }}
        </div>

        <div :class="$style.close">
          <Button variant="ghost" is-icon size="sm" @click="handleCloseDrawer">
            <i class="xl-icon-general-close-l"></i>
          </Button>
        </div>
      </div>
      <div :class="$style.tabs">
        <div :class="$style.tabsLeft">
          <div
            v-if="!taskDetailStore.singleTask"
            :class="[$style.tabLab, tab === 'file' && $style['is-active']]"
            @click="handleChangeTab('file')"
          >文件</div>
          <div
            :class="[$style.tabLab, tab === 'detail' && $style['is-active']]"
            @click="handleChangeTab('detail')"
          >详情信息</div>
        </div>
        <div v-if="isShowAddBtn" :class="$style.tabsRight">
          <Button variant="ghost" size="sm" style="color: var(--primary-primary-default);" @click="handleKeepAdd">
            <i class="xl-icon-add"></i>继续添加
          </Button>
        </div>
      </div>
      <div v-if="!isDetailTab" :class="$style.navigationWrapper">
        <div :class="$style.status">
          <div :class="$style.back" v-if="navList.length && !isDetailTab" @click="handleBack">
            <i class="xl-icon-direction-left-m"></i>
          </div>
          <div :class="$style.info">{{ addStatusTxt }}</div>
        </div>
        <div v-if="!isDetailTab" :class="$style.filter">
          <DropdownMenu
            :items="dropdownMenuList"
            @select="handleCheckboxSelect"
            side="bottom"
            align="end"
          >
            <Button
              size="sm"
              :variant="!isShowAll ? 'outline' : 'ghost'"
              right-icon="xl-icon-general-close-m"
              :has-right-icon="!isShowAll"
              @right-icon-click="handleFilterReset"
            >
              <div
                :class="[$style.filterContent, !isShowAll && $style['is-active']]"
              >
                <i class="xl-icon-filter"></i>
                <span v-if="!isShowAll">{{ filterData.label }}</span>
              </div>
            </Button>
          </DropdownMenu>
        </div>
      </div>
      <div :class="$style.content">
        <div v-if="!isDetailTab" ref="detailTaskList" :class="$style.taskList">
          <div v-for="taskMap in sortTask" :key="taskMap.label">
            <div v-if="taskMap.isShow">
              <div :class="$style.taskTitle">{{taskMap.label}}</div>
              <DetailTaskItem
                v-for="item in taskMap.list"
                @click="handleClickItem(item)"
                @right-click="handleContextmenu"
                @customFile="customFile"
                :currentTaskIsDown="currentTaskIsDown"
                :key="item.key"
                :task="item"
              />
            </div>
          </div>

          <DefaultPage v-show="!isDetailTab && !filterList.length" :isDownload="false" />
        </div>
        <div v-else :class="$style.taskDetail">
          <Detail></Detail>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" module>
.taskSideDrawer {
  --drawer-side-padding: 24px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: var(--z-index-drawer);

  .mask {
    background-color: var(--background-background-mask);
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }

  .contentWrapper {
    position: absolute;
    width: 500px;
    height: 100%;
    right: 0;
    top: 0;
    background: var(--background-background-elevated);
    display: flex;
    flex-direction: column;
  }

  :global(.xly-icon-general-close-l) {
    color: var(--font-font-1);
  }

  .header {
    flex-shrink: 0;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--drawer-side-padding);
  }

  .title {
    font-weight: 700;
    font-size: 14px;
    line-height: 22px;
    color: var(--font-font-1);
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    margin-right: 16px;
  }

  .close {
    flex-shrink: 0;
  }

  .tabs {
    flex-shrink: 0;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--drawer-side-padding);
  }

  .tabsLeft {
    font-size: 14px;
    color: var(--font-font-2);
    height: 100%;
    display: flex;
    align-items: center;
    position: relative;
  }

  .tabLab {
    position: relative;
    margin-left: 40px;
    cursor: pointer;
    height: 100%;
    line-height: 38px;
    transition: color 0.2s ease;

    &.is-active {
      color: var(--primary-primary-font-default);

      &::after {
        position: absolute;
        content: "";
        width: 100%;
        height: 2px;
        border-radius: 2px;
        background-color: var(--primary-primary-font-default);
        bottom: 0;
        left: 0;
      }
    }

    &:first-of-type {
      margin-left: 0px;
    }
  }

  .tabsRight {}

  .navigationWrapper {
    flex-shrink: 0;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--drawer-side-padding);
  }

  .status {
    margin-right: 16px;
    display: flex;
    align-items: center;
  }

  .back {
    margin-right: 8px;
    cursor: pointer;
  }

  .info {
    line-height: 13px;
    color: var(--font-font-2);
    font-size: 13px;
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 1;
    -webkit-line-clamp: 1;
  }

  .xl-icon-direction-left-m {
    margin-right: 8px;
  }

  .filter {
    flex-shrink: 0;
  }

  .content {
    flex: 1;
    overflow: auto;
    .taskTitle {
      margin: 4px 0;
      color: var(--font-font-3);
      font-size: 12px;
      line-height: 20px;
      padding-left: 12px;
      &:first-of-type {
        margin-top: 0;
      }
    }
  }

  .taskList {
    position: relative;
    padding: 0 12px;
    overflow: auto;
    height: 100%;
  }

  .taskDetail {
    margin-top: 16px;
  }

  .filterContent {
    display: flex;
    align-items: center;
    gap: 6px;

    &.is-active {
      color: var(--primary-primary-default);
    }
  }
}
</style>