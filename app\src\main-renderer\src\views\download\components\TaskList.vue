<script lang="ts" setup>
import { ref, watch, useTemplateRef, nextTick } from 'vue'
import { clipboard } from 'electron'
import Checkbox from "@root/common/components/ui/checkbox/index.vue"

import { FolderDialogHelper } from '@root/common/helper/folder-dialog-helper'
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'
import { taskExtraFunc } from '@/common/task-extra'
import { generateTaskContextMenu, moveTaskMessage } from '../utils'
import { CreateCommonContextmenu } from '@root/common/components/ui/contextmenu'
import XMPMessage from '@root/common/components/ui/message/index'
import { IDialogStatus, IDialogInfo } from '../types'
import { ThunderUtil } from '@root/common/utils'
import TaskItem from './TaskItem.vue'
import { getHistoryMovePath, setHistorySaveMovePath } from '../utils'
import { TaskCtrlOperatorNS } from '@/common/task-ctrl-operator'
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'

import * as BaseType from '@root/common/task/base'

const props = withDefaults(defineProps<{
  taskList: BaseType.IDownSimpleTaskInfo[]
  completed?: boolean
}>(), {
  taskList: () => [],
})
const emits = defineEmits<{
  (e: 'viewTaskDetail', data: BaseType.IDownSimpleTaskInfo): void
  (e: 'openRenameDialog', data: BaseType.IDownSimpleTaskInfo): void
  (e: 'handleAddCloud', data: BaseType.IDownSimpleTaskInfo[]): void
  (e: 'updateSelectedId', id: number[]): void
  (e: 'showDialog', data: BaseType.IDownSimpleTaskInfo, status: IDialogStatus, icon: string): void
}>()

const downloadScrollerVm = ref<any>()

const $rootElement = useTemplateRef<HTMLDivElement>('$rootElement')

const isShowDialog = ref(false)
/** 当前双击的task */
const currentTask = ref<{
  path: string,
  name: string,
  url: string,
  size: number,
}>({
  path: '',
  name: '',
  url: '',
  size: 0
})
const clearLocalFile = ref(true) // 清除checkbox本地文件
const currentStatus = ref<IDialogStatus>(IDialogStatus.Fail)
const dialogInfo = ref<IDialogInfo>({
  title: '确定删除该下载任务',
  tip: '删除任务后，可在回收站找回',
  confirmText: '确定删除',
  icon: ''
})

/** 选中的id */
const selectedIds = ref<number[]>([])
/** 上一个选中的Id */
const lastItem = ref<BaseType.IDownSimpleTaskInfo|null>(null)

const handleChooseAll = () => {
  console.log('handleChooseAll')
  selectedIds.value = props.taskList.map(item => item.taskId)
}

const handleShowDrawer = async (task: BaseType.IDownSimpleTaskInfo) => {
  console.log('handleShowDrawer', task)
  selectedIds.value = [ task.taskId ]
  emits('viewTaskDetail', task)
}

const handleContextmenu = async (event: MouseEvent, task: BaseType.IDownSimpleTaskInfo) => {
  console.log('handleContextmenu', task)
  // lastItem.value = task
  // 多选
  if (!selectedIds.value.includes(task.taskId)) {
    selectedIds.value = [ task.taskId ]
  }
  let isBtSubFileScheduler = false
  let isSupportPlay = false
  if (task.taskType === BaseType.TaskType.Bt) {
    isBtSubFileScheduler = (await taskExtraFunc.getBtSubFileScheduler(task.taskId)) === BaseType.XLBTTaskSubFileSchedulerType.XL_BTTaskSubFileSequnecialScheduler
  }
  if (selectedIds.value.length === 1) {
    isSupportPlay = await taskExtraFunc.getIsSupportPlay(selectedIds.value[0])
  }
  const selectedTasks = props.taskList.filter(item => selectedIds.value.includes(item.taskId))
  // 判断是否存云盘
  // let isAlreadyCloud = false
  // for (const item of selectedTasks) {
  //   isAlreadyCloud = !!(await taskExtraFunc.getUserData(item.taskId, userDataPan.key, userDataPan.field, userDataPan.defaultVal))
  //   if (isAlreadyCloud) {
  //     break;
  //   }
  // }
  // 是否存在上一次移动路径
  const movePathList = await getHistoryMovePath()
  const menuList = generateTaskContextMenu({
    taskInfos: selectedTasks,
    isSupportPlay,
    isBtSubFileScheduler: isBtSubFileScheduler,
    movePathList
  })
  console.log('>>>>>>>>>>>>> menuList', menuList)
  CreateCommonContextmenu({
    menuList,
    parentElement: $rootElement.value!,
    clickPosition: {
      x: event.clientX,
      y: event.clientY,
    },
    onClose: () => {

    },
    onMenuItemClick: (item) => {
      console.log('>>>>>>>>>> item', item)
      let key = item.key
      let lastPath = ''
      if (item.custom === 'moveHistoryPath') {
        key = 'moveHistoryPath'
        lastPath = item.key
      }
      handleFileOperation(key, lastPath, JSON.parse(JSON.stringify(selectedTasks)))
    }
  })
}

const handleFileOperation = async (key: string, lastPath: string, tasks: BaseType.IDownSimpleTaskInfo[]) => {
  console.log('handleFileOperation', key, tasks)
  const ids = tasks.map(task => {
    return task.taskId
  })
  let isClear = true
  switch (key) {
    case 'play':
      console.log('play')
      customFile(tasks[0])
      break
    case 'openFile':
      console.log('openFile')
      customFile(tasks[0])
      break
    case 'pause':
      console.log('pause')
      pauseTask(tasks)
      break
    case 'download':
      console.log('download')
      startTask(tasks)
      break
    case 'openFolder':
      console.log('openFolder')
      openFile(tasks[0])
      break
    case 'delete':
      console.log('delete')
      handleShowDialog(null, IDialogStatus.Recycle, '')
      isClear = false
      break
    case 'shiftDelete':
      isClear = false
      handleShowDialog(null, IDialogStatus.Delete, '')
      break
    case'rename':
      console.log('rename')
      openRenameDialog(tasks[0])
      break
    case'retryDownload':
      console.log('retryDownload')
      // 判断是否存在未失效
      let isExist = false
      for(const task of tasks) {
        isExist = await TaskCtrlOperatorNS.isExistTaskFile(task)
        if (isExist) { break }
      }
      console.log('>>>>>>>>>>> isExist', isExist)
      if (isExist) {
        isClear = false
        handleShowDialog(null, IDialogStatus.RetryDownload, '')
      } else {
        retryDownload(ids)
      }
      break
    case 'copy':
      console.log('copy')
      copyUrl(tasks)
      break
    case 'otherDirectory':
      console.log('otherDirectory')
      handleMoveTask(tasks)
      break
    case 'moveHistoryPath':
      console.log('moveHistoryPath')
      handleMoveTask(tasks, lastPath)
      break
    case 'fileDetail':
      console.log('fileDetail')
      handleShowDrawer(tasks[0])
      break
    case'report':
      console.log('report')
      break
    case 'btSubFileScheduler':
      handleUpdateBtSubFileScheduler(tasks[0])
      break
    case 'addCloud':
      console.log('addCloud')
      emits('handleAddCloud', tasks)
      break
    default:
      console.log('default')
      break
  }
  if (isClear) {
    clearSelectId()
  }

}

/** 移动任务 */
const handleMoveTask = async (tasks: BaseType.IDownSimpleTaskInfo[], lastPath='') => {
  console.log('handleMoveTask', tasks, lastPath)
  const oneTask = tasks[0]
  let fails:{ name: string | undefined; path: string | undefined; newPath: string; message: string; }[] = []
  let selectedPath:null|string = ''
  if (lastPath) {
    selectedPath = lastPath
  } else {
    selectedPath = await FolderDialogHelper.selectDirectory(oneTask.savePath, '选择文件夹')
  }
  if (!selectedPath) { return }
  setHistorySaveMovePath(selectedPath)
  for (const task of tasks) {
    console.log('>>>>>>>> selectedPath', selectedPath)
    if (selectedPath) {
      const res = await taskExtraFunc.taskMove(task.taskId, selectedPath)
      const message = moveTaskMessage(res)
      if (res !== BaseType.MoveTaskToNewPathResult.Succ) {
        fails.push({name: task.taskName, path: task.savePath, newPath: selectedPath, message})
      }
    }
  }
  if (fails.length > 0) {
    fails.forEach(item => {
      XMPMessage({
        message: item.message,
        type: 'error',
        duration: 3000,
      })
    })
  } else {
    XMPMessage({
      message: '移动成功',
      type:'success',
      duration: 3000,
    })
  }
  console.log('>>>>>>>>>>>>>>>>> fails', fails)
}

/** 顺序下载 */
const handleUpdateBtSubFileScheduler = async (task: BaseType.IDownSimpleTaskInfo) => {
  let isBtSubFileScheduler = false
  if (task.taskType === BaseType.TaskType.Bt) {
    isBtSubFileScheduler = (await taskExtraFunc.getBtSubFileScheduler(task.taskId)) === BaseType.XLBTTaskSubFileSchedulerType.XL_BTTaskSubFileSequnecialScheduler
  }
  const scheduler = isBtSubFileScheduler ? BaseType.XLBTTaskSubFileSchedulerType.XL_BTTaskSubFileDefaultScheduler : BaseType.XLBTTaskSubFileSchedulerType.XL_BTTaskSubFileSequnecialScheduler
  taskExtraFunc.updateBtSubFileScheduler(task.taskId, scheduler)
}

/** 消费文件 */
const customFile = (task: BaseType.IDownSimpleTaskInfo) => {
  console.log('>>>> 播放handlePlay', task)
  ConsumeManagerNs.consumeTask(task.taskId, -1)
}

/** 暂停任务 */
const pauseTask = (tasks: BaseType.IDownSimpleTaskInfo[]) => {
  if (tasks.length === 1 && tasks[0].taskId) {
    taskExtraFunc.stopTaskById(tasks[0].taskId, BaseType.TaskStopReason.Manual)
  } else if (tasks.length > 1) {
    // 批量操作
    const ids = tasks.map(task => {
      return task.taskId
    })
    console.log('批量操作 pauseTask')
    taskExtraFunc.batchStopTasks(ids, BaseType.TaskStopReason.Manual)
  }
}

/** 开始任务 */
const startTask = (tasks: BaseType.IDownSimpleTaskInfo[]) => {
  if (tasks.length === 1 && tasks[0].taskId) {
    taskExtraFunc.startTaskById(tasks[0].taskId)
  } else if (tasks.length > 1) {
    // 批量操作
    const ids = tasks.map(task => {
      return task.taskId
    })
    console.log('批量操作 startTask')
    taskExtraFunc.batchStartTasks(ids)
  }
}

/** 移到回收站 */
const handleRecycle = async(ids: number[]) => {
  if (ids.length === 1) {
    taskExtraFunc.recycleTaskById(ids[0])
  } else if (ids.length > 1) {
    console.log('批量操作 handleRecycle', ids)
    const res = await taskExtraFunc.batchRecycleTasks(ids)
    console.log('>>>>>>>>>>>>> res', res)
  }
  clearSelectId()
}

/** 彻底删除任务 */
const deleteTask = async (ids: number[], deleteFile: boolean) => {
  if (ids.length === 1) {
    const res = taskExtraFunc.delTaskById(ids[0], deleteFile)
  } else if (ids.length > 1) {
    // 批量操作
    console.log('批量操作 deleteTask', ids)
    const res = await taskExtraFunc.branchDelTaskById(ids, deleteFile)
    console.log('>>>>>>>>>>>>>>> res', res)
  }
  clearSelectId()
}

/** 复制链接 */
const copyUrl = async (tasks: BaseType.IDownSimpleTaskInfo[]) => {
  // 判断task是否为任务组
  let urls: string[] = []
  for (const task of tasks) {
    if (task.taskType === BaseType.TaskType.Group) {
      // 解析任务组数
      const taskIds = await taskExtraFunc.getGroupSubTaskIds(task.taskId)
      for (const taskId of taskIds) {
        const url = await taskExtraFunc.getTaskUrl(taskId)
        url && urls.push(url)
      }
    } else {
      task.url && urls.push(task.url)
    }
  }

  if (urls.length > 0) {
    const urlsStr = urls.join('\n')
    clipboard.writeText(urlsStr)
    XMPMessage({
      message: '复制成功',
      type: 'success'
    })
  }
}

/** 重新下载 */
const retryDownload = async (ids: number[]) => {
  console.log('retryDownload', ids)
  for (const id of ids) {
    await taskExtraFunc.reDownloadTaskById(id)
  }
}

/** 打开重命名弹窗 */
const openRenameDialog = (task: BaseType.IDownSimpleTaskInfo) => {
  console.log('open rename dialog', task)
  emits('openRenameDialog', task)
}

/** 打开文件 */
const openFile = async (task: BaseType.IDownSimpleTaskInfo) => {
  console.log('>>>>>>> open file', task)
  const isSuccess = await ConsumeManagerNs.openTaskFolder(task.taskId)
  console.log('> code', isSuccess)
  if (!isSuccess) {
    XMPMessage({
      message: '未找到对应文件',
      type: 'warning'
    })
  }
}

const handleItemClick = (event: MouseEvent, item: BaseType.IDownSimpleTaskInfo) => {
  console.log('handleItemClick',event, event.ctrlKey, item)
  if (event.ctrlKey) {
    console.log('ctrl')
    if (item.taskId && selectedIds.value.includes(item.taskId)) {
      selectedIds.value = selectedIds.value.filter(id => id !== item.taskId)
    } else {
      selectedIds.value.push(item.taskId)
    }
  } else if (event.shiftKey) {
    selectedIds.value = []
    let lastIndex = props.taskList.findIndex(item => lastItem.value && item.taskId === lastItem.value.taskId)
    let index = props.taskList.indexOf(item)
    if (index >= lastIndex) {
      for (let i = lastIndex; i <= index; i++) {
        selectedIds.value.push(props.taskList[i].taskId)
      }
    } else {
      for (let i = index; i <= lastIndex; i++) {
        selectedIds.value.push(props.taskList[i].taskId)
      }
    }
  } else {
    selectedIds.value = [ item.taskId ]
  }
  lastItem.value = item
}

const handleDblclick = async (
  task: BaseType.IDownSimpleTaskInfo,
  icon: string,
  taskFileExists: boolean,
  isSupportPlay: boolean
) => {
  // 消费或者打开文件夹
  console.log('handleDblclick', task)
  console.log('>>>>>>>>>>>>>> task.taskStatus', task.taskStatus)

  if (!taskFileExists && task.taskStatus === BaseType.TaskStatus.Succeeded) {
    // 失效文件
    handleShowDialog(task, IDialogStatus.Lost, icon)
  } else if (task.taskStatus === BaseType.TaskStatus.Failed || task.taskStatus === BaseType.TaskStatus.Unkown) {
    // 调起弹窗
    handleShowDialog(task, IDialogStatus.Fail, icon)
  } else {
    // 消费
    // 非视频 & 未下载完
    if (task.taskStatus === BaseType.TaskStatus.Succeeded || isSupportPlay || task.taskType === BaseType.TaskType.Group || task.taskType === BaseType.TaskType.Bt) {
      if (isSupportPlay && task.taskStatus === BaseType.TaskStatus.Stopped) {
        XMPMessage({
          message: '已为您打开文件，任务继续下载',
          type: 'info'
        })
      }
      ConsumeManagerNs.consumeTask(task.taskId, -1)
    } else {
      XMPMessage({
        message: '该任务需要下载完成后打开',
        type: 'info'
      })
    }
  }
}

const handleDialogConfirm = () => {
  console.log('handleDialogConfirm', currentStatus, clearLocalFile.value)
  const ids = [...selectedIds.value]
  switch (currentStatus.value) {
    case IDialogStatus.Fail:
    case IDialogStatus.Lost:
    case IDialogStatus.RetryDownload:
      retryDownload(ids)
      break
    case IDialogStatus.Recycle:
      handleRecycle(ids)
      break
    case IDialogStatus.Delete:
      deleteTask(ids, clearLocalFile.value)
      break
    default:
      break
  }
  handleCloseDialog()
}
const handleCloseDialog = () => {
  isShowDialog.value = false
  clearSelectId()
}

const handleShowDialog = (task: BaseType.IDownSimpleTaskInfo | null, status: IDialogStatus, icon: string) => {
  isShowDialog.value = true
  currentTask.value = {
    name: task?.taskName || '',
    path: task?.savePath || '',
    url: task?.url || '',
    size: task?.fileSize || 0,
  }
  console.log('> handleShowDialog', currentTask.value, status)
  currentStatus.value = status
  let info: IDialogInfo = {
    title: '',
    tip: '',
    confirmText: '',
    icon
  }
  switch (status) {
    case IDialogStatus.Lost:
      info = {
        title: '该文件已丢失无法预览',
        tip: '该文件已从本地删除或丢失，请尝试重新下载',
        confirmText: '重新下载',
        icon
      }
      break
    case IDialogStatus.RetryDownload:
      info = {
        title: '重新下载',
        tip: '重新下载会删除本地文件，确定要重新下载吗',
        confirmText: '确定',
        icon
      }
      break
    case IDialogStatus.Recycle:
      info = {
        title: '确定删除该下载任务',
        tip: '删除任务后，可在回收站找回',
        confirmText: '确认删除',
        icon
      }
      break
    case IDialogStatus.Delete:
      clearLocalFile.value = true
      info = {
        title: '确定彻底删除该任务？',
        tip: '',
        confirmText: '确认删除',
        icon
      }
      break
    default:
      info = {
        title: '下载失败无法预览',
        tip: '该文件下载失败无法预览，请尝试重新下载',
        confirmText: '重新下载',
        icon
      }
      break
  }
  dialogInfo.value = info
}

watch(selectedIds, (newVal) => {
  emits('updateSelectedId', newVal)
}, { deep: true })

watch(()=> props.taskList, (newVal, oldVal) => {
  if (downloadScrollerVm.value) {
    downloadScrollerVm.value.updateVisibleItems(true)
  }
  if (newVal.length && newVal.length !== oldVal.length) {
    // 校验是否下载完成, 下载完从selectedIds剔除调
    for (let i = 0; i < selectedIds.value.length; i++) {
      const id = selectedIds.value[i]
      newVal.find(item => item.taskId === id) && selectedIds.value.splice(i, 1)
    }
    emits('updateSelectedId', selectedIds.value)
  }
}, { deep: true })

const exposeRecycle = () => {
  handleShowDialog(null, IDialogStatus.Recycle, '')
}

const clearSelectId = () => {
  console.log('>>>>>>>>>>>> 清空选中')
  selectedIds.value = []
}

const scrollToItem = (index: number, taskId: number) => {
  console.log('>>>>>>>>>>>>>>>  index', index, downloadScrollerVm.value)
  selectedIds.value = [ taskId ]
  downloadScrollerVm.value && downloadScrollerVm.value.scrollToItem(index || 0)
}

defineExpose<{
  exposeRecycle: () => void;
  clearSelectId: () => void;
  scrollToItem: (index: number, taskId: number) => void;
}>({
  exposeRecycle,
  clearSelectId,
  scrollToItem
});

</script>


<template>
  <div
    ref="$rootElement"
    class="content-list-wrapper"
    tabindex="0"
    @keydown.ctrl.a="handleChooseAll"
    @click.self="clearSelectId"
  >
    <RecycleScroller
      v-if="taskList.length"
      v-slot="{ item, index }"
      ref="downloadScrollerVm"
      class="file-list__wrapper"
      key-field="taskId"
      :items="taskList"
      :item-size="completed ? 86 : 94"
      @click.self="clearSelectId"
    >
      <TaskItem
        :key="item.taskId"
        :data="item"
        :data-index="index"
        :isActive="selectedIds.includes(item.taskId)"
        :completed="completed"
        @customFile="customFile"
        @openFile="openFile"
        @viewTaskDetail="handleShowDrawer"
        @right-click="handleContextmenu"
        @retryDownload="retryDownload"
        @pauseTask="pauseTask"
        @startTask="startTask"
        @recycle="(task) => handleShowDialog(task, IDialogStatus.Recycle, '')"
        @click="(event) => handleItemClick(event, item)"
        @handleDblclick="handleDblclick"
      />
    </RecycleScroller>

    <Dialog
      :title="dialogInfo.title"
      :variant="[IDialogStatus.Lost, IDialogStatus.Fail, IDialogStatus.RetryDownload ].includes(currentStatus) ? 'thunder' : 'error'"
      :show-trigger="false"
      v-model:open="isShowDialog"
      @confirm="handleDialogConfirm"
      @cancel="handleCloseDialog"
      :confirm-text="dialogInfo.confirmText"
    >
      <div>
        <p v-if="dialogInfo.tip" class="dialog-task-tips">
          {{ dialogInfo.tip }}
        </p>
        <div v-if="currentStatus === IDialogStatus.Fail"  class="dialog-task-content">
          <div class="dialog-task-icon file-icon-type" :class="dialogInfo.icon"></div>
          <div class="dialog-task-info">
            <div class="dialog-task-name">{{ currentTask.name }}</div>
            <div class="dialog-task-size">{{ ThunderUtil.bytesToSize(currentTask.size, 2) }}</div>
          </div>
        </div>
        <Checkbox v-if="currentStatus === IDialogStatus.Delete" v-model="clearLocalFile" label="clearLocalFile">
          <span class="download-trash-checkbox-label">同时删除本地文件</span>
        </Checkbox>
      </div>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.content-list-wrapper {
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}
.dialog-task-tips {
  color: var(--font-font-2);
  font-size: 13px;
  line-height: 38px;
  height: 38px;
  // margin-top: 20px;
}
.file-list__wrapper {
  padding: 0 22px;
  min-height: 0;
  overflow: overlay;
  height: 100%;
  flex: 1;
}
.dialog-task-content {
  display: flex;
  align-items: center;
  height: 72px;
  padding: 0 20px;
  margin-top: 8px;
  box-sizing: border-box;
  border: 1px solid rgba(229, 230, 235, 1);
  border-radius: var(--border-radius-M);

  .dialog-task-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    position: relative;
    border-radius: 4px;
  }
  .dialog-task-info {
    margin-left: 12px;
  }
  .dialog-task-name {
    color: var(--font-font-1);
    font-size: 13px;
    line-height: 22px;
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 1;
    -webkit-line-clamp: 1;
  }
  .dialog-task-size {
    color: var(--font-font-3);
    font-size: 12px;
    line-height: 20px;
    margin-top: 2px;
  }
}
</style>