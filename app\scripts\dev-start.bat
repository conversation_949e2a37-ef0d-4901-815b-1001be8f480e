@echo off
echo Starting development mode...
echo.

echo Starting modal-renderer development server...
start "Modal Renderer Dev Server" cmd /k "cd src\modal-renderer && npm run dev"

echo Waiting for modal-renderer server to start...
timeout /t 10 /nobreak > nul

echo Checking if modal-renderer server is running...
netstat -ano | findstr :9529
if %errorlevel% neq 0 (
    echo Warning: Modal renderer server may not be running on port 9529
) else (
    echo Modal renderer server is running on port 9529
)

echo Starting main application...
cd ..\bin\Release\ && Thunder.exe --inspect=5858

echo Development mode started!
pause 