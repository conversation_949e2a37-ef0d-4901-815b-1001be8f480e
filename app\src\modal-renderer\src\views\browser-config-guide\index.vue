<template>
  <div class="browser-config-guide-container">
    <div class="browser-config-guide-header draggable">
      <span>
        千万用户正在使用插件下载支持
      </span>
      <div class="browser-config-guide-header-right none-draggable">
        <Tooltip side="left" to=".browser-config-guide-header-right" contentClass="browser-help-tooltip-tip-content">
          <template #trigger>
            <i class="xl-icon-tips-question-circle-l"></i>
          </template>
          <template #content>
            <ul class="browser-help-tooltip">
              <li>下载支持关闭：请到对应浏览器的扩展管理设置。</li>
              <li>视频下载支持：由于部分站点做了保护，或者是基于M3U8协议播放的视频，将无法显示迅雷下载。</li>
            </ul>
          </template>
        </Tooltip>
        <Button variant="ghost" is-icon @click="handleClose" class="none-draggable">
          <i class="xl-icon-general-close-m"></i>
        </Button>
      </div>
    </div>
    <div class="browser-config-guide-body">
        <!-- 轮播图 -->
        <div class="carousel-section">
          <!-- Tab指示器 -->
          <div class="carousel-indicators">
            <ul>
              <li
                v-for="(tab, index) in carouselTabs"
                :key="index"
                :class="['indicator', { active: currentSlide === index }]"
                @click="goToSlide(index)"
                @mouseenter="goToSlide(index)"
                :title="`点击查看${tab.description}`"
              >
                {{ tab.title }}
              </li>
            </ul>
          </div>
          
          <div class="carousel-container">
            <!-- 轮播内容 -->
            <div class="carousel-track" :style="{ transform: `translateX(${-currentSlide * 33.333333}%)` }">
              <div v-for="(tab, index) in carouselTabs" :key="index" class="carousel-slide">
                <img 
                  :src="tab.image" 
                  :alt="tab.title"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 浏览器列表 -->
        <div class="browser-list-section">

          <!-- 常规浏览器列表 -->
          <div v-if="browserList.length > 0" class="browser-list">
            <div 
              v-for="browser in browserList"
              :key="browser.exeName"
              :class="[
                'browser-item',
                {
                  'is-default': browser.default,
                  'is-installed': browser.install,
                  'is-opened': browser.open,
                  'is-cooperate': browser.cooperate
                }
              ]"
                          >
              <span class="browser-support-icon">
                <img 
                  :src="browser.logo" 
                  :alt="browser.name"
                />
              </span>
              <div class="browser-info">
                <div class="browser-name">
                  {{ browser.name }}
                </div>
                <div class="browser-desc">插件下载支持</div>
                <div class="browser-tags">
                  <span v-if="browser.default" class="status-tag default">默认</span>
                  <span v-if="browser.cooperate" class="status-tag recommend">推荐</span>
                </div>
              </div>
              <div class="browser-action">
                <Button 
                  v-if="browser.open" 
                  variant="primary" 
                  size="sm"
                  disabled
                >
                  已开启
                </Button>
                <Button 
                  v-else
                  variant="primary" 
                  size="sm"
                  :loading="actionLoading === browser.exeName"
                  @click="handleBrowserAction(browser)"
                >
                  一键开启
                </Button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">
              <i class="xl-icon-info"></i>
            </div>
            <p>未检测到已安装的浏览器</p>
            <p>请先安装 Chrome、Edge 或其他主流浏览器</p>
          </div>
        </div>
      </div>
  </div>

</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import Button from '@root/common/components/ui/button/index.vue'
import { PopUpNS } from '@root/common/pop-up'
import { usePositionMixinComponent } from '@/common/mixins';
import * as PopUpTypes from '@root/common/pop-up/types'
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import { BrowserHelperNs, IBrowserInfo, IMoreInfo } from '@root/common/helper/browser-config-helper'
import MessageBox from '@root/common/components/ui/message-box'
// import { killRunningChrome, resetSecurePreferencesSetting, findProcess } from '../../utils/chrome-extension-install'
// import { SinglePromise } from '../../utils/single-promise'


// 轮播图资源
import browser01 from '@/common/assets/images/dialog/browser-01.png'
import browser02 from '@/common/assets/images/dialog/browser-02.png'
import browser03 from '@/common/assets/images/dialog/browser-03.png'

// 轮播图tab数据
const carouselTabs = [
  {
    title: '视频下载',
    image: browser01,
    description: '一键下载各大视频网站内容'
  },
  {
    title: '链接/文件下载',
    image: browser02,
    description: '智能识别下载链接，快速下载文件'
  },
  {
    title: '图片批量下载',
    image: browser03,
    description: '批量下载网页图片，高效便捷'
  }
]

interface CooperateInfo {
  name: string
  proName: string
  browserName: string
  url: string
  installParams: string
  icon: string
  logo: string
}

interface BrowserPluginDialogProps {
  visible?: boolean
  showMoreSupport?: boolean
  from?: string
  isManual?: boolean
}

const props = withDefaults(defineProps<BrowserPluginDialogProps>(), {
  visible: false,
  showMoreSupport: true,
  from: 'unknown',
  isManual: false
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
  refresh: []
}>()

// 使用基类的逻辑
const { overridePosition, resizeToFitContent } = usePositionMixinComponent();

// 重写控制位置基类非响应式数据
overridePosition({
  relatePos: PopUpTypes.RelatePosType.CenterParent,
  autoSize: true,
  show: true,
  selector: '.browser-config-guide-container',
})

const getBrowserPluginUrl = (browserName: string): string => {
  const urls: Record<string, string> = {
    'chrome': 'https://chrome.google.com/webstore/detail/thunder-download-support/ncennffkjdiamlpmcbajkmaiiiddgioo',
    '360se': 'https://ext.se.360.cn/webstore/detail/iiikneblakeefndgjndjnfjolkblgphj',
    'edge': 'https://microsoftedge.microsoft.com/addons/detail/thunder-download-support/jllpkdkcdjndhggodimiphkghogcpida'
  }
  return urls[browserName] || urls['chrome']
}

const browserList = ref<IMoreInfo[]>([])
const loading = ref(false)
const actionLoading = ref<string>('')

// 单例Promise实例，防止重复执行
// const singlePromise = new SinglePromise()

// 轮播图相关
const currentSlide = ref(0)
const carouselTimer = ref<number>()

const hasActiveBrowser = computed(() => {
  return browserList.value.some(browser => browser.install && browser.open)
})

// Dialog组件相关
const updateVisible = (value: boolean) => {
  emit('update:visible', value)
}

// 轮播图自动播放
const startCarousel = () => {
  if (carouselTabs.length === 0) return // 防止空数组导致的问题
  
  carouselTimer.value = setInterval(() => {
    currentSlide.value = (currentSlide.value + 1) % carouselTabs.length
    console.log('轮播切换到:', currentSlide.value, '总数:', carouselTabs.length)
  }, 5000) as unknown as number
}

const stopCarousel = () => {
  if (carouselTimer.value) {
    clearInterval(carouselTimer.value)
    carouselTimer.value = undefined
  }
}

const goToSlide = (index: number) => {
  if (index >= 0 && index < carouselTabs.length) { // 边界检查
    currentSlide.value = index
    stopCarousel()
    startCarousel()
  }
}

// 监听 visible 变化
watch(() => props.visible, async (visible) => {
  if (visible) {
    await loadBrowserData()
    await loadCooperateInfo()
    startCarousel()
  } else {
    stopCarousel()
  }
})

const loadBrowserData = async () => {
  loading.value = true
  try {
    
    const browserInfos = await BrowserHelperNs.getInfos(true)
    browserList.value = convertToBrowserList(browserInfos)
    
    console.log('浏览器数据加载完成:', browserList.value)
  } catch (error) {
    console.error('加载浏览器数据失败:', error)
    browserList.value = []
  } finally {
    loading.value = false
  }
}

const loadCooperateInfo = async () => {
  try {
    // 这里应该调用实际的API获取合作浏览器信息
    // const ret = await client.callServerFunction('GetRemoteGlobalConfigValue', 'main', 'BrowserExtCooperate', {})
    
    // 模拟数据，实际项目中需要替换为真实API调用
    const mockCooperateInfo = {
      name: '360ChromeX',
      proName: '360ChromeX.exe',
      browserName: '360极速浏览器X',
      url: 'https://down.360safe.com/cse/360csex_21.0.1032.0.exe',
      installParams: '--360chromesetup_pid=llqxl000004 --silent-install=3_1_1',
      icon: '',
      logo: require('@/common/assets/images/browser/logo-360.png')
    }
    
    // 检查是否已安装
    const isInstalled = await checkBrowserInstalled(mockCooperateInfo.browserName)
    
    // 标记合作浏览器
    browserList.value = browserList.value.map(item => {
      item.cooperate = item.exeName.toLowerCase() === mockCooperateInfo.name.toLowerCase()
      return item
    })
    
  } catch (error) {
    console.error('加载合作浏览器信息失败:', error)
  }
}

const checkBrowserInstalled = async (browserName: string): Promise<boolean> => {
  // 这里应该调用实际的API检查浏览器是否已安装
  // return await client.callServerFunction('IsBrowserInstalled', browserName)
  
  // 模拟检查逻辑
  return false
}

const convertToBrowserList = (browserInfos: Map<string, IBrowserInfo>): IMoreInfo[] => {
  const list: IMoreInfo[] = []
  
  browserInfos.forEach((info, name) => {
    if (info.browserExist) {
      list.push({
        icon: info.icon,
        name: info.browserTitle,
        default: name === 'chrome', // 简化逻辑，实际应该检查系统默认浏览器
        install: info.browserAddonInstall,
        open: info.browserAddonOpen,
        exeName: name,
        logo: info.logo,
        cooperate: false
      })
    }
  })
  
  return list
}

const handleBrowserAction = async (browser: IMoreInfo) => {
  // console.log('浏览器操作:', browser.name)
  // actionLoading.value = browser.exeName
  // try {
    
  //   // 实际的开启逻辑
  //   await openBrowserPlugin(browser)
    
  //   // 重新检测状态
  //   setTimeout(async () => {
  //     await loadBrowserData()
  //   }, 1000)
  // } catch (error) {
  //   console.error('浏览器操作失败:', error)
  // } finally {
  //   actionLoading.value = ''
  // }
  // '您尚未安装360安全浏览器，是否立即下载并安装？', '安装插件需要360安全浏览器',
  MessageBox.alert('敬请期待', '安装插件功能即将上线', {
    type: 'default',
    icon: 'xl-icon-general-details-m',
    showCancelButton: true,
    cancelButtonText: '取消',
    closeOnClickModal: true,
    closeOnPressEscape: true,
    modal: false,
    appendTo: '.browser-config-guide-container',
  })
  .then(() => {
    // 点击确认按钮
    
  })
  .catch(() => {
    // 关闭弹窗
  });
}

// 确认对话框相关
const showKillConfirm = async (): Promise<boolean> => {
  return confirm('Chrome浏览器正在运行，请关闭浏览器后重试。是否强制关闭？')
}

const showRetryConfirm = async (): Promise<boolean> => {
  return confirm('Chrome浏览器关闭失败，可尝试手动关闭浏览器后重试。是否重试？')
}

const showInstallConfirm = async (browserName: string): Promise<boolean> => {
  return confirm(`您当前未安装${browserName}，是否安装${browserName}并下载插件？`)
}

// Chrome扩展特殊处理
const installChromeExtension = async (processName: string, browserInfo: IBrowserInfo): Promise<{ success: boolean; canceled: boolean }> => {
  // 临时注释掉singlePromise相关代码
  // return singlePromise.run('chrome-addons', async (): Promise<{ success: boolean; canceled: boolean }> => {
    let result: { success: boolean; canceled: boolean } = { success: false, canceled: false }
    
    try {
      // 检查Chrome是否在运行
      // let findResult = await findProcess(processName)
      // if (findResult?.length) {
      //   if (!(await showKillConfirm())) {
      //     result.canceled = true
      //     return result
      //   }

      //   // 尝试关闭Chrome
      //   for (let i = 0; i < 3; i++) {
      //     await killRunningChrome()
      //     await new Promise(resolve => setTimeout(resolve, 1000))
      //     findResult = await findProcess(processName)
      //     if (!findResult?.length) {
      //       break
      //     }
      //   }

      //   if (findResult?.length) {
      //     await showRetryConfirm()
      //     return result
      //   }
      // }

      // // 重置Chrome扩展配置
      // await resetSecurePreferencesSetting(browserInfo.browserAddonInstall, '\\Google\\Chrome')
      
      console.log('Chrome扩展处理逻辑待实现')
      result.success = true
    } catch (error) {
      console.error('安装Chrome扩展失败:', error)
    }
    
    return result
  // })
}

// 直接修改浏览器配置文件开启插件
const openAddonsByConfig = async (configPath: string, browserName: string): Promise<boolean> => {
  try {
    // 这里需要根据thunder_2025的架构实现文件读写
    // 临时返回true，需要实际实现
    console.log('通过配置文件开启插件:', configPath, browserName)
    return true
  } catch (error) {
    console.error('通过配置文件开启插件失败:', error)
    return false
  }
}

const openBrowserPlugin = async (browser: IMoreInfo) => {
  console.log('开启浏览器插件:', browser.exeName)
  
  try {
    // 获取浏览器信息
    const browserInfos = await BrowserHelperNs.getInfos()
    const browserInfo = browserInfos.get(browser.exeName.toLowerCase())
    
    if (!browserInfo?.browserExist) {
      console.error('浏览器不存在:', browser.exeName)
      return
    }

    let cmdLine = browserInfo.installCmdLine
    let isUseCmd = true
    const processName = browser.exeName + '.exe'

    // Chrome浏览器特殊处理
    if (processName === 'chrome.exe') {
      const result = await installChromeExtension(processName, browserInfo)
      if (result.success || result.canceled) {
        cmdLine = `https://misc-xl9-ssl.xunlei.com/pages/dist/#/browser-plugin/chrome?pid=unknown`
      } else {
        await showRetryConfirm()
        return
      }
    } else if (browserInfo.browserAddonInstall && !browserInfo.browserAddonOpen) {
      // 插件已安装但未开启
      // 浏览器未运行，直接修改配置文件
      isUseCmd = false
      await openAddonsByConfig(browserInfo.browserAddonConfigPath, browser.exeName)
    }

    if (isUseCmd) {
      // 使用命令行或网页方式开启
      // 这里需要根据thunder_2025的架构实现浏览器启动
      console.log('使用命令行开启:', browserInfo.browserPath, cmdLine)
      // window.open(cmdLine, '_blank') // 临时方案
    } else {
      // 已通过配置文件开启，显示成功提示
      console.log('已通过配置文件开启插件')
    }
  } catch (error) {
    console.error('开启浏览器插件失败:', error)
  }
}

const installCooperateBrowser = async (cooperate: CooperateInfo) => {
  console.log('安装合作浏览器:', cooperate.browserName)
  
  try {
    if (!(await showInstallConfirm(cooperate.browserName))) {
      return false
    }
    
    // 这里需要根据thunder_2025的架构实现下载和安装
    // 1. 下载安装包
    console.log('下载安装包:', cooperate.url)
    
    // 2. 执行安装
    console.log('执行安装:', cooperate.installParams)
    
    // 模拟安装过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 3. 检查安装结果
    const isInstalled = await checkBrowserInstalled(cooperate.browserName)
    if (isInstalled) {
      console.log('安装成功:', cooperate.browserName)
      return true
    } else {
      console.error('安装失败:', cooperate.browserName)
      return false
    }
  } catch (error) {
    console.error('安装合作浏览器失败:', error)
    return false
  }
}


const handleClose = () => {
  const currentWindow = PopUpNS.getCurrentWindow()
  currentWindow.close()
}


const handleRefresh = async () => {
  await loadBrowserData()
  await loadCooperateInfo()
  emit('refresh')
}

onMounted(() => {
  loadBrowserData()
  loadCooperateInfo()
  startCarousel()
})

onUnmounted(() => {
  stopCarousel()
})

</script>

<style lang="scss" scoped>

.browser-config-guide {
  &-container {
    width: 540px;
    height: 454px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 64px;
    padding: 24px 24px 0 24px;
    span {
      color: var(--font-font-1, #272E3B);
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
    }

    i {
      color: var(--font-font-3);
      font-size: 16px;
      cursor: pointer;
    }
  }
}

// 帮助提示样式
.browser-help-tooltip-tip-content {
  width: 265px;
  height: 130px;
}
.browser-help-tooltip {
  width: 235px;
  margin: 15px 20px 15px 10px;
  color: var(--font-font-2, #4E5769);
  overflow: hidden;
  white-space: wrap;
  word-break: break-all;
  font-size: 12px;
  line-height: 20px;
  li {
    list-style: disc;
    list-style-position: inside;
    /* 让li标签第二行文字与第一行左对齐，而不是与圆点对齐 */
    text-indent: -1.5em;
    padding-left: 1.5em;
    
  }
}
.browser-config-guide-body {
  width: 540px;
  height: auto;
  padding: 0 24px 24px 24px;
}

// 轮播图样式 - 修复布局
.carousel-section {
  margin: 12px 0;
  
  // Tab标签栏 - 移到外面
  .carousel-indicators {
    height: 40px;
    display: flex;
    margin-bottom: 8px;
    
    ul {
      display: flex;
      justify-content: flex-start;
      width: 100%;
      margin: 0;
      padding: 0;
      list-style: none;
      gap: 40px;
    }
    
    .indicator {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      border: none;
      background: transparent;
      cursor: pointer;
      font-size: 14px;
      color: var(--font-font-2, #4E5769);
      transition: all 0.3s ease;
      white-space: nowrap;
      line-height: 1;
      
      &.active {
        color: var(--primary-primary-font-default, #226DF5);
        
        &::after {
          position: absolute;
          bottom: 0;
          left: 0;
          display: block;
          width: 100%;
          height: 2px;
          background: var(--primary-primary-font-default, #226DF5);
          content: "";
          border-radius: 1px;
        }
      }
    }
  }
  
  // 轮播容器
  .carousel-container {
    position: relative;
    width: 100%;
    height: 160px;
    background: var(--background-bg-2, #F2F3F5);
    border-radius: 6px;
    overflow: hidden; // 隐藏超出范围的内容
  }
  
  // 轮播内容区 - 原始样式
  .carousel-track {
    display: flex;
    width: 300%; // 固定宽度为3倍，对应3个slide
    height: 100%;
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .carousel-slide {
    width: 33.333333%; // 精确的1/3宽度
    height: 160px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-shrink: 0; // 防止压缩
    border-radius: var(--border-radius-L, 12px);
    background: var(--background-background-upload, rgba(34, 109, 245, 0.08));
    overflow: hidden;

    img {
      width: 720px;
      height: auto;
    }
  }
}

@keyframes slideIn {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 浏览器列表样式 - 原始xly-dialog-support样式
.browser-list-section {
  width: 100%;
  margin-top: 8px;
 
}

.browser-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.browser-item {
  width: 100%;
  height: 40px;
  position: relative;
  display: flex;
  align-items: center;
}

// 移除多余的 browser-icon 样式，改用原始的 browser-support-icon

.browser-info {
  flex: 1;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  .browser-name {
    font-size: 14px;
    color: var(--font-font-1, #272E3B);
    display: inline-flex;
    align-items: center;
  }
  .browser-desc {
    position: relative;
    margin-left: 6px;
    color: var(--font-font-3, #86909C);
    font-size: 12px;
  }
}

// 图标样式
.browser-support-icon {
  margin-right: 8px;
  width: 24px;
  height: 24px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.browser-tags {
  display: inline-flex;
  gap: 4px;
}

.status-tag {
  width: auto;
  height: 20px;
  color: #fff;
  font-size: 12px;
  line-height: 20px;
  border-radius: var(--radius_4, 4px);
  padding: 0 6px;
  
  &.default {
    background: var(--primary-primary-background-default, #E8F4FF);
    color: var(--button-button-lead-font-default-2, #226DF5);
  }

  &.recommend {
    background: var(--button-button-warn-default, #FFECE8);
    color: var(--auxiliary-auxiliary-orange, #FF8024);
  }
}

.browser-action {
  width: 76px;
  height: 32px;
  position: absolute;
  right: 0;
  
  :global(.button) {
    width: 78px;
    height: 32px;
  }
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: var(--font-font-2, #4E5769);
  
  p {
    font-size: 14px;
    margin: 8px 0;
    color: var(--font-font-2, #4E5769);
  }
}

// 对话框帮助按钮样式
.dialog-help {
  position: absolute;
  top: 8px;
  right: 42px;
  line-height: 16px;
  cursor: pointer;
  
  i {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 28px;
    height: 28px;
    color: var(--td-font-3, #86909C);
    border-radius: var(--td-border-radius-m, 6px);
    cursor: pointer;
    
    &:hover {
      color: var(--td-font-1, #1D2129);
      background: var(--td-fill-3, #F7F8FA);
    }
  }
}
</style> 