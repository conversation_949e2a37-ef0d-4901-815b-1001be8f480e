/**
 * @description: 登录本地数据管理
 */
import { join } from 'path';
import { SinglePromise } from '@root/common/single-promise';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { ToolsUtilitiesAWNS } from '@root/common/tools-utilities';
import { ILastLoginData, ILocalData, ILoginHistory, IOauthLogoutInfo } from '@root/common/account/impl/login-options-define';

export class LoginDataHelper {
  // 由于必须16位，所以拼接成这样了，用来加解密buffer
  private _inited: boolean = false; // tslint:disable-line
  private _key: string = 'XUNLEIACCOUNT123'; // tslint:disable-line
  private _loginDataDir: string ;// tslint:disable-line
  private _loginDataPath: string; // tslint:disable-line
  private _loginHistoryList: ILoginHistory[]; // tslint:disable-line
  private _lastLoginData: ILastLoginData; // tslint:disable-line
  private _sdkData: { [key: string]: string }; // tslint:disable-line
  private _oAuthLogoutInfo: IOauthLogoutInfo | null = null; // tslint:disable-line
  private _singlePromise: SinglePromise; // tslint:disable-line

  constructor(loginDataDir: string) {
    this._loginDataDir = loginDataDir;
    this._lastLoginData = null;
    this._loginHistoryList = [];
    this._loginDataPath = join(this._loginDataDir, 'account.dat');
    this._sdkData = {};
    this._singlePromise = new SinglePromise();
  }

  async init(): Promise<void> {
    if (!this._inited) {
      return this._singlePromise.run('init-local-data', async () => {
        if (!await FileSystemAWNS.dirExistsAW(this._loginDataDir)) {
          await FileSystemAWNS.mkdirsAW(this._loginDataDir);
        }
        await this.loadFromFile();
        this._inited = true;
      });
    }
  }

  get lastLoginData(): ILastLoginData {
    return this._lastLoginData;
  }

  set lastLoginData(data: ILastLoginData) {
    this._lastLoginData = data;
  }

  get loginHistoryList(): ILoginHistory[] {
    return this._loginHistoryList;
  }

  get oAuthLogoutInfo(): IOauthLogoutInfo {
    return this._oAuthLogoutInfo;
  }

  set oAuthLogoutInfo(info: IOauthLogoutInfo) {
    this._oAuthLogoutInfo = info;
  }

  async getSDKDataItem(key: string): Promise<string | null> {
    await this.init();
    return this._sdkData[key] ? this._sdkData[key] : null;
  }

  async setSDKDataItem(key: string, value: string): Promise<void> {
    await this.init();
    this._sdkData[key] = value;
    await this.saveData();
  }

  async removeSDKDataItem(key: string): Promise<void> {
    delete this._sdkData[key];
    await this.saveData();
  }

  // 为了兼容版本回退，不采用同一个文件
  async loadFromFile(): Promise<void> {
    let localData: ILocalData = {
      version: '1.0.0',
      sdkData: {},
      sdkDataVersion: '1.0.1'
    };
    do {
      let fileExist: boolean = await FileSystemAWNS.existsAW(this._loginDataPath);
      if (!fileExist) {
        break;
      }

      let dataBuf: Buffer = await FileSystemAWNS.readFileAW(this._loginDataPath);
      if (!dataBuf) {
        break;
      }
      const decryptBufferData: Buffer = ToolsUtilitiesAWNS.decryptBuffer(dataBuf, this._key);
      if (!decryptBufferData) {
        break;
      }
      let bufStr: string = decryptBufferData.toString();
      let data: any = null;
      try {
        data = JSON.parse(bufStr);
      } catch (error) {
        data = null;
      }

      if (data?.version === '1.0.0') {
        localData = data;
        this._loginHistoryList = localData.loginHistoryList || [];
        this._lastLoginData = localData.lastLoginData || null;
        this._sdkData = localData.sdkData;
      }
    } while (0);
  }

  async saveData(): Promise<boolean> {
    const localData: ILocalData = {
      version: '1.0.0',
      sdkData: { ...this._sdkData },
      sdkDataVersion: '1.0.1'
    };
    if (this.loginHistoryList) {
      localData.loginHistoryList = [...this.loginHistoryList];
    }
    if (this.lastLoginData) {
      localData.lastLoginData = { ...this.lastLoginData };
    }
    if (this.oAuthLogoutInfo) {
      localData.oauthLogoutInfo = { ...this.oAuthLogoutInfo };
    }
    const str: string = JSON.stringify(localData);
    const buffer: Buffer = Buffer.from(str);
    const encryptBuffer: Buffer = ToolsUtilitiesAWNS.encryptBuffer(buffer, this._key);
    return FileSystemAWNS.writeFileAW(this._loginDataPath, encryptBuffer);
  }

  getLoginHistory(userName: string): ILoginHistory {
    let result: ILoginHistory = null;
    for (let index: number = 0; index < this._loginHistoryList.length; index++) {
      let item: ILoginHistory = this._loginHistoryList[index];
      if (item?.userName === userName) {
        result = item;
        break;
      }
    }
    return { ...result };
  }

  insertLoginHistory(loginItem: ILoginHistory, index: number): void {
    if (this._isloginHistoryValid(loginItem)) {
      this._loginHistoryList.splice(index ?? 0, 0, loginItem);
      this._loginHistoryList = this._loginHistoryList;
    }
  }

  // backtrack: 删除历史记录后是否需要回溯上一次自动登录
  deleteLoginHistory(deletedItem: ILoginHistory, backtrack: boolean = false): boolean {
    let deleteDone: boolean = false;
    for (let index: number = 0; index < this._loginHistoryList.length; index++) {
      let item: ILoginHistory = this._loginHistoryList[index];
      if (item?.userName === deletedItem?.userName) {
        this._loginHistoryList.splice(index, 1);
        deleteDone = true;
        break;
      }
    }
    if (backtrack) {
      if (this._lastLoginData && deletedItem?.userId === this._lastLoginData.userId) {
        this._lastLoginData = null;
        if (this._loginHistoryList && this._loginHistoryList[0]?.autoLogin) {
          this._lastLoginData = {
            userName: this._loginHistoryList[0].userName,
            autoLogin: true,
            userId: this._loginHistoryList[0].userId
          };
        }
      }
    }
    return deleteDone;
  }

  deleteAllLoginHistory(): void {
    for (let index: number = 0; index < this._loginHistoryList.length; index++) {
      if (this._lastLoginData?.userId === this._loginHistoryList[index]?.userId) {
        this._lastLoginData = null;
        break;
      }
    }
    this._loginHistoryList = [];
  }

  private _isloginHistoryValid(loginHistory: ILoginHistory): boolean { // tslint:disable-line
    let isValid: boolean = false;
    do {
      if (!loginHistory) {
        break;
      }
      if (
        typeof loginHistory.userName !== 'string' ||
        loginHistory.userName.length <= 0
      ) {
        break;
      }
      // if (typeof loginHistory.autoLogin !== 'boolean') {
      //   break;
      // }
      // if (
      //   loginHistory.loginKey &&
      //   (typeof loginHistory.loginKey !== 'string' ||
      //     loginHistory.loginKey.length <= 0)
      // ) {
      //   break;
      // }
      isValid = true;
    } while (false);

    return isValid;
  }
}
