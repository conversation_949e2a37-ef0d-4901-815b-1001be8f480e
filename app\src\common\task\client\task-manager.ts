import { CallApiProxyImplWithIpcClient } from '../call-api-impl';
import * as BaseType from '../base';
import { Task } from './task'
import { EventEmitter } from 'events';
import { DkHelper } from './dk-helper';
import { CategoryManager } from './category-manager';
export class TaskManager {
    private static instance: TaskManager;
    private apiProxy: CallApiProxyImplWithIpcClient = new CallApiProxyImplWithIpcClient();
    private eventContainor: EventEmitter = new EventEmitter();
    private tpPeerid: string = '';
    private categoryManager = new CategoryManager();

    public async init(): Promise<void> {
        this.apiProxy!.AttachServerEvent('TaskManagerTaskStatusChange', (taskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
            let task = new Task(this.apiProxy!, taskId);
            this.eventContainor.emit('TaskManagerTaskStatusChange', task, eOld, eNew);
        });
        this.apiProxy!.AttachServerEvent('TaskManagerTaskDetailChange', (taskId: number, flags: BaseType.TaskDetailChangedFlags) => {
            let task = new Task(this.apiProxy!, taskId);
            this.eventContainor.emit('TaskManagerTaskDetailChange', task, flags);
        });
        this.apiProxy!.AttachServerEvent('TaskManagerGroupSubTaskDetailChange', (taskId: number, groupTaskId: number, flags: BaseType.TaskDetailChangedFlags) => {
            let task = new Task(this.apiProxy!, taskId);
            let groupTask = new Task(this.apiProxy!, groupTaskId);
            this.eventContainor.emit('TaskManagerGroupSubTaskDetailChange', task, groupTask, flags);
        });
        this.apiProxy!.AttachServerEvent('TaskManagerGroupSubTaskStatusChange', (taskId: number, groupTaskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
            let task = new Task(this.apiProxy!, taskId);
            let groupTask = new Task(this.apiProxy!, groupTaskId);
            this.eventContainor.emit('TaskManagerGroupSubTaskStatusChange', task, groupTask, eOld, eNew);
        });
        this.apiProxy!.AttachServerEvent('TaskManagerTaskDeleted', (taskId: number, groupTaskId: number) => {
            this.eventContainor.emit('TaskManagerTaskDeleted', taskId, groupTaskId);
        });
        this.apiProxy!.AttachServerEvent('TaskManagerBtSubTaskDetailChange', (taskId: number, fileIndex: number, flags: BaseType.BtSubFileDetailChangeFlags) => {
            let task = new Task(this.apiProxy!, taskId);
            this.eventContainor.emit('TaskManagerBtSubTaskDetailChange', task, fileIndex, flags);
        });
        this.apiProxy!.AttachServerEvent('TaskManagerBtSubTaskStatusChange', (taskId: number, fileIndex: number, eOld: BaseType.BtSubFileStatus, eNew: BaseType.BtSubFileStatus) => {
            let task = new Task(this.apiProxy!, taskId);
            this.eventContainor.emit('TaskManagerBtSubTaskStatusChange', task, fileIndex, eOld, eNew);
        });
        DkHelper.init(this.apiProxy);
        this.categoryManager.init(this.apiProxy);
    }

    public static GetInstance(): TaskManager {
        if (!TaskManager.instance) {
            if (global.TaskManagerClientInstance) {
                TaskManager.instance = global.TaskManagerClientInstance;
            } else {
                TaskManager.instance = new TaskManager();
                global.TaskManagerClientInstance = TaskManager.instance;
            }
        }
        return TaskManager.instance;
    }

    
    public getCategoryManager(): CategoryManager {
        return this.categoryManager;
    }

    public async findTaskById(taskId: number): Promise<Task | undefined> {
        // TODO 
        let task = new Task(this.apiProxy!, taskId);
        return task;
    }

    public async findRepeatTask(infos: BaseType.NewTaskSet[]): Promise<BaseType.FindRepeatTaskResultItem[]> {
        let ret = await this.apiProxy!.CallApi('TaskManagerFindRepeatTask', infos);
        if (ret.bSucc) {
            return ret.result as BaseType.FindRepeatTaskResultItem[];
        }
        return [];
    }

    public async checkRepeatAndCreateTask(info: BaseType.NewTaskSet): Promise<{task: Task|undefined, exist: boolean}> {
        let ret = await this.apiProxy!.CallApi('TaskManagerCheckRepeatAndCreateTask', info);
        if (ret.bSucc) {
            return {task: (ret.result as any).taskId > 0 ? new Task(this.apiProxy!, (ret.result as any).taskId) : undefined, exist: (ret.result as any).exist};
        }
        return {task: undefined, exist: false};
    }

    public async createTask(info: BaseType.NewTaskSet): Promise<Task | undefined> {
        let ret = await this.apiProxy!.CallApi('TaskManagerCreateTask', info);
        if (ret.bSucc) {
            let task = new Task(this.apiProxy!, ret.result);
            return task;
        }
        return undefined;
    }

    public async createGroupTask(taskInfo: BaseType.NewTaskInfo, groupInfoList: BaseType.NewGroupTaskInfo[]): Promise<Task | undefined> {
        let ret = await this.apiProxy!.CallApi('TaskManagerCreateGroupTask', taskInfo, groupInfoList);
        if (ret.bSucc) {
            let task = new Task(this.apiProxy!, ret.result);
            return task;
        }
        return undefined;
    }

    public async createP2spTask(taskInfo: BaseType.NewTaskInfo, p2spInfo: BaseType.NewP2spTaskInfo): Promise<Task | undefined> {
        let ret = await this.apiProxy!.CallApi('TaskManagerCreateP2spTask', taskInfo, p2spInfo);
        if (ret.bSucc) {
            let task = new Task(this.apiProxy!, ret.result);
            return task;
        }
        return undefined;
    }

    public updateMaxDownloadTaskCount(n: number) {
        this.apiProxy!.CallApi('TaskManagerUpdateMaxDownloadTaskCount', n);
    }

    public setGlobalExtInfo(info: string, append: boolean) {
        this.apiProxy!.CallApi('TaskManagerSetGlobalExtInfo', info, append);
    }

    public async getTpPeerId(): Promise<string> {
        if (this.tpPeerid.length > 0) {
            return this.tpPeerid;
        }

        let ret = await this.apiProxy!.CallApi('TaskManagerGetTpPeerId');
        if (ret.bSucc) {
            return ret.result as string;
        }
        return '';
    }

    public setUserInfo(userId: string, jumpKey: string) {
        this.apiProxy!.CallApi('TaskManagerSetUserInfo', userId, jumpKey);
    }

    batchStartTasks(taskIds: number[]) {
        this.apiProxy!.CallApi('TaskManagerBatchStartTasks', taskIds);
    }

    batchStopTasks(taskIds: number[], r: BaseType.TaskStopReason) {
        this.apiProxy!.CallApi('TaskManagerBatchStopTasks', taskIds, r);
    }

    batchDeleteTasks(taskIds: number[], deleteFile: boolean) {
        this.apiProxy!.CallApi('TaskManagerBatchDeleteTasks', taskIds, deleteFile);
    }

    batchRecycleTasks(taskIds: number[]) {
        this.apiProxy!.CallApi('TaskManagerBatchRecycleTasks', taskIds);
    }

    public attachTaskDetailChangeEvent(cb: (task: Task, flags: BaseType.TaskDetailChangedFlags) => void): void {
        this.eventContainor.on('TaskManagerTaskDetailChange', cb);
    }

    public detachTaskDetailChangeEvent(cb: (task: Task, flags: BaseType.TaskDetailChangedFlags) => void): void {
        this.eventContainor.off('TaskManagerTaskDetailChange', cb);
    }

    public attachTaskStatusChangeEvent(cb: (task: Task, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => void): void {
        this.eventContainor.on('TaskManagerTaskStatusChange', cb);
    }

    public detachTaskStatusChangeEvent(cb: (task: Task, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => void): void {
        this.eventContainor.off('TaskManagerTaskStatusChange', cb);
    }

    // 任务组子任务详情变化
    public attachGroupSubTaskDetailChangeEvent(cb: (task: Task, groupTaskId: number, flags: BaseType.TaskDetailChangedFlags) => void): void {
        this.eventContainor.on('TaskManagerGroupSubTaskDetailChange', cb);
    }

    public detachGroupSubTaskDetailChangeEvent(cb: (task: Task, groupTaskId: number, flags: BaseType.TaskDetailChangedFlags) => void): void {
        this.eventContainor.off('TaskManagerGroupSubTaskDetailChange', cb);
    }

    // 任务组子任务状态变化
    public attachGroupSubTaskStatusChangeEvent(cb: (task: Task, groupTaskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => void): void {
        this.eventContainor.on('TaskManagerGroupSubTaskStatusChange', cb);
    }

    public detachGroupSubTaskStatusChangeEvent(cb: (task: Task, groupTaskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => void): void {
        this.eventContainor.off('TaskManagerGroupSubTaskStatusChange', cb);
    }

    public attachTaskDeletedEvent(cb: (taskId: number, groupTaskId: number) => void): void {
        this.eventContainor.on('TaskManagerTaskDeleted', cb);
    }

    public detachTaskDeletedEvent(cb: (taskId: number) => void) {
        this.eventContainor.off('TaskManagerTaskDeleted', cb);
    }

    public attachBtSubTaskDetailChangeEvent(cb: (task: Task, fileIndex: number, flags: BaseType.BtSubFileDetailChangeFlags) => void): void {
        this.eventContainor.on('TaskManagerBtSubTaskDetailChange', cb);
    }

    public detachBtSubTaskDetailChangeEvent(cb: (task: Task, fileIndex: number, flags: BaseType.BtSubFileDetailChangeFlags) => void) {
        this.eventContainor.on('TaskManagerBtSubTaskDetailChange', cb);
    }

    public attachBtSubTaskStatusChangeEvent(cb: (taskId: number, fileIndex: number, eOld: BaseType.BtSubFileStatus, eNew: BaseType.BtSubFileStatus) => void): void {
        this.eventContainor.on('TaskManagerBtSubTaskStatusChange', cb);
    }

    public detachBtSubTaskStatusChangeEvent(cb: (taskId: number, fileIndex: number, eOld: BaseType.BtSubFileStatus, eNew: BaseType.BtSubFileStatus) => void) {
        this.eventContainor.on('TaskManagerBtSubTaskStatusChange', cb);
    }

    public async setProxy(host: string,
                          port: number,
                          userName: string,
                          passWord: string,
                          proxyType: BaseType.ProxyType) {
        this.apiProxy!.CallApi('TaskManagerSetProxy', host, port, userName, passWord, proxyType);
    }
}