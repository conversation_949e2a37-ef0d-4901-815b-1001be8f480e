[2025-08-12 16:15:52.733][27544:1356][info][AplayerOperationWin::AsyncStartup]: wnd=0x70864,strId=parent (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\player_control\src\AplayerOperationWin.cpp:79)
[2025-08-12 16:15:52.734][27544:1356][info][XLIPCServer::Start]: default loop=0x7ff9e34ba240,out=0x7ff944672318 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\player_control\src\protocol\xlipc_server.cpp:39)
[2025-08-12 16:15:52.734][27544:1356][info][AplayerOperationWin::AsyncStartup]: wnd=0xc092c,strId=float (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\player_control\src\AplayerOperationWin.cpp:79)
[2025-08-12 16:15:52.769][27544:1356][info][AplayerOperationWin::AsyncStartupClient]: AplayerOperationWin AsyncStartupClient param=--server-id=XmplitePlayer1754986552732 --client-id=XmpPlayerAplayer1754986552732 --process-id=27544, ret=42 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\player_control\src\AplayerOperationWin.cpp:227)
[2025-08-12 16:15:52.784][27544:1356][info][XLIPCServer::OnPipeConnectionCallback]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\player_control\src\protocol\xlipc_server.cpp:306)
[2025-08-12 16:15:52.784][27544:1356][info][XLIPCServer::OnPipeConnection]: uv_accept success, uv_read_start. (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\player_control\src\protocol\xlipc_server.cpp:245)
[2025-08-12 16:15:52.784][27544:1356][error][AplayerOperationWin::OnAcceptSession]: new connection (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\player_control\src\AplayerOperationWin.cpp:46)
[2025-08-12 16:15:52.784][27544:1356][info][AplayerOperationWin::AsyncOpen]: AplayerOperationWin async open client startup (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\player_control\src\AplayerOperationWin.cpp:143)
[2025-08-12 16:15:52.790][27544:1356][info][AplayerOperationWin::AsyncOpen]: AplayerOperationWin async create player return (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\player_control\src\AplayerOperationWin.cpp:154)
