/**
 * @description: 获取灰度配置模块信息
 */
import * as os from 'os';
import * as path from 'path';
import * as crypto from 'crypto';
import EventEmitter from 'events';
import { config } from '@root/common/config/config';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { ThunderHelper } from '@root/common/thunder-helper';
import { ConfigInitState } from '@root/common/config/types';
import { GetProfilesPath } from '@root/common/xxx-node-path';
import { AccountHelper } from '@root/common/account/client/accountHelper';

class ConfigRemoteGlobalServer extends EventEmitter {
  private configJsonData: any = null;
  private token: number = 0;
  private initState_: ConfigInitState = ConfigInitState.None;

  private async getBaseConfigUrl(): Promise<string> {
    const testServ: boolean = await config.getValue('ConfigRemoteGlobal', 'TestServer', false) as boolean;
    if (testServ) {
      // 这里需配置host *************  alpha-conf-m-ssl.xunlei.com
      return 'http://alpha-conf-m-ssl.xunlei.com/external/e095b1fb-6807-11e9-80e6-0242ac160003?';
    } else {
      return 'https://conf-m-ssl.xunlei.com/external/e095b1fb-6807-11e9-80e6-0242ac160003?';
    }
  }

  private async getUpdateInterval(): Promise<number> {
    const interval: number = await config.getValue('ConfigRemoteGlobal', 'Interval', 24 * 60 * 60 * 1000) as number;
    return interval;
  }

  private getVersionCode(vers: string[]): string {
    const majorNum = vers[0];
    const minorNum = vers[1];
    const revisionNum = vers[2];
    const buildNum = vers[3];

    let versionCode = buildNum;
    if (majorNum && minorNum && revisionNum && buildNum) {
      versionCode = majorNum.padStart(2, '0') + minorNum.padStart(2, '0') + revisionNum.padStart(2, '0') + buildNum.padStart(4, '0');
    }
    return versionCode;
  }

  private async getConfigUrl(): Promise<string> {
    let url: string = await this.getBaseConfigUrl();
    const params: { [key: string]: string } = {};
    if (await AccountHelper.getInstance().isSignIn()) {
      let userInfo: any = await AccountHelper.getInstance().getUserInfo();
      params['user-id'] = userInfo.id!;
    }
    const thunderVersion: string = ThunderHelper.getFileVersion(process.execPath);
    if (thunderVersion && thunderVersion !== '') {
      params['version-name'] = thunderVersion;
      const versions: string[] = thunderVersion.split('.');
      if (versions && versions.length === 4) {
        params['version-code'] = this.getVersionCode(versions);
      }
    }
    let peerId: string = ThunderHelper.getPeerId();
    if (peerId && peerId !== '') {
      params['device-id'] = peerId;
      const sha256: crypto.Hash = crypto.createHash('sha256');
      sha256.update(peerId);
      const hex: string = sha256.digest('hex');
      if (hex) {
        peerId = hex.toUpperCase();
      }
      params['peer-id'] = peerId;
    }
    const channelId: string = ThunderHelper.getInstallChannel();
    if (channelId && channelId !== '') {
      params['channel'] = channelId;
    }
    params['app-type'] = 'pc';
    const date: Date = new Date();
    const timezoneOffset: number = date.getTimezoneOffset();
    params['time-zome'] = (timezoneOffset / 60).toString();
    params['language'] = window.navigator.language;
    const osVer: string = os.release();
    params['os'] = osVer;

    let firstParam: boolean = true;
    for (const key in params) {
      if (firstParam) {
        firstParam = false;
      } else {
        url += '&';
      }
      const value: string = params[key];
      url += key + '=' + value;
    }
    // logger.information('getConfigUrl url:', url);
    return url;
  }

  private async requestConfig(): Promise<any> {
    let ret: any = null;
    const cfg_path: string = path.join(GetProfilesPath(), 'global_remote_config.json');
    try {
      const url: string = await this.getConfigUrl();

      const response = await fetch(url);
      if (response?.status === 200) {
        const data = await response.json();
        if (data?.result === 'ok' && data?.values) {
          ret = data.values;
          FileSystemAWNS.writeFileAW(cfg_path, JSON.stringify(ret));
        }
        // logger.information('url: ', url, ', response:', data);
      } else {
        // logger.information('url: ', url, ', response:', response);
      }
    } catch (error) {
      // logger.warning(error);
    }

    if (!ret) {
      try{
        ret = JSON.parse((await FileSystemAWNS.readFileAW(cfg_path)).toString());
      } catch (error) {

      }
    }
    return ret;
  }

  private async updateConfig(): Promise<void> {
    const token: number = ++this.token;
    const data: any = await this.requestConfig();
    if (token === this.token) {
      this.configJsonData = data;
    }
  }

  public async init(): Promise<void> {
    do {
      if (this.initState_ === ConfigInitState.Done) {
        break;
      }

      if (this.initState_ === ConfigInitState.Ing) {
        return new Promise<void>(
          (resolve: (value: void) => void): void => {
            this.once('OnRemoteGlobalConfigLoaded', () => {
              resolve();
            });
          }
        );
      }

      this.initState_ = ConfigInitState.Ing;
      const token: number = this.token;
      const data: any = await this.requestConfig();
      if (token === this.token) {
        this.configJsonData = data;
      }

      const interval: number = await this.getUpdateInterval();
      if (interval > 0) {
        setInterval(() => {
          // logger.information('time out updateConfig');
          this.updateConfig().catch();
        }, interval);
      }
      this.initState_ = ConfigInitState.Done;
      this.emit('OnRemoteGlobalConfigLoaded');
    } while (0);
  }

  public async isConfigExists(groupName: string, keyName?: string): Promise<boolean> {
    await this.init();
    let exists: boolean = false;
    do {
      if (this.configJsonData === null || this.configJsonData === undefined) {
        break;
      }
      if (keyName === null || keyName === undefined || keyName === '') {
        if (this.configJsonData[groupName] !== null && this.configJsonData[groupName] !== undefined) {
          exists = true;
        }
      } else {
        if (
          this.configJsonData[groupName] !== null &&
          this.configJsonData[groupName] !== undefined &&
          this.configJsonData[groupName][keyName] !== null &&
          this.configJsonData[groupName][keyName] !== undefined
        ) {
          exists = true;
        }
      }
    } while (false);
    return exists;
  }

  // groupName 为配置项中的每项key值，keyName为对应项中的某个子项
  public async getConfigValue(groupName: string, keyName?: string, defaultValue?: any): Promise<any> {
    await this.init();
    let data: any = defaultValue;
    do {
      if (this.configJsonData === null || this.configJsonData === undefined) {
        break;
      }
      if (keyName === null || keyName === undefined || keyName === '') {
        if (this.configJsonData[groupName] !== null && this.configJsonData[groupName] !== undefined) {
          data = this.configJsonData[groupName];
        }
      } else {
        if (
          this.configJsonData[groupName] !== null &&
          this.configJsonData[groupName] !== undefined &&
          this.configJsonData[groupName][keyName] !== null &&
          this.configJsonData[groupName][keyName] !== undefined
        ) {
          data = this.configJsonData[groupName][keyName];
        }
      }
    } while (false);
    return data;
  }
}

const configRemoteGlobal: ConfigRemoteGlobalServer = new ConfigRemoteGlobalServer();
export default configRemoteGlobal;