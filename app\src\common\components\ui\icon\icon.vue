<template>
  <svg v-if="svg" class="td-icon-svg" :class="`td-icon-svg-${type}`" aria-hidden="true">
    <use xmlns:xlink="http://www.w3.org/1999/xlink" :xlink:href="`#td-icon-svg-${type}`"></use>
  </svg>
  <i v-else :class="`td-icon-${type}`"></i>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  props: {
    type: {
      type: String,
      required: true
    },
    svg: {
      type: Boolean,
      default: false
    }
  }
})
</script>

<style>
@import '@root/main-renderer/src/assets/css/xl-icon.scss';
@import './icon.scss';
</style>
