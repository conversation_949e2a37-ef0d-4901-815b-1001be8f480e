<script setup lang="ts">
import { XmpPlayer } from './xmp-player/index'

function onPlayerShow() {
  console.log('onPlayerShow')
}
function onPlayerHide() {
  console.log('onPlayerHide')
}
function onPlayerInited(isInitialMedia: boolean) {
  console.log('onPlayerInited', isInitialMedia)
}

</script>

<template>
  <div style="width: 100%; height: 100%;">
    <XmpPlayer playerType="aplayer" @show="onPlayerShow" @hide="onPlayerHide" @inited="onPlayerInited" />
  </div>
  <!--  -->
</template>

<style scoped lang="scss"></style>
