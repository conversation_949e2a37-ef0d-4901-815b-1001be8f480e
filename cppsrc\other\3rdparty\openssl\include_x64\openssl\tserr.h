/*
 * Generated by util/mkerr.pl DO NOT EDIT
 * Copyright 1995-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OPENSSL_TSERR_H
# define OPENSSL_TSERR_H
# pragma once

# include <openssl/opensslconf.h>
# include <openssl/symhacks.h>
# include <openssl/cryptoerr_legacy.h>


# ifndef OPENSSL_NO_TS


/*
 * TS reason codes.
 */
#  define TS_R_BAD_PKCS7_TYPE                              132
#  define TS_R_BAD_TYPE                                    133
#  define TS_R_CANNOT_LOAD_CERT                            137
#  define TS_R_CANNOT_LOAD_KEY                             138
#  define TS_R_CERTIFICATE_VERIFY_ERROR                    100
#  define TS_R_COULD_NOT_SET_ENGINE                        127
#  define TS_R_COULD_NOT_SET_TIME                          115
#  define TS_R_DETACHED_CONTENT                            134
#  define TS_R_ESS_ADD_SIGNING_CERT_ERROR                  116
#  define TS_R_ESS_ADD_SIGNING_CERT_V2_ERROR               139
#  define TS_R_ESS_SIGNING_CERTIFICATE_ERROR               101
#  define TS_R_INVALID_NULL_POINTER                        102
#  define TS_R_INVALID_SIGNER_CERTIFICATE_PURPOSE          117
#  define TS_R_MESSAGE_IMPRINT_MISMATCH                    103
#  define TS_R_NONCE_MISMATCH                              104
#  define TS_R_NONCE_NOT_RETURNED                          105
#  define TS_R_NO_CONTENT                                  106
#  define TS_R_NO_TIME_STAMP_TOKEN                         107
#  define TS_R_PKCS7_ADD_SIGNATURE_ERROR                   118
#  define TS_R_PKCS7_ADD_SIGNED_ATTR_ERROR                 119
#  define TS_R_PKCS7_TO_TS_TST_INFO_FAILED                 129
#  define TS_R_POLICY_MISMATCH                             108
#  define TS_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE      120
#  define TS_R_RESPONSE_SETUP_ERROR                        121
#  define TS_R_SIGNATURE_FAILURE                           109
#  define TS_R_THERE_MUST_BE_ONE_SIGNER                    110
#  define TS_R_TIME_SYSCALL_ERROR                          122
#  define TS_R_TOKEN_NOT_PRESENT                           130
#  define TS_R_TOKEN_PRESENT                               131
#  define TS_R_TSA_NAME_MISMATCH                           111
#  define TS_R_TSA_UNTRUSTED                               112
#  define TS_R_TST_INFO_SETUP_ERROR                        123
#  define TS_R_TS_DATASIGN                                 124
#  define TS_R_UNACCEPTABLE_POLICY                         125
#  define TS_R_UNSUPPORTED_MD_ALGORITHM                    126
#  define TS_R_UNSUPPORTED_VERSION                         113
#  define TS_R_VAR_BAD_VALUE                               135
#  define TS_R_VAR_LOOKUP_FAILURE                          136
#  define TS_R_WRONG_CONTENT_TYPE                          114

# endif
#endif
