<template>
  <div
    class="22 td-tree-node"
    :class="{
      'is-expanded': currentExpanded,
      'is-last-leaf-with-parent': isLastLeafWithParent,
      'is-selected': isSelected
    }"
    :has-children="hasChildren"
    :style="{ 'padding-left': paddingLeft }"
  >
    <div class="td-tree-node__content">
      <div
        class="td-tree-node__prefix"
        v-if="isShowHint && $slots.prefix"
      >
        <slot
          name="prefix"
          :node="node"
        ></slot>
      </div>
      <div
        class="td-tree-node__expand_icon"
        :class="{ 'is-hidden': !expandable || !hasChildren }"
      >
        <td-icon
          class="td-tree-node__expand-icon"
          :class="{ 'is-expanded': currentExpanded }"
          type="arrow-drop"
          @click="handleExpandIconClick"
        ></td-icon>
      </div>
      <div
        class="td-tree-node__checkbox"
        v-if="checkable"
      >
        <td-checkbox
          :indeterminate="indeterminate"
          :model-value="checked"
          :disabled="disabled"
          @update:model-value="handleInput"
        ></td-checkbox>
      </div>
      <div
        class="td-tree-node__image-icon"
        v-if="$slots.icon"
        @click="handleClickLabel"
        @dblclick="handleDbclickLabel"
      >
        <slot
          name="icon"
          :node="node"
        ></slot>
      </div>
      <div
        class="td-tree-node__label"
        @click="handleClickLabel"
        @dblclick="handleDbclickLabel"
      >
        <slot
          name="node"
          :node="node"
        >
          <slot
            name="label"
            :node="node"
          >
            {{ node.label || label }}
          </slot>
        </slot>
      </div>
    </div>
    <div
      class="td-tree-node__children"
      v-if="!inTable && hasChildren && currentExpanded"
    >
      <td-tree-node
        v-for="child in node.children"
        :key="child.id || child.label"
        :node="child"
        :level="level + 1"
        :checked="child.checked"
        :disabled="child.disabled || disabled"
        :expandable="child.expandable || expandable || getChildExpandable(child)"
        :has-children="child.hasChildren || getChildHasChildren(child)"
        :expanded="child.expanded"
        :checkable="child.checkable || checkable"
        :indeterminate="child.indeterminate"
        :treeEnabled="treeEnabled"
        :isShowHint="isShowHint"
        :inTable="inTable"
        :selected-node="selectedNode"
        @click-label="handleChildClickLabel"
        @update:expanded="handleChildUpdateExpanded"
        @change="handleChildChange"
        @dbclick-label="handleChildDbclickLabel"
      >
        <template
          v-if="$slots.node"
          #node="child"
        >
          <slot
            name="node"
            :node="child.node"
          ></slot>
        </template>
        <template
          v-if="$slots.label"
          #label="child"
        >
          <slot
            name="label"
            :node="child.node"
          ></slot>
        </template>
        <template
          v-if="$slots.icon"
          #icon="child"
        >
          <slot
            name="icon"
            :node="child.node"
          ></slot>
        </template>
        <template
          v-if="$slots.prefix"
          #prefix="child"
        >
          <slot
            name="prefix"
            :node="child.node"
          ></slot>
        </template>
      </td-tree-node>
    </div>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'
import Checkbox from '@root/common/components/ui/checkbox/index.vue'
import Icon from '@root/common/components/ui/icon/icon.vue'

export default defineComponent({
  name: 'TdTreeNode',

  components: {
    TdCheckbox: Checkbox,
    TdIcon: Icon,
  },

  props: {
    node: {
      type: Object,
      default: () => ({ label: '', children: [] }),
    },
    label: String,
    hasChildren: Boolean,
    level: Number,
    checked: Boolean,
    disabled: Boolean,
    expanded: Boolean,
    indeterminate: Boolean,
    checkable: Boolean,
    expandable: Boolean,
    treeEnabled: {
      type: Boolean,
      default: true,
    },
    isShowHint: Boolean,
    inTable: {
      type: Boolean,
      default: false,
    },
    selectedNode: {
      type: Object,
      default: null,
    },
  },

  emits: ['click-label', 'update:expanded', 'change', 'dbclick-label'],

  setup(props, { emit }) {
    const hasChildren = computed(() => {
      return props.hasChildren
    })

    const paddingLeft = computed(() => {
      const basePadding = props.level ? 20 : 0
      return `${basePadding}px`
    })

    // 当前的展开状态：优先使用props.expanded，如果没有则使用props.node.expanded
    const currentExpanded = computed(() => {
      // 在表格模式下，优先使用来自table组件的expanded属性
      if (props.inTable || props.expanded !== undefined) {
        return Boolean(props.expanded)
      }
      // 在独立树组件模式下，使用node.expanded
      return Boolean(props.node.expanded)
    })

    // 是否是最末端的叶子节点，有父级，且没有children
    const isLastLeafWithParent = computed(() => {
      const isLeafNode = !props.hasChildren
      const hasParent = props.level > 0
      return isLeafNode && hasParent
    })

    // 判断当前节点是否被选中
    const isSelected = computed(() => {
      const selected = props.selectedNode && props.selectedNode === props.node

      // 调试信息：只在有选中节点时输出
      if (props.selectedNode) {
        console.log(`[TreeNode] 选中状态判断 - 节点: "${props.node.label}"`)
        console.log(`[TreeNode] selectedNode.label: "${props.selectedNode.label}"`)
        console.log(`[TreeNode] 对象引用相等: ${props.selectedNode === props.node}`)
        console.log(`[TreeNode] 最终选中状态: ${selected}`)
        console.log('---')
      }

      return selected
    })

    const handleClickLabel = event => {
      event?.stopPropagation()
      emit('click-label', props.node)
    }

    const handleExpandIconClick = event => {
      event.stopPropagation()

      const newExpandedState = !currentExpanded.value

      // 更新node的expanded状态（如果存在）
      if (props.node) {
        props.node.expanded = newExpandedState
      }

      emit('update:expanded', props.node, newExpandedState)
    }

    const handleInput = checked => {
      emit('change', props.node, checked)
    }

    const handleDbclickLabel = event => {
      event?.stopPropagation()
      emit('dbclick-label', props.node)
    }

    const getChildExpandable = child => {
      return child && child.children && Array.isArray(child.children) && child.children.length > 0
    }

    const getChildHasChildren = child => {
      return child && child.children && Array.isArray(child.children) && child.children.length > 0
    }

    // 子组件事件处理函数
    const handleChildClickLabel = (childNode) => {
      emit('click-label', childNode)
    }

    const handleChildUpdateExpanded = (child, val) => {
      emit('update:expanded', child, val)
    }

    const handleChildChange = (child, val) => {
      emit('change', child, val)
    }

    const handleChildDbclickLabel = (childNode) => {
      emit('dbclick-label', childNode)
    }

    return {
      hasChildren,
      paddingLeft,
      currentExpanded,
      isLastLeafWithParent,
      isSelected,
      handleClickLabel,
      handleExpandIconClick,
      handleInput,
      handleDbclickLabel,
      getChildExpandable,
      getChildHasChildren,
      handleChildClickLabel,
      handleChildUpdateExpanded,
      handleChildChange,
      handleChildDbclickLabel,
    }
  },
})
</script>

<style lang="scss" scoped>
@import './tree.scss';
</style>
