import { MainToRenderer_Window_Channel, RendererToMain_Window_Channel } from '@root/common/constant'
import { debuggerLogger } from '@root/common/logger'
import { createGlobalState, useEventListener } from '@vueuse/core'
import { useIpcRendererOn } from '@vueuse/electron'
import { BrowserWindow, ipcRenderer } from 'electron'
import { readonly, ref } from 'vue'

type TBrowserWindowIns = InstanceType<typeof BrowserWindow>

let g_bShowCursor: boolean = true;
export const useWindowControlGlobal = createGlobalState(() => {
  const windowControlGlobal_ref = ref({
    isWindowMax: false,
    isMinimize: false,
    isFullScreen: !!document.fullscreenElement,
    focus: document.visibilityState === 'visible', // 窗口 focus
    isWindowFullyDraggable: false,
    isPin: false, // 置顶
  })

  useIpcRendererOn(MainToRenderer_Window_Channel.onFullscreen, (_, isFullScreen: boolean) => {
    windowControlGlobal_ref.value.isFullScreen = isFullScreen
  })
  useIpcRendererOn(MainToRenderer_Window_Channel.onFocus, (_) => {
    debuggerLogger.log('browserWindow onFocus')
    windowControlGlobal_ref.value.focus = true
  })
  useIpcRendererOn(MainToRenderer_Window_Channel.onBlur, (_) => {
    windowControlGlobal_ref.value.focus = false
  })
  useIpcRendererOn(MainToRenderer_Window_Channel.onMaximize, (_, isMaximize: boolean) => {
    windowControlGlobal_ref.value.isWindowMax = isMaximize
  })
  useIpcRendererOn(MainToRenderer_Window_Channel.onMinimize, (_) => {
    windowControlGlobal_ref.value.isMinimize = true
  })
  useIpcRendererOn(MainToRenderer_Window_Channel.onRestore, (_) => {
    windowControlGlobal_ref.value.isMinimize = false
  })

  useEventListener(document, 'fullscreenchange', () => {
    windowControlGlobal_ref.value.isFullScreen = !!document.fullscreenElement
  })

  function sendfullyDraggableIPC(draggable: boolean) {
    ipcRenderer.send(
      RendererToMain_Window_Channel.windowFullyDraggable,
      draggable,
    )
  }

  const windowControlGlobalAction = {
    minizeWindow() {
      ipcRenderer.send(RendererToMain_Window_Channel.minizeWindow)
    },
    toggleMaxWindow() {
      ipcRenderer.send(RendererToMain_Window_Channel.maxWindow)
    },
    async closeWindow() {
      ipcRenderer.send(RendererToMain_Window_Channel.closeWindow)
    },
    hideWindow() {
      ipcRenderer.send(RendererToMain_Window_Channel.hideWindow)
    },
    focusWindow() {
      ipcRenderer.send(RendererToMain_Window_Channel.focusWindow)
    },
    toggleFullscreen() {
      if (windowControlGlobal_ref.value.isFullScreen) {
        ipcRenderer.send(RendererToMain_Window_Channel.fullscreenOff)
      } else {
        ipcRenderer.send(RendererToMain_Window_Channel.fullscreen)
      }
    },
    fullyDraggable(draggable: boolean) {
      if (!windowControlGlobal_ref.value.isFullScreen) {
        sendfullyDraggableIPC(draggable)
        windowControlGlobal_ref.value.isWindowFullyDraggable = draggable
      }
    },
    togglePin() {
      windowControlGlobal_ref.value.isPin = !windowControlGlobal_ref.value.isPin
      ipcRenderer.send(
        RendererToMain_Window_Channel.windowPin,
        windowControlGlobal_ref.value.isPin,
      )
    },
    setTrafficLightPosition(position: Parameters<TBrowserWindowIns['setTrafficLightPosition']>[0]) {
      ipcRenderer.send(
        RendererToMain_Window_Channel.setTrafficLightPosition,
        position,
      )
    },
    setWindowButtonVisibility(isVisible: Parameters<TBrowserWindowIns['setWindowButtonVisibility']>[0]) {
      ipcRenderer.send(
        RendererToMain_Window_Channel.setWindowButtonVisibility,
        isVisible,
      )
    },
    showCursor(show: boolean) {
      if (show === g_bShowCursor) {
        return;
      }
      g_bShowCursor = show;
      ipcRenderer.send(RendererToMain_Window_Channel.showCursor, show)
    },
    hideOrShowPlayerControlWnd(show: boolean) {
      ipcRenderer.send(RendererToMain_Window_Channel.hideOrShowPlayerControlWnd, show)
    }
  }

  return { windowControlGlobal: readonly(windowControlGlobal_ref), windowControlGlobalAction }
})
