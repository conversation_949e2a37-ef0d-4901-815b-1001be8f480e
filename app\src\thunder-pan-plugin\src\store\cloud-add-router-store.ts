import { API_TASK, IExtendDriveFile } from '@root/common/thunder-pan-manager/pan-sdk/types'
import { createGlobalState } from '@vueuse/core'
import { computed, readonly, ref } from 'vue'

export type TPanRoute = {
  id: string
  title: string
} & IExtendDriveFile & API_TASK.DriveTask

export const useCloudAddRouterStore = createGlobalState(() => {
  function getRootRouter () {
    return {
      id: '',
      title: '全部文件',
    }
  }

  function resetRouterList () {
    routerList_ref.value = [
      getRootRouter(),
    ]
  }
  const routerList_ref = ref<TPanRoute[]>([])
  resetRouterList()

  const isEnterFolder = computed(() => {
    return routerList_ref.value.length > 1
  })

  const currentParentFile = computed(() => {
    return routerList_ref.value.slice(-1)[0]
  })

  function setRouterList (routeList: TPanRoute[], isConcatRoot = false) {
    if (isConcatRoot) {
      routerList_ref.value = [getRootRouter(), ...routeList]
    } else {
      routerList_ref.value = routeList
    }
  }

  return {
    routerList_computed: readonly(routerList_ref),
    currentParentFile: readonly(currentParentFile),
    isEnterFolder,
    setRouterList,
    resetRouterList,
  }
})
