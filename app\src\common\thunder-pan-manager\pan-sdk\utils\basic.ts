// @ts-nocheck
import { Dictionary } from '../types';

export const _typeof = v => Object.prototype.toString.call(v)

export const isFn = fn => _typeof(fn) === '[object Function]'

/**
 * 按指定长度分割数组
 * @param {Array} array 原数组
 * @param {Number} size 切割长度
 * @returns {Array<Array>} 切割后的数组
 */
export function chunk<T>(array: T[], size: number): T[][] {
  const result: T[][] = [];
  let index = 0;

  while (index < array.length) {
    result.push(array.slice(index, index + size));
    index += size;
  }

  return result;
}

/**
 * 数组扁平化
 * @param {Array} array 原数组
 * @returns {Array} 扁平化后的数组
 */
export function flatten<T>(array: T[]): T[] {
  const result: T[] = [];

  for (const item of array) {
    if (Array.isArray(item)) {
      result.push(...flatten(item));
    } else {
      result.push(item);
    }
  }

  return result;
}

/**
 * 扩展对象
 */
export function objectAssign<T> (...args: object[]): T {
  return Object.assign({}, ...args)
}

/**
 * 冻结对象
 */
export function objectFreeze (target) {
  return Object.freeze(target);
}

/**
 * 数组展开
 */
export function spreadArray (...args: [][]) {
  let newArr = []
  args.forEach(arg => {
    newArr = [
      ...newArr,
      ...arg
    ]
  })
  return newArr
}

/**
 * Function.bind
 */
export function bindFunc<T> (func: Function, context: any): T {
  return func.bind(context)
}

/**
 * Function.apply
 */
export function applyFunc<T> (func: Function, context: any, args: any[]): T {
  return func.apply(context, args)
}

/**
 * Function.call
 */
export function callFunc<T> (func: Function, context: any, ...args: any[]): T {
  return func.call(context, ...args)
}

/**
 * 安全解析 JSON
 * @param json 原始 JSON 数据
 * @param defaultVal 默认值
 * @returns
 */
export function safeJsonParse (json: string, defaultVal: any = {}) {
  try {
    return JSON.parse(json)
  } catch {
    return defaultVal
  }
}

/**
 * 将对象的第一层数据转化为字符串数据
 * @param obj
 */
export function convertEveryKeyToString (obj: any) {
  const newObj = {}
  if (typeof obj === 'object') {
    const keys = Object.keys(obj)

    keys.forEach(key => {
      const item = obj[key]
      if (typeof item !== 'string') {
        if (typeof item === 'object') {
          newObj[key] = JSON.stringify(item)
        } else {
          newObj[key] = String(item)
        }
      } else {
        newObj[key] = item
      }
    })
  }

  return newObj
}

export function sleep (time: number) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(time)
    }, time)
  })
}