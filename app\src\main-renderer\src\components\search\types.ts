import type { TaskBase } from '@root/common/task/base'

export interface BookmarkItem {
  id: string
  title: string
  url: string
  icon?: string
  time?: string
  type: string
}

export type { TaskBase }

export interface LinkFile {
  LinkID: string
  UserID: string
  ParentID: string
  IsDir: number // 1表示文件夹，0表示文件
  Url: string
  Name: string
  Source: number
  Size: number
  MediaType: number // 媒体类型: 0-未知、视频-1、音频-2、图片-3、归档文件-4、文本-5、安装包-6、字体-7、字幕-8
  CreateAt: number
  UpdateAt: number
  IconLink: string  // 图标
  Audit?: {
    Status: number // 未知 AUDIT_UNKNOW = 0; 通过 AUDIT_ACCEPTED = 1; 拒绝 AUDIT_REJECTED = 2;
    Message: string // 审核信息
  }
}

export interface CloudFile {
  Category: number
  CreateAt: string // 创建时间
  FileID: string // 文件ID
  IconLink: string // 图标
  IsTrashed: number // 是否删除
  Name: string // 文件名
  ParentID: string // 父文件ID
  Size: number // 文件大小
  Source: string // 来源
  Space: string // 空间
  Status: number // 文件状态：1: 等待中 2: 进行中 3: 失败 4: 完成
  Type: number // 类型：0文件，1文件夹
  UpdateAt: string // 更新时间
  UserID: string // 用户ID
  Audit?: {
    Status: number // 审核状态：0-未知、1-允许、2-敏感资源、3-文件名包含敏感词、4-无效资源
    Message: string // 审核状态说明
    Title: string // 审核状态简短说明
  }
}


export interface SearchResults {
  links: LinkFile[]
  cloud: CloudFile[]
  downloads: TaskBase[]
  bookmarks: BookmarkItem[]
}

export type SearchResult = LinkFile | CloudFile | TaskBase | BookmarkItem