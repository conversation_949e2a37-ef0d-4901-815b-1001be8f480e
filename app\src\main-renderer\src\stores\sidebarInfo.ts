import { defineStore } from 'pinia'
import { IDownloadCountInfo, IDownloadCountInfoType } from '@/types/sidebarInfo'

export const useSidebarInfoStore = defineStore('sidebarInfo', {
  state: () => ({
    downloadCountInfo: {
      downCount: 0, // 普通下载数量
      panDownCount: 0 // 云盘下载
    },
  }),
  getters: {
    getDownloadCount: (state) =>  {
      return state.downloadCountInfo.downCount + state.downloadCountInfo.panDownCount
    }
  },
  actions: {
    setDownloadInfo(val: IDownloadCountInfo) {
      this.downloadCountInfo = { ...this.downloadCountInfo, ...val }
    },
    setDownloadByKey(key: IDownloadCountInfoType, val) {
      this.downloadCountInfo[key] = val
    }
  },
})