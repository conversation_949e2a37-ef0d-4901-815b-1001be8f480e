<template>
  <div class="search-dropdown" ref="searchDropdownRef" @mousedown.prevent>
    <!-- Tab栏 -->
    <div class="search-tabs">
      <div 
        v-for="tab in tabs" 
        :key="tab.key"
        :class="['search-tab', { active: activeTab === tab.key }]"
        @click="activeTab = tab.key"
      >
        {{ tab.label }}
      </div>
    </div>

    <!-- 整体内容区域 -->
    <div class="search-container">
      <!-- 可滚动的搜索结果内容 -->
      <div class="search-content" @scroll="handleScroll">
        <!-- 空搜索时显示历史记录 -->
        <template v-if="!searchText.trim()">
          <SearchHistory
            ref="searchHistoryRef"
            v-model="searchHistory"
            :max-display-count="10"
            :portal-container="searchDropdownRef"
            @select="handleSelectHistory"
            @delete="handleDeleteHistory"
            @clear="handleClearHistory"
            @update:modelValue="updateSearchHistory"
          />
        </template>

        <!-- 搜索结果 -->
        <div v-else class="search-results">
          <!-- 综合tab - 显示所有类型结果 -->
          <div v-if="activeTab === 'all'" class="all-results">
            <!-- 链接结果 -->
            <div v-if="results.links.length > 0" class="result-section">
              <div class="section-header">
                链接
              </div>
              <SearchLinkItem
                v-for="(item, index) in results.links.slice(0, 3)"
                :key="`link-${index}`"
                :link="item as any"
                :focused="focusedIndex === getFocusIndex('link', index)"
                :high-light-words="linksState.HighLightWords"
                @select="handleSelectLinkResult"
                @locate="handleLocateLinkResult"
                :class="{'search-item-focused': focusedIndex === getFocusIndex('link', index)}"
              />
              <div v-if="results.links.length > 3" class="view-more" @click="activeTab = 'links'">
                <i class="xl-icon-search"></i>
                查看更多相关结果
              </div>
            </div>

            <!-- 云盘结果 -->
            <div v-if="results.cloud.length > 0" class="result-section">
              <div class="section-header">
                云盘
              </div>
              <SearchCloudItem
                v-for="(item, index) in results.cloud.slice(0, 3)"
                :key="`cloud-${index}`"
                :file="item"
                :focused="focusedIndex === getFocusIndex('cloud', index)"
                :high-light-words="cloudState.HighLightWords"
                @select="handleSelectCloudResult"
                @locate="handleLocateCloudResult"
                :class="{'search-item-focused': focusedIndex === getFocusIndex('cloud', index)}"
              />
              <div v-if="results.cloud.length > 3" class="view-more" @click="activeTab = 'cloud'">
                <i class="xl-icon-search"></i>
                查看更多相关结果
              </div>
            </div>

            <!-- 下载结果 -->
            <div v-if="results.downloads.length > 0" class="result-section">
              <div class="section-header">
                下载
              </div>
              <SearchDownloadItem
                v-for="(item, index) in results.downloads.slice(0, 3)"
                :key="`download-${item.taskId}`"
                :task="item"
                :focused="focusedIndex === getFocusIndex('download', index)"
                :search-text="searchText"
                @select="handleSelectDownloadResult"
                @locate="handleLocateDownloadResult"
                :class="{'search-item-focused': focusedIndex === getFocusIndex('download', index)}"
              />
              <div v-if="results.downloads.length > 3" class="view-more" @click="activeTab = 'downloads'">
                <i class="xl-icon-search"></i>
                查看更多相关结果
              </div>
            </div>
          </div>

          <div v-else-if="activeTab === 'links'" class="single-category-results">
            <div class="results-count" v-if="getCurrentResults().length > 0">
              共 {{ getCurrentResults().length }} 条{{ getCurrentTabLabel() }}
            </div>
            <div class="results-list">
              <SearchLinkItem
                v-for="(item, index) in getCurrentResults()"
                :key="`link-${index}`"
                :link="item as any"
                :focused="focusedIndex === index"
                :high-light-words="linksState.HighLightWords"
                @select="handleSelectLinkResult"
                @locate="handleLocateLinkResult"
                :class="{'search-item-focused': focusedIndex === index}"
              />
            </div>
            <!-- 加载状态 -->
            <div v-if="isLoadingMore" class="loading-more">
              <i class="xl-icon-general-loading-l"></i>
            </div>
            <!-- 加载异常 -->
            <div v-else-if="hasLoadError" class="load-error">
              加载异常，请稍后重试
            </div>
            <!-- 没有更多数据 -->
            <div v-else-if="hasNoMoreData && getCurrentResults().length > 0" class="no-more-data">
              已经到最底了
            </div>
          </div>

          <!-- 云盘tab -->
          <div v-else-if="activeTab === 'cloud'" class="single-category-results">
            <!-- 未登录状态 -->
            <div v-if="!isLoggedIn" class="login-prompt-cloud">
              <div class="login-message">登录后查看云盘内容</div>
              <button class="login-btn" @click="handleLogin">立即登录</button>
            </div>
            <!-- 已登录状态 -->
            <template v-else>
              <div class="results-count" v-if="getCurrentResults().length > 0">
                共 {{ getCurrentResults().length }} 条{{ getCurrentTabLabel() }}
              </div>
              <div class="results-list">
                <SearchCloudItem
                  v-for="(item, index) in getCurrentResults() as CloudFile[]"
                  :key="`cloud-${index}`"
                  :file="item"
                  :focused="focusedIndex === index"
                  :high-light-words="cloudState.HighLightWords"
                  @select="handleSelectCloudResult"
                  @locate="handleLocateCloudResult"
                  :class="{'search-item-focused': focusedIndex === index}"
                />
              </div>
              <!-- 加载状态 -->
              <div v-if="isLoadingMore" class="loading-more">
                <i class="xl-icon-general-loading-l"></i>
              </div>
              <!-- 加载异常 -->
              <div v-else-if="hasLoadError" class="load-error">
                加载异常，请稍后重试
              </div>
              <!-- 没有更多数据 -->
              <div v-else-if="hasNoMoreData && getCurrentResults().length > 0" class="no-more-data">
                已经到最底了
              </div>
            </template>
          </div>

          <!-- 下载tab -->
          <div v-else-if="activeTab === 'downloads'" class="single-category-results">
            <div class="results-count" v-if="getCurrentResults().length > 0">
              共 {{ getCurrentResults().length }} 条{{ getCurrentTabLabel() }}
            </div>
            <div class="results-list">
              <SearchDownloadItem
                v-for="(item, index) in getCurrentResults() as TaskBase[]"
                :key="`download-${item.taskId}`"
                :task="item"
                :focused="focusedIndex === index"
                :search-text="searchText"
                @select="handleSelectDownloadResult"
                @locate="handleLocateDownloadResult"
                :class="{'search-item-focused': focusedIndex === index}"
              />
            </div>
            <!-- 加载状态 -->
            <div v-if="downloadsState.loadingMore" class="loading-more">
              <i class="xl-icon-general-loading-l"></i>
            </div>
            <!-- 加载异常 -->
            <div v-else-if="downloadsState.loadError" class="load-error">
              加载异常，请稍后重试
            </div>
            <!-- 没有更多数据 -->
            <div v-else-if="downloadsState.noMoreData && getCurrentResults().length > 0" class="no-more-data">
              已经到最底了
            </div>
          </div>

          <!-- 加载中提示 -->
          <div v-if="shouldShowLoading" class="search-loading">
            搜索结果加载中...
          </div>

          <!-- 无结果提示 - 统一放在这里，根据条件控制显示 -->
          <div v-else-if="shouldShowNoResults" class="no-results">
            <div>找不到"{{ getEllipsisText(searchText) }}"相关内容</div>
            <div>
              可以尝试在 <span class="search-link" @click="handleGlobalSearch">全网搜索"{{ getEllipsisText(searchText) }}"</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区 - 移到滚动区域外 -->
      <div class="search-footer" v-if="searchText.trim() && ((activeTab === 'all' && hasAnyResults) || (activeTab !== 'all' && getCurrentResults().length > 0))">
        <div class="global-search" @click="handleGlobalSearch">
          没有找到内容？试试 <span class="search-link">全网搜索"{{ searchText.slice(0, 8) }}{{ searchText.length > 8 ? '...' : '' }}"</span>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { DkHelper } from '@root/common/task/impl/dk-helper'
import { URLHelperNS } from '@root/common/task/client/url-helper';
import { DownloadModelManagerClient } from '@root/common/ui-operation/client/download-model'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper';
import { useUserStore } from '@root/main-renderer/src/stores/user'
import { PopUpNS } from '@root/common/pop-up'
import { LinkHubHelper } from '@root/common/link-hub/client/link-hub-helper'
import { useRouter } from 'vue-router'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import SearchLinkItem from './search-link-item/index.vue'
import SearchHistory from './search-history/index.vue'
import SearchDownloadItem from './search-download-item/index.vue'
import SearchCloudItem from './search-cloud-item/index.vue'
import { message } from '@root/common/components/ui/message/index'
// import SearchBookmarkItem from './search-bookmark-item/index.vue'
import { GetTaskManager } from '@root/common/task/impl/task-manager'
import { ThunderHelper } from '@root/common/thunder-helper'
import { requestHelper } from '@/utils/request'
import { env } from '@root/common/env'
import { commonSites } from './search-history/common-sites'
import { SearchResults, LinkFile, CloudFile, BookmarkItem, SearchResult, TaskBase } from './types'

const props = defineProps<{
  searchText: string
}>()

const emit = defineEmits<{
  locateLink: [link: LinkFile]
  locateDownload: [task: TaskBase]
  locateCloud: [file: CloudFile]
  locateBookmark: [bookmark: BookmarkItem]
  select: [result: SearchResult]
  updateSearchText: [text: string]
}>()

// 状态管理
const activeTab = ref('all')
const focusedIndex = ref(0)
const searchHistory = ref<string[]>([])
const searchDropdownRef = ref<HTMLElement>()
const searchHistoryRef = ref<InstanceType<typeof SearchHistory> | null>(null)

const router = useRouter()
const thunderPanClientSDK = ThunderPanClientSDK.getInstance()

// 链接标签页状态
const linksState = ref({
  loading: false,  // 新增
  loadingMore: false,
  loadError: false,
  noMoreData: false,
  pageIndex: 0,
  pageSize: 10,
  HighLightWords: [] as string[]
})

// 云盘标签页状态
const cloudState = ref({
  loading: false,  // 新增
  loadingMore: false,
  loadError: false,
  noMoreData: false,
  pageToken: '',
  pageSize: 10,
  HighLightWords: [] as string[]
})

// 下载标签页状态
const downloadsState = ref({
  loading: false,  // 新增
  loadingMore: false,
  loadError: false,
  noMoreData: false,
  pageIndex: 0,
  pageSize: 10,
  allTaskIds: [] as number[] // 存储所有任务ID，改为 number[] 类型
})

const userStore = useUserStore();
const isLoggedIn = computed(() => {
  return !!userStore?.userInfo?.id
})

// Tab配置
const tabs = [
  { key: 'all', label: '综合' },
  { key: 'links', label: '链接' },
  { key: 'cloud', label: '云盘' },
  { key: 'downloads', label: '下载' },
  // { key: 'bookmarks', label: '网址收藏' } // 暂时隐藏网址收藏tab
]

// 模拟搜索结果
const results = ref<SearchResults>({
  links: [],
  cloud: [],
  downloads: [],
  bookmarks: []
})

// 计算属性
const hasAnyResults = computed(() => {
  return results.value.links.length > 0 || 
         results.value.cloud.length > 0 || 
         results.value.downloads.length > 0 || 
         results.value.bookmarks.length > 0
})

// 当前标签页是否正在加载更多数据
const isLoadingMore = computed(() => {
  if (activeTab.value === 'links') {
    return linksState.value.loadingMore
  } else if (activeTab.value === 'cloud') {
    return cloudState.value.loadingMore
  } else if (activeTab.value === 'downloads') {
    return downloadsState.value.loadingMore
  }
  return false
})

// 当前标签页是否有加载错误
const hasLoadError = computed(() => {
  if (activeTab.value === 'links') {
    return linksState.value.loadError
  } else if (activeTab.value === 'cloud') {
    return cloudState.value.loadError
  } else if (activeTab.value === 'downloads') {
    return downloadsState.value.loadError
  }
  return false
})

// 当前标签页是否没有更多数据
const hasNoMoreData = computed(() => {
  if (activeTab.value === 'links') {
    return linksState.value.noMoreData
  } else if (activeTab.value === 'cloud') {
    return cloudState.value.noMoreData
  } else if (activeTab.value === 'downloads') {
    return downloadsState.value.noMoreData
  }
  return false
})

const getEllipsisText = (text: string) => {
  return text.slice(0, 8) + (text.length > 8 ? '...' : '')
}

const getCurrentResults = () => {
  switch (activeTab.value) {
    case 'links': return results.value.links
    case 'cloud': return results.value.cloud
    case 'downloads': return results.value.downloads
    case 'bookmarks': return results.value.bookmarks
    default: return []
  }
}

const getCurrentTabLabel = () => {
  const tab = tabs.find(t => t.key === activeTab.value)
  return tab?.label || ''
}

const getFocusIndex = (type: string, index: number) => {
  let offset = 0
  if (type === 'cloud') offset += Math.min(3, results.value.links.length)
  if (type === 'download') offset += Math.min(3, results.value.links.length) + Math.min(3, results.value.cloud.length)
  return offset + index
}

// 搜索链接的方法
const searchLinks = async (keyword: string, isLoadMore = false): Promise<LinkFile[]> => {
  try {
    const beginIndex = isLoadMore ? linksState.value.pageIndex * linksState.value.pageSize : 0
    
    // 使用 LinkHubHelper 获取所有链接
    const data = await LinkHubHelper.getInstance().searchLinks({
      keyword: keyword,
      rangeInfo: {
        range: { beginIndex, count: linksState.value.pageSize },
      },
      reload: !isLoadMore,
    })
    
    const links = data?.searchList?.Links || []

    console.log('searchLinks', data)
    
    linksState.value.pageIndex++
    linksState.value.noMoreData = !data.hasMore
    if (!isLoadMore) {
      // 搜索pc这边的架构复用链接库，这边只能放在同一级, 所以直接从第一条里面取
      const highLightWords = data?.searchList?.Links?.[0]?.HighLightWords || []
      linksState.value.HighLightWords = highLightWords?.length ? highLightWords : [props.searchText]
    }
    
    return links
  } catch (error) {
    console.error('搜索链接失败:', error)
    linksState.value.loadError = true
    return []
  }
}

// 搜索云盘文件的方法
const searchCloud = async (keyword: string, isLoadMore = false): Promise<CloudFile[]> => {
  try {
    const baseUrl =  env === 'prod' ? 'https://api-gateway-pan.xunlei.com'  :  'https://test-api-gateway-pan.xunlei.com' 
    const method = 'GET'
    const userId = userStore.userInfo?.sub || ''
    if (!isLoggedIn.value || !userId) return []

    const data: any = {
      user_id: userId,
      limit: cloudState.value.pageSize,
      keyword: keyword,
    }
    
    // 如果是加载更多且有pageToken，添加到请求参数中
    if (isLoadMore && cloudState.value.pageToken) {
      data.page_token = cloudState.value.pageToken
    }
    
    const url = `${baseUrl}/xlppc.searcher.api/drive_file_search`
    const res = await requestHelper.request({
      method,
      url,
      body: {
        ...data,
      },
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true,
    }) as any
    
    console.log('searchCloud res', res)

    const files: CloudFile[] = res?.data?.Files || []
    
    // 保存下一页的token
    if (res?.data?.NextPageToken) {
      cloudState.value.pageToken = res.data.NextPageToken
    } else {
      // 如果没有下一页token，说明没有更多数据了
      cloudState.value.noMoreData = true
    }
    if (files.length < cloudState.value.pageSize) {
      cloudState.value.noMoreData = true
    }
    if (!isLoadMore) {
      cloudState.value.HighLightWords = res?.data?.HighLightWords?.length ? res?.data?.HighLightWords : [props.searchText]
    }
    
    return files
  } catch (error) {
    console.error('搜索云盘文件失败:', error)
    cloudState.value.loadError = true
    return []
  }
}

// 搜索下载任务的方法
const searchDownloads = async (keyword: string): Promise<TaskBase[]> => {
  try {
    const taskManager = GetTaskManager()
    
    // 搜索下载任务，isPanTask 设为 true 表示搜索本地下载任务
    const taskIds = await taskManager.searchTask(keyword, true)
    
    // 存储所有任务ID，以便后续分批加载
    downloadsState.value.allTaskIds = taskIds
    // 第一次加载，pageIndex 设为 1
    downloadsState.value.pageIndex = 1
    
    // 获取第一批任务详情（前10条）
    const tasks:TaskBase[] = []
    const firstBatchIds = taskIds.slice(0, downloadsState.value.pageSize)
    
    for (const taskId of firstBatchIds) {
      const task = await taskManager.findTaskById(taskId)
      if (task) {
        tasks.push(task.getTaskBase())
      }
    }
    console.log('searchDownloads tasks', tasks)
    
    // 如果任务总数小于等于每页数量，则标记为没有更多数据
    if (taskIds.length <= downloadsState.value.pageSize) {
      downloadsState.value.noMoreData = true
    }
    
    return tasks
  } catch (error) {
    console.error('搜索下载任务失败:', error)
    return []
  }
}

// 搜索网址收藏的方法
// const searchBookmarks = async (keyword: string): Promise<BookmarkItem[]> => {
//   try {
//     // 如果用户未登录，返回空数组
//     if (!isLoggedIn.value) {
//       return []
//     }
    
//     // 实际项目中应该使用API请求获取数据
    
//     // 使用模拟数据进行测试
//     const mockBookmarks: BookmarkItem[] = [
//       {
//         id: 'bookmark_001',
//         title: '迅雷官网',
//         url: 'https://www.xunlei.com',
//         time: '2023-05-20',
//         type: 'bookmark'
//       },
//     ]
    
//     return mockBookmarks
//   } catch (error) {
//     console.error('搜索书签失败:', error)
//     return []
//   }
// }

const updateSearchHistory = (newHistory: string[]) => {
  searchHistory.value = newHistory
}

const resetPageState = () => {
  console.log('>>>>>>>>>>>>>>>>> resetPageState')
  // 重置分页状态
  linksState.value.pageIndex = 0
  linksState.value.noMoreData = false
  linksState.value.loadError = false
  
  cloudState.value.pageToken = ''
  cloudState.value.noMoreData = false
  cloudState.value.loadError = false

  downloadsState.value.pageIndex = 0
  downloadsState.value.noMoreData = false
  downloadsState.value.loadError = false
}

// 防抖搜索函数
const debouncedSearch = useDebounceFn(async (keyword: string) => {
  if (!keyword.trim()) {
    results.value = { links: [], cloud: [], downloads: [], bookmarks: [] }
    return
  }

  // 设置所有分类为加载状态
  resetPageState()
  linksState.value.loading = true
  cloudState.value.loading = true
  downloadsState.value.loading = true

  try {
    results.value = { links: [], cloud: [], downloads: [], bookmarks: [] }
    
    // 并行启动搜索，每个请求完成后更新自己的状态
    await Promise.allSettled([
      searchLinks(keyword).then(res => {
        console.log('searchLinks res', res)
        results.value.links = res
      }).finally(() => {
        linksState.value.loading = false
      }),
      searchCloud(keyword).then(res => {
        results.value.cloud = res
      }).finally(() => {
        cloudState.value.loading = false
      }),
      searchDownloads(keyword).then(res => {
        results.value.downloads = res
      }).finally(() => {
        downloadsState.value.loading = false
      })
    ])
  } catch (err) {
    console.error('搜索失败:', err)
  }
}, 300)

// 处理滚动加载
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  
  // 如果正在加载，则不处理
  if (isLoadingMore.value) return
  
  // 获取内容高度、滚动位置和视口高度
  const scrollHeight = target.scrollHeight
  const scrollTop = target.scrollTop
  const clientHeight = target.clientHeight
  
  // 判断是否滚动到底部（预留10px的缓冲区）
  const isBottom = scrollHeight - scrollTop - clientHeight < 10
  
  // 如果已经到底部，并且不是"全部"标签页，并且有结果，并且没有更多数据的标志未设置
  if (isBottom && activeTab.value !== 'all' && getCurrentResults().length > 0 && !hasNoMoreData.value) {
    if (activeTab.value === 'links') {
      loadMoreLinks()
    } else if (activeTab.value === 'cloud') {
      loadMoreCloudData()
    } else if (activeTab.value === 'downloads') {
      loadMoreDownloads()
    }
  }
}

// 加载更多链接数据
const loadMoreLinks = async () => {
  if (linksState.value.loadingMore || linksState.value.noMoreData || linksState.value.loading) return
  
  linksState.value.loadingMore = true
  
  try {
    const moreLinks = await searchLinks(props.searchText, true)
    
    if (moreLinks.length > 0) {
      results.value.links = [...results.value.links, ...moreLinks]
    }
    
    // 如果返回的数据少于请求的数量，说明没有更多数据了
    if (moreLinks.length < linksState.value.pageSize) {
      linksState.value.noMoreData = true
    }
  } catch (error) {
    console.error('加载更多链接失败:', error)
    linksState.value.loadError = true
  } finally {
    linksState.value.loadingMore = false
  }
}

// 加载更多云盘数据
const loadMoreCloudData = async () => {
  if (cloudState.value.loadingMore || cloudState.value.noMoreData || cloudState.value.loading) return
  
  cloudState.value.loadingMore = true
  
  try {
    const moreCloudFiles = await searchCloud(props.searchText, true)
    
    if (moreCloudFiles.length > 0) {
      results.value.cloud = [...results.value.cloud, ...moreCloudFiles]
    }
    
    // 如果没有下一页token，说明没有更多数据了
    if (!cloudState.value.pageToken) {
      cloudState.value.noMoreData = true
    }
  } catch (error) {
    console.error('加载更多云盘数据失败:', error)
    cloudState.value.loadError = true
  } finally {
    cloudState.value.loadingMore = false
  }
}

// 加载更多下载任务
const loadMoreDownloads = async () => {
  if (downloadsState.value.loadingMore || downloadsState.value.noMoreData || downloadsState.value.loading) return
  
  downloadsState.value.loadingMore = true
  
  try {
    const taskManager = GetTaskManager()
    const startIndex = downloadsState.value.pageIndex * downloadsState.value.pageSize
    const endIndex = startIndex + downloadsState.value.pageSize
    
    // 获取下一批任务ID
    const nextBatchIds = downloadsState.value.allTaskIds.slice(startIndex, endIndex)
    
    if (nextBatchIds.length === 0) {
      downloadsState.value.noMoreData = true
      return
    }
    
    // 获取任务详情
    const moreTasks: TaskBase[] = []
    for (const taskId of nextBatchIds) {
      const task = await taskManager.findTaskById(taskId)
      if (task) {
        moreTasks.push(task.getTaskBase())
      }
    }
    
    // 添加到结果中
    if (moreTasks.length > 0) {
      results.value.downloads = [...results.value.downloads, ...moreTasks]
      // 增加页码
      downloadsState.value.pageIndex++
    }
    
    // 如果已加载的任务数量等于或超过总任务数，标记为没有更多数据
    if (results.value.downloads.length >= downloadsState.value.allTaskIds.length) {
      downloadsState.value.noMoreData = true
    }
  } catch (error) {
    console.error('加载更多下载任务失败:', error)
    downloadsState.value.loadError = true
  } finally {
    downloadsState.value.loadingMore = false
  }
}

// 加载搜索历史记录
const loadSearchHistory = async () => {
  try {
    const sites = await commonSites.getSites()
    searchHistory.value = sites.map(site => site.name)
  } catch (err) {
    console.error('加载搜索历史失败:', err)
    searchHistory.value = []
  }
}

// 添加搜索历史
const addSearchHistory = async (keyword: string, type?: string) => {
  if (!keyword.trim()) return
  
  try {
    await commonSites.addSite(keyword.trim(), type)
    await loadSearchHistory() // 重新加载历史记录
  } catch (err) {
    console.error('添加搜索历史失败:', err)
  }
}

// 删除搜索历史
const deleteSearchHistory = async (index: number) => {
  try {
    const sites = await commonSites.getSites()
    if (index >= 0 && index < sites.length) {
      await commonSites.deleteSite(sites[index].id)
      await loadSearchHistory() // 重新加载历史记录
    }
  } catch (err) {
    console.error('删除搜索历史失败:', err)
  }
}

// 清空搜索历史
const clearSearchHistory = async () => {
  try {
    await commonSites.clearSites()
    searchHistory.value = []
  } catch (err) {
    console.error('清空搜索历史失败:', err)
  }
}

// 事件处理
const handleSelectResult = (result: SearchResult) => {
  // 当用户确认选择结果时，添加到搜索历史
  addSearchHistory(props.searchText)
  emit('select', result)
}

const handleSelectLinkResult = (link: any) => {
  handleSelectResult(link)
}

// 处理定位链接结果
const handleLocateLinkResult = (link: LinkFile) => {
  console.log('handleLocateLinkResult', link)
  // 将搜索关键词添加到历史记录
  addSearchHistory(props.searchText)
  emit('locateLink', link)
  
  if (link.ParentID) {
    // 如果有父目录，跳转到父目录并高亮显示当前项
    router.push(`/link/${link.ParentID}?detailId=${link.LinkID}`)
  } else {
    // 对于根目录下的文件，跳转到链接列表页面并高亮显示
    router.push(`/link?linkId=${link.LinkID}`)
  }
}

const handleSelectDownloadResult = (task: TaskBase) => {
  handleSelectResult(task)
}

// 处理定位下载结果
const handleLocateDownloadResult = (task: TaskBase) => {
  console.log('handleLocateDownloadResult', task)
  // 定位到下载tab并选中该任务
  // 这里需要与外部组件通信，可以通过emit向上传递
  addSearchHistory(props.searchText)
  emit('locateDownload', task)
  if (!task.taskId) {
    message({ message: '任务已失效，无法打开', type: 'warning' })
    return
  }
  DownloadModelManagerClient.GetInstance().positionDownloadTask({taskId: task.taskId, isPassive: true})
}

const handleSelectCloudResult = (file: CloudFile) => {
  handleSelectResult(file)
}

const handleLocateCloudResult = (file: CloudFile) => {
  // 定位到云盘tab并选中该文件
  addSearchHistory(props.searchText)
  emit('locateCloud', file)
  router.push('/cloud')
  setTimeout(() => {
    console.log('>>>>>>>>>>>>>>>>>> props.file.FileID', file.FileID)
    thunderPanClientSDK.openDirectory(file.FileID)
  }, 100)
}

// 处理书签结果选择
// const handleSelectBookmarkResult = (bookmark: BookmarkItem) => {
//   console.log('handleSelectBookmarkResult', bookmark)
//   // 已在组件内部处理打开浏览器
//   // emit('locateBookmark', bookmark)
// }

const handleSelectHistory = (keyword: string) => {
  // 通知父组件更新输入框内容
  emit('updateSearchText', keyword)
  // 使用选中的关键词进行搜索
  debouncedSearch(keyword)
  // 注意：这里不添加到历史记录，只有当用户真正选择搜索结果时才添加
}

const handleDeleteHistory = (index: number) => {
  deleteSearchHistory(index)
}

const handleClearHistory = () => {
  clearSearchHistory()
}

const handleGlobalSearch = () => {
  // 打开默认浏览器进行全网搜索
  console.log('Global search:', props.searchText)
  // 将全网搜索的关键词也添加到历史记录中
  if (props.searchText.trim()) {
    addSearchHistory(props.searchText.trim(), 'global')
  }
  ThunderHelper.openURLByDefault(`https://www.baidu.com/s?wd=${props.searchText}`)
}

const handleLogin = () => {
  // 处理登录
  PopUpNS.showLoginDlg()
}

/**
 * 解析Thunder链接，返回真实URL
 * 参考 create-task/index.vue 中的 parseThunderUrlIfNeeded 函数
 * @param url 可能是Thunder链接的URL
 * @returns 解析后的真实URL，如果不是Thunder链接则返回原URL
 */
 async function parseThunderUrlIfNeeded(url: string): Promise<string> {
  try {
    const isThunderUrl = await DkHelper.isThunderPrivateUrl(url)

    if (isThunderUrl) {
      console.log('[AutoCreateTaskView] 检测到Thunder链接，开始解析:', url)

      const thunderParseResult = await DkHelper.parseThunderPrivateUrl(url)

      if (thunderParseResult && thunderParseResult.trim()) {
        console.log('[AutoCreateTaskView] Thunder链接解析成功，解析后的URL:', thunderParseResult)
        return thunderParseResult.trim()
      } else {
        console.log('[AutoCreateTaskView] Thunder链接解析失败，返回原URL:', url)
        return url
      }
    }

    return ''
  } catch (error) {
    console.error('[AutoCreateTaskView] 解析Thunder链接时出错，返回原URL:', error, 'URL:', url)
    return url
  }
}

// 键盘事件处理
const handleKeyDown = async (event: KeyboardEvent) => {
  if (event.key === 'ArrowDown') {
    event.preventDefault()
    let total = 0
    if (activeTab.value === 'all') {
      total = Math.min(results.value.links.length, 3) + Math.min(results.value.cloud.length, 3) + Math.min(results.value.downloads.length, 3)
    } else {
      total = getCurrentResults().length
    }
    if (total > 0 && focusedIndex.value < total - 1) {
      focusedIndex.value++
    }
    // 当前item滚动到可视区域
    nextTick(() => {
      const contentEl = document.querySelector('.search-content')
      if (contentEl) {
        const itemEl = document.querySelector('.search-item-focused')
        if (itemEl) {
          itemEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
        }
      }
    })
    return
  } else if (event.key === 'ArrowUp') {
    event.preventDefault()
    let total = 0
    if (activeTab.value === 'all') {
      // 综合tab下
      total = hasAnyResults.value ? 3 : 0
    } else {
      total = getCurrentResults().length
    }
    const hasResult = (activeTab.value === 'all' && hasAnyResults.value) || (activeTab.value !== 'all' && getCurrentResults().length > 0)
    if (hasResult && focusedIndex.value > 0) {
      focusedIndex.value--
    }
    // 当前item滚动到可视区域
    nextTick(() => {
      const contentEl = document.querySelector('.search-content')
      if (contentEl) {
        const itemEl = document.querySelector('.search-item-focused')
        if (itemEl) {
          itemEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
        }
      }
    })
    return
  } else if (event.key === 'Enter') {
    event.preventDefault()
    
    // 查找带有特殊class的元素并触发点击
    const focusedElement = document.querySelector('.search-item-focused') as HTMLElement
    if (focusedElement) {
      focusedElement.click()
    } else if (props.searchText.trim() && !linksState.value.loading) {
      
      // const match = URLHelperNS.isBirdKey(props.searchText);
      // console.log('>>>>>>>>>>>>>>>>>> match', match)
      // if (match && match.length) {
      //   // 如果是迅雷口令，则打开迅雷
      //   console.log('>>>>>>>>>>>>>>>>>> 迅雷口令', props.searchText)
      //   return 
      // }
      const realUrl = await parseThunderUrlIfNeeded(props.searchText)
      
      if (realUrl) {
        // 然后获取真实URL的任务类型
        const taskType = await DkHelper.getTaskTypeFromUrl(realUrl)
        let data: ThunderNewTaskHelperNS.INewTaskData = {
            url: realUrl,
            taskType,
        }
        console.log('>>>>>>>>>>>>>>>>>> 打开新建面板', data)
        ThunderNewTaskHelperNS.showPreCreateTaskWindow([data]);
      } else {
        handleGlobalSearch()
      }
    }
  } else if (event.key === 'Tab') {
    event.preventDefault()
    // 切换tab
    const currentIndex = tabs.findIndex(t => t.key === activeTab.value)
    const nextIndex = (currentIndex + 1) % tabs.length
    activeTab.value = tabs[nextIndex].key
  }
}

// 监听搜索文本变化
watch(() => props.searchText, (newText) => {
  debouncedSearch(newText)
}, { immediate: true })

// 监听activeTab变化，重置焦点
watch(activeTab, () => {
  focusedIndex.value = 0
  
  // 将滚动位置重置到顶部
  nextTick(() => {
    const contentEl = document.querySelector('.search-content')
    if (contentEl) {
      contentEl.scrollTop = 0
    }
  })
})

// 计算是否应该显示加载中提示
const shouldShowLoading = computed(() => {
  // 只有在加载中且当前标签页没有结果时显示
  if (activeTab.value === 'all') {
    return linksState.value.loading && cloudState.value.loading && downloadsState.value.loading
  } else if (activeTab.value === 'cloud') {
    return cloudState.value.loading
  } else if (activeTab.value === 'links') {
    return linksState.value.loading
  } else if (activeTab.value === 'downloads') {
    return downloadsState.value.loading
  } else {
    return false
  }
});

// 计算是否应该显示无结果提示
const shouldShowNoResults = computed(() => {
  // 有加载中状态时不显示
  if (shouldShowLoading.value) return false;
  
  if (activeTab.value === 'all') {
    // 综合tab下，没有任何结果时显示
    return !hasAnyResults.value;
  } else if (activeTab.value === 'cloud' && !isLoggedIn.value) {
    // 云盘tab下未登录时不显示无结果提示
    return false;
  } else {
    // 其他tab下，当前tab没有结果时显示
    return getCurrentResults().length === 0;
  }
});

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
  
  // 加载搜索历史
  loadSearchHistory()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style lang="scss" scoped>
.search-dropdown {
  width: 480px;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 6px;
  border-radius: var(--border-radius-M, 8px);
  background: var(--background-background-elevated, #FFF);
  box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
  min-height: 272px;
  max-height: 602px;
  z-index: 9;
  display: flex;
  flex-direction: column;
}

.search-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-border-2, #E5E6EB);
  padding: var(--border-radius-M, 8px) 24px 0px 24px;
  border-bottom: 1px solid var(--border-border-3, #F2F3F5);
  gap: 40px;
}

.search-tab {
  padding: 8px 0px;
  cursor: pointer;
  color: var(--font-font-3, #86909C);
  font-size: 14px;
  line-height: 22px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;

  &:hover {
    color: var(--font-font-1, #1D2129);
  }

  &.active {
    color: var(--primary-primary-font-default, #226DF5);
    border-bottom-color: var(--primary-primary-font-default, #226DF5);
  }
}

.search-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 确保容器不会溢出 */
}

.search-content {
  flex: 1;
  overflow-y: auto;
  padding: 0px 24px;
}

.search-results {
  width: 100%;
}

.result-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  
}

.section-header {
  display: flex;
  align-items: center;
  padding: 12px 0 4px 0;
  line-height: 22px;
  color: var(--font-font-1, #272E3B);
  font-size: 13px;
}

.view-more {
  line-height: 40px;
  color: var(--font-font-1, #272E3B);
  font-size: 12px;
  cursor: pointer;
  i {
    font-size: 16px;
    margin-right: 12px;
  }
}

.single-category-results {
  .results-count {
    padding: 8px 0 4px 0;
    color: var(--font-font-3, #86909C);
    font-size: 12px;
    line-height: 20px;
  }
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 216px;
  color: var(--font-font-3, #86909C);
  font-size: 13px;
  line-height: 22px;
  text-align: center;
  gap: 16px;
  
  .search-link {
    color: var(--primary-primary-default, #226DF5);
    cursor: pointer;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  margin: 10px 0;
  
  i {
    color: #3F85FF;
    font-size: 20px;
    animation: spin 1s linear infinite;
  }
}

.load-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  color: var(--font-font-3, #86909C);
}

.no-more-data {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 40px;
  font-size: 13px;
  color: var(--font-font-3, #86909C);
}

.login-prompt-cloud {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 214px;
  
  .login-message {
    color: var(--font-font-3, #86909C);
    font-size: 13px;
    line-height: 22px;
    margin-bottom: 8px;
  }
  
  .login-btn {
    width: 76px;
    height: 32px;
    border-radius: var(--border-radius-S, 6px);
    background: var(--primary-primary-default, #226DF5);
    color: var(--button-button1-font-default, #FFF);
    font-size: 13px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.login-prompt-bookmarks {
  display: flex;
  width: 432px;
  height: 40px;
  margin-top: 12px;
  padding: 9px 12px 9px 12px;
  border-radius: var(--border-radius-M, 8px);
  background: var(--button-button-lead-default, rgba(34, 109, 245, 0.10));
  color: var(--font-font-2, #4E5769);
  font-size: 13px;
  
  .login-link {
    margin-left: 8px;
    color: var(--primary-primary-default, #226DF5);
    cursor: pointer;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.search-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 18px 0;
  background: var(--background-background-elevated, #FFF);
  border-top: 1px solid var(--border-border-3, #F2F3F5);
  flex-shrink: 0; /* 防止footer被压缩 */
}

.global-search {
  font-size: 13px;
  color: var(--font-font-3, #86909C);
  cursor: pointer;

  .search-link {
    color: var(--primary-primary-default, #226DF5);
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 216px;
  color: var(--font-font-3, #86909C);
  font-size: 13px;
  text-align: center;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style> 