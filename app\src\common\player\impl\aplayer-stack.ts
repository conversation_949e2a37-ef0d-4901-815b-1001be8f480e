import requireNodeFile from '@root/common/require-node-file'
import { GetPlayerControlAddonNodeName, GetXxxNodePath } from '@root/common/xxx-node-path'
import { EventEmitter } from 'events'
import * as path from 'path'
import type * as BaseType from '../base'
import { AplayerPlayHistory } from './aplayer-history'
import { AplayerMedia } from './aplayer-media'
import { AplayerPlayList } from './play-list'

export class AplayerStack {
  private playercontrol: any = requireNodeFile(path.join(GetXxxNodePath(), GetPlayerControlAddonNodeName()));
  private static instance: AplayerStack;
  private currMedia: AplayerMedia | null = null;
  private nativeStack: any;
  private playList: AplayerPlayList;
  private playHistory: AplayerPlayHistory;
  private nativeMediaChangeEventCookie: number = 0;
  private nativeMediaCloseEventCookie: number = 0;
  private nativeQuitPlayEventCookie: number = 0;
  private nativeInitFinishEventCookie: number = 0;
  private nativeWndMessageEventCookie: number = 0;
  private eventContainor: EventEmitter = new EventEmitter();
  private openMediaHook: ((attr: BaseType.MediaAttribute)=>Promise<void>)|null = null;
  private closeWndHooks: (()=>void)[] = [];

  constructor() {
    this.nativeStack = new this.playercontrol.NativeAplayerStack();
    this.playList = new AplayerPlayList(this.nativeStack.getPlayList());
    this.playHistory = new AplayerPlayHistory(this.nativeStack.getPlayHistory())
    this.attachMediaChangeEvent((m: AplayerMedia) => {
      try {
        this.currMedia = m;
      } catch (e) {
        console.log('wsw============e=', e);
      }
    });
    this.attachMediaClosedEvent(() => {
      this.currMedia = null;
    });
  }

  public static GetInstance(): AplayerStack {
    if (!AplayerStack.instance) {
      if (global.AplayerStackImplInstance) {
        AplayerStack.instance = global.AplayerStackImplInstance;
      } else {
        AplayerStack.instance = new AplayerStack();
        global.AplayerStackImplInstance = AplayerStack.instance;
      }
    }
    return AplayerStack.instance
  }

  public async createPlayerWnd(floatWnd: number, parentWnd: number): Promise<number> {
    this.playercontrol.setWndEx(floatWnd, parentWnd);
    return await new Promise<number>((v) => {
      this.playercontrol.getAplayerWnd((wnd: number) => {
        v(wnd);
      });
    });
  }

  public getCurrPlayMedia(): AplayerMedia | null {
    return this.currMedia
  }

  public openMedia(attr: BaseType.MediaAttribute): void {
    if (!this.openMediaHook) {
      this.nativeStack.openMedia(attr);
    } else {
      this.openMediaHook(attr).then(() => {
        this.nativeStack.openMedia(attr);
      })
    }
  }

  reOpenMedia(r: BaseType.OpenMediaReason): void {
    return this.nativeStack.reOpenMedia(r)
  }

  public closeMedia(): void {
    return this.nativeStack.closeMedia()
  }

  public pauseMedia(): void {
    return this.nativeStack.pauseMedia()
  }

  public playMedia(): void {
    return this.nativeStack.playMedia()
  }

  public async createSnapshot(filePath: string, out: string, w: number, h: number): Promise<string> {
    return await new Promise((v) => {
      this.nativeStack.createSnapshot(filePath, out, w, h, (succ: boolean, filePath: string) => {
        v(filePath);
      });
    });
  }

  public setMediaOpenHook(cb: (attr: BaseType.MediaAttribute)=>Promise<void>) {
    this.openMediaHook = cb;
  }

  public addCloseWndHook(cb: ()=>void) {
    this.closeWndHooks.push(cb);
  }

  public attachMediaChangeEvent(cb: (m: AplayerMedia) => void): void {
    this.eventContainor.on('MediaChangeEvent', cb);
    if (this.nativeMediaChangeEventCookie === 0) {
      this.nativeMediaChangeEventCookie = this.nativeStack.attachMediaChangeEvent((m: any) => {
        try {
          this.eventContainor.emit('MediaChangeEvent', new AplayerMedia(m));
        } catch (e) {
          console.log('MediaChangeEvent emit error,e=', e);
        }
      });
    }
  }

  public detachMediaChangeEvent(cb: (m: AplayerMedia) => void): void {
    this.eventContainor.off('MediaChangeEvent', cb);
    if (this.eventContainor.listenerCount('MediaChangeEvent') === 0) {
      console.log('detach MediaChangeEvent');
      this.nativeStack.detachMediaChangeEvent(this.nativeMediaChangeEventCookie);
      this.nativeMediaChangeEventCookie = 0;
    }
  }

  public attachMediaClosedEvent(cb: (m: AplayerMedia) => void): void {
    this.eventContainor.on('MediaCloseEvent', cb);
    if (this.nativeMediaCloseEventCookie === 0) {
      this.nativeMediaCloseEventCookie = this.nativeStack.attachMediaClosedEvent((m: any) => {
        this.eventContainor.emit('MediaCloseEvent', new AplayerMedia(m));
      });
    }
  }

  public detachMediaCloseEvent(cb: (m: AplayerMedia) => void): void {
    this.eventContainor.off('MediaCloseEvent', cb);
    if (this.eventContainor.listenerCount('MediaCloseEvent') === 0) {
      this.nativeStack.detachMediaCloseEvent(this.nativeMediaCloseEventCookie);
      this.nativeMediaCloseEventCookie = 0;
    }
  }

  public attachQuitPlayEvent(cb: (m: AplayerMedia) => void): void {
    this.eventContainor.on('QuitPlay', cb);
    if (this.nativeQuitPlayEventCookie === 0) {
      this.nativeQuitPlayEventCookie = this.nativeStack.attachQuitPlayEvent((m: any) => {
        this.eventContainor.emit('QuitPlay', new AplayerMedia(m));
      });
    }
  }

  public detachQuitPlayEvent(cb: (m: AplayerMedia) => void): void {
    this.eventContainor.off('QuitPlay', cb);
    if (this.eventContainor.listenerCount('QuitPlay') === 0) {
      this.nativeStack.detachQuitPlayEvent(this.nativeQuitPlayEventCookie);
      this.nativeQuitPlayEventCookie = 0;
    }
  }

  public attachInitFinishEvent(cb: () => void): void {
    this.eventContainor.on('InitFinish', cb);
    if (this.nativeInitFinishEventCookie === 0) {
      this.nativeInitFinishEventCookie = this.nativeStack.attachInitFinishEvent(() => {
        this.eventContainor.emit('InitFinish');
      });
    }
  }

  public detachInitFinishEvent(cb: () => void): void {
    this.eventContainor.off('InitFinish', cb);
    if (this.eventContainor.listenerCount('InitFinish') === 0) {
      this.nativeStack.detachInitFinishEvent(this.nativeInitFinishEventCookie);
      this.nativeInitFinishEventCookie = 0;
    }
  }

  public attachWndMessageEvent(cb:(msg: number, wParam: number, lParam: number) => void): void {
    this.eventContainor.on('WndMessageEvent', cb);
    if (this.nativeWndMessageEventCookie === 0) {
      this.nativeWndMessageEventCookie = this.nativeStack.attachWndMessageEvent((msg: number, wParam: number, lParam: number) => {
        this.eventContainor.emit('WndMessageEvent', msg, wParam, lParam);
      });
    }
  }

  public detachWndMessageEvent(cb:(msg: number, wParam: number, lParam: number) => void): void {
    this.eventContainor.off('WndMessageEvent', cb);
    if (this.eventContainor.listenerCount('WndMessageEvent') === 0) {
      this.nativeStack.detachWndMessageEvent(this.nativeWndMessageEventCookie);
      this.nativeWndMessageEventCookie = 0;
    }
  }

  public getSupportedPlaySpeedList(): BaseType.PlaySpeedDisplayInfo[] {
    return this.nativeStack.getSupportedPlaySpeedList()
  }

  getCurrentPlaySpeedId(): string {
    return this.nativeStack.getCurrentPlaySpeedId()
  }

  public switchSpeed(id: string): void {
    this.nativeStack.switchSpeed(id)
  }

  public async isSilent(): Promise<boolean> {
    return await new Promise((v: (is: boolean) => void) => {
      this.nativeStack.isSilent((n: number) => {
        v(!!n)
      })
    })
  }

  public setSilent(b: boolean): void {
    this.nativeStack.setSilent(b ? 1 : 0)
  }

  public async getVolume(): Promise<number> {
    return await new Promise((v: (n: number) => void) => {
      this.nativeStack.getVolume((n: number) => {
        v(n)
      })
    })
  }

  public setVolume(n: number): void {
    this.nativeStack.setVolume(n)
  }

  public async getCurrentPlaySequence(): Promise<BaseType.ListPlaySequence> {
    return await new Promise((v: (seq: BaseType.ListPlaySequence) => void) => {
      this.nativeStack.getCurrentPlaySequence((n: number) => {
        v(n as BaseType.ListPlaySequence)
      })
    })
  }

  public setPlaySequence(seq: BaseType.ListPlaySequence) {
    this.nativeStack.setPlaySequence(seq as number)
  }

  // public attachPlayViewSizeChangeEvent(cb: (viewId: string, x: number, y: number, w: number, h: number, angle: number) => void): number {
  //   return this.nativeStack.attachPlayViewSizeChangeEvent(cb);
  // }

  // public detachPlayViewSizeChangeEvent(cookie: number): void {
  //   this.nativeStack.detachPlayViewSizeChangeEvent(cookie);
  // }

  // public attachPlayViewVisibleChangeEvent(cb: (viewId: string, bVisible: boolean) => void): number {
  //   return this.nativeStack.attachPlayViewVisibleChangeEvent(cb);
  // }

  // public detachPlayViewVisibleChangeEvent(cookie: number): void {
  //   this.nativeStack.detachPlayViewVisibleChangeEvent(cookie);
  // }

  public closePlayWindow() {
    this.closeMedia();
    if (this.closeWndHooks.length > 0) {
      for (let cb of this.closeWndHooks) {
        cb();
      }
    }
  }

  public getPlayList(): AplayerPlayList {
    return this.playList
  }

  public getPlayHistory(): AplayerPlayHistory {
    return this.playHistory;
  }

  public async isMediaLocalPlay(
    attr: BaseType.MediaAttribute,
  ): Promise<boolean> {
    return await new Promise((v) => {
      this.nativeStack.isMediaLocalPlay(attr, (b: boolean) => {
        v(b)
      })
    })
  }

  public async getAplayerVersion(): Promise<string> {
    return await new Promise((v) => {
      this.nativeStack.getAplayerVersion((version: string) => {
        v(version)
      })
    })
  }

  public imageRatio(id: BaseType.ImageRatioType): void {
    this.nativeStack.imageRatio(id);
  }

  public getImageRatioItems(): BaseType.ImageRatioItem[] {
    let items = this.nativeStack.getImageRatioItems();
    if (!items) {
      return [];
    }

    return items as BaseType.ImageRatioItem[];
  }

  public imageRotation(angle: number): void {
    this.nativeStack.imageRotation(angle);
  }

  public imageFlip(bHorizontal: boolean, bRestore: boolean): void {
    this.nativeStack.imageFlip(bHorizontal, bRestore);
  }
}
