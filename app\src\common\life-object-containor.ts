export class LifeObjectContainor {
    private static instance: LifeObjectContainor;
    private lifeObject: Map<string, any> = new Map();
    private id: number = 0;

    public static GetInstance(): LifeObjectContainor {
        if (!LifeObjectContainor.instance) {
            if (global.LifeObjectContainorInstance) {
                LifeObjectContainor.instance = global.LifeObjectContainorInstance;
            } else {
                LifeObjectContainor.instance = new LifeObjectContainor();
                global.LifeObjectContainorInstance = LifeObjectContainor.instance;
            }
        }
        return LifeObjectContainor.instance;
    }

    public addObject(obj:any): string {
        this.id++;
        this.lifeObject.set(this.id.toString(), obj);
        return this.id.toString();
    }

    public getObject(id: string): any {
        return this.lifeObject.get(id);
    }

    public deleteObject(id: string): void {
        this.lifeObject.delete(id);
    }
}