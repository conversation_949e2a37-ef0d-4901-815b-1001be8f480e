import * as BaseType from '../base'

export class AplayerPlayHistory {
    private nativeObj: any;
    constructor(obj: any) {
        this.nativeObj = obj;
    }

    public deleteItem(id: number) {
        this.nativeObj.deleteItem(id);
    }

    public deleteAllItem() {
        this.nativeObj.deleteAllItem();
    }

    public getItemById(id: number): BaseType.PlayHistoryItem {
        return this.nativeObj.getItemById(id);
    }

    public playItem(id: number) {
        this.nativeObj.playItem(id);
    }

    public getList(): BaseType.PlayHistoryItem[] {
        return this.nativeObj.getList();
    }

    public getSelect(): number {
        return this.nativeObj.getSelect();
    }

    public isPrepared(): boolean {
        return this.nativeObj.isPrepared();
    }

    public attachPreparedEvent(cb: () => void): number {
        return this.nativeObj.attachPreparedEvent(cb);
    }

    public detachPreparedEvent(cookie: number): void {
        this.nativeObj.detachPreparedEvent(cookie);
    }

    public attachItemUpdate(cb: (id: number, bNewAdd: boolean) => void): number {
        return this.nativeObj.attachItemUpdate(cb);
    }

    public detachItemUpdate(cookie: number): void {
        return this.nativeObj.detachItemUpdate(cookie);
    }

    public attachItemDeleteEvent(cb: (id: number)=>void): number {
        return this.nativeObj.attachItemDeleteEvent(cb);
    }

    public detachItemDeleteEvent(cookie: number): void {
        this.nativeObj.detachItemDeleteEvent(cookie);
    }
}