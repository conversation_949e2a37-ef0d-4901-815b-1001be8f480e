import { TaskType } from '@root/common/task/base'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper'

// ==================== 任务错误类型定义 ====================

/** 任务错误类型 */
export type TaskErrorType = 'existed' | 'other'

/** 任务错误接口 */
export interface TaskError extends Error {
  /** 错误类型 */
  type: TaskErrorType
  /** 任务类型 */
  taskType?: TaskType
  /** 任务ID */
  taskId?: number
  /** 任务URL */
  url?: string
}

/** 创建任务错误的选项 */
export interface CreateTaskErrorOptions {
  /** 任务类型 */
  taskType?: TaskType
  /** 任务ID */
  taskId?: number
  /** 任务URL */
  url?: string
}

/** 创建任务错误的工厂函数类型 */
export type CreateTaskErrorFunction = (
  type: TaskErrorType,
  message: string,
  options?: CreateTaskErrorOptions
) => TaskError

// ==================== 原有的类型定义 ====================

export interface INewTaskDataItem {
  url: string
  cookie?: string
  referer?: string
  fileName?: string
  browser?: string
  statClick?: string
  specialType?: number
  fileSize?: number
  fileType?: string
  fileHash?: string
  subDir?: string
  gcid?: string
  cid?: string
  thread?: number
  nameFixedFlag?: number
  birdkeyChars?: string
  searchFrom?: string
  clickFrom?: string
  userAgent?: string
  taskType?: TaskType
  startMode?: number // 复用之前的字段，从12.1.2版本开始，该字段表示浏览器插件启动迅雷直接下载
  accelerable?: boolean // 是否为应用分发的包
  // 原始包/加速包的url，referer，fileName，nameFixedFlag等信息
  depotCacheUrl?: string // 应用分发需求：加速包url、原始包url
  depotCacheReferer?: string // 应用分发SDK，下载需要指定referer
  depotCacheFileName?: string // 应用分发需求：加速包、原始包 url对应的文件名 (含文件名已编辑状态)
  depotCacheIcon?: string // 应用分发需求：加速包的图标地址
  // depotCacheNameFlag?: number; // 应用分发需求：加速包、原始包 文件名的可修改状态
  depotStat?: { pkg_name: string; soft_id: number } // 应用分发需求的上报信息

  userdata?: { callbackId: number; processId: number } // 12.1.8添加的字段，支持ThunderAgent传递自定义字段，内容为JSON.stringify()的字符串
  taskIcon?: string
  httpHeaderField?: string // 设置http头的 Authorization
} // 这个字段用于专用链excludePath对应的子文件保存的目录

/** 新建任务数据项数组类型 */
export type INewTaskDataArray = INewTaskDataItem[]

/**
 * 新建任务相关类型定义
 */

// ==================== 枚举和常量 ====================

/** 任务解析状态 */
export type TaskParseStatusType = 'loading' | 'error' | 'success'

export const TaskParseStatus = {
  LOADING: 'loading' as const,
  ERROR: 'error' as const,
  SUCCESS: 'success' as const,
} as const

/** 磁力任务处理状态 */
export type MagnetTaskStatusType = 'magnet' | 'loading' | 'torrent' | 'done'

export const MagnetTaskStatus = {
  MAGNET: 'magnet' as const,
  LOADING: 'loading' as const,
  TORRENT: 'torrent' as const,
  DONE: 'done' as const,
} as const

/** Emule任务处理状态 */
export type EmuleTaskStatusType = 'parsing' | 'error' | 'parsed' | 'creating' | 'done'

export const EmuleTaskStatus = {
  PARSING: 'parsing' as const,
  ERROR: 'error' as const,
  PARSED: 'parsed' as const,
  CREATING: 'creating' as const,
  DONE: 'done' as const,
} as const

/** HTTP文件类型 */
export type HttpFileTypeEnum = 'video' | 'audio' | 'document' | 'archive' | 'image' | 'unknown'

export const HttpFileType = {
  VIDEO: 'video' as const,
  AUDIO: 'audio' as const,
  DOCUMENT: 'document' as const,
  ARCHIVE: 'archive' as const,
  IMAGE: 'image' as const,
  UNKNOWN: 'unknown' as const,
} as const

// ==================== 文件和任务接口 ====================

/** 任务设置信息 */
export interface TaskSettings {
  /** 下载链接 */
  downloadUrl: string
  /** 下载完成后打开 */
  openAfterDownload: boolean
  /** 只从原始地址下载 */
  downloadFromOriginalOnly: boolean
  /** 线程数 */
  threadCount: number
  /** 登录FTP服务器 */
  loginFtpServer: boolean
  /** FTP用户名 */
  ftpUsername: string
  /** FTP密码 */
  ftpPassword: string
  /** 自定义任务名称 */
  customTaskName?: string
}

/** BT任务文件信息 */
export interface TaskFileInfo {
  fileSize: number
  realIndex: number
  fileOffset: number
  fileName: string
  filePath: string
}

/** 磁力链任务信息 */
export interface TaskInfoWithStatus {
  title: string
  infoId: string
  url?: string
  status?: TaskParseStatusType
  trackerUrls: string[]
  fileLists: TaskFileInfo[]
  torrentFilePath?: string // 存储torrent文件的完整路径
  settings?: TaskSettings
}

/** Emule任务信息 */
export interface EmuleTaskInfo {
  title: string
  infoId: string // 使用fileHash作为唯一标识
  url: string
  status: EmuleTaskStatusType
  fileName: string
  fileSize: number
  fileHash: string
  addTime: number
  settings?: TaskSettings
}

// ==================== P2SP任务接口 ====================

/** P2SP任务信息 */
export interface P2spTaskInfo {
  infoId: string
  title: string
  url: string
  status: string
  taskType: number
  fileName: string
  fileSize?: number
  fileCategory: string
}

// ==================== 文件选择接口 ====================

/** 文件选择信息 */
export interface FileSelectionInfo {
  fileIndexes: number[]
  fileKeys: string[]
}

/** 任务文件选择状态映射 */
export interface TaskFileSelectionMap {
  // 以url作为任务的id，拒绝infoId
  [taskId: string]: FileSelectionInfo
}

// ==================== 组件通信接口 ====================

/** 重试磁力任务事件参数 */
export interface RetryMagnetTaskEvent {
  taskId: string
  url: string
  title: string
  originalTask: TaskInfoWithStatus
}

// ==================== 任务创建接口 ====================

/** 任务基础配置 */
export interface TaskBaseConfig {
  background: boolean
  savePath: string
  taskName: string
}

/** P2SP任务配置 */
export interface P2spTaskConfig extends TaskBaseConfig {
  url: string
  refUrl?: string
  cookie?: string
  userAgent?: string
}

/** BT任务配置 */
export interface BtTaskConfig extends TaskBaseConfig {
  origin?: string
  seedFile?: string
  displayName: string
  fileRealIndexLists: number[]
  fileLists: TaskFileInfo[]
  infoId: string
  tracker: string
}

/** 任务组配置 */
export interface GroupTaskConfig {
  savePath: string
  taskName: string
  background: boolean
  subTasks: any[]
}

// ==================== 应用状态接口 ====================

/** 下载配置 */
export interface DownloadConfig {
  downloadDestination: string
  downloadPath: string
  taskSavePath: string
  downloadType: 'local' | 'cloud'
  freeCloudAddCount: number
}

/** 进度监控信息 */
export interface ProgressInfo {
  downloadProgress: number // 0-100
  downloadSpeed: number // 字节/秒
  fileSize: number
  receivedSize: number
  taskId: number
}

// ==================== 路径和空间信息接口 ====================

/** 磁盘分区空间信息 */
export interface PartitionSpaceInfo {
  /** 总空间大小（字节） */
  total: number
  /** 剩余空间大小（字节） */
  free: number
}

/** 路径详情信息（包含空间信息） */
export interface PathDetailInfo {
  /** 路径 */
  dir: string
  /** 路径别名，例如：桌面/我的文档 */
  alias: string
  /** 是否可删除 */
  canDelete: boolean
  /** 云盘路径的id（可选） */
  id?: string
  /** 磁盘空间信息 */
  spaceInfo: PartitionSpaceInfo
}

export interface CloudPathDetailInfo {
  /** 是否可删除 */
  canDelete: boolean
  /** 云盘路径的id（可选） */
  id: string
  /** 云盘路径的名称 */
  name: string
}

// ==================== URL和数据映射接口 ====================

/** URL类型信息 */
export interface IUrlWithType {
  /** URL地址 */
  url: string
  /** 任务类型 */
  taskType: TaskType
}

/** URL类型信息数组 */
export type IUrlWithTypeArray = IUrlWithType[]

/** URL详细信息映射 */
export interface IUrlDataMap {
  [url: string]: IUrlDetailInfo
}

export interface ITaskExtraDataType {
  /** 磁力链任务的解析状态 (仅当taskType为Magnet时存在) */
  [url: string]: ITaskExtraDataInfoType
}

export interface ITaskExtraDataInfoType {
  torrentPath: string
  status: TaskParseStatusType
}

/**
 * 任务扩展数据映射
 * 用于存储每个URL对应的任务扩展数据
 */
export interface TaskExtDataMap {
  [url: string]: ThunderNewTaskHelperNS.ITaskExtData
}

/** URL详细信息基础接口 */
export interface IUrlDetailInfoBase {
  /** URL地址 */
  url: string
  /** 文件类型 */
  fileType?: string
  /** 文件名 */
  fileName?: string
  /** 添加时间 */
  addTime?: number
  /** 协议类型 */
  protocol?: 'HTTP' | 'FTP'
  /** 任务类型 */
  taskType?: TaskType
  /** 解析详情 */
  detail?: any
  /** 文件大小 */
  fileSize?: number
  /** 任务扩展数据，包含：
   * - checkedFileIndexes: 选中的文件索引列表
   * - fileName: 自定义的文件名
   */
  extData?: ThunderNewTaskHelperNS.ITaskExtData
  /** 其他扩展属性 */
  [key: string]: any
}

/** P2SP任务详细信息接口 */
export interface IP2spUrlDetailInfo extends IUrlDetailInfoBase {
  taskType: 1 // TaskType.P2sp
  /** 完整路径 */
  fullPath?: string
  /** 主机名 */
  hostName?: string
  /** 密码 */
  password?: string
  /** 端口号 */
  port?: number
  /** 协议模式 */
  schema?: string
  /** 用户名 */
  userName?: string
}

/** Emule任务详细信息接口 */
export interface IEmuleUrlDetailInfo extends IUrlDetailInfoBase {
  taskType: TaskType.Emule // TaskType.Emule
  /** 文件哈希值 */
  fileHash: string
}

/** 其他类型任务详细信息接口 */
export interface IOtherUrlDetailInfo extends IUrlDetailInfoBase {
  taskType?: Exclude<TaskType, 1 | 4> // 排除 P2sp 和 Emule 类型
  /** 任务解析状态（主要用于磁力链） */
  status?: TaskParseStatusType
  /** 重试次数（用于磁力链解析失败重试） */
  retryCount?: number
}

/** URL详细信息联合类型 */
export type IUrlDetailInfo = IP2spUrlDetailInfo | IEmuleUrlDetailInfo | IOtherUrlDetailInfo

// 为了更好的类型安全，我们也可以定义一个联合类型
export type IUrlWithTypeUnion =
  | {
      /** URL地址 */
      url: string
      /** 任务类型 */
      taskType: Exclude<TaskType, TaskType.Magnet> // 排除磁力链类型
    }
  | {
      /** URL地址 */
      url: string
      /** 任务类型 */
      taskType: TaskType.Magnet // 磁力链类型
      /** 磁力链任务的解析状态 */
      status: TaskParseStatusType
    }

// ==================== 下载事件参数接口 ====================

/** 下载事件参数 */
export interface DownloadEventParams {
  /** 选中的文件索引映射 */
  checkedFileIndexes: TaskFileSelectionMap
  /** 下载类型 */
  type: 'download' | 'cloud'
  /** 路径（下载类型传递本地路径，云盘类型传递云盘ID） */
  path?: string
}

/** 云盘路径选择操作类型 */
export type CloudPathPickedType =
  | 'select-checked-pan-path' // 选择已勾选的云盘路径
  | 'choose-pan-path' // 选择云盘文件夹
  | 'clear-pan-path' // 清空云盘历史路径
  | 'remove-pan-path' // 删除某一条云盘路径

/**
 * 云盘配额信息
 * 从 IGetCurrentUserQuotasResponse 提取的配额信息类型
 */
export interface IQuotas {
  /** 配额上限（字节） */
  limit: number
  /** 已使用空间（字节） */
  usage: number
  /** 剩余空间（字节） */
  surplus: number
}

// 重新导出 DownloadPathType 以保持向后兼容性
export const DownloadPathType = ThunderNewTaskHelperNS.DownloadPathType
export type DownloadPathType = ThunderNewTaskHelperNS.DownloadPathType

/**
 * DownloadButton 组件的 submit 事件参数类型
 * 用于 handleDownloadButtonSubmit 函数的参数类型
 */
export interface DownloadButtonSubmitParams {
  /** 下载路径类型 */
  type: DownloadPathType
  /** 路径（下载类型传递本地路径，云盘类型传递云盘ID） */
  path?: string
  /** 场景编号，用于标识不同使用场景 */
  scene?: string
}

// ==================== 树形节点类型定义 ====================

/** 文件分类类型 */
export type FileCategoryType = 'video' | 'image' | 'zip' | 'other'

/** 节点类型 */
export type NodeType = 'branch' | 'leaf'

/** 基础节点接口 */
export interface IBaseNode {
  /** 节点类型 */
  type: NodeType
  /** 节点名称 */
  name: string
  /** 节点唯一标识 */
  key: string
  /** 完整路径 */
  fullPath: string
  /** 父节点引用 */
  parent: IBranchNode | null
  /** 节点图标 */
  icon: string
  /** 节点层级 */
  level: number
  /** 任务类型 */
  taskType: TaskType
  /** 文件大小（字节） */
  fileSize: number
  /** 是否为根节点 */
  isRoot: boolean
  /** 任务ID */
  taskId?: string
  /** 任务索引（用于排序） */
  taskIndex?: number
  /** 任务URL */
  url?: string
  /** 协议类型 */
  protocol?: 'HTTP' | 'FTP'
  /** 添加时间 */
  addTime?: number
}

/** 分支节点接口（目录节点） */
export interface IBranchNode extends IBaseNode {
  /** 节点类型 - 固定为 'branch' */
  type: 'branch'
  /** 子节点数组 */
  children: (IBranchNode | ILeafNode)[]
  /** 是否为根节点 - 分支节点可能是根节点 */
  isRoot: boolean
  /** 任务ID */
  taskId?: string
  /** 任务索引（用于排序） */
  taskIndex?: number
  /** 任务URL */
  url?: string
}

/** 叶子节点接口（文件节点） */
export interface ILeafNode extends IBaseNode {
  /** 节点类型 - 固定为 'leaf' */
  type: 'leaf'
  /** 文件后缀名 */
  suffix: string
  /** 文件真实索引 */
  realIndex: number
  /** 是否默认选中 */
  default: boolean
  /** 是否为根节点 - 叶子节点永远不是根节点 */
  isRoot: false
  /** 文件分类 */
  fileCategory: FileCategoryType
  /** 是否为特殊空节点 */
  isSpecialEmptyNode: boolean
  /** 任务状态（仅特殊空节点有效） */
  status?: TaskParseStatusType
  /** 任务ID */
  taskId?: string
  /** 任务索引（用于排序） */
  taskIndex?: number
  /** 任务URL */
  url?: string
  /** 协议类型 */
  protocol?: 'HTTP' | 'FTP'
  /** 添加时间 */
  addTime?: number
}

/** 特殊空节点接口（磁力链解析状态节点） */
export interface ISpecialEmptyNode extends Omit<ILeafNode, 'fileSize' | 'realIndex' | 'default' | 'isSpecialEmptyNode' | 'fileCategory'> {
  /** 节点类型 - 固定为 'leaf' */
  type: 'leaf'
  /** 是否为特殊空节点 - 固定为 true */
  isSpecialEmptyNode: true
  /** 任务状态 */
  status: TaskParseStatusType
  /** 文件大小 - 特殊空节点为 0 */
  fileSize: 0
  /** 文件真实索引 - 特殊空节点为 -1 */
  realIndex: -1
  /** 是否默认选中 - 特殊空节点为 false */
  default: false
  /** 文件分类 - 特殊空节点为 'other' */
  fileCategory: 'other'
  /** 任务ID */
  taskId: string
  /** 任务索引 */
  taskIndex: number
  /** 任务URL */
  url: string
}

/** 普通叶子节点接口（真实文件节点） */
export interface INormalLeafNode extends Omit<ILeafNode, 'isSpecialEmptyNode'> {
  /** 节点类型 - 固定为 'leaf' */
  type: 'leaf'
  /** 是否为特殊空节点 - 固定为 false */
  isSpecialEmptyNode: false
  /** 文件大小（字节） */
  fileSize: number
  /** 文件真实索引 */
  realIndex: number
  /** 是否默认选中 */
  default: boolean
  /** 是否为根节点 - 固定为 false */
  isRoot: false
  /** 文件分类 */
  fileCategory: FileCategoryType
  /** 任务状态 - 普通叶子节点可能没有状态 */
  status?: TaskParseStatusType
}

/** 节点联合类型 */
export type TreeNode = IBranchNode | ILeafNode

/** 分支节点映射表类型 */
export type BranchMap = Record<string, IBranchNode>

/** 叶子节点映射表类型 */
export type LeafMap = Record<string, ILeafNode>

/** 路径到key的映射表类型 */
export type PathToKeyMap = Record<string, string>

/** 节点创建选项接口 */
export interface INodeCreationOptions {
  /** 父目录路径 */
  pwd: string
  /** 节点名称 */
  name: string
  /** 任务类型 */
  taskType: TaskType
  /** 节点层级 */
  level?: number
  /** 任务ID */
  taskId?: string
  /** 任务索引 */
  taskIndex?: number
  /** 任务URL */
  url?: string
}

/** 叶子节点创建选项接口 */
export interface ILeafNodeCreationOptions {
  /** 父目录路径 */
  pwd: string
  /** 文件数据 */
  leafData: {
    fileName: string
    filePath: string
    fileSize: number
    realIndex: number
    url?: string
  }
  /** 任务类型 */
  taskType?: TaskType
  /** 节点层级 */
  level?: number
  /** 任务URL */
  url?: string
  /** 文件真实索引 */
  realIndex?: number
  /** 完整的任务数据 */
  taskData?: any
}

/** 特殊空节点创建选项接口 */
export interface ISpecialEmptyNodeCreationOptions {
  /** 任务标题 */
  taskTitle: string
  /** 任务状态 */
  status: TaskParseStatusType
  /** 任务ID */
  taskId: string
  /** 任务索引 */
  taskIndex: number
  /** 任务类型 */
  taskType: TaskType
  /** 任务URL */
  url: string
  /** 显示名称 */
  displayName?: string
}

/** 节点类型守卫函数 */
export const isBranchNode = (node: TreeNode): node is IBranchNode => {
  return node.type === 'branch'
}

export const isLeafNode = (node: TreeNode): node is ILeafNode => {
  return node.type === 'leaf'
}

export const isSpecialEmptyNode = (node: TreeNode): node is ISpecialEmptyNode => {
  return node.type === 'leaf' && node.isSpecialEmptyNode === true
}

export const isNormalLeafNode = (node: TreeNode): node is INormalLeafNode => {
  return node.type === 'leaf' && node.isSpecialEmptyNode === false
}

/** 节点工具函数类型 */
export interface INodeUtils {
  /** 创建分支节点 */
  createBranchNode(options: INodeCreationOptions): IBranchNode
  /** 创建叶子节点 */
  createLeafNode(options: ILeafNodeCreationOptions): ILeafNode
  /** 创建特殊空节点 */
  createSpecialEmptyNode(options: ISpecialEmptyNodeCreationOptions): ISpecialEmptyNode
  /** 删除节点 */
  deleteNode(nodeKey: string): void
  /** 计算分支节点大小 */
  calculateBranchSize(branch: IBranchNode): number
  /** 计算选中节点大小 */
  calculateSelectedSize(node: TreeNode): number
}

// ==================== 稍后处理任务相关类型 ====================

/**
 * 稍后处理任务的参数类型
 */
export interface HandleTaskLaterParams {
  /**
   * 所有URL与类型数组
   */
  allUrlsWithType: IUrlWithTypeArray
  /**
   * 数据映射
   */
  dataMap: IUrlDataMap
  /**
   * URL额外数据映射
   */
  urlExtraDataMap: ITaskExtraDataType
  /**
   * 选中的文件索引映射
   */
  checkedFileIndexes: TaskFileSelectionMap
  /**
   * 是否有有效任务
   */
  hasValidTasks?: boolean
  /**
   * 选项额外数据
   */
  optionsExtData?: TaskExtDataMap
  /**
   * 场景标识
   */
  scene?: string
}

/**
 * 稍后处理任务的返回结果类型
 */
export interface HandleTaskLaterResult {
  success: boolean
  message: string
  savedCount?: number
  failedCount?: number
}
