/**
 * 窗口相关的Windows宏定义
 */

export enum OptionOfHWNDInAfter {
  HWND_NOTOPMOST = -2,
  HWND_TOPMOST,
  HWND_TOP,
  HWND_BOTTOM
}

export enum Uflag {
  SWP_ASYNCWINDOWPOS = 0x4000,
  SWP_DEFERERASE = 0x2000,
  SWP_DRAWFRAME = 0x0020,
  SWP_FRAMECHANGED = 0x0020,
  SWP_HIDEWINDOW = 0x0080,
  SWP_NOACTIVATE = 0x0010,
  SWP_NOCOPYBITS = 0x0100,
  SWP_NOMOVE = 0x0002,
  SWP_NOOWNERZORDER = 0x0200,
  SWP_NOREDRAW = 0x0008,
  SWP_NOREPOSITION = 0x0200,
  SWP_NOSENDCHANGING = 0x0400,
  SWP_NOSIZE = 0x0001,
  SWP_NOZORDER = 0x0004,
  SWP_SHOWWINDOW = 0x0040
}

export enum CmdShow {
  SW_FORCEMINIMIZE = 11,
  SW_HIDE = 0,
  SW_MAXIMIZE = 3,
  SW_MINIMIZE = 6,
  SW_RESTORE = 9,
  SW_SHOW = 5,
  SW_SHOWDEFAULT = 10,
  SW_SHOWMAXIMIZED = 3,
  SW_SHOWMINIMIZED = 2,
  SW_SHOWMINNOACTIVE = 7,
  SW_SHOWNA = 8,
  SW_SHOWNOACTIVATE = 4,
  SW_SHOWNORMAL = 1
}

export enum WindowMessage {
  WM_CREATE = 0x0001,
  WM_DESTROY = 0x0002,
  WM_MOVE = 0x0003,
  WM_SIZE = 0x0005,
  WM_ACTIVATE = 0x0006,
  WM_SETFOCUS = 0x0007,
  WM_KILLFOCUS = 0x0008,
  WM_ENABLE = 0x000a,
  WM_KEYDOWN = 0x0100,
  WM_KEYUP = 0x0101,
  WM_SYSKEYDOWN = 0x0104,
  WM_MOUSEMOVE = 0x0200,
  WM_SETCURSOR = 0x0020,
  WM_LBUTTONDOWN = 0x0201,
  WM_LBUTTONUP = 0x0202,
  WM_LBUTTONDBLCLK = 0x0203,
  WM_RBUTTONDOWN = 0x0204,
  WM_RBUTTONUP = 0x0205,
  WM_MBUTTONDOWN = 0x0207,
  WM_MOUSEWHEEL = 0x020a,
  WM_XBUTTONDOWN = 0x020B,
  WM_MOUSELEAVE = 0x02a3,
  WM_DPICHANGED = 0x02e0,
  WM_GETMINMAXINFO = 0x0024,
  WM_NCHITTEST = 0x0084,
  WM_NCMOUSEMOVE = 0x00a0,
  WM_NCLBUTTONDOWN = 0x00a1,
  WM_NCLBUTTONUP = 0x00a2,
  WM_NCLBUTTONDBLCLK = 0x00a3,
  WM_NCRBUTTONDOWN = 0x00a4,
  WM_NCRBUTTONUP = 0x00a5,
  WM_NCRBUTTONDBLCLK = 0x00a6,
  WM_NCMBUTTONDOWN = 0x00a7,
  WM_NCMBUTTONUP = 0x00a8,
  WM_NCMBUTTONDBLCLK = 0x00a9,
  WM_WINDOWPOSCHANGED = 0x0047,
  WM_WINDOWPOSCHANGING = 0x0046,
  WM_ACTIVATEAPP = 0x001c,
  WM_DWMCOMPOSITIONCHANGED = 0x031e,
  WM_QUERYENDSESSION = 0x0011
}

export enum GWCmd {
  GW_HWNDFIRST = 0,
  GW_HWNDLAST = 1,
  GW_HWNDNEXT = 2,
  GW_HWNDPREV = 3,
  GW_OWNER = 4,
  GW_CHILD = 5,
  GW_ENABLEDPOPUP = 6
}
