<template>
  <div>
    <div style="margin-bottom: 20px">
      <button
        @click="useMockData1"
        style="margin-right: 10px"
      >
        自动创建一个直链任务
      </button>

      <button @click="useMockData2">模拟两个直链任务</button>
    </div>

    <!-- <DriveList /> -->
    <!-- 
    <AutoCreateTask
      v-if="showTaskComponent"
      :task-data="currentTaskData"
    ></AutoCreateTask> -->
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
// import AutoCreateTask from '@root/modal-renderer/src/components/auto-create-task/auto-create-task.vue'
import type { INewTaskDataItem } from '@root/modal-renderer/types/new-task.type'

// Mock data for testing

// 模拟一个视频直链任务
const mockTaskData1: INewTaskDataItem[] = [
  {
    url: 'https://example.com/file2.mp4',
    fileName: '[NEO·QSW&DAY&FSD]假面三仁 岁ー沙才之人乙 ヲウ RID.mp4',
    fileType: 'mp4',
    fileSize: 208318382080, // ~193.9GB
    taskType: 1,
  },
]

// 模拟两个直链任务
const mockTaskData2: INewTaskDataItem[] = [
  {
    url: 'https://example.com/file1.exe',
    fileName: '[NEO·QSW&DAY&FSD]假面三仁 岁ー沙才之人乙 ヲウ RID.exe',
    fileType: 'exe',
    fileSize: 208318382080, // ~193.9GB
    taskType: 1,
  },
  {
    url: 'https://example.com/file2.mp4',
    fileName: '[NEO·QSW&DAY&FSD]假面三仁 岁ー沙才之人乙 ヲウ RID.mp4',
    fileType: 'mp4',
    fileSize: 208318382080, // ~193.9GB
    taskType: 1,
  },
]

// 当前使用的任务数据
const currentTaskData = ref<INewTaskDataItem[]>([])

// 控制是否显示 AutoCreateTask 组件
const showTaskComponent = ref(false)

// 切换到 mockTaskData1
const useMockData1 = () => {
  currentTaskData.value = mockTaskData1
  showTaskComponent.value = true
}

// 切换到 mockTaskData2
const useMockData2 = () => {
  currentTaskData.value = mockTaskData2
  showTaskComponent.value = true
}
</script>

<style lang="scss" scoped></style>
