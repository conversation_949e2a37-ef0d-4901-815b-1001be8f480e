<script setup lang="ts">
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuRoot,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from 'reka-ui'
import './style.css'
import { onBeforeUnmount, ref, watch } from 'vue'

interface MenuItem {
  label: string
  icon?: string
  disabled?: boolean
  children?: MenuItem[]
  hasSeparator?: boolean
  key: string
  rightText?: string
  rightIcon?: string
  leftText?: string
  checkbox?: boolean
  checked?: boolean
}

interface Props {
  items: MenuItem[]
  contentClass?: string
  itemClass?: string
  checkboxItemClass?: string
  subTriggerClass?: string
  subContentClass?: string
  separatorClass?: string
  subMenuClass?: string
  sideOffset?: number
  subSideOffset?: number
  align?: 'start' | 'center' | 'end'
  side?: 'top' | 'right' | 'bottom' | 'left'
  leftTextClass?: string
  enableScrollClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  contentClass: '',
  itemClass: '',
  checkboxItemClass: '',
  subTriggerClass: '',
  subContentClass: '',
  separatorClass: '',
  subMenuClass: '',
  sideOffset: 4,
  subSideOffset: 8,
  align: 'center',
  side: 'bottom',
  leftTextClass: '',
  enableScrollClose: false,
})

const emits = defineEmits<{
  (e: 'select', key: string): void
  (e: 'open', open: boolean): void
}>()

// 菜单打开状态
const isOpen = ref(false)

// 窗口焦点状态
const isDocumentFocused = ref(true)

// 事件监听器数组，用于管理全局事件
const eventListeners = ref<{ type: string; handler: (e: Event) => void }[]>([])

// 更新文档焦点状态
const updateDocumentFocus = () => {
  isDocumentFocused.value = document.hasFocus()
}

// 处理滚动关闭
const handleScrollClose = () => {
  // 如果禁用了滚动关闭功能，直接返回
  if (!props.enableScrollClose) return

  // 模拟点击外部事件来触发 reka-ui 的 dismiss 机制
  simulatePointerDownOutside()
}

// 处理 document 失焦事件
const handleDocumentBlur = () => {
  // 当 document 失焦时，关闭下拉菜单
  if (isOpen.value) {
    simulatePointerDownOutside()
  }
}

// 监听文档焦点状态变化
watch(isDocumentFocused, (newVal) => {
  if (!newVal) {
    handleDocumentBlur()
  }
})

// 模拟点击外部事件
const simulatePointerDownOutside = () => {
  // 创建一个模拟的 pointerdown 事件
  const simulatedEvent = new PointerEvent('pointerdown', {
    bubbles: true,
    cancelable: true,
    pointerType: 'mouse',
    clientX: 0,
    clientY: 0
  })

  // 手动设置 target 属性
  Object.defineProperty(simulatedEvent, 'target', {
    value: document.body,
    writable: false
  })

  // 分发事件到 document
  document.dispatchEvent(simulatedEvent)
}

// 绑定全局事件监听器
const bindGlobalEvents = () => {
  // 监听滚动相关事件
  if (props.enableScrollClose) {
    const scrollEvents = ['wheel', 'scroll', 'touchmove']
    scrollEvents.forEach(eventType => {
      const handler = () => {
        handleScrollClose()
      }
      eventListeners.value.push({
        type: eventType,
        handler: handler
      })
      document.addEventListener(eventType, handler, { passive: true })
    })
  }

  // 监听窗口焦点状态变化
  const windowFocusEvents = ['focus', 'blur']
  windowFocusEvents.forEach(eventType => {
    const handler = () => {
      updateDocumentFocus()
    }
    eventListeners.value.push({
      type: eventType,
      handler: handler
    })
    window.addEventListener(eventType, handler, { passive: true })
  })
}

// 解绑全局事件
const unbindGlobalEvents = () => {
  // 移除所有存储的事件监听器
  eventListeners.value.forEach(({ type, handler }) => {
    // 根据事件类型决定从哪个对象移除监听器
    if (['focus', 'blur'].includes(type)) {
      // 窗口焦点事件从 window 移除
      window.removeEventListener(type, handler)
    } else {
      // 其他事件从 document 移除
      document.removeEventListener(type, handler)
    }
  })

  // 清空事件监听器数组
  eventListeners.value = []
}

// 监听菜单打开状态变化
watch(() => isOpen.value, (newOpen) => {
  if (newOpen) {
    // 菜单打开时绑定全局事件
    bindGlobalEvents()
  } else {
    // 菜单关闭时解绑全局事件
    unbindGlobalEvents()
  }
})

// 处理菜单状态更新
const handleOpenChange = (open: boolean) => {
  isOpen.value = open
  isDocumentFocused.value = open
  emits('open', open)
}

onBeforeUnmount(() => {
  // 组件卸载时清理事件监听器
  unbindGlobalEvents()
})

</script>

<template>
  <DropdownMenuRoot @update:open="handleOpenChange">
    <DropdownMenuTrigger>
      <slot />
    </DropdownMenuTrigger>
    <DropdownMenuPortal>
      <DropdownMenuContent :side-offset="props.sideOffset" :align="props.align" :side="props.side" :class="[
        'DropdownMenuContent',
        props.contentClass,
      ]">
        <DropdownMenuGroup v-for="item in props.items" :key="item.key">
          <DropdownMenuSub v-if="item.children" :class="[
            'DropdownMenuSub',
            props.subMenuClass,
          ]">
            <DropdownMenuSubTrigger :class="[
              'DropdownMenuSubTrigger',
              props.itemClass,
              props.subTriggerClass,
            ]">
              {{ item.label }}
              <i class="xl-icon-arrow-right DropdownMenuArrow"></i>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent :side-offset="props.subSideOffset" :class="[
                'DropdownMenuSubContent',
                props.subContentClass,
                `DropdownMenuSubContent-${item.key}`
              ]">
                <DropdownMenuItem :disabled="child.disabled" v-for="child in item.children" :key="child.key"
                  @select="emits('select', child.key)" :class="[
                    'DropdownMenuItem',
                    props.itemClass,
                  ]">
                  <span class="LeftSlot" v-if="child.icon">
                    <i :class="child.icon"></i>
                  </span>
                  <span class="LeftSlot" v-if="child.checkbox && child.checked">
                    <i class="xl-icon-drop-down-select"></i>
                  </span>
                  {{ child.label }}
                  <div class="RightSlot" v-if="child.rightText || child.rightIcon">
                    <span v-if="child.rightText">{{ child.rightText }}</span>
                    <i v-if="child.rightIcon" :class="child.rightIcon"></i>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
          <DropdownMenuSeparator :class="['DropdownMenuSeparator', props.separatorClass]" v-if="item.hasSeparator" />
          <DropdownMenuItem :disabled="item.disabled" v-if="!item.children" @select="emits('select', item.key)" :class="[
            'DropdownMenuItem',
            props.itemClass,
          ]">
            <span :class="['LeftSlot', props.leftTextClass]" v-if="item.leftText">
              <span>{{ item.leftText }}</span>
            </span>
            <span class="LeftSlot" v-if="item.icon">
              <i :class="item.icon"></i>
            </span>
            <span class="LeftSlot" v-if="item.checkbox && item.checked">
              <i class="xl-icon-drop-down-select"></i>
            </span>
            {{ item.label }}
            <div class="RightSlot" v-if="item.rightText || item.rightIcon">
              <span v-if="item.rightText">{{ item.rightText }}</span>
              <i v-if="item.rightIcon" :class="item.rightIcon"></i>
            </div>
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenuPortal>
  </DropdownMenuRoot>

</template>
