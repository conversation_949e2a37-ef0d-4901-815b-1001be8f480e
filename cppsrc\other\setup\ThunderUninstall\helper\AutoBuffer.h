#pragma 





class CAutoBuffer
{
	CAutoBuffer(const CAutoBuffer&);
public:
	CAutoBuffer():m_dwBufferSize(0), m_pBuffer(NULL)
	{}
	CAutoBuffer(DWORD dwBufferSize):m_dwBufferSize(dwBufferSize)
	{
		m_pBuffer = new char[dwBufferSize];
	}
	~CAutoBuffer()
	{
		Release();
	}
	HRESULT Release()
	{
		if (m_pBuffer)
		{
			delete[] m_pBuffer;
			m_pBuffer = NULL;
			m_dwBufferSize = 0;
		}
		return S_OK;
	}
	HRESULT ZeroBuffer()
	{
		ZeroMemory(m_pBuffer, m_dwBufferSize * sizeof(char));
		return S_OK;
	}
	DWORD GetBufferSize()
	{
		return m_dwBufferSize;
	}
	void* GetBuffer()
	{
		return m_pBuffer;
	}
private:
	DWORD m_dwBufferSize;
	void* m_pBuffer;
};