<script lang="ts" setup>
import Button from '@root/common/components/ui/button/index.vue'

const props = withDefaults(defineProps<{
  text?: string
  imgSrc?: string
  subtext?: string
  buttonText?: string
  buttonSize?: 'default' | 'sm' | 'lg'
  buttonVariant?: 'default' | 'primary' | 'secondary' | 'warning' | 'weak-lead' | 'outline' | 'ghost'
  buttonLeftIcon?: string
}>(), {
  text: '暂无内容',
  buttonSize: 'default',
  buttonVariant: 'primary'
})

const emit = defineEmits<{
  (e: 'button-click'): void
}>()
</script>

<template>
  <div class="empty-holder-container">
    <inline-svg class="default-img" :src="require('@root/common/assets/img/ic_pan_empty_light.svg')" />

    <div class="text-wrapper">
      <div v-if="text" class="default-text">{{ text }}</div>
      <div v-if="text" class="default-subtext">{{ subtext }}</div>
    </div>

    <Button
      v-if="buttonText"
      :size="buttonSize"
      :variant="buttonVariant"
      @click="emit('button-click')"
    >
      <i v-if="buttonLeftIcon" :class="`button-left-icon ${buttonLeftIcon}`"></i>
      {{ buttonText }}
    </Button>
  </div>
</template>

<style lang="scss" scoped>
.empty-holder-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 18px;

  .default-img {
    width: 140px;
    height: 140px;
  }

  .text-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 6px;

    .default-text {
      font-size: 13px;
      color: var(--font-font-1);
    }
    .default-subtext {
      font-size: 11px;
      color: var(--font-font-3);
    }
  }

  .button-left-icon {
    margin-right: 6px;
  }
}
</style>