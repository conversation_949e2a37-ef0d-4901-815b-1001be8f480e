import { Reactive, reactive } from "vue"

export enum ETabId {
  ALL = 'all',
  CLOUD_ADD = 'cloud-add',
  TRANSFER_FILE = 'transfer-file',
  UPLOAD = 'upload'
}

export interface ITabItem {
  id: ETabId              // 唯一标识
  title: string           // 标题
  count: number           // 数量
  visible: boolean        // 是否可见
}

export class TabsManager {
  private static _instance: TabsManager

  static getInstance () {
    if (TabsManager._instance) {
      return TabsManager._instance
    } else {
      TabsManager._instance = new TabsManager()
      return TabsManager._instance
    }
  }

  private tabs = reactive<ITabItem[]>([]);
  private currentTab!: Reactive<ITabItem>

  init () {
    this.tabs.splice(0, this.tabs.length)
    this.tabs.push(...[
      {
        id: ETabId.ALL,
        title: '云盘文件',
        count: 0,
        visible: true,
      },
      {
        id: ETabId.CLOUD_ADD,
        title: '云添加',
        count: 0,
        visible: true,
      },
      {
        id: ETabId.TRANSFER_FILE,
        title: '转存',
        count: 0,
        visible: false,
      },
      {
        id: ETabId.UPLOAD,
        title: '上传',
        count: 0,
        visible: false,
      }
    ])

    this.currentTab = reactive({
      id: ETabId.ALL,
      title: '所有',
      count: 0,
      visible: true,
    })

    return this.getTabs()
  }

  getTabs () {
    return this.tabs.filter(tab => tab.visible)
  }

  getCurrentTab () {
    return this.currentTab
  }

  setCurrentTab (tabId: ETabId) {
    const menu = this.findTabItemById(this.tabs, tabId);

    if (menu) {
      this.updateCurrentTab(menu);
    }
  }

  updateTabCount (tabId: ETabId, count: number) {
    const tab = this.tabs.find(tab => tab.id === tabId)

    if (tab) {
      tab.count = count
    }
  }

  private updateCurrentTab (tab: ITabItem) {
    Object.assign(this.currentTab, tab)
  }

  private findTabItemById(tabs: ITabItem[], targetId: ETabId): ITabItem | undefined {
    // 遍历当前层级的菜单项
    for (const item of tabs) {
      if (item.id === targetId) {
        return item; // 找到目标项，返回
      }
    }

    return undefined; // 如果遍历完都没有找到，返回 undefined
  }
}
