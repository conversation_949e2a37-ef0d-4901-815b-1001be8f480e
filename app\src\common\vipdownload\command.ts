export interface Command {
  execute(): Promise<void>;
  cancel(): Promise<void>;
}

export interface TokenInfo {
  flux_need: number;
  expire: number;
  time_interval: number;
  result: number;
  gcid: string;
  simple_msg: string;
  message: string;
  flux_deduction_result: number;
  token?: string;
  sec_result?: number; // (0 表示可秒下，200-300 不可秒下)
  filter_result?: number; // 0 表示非敏感 10-30 敏感资源
}
export interface TokenResponse{
  client_sequence?: number;
  flux_remain?: number;
  flux_need?: number;
  flux_capacity?: number;
  result: number;
  message?: string;
  task_infos?: TokenInfo[];
}

export interface SpeedTaskInfos {
  url: string; // 资源URL。必须为UTF8编码。bt的url格式为bt://$infohash/$index.
  filename: string;
  gcid: string;
  cid: string;
  filesize: number;
  file_index?: number; // bt子文件索引。非bt任务无此字段。有该字段时，上层的infohash字段必须存在且不为空。
  refer_url: string;
}

export interface SpeedBody {
  peer_id: string;
  infohash?: string;
  bt_title?: string;
  task_infos: SpeedTaskInfos[];
}

export interface VipGlobalData {
  peerid: string;
  versionName: string;
  versionCode: number;
  clientName: string;
  host: string;
  seq: number;
}

export enum ForbiddenStatus {
  None = 0, // 可能还在查询中，还不确定是否封禁
  YesForbidden, // 确定已经被封禁
  NoForbidden, // 未封禁
}

export enum SecStatus {
  None = 0, // 可能还在查询中，还不确定是否可加速
  YesSec, // 确定已经可秒下
  NoSec, // 不可秒下
}

export enum AccVipType {
  None = 0, // 非会员
  Vip,
  Super,
}

export interface AccInfo {
  forbiddenStatus: ForbiddenStatus; //
  secStatus: SecStatus;
  isVipChannel: boolean; // 会员加速是否开启
  isSuperVipChannel: boolean;
}

export interface IVipGlobalHandler {
    getData(): VipGlobalData;

    onAccInfoChange(taskId: number, info: AccInfo): void;
}