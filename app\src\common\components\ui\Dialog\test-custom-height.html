<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义输入框高度演示</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .demo-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            background-color: #28a745;
            color: white;
            margin-right: 10px;
        }
        .demo-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        /* 模拟Prompt组件的样式 */
        .mock-prompt {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: white;
            margin-top: 20px;
            max-width: 600px;
        }
        .mock-textarea {
            width: 100%;
            padding: 14px;
            border: 2px solid #4a90e2;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            outline: none;
            transition: all 0.25s ease;
            background-color: #f8f9fa;
            resize: vertical;
            font-family: monospace;
            line-height: 1.5;
        }
        .mock-textarea.error {
            border-color: #dc3545;
            background-color: #fff8f8;
        }
        .mock-textarea:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        /* 错误信息容器 */
        .error-message-container {
            min-height: 24px;
            max-height: 24px;
            transition: none;
            margin-top: 8px;
        }
        .error-message-container.has-error .error-message-content {
            opacity: 1;
            transform: translateY(0);
        }
        .error-message-content {
            color: #dc3545;
            font-size: 12px;
            line-height: 1.4;
            padding: 2px 0;
            opacity: 0;
            transform: translateY(-8px);
            transition: all 0.25s ease;
        }
        .error-message-container:not(.has-error)::after {
            content: '\00A0';
            color: transparent;
            font-size: 12px;
            line-height: 1.4;
            padding: 2px 0;
        }
        
        .info-box {
            background: #e6f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-size: 14px;
        }
        .stats {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <h1>📏 自定义输入框高度演示</h1>
            <p>展示如何设置400px高度的textarea输入框，支持多行文本和实时验证</p>
            
            <div class="demo-section">
                <h3>🎯 功能特点</h3>
                <ul>
                    <li>📏 自定义高度：400px高的文本区域</li>
                    <li>📝 多行输入：支持换行和长文本</li>
                    <li>⚡ 实时验证：字符数和行数限制</li>
                    <li>🔧 固定高度：错误信息区域不跳动</li>
                    <li>⌨️ 快捷键：Ctrl+Enter确认，Escape取消</li>
                    <li>🎨 等宽字体：代码编辑友好</li>
                </ul>
                
                <div class="info-box">
                    <strong>🎛️ 操作说明：</strong><br>
                    • <kbd>Enter</kbd> - 换行<br>
                    • <kbd>Ctrl</kbd> + <kbd>Enter</kbd> - 确认提交<br>
                    • <kbd>Escape</kbd> - 取消<br>
                    • 限制：最多20行，最多1000字符
                </div>
                
                <button class="demo-btn" @click="showDemo">
                    🚀 体验400px高度输入框
                </button>
                
                <div v-if="showTextarea" class="mock-prompt">
                    <textarea 
                        v-model="textareaInput" 
                        class="mock-textarea"
                        :class="{ error: textareaError }"
                        :style="{ height: '400px' }"
                        placeholder="这是一个400px高的文本区域，可以输入多行文本...
试试输入一些内容：
- 支持多行文本
- 实时字符计数
- 实时行数统计
- 错误状态提示

使用 Ctrl+Enter 可以快速确认哦！"
                        @input="validateTextarea"
                        @keydown="handleKeydown"
                    ></textarea>
                    
                    <div 
                        class="error-message-container"
                        :class="{ 'has-error': textareaError }"
                    >
                        <div v-if="textareaError" class="error-message-content">
                            {{ textareaErrorMessage }}
                        </div>
                    </div>
                    
                    <div class="stats">
                        📊 统计信息：
                        字符数 {{ textareaInput.length }}/1000 |
                        行数 {{ textareaInput.split('\n').length }}/20 |
                        状态: {{ textareaError ? '❌ 有错误' : '✅ 正常' }}
                    </div>
                    
                    <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 4px; font-size: 12px;">
                        💡 <strong>提示：</strong> 这个演示展示了如何在Vue3 Dialog组件中使用400px高度的textarea，
                        配合固定高度的错误信息区域，确保界面不会因验证信息的出现/消失而跳动。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        
        createApp({
            setup() {
                const showTextarea = ref(false);
                const textareaInput = ref('');
                const textareaError = ref(false);
                const textareaErrorMessage = ref('');
                
                const showDemo = () => {
                    showTextarea.value = !showTextarea.value;
                    if (showTextarea.value) {
                        // 设置一些示例文本
                        textareaInput.value = `欢迎使用400px高度的文本输入框！

这是一个功能丰富的文本区域：
✅ 支持多行文本输入
✅ 实时验证和错误提示
✅ 固定高度，避免界面跳动
✅ 快捷键支持（Ctrl+Enter确认）

试试修改这段文本，观察实时验证效果...`;
                        
                        // 触发一次验证
                        setTimeout(() => {
                            validateTextarea();
                        }, 100);
                    }
                };
                
                const validateTextarea = () => {
                    const value = textareaInput.value;
                    const lines = value.split('\n').length;
                    
                    if (!value.trim()) {
                        textareaError.value = true;
                        textareaErrorMessage.value = '⚠️ 请输入内容';
                    } else if (lines > 20) {
                        textareaError.value = true;
                        textareaErrorMessage.value = `❌ 行数过多！最多20行，当前${lines}行`;
                    } else if (value.length > 1000) {
                        textareaError.value = true;
                        textareaErrorMessage.value = `❌ 内容过长！最多1000字符，当前${value.length}字符`;
                    } else {
                        textareaError.value = false;
                        textareaErrorMessage.value = '';
                    }
                };
                
                const handleKeydown = (e) => {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        if (!textareaError.value && textareaInput.value.trim()) {
                            alert(`✅ 模拟确认成功！\n字符数: ${textareaInput.value.length}\n行数: ${textareaInput.value.split('\n').length}`);
                        }
                    } else if (e.key === 'Escape') {
                        showTextarea.value = false;
                        textareaInput.value = '';
                    }
                };
                
                return {
                    showTextarea,
                    textareaInput,
                    textareaError,
                    textareaErrorMessage,
                    showDemo,
                    validateTextarea,
                    handleKeydown
                };
            }
        }).mount('#app');
    </script>
</body>
</html> 