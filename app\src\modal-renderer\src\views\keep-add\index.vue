<script lang="ts" setup>
import { onMounted, ref, computed, watch } from 'vue'
import { usePositionMixinComponent } from '@root/modal-renderer/src/common/mixins'
import { PopUpNS } from '@root/common/pop-up'
import * as PopUpTypes from '@root/common/pop-up/types'
import { taskExtraClientFunc } from '@/common/impl-task'
import { TaskBase, TaskType, BtFileDownloadInfo } from '@root/common/task/base'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import TdTable from '@root/common/components/ui/table/table.vue'
import { ThunderUtil } from '@root/common/utils'
import { useKeepAdd } from './index'
import { BtSubFileStatus } from '@root/common/task/base';
import { ILeaf, IBranch } from './type'


// 使用基类的逻辑
const { overridePosition, resizeToFitContent } = usePositionMixinComponent()

const defaultPositionOptions = {
  autoSize: true,
  show: true,
  windowWidth: 680,
  windowHeight: 592,
  relatePos: PopUpTypes.RelatePosType.CenterParent,
}

// 重写控制位置基类非响应式数据
overridePosition(defaultPositionOptions)

interface IProps {
  options: {
    taskId: number,
  }
}

const {
  downloadTotal,
  currentTaskInfo,
  initTableData
} = useKeepAdd()

const props = defineProps<IProps>()
const emits = defineEmits(['close'])

const columns = ref([
  {
    label: '文件名',
    prop: 'name',
    width: 'auto',
    sortable: true,
  },
  {
    label: '进度',
    prop: 'progressStr',
    width: '100px',
    sortable: false,
  },
  {
    label: '大小',
    prop: 'fileSize',
    width: '100px',
    sortable: true,
  },
])

const disabledKeys = ref<string[]>([])
const defaultCheckedKeys = ref<string[]>([])
const checkedKeys = ref<string[]>([])
const branchMapKeys = ref<string[]>([])

let sourceLeafMap: {[key: string]: ILeaf;} | null = null
const sourceBranchMap = ref<{[key: string]: IBranch;} | null>(null)
// const sourceLeafMap = ref<{[key: string]: ILeaf;} | null>(null)
const tableData = ref<(IBranch | ILeaf)[]>([])
const isUpdateKeys = ref(false)

const infoText = computed(() => {
  const size = ThunderUtil.bytesToSize(currentTaskInfo.value?.fileSize ?? 0, 2, undefined, true)
  return `已选 ${checkedKeys.value.length}/${downloadTotal.value} 个文件 · ${size}`
})

const fileIcon = computed(() => {
  const type = currentTaskInfo.value?.taskType ?? TaskType.Bt
  return TaskUtilHelper.getTaskIcon('', type)
})

function getFileSubIcon (file: any) {
  // console.log('>>>>>>>>>> file', file) FIXME: 此处每次展开点击都会执行
  let type: TaskType | undefined = undefined
  if (file.type === 'branch' || file?.taskType === TaskType.Bt) {
    type = TaskType.Group
  }
  return TaskUtilHelper.getTaskIcon(file?.name, type)
}

console.log('>>>>>>>>>>>>>>>> props', props)

const handleClose = () => {
  console.log('>>>>>>>>>>> 关闭弹窗')
  emits('close', {scene: 'close'})
}

const handleConfirm = async () => {
  console.log('>>>>>>>>>>> 确认', checkedKeys.value)
  const btMap: {[key: string]: number[]} = {}
  const commonTaskIds: number[] = []
  const isBtTask = currentTaskInfo.value?.taskType === TaskType.Bt
  const currentTaskId = currentTaskInfo.value?.taskId
  for (const key of checkedKeys.value) {
    if(sourceLeafMap && sourceLeafMap.hasOwnProperty(key)) {
      // 判断是否存在rootBtTaskId
      const leaf = sourceLeafMap[key]
      console.log('>>>>>>>>>>>>> leaf', leaf)
      const rootBtTaskId = isBtTask ? currentTaskId : leaf.rootBtTaskId
      if (rootBtTaskId) {
        if (!btMap[rootBtTaskId]) {
          btMap[rootBtTaskId] = []
        }
        typeof leaf.realIndex === 'number' && btMap[rootBtTaskId].push(leaf.realIndex)
      } else {
        // 普通任务
        leaf.taskId && commonTaskIds.push(leaf.taskId)
      }
    }
  }

  
  if (!currentTaskId) return
  if (currentTaskInfo.value?.taskType === TaskType.Bt) {
    const indexList = btMap[currentTaskId]
    if (indexList) {
      taskExtraClientFunc.updateDownloadIndex(currentTaskId, indexList)
    }
  } else if (currentTaskInfo.value?.taskType === TaskType.Group) {
    // 组任务
    // 获取对应的key,合成数组, 并且转换成number
    const subTaskKeys = Object.keys(btMap).map(key => parseInt(key))
    console.log('>>>>>>>>>>>>>>>> subTaskKeys', subTaskKeys)
    commonTaskIds.push(...subTaskKeys)
    await taskExtraClientFunc.updateGroupSubSelectTask(currentTaskId, commonTaskIds)
    const btInfos: {taskId: number, indexs: number[]}[] = []
    for (const key in btMap) {
      const indexList = btMap[key]
      if (indexList) {
        btInfos.push({taskId: parseInt(key), indexs: indexList})
      }
    }
    if (btInfos.length) {
      taskExtraClientFunc.updateGroupBtSubSelectTask(currentTaskId, btInfos)
    }
  }

  console.log('>>>>>>>>>>>>> currentTaskId', currentTaskId, btMap, commonTaskIds)
  // 判断是否为bt子文件
  emits('close', {scene: 'confirm'})
}

const updateCheckedKeys = (checkedKey, key, checked) => {
  checkedKeys.value = checkedKey.filter(item => !branchMapKeys.value.includes(item))
  isUpdateKeys.value = judgeChangeKeys(checkedKeys.value, defaultCheckedKeys.value) && checkedKeys.value.length > 0
}

function judgeChangeKeys(arr1: string[], arr2: string[]) {
  // 检查是否为同一个引用
  if (arr1 === arr2) return false;
  // 检查是否都是数组
  if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
  // 检查长度是否相同
  if (arr1.length !== arr2.length) return true;
  return !arr1.every(item => arr2.includes(item))
}

onMounted(async () => {
  // taskInfo.value = await taskExtraClientFunc.getTaskBase(props.options.taskId)
  // downloadTotal.value = await taskExtraClientFunc.getDownloadTotal(props.options.taskId)
  // console.log('>>>>>>>>>>>>> v', taskInfo.value)
  const { branchMap, leafMap } = await initTableData(props.options.taskId)
  console.log('>>>>>>>>>> res', branchMap)
  sourceBranchMap.value = branchMap
  sourceLeafMap = leafMap
  if (branchMap) {
    tableData.value = branchMap['']?.children ?? []
  }
  // 获取所有branch的key
  for (const key in branchMap) {
    branchMapKeys.value.push(key)
  }
  const defaultChecked: string[] = []
  const disabledKey: string[] = []
  if (leafMap) {
    for (const key in leafMap) {
      if (leafMap.hasOwnProperty(key)) {
        const leaf = leafMap[key]
        if (leaf.download) {
          defaultChecked.push(leaf.key)
        }
        if (leaf.fileStatus === BtSubFileStatus.Complete) {
          disabledKey.push(leaf.key)
        }
      }
    }
  }
  disabledKeys.value = disabledKey
  defaultCheckedKeys.value = defaultChecked
  checkedKeys.value = JSON.parse(JSON.stringify(defaultChecked))
})
</script>



<template>
  <div class="keep-add">
    <div class="keep-add__header draggable">
      <div class="keep-add__title none-draggable">
        <div class="keep-add__icon">
          <img src="./assets/xl-icon.svg" alt="">
        </div>
        添加文件
      </div>
      <div class="keep-add__close none-draggable">
        <Button variant="ghost" is-icon size="sm" @click="handleClose">
          <i class="xl-icon-general-close-l"></i>
        </Button>
      </div>
    </div>

    <div class="keep-add__file">
      <div class="keep-add__file-icon file-icon-type is-large" :class="fileIcon"></div>
      <div class="keep-add__file-right ">
        <div class="keep-add__file-name">
          {{ currentTaskInfo?.taskName }}
        </div>
        <div class="keep-add__file-info">
          {{ infoText }}
        </div>
      </div>
    </div>
    
    <div class="keep-add__body-wrapper">
      <div class="keep-add__body">
        <td-table
          ref="table"
          :columns="columns"
          :data="tableData"
          :height="252"
          :rowHeight="40"
          :checkable="true"
          :headerEnabled="true"
          :disabled-keys="disabledKeys"
          :default-checked-keys="defaultCheckedKeys"
          @checked-change="updateCheckedKeys"
        >
          <template #icon="{ prop, value, row }">
            <i class="file-icon-type is-small-middle" :class="getFileSubIcon(row)"></i>
          </template>
          <template #label="{ prop, value, row }">
            <div>
              <p class="keep-add__table-name">{{ row.name }}</p>
            </div>
          </template>
          <template #default="{ prop, value, row }">
            <template v-if="prop === 'progressStr'">
              <p class="keep-add__table-size">{{ row.data ? row.data.progressStr : row.progressStr }}</p>
            </template>
            <template v-if="prop === 'fileSize'">
              <p class="keep-add__table-size">{{ ThunderUtil.bytesToSize(value, 2) }}</p>
            </template>
          </template>
        </td-table>
      </div>
    </div>
    
    <div class="keep-add_footer">
      <Button class="keep-add__confirm" size="lg" :disabled="!isUpdateKeys" @click="handleConfirm">确认</Button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.keep-add__table-name {
  color: var(--font-font-1);
  font-size: 13px;
  line-height: 32px;
  max-width: 335px;
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
.keep-add__table-progress {

}
.keep-add__table-size {

}
.keep-add {
  --keep-add-padding-left-right: 24px;

  background: var(--background-background-elevated);
  width: 680px;
  height: 592px;
  .draggable {
    -webkit-app-region: drag;
  }

  .none-draggable {
    -webkit-app-region: no-drag;
  }

  .keep-add__header {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    margin: 0 var(--keep-add-padding-left-right);
  }

  .keep-add__title {
    color: var(--font-font-1);
    margin-right: 8px;
    display: flex;
    align-items: center;
  }
  .keep-add__icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;

    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  .keep-add__file {
    display: flex;
    align-items: center;
    height: 80px;
    padding: 0 var(--keep-add-padding-left-right) 0 var(--keep-add-padding-left-right);
    margin-top: 8px;
  }

  .keep-add__file-icon {
    flex-shrink: 0;
  }

  .keep-add__file-right {
    margin-left: 16px;
  }

  .keep-add__file-name {
    color: var(--font-font-1);
    font-size: 14px;
    line-height: 22px;
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 1;
    -webkit-line-clamp: 1;
  }
  .keep-add__file-info {
    color: var(--font-font-3);
    font-size: 12px;
    line-height: 20px;
    margin-top: 4px;
  }

  .keep-add__body-wrapper {
    margin-top: 12px;
    padding: 0 var(--keep-add-padding-left-right);
    height: 340px;
  }

  .keep-add__body {
    width: 100%;
    height: 100%;
    border: 1px solid var(--border-border-2);
    border-radius: var(--border-radius-L);
  }

  .keep-add_footer {
    margin-top: 24px;
    padding: 0 var(--keep-add-padding-left-right);
  }

  .keep-add__confirm {
    width: 100%;
  }
}
</style>