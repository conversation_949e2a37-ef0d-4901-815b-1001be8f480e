#!/usr/bin/env node

/**
 * CI 构建脚本 - 专门用于Windows打包机
 * 整合了SASS修复和构建流程，确保稳定性
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function log(message) {
  console.log(`[CI BUILD] ${message}`);
}

function execWithLog(command, options = {}) {
  log(`执行: ${command}`);
  try {
    const result = execSync(command, {
      stdio: 'inherit',
      encoding: 'utf8',
      ...options
    });
    return result;
  } catch (error) {
    log(`错误: ${error.message}`);
    throw error;
  }
}

async function main() {
  log('开始 CI 构建流程...');
  
  try {
    // 1. 准备SASS环境
    log('步骤 1: 准备SASS环境');
    execWithLog('node scripts/prepare-sass-build.js');
    
    // 2. 构建Electron组件
    log('步骤 2: 构建Electron组件');
    execWithLog('npm run build:electron');
    
    // 3. 构建插件（使用优化的环境变量）
    log('步骤 3: 构建Pan插件');
    execWithLog('npm run build:pan:plugin');
    
    log('步骤 4: 构建Player插件');
    execWithLog('npm run build:player:plugin');
    
    // 4. 后处理
    log('步骤 5: 后处理 - 构建ASAR');
    execWithLog('npm run postbuild');
    
    log('✅ CI 构建完成!');
    
  } catch (error) {
    log(`❌ 构建失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行构建
main().catch(error => {
  log(`❌ 构建异常: ${error.message}`);
  process.exit(1);
}); 