export namespace API_TASK {
    /**
     * - DEFAULT: DEFAULT 创建任务，当id已存在时返回错误
     *  - RETRY: RETRY 重试这个id对应的任务，任务id不变
     *  - RETRY_WITH_NEW_ID: RETRY_WITH_NEW_ID 重试这个id对应的任务，更新任务id，并删掉旧任务
     */
    export type CreateTaskRequestCreateType = "DEFAULT" | "RETRY" | "RETRY_WITH_NEW_ID";
    /**
     * ClearTaskListResponse 清空任务列表响应报文
     */
    export interface DriveClearTaskBufferAdminResponse {
    }
    /**
     * ClearTaskListRequest 清空任务列表请求报文
     */
    export interface DriveClearTaskListRequest {
        /**
         * 任务类型, 必需传入
         */
        type?: string;
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 仅清空指定状态的任务, 不传入则清空全部
         */
        phases?: DrivePhaseType[];
        /**
         * 仅清空指定客户端的任务, 不传入则清空全部
         */
        client_id?: string;
        /**
         * 是否同时删除任务关联的文件
         */
        delete_files?: boolean;
    }
    /**
     * ClearTaskListResponse 清空任务列表响应报文
     */
    export interface DriveClearTaskListResponse {
    }
    /**
     * ClearTasksAdminRequest 清理任务admin请求
     */
    export interface DriveClearTasksAdminRequest {
        /**
         * 用户id，必须传入
         */
        user_id?: string;
        /**
         * 用户空间
         */
        space?: string;
        /**
         * filters 过滤器
         * 结构是: 参数名->过滤操作
         * json 结构:
         * {"start_time":{"gt":"2006-01-02T15:04:05.999Z07:00"}}
         */
        filter?: string;
        /**
         * 任务id
         */
        task_id?: string;
        /**
         * params 扩展参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * ClearTasksAdminResponse 清理任务admin响应
     */
    export interface DriveClearTasksAdminResponse {
        /**
         * PhaseType 状态
         */
        phase?: DrivePhaseType;
    }
    /**
     * ClearTasksBufferAdminRequest 清理任务admin请求
     */
    export interface DriveClearTasksBufferAdminRequest {
        /**
         * 用户id，必须传入
         */
        user_id?: string;
        /**
         * type 任务类型
         * 如果type为空，那么就是获取所有任务
         */
        type?: string;
        /**
         * 任务 id
         */
        task_ids?: string[];
    }
    /**
     * CreateTaskRequest 创建任务请求报文
     */
    export interface DriveCreateTaskRequest {
        /**
         * spcace 空间
         */
        space?: string;
        /**
         * type 任务类型
         * decompress 压缩任务
         */
        type?: string;
        /**
         * file_name 文件名
         */
        file_name?: string;
        /**
         * file_size 任务文件总大小
         */
        file_size?: string; // int64
        /**
         * params 任务内容
         */
        params?: {
            [name: string]: string;
        };
        /**
         * name 任务名
         */
        name?: string;
        /**
         * id 基于这个id对应的任务创建一个新任务，并删掉旧任务
         */
        id?: string;
        /**
         * create_type 任务创建方式
         */
        create_type?: CreateTaskRequestCreateType;
        /**
         * status_ids  重试指定的子任务id
         */
        status_ids?: string[];
    }
    /**
     * CreateTaskResponse 创建任务响应报文
     */
    export interface DriveCreateTaskResponse {
        task?: DriveTask;
    }
    /**
     * DeleteTaskResponse 删除任务响应
     */
    export interface DriveDeleteTasksResponse {
    }
    /**
     * GetStatusesResponse 获取任务状态信息。已过期
     */
    export interface DriveGetStatusesResponse {
        /**
         * statuses 返回的状态列表
         */
        statuses?: DriveTaskStatus[];
    }
    /**
     * GetTaskStatusesRequest 获取任务列表请求
     */
    export interface DriveGetTaskStatusesResponse {
        /**
         * statuses 任务详情
         */
        statuses?: DriveTaskStatus[];
        /**
         * next_page_token 偏移量，下一次拉取数据时的标记
         */
        next_page_token?: string;
        /**
         * expires_in 任务状态列表过期时间(单位:秒)
         */
        expires_in?: number; // int32
    }
    /**
     * 图片尺寸
     * - SIZE_DEFAULT: 默认 具体行为由服务端定义
     *  - SIZE_SMALL: 小图
     *  - SIZE_MEDIUM: 中图
     *  - SIZE_LARGE: 大图
     *  - SIZE_BIG: 兼容TV端视频缩略图 width*height=720*406
     */
    export type DriveImageSize = "SIZE_DEFAULT" | "SIZE_SMALL" | "SIZE_MEDIUM" | "SIZE_LARGE" | "SIZE_BIG";
    /**
     * ListTasksResponse 任务列表响应
     */
    export interface DriveListTasksResponse {
        /**
         * tasks 任务列表
         */
        tasks?: DriveTask[];
        /**
         * next_page_token 偏移量，下一次拉取数据时的标记
         */
        next_page_token?: string;
        /**
         * expires_in 任务列表过期时间(单位:秒)
         */
        expires_in?: number; // int32
        /**
         * expires_in_ms 任务列表过期时间(单位:毫秒)
         */
        expires_in_ms?: number; // int32
    }
    export interface DriveOnMessageResponse {
        /**
         * 返回状态码
         */
        code?: OnMessageResponseCode;
        /**
         * 错误描述
         */
        error?: string;
    }
    /**
     * PhaseType 文件阶段类型
     * - PHASE_TYPE_PENDING: 等待中
     *  - PHASE_TYPE_RUNNING: 进行中
     *  - PHASE_TYPE_ERROR: 失败
     *  - PHASE_TYPE_COMPLETE: 完成
     *  - PHASE_TYPE_PAUSED: 暂停
     */
    export type DrivePhaseType = "PHASE_TYPE_UNKNOW" | "PHASE_TYPE_PENDING" | "PHASE_TYPE_RUNNING" | "PHASE_TYPE_ERROR" | "PHASE_TYPE_COMPLETE" | "PHASE_TYPE_PAUSED";
    /**
     * Task 任务。比如离线任务
     */
    export interface DriveTask {
        /**
         * kind 状态类型 固定值：drive#task
         */
        kind?: string;
        /**
         * id  任务id
         */
        id?: string;
        /**
         * name 任务名称。例如：xxxx种子离线下载
         */
        name?: string;
        /**
         * type 任务类型。例如，
         * - offline 离线任务
         * - transcoding 转码任务
         */
        type?: string;
        /**
         * user_id 所属用户
         */
        user_id?: string;
        /**
         * statuses 任务状态。如果是BT文件，那么status会有多个（列举bt种子内的文件并创建离线任务）。
         */
        statuses?: DriveTaskStatus[];
        /**
         * status_size 子状态个数
         */
        status_size?: number; // int32
        /**
         * params 任务内容
         */
        params?: {
            [name: string]: string;
        };
        /**
         * file_id 任务对应的文件ID。如果是BT文件，那么task对应的file应该是文件夹。
         */
        file_id?: string;
        /**
         * file_name 任务对应的文件名。
         */
        file_name?: string;
        /**
         * file_size 文件大小
         */
        file_size?: string; // int64
        /**
         * message 任务状态说明，用于客户端展现
         */
        message?: string;
        /**
         * created_time 任务创建时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        created_time?: string;
        /**
         * updated_time 任务完成时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        updated_time?: string;
        /**
         * third_task_id 三方任务ID
         */
        third_task_id?: string;
        /**
         * phase 运行阶段
         */
        phase?: DrivePhaseType;
        /**
         * progress 进度 % 百分比
         */
        progress?: number; // float
        /**
         * icon_link 任务图标
         */
        icon_link?: string;
        /**
         * callback 任务完成之后的通知地址（给用户）。
         * 例如：可以是mqtt的地址、https地址等
         */
        callback?: string;
        /**
         * 引用的资源
         */
        reference_resource?: ProtobufAny;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * TaskMessage 任务消息, 用于异步通知
     */
    export interface DriveTaskMessage {
        /**
         * id  消息id
         */
        id?: string;
        /**
         * event 事件类型, 例如: 创建/开始执行/完成/错误/自定义等
         */
        event?: string;
        /**
         * round 本消息推送的次数
         */
        round?: number; // int32
        /**
         * created_time 本消息创建时间，日期时间格式使用RFC 3339格式精确到毫秒
         */
        created_time?: string;
        /**
         * task_id 任务id
         */
        task_id?: string;
        /**
         * task_type 任务类型, 例如: upload/offline
         */
        task_type?: string;
        /**
         * status_ids 关联子任务id列表
         */
        status_ids?: string[];
        /**
         * meta 其它附加信息
         */
        meta?: {
            [name: string]: string;
        };
        /**
         * user_id 所属用户id
         */
        user_id?: string;
        /**
         * project_id 所属项目id
         */
        project_id?: string;
        /**
         * space 所属空间
         */
        space?: string;
    }
    /**
     * Status 状态
     */
    export interface DriveTaskStatus {
        /**
         * kind 固定值：drive#status
         */
        kind?: string;
        /**
         * id 状态id
         */
        id?: string;
        /**
         * file_id 归属的文件id
         */
        file_id?: string;
        /**
         * name 状态名称: 离线下载、解压等
         */
        name?: string;
        /**
         * icon_link 图标
         */
        icon_link?: string;
        /**
         * progress 进度 % 百分比
         */
        progress?: number; // float
        /**
         * file_name bt任务内子文件对应的文件名。
         * 如果包含文件夹，那么file_name的形式是：/foo/bar/name.mov
         */
        file_name?: string;
        /**
         * file_size 文件大小
         */
        file_size?: string; // int64
        /**
         * phase 运行阶段
         */
        phase?: DrivePhaseType;
        /**
         * message 任务状态说明，用于客户端展现。
         * 如果是bt文件，这里就是单个任务的状态信息。
         */
        message?: string;
        /**
         * created_time 状态创建时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        created_time?: string;
        /**
         * updated_time 状态完成时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        updated_time?: string;
        /**
         * params 响应参数
         * 如果是离线任务，那么这里面包含所有子文件信息
         */
        params?: {
            [name: string]: string;
        };
        /**
         * 引用的资源
         */
        reference_resource?: ProtobufAny;
    }
    /**
     * UpdateTaskRequest 更新任务请求报文
     */
    export interface DriveUpdateTaskRequest {
        /**
         * spcace 空间
         */
        space?: string;
        /**
         * type 任务类型
         * decompress 解压任务
         */
        type?: string;
        /**
         * 任务id
         */
        id?: string;
        /**
         * phase 状态
         */
        phase?: DrivePhaseType;
        /**
         * progress 进度
         */
        progress?: number; // float
        /**
         * task的文件id
         */
        file_id?: string;
        /**
         * file_name 对应的文件名
         */
        file_name?: string;
        /**
         * file_size 文件大小
         */
        file_size?: string; // int64
        /**
         * set_params 设置参数
         * is_private：是否私密空间
         */
        set_params?: {
            [name: string]: string;
        };
        /**
         * delete_params 删除参数
         * is_private：是否私密空间
         */
        delete_params?: string[];
        /**
         * message 错误提示信息
         */
        message?: string;
        /**
         * name 任务名
         */
        name?: string;
    }
    /**
     * UpdateTaskResponse 更新任务响应报文
     */
    export interface DriveUpdateTaskResponse {
    }
    export interface GooglerpcStatus {
        code?: number; // int32
        message?: string;
        details?: ProtobufAny[];
    }
    /**
     * - CONTINUE: CONTINUE 继续执行后续Runner
     *  - BREAK: BREAK 任务结束,不需要继续执行后续Runner
     *  - RETRY: RETRY 需要重试当前Runner
     */
    export type OnMessageResponseCode = "CONTINUE" | "BREAK" | "RETRY";
    /**
     * `Any` contains an arbitrary serialized protocol buffer message along with a
     * URL that describes the type of the serialized message.
     * 
     * Protobuf library provides support to pack/unpack Any values in the form
     * of utility functions or additional generated methods of the Any type.
     * 
     * Example 1: Pack and unpack a message in C++.
     * 
     *     Foo foo = ...;
     *     Any any;
     *     any.PackFrom(foo);
     *     ...
     *     if (any.UnpackTo(&foo)) {
     *       ...
     *     }
     * 
     * Example 2: Pack and unpack a message in Java.
     * 
     *     Foo foo = ...;
     *     Any any = Any.pack(foo);
     *     ...
     *     if (any.is(Foo.class)) {
     *       foo = any.unpack(Foo.class);
     *     }
     * 
     * Example 3: Pack and unpack a message in Python.
     * 
     *     foo = Foo(...)
     *     any = Any()
     *     any.Pack(foo)
     *     ...
     *     if any.Is(Foo.DESCRIPTOR):
     *       any.Unpack(foo)
     *       ...
     * 
     * Example 4: Pack and unpack a message in Go
     * 
     *      foo := &pb.Foo{...}
     *      any, err := anypb.New(foo)
     *      if err != nil {
     *        ...
     *      }
     *      ...
     *      foo := &pb.Foo{}
     *      if err := any.UnmarshalTo(foo); err != nil {
     *        ...
     *      }
     * 
     * The pack methods provided by protobuf library will by default use
     * 'type.googleapis.com/full.type.name' as the type URL and the unpack
     * methods only use the fully qualified type name after the last '/'
     * in the type URL, for example "foo.bar.com/x/y.z" will yield type
     * name "y.z".
     * 
     * 
     * JSON
     * 
     * The JSON representation of an `Any` value uses the regular
     * representation of the deserialized, embedded message, with an
     * additional field `@type` which contains the type URL. Example:
     * 
     *     package google.profile;
     *     message Person {
     *       string first_name = 1;
     *       string last_name = 2;
     *     }
     * 
     *     {
     *       "@type": "type.googleapis.com/google.profile.Person",
     *       "firstName": <string>,
     *       "lastName": <string>
     *     }
     * 
     * If the embedded message type is well-known and has a custom JSON
     * representation, that representation will be embedded adding a field
     * `value` which holds the custom JSON in addition to the `@type`
     * field. Example (for message [google.protobuf.Duration][]):
     * 
     *     {
     *       "@type": "type.googleapis.com/google.protobuf.Duration",
     *       "value": "1.212s"
     *     }
     */
    export interface ProtobufAny {
        [name: string]: any;
        /**
         * A URL/resource name that uniquely identifies the type of the serialized
         * protocol buffer message. This string must contain at least
         * one "/" character. The last segment of the URL's path must represent
         * the fully qualified name of the type (as in
         * `path/google.protobuf.Duration`). The name should be in a canonical form
         * (e.g., leading "." is not accepted).
         * 
         * In practice, teams usually precompile into the binary all types that they
         * expect it to use in the context of Any. However, for URLs which use the
         * scheme `http`, `https`, or no scheme, one can optionally set up a type
         * server that maps type URLs to message definitions as follows:
         * 
         * * If no scheme is provided, `https` is assumed.
         * * An HTTP GET on the URL must yield a [google.protobuf.Type][]
         *   value in binary format, or produce an error.
         * * Applications are allowed to cache lookup results based on the
         *   URL, or have them precompiled into a binary to avoid any
         *   lookup. Therefore, binary compatibility needs to be preserved
         *   on changes to types. (Use versioned type names to manage
         *   breaking changes.)
         * 
         * Note: this functionality is not currently available in the official
         * protobuf release, and it is not used for type URLs beginning with
         * type.googleapis.com.
         * 
         * Schemes other than `http`, `https` (or the empty scheme) might be
         * used with implementation specific semantics.
         */
        "@type"?: string;
    }
}
