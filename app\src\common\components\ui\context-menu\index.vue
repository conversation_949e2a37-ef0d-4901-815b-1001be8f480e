<script setup lang="ts">
import {
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuPortal,
  ContextMenuRoot,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuTrigger,
  ContextMenuGroup,
} from 'reka-ui'
import { ref, onBeforeUnmount, watch } from 'vue'
import './style.css'

export interface MenuItem {
  label: string
  icon?: string
  disabled?: boolean
  children?: MenuItem[]
  hasSeparator?: boolean
  key: string
  rightText?: string
  isHidden?: boolean
}

interface Props {
  items: MenuItem[]
  contentClass?: string
  itemClass?: string
  subTriggerClass?: string
  subContentClass?: string
  separatorClass?: string
  subMenuClass?: string
  subSideOffset?: number
  disabled?: boolean
  // 新增：是否启用滚动关闭功能
  enableScrollClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  contentClass: '',
  itemClass: '',
  subTriggerClass: '',
  subContentClass: '',
  separatorClass: '',
  subMenuClass: '',
  subSideOffset: 8,
  disabled: false,
  enableScrollClose: false
})

const emits = defineEmits<{
  (e: 'select', key: string): void
  (e: 'update:open', open: boolean): void
}>()

// 菜单打开状态
const isOpen = ref(false)

// 事件监听器数组，用于管理全局事件
const eventListeners = ref<{ type: string; handler: (e: Event) => void }[]>([])


// 处理滚动关闭
const handleScrollClose = (e: Event) => {
  // 如果禁用了滚动关闭功能，直接返回
  if (!props.enableScrollClose) return

  // 模拟点击外部事件来触发 reka-ui 的 dismiss 机制
  simulatePointerDownOutside()
}

// 模拟点击外部事件
const simulatePointerDownOutside = () => {
  // 创建一个模拟的 pointerdown 事件
  const simulatedEvent = new PointerEvent('pointerdown', {
    bubbles: true,
    cancelable: true,
    pointerType: 'mouse',
    clientX: 0,
    clientY: 0
  })

  // 手动设置 target 属性
  Object.defineProperty(simulatedEvent, 'target', {
    value: document.body,
    writable: false
  })

  // 分发事件到 document
  document.dispatchEvent(simulatedEvent)
}

// 绑定全局滚动事件
const bindScrollEvents = () => {
  if (!props.enableScrollClose) return

  // 监听滚动相关事件
  const scrollEvents = ['wheel', 'scroll', 'touchmove']

  scrollEvents.forEach(eventType => {
    const handler = (e: Event) => {
      handleScrollClose(e)
    }

    // 存储事件监听器引用
    eventListeners.value.push({
      type: eventType,
      handler: handler
    })

    // 添加事件监听器
    document.addEventListener(eventType, handler, { passive: true })
  })
}

// 解绑全局滚动事件
const unbindScrollEvents = () => {
  // 移除所有存储的事件监听器
  eventListeners.value.forEach(({ type, handler }) => {
    document.removeEventListener(type, handler)
  })

  // 清空事件监听器数组
  eventListeners.value = []
}

// 监听菜单打开状态变化
watch(() => isOpen.value, (newOpen) => {
  if (newOpen) {
    // 菜单打开时绑定滚动事件
    bindScrollEvents()
  } else {
    // 菜单关闭时解绑滚动事件
    unbindScrollEvents()
  }
})

// 处理菜单状态更新
const handleOpenChange = (open: boolean) => {
  isOpen.value = open
  emits('update:open', open)
}

onBeforeUnmount(() => {
  // 组件卸载时清理事件监听器
  unbindScrollEvents()
})

</script>

<template>
  <ContextMenuRoot @update:open="handleOpenChange">
    <ContextMenuTrigger :disabled="props.disabled">
      <slot />
    </ContextMenuTrigger>
    <ContextMenuPortal>
      <ContextMenuContent :class="[
        'ContextMenuContent',
        props.contentClass,
      ]">
        <ContextMenuGroup v-for="item in props.items" :key="item.key">
          <ContextMenuSub v-if="item.children" :class="[
            'ContextMenuSub',
            props.subMenuClass,
          ]">
            <ContextMenuSubTrigger :class="[
              'ContextMenuSubTrigger',
              props.subTriggerClass,
            ]">
              {{ item.label }}
              <i class="xl-icon-arrow-right ContextMenuArrow"></i>
            </ContextMenuSubTrigger>
            <ContextMenuSubContent :side-offset="props.subSideOffset" :class="[
              'ContextMenuSubContent',
              props.subContentClass,
            ]">
              <ContextMenuGroup v-for="child in item.children" :key="child.key">
                <ContextMenuItem v-if="!child.isHidden" :disabled="child.disabled" @select="emits('select', child.key)"
                  :class="[
                    'ContextMenuItem',
                    props.itemClass,
                  ]">
                  <span class="LeftSlot" v-if="child.icon">
                    <i :class="child.icon"></i>
                  </span>
                  {{ child.label }}
                  <div class="RightSlot" v-if="child.rightText">
                    <span>{{ child.rightText }}</span>
                  </div>
                </ContextMenuItem>
              </ContextMenuGroup>
            </ContextMenuSubContent>
          </ContextMenuSub>
          <ContextMenuSeparator class="ContextMenuSeparator" v-if="item.hasSeparator" />
          <ContextMenuItem :disabled="item.disabled" v-if="!item.children && !item.isHidden"
            @select="emits('select', item.key)" :class="[
              'ContextMenuItem',
              props.itemClass,
            ]">
            <span class="LeftSlot" v-if="item.icon">
              <i :class="item.icon"></i>
            </span>
            {{ item.label }}
            <div class="RightSlot" v-if="item.rightText">
              <span>{{ item.rightText }}</span>
            </div>
          </ContextMenuItem>
        </ContextMenuGroup>
      </ContextMenuContent>
    </ContextMenuPortal>
  </ContextMenuRoot>
</template>