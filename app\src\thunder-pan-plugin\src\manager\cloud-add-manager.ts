import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/utils/user-helper'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { API_FILE, API_TASK } from '@root/common/thunder-pan-manager/pan-sdk/types'
import { reactive } from 'vue'
import { IGetCurrentFileListOptions, TypeDriveSortInfo } from './drive-file-manager'
import { SortOrderType } from '@root/common/thunder-pan-manager/pan-sdk/utils/file'
import { FilterManager } from './filter-manager'

export interface ICloudAddCurrentData {
  list: API_TASK.DriveTask[]
  isFetching: boolean
  childList: API_TASK.DriveTask[]
  pickedIds: string[]
  sortInfo: TypeDriveSortInfo
}

function genDefaultCurrentData (): ICloudAddCurrentData {
  return {
    list: [],
    isFetching: false,
    childList: [],
    pickedIds: [],
    sortInfo: {
      type: 'modified_time',
      order: SortOrderType.DESC,
    },
  }
}

export class CloudAddManager {
  private static _instance: CloudAddManager

  static getInstance () {
    if (CloudAddManager._instance) {
      return CloudAddManager._instance
    } else {
      CloudAddManager._instance = new CloudAddManager()
      return CloudAddManager._instance
    }
  }

  static convertToFileData (data: API_TASK.DriveTask): API_FILE.DriveFile {
    return {
      ...data,
      ...data.reference_resource,
      id: data.id,
      name: data.file_name,
      size: data.file_size,
      params: data.params,
      user_modified_time: data.updated_time,
    }
  }

  private current: ICloudAddCurrentData = reactive<ICloudAddCurrentData>(genDefaultCurrentData())
  private currentPageToken: string = ''
  private currentChildHasMore: boolean = true
  private currentChildPageToken: string = ''

  reset () {
    this.current = reactive<ICloudAddCurrentData>(genDefaultCurrentData())
    this.resetChildFolder()
  }

  resetChildFolder () {
    this.current.childList.splice(0, this.current.childList.length)
    this.currentChildHasMore = true
    this.currentChildPageToken = ''
  }

  getCurrentData () {
    return this.current
  }

  getCurrentPageToken () {
    return this.currentPageToken
  }

  setSortType (sortInfo: TypeDriveSortInfo) {
    this.current.sortInfo = sortInfo
  }

  private _getSortOrder () {
    switch (this.current.sortInfo.type) {
      case 'modified_time': {
        return this.current.sortInfo.order === SortOrderType.DESC ? 'TASK_MODIFY_TIME_DESC' : 'TASK_MODIFY_TIME_ASC'
      }
      default: {
        return 'TASK_MODIFY_TIME_DESC'
      }
    }
  }

  async fetchList (options: IGetCurrentFileListOptions = {}) {
    if (this.current.isFetching) return
    // 重置数据
    if (options.reset) {
      this.current.list = []
      this.currentPageToken = ''
    }
    this.current.isFetching = true

    try {
      await UserHelper.waitUserSignin()

      const customFilter = FilterManager.getInstance().getCurrentFilterForService('cloud-add_')
      const res = await ThunderPanClientSDK.getInstance().getUrlTaskList({
        params: {
          page_token: this.currentPageToken,
          limit: 200,
          filters: {
            ...customFilter,
          },
          order: this._getSortOrder(),
        }
      })

      if (res.success && res.data && res.data.tasks) {
        this.current.list.push(...res.data.tasks)
        this.currentPageToken = res.data.next_page_token ?? ''
      }
    } catch (err) {

    } finally {
      this.current.isFetching = false
    }
  }

  async fetchChildList (id: string, space?: string, options: IGetCurrentFileListOptions = {}) {
    if (!this.currentChildHasMore || this.current.isFetching) return
    // 重置数据
    if (options.reset) {
      this.resetChildFolder()
    }

    this.current.isFetching = true

    try {
      const res = await ThunderPanClientSDK.getInstance().getUrlTaskStatusById(id, {
        params: {
          space: space,
          page_token: this.currentChildPageToken,
          limit: 200,
        }
      })

      if (res.success && res.data && res.data.statuses) {
        this.current.childList.push(...res.data.statuses)
        this.currentChildHasMore = res.data.next_page_token !== ''
        this.currentChildPageToken = res.data.next_page_token ?? ''
      }
    } catch (err) {

    } finally {
      this.current.isFetching = false
    }
  }

  async deleteByIds (ids: string[]) {
    const res = await ThunderPanClientSDK.getInstance().batchDeleteUrlTasks(ids)

    if (res.success) {
      this.UIDeleteItems(ids)
    }
  }

  appendTasks (task: API_TASK.DriveTask[]) {
    this.current.list.unshift(...task)
  }

  updateTask (newTaskData: API_TASK.DriveTask) {
    this.current.list.forEach(task => {
      if (task.id === newTaskData.id) {
        Object.assign(task, newTaskData)
      }
    })
  }

  UIDeleteItems(ids: string[]) {
    this.current.list = this.current.list.filter(item => { return !ids.includes(item.id!) })
  }

  cleanPicked () {
    this.current.pickedIds = []
  }

  setPickedIds (ids: string[]) {
    this.current.pickedIds = ids
  }

  togglePickedId (id: string, forceCurrent?: boolean) {
    // 强制选中当前指定项
    if (forceCurrent) {
      this.current.pickedIds = [id]
      return
    }

    if (!this.current.pickedIds.includes(id)) {
      this.current.pickedIds.push(id)
    } else {
      const index = this.current.pickedIds.indexOf(id)
      if (index > -1) {
        this.current.pickedIds.splice(index, 1)
      }
    }
  }
}
