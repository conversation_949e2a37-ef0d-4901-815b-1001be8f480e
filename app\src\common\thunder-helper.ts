/**
 * @description: 对 thunder_helper.node 的napi接口封装
 */

import * as path from 'path';
import { BrowserWindow, Rectangle } from 'electron';
import { GetXxxNodePath } from '@root/common/xxx-node-path';
import requireNodeFile from '@root/common/require-node-file';
import { OptionOfHWNDInAfter, Uflag, CmdShow, GWCmd } from '@root/common/window-define';

const thunderHelper: any = requireNodeFile(path.join(GetXxxNodePath(), 'thunder_helper.node'));

export type CRect = { left: number; top: number; right: number; bottom: number };

export enum ClipboardTextType {
  Text = 1,
  Protocol,
};

export interface AttachShaowWindowOptions {
  shadow_size?: number;
  replace_wndproc?: boolean;
  round_rect?: boolean;
  dpi_factor?: number;
  image?: string;
  rect?: CRect;
}

export enum RegistryHKey {
  HKEY_CLASSES_ROOT = 'HKEY_CLASSES_ROOT',
  HKEY_CURRENT_USER = 'HKEY_CURRENT_USER',
  HKEY_LOCAL_MACHINE = 'HKEY_LOCAL_MACHINE',
  HKEY_USERS = 'HKEY_USERS'
}

export enum RegistryDataType {
  REG_NONE = 0,
  REG_SZ = 1,
  REG_EXPAND_SZ = 2,
  REG_BINARY = 3,
  REG_DWORD = 4,
  REG_DWORD_LITTLE_ENDIAN = 4,
  REG_DWORD_BIG_ENDIAN = 5,
  REG_LINK = 6,
  REG_MULTI_SZ = 7,
  REG_RESOURCE_LIST = 8,
  REG_FULL_RESOURCE_DESCRIPTOR = 9,
  REG_RESOURCE_REQUIREMENTS_LIST = 10,
  REG_QWORD = 11,
  REG_QWORD_LITTLE_ENDIAN = 11
}

export enum DriverType {
  DRIVE_UNKNOWN = 0,
  DRIVE_NO_ROOT_DIR,
  DRIVE_REMOVABLE, // 可移动磁盘
  DRIVE_FIXED, // 本地硬盘
  DRIVE_REMOTE, // 网络磁盘
  DRIVE_CDROM, // 光驱
  DRIVE_RAMDISK
}

class ThunderHelperProxy {
  /** 剪贴板相关start */
  private clipboard_: {
    initClipboard?: () => void;
    attachClipboard?: (handler: (type: ClipboardTextType, textList: string[], clipboardProcessName: string) => void) => number;
    detachClipboard?: (id: number) => void;
  } | null = null;

  private shadow_: {
    initGDIPlus?: () => void;
    uninitGDIPlus?: () => void;
    attachShadowWindow?: (hwnd: number, shadow_size?: number, parent?: number, replace_wndproc?: boolean,
      round_rect?: boolean, dpi?: number, image?: string, rect?: CRect) => number;
    drawShadowWindow?: (hwnd: number, dpi?: number) => void;
    setShadowWindowResizable?: (hwnd: number, resizable: boolean) => void;
    destroyShadowWindow?: (hwnd: number) => boolean;
  } | null = null;

  private xlstat_: {
    asyncTrackEvent: (key: string, attr1: string, attr2: string, cost1: number, cost2: number, cost3: number, cost4: number, extData: string, cookie: number, callback: (result: number) => void) => void,
    trackEvent: (key: string, attr1: string, attr2: string, cost1: number, cost2: number, cost3: number, cost4: number, extData: string, cookie: number) => number,
    trackClick: (key: string, cookie: number) => void,
    setUserID: (userID: number, cookie: number) => void,
    asyncInitParam: <T>(param: T, callback: (result: boolean, cookie: number) => void) => void,
    asyncUninit: (cookie: number, callback: () => void) => void,
    initParamRemote: <T>(param: T, callback: (cookie: number) => void) => void,
    uninitRemote: (cookie: number, callback: () => void) => void,
    waitFinish: () => void,
  } | null = null;

  private get clipboard() {
    if (!this.clipboard_) {
      if (thunderHelper?.NativeClipboardMonitor) {
        this.clipboard_ = new (thunderHelper.NativeClipboardMonitor)();
      }
    }
    return this.clipboard_;
  }

  /** 初始化剪贴板监听 */
  initClipboard(): void {
    this.clipboard?.initClipboard?.();
  }

  /** 订阅剪贴板变化 */
  attachClipboard(handler: (type: ClipboardTextType, textList: string[], clipboardProcessName: string) => void): number {
    return this.clipboard?.attachClipboard?.(handler) ?? 0;
  }

  /** 取消订阅剪贴板变化 */
  detachClipboard(id: number): void {
    this.clipboard?.detachClipboard?.(id);
  }

  /** 清空剪贴板 */
  emptyClipBoard(): void {
    thunderHelper?.emptyClipBoard?.();
  }

  /**剪贴板相关end */

  /** xlstat相关start */
  get xlstat() {
    if (!this.xlstat_) {
      if (thunderHelper?.NativeXLStat) {
        this.xlstat_ = new thunderHelper.NativeXLStat();
      }
    }
    return this.xlstat_;
  }

  /** xlstat相关end */

  /** 阴影窗口相关start */
  get shadowWindow() {
    if (!this.shadow_) {
      if (thunderHelper?.NativeShadowWindow) {
        this.shadow_ = new (thunderHelper.NativeShadowWindow)();
      }
    }
    return this.shadow_;
  }

  /** 初始化gidplus */
  initGDIPlus(): void {
    this.shadowWindow?.initGDIPlus?.();
  }

  /** 反初始化gdiplus */
  uninitGDIPlus(): void {
    this.shadowWindow?.uninitGDIPlus?.();
  }

  /** 给指定窗口创建阴影窗口 */
  attachShadowWindow(win: BrowserWindow, parent: BrowserWindow | null, options: AttachShaowWindowOptions): number {
    let shadowHwnd: number = 0;
    do {
      if (!win) {
        break;
      }
      if (process.type === 'browser') {
        const hwnd: number = win.getNativeWindowHandle()?.readUIntLE(0, 4);

        const parentHwnd: number | undefined = parent?.getNativeWindowHandle()?.readUIntLE(0, 4);
        shadowHwnd = this.shadowWindow?.attachShadowWindow?.(hwnd, options?.shadow_size, parentHwnd, options?.replace_wndproc, options?.round_rect, options?.dpi_factor, options?.image, options?.rect) ?? 0;
      }
    } while (0);
    return shadowHwnd;
  }

  /** 绘制阴影窗口 */
  drawShadowWindow(win: BrowserWindow, dpi?: number): void {
    do {
      if (!win) {
        break;
      }

      if (process.type === 'browser') {
        const hwnd: number = win.getNativeWindowHandle()?.readUIntLE(0, 4);
        this.shadowWindow?.drawShadowWindow?.(hwnd, dpi);
      }
    } while (0);
  }

  /** 设置阴影窗口可拖拽 */
  setShadowWindowResizable(win: BrowserWindow, resizable: boolean): void {
    do {
      if (!win) {
        break;
      }

      if (process.type === 'browser') {
        const hwnd: number = win.getNativeWindowHandle()?.readUIntLE(0, 4);
        this.shadowWindow?.setShadowWindowResizable?.(hwnd, resizable);
      }
    } while (0);
  }

  /** 销毁指定窗口的阴影窗口 */
  destroyShadowWindow(hwnd: number): boolean {
    let result: boolean = false;
    do {
      if (!hwnd) {
        break;
      }

      result = !!this.shadowWindow?.destroyShadowWindow?.(hwnd);
    } while (0);
    return result;
  }

  /** 阴影窗口相关end */

  /** 设置命令行回调 */
  setCommandLineCallback(callback: (command: string) => void, newtaskcallback:(...args: any[]) => void): void {
    thunderHelper?.setCommandLineCallback?.(callback, newtaskcallback);
  }

  /** 获取窗口物理矩形
   * @param 如果参数传递为BrowserWindow仅支持在browser进程调用接口
   */
  getWindowRect(wnd: BrowserWindow): Rectangle;
  getWindowRect(wnd: number): Rectangle;
  getWindowRect(wnd: BrowserWindow | number): Rectangle | null {
    let rect: Rectangle | null = null;
    do {
      if (typeof wnd === 'number') {
        rect = thunderHelper?.getWindowRect?.(wnd);
        break;
      }

      if (process.type === 'browser') {
        const hwnd: number = wnd.getNativeWindowHandle()?.readUIntLE(0, 4);
        rect = thunderHelper?.getWindowRect?.(hwnd);
        break;
      }
    } while (0);
    return rect;
  }

  /** 调整窗口大小，位置，z序等
   * @param 如果参数传递为BrowserWindow仅支持在browser进程调用接口
   */
  setWindowPos(wnd: BrowserWindow, options: OptionOfHWNDInAfter, x: number, y: number, width: number, height: number, flag: Uflag): boolean;
  setWindowPos(wnd: BrowserWindow, options: BrowserWindow, x: number, y: number, width: number, height: number, flag: Uflag): boolean;
  setWindowPos(wnd: number, options: OptionOfHWNDInAfter, x: number, y: number, width: number, height: number, flag: Uflag): boolean;
  setWindowPos(wnd: number, options: BrowserWindow, x: number, y: number, width: number, height: number, flag: Uflag): boolean;
  setWindowPos(wnd: BrowserWindow | number, options: OptionOfHWNDInAfter | BrowserWindow, x: number, y: number, width: number, height: number, flags: Uflag): boolean {
    let ret: boolean = false;
    do {
      if (options instanceof BrowserWindow) {
        if (process.type !== 'browser') {
          break;
        }
        const hwnd: number = options.getNativeWindowHandle()?.readUIntLE(0, 4);
        if (!hwnd) {
          break;
        }
        options = hwnd;
      }

      if (typeof wnd === 'number') {
        ret = thunderHelper?.setWindowPos?.(wnd, options, x, y, width, height, flags);
        break;
      }

      if (process.type === 'browser') {
        const hwnd: number = wnd.getNativeWindowHandle()?.readUIntLE(0, 4);
        ret = thunderHelper?.setWindowPos?.(hwnd, options, x, y, width, height, flags);
        break;
      }
    } while (0);
    return ret;
  }

  /** 显示窗口
   * @param 如果参数传递为BrowserWindow仅支持在browser进程调用接口
   */
  showWindow(wnd: BrowserWindow, cmd?: CmdShow): boolean;
  showWindow(wnd: number, cmd?: CmdShow): boolean;
  showWindow(wnd: BrowserWindow | number, cmd?: CmdShow): boolean {
    let ret: boolean = false;
    do {
      if (typeof wnd === 'number') {
        ret = thunderHelper?.showWindow?.(wnd, cmd ?? CmdShow.SW_SHOW);
        break;
      }

      if (process.type === 'browser') {
        const hwnd: number = wnd.getNativeWindowHandle()?.readUIntLE(0, 4);
        ret = thunderHelper?.showWindow?.(hwnd, cmd ?? CmdShow.SW_SHOW);
        break;
      }
    } while (0);
    return ret;
  }

  /** 隐藏窗口
   * @param 如果参数传递为BrowserWindow仅支持在browser进程调用接口
   */
  hideWindow(wnd: BrowserWindow): boolean;
  hideWindow(wnd: number): boolean;
  hideWindow(wnd: BrowserWindow | number): boolean {
    let ret: boolean = false;
    do {
      if (typeof wnd === 'number') {
        ret = thunderHelper?.showWindow?.(wnd, CmdShow.SW_HIDE);
        break;
      }

      if (process.type === 'browser') {
        const hwnd: number = wnd.getNativeWindowHandle()?.readUIntLE(0, 4);
        ret = thunderHelper?.showWindow?.(hwnd, CmdShow.SW_HIDE);
        break;
      }
    } while (0);
    return ret;
  }

  /** 获取指定窗口的下一个窗口
   * @param 如果参数传递为BrowserWindow仅支持在browser进程调用接口
   */
  getNextWindow(wnd: BrowserWindow, wcmd: GWCmd): number | null;
  getNextWindow(wnd: number, wcmd: GWCmd): number | null;
  getNextWindow(wnd: BrowserWindow | number, wcmd: GWCmd): number | null {
    let next_hwnd: number | null = null;
    do {
      if (typeof wnd === 'number') {
        next_hwnd = thunderHelper?.getNextWindow?.(wnd, wcmd);
        break;
      }

      if (process.type === 'browser') {
        const hwnd: number = wnd.getNativeWindowHandle()?.readUIntLE(0, 4);
        next_hwnd = thunderHelper?.getNextWindow?.(hwnd, wcmd);
        break;
      }
    } while (0);
    return next_hwnd;
  }

  /**
   * 是否支持获取指定监视器的DPI （win8.1及以上才支持）
   */
  getDPIAwareSupport(): boolean {
    return thunderHelper?.getDPIAwareSupport?.() ?? false;
  }

  /** 获取指定窗口所在监视器的DPI
   * @param 如果参数传递为BrowserWindow仅支持在browser进程调用接口
   */
  getMonitorDPIFactor(wnd: BrowserWindow): number;
  getMonitorDPIFactor(wnd: number): number;
  getMonitorDPIFactor(wnd: BrowserWindow | number): number {
    let dpiFactor: number = 1.0;
    do {
      if (typeof wnd === 'number') {
        dpiFactor = thunderHelper?.getMonitorDPIFactor?.(wnd);
        break;
      }

      if (process.type === 'browser') {
        const hwnd: number = wnd.getNativeWindowHandle()?.readUIntLE(0, 4);
        dpiFactor = thunderHelper?.getMonitorDPIFactor?.(hwnd);
        break;
      }
    } while (0);
    return dpiFactor ?? 1.0;
  }

  /** 获取系统的DPI */
  getSysDPIFactor(): number {
    return thunderHelper?.getSysDPIFactor?.() ?? 1.0;
  }

  /** 比较两个字符串，虑区域设置和语言习惯 */
  compareStr(str1: string, str2: string): number {
    return thunderHelper?.compareStr?.(str1, str2);
  }

  /** 获取可执行文件的版本信息 */
  getFileVersion(file: string): string {
    return thunderHelper?.getFileVersion?.(file);
  }

  /**
   * 读取注册表键值
   *
   * 64位程序默认访问：HKEY_LOCAL_MACHINE\SOFTWARE
   *
   * 64位程序访问32位注册表：HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node
   */
  readRegString(hkey: RegistryHKey, subkey: string, value: string): string {
    return thunderHelper?.readRegString?.(hkey, subkey, value);
  }

  writeRegValue(hkey: RegistryHKey, subkey: string, value: string, data: string, dataType: RegistryDataType): number {
    return thunderHelper?.writeRegValue?.(hkey, subkey, value, data, dataType);
  }

  deleteRegKey(hkey: RegistryHKey, subkey: string): number {
    return thunderHelper?.deleteRegKey?.(hkey, subkey);
  }

  deleteRegValue(hkey: RegistryHKey, subkey: string, value: string): number {
    return thunderHelper?.deleteRegValue?.(hkey, subkey, value);
  }

  createRegKey(hkey: RegistryHKey, subkey: string): number {
    return thunderHelper?.createRegKey?.(hkey, subkey);
  }

  /** 刷新系统图标 */
  refreshIcon(): void {
    thunderHelper?.refreshIcon?.();
  }

  /** 获取peerid */
  getPeerId(): string {
    return thunderHelper?.getPeerId?.();
  }

  /** 获取安装渠道 */
  getInstallChannel(): string {
    return thunderHelper?.getInstallChannel?.();
  }

  /** 获取用户共享的公共文件夹，在系统分区的Users文件夹下，一般为 C:\Users\<USER>\AppData\LocalLow 目录 */
  getAppDataLocalLowPath(): string {
    return thunderHelper?.GetAppDataLocalLowPath?.();
  }

  /** 获取系统目录 */
  getSystemDirectory(): string {
    return thunderHelper?.getSystemDirectory?.();
  }

  /** 获取指定ini格式的文件内容的键值 */
  readINI(filePath: string, section: string, key: string): string {
    return thunderHelper?.readINI(filePath, section, key);
  }

  /** 修改指定ini格式的文件内容的键值 */
  writeINI(filePath: string, section: string, key: string, value: string): boolean {
    return thunderHelper?.writeINI?.(filePath, section, key, value) === 1 ? true : false;
  }

  /** 获取硬件信息, 一般返回cpu批次序列号 */
  getDmideCode(): string {
    return thunderHelper?.getDmideCode?.();
  }

  /** 获取磁盘剩余空间大小,可直接传入路径 */
  getFreePartitionSpace(dir: string): number {
    return thunderHelper?.getFreePartitionSpace?.(dir);
  }

  /** 获取磁盘的空间信息：总大小&剩余空间，可直接传递路径 */
  getPartitionSpace(dir: string): { total: number; free: number } {
    return thunderHelper?.getPartitionSpace?.(dir) ?? { total: -1, free: -1 };
  }

  /** 获取本地盘符列表
   * @return ['C:\', 'D:\', 'E:\', 'F:\', 'G:\']
   */
  getLogicalDriveStrings(): string[] {
    return thunderHelper?.getLogicalDriveStrings?.() ?? [];
  }

  /** 执行指定程序，
   * @return 返回值<=32则表示拉起失败
   * @example shellExecute(0, 'open', 'C:\\Test\\Thunder.exe', '-StartType:DesktopIcon', '', CmdShow.SW_SHOW)
  */
  shellExecute(hwnd: number, operation: string, filePath: string, params: string, workDir: string, showCmd: CmdShow): number {
    return thunderHelper?.shellExecute?.(hwnd, operation, filePath, params, workDir, showCmd) ?? 0;
  }

  async asyncShellExecute(hwnd: number, operation: string, filePath: string, params: string, workDir: string, showCmd: CmdShow): Promise<number> {
    return await new Promise((v) => {
      if (thunderHelper.asyncShellExecute) {
        thunderHelper.asyncShellExecute(hwnd, operation, filePath, params, workDir, showCmd, (n: number) => {
          v(n);
        });
      } else {
        v(0);
      }
    })
  }

  isFilePlayable(filePath: string): boolean {
    return thunderHelper.isFilePlayable(filePath);
  }

  /** 获取路径的盘符 */
  checkDisk(driver: string): string {
    let ret: string = driver;
    if (/^[a-zA-Z]:\\/.test(driver)) {
      ret = driver.slice(0, 3);
    } else {
      // https://docs.microsoft.com/zh-cn/windows/win32/api/fileapi/nf-fileapi-getdrivetypea
      // 对于参数的解释 The root directory for the drive.A trailing backslash is required.
      // If this parameter is NULL, the function uses the root of the current directory.
      if (driver && driver[driver.length - 1] !== '\\') {
        ret = driver + '\\';
      }
    }
    return ret;
  }

  /** 获取磁盘类型,可直接传入路径 */
  getDriveType(driver: string): DriverType {
    driver = this.checkDisk(driver);
    return thunderHelper?.getDriveType?.(driver);
  }

  terminateProcess() {
    thunderHelper.terminateProcess();
  }

  openURLByDefault(url: string) {
    thunderHelper.openURLByDefault(url);
  }

  /**
   * @description 窗口前置
   * @method setForegroundWindow
   * @param {BrowserWindow} window BrowserWindow 对象
   * @return {void}
   */
  async setForegroundWindow(window: any): Promise<void> {
    if (window !== null) {
      let hwndBuf: Buffer | undefined = undefined;
      let childWindows: any[] = [];
      if (process.type === 'renderer') {
        hwndBuf = await window.getNativeWindowHandle();
        childWindows = await window.getChildWindows();
      } else {
        hwndBuf = window.getNativeWindowHandle();
        childWindows = window.getChildWindows();
      }
      const topMostWindows: any[] = [];
      for (let i: number = 0; i < childWindows.length; ++i) {
        const childWindow: any = childWindows[i];
        let top: boolean = false;
        if (process.type === 'renderer') {
          if (!(await childWindow.isDestroyed())) {
            top = await childWindow.isAlwaysOnTop();
          }
        } else {
          if (!childWindow.isDestroyed()) {
            top = childWindow.isAlwaysOnTop();
          }
        }
        if (top) {
          topMostWindows.push(childWindow);
        }
      }
      const hwndVal: number = hwndBuf!.readUIntLE(0, 4);
      thunderHelper.setWindowPos(hwndVal, OptionOfHWNDInAfter.HWND_TOP, 0, 0, 0, 0, Uflag.SWP_NOSIZE | Uflag.SWP_NOMOVE);
      if (thunderHelper.setForegroundWindow(hwndVal)) {
        console.log('setForegroundWindow sucessful');
      } else {
        console.log('setForegroundWindow failed');
      }
      topMostWindows.forEach(async (childWindow: any) => {
        if (process.type === 'renderer') {
          if (!(await childWindow.isDestroyed())) {
            childWindow.setAlwaysOnTop(true).catch();
          }
        } else {
          if (!childWindow.isDestroyed()) {
            childWindow.setAlwaysOnTop(true);
          }
        }
      });
    }
  }

  // 退出迅雷启动动画
  endThunderStartProcess() {
    thunderHelper.endThunderStartProcess();
  }
}

const instance: ThunderHelperProxy = new ThunderHelperProxy();
export const ThunderHelper: ThunderHelperProxy = instance;

