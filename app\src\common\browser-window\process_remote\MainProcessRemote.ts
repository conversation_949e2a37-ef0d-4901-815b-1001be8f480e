import { ipcMain, IpcMainEvent, IpcMainInvokeEvent } from 'electron'

import { UUIDUtility } from '@xbase/electron_base_kit'

import { buildXbaseRemoteNamespaceChannelString } from './Common'

class MainProcessRemote {
  private _uuidSet: Set<string> = new Set()

  constructor() {}

  // mainProcess app.whenReady 里执行
  prepare() {
    this._initEvent()
  }

  handle(
    channel: string,
    listener: (
      event: IpcMainInvokeEvent,
      ...args: any[]
    ) => Promise<void> | any,
  ): void {
    ipcMain.handle(buildXbaseRemoteNamespaceChannelString(channel), listener)
  }

  handleOnce(
    channel: string,
    listener: (
      event: IpcMainInvokeEvent,
      ...args: any[]
    ) => Promise<void> | any,
  ): void {
    ipcMain.handleOnce(
      buildXbaseRemoteNamespaceChannelString(channel),
      listener,
    )
  }

  on(
    channel: string,
    listener: (event: IpcMainEvent, ...args: any[]) => void,
  ): this {
    ipcMain.on(buildXbaseRemoteNamespaceChannelString(channel), listener)
    return this
  }

  once(
    channel: string,
    listener: (event: IpcMainEvent, ...args: any[]) => void,
  ): this {
    ipcMain.once(buildXbaseRemoteNamespaceChannelString(channel), listener)
    return this
  }

  // removeAllListeners(channel?: string): this {
  //   if (typeof channel === 'string') {
  //     ipcMain.removeAllListeners(
  //       buildXbaseRemoteNamespaceChannelString(channel),
  //     );
  //   } else {
  //     ipcMain.removeAllListeners();
  //   }
  //   return this;
  // }

  removeHandler(channel: string): void {
    ipcMain.removeHandler(buildXbaseRemoteNamespaceChannelString(channel))
  }

  removeListener(channel: string, listener: (...args: any[]) => void): this {
    ipcMain.removeListener(
      buildXbaseRemoteNamespaceChannelString(channel),
      listener,
    )
    return this
  }

  private _initEvent() {
    ipcMain.handle(
      buildXbaseRemoteNamespaceChannelString('register-window'),
      async () => {
        console.log('MainProcessRemote handle register-window')
        return this._generateUUID()
      },
    )
    ipcMain.on(
      buildXbaseRemoteNamespaceChannelString('window-beforeunload'),
      (event, data) => {
        console.log('MainProcessRemote on window-beforeunload')
        // TODO: ....
        console.log(data.message) // 输出：Window is about to close.
      },
    )
  }

  private _generateUUID() {
    let uuid: string = ''
    do {
      uuid = UUIDUtility.uuidv4()
      if (!this._uuidSet.has(uuid)) {
        this._uuidSet.add(uuid)
        break
      }
    } while (true)
    return uuid
  }
}

const mainProcessRemote = new MainProcessRemote()

export { mainProcessRemote }
