<script setup lang="ts">
import TDCheckbox from '@root/common/components/ui/checkbox/index.vue'
import Button from '@root/common/components/ui/button/index.vue'
import Progress from '@root/common/components/ui/progress/index.vue'

import { computed, ref } from 'vue';
import { API_TASK } from '@root/common/thunder-pan-manager/pan-sdk/types';
import { formatSize, isFolder } from '@root/common/thunder-pan-manager/pan-sdk/utils/drive'
import { ECloudAddTaskOperation, formatDate } from '@root/common/thunder-pan-manager/pan-sdk/utils/file'

const props = withDefaults(defineProps<{
  file: API_TASK.DriveTask
  index: number
  pickedIds?: string[]
  isExpand?: boolean  // 是否是云添加文件夹内的任务
}>(), {
  pickedIds: () => [],
  isExpand: false,
})

const emit = defineEmits<{
  (e: 'consume', file: API_TASK.DriveTask): void
  (e: 'right-click', event: MouseEvent, file: API_TASK.DriveTask): void
  (e: 'item-click', event: MouseEvent, index: number, file: API_TASK.DriveTask): void
  (e: 'item-dblclick', file: API_TASK.DriveTask): void
  (e: 'checkbox-click', isCheck: boolean, file: API_TASK.DriveTask): void
  (e: 'operation-click', type: string, file: API_TASK.DriveTask): void
}>()

const canRetry = ref(false)

const isPicked = computed(() => props.pickedIds.includes(props.file.id!))
const isPhaseError = computed(() => ['PHASE_TYPE_ERROR'].includes(props.file.phase!))
const isPhaseComplete = computed(() => ['PHASE_TYPE_COMPLETE'].includes(props.file.phase!))
const isPhaseRunning = computed(() => ['PHASE_TYPE_RUNNING'].includes(props.file.phase!))
const isPhasePending = computed(() => ['PHASE_TYPE_PENDING'].includes(props.file.phase!))
const isAdding = computed(() => isPhaseRunning.value || isPhasePending.value)
const messageShow = computed(() => {
  const message = props.file.message!;
  const messageMap: { [key: string]: { error: string; errorDescription: string } } = {
    云盘文件已删除: {
      error: '云盘文件已删除',
      errorDescription: '',
    },
    '文件已删除，查看失败': {
      error: '云盘文件已删除',
      errorDescription: '',
    },
  };
  if (messageMap[message] !== undefined) {
    return messageMap[message];
  }
  if (/^(云盘|网盘)?空间不足/.test(message)) {
    return {
      error: '云盘空间不足',
      errorDescription: message,
    };
  }
  return {
    error: '添加失败',
    errorDescription: message,
  };
})

function handleItemClick (event: MouseEvent) {
  emit('item-click', event, props.index, props.file)
}

function handleItemDblclick () {
  if (isPhaseError.value) return

  emit('item-dblclick', props.file)
}

function handleCheckChange (isCheck: boolean) {
  emit('checkbox-click', isCheck, props.file)
}

function handleConsume () {
  if (isPhaseError.value) return

  emit('consume', props.file)
}

function handleContextMenu (event: MouseEvent) {
  emit('right-click', event, props.file)
}

function handleOperationClick (type: string) {
  emit('operation-click', type, props.file)
}

function handleFileNameClick (event: MouseEvent) {
  if (isPhaseError.value) {
    handleItemClick(event)
  } else {
    handleConsume()
  }
}
</script>

<template>
  <div
    class="file-item"
    :class="{
      'is-selected': isPicked,
      'is-disabled': isPhaseError,
    }"
    @click.stop="handleItemClick"
    @click.right.stop="handleContextMenu($event)"
    @dblclick.stop="handleItemDblclick"
  >
    <!-- 文件名复合区 -->
    <div class="file-name-container">
      <!-- 复选框 -->
      <div
        class="checkbox"
        @click.stop
      >
        <TDCheckbox label="" :model-value="isPicked" @update:model-value="handleCheckChange" />
      </div>

      <!-- 文件图标 -->
      <div class="icon">
        <img :src="file.icon_link" alt="">
      </div>

      <!-- 文件名 -->
      <div class="text">
        <span v-tooltip="file.file_name" class="file-name disable-drag-select"  @click.stop="handleFileNameClick">{{ file.file_name }}</span>
      </div>

      <!-- 文件状态 -->
      <div class="file-status">
        <p v-if="isPhasePending">等待添加 0%</p>
        <p v-if="isPhaseRunning" class="icon-tips">
          <Progress
            type="circle"
            :percentage="file.progress ?? 0"
            :strokeWidth="1.5"
            :width="15"
            :height="16"
          />
          <span>添加中 {{ file.progress }}%</span>
        </p>
        <p v-if="isPhaseComplete" class="icon-tips">
          <i class="xl-icon-tips-success"></i>
          <span v-show="file.progress != 100">{{ file.message }}</span>
        </p>
        <p v-if="isPhaseError" class="icon-tips">
          <i v-if="messageShow.errorDescription" class="xl-icon-tips-warning" v-tooltip="messageShow.errorDescription" />
          {{ messageShow.error }}
        </p>
      </div>

      <!-- 快捷操作区 -->
      <div class="operation-area">
        <Button
          v-if="isAdding && !isExpand"
          v-tooltip="'刷新'"
          size="sm"
          class="disable-drag-select"
          variant="ghost"
          :is-icon="true"
          @click.stop="handleOperationClick(ECloudAddTaskOperation.REFRESH)"
        >
          <i class="xl-icon-refresh"></i>
        </Button>
        <Button
          v-if="isPhaseComplete"
          v-tooltip="'打开文件夹'"
          size="sm"
          class="disable-drag-select"
          variant="ghost"
          :is-icon="true"
          @click.stop="handleOperationClick(ECloudAddTaskOperation.OPEN_FOLDER)"
        >
          <i class="xl-icon-general-openfolder-l"></i>
        </Button>
        <Button
          v-if="isPhaseComplete && !isExpand"
          v-tooltip="'清除记录'"
          size="sm"
          class="disable-drag-select"
          variant="ghost"
          :is-icon="true"
          @click.stop="handleOperationClick(ECloudAddTaskOperation.DELETE)"
        >
          <i class="xl-icon-nav-recyclebin"></i>
        </Button>
        <Button
          v-if="canRetry && !isExpand"
          v-tooltip="'重试'"
          size="sm"
          class="disable-drag-select"
          variant="ghost"
          :is-icon="true"
          @click.stop="handleOperationClick(ECloudAddTaskOperation.RETRY)"
        >
          <i class="xl-icon-general-retry-m"></i>
        </Button>
        <Button
          v-if="(isPhaseError || isAdding) && !isExpand"
          v-tooltip="isAdding ? '取消任务' : '清除记录'"
          size="sm"
          class="disable-drag-select"
          variant="ghost"
          :is-icon="true"
          @click.stop="handleOperationClick(ECloudAddTaskOperation.DELETE)"
        >
          <i :class="isAdding ? 'xl-icon-general-close-m' : 'xl-icon-nav-recyclebin'"></i>
        </Button>
      </div>
    </div>

    <!-- 文件大小 -->
    <div class="file-size">
      {{ !isFolder(file) ? formatSize(file.file_size!, 0) : '-' }}
    </div>

    <!-- 修改时间 -->
    <div class="file-time">
      {{ formatDate(file.updated_time!, 'YYYY-MM-DD HH:mm') }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.file-item {
  display: flex;
  align-items: center;
  gap: 24px;
  height: 56px;
  margin: 0 28px;
  padding: 0 12px;
  font-size: 12px;
  line-height: 22px;
  color: var(--font-font-1, #272E3B);
  border-radius: var(--border-radius-M2, 10px);
  overflow: hidden;

  .file-name-container {
    display: flex;
    align-items: center;
    flex-grow: 1;

    .checkbox {
      flex-shrink: 0;

      &.is-hidden {
        visibility: hidden;
      }
    }

    .icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      margin: 0 12px;

      img {
        width: 100%;
      }
    }

    .text {
      flex-grow: 1;
      display: flex;
      align-items: center;
      padding: 4px;

      .file-name {
        -webkit-line-clamp: 1;
        display: -webkit-box;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        white-space: pre-wrap;
        transition: color .2s;

        &:hover {
          color: var(--primary-primary-default);
          cursor: pointer;
        }
      }
    }

    .file-status {
      flex-shrink: 0;
      margin-left: 24px;

      .icon-tips {
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .xl-icon-tips-success {
        color: var(--functional-success-default);
      }
      .xl-icon-tips-warning {
        color: var(--functional-warning-default);
      }
    }

    .operation-area {
      display: none;
      flex-shrink: 0;
      margin-left: 24px;
    }
  }

  .file-size {
    flex-shrink: 0;
    width: 58px;
  }

  .file-time {
    flex-shrink: 0;
    width: 110px;
  }

  &.is-selected,
  &:hover {
    background-color: var(--fill-fill-3, #0C18310A);

    .file-status {
      display: none;
    }
  }

  &.is-disabled {
    .icon {
      opacity: 0.4;
    }

    .file-name,
    .file-name:hover,
    .file-size,
    .file-time {
      color: var(--font-font-4) !important;
      cursor: default !important;
    }
  }

  &:hover:not(.is-selected) {
    .file-name-container {
      .operation-area {
        display: block;
      }
    }
  }
}
</style>
