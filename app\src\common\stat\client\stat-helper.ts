import { StatCallApiProxyImplWithIpcClient } from "../stat-call-api-impl";
import { UserInfo } from "@root/common/account/impl/accountHelper";
import { XLStatNS } from "../impl/xlstat";

export class StatHelper {
  private static instance: StatHelper;
  private apiProxy: StatCallApiProxyImplWithIpcClient = new StatCallApiProxyImplWithIpcClient();

  public static getInstance(): StatHelper {
    if (!StatHelper.instance) {
      if (global.StatHelperClientInstance) {
        StatHelper.instance = global.StatHelperClientInstance;
      } else {
        StatHelper.instance = new StatHelper();
        global.StatHelperClientInstance = StatHelper.instance;
      }
    }
    return StatHelper.instance;
  }

  public async initParam(param: XLStatNS.IInitParam): Promise<void> {
    await this.apiProxy.CallApi('StatHelperinitParam', param);
  }

  public async setUserInfo(userID: number = 0, userInfo: UserInfo | null): Promise<void> {
    await this.apiProxy.CallApi('StatHelperSetUserInfo', userID, userInfo);
  }

  public async trackEvent(param: any): Promise<boolean> {
    let info = await this.apiProxy.CallApi('StatHelperTrackEvent', param);
    return (info.result as boolean) ?? false;
  }

  public async trackClick(key: string, cookie: number = 0): Promise<void> {
    await this.apiProxy.CallApi('StatHelperTrackClick', key, cookie);
  }

  public async asyncUninit(cookie: number = 0): Promise<void> {
    await this.apiProxy.CallApi('StatHelperAsyncUninit', cookie);
  }

  public async uninit(): Promise<void> {
    await this.apiProxy.CallApi('StatHelperUninit');
  }
}