# https://docs.microsoft.com/en-us/previous-versions/visualstudio/visual-studio-2013/thxezb7y(v=vs.120)?redirectedfrom=MSDN
MACRO(SET_MSVC_WARNING warning)
IF(MSVC)
	string(FIND ${CMAKE_CXX_FLAGS} ${warning} result)
	if (result EQUAL -1)
		message (STATUS "set msvc waring ${warning}")
		set(CMAKE_CXX_FLAGS "${warning} ${CMAKE_CXX_FLAGS}" CACHE STRING "" FORCE)
	endif ()
ENDIF(MSVC)
ENDMACRO(SET_MSVC_WARNING)