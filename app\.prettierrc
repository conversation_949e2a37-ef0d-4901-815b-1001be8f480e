{"singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": false, "trailingComma": "es5", "arrowParens": "avoid", "endOfLine": "lf", "bracketSpacing": true, "bracketSameLine": false, "vueIndentScriptAndStyle": false, "htmlWhitespaceSensitivity": "ignore", "singleAttributePerLine": true, "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-organize-attributes", "prettier-plugin-css-order"], "cssDeclarationSorterOrder": "smacss", "importOrder": ["^@electron.*", "^@rsbuild.*", "^@vueuse.*", "^@xbase.*", "^@xunlei.*", "^vue$", "^vue.*", "^[a-zA-Z]", "^@/(.*)$", "^@root/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"]}