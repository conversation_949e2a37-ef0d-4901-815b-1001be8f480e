export const CONST_A = 'A'
export const RendererToMain_Window_Channel = {
  minizeWindow: 'window:minizeWindow',
  closeWindow: 'window:closeWindow',
  maxWindow: 'window:maxWindow',
  fullscreen: 'window:fullscreen',
  fullscreenOff: 'window:fullscreenOff',
  windowCanMove: 'window:canMove',
  setPosition: 'window:setPosition',
  windowFullyDraggable: 'window:fullyDraggable',
  windowPin: 'window:pin',
  setTrafficLightPosition: 'window:setTrafficLightPosition',
  setWindowButtonVisibility: 'window:setWindowButtonVisibility',
  showCursor: 'window:showCursor',
  hideOrShowPlayerControlWnd: 'window:showPcWnd',
}
export const RendererToMain_Theme_Channel = {
  darkModeSet: 'dark-mode:set',
  darkModeSystem: 'dark-mode:system',
}

export const MainToRenderer_Window_Channel = {
  fullscreenRes: 'window:fullscreenRes',
  onFullscreen: 'window:onFullscreen',
  onFocus: 'window:onFocus',
  onBlur: 'window:onBlur',
  onMaximize: 'window:onMaximize',
  onMinimize: 'window:onMinimize',
  onRestore: 'window:onRestore',
  onHide: 'window:onHide',
  onShow: 'window:onShow',
}

export enum Pages {
  Home = 'home',
  About = 'about',
  Update = 'update',
}

export enum Channels {
  // main events
  Close = 'Close',
  Quit = 'Quit',
  Minimize = 'Minimize',
  Maximize = 'Maximize',
  GetPackageJson = 'GetPackageJson',
  OpenExternal = 'OpenExternal',
  Broadcast = 'Broadcast',
  ToggleTheme = 'ToggleTheme',
  Render = 'Render',

  // app updater
  AppUpdaterConfirm = 'AppUpdaterConfirm',
  AppUpdaterProgress = 'AppUpdaterProgress',
  AppUpdaterAbort = 'AppUpdaterAbort',

  // store
  GetUserStore = 'GetUserStore',
  SetUserStore = 'SetUserStore',

  // sub window
  AboutMe = 'AboutMe',

  //
  ShellExecute = 'ShllExecute',
  ShellExecuteAnswer = 'ShllExecuteAnswer',
}

export const MainToRenderer_Stat_Channel = {
  playerStat: 'stat:player',
}

export const MainToRenderer_Player_Channel = {
  hidePlayer: 'player:hidePlayer',
}

export const Dialog_Channels = {
  openDirectory: 'dialog:openDirectory',
  openFile: 'dialog:openFile',
  saveFile: 'dialog:saveFile'
}

export const RouteHistoryManager_Channels = {
  navigateToPath: 'NavigateToPath',
}