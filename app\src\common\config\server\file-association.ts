/**
 * @description: 文件关联
 * @example: 1. 当前用户设置: \HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FileExts
 *           2. 全局设置 \HKEY_CURRENT_USER\SOFTWARE\Classes\
 *           3. 计算机全局设置 \HKEY_CLASSES_ROOT\.mp4
 */

import path from 'path';
import { ThunderHelper, RegistryHKey } from '@root/common/thunder-helper';

const regSz: number = 1;
const associateExtInfo: Map<string, string[]> = new Map();
associateExtInfo.set('td', ['TDFile', '迅雷临时数据文件', 'XLTempFile.ico', '使用迅雷下载未完成文件']);
associateExtInfo.set('xltd', ['TDFile', '迅雷临时数据文件', 'XLTempFile.ico', '使用迅雷下载未完成文件']);
associateExtInfo.set('downlist', ['LSTFile', '迅雷专有下载文件', 'XLDownloadList.ico', '使用迅雷下载该任务列表文件']);
associateExtInfo.set('torrent', ['Bittorrent', 'BT种子文件', 'TorrentFile.ico', '使用迅雷下载该BT文件']);
associateExtInfo.set('thunderskin', ['ThunderSkin', '迅雷9皮肤文件', 'thunderskin.ico', '为迅雷9应用该皮肤']);
associateExtInfo.set('xlb', ['XLB', '迅雷下载合集文件', 'DownloadCollection.ico', '使用迅雷查看该下载合集文件']);

function setRegValue(root: RegistryHKey, key: string, name: string, value: string, type: number): void {
  ThunderHelper.createRegKey(root, key);
  ThunderHelper.writeRegValue(root, key, name, value, type);
}

function setAssociateRegInfo(extName: string): void {
  do {
    if (!extName || extName === '') {
      break;
    }

    const extInfo: string[] | undefined = associateExtInfo.get(extName);
    if (!extInfo) {
      break;
    }

    const fileType: string = 'Xunlei.' + extInfo[0] + '.6';
    const thunderPath: string = process.execPath;
    const icoPath: string = path.join(thunderPath, '../icon/' + extInfo[2]);

    let key: string = 'Software\\Classes\\.' + extName;
    setRegValue(RegistryHKey.HKEY_CURRENT_USER, key, '', fileType, regSz);

    key = 'Software\\Classes\\' + fileType;
    setRegValue(RegistryHKey.HKEY_CURRENT_USER, key, '', extInfo[1], regSz);

    key = 'Software\\Classes\\' + fileType + '\\DefaultIcon';
    setRegValue(RegistryHKey.HKEY_CURRENT_USER, key, '', icoPath, regSz);

    key = 'Software\\Classes\\' + fileType + '\\Shell\\Open';
    setRegValue(RegistryHKey.HKEY_CURRENT_USER, key, '', extInfo[3], regSz);

    key = 'Software\\Classes\\' + fileType + '\\Shell\\Open\\command';
    setRegValue(RegistryHKey.HKEY_CURRENT_USER, key, '', '"' + thunderPath + '"' + ' ' + '"%1"' + ' -StartType:' + extName, regSz);
  } while (0);
}

function update(extName: string): void {
  do {
    if (!extName || extName === '') {
      break;
    }

    const extInfo: string[] | undefined = associateExtInfo.get(extName);
    if (!extInfo) {
      break;
    }
    let key: string = 'Software\\Classes\\.' + extName;
    ThunderHelper.deleteRegKey(RegistryHKey.HKEY_CURRENT_USER, key);

    key = 'Software\\Classes\\' + extInfo[0];
    ThunderHelper.deleteRegKey(RegistryHKey.HKEY_CURRENT_USER, key);

    key = 'Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\FileExts\\.' + extName + '\\OpenWithList';
    ThunderHelper.deleteRegKey(RegistryHKey.HKEY_CURRENT_USER, key);

    key = 'Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\FileExts\\.' + extName + '\\OpenWithProgids';
    ThunderHelper.deleteRegKey(RegistryHKey.HKEY_CURRENT_USER, key);

    key = 'Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\FileExts\\.' + extName + '\\UserChoice';
    ThunderHelper.deleteRegKey(RegistryHKey.HKEY_CURRENT_USER, key);

    key = 'Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\FileExts\\.' + extName;
    ThunderHelper.deleteRegKey(RegistryHKey.HKEY_CURRENT_USER, key);

    setAssociateRegInfo(extName);

    ThunderHelper.refreshIcon();
  } while (0);
}

function updateUserChoice(extName: string): void {
  do {
    const key: string =
      'Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\FileExts\\.' + extName + '\\OpenWithList';
    let value: string = ThunderHelper.readRegString(RegistryHKey.HKEY_CURRENT_USER, key, 'MRUList');
    if (value === null || value === undefined || value === '') {
      break;
    }
    const firstChar: string = value.substring(0, 1);
    value = ThunderHelper.readRegString(RegistryHKey.HKEY_CURRENT_USER, key, firstChar);
    if (value?.toLowerCase() !== 'Thunder.exe'.toLowerCase()) {
      update(extName);
    }
  } while (false);
}

export namespace FileAssociationNS {
  export function checkAndRegisterFileType(extName: string): void {
    do {
      if (!extName || extName === '') {
        break;
      }

      const extInfo: string[] | undefined = associateExtInfo.get(extName);
      if (!extInfo) {
        break;
      }

      const fileType: string = 'Xunlei.' + extInfo[0] + '.6';
      const value: string = ThunderHelper.readRegString(RegistryHKey.HKEY_CURRENT_USER, 'Software\\Classes\\.' + extName, '');
      // 如果关联的是迅雷，那么就不用改注册表了
      if (value === fileType) {
        break;
      }

      ThunderHelper.deleteRegKey(RegistryHKey.HKEY_CURRENT_USER, 'Software\\Classes\\.' + extName);
      ThunderHelper.deleteRegKey(RegistryHKey.HKEY_CURRENT_USER, 'Software\\Classes\\' + extInfo[0]);
      ThunderHelper.deleteRegKey(
        RegistryHKey.HKEY_CURRENT_USER,
        'Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\FileExts\\.' + extName
      );

      setAssociateRegInfo(extName);

      ThunderHelper.refreshIcon();
    } while (0);
  }

  // 单独给.torrent封装的接口，目的是解决关联问题
  export function checkAndRegisterTorrentFile(): void {
    do {
      const extName: string = 'torrent';

      const value: string = ThunderHelper.readRegString(
        RegistryHKey.HKEY_CURRENT_USER,
        'Software\\Classes\\Xunlei.Bittorrent.6\\Shell\\Open\\command',
        ''
      );
      if (value && value !== '') {
        // 查找关联的是否为当前thunder.exe，thunderPath为value字串即关联当前exe
        // const thunderPath: string = process.execPath;
        // const exists: boolean = value.includes(thunderPath);
        // if (!exists) {
          // 2023.4.20 百度云盘会修改 HKCU\Software\Classes\.torrent\(Default)，但是HKEY_CURRENT_USER\SOFTWARE\Classes\.torrent\OpenWithProgids 里仍然是Xunlei.Bittorrent.6
          // [HKEY_CURRENT_USER\SOFTWARE\Classes\.torrent]
          // @="BaiduYunGuanjia.torrent"

          // [HKEY_CURRENT_USER\SOFTWARE\Classes\.torrent\OpenWithProgids]
        // "Xunlei.Bittorrent.6"=""
        // 这样会导致 如果启动的目录同注册表关联时（不存在多个迅雷运行路径时），不会再去修改HKCU\Software\Classes\.torrent\(Default)
          checkAndRegisterFileType(extName);
        // }
        updateUserChoice(extName);
      } else {
        // 没有查找到的话，在走以前逻辑
        checkAndRegisterFileType(extName);
      }
    } while (0);
  }

  export async function doAsynCheckAndRegisterProtocal(protocal: string): Promise<boolean> {
    let ret: boolean = false;
    do {
      const thunderPath: string = process.execPath;
      const protocalCommand: string = '"' + thunderPath + '" "%1"' + ' -StartType:' + protocal;
      const command: string = ThunderHelper.readRegString(
        RegistryHKey.HKEY_CURRENT_USER,
        'Software\\classes\\' + protocal + '\\Shell\\Open\\command',
        ''
      );
      if (command === protocalCommand) {
        break;
      }

      ThunderHelper.deleteRegKey(RegistryHKey.HKEY_CURRENT_USER, 'Software\\classes\\' + protocal);
      setRegValue(RegistryHKey.HKEY_CURRENT_USER, 'Software\\classes\\' + protocal, 'URL Protocol', '', regSz);
      setRegValue(
        RegistryHKey.HKEY_CURRENT_USER,
        'Software\\classes\\' + protocal + '\\Shell\\Open\\command',
        '',
        protocalCommand,
        regSz
      );
      ret = true;
    } while (false);
    return ret;
  }

  export async function asynCheckAndAssociate(): Promise<void> {
    checkAndRegisterFileType('td');
    checkAndRegisterFileType('downlist');
    await doAsynCheckAndRegisterProtocal('ed2k');
    await doAsynCheckAndRegisterProtocal('magnet');
    await doAsynCheckAndRegisterProtocal('thunder');
    await doAsynCheckAndRegisterProtocal('thunderx');
    await doAsynCheckAndRegisterProtocal('xlb');
    return;
  }
}
