<script setup lang="ts">
import { computed, ref, onMounted, watch, onUnmounted, nextTick, onBeforeMount } from 'vue'
import path from 'path'
import fs  from 'fs'
import { Stats } from 'fs'
import Progress from '@root/common/components/ui/progress/index.vue'
// import Tooltip from '@root/common/components/ui/tooltip/Tooltip.vue'
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import { TimeHelperNS } from '@root/common/helper/time-helper'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import { ThunderUtil } from '@root/common/utils'
import { useTaskErrorCode } from '@/stores/taskErrorCode'
import { taskExtraFunc } from '@/common/task-extra'
import { divideAndFormat } from '../utils'
import { watchThrottled } from '@vueuse/core'
import { TaskCtrlOperatorNS } from '@/common/task-ctrl-operator'
import * as BaseType from '@root/common/task/base'


interface IProps {
  completed?: boolean
  isActive?: boolean
  data: BaseType.IDownSimpleTaskInfo
}

const taskErrorCode = useTaskErrorCode()

const props = withDefaults(defineProps<IProps>(), {
  completed: false,
  isActive: false,
})

const emits = defineEmits<{
  (e: 'openFile', name: BaseType.IDownSimpleTaskInfo): void // 打开文件夹
  (e: 'pauseTask', data: BaseType.IDownSimpleTaskInfo[]): void // 暂停任务
  (e: 'startTask', data: BaseType.IDownSimpleTaskInfo[]): void // 开始任务
  (e: 'recycle', data: BaseType.IDownSimpleTaskInfo): void // 删除任务
  (e: 'customFile', data: BaseType.IDownSimpleTaskInfo):void // 播放
  (e: 'viewTaskDetail', data: BaseType.IDownSimpleTaskInfo): void
  (e: 'retryDownload', id: number[]): void
  (e: 'handleDblclick', data: BaseType.IDownSimpleTaskInfo, icon: string, taskFileExist: boolean, isSupportPlay: boolean): void
  (e: 'right-click', event: MouseEvent, file: BaseType.IDownSimpleTaskInfo): void
}>()
const isPanTask = ref(false)
/** 单文件bt  */
const isSingleBtTask = ref(false)
const singleName = ref('')
/** 下载文件数 TODO: 存公共属性*/
const downComplete = ref(0)
/** 下载文件总数 */
const downloadTotal = ref(0)
/** 是否支持播放 */
const isSupportPlay = ref(false)

const taskStatusDesc = ref('')
/** 文件是否存在 */
const taskFileExist = ref(true)

const isGray = computed(() => {
  return isError.value || !taskFileExist.value
})

const isError = computed(() => {
  return props.data.errorCode !== 0 || (props.data.taskStatus && [BaseType.TaskStatus.Failed, BaseType.TaskStatus.Unkown].includes(props.data.taskStatus))
})

const infoText = computed(() => {
  if (isError.value) {
    return taskErrorCode.getTaskErrorInfoByCode(props.data.errorCode || 0)
  }
  if (!taskFileExist.value) {
    return '文件已删除'
  }
  const size = ThunderUtil.bytesToSize(props.data.fileSize, 2)
  if (props.completed) {
    const time = props.data.completionTime ? TimeHelperNS.formatDate(new Date(props.data.completionTime), 'yyyy-MM-dd hh:mm:ss') : ''
    return `${time}   ${size}`
  } else { // 下载文件大小
    if (!props.data.fileSize) return '未知大小'
    const finishSize = ThunderUtil.bytesToSize(props.data.downloadSize, 2)
    return `${finishSize}/${size}`
  }
})

const fileIcon = computed(() => {
  if (isSingleBtTask.value && singleName.value) {
    return TaskUtilHelper.getTaskIcon(singleName.value)
  }
  return props.data.taskName ? TaskUtilHelper.getTaskIcon(props.data.taskName, props.data.taskType) : ''
})


const isBtTask = computed(() => {
  return props.data.taskType === BaseType.TaskType.Bt
})
const isGroupTask = computed(() => {
  return props.data.taskType === BaseType.TaskType.Group
})

/** 是否為多子文件下載 */
const isMultiSubTask = computed(() => {
  return ((isBtTask.value && !isSingleBtTask.value) || isGroupTask.value) // && downloadTotal.value > 0
})
/** 下载文件數详情 */
const btDetailTxt = computed(() => {
  if (isCompleted.value) {
    return `共 ${downComplete.value} 个文件 文件详情 > `
  }
  return `已下载 ${downComplete.value}/${downloadTotal.value}  文件详情 > `
})
/** 任务是否开始 */
const isStarted = computed(() => {
  if (typeof props.data.taskStatus !== 'number') return false
  const { StartWaiting, StartPending, Started } = BaseType.TaskStatus
  return [StartWaiting, StartPending, Started].includes(props.data.taskStatus)
})

const isCompleted = computed(() => {
  return props.data.taskStatus === BaseType.TaskStatus.Succeeded || props.data.taskStatus === BaseType.TaskStatus.Seeding
})

const taskIsDownload = computed(() => {
  return props.data.taskStatus === BaseType.TaskStatus.Started
})

const fullFileName = computed(() => {
  const savePath = props.data.savePath || ''
  let fileName: string = props.data.taskName || '';
  // 遇到这样的情况，文件夹本身存在997（无后缀）的文件，然后正在下载->已完成会自动重命名997 (1).
  // 最后加了个莫名其妙的.，这个.在windows重命名下是无法加上去的，然后导致文件任务不存在，做特殊处理
  // if (fileName !== undefined && fileName.indexOf('.') === fileName.length - 1) {
  //   fileName = fileName.substring(0, fileName.length - 1);
  // }
  // if (isGroupTask.value) {
  //   return savePath
  // } else if (isBtTask.value && isSingleBtTask.value) {
  //   return path.join(savePath, fileName);
  // }
  return path.join(savePath, fileName);
})

function getTaskStatusPrompt(taskBase: BaseType.IDownSimpleTaskInfo) {
    let prompt: string = '';
    if (taskBase === null) {
      taskStatusDesc.value = prompt
      return
    }
    switch (taskBase.taskStatus) {
      case BaseType.TaskStatus.Unkown:
        prompt = '未知错误';
        break;
      case BaseType.TaskStatus.StandBy:
        prompt = '准备开始';
        break;
      case BaseType.TaskStatus.PreDownloading:
        prompt = '等待中';
        break;
      case BaseType.TaskStatus.StartWaiting:
        prompt = '排队等待';
        break;
      case BaseType.TaskStatus.StartPending:
        prompt = '正在开始';
        break;
      case BaseType.TaskStatus.StopPending:
        prompt = '正在停止';
        break;
      case BaseType.TaskStatus.Stopped:
        prompt = '已暂停';
        break;
      // case BaseType.TaskStatus.Failed:
      //   prompt = '任务出错';
      //   prompt = taskErrorCode.getTaskErrorInfoByCode(taskBase.errorCode);
      //   break;
      // case BaseType.TaskStatus.Succeeded:
      // case BaseType.TaskStatus.Seeding:
      //   prompt = '完成';
      //   break;
      case BaseType.TaskStatus.DestroyPending:
        prompt = '正在删除';
        break;
      case BaseType.TaskStatus.End:
        prompt = '已结束';
        break;
      default:
        prompt = '';
        break;
    }
    taskStatusDesc.value = prompt;
}

const speed = computed(() => {
  if (taskStatusDesc.value) { return taskStatusDesc.value }
  if ([BaseType.TaskStatus.Seeding, BaseType.TaskStatus.Succeeded, BaseType.TaskStatus.Failed].includes(props.data.taskStatus || -1)) { return ''}
  if (!props.data.downloadSpeed && !props.data.fileSize) return '资源连接中...'
  const { speed, unit } = ThunderUtil.formatSpeed(props.data.downloadSpeed || 0)
  return speed + unit
})

const taskPercentage = computed(() => {
  if (!(typeof props.data.downloadSize === 'number' && typeof props.data.fileSize === 'number')) return 0
  console.log('>>>>>>>>>>>> props.data.downloadSize', props.data.downloadSize)
  return divideAndFormat(props.data.downloadSize, props.data.fileSize, 2)
})

/** 剩余时间 */
const residueTime = computed(() => {
  if (!(typeof props.data.downloadSpeed === 'number' && typeof props.data.downloadSize === 'number' && typeof props.data.fileSize === 'number' && props.data.downloadSpeed > 0)) { return '' }
  const residueSize = props.data.fileSize - props.data.downloadSize
  const time = residueSize / props.data.downloadSpeed
  console.log('>>>>>>>>>>>>>>> time', time)
  if (time) {
    return `剩余 ${TimeHelperNS.formatSeconds(time)}`
  }
  return '剩余 --:--:--'
})

const showBtnGroup = computed(() => {
  // 是否下载完成
  if (isError.value) {
    return {
      play: false,
      download: false,
      pause: false,
      retry: true,
      del: true,
      open: false,
    }
  }
  return {
    play: isSupportPlay.value,
    download: !props.completed,
    pause: isStarted.value,
    retry: false,
    del: false,
    open: !isSupportPlay.value && props.completed && props.data.savePath
  }
})

const handleCustomFile = () => {
  if (isMultiSubTask.value) {
    emits('openFile', props.data)
  } else {
    emits('customFile', props.data)
  }
}

const handleStartOrStopTask = () => {
  if (isStarted.value) {
    emits('pauseTask', [props.data])
  } else {
    emits('startTask', [props.data])
  }
}

/** 重新下载 */
const handleRetryDownload = () => {
  emits('retryDownload', [props.data.taskId])
}

/** 打开文件详情 */
const handleOpenDetail = () => {
  emits('viewTaskDetail', props.data)
}

const handleContextMenu = async (event: MouseEvent) => {
  emits('right-click', event, props.data)
}

const handleItemDblclick = (event: MouseEvent): void => {
  emits('handleDblclick', props.data, fileIcon.value, taskFileExist.value, isSupportPlay.value);
}

const handleClickName = (event: MouseEvent): void => {
  emits('handleDblclick', props.data, fileIcon.value, taskFileExist.value, isSupportPlay.value);
}

const deleteTask = () => {
  emits('recycle', props.data)
}

const handlePlay = () => {
  emits('customFile', props.data)
}

/** 跟新文件数 */
const handleUpdateCount = async (newVal) => {
  if (!isMultiSubTask.value || !newVal) { return }
  
  if (isGroupTask.value) {
    downComplete.value = await taskExtraFunc.getGroupCompleteCount(newVal.taskId)
    downloadTotal.value = await taskExtraFunc.getGroupDownloadCount(newVal.taskId)
  } else {
    downloadTotal.value = await taskExtraFunc.getDownloadCount(newVal.taskId)
    downComplete.value = await taskExtraFunc.getCompleteCount(newVal.taskId)
  }
}

  async function handleWatchFile (curr?: Stats, prev?: Stats): Promise<void> {
    // atime "访问时间" - 文件数据最近被访问的时间；
    // mtime "修改时间" - 文件数据最近被修改的时间；
    // ctime "变化时间" - 文件状态最近更改的时间（修改索引节点数据）
    // birthtime "创建时间" - 文件创建的时间。 当文件被创建时设定一次。
    // 如果你想在文件被修改而不只是访问时得到通知，则需要比较 curr.mtime 和 prev.mtime

    const isExist = await TaskCtrlOperatorNS.isExistTaskFile(props.data)
    taskFileExist.value = isExist;
  }

const dataBaseWatch = watchThrottled(() => props.data, (newVal) => {
  nextTick(async () => {
    handleUpdateCount(newVal)
    getTaskStatusPrompt(newVal)
  })
}, {
  deep: true,
  immediate: true,
  throttle: 500,
})

const fullFileNameWatch = watch(fullFileName, async (newVal, oldVal) => {
  if (oldVal !== undefined) {
    fs.unwatchFile(oldVal, handleWatchFile);
  }
  if (isCompleted.value && newVal) {
    fs.watchFile(newVal, handleWatchFile);
  }
}, { immediate: true})

onBeforeMount(async () => {
  console.log('>>>>>>> props.data', props.data)
  isSupportPlay.value = await taskExtraFunc.getIsSupportPlay(props.data.taskId)
  isPanTask.value = !!(await taskExtraFunc.isPanTask(props.data.taskId))
  if (isBtTask.value) {
    isSingleBtTask.value = await taskExtraFunc.isSingleBT(props.data.taskId)
    const btSubTask = await taskExtraFunc.getBtFileInfos(props.data.taskId)
    if (btSubTask && btSubTask.length === 1) {
      singleName.value = btSubTask[0].fileName
    }
  }
})

onUnmounted(() => {
  // 关闭监听
  console.log('>>>>>>>>>>>>> 销毁', fullFileName.value)
  fs.unwatchFile(fullFileName.value, handleWatchFile);
  dataBaseWatch()
  fullFileNameWatch()
})
</script>


<template>
  <div
    class="task-item"
    :class="{ 'is-active': isActive }"
    @click.right.stop="handleContextMenu($event)"
    @dblclick.stop="handleItemDblclick"
  >
    <div class="task-item-left">
    <!--   <Tooltip :delayDuration="200" align="center" side="top" :disabled="!isSupportPlay">
        <template #trigger>
            <div
              class="task-item-icon file-icon-type"
              :class="{ 'is-error': isError, [fileIcon]: fileIcon }"
            >
            <div v-if="isPanTask" class="task-item-icon__tag"></div>
          </div>
        </template>
        
        <template #content>
          <div class="task-item-icon__detail">
            <div class="task-item-icon__detail__bk">
              <img src="https://backstage-img-ssl.a.88cdn.com/ff836991403d65958543f195c9aca4efaeca7f34" alt="">
            </div>
            <div class="task-item-icon__detail__play">
              <img src="@root/common/assets/img/play.svg" alt="play">
            </div>
            <div class="task-item-icon__detail__time">
              02:23:19
            </div>
            <Progress
              class="task-item__progress"
              :percentage="80"
              :height="4"
              background="backdrop-filter: blur(4px)"
              fillBorderRadius='0'
            ></Progress>
          </div>
        </template>
      </Tooltip> -->
      <div
        class="task-item-icon file-icon-type"
        :class="{ 'is-gray': isGray, [fileIcon]: fileIcon }"
      >
        <div v-if="isPanTask" class="task-item-icon__tag" v-tooltip="'来自云盘'"></div>
      </div>
      <!-- 这里是下载图片和详情窗口 -->
    </div>
    <div class="task-item-content">
      <div class="task-item-name">
        <span :class="{'is-hover': !isGray, 'is-gray': isGray }" v-tooltip="data.taskName" @click.stop="handleClickName">
          {{ data.taskName }}
        </span>
      </div>
      <Progress
        v-if="!completed"
        :percentage="taskPercentage"
        :processing="taskIsDownload"
        :height="4"
      />
      <div class="task-item-info">
        <div class="task-item-info_left">
          <div
            v-if="isMultiSubTask"
            class="task-item-info__detail"
            @click.stop="handleOpenDetail"
          >
            {{ btDetailTxt }}
          </div>
          <div class="task-item-info__text-wrapper" :class="{ 'is-error': isGray}">
            <i
              v-if="isGray"
              class="task-item-info__icon xl-icon-general-exclamation-circle-l"
            ></i>
            <span class="task-item-info__text">{{ infoText }}</span>
          </div>
        </div>
        <div class="task-item-info_right">
          <span class="task-item-info__time" v-if="taskIsDownload && isStarted">{{ residueTime }}</span>
          <span class="task-item-info__speed">{{ speed }}</span>
        </div>
      </div>
    </div>

    <div class="task-item-btn__group" @dblclick.stop>
      <div v-if="showBtnGroup.play" class="task-item-btn">
        <Button variant="ghost" is-icon size="sm" @click.stop="handlePlay" v-tooltip="'播放'">
          <i class="xl-icon-general-play-l"></i>
        </Button>
      </div>
      <div v-if="showBtnGroup.download" class="task-item-btn">
        <Button variant="ghost" is-icon size="sm" @click.stop="handleStartOrStopTask" v-tooltip="showBtnGroup.pause ? '暂停' : '下载'">
          <i :class="`xl-icon-general-${showBtnGroup.pause ? 'stop' : 'download'}-m`"></i>
        </Button>
      </div>
      <div v-if="showBtnGroup.del" class="task-item-btn">
        <Button variant="ghost" is-icon size="sm" @click.stop="deleteTask" v-tooltip="'删除'">
          <i class="xl-icon-general-close-l"></i>
        </Button>
      </div>
      <div v-if="showBtnGroup.retry" class="task-item-btn">
        <Button variant="ghost" is-icon size="sm" @click.stop="handleRetryDownload" v-tooltip="'重新下载'">
          <i class="xl-icon-general-retry-l"></i>
        </Button>
      </div>
      <div v-if="showBtnGroup.open" class="task-item-btn">
        <Button variant="ghost" is-icon size="sm" @click.stop="handleCustomFile" v-tooltip="isMultiSubTask ? '打开文件夹' : '打开文件'">
          <i class="xl-icon-general-openfile-m"></i>
        </Button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.task-item {
  display: flex;
  align-items: start;
  padding: 18px 18px;
  box-sizing: border-box;
  border-radius: 10px;
  // margin-top: 4px;
  // &:first-of-type {
  //   margin-top: 0;
  // }

  &:hover {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.08));
  }

  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &.is-active {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.08));
  }

  .task-item-left {
    position: relative;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
  }

  .task-item-icon {
    width: 40px;
    height: 40px;
    position: relative;
    border-radius: 4px;

    img {
      display: block;
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }

    &.is-gray {
      opacity: 0.5;
    }
  }

  .task-item-icon__tag {
    background: url(@root/common/assets/img/download/ic_pan_mark.svg) no-repeat center center;
    background-size: 100% 100%;
    position: absolute;
    width: 20px;
    height: 20px;
    bottom: -4px;
    right: -5px;
  }

  .task-item-icon__detail {
    position: relative;
    width: 142px;
    height: 80px;
    border-radius: 8px;
    z-index: 1;
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);

    &__bk {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }

    .task-item__progress {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
    }

    .task-item-icon__detail__time {
      position: absolute;
      right: 4px;
      bottom: 4px;
      background: var(--background-background-tooltip-black);
      color: var(--font-font-light);
      font-size: 12px;
      font-weight: 500;
      height: 18px;
      line-height: 18px;
      padding: 0 2.5px;
      border-radius: var(--border-radius-S);
    }

    .task-item-icon__detail__play {
      width: 32px;
      height: 32px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .task-item-content {
    flex: 1;
    margin-left: 16px;
  }

  .task-item-name {
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 1;
    -webkit-line-clamp: 1;
    color: var(--font-font-1);
    font-size: 13px;
    line-height: 22px;
    margin-bottom: 4px;

    .is-hover {
      cursor: pointer;

      &:hover {
        color: var(--primary-primary-default);
      }
    }

    .is-gray {
      color: var(--font-font-4);
    }
  }

  .task-item-info {
    display: flex;
    margin-top: 4px;
    color: var(--font-font-3, rgba(137, 142, 151, 1));
    font-size: 12px;
    line-height: 20px;
  }

  .task-item-info_left {
    flex: 1;
    display: flex;
    align-items: center;
    overflow: hidden;
    white-space: break-spaces;
  }

  .task-item-info_right {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    overflow: hidden;
  }

  .task-item-info__detail {
    flex-shrink: 0;
    margin-right: 6px;
    cursor: pointer;

    &:hover {
      color: var(--primary-primary-default);
    }
  }

  .task-item-info__text-wrapper {
    display: flex;
    align-items: center;
    .task-item-info__icon {
      margin-right: 6px;
    }
  }

  .task-item-info__text {
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 1;
    -webkit-line-clamp: 1;
  }

  .is-error {
    color: var(--functional-error-default);
  }

  .task-item-info__time {
    min-width: 80px;
  }

  .task-item-info__speed {
    min-width: 80px;
    margin-left: 6px;
    text-align: end;
  }

  .task-item-btn__group {
    flex-shrink: 0;
    margin-left: 32px;
    display: flex;
    align-items: center;
    height: 32px;
    width: 80px;
    justify-content: flex-end
  }

  .task-item-btn {
    margin-left: 16px;
    width: 32px;
    height: 32px;

    &:first-of-type {
      margin-left: 0;
    }
  }
}
</style>

<style lang="scss">
.task-item-icon__detail {
  position: relative;
  width: 142px;
  height: 80px;
  border-radius: 8px;
  z-index: 1;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);

  img {
    display: block;
    width: 100%;
    height: 100%;
  }

  &__bk {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .task-item__progress {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }

  .task-item-icon__detail__time {
    position: absolute;
    right: 4px;
    bottom: 4px;
    background: var(--background-background-tooltip-black);
    color: var(--font-font-light);
    font-size: 12px;
    font-weight: 500;
    height: 18px;
    line-height: 18px;
    padding: 0 2.5px;
    border-radius: var(--border-radius-S);
  }

  .task-item-icon__detail__play {
    width: 32px;
    height: 32px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>