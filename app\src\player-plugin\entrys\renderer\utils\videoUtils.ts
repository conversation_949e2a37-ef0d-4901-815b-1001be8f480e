export namespace VideoUtils {
  export function formatTime(second: number) {
    if (second < 0) {
      return ''
    }

    const hours = Math.floor(second / 3600)
    const minutes = Math.floor((second % 3600) / 60)
    const seconds = Math.floor(second % 60)

    const pad = (num: number) => String(num).padStart(2, '0')

    let str = ''
    if (hours > 0) {
      str += `${pad(hours)}:`
    }
    return str + `${pad(minutes)}:${pad(seconds)}`
  }
}