<script setup lang="ts">
import { PopUpNS } from '@root/common/pop-up';

const props = defineProps<{
  type: 'hidden' | 'empty'
  isLogin: boolean
}>()

const onLogin = () => {
  console.log('onLogin')
  PopUpNS.showLoginDlg()
}

</script>

<template>
  <div class="recenty-play-empty-container">
    <div class="recenty-play-empty-icon">
      <inline-svg
        :src="type === 'empty' ? require('@root/common/assets/img/ic_recentplay_empty.svg') : require('@root/common/assets/img/ic_recentplay_hidden.svg')" />
    </div>
    <span class="recenty-play-empty-title">{{ type === 'empty' ? '暂无播放记录' : '最近播放已隐藏' }}</span>
    <template v-if="!isLogin && type === 'empty'">
      <span class="recenty-play-empty-desc">登录后可同步其他设备播放记录，追剧不断层</span>
      <Button variant="primary" @click="onLogin">立即登录</Button>
    </template>

  </div>
</template>

<style lang="scss" scoped>
.recenty-play-empty {
  &-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    width: 100%;
    padding-top: 131px;
  }

  &-icon {
    height: 140px;
    width: 140px;
  }

  &-title {
    margin-top: 18px;
    font-size: 13px;
    line-height: 22px;
    color: var(--font-font-1, #272E3B);
  }

  &-desc {
    margin-top: 8px;
    font-size: 12px;
    line-height: 20px;
    color: var(--font-font-3, #86909C);
    margin-bottom: 18px;
  }


}
</style>
