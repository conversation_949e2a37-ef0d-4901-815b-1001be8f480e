#include "xlipc_client.h"
#include <assert.h>
#include <spdlog/xllog.h>
#include <xlcommon.h>

XLIPCClient::XLIPCClient(const wchar_t* id, const wchar_t* client_id)
	: start_(false)
	, id_(id)
	, client_id_(client_id)
	, client_pipe_(NULL)
	, callback_(NULL)
	, connect_session_(NULL)
{

}

XLIPCClient::~XLIPCClient()
{
	Close();
}

std::wstring XLIPCClient::GetPipeName()
{
	std::wstring pipe_name(L"\\\\.\\pipe\\Xunlei.IPC.");
	pipe_name += id_;
	pipe_name += L".Pipe.{E58F84D6-733B-4668-BB5E-0F5E32901C1B}";
	return pipe_name;
}

int XLIPCClient::Connect()
{
	int ret = 0;
	do
	{
		if (start_)
		{
			XL_SPDLOG_WARN("client already started!");
			break;
		}
		if (client_pipe_ == NULL)
		{
			std::wstring pipe_name_ = GetPipeName();
			XL_SPDLOG_INFO("Connect pipe");
			client_pipe_ = new XLIPCPipe(pipe_name_);
		}
		client_pipe_->SetCallback(this);
		client_pipe_->Connect();
		start_ = true;
	} while (0);
	return ret;
}

int XLIPCClient::Close()
{
	Disconnect(XLIPC_RESULT_SUCCESS);
	return 0;
}

long XLIPCClient::SendData(XLIPCConnectSession* /*connect_session*/, void* data, uint32_t len)
{
	long ret = XLIPC_RESULT_FAILED;
	do
	{
		if (data == NULL || len <= 0)
		{
			break;
		}
		if (!IsConnect())
		{
			XL_SPDLOG_ERROR("not connect, send failed!");
			break;
		}
		XL_SPDLOG_DEBUG("len:{:d}", len);
		ret = client_pipe_->Write(data, len);
		if (ret != XLIPC_RESULT_SUCCESS)
		{
			XL_SPDLOG_ERROR("client_pipe_->Write error! ret:{:d}", ret);
			Disconnect(ret);
		}
	} while (!this);
	return ret;
}

void XLIPCClient::SetCallback(IXLIPCClientCallback* callback)
{
	callback_ = callback;
}

IXLIPCClientCallback* XLIPCClient::GetCallback()
{
	return callback_;
}

const wchar_t* XLIPCClient::GetId() const
{
	return id_.c_str();
}

const wchar_t* XLIPCClient::GetClientId() const
{
	return client_id_.c_str();
}

bool XLIPCClient::IsConnect() const
{
	return connect_session_ != NULL;
}

void XLIPCClient::Disconnect(int error_code)
{
	TerminateProcess(GetCurrentProcess(), 0xdead);
	do
	{
		if (!start_)
		{
			break;
		}
		start_ = false;
		if (IsConnect())
		{
			if (connect_session_ != NULL)
			{
				connect_session_->Release();
				connect_session_ = NULL;
			}
			if (client_pipe_)
			{
				client_pipe_->SetCallback(NULL);
				client_pipe_->Disconnect();
				client_pipe_->SafeDelete();
				client_pipe_ = NULL;
			}
			if (callback_ != NULL)
			{
				callback_->OnDisconnect(error_code);
			}
		}
		else
		{
			if (client_pipe_)
			{
				client_pipe_->SetCallback(NULL);
				client_pipe_->Disconnect();
				client_pipe_->SafeDelete();
				client_pipe_ = NULL;
			}
		}
	} while (false);
}

void XLIPCClient::OnPipeConnect(int status)
{
	do
	{
		if (status < 0)
		{
			XL_SPDLOG_ERROR("OnPipeConnect failed, status:{:d}", status);
			Close();
			if (callback_)
			{
				callback_->OnConnectFailed(status);
			}
			break;
		}
		assert(connect_session_ == NULL);
		connect_session_ = new XLIPCConnectSession(this);
		client_pipe_->ReadStart();
		XLIPCStream* stream = new XLIPCStream();
		std::string strClientId;
		xl::text::transcode::Unicode_to_UTF8(client_id_.c_str(), client_id_.length(), strClientId);
		stream->WriteUtf8(strClientId.c_str());
		int ret = connect_session_->AsynCall("__OnSessionConnected__", stream);
		stream->Release();
		if (ret == XLIPC_RESULT_SUCCESS)
		{
			if (callback_)
			{
				callback_->OnConnect(connect_session_);
			}
		}
		else
		{
			XL_SPDLOG_ERROR("AsynCall OnSessionConnected failed! ret:{:d}", ret);
			Disconnect(ret);
		}
	} while (false);
}

void XLIPCClient::OnPipeDisconnect(int status)
{
	Disconnect(status);
}

void XLIPCClient::OnPipeRead(const char* buf, int size)
{
	if (size < 0)
	{
		XL_SPDLOG_ERROR("OnPipeRead error, error code:{:d}", size);
		Disconnect(size);
	}
	else if (size > 0)
	{
		XL_SPDLOG_DEBUG(L"len:{:d}", size);
		assert(connect_session_);
		if (connect_session_)
		{
			connect_session_->OnRecvData(buf, size);
		}
	}
}

void XLIPCClient::OnPipeWrite(int status)
{
	if (status != XLIPC_RESULT_SUCCESS)
	{
		XL_SPDLOG_ERROR("OnPipeWrite error! status:{:d}", status);
		Disconnect(status);
	}
}
