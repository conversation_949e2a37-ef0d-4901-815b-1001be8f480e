import { type InjectionKey, inject, provide } from 'vue'

import type { DownloadEventParams } from '@root/modal-renderer/types/new-task.type'

// 定义注入键
export const DOWNLOAD_HANDLER_KEY: InjectionKey<(params: DownloadEventParams) => void> =
  Symbol('downloadHandler')
export const CLOSE_WINDOW_HANDLER_KEY: InjectionKey<(scene: string) => void> =
  Symbol('closeWindowHandler')

// Provider 组合式函数 - 在父组件中使用
export function provideDownloadHandler(handler: (params: DownloadEventParams) => void) {
  provide(DOWNLOAD_HANDLER_KEY, handler)
}

// Provider 关闭窗口处理函数 - 在父组件中使用
export function provideCloseWindowHandler(handler: (scene: string) => void) {
  provide(CLOSE_WINDOW_HANDLER_KEY, handler)
}

// Consumer 组合式函数 - 在子组件中使用
export function useDownloadHandler() {
  const downloadHandler = inject(DOWNLOAD_HANDLER_KEY)

  if (!downloadHandler) {
    throw new Error(
      'Download handler not provided. Make sure to call provideDownloadHandler in parent component.'
    )
  }

  return {
    handleDownload: downloadHandler,
  }
}

// Consumer 关闭窗口处理函数 - 在子组件中使用
export function useCloseWindowHandler() {
  const closeWindowHandler = inject(CLOSE_WINDOW_HANDLER_KEY)

  if (!closeWindowHandler) {
    throw new Error(
      'Close window handler not provided. Make sure to call provideCloseWindowHandler in parent component.'
    )
  }

  return {
    handleCloseWindow: (scene?: string) => {
      if (!scene || !scene.trim()) {
        console.error('❌ 关闭窗口失败：必须提供场景编号')
        throw new Error('关闭窗口时必须提供场景编号，如: handleCloseWindow("download-complete")')
      }
      closeWindowHandler(scene.trim())
    },
  }
}
