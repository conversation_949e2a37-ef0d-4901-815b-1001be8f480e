cmake_minimum_required(VERSION 3.15)

#cmake 策略
cmake_policy(SET CMP0091 NEW)
cmake_policy(SET CMP0042 NEW)

# 设置c++版本
set(CMAKE_CXX_STANDARD 20)

#设置自己的插件名称
project(thunder_helper)

#node api 使用版本
add_definitions(-DNAPI_VERSION=6)

#cmake.js 头文件路径
include_directories(${CMAKE_JS_INC})

#源码头文件包含路径
include_directories(SYSTEM "${CMAKE_CURRENT_SOURCE_DIR}/src")

#源码路径
file(GLOB_RECURSE ALL_SOURCE ${CMAKE_CURRENT_SOURCE_DIR}/*)

#目录结构分组
foreach(fileItem ${ALL_SOURCE})       
    # Get the directory of the source file
    get_filename_component(PARENT_DIR "${fileItem}" DIRECTORY)
    # Remove common directory prefix to make the group
    string(REPLACE "${CMAKE_CURRENT_SOURCE_DIR}/src" "" GROUP "${PARENT_DIR}")
    # Make sure we are using windows slashes
    string(REPLACE "/" "\\" GROUP "${GROUP}")
    # Group into "Source Files" and "Header Files"
    set(GROUP "${GROUP}")
    source_group("src\\${GROUP}" FILES "${fileItem}")
endforeach()

if (${CMAKE_SYSTEM_NAME} STREQUAL Darwin)
  file(GLOB_RECURSE NO_NEED_SOURCE1 ${CMAKE_CURRENT_SOURCE_DIR}/src/win/*)
  list(REMOVE_ITEM ALL_SOURCE ${NO_NEED_SOURCE1})
  include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/include)
  include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/third_part/include)
  include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/common/include)
  add_library(${PROJECT_NAME} SHARED ${ALL_SOURCE} ${CMAKE_JS_SRC})
  set_target_properties(${PROJECT_NAME} PROPERTIES PREFIX "" SUFFIX ".node")
  target_link_libraries(${PROJECT_NAME} ${CMAKE_JS_LIB}
  libuv
  common
    )
    
elseif (${CMAKE_SYSTEM_NAME} STREQUAL Windows)
  add_definitions(-D__WIN32__ -DETW_LOGGER
  
  )
  file(GLOB_RECURSE NO_NEED_SOURCE1 ${CMAKE_CURRENT_SOURCE_DIR}/src/mac/*)
  list(REMOVE_ITEM ALL_SOURCE ${NO_NEED_SOURCE1})
  include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/include)
  include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/third_part/include)
  include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../native_core/src/main/cpp/common/include)
  include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../include)
  include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../include/WTL)
  include_directories( ${CMAKE_CURRENT_SOURCE_DIR}/../win_common/src)

  set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Zi /MTd /utf-8")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi /MT /utf-8")
  set(CMAKE_SHARED_LINKER_FLAGS_DEBUG "${CMAKE_SHARED_LINKER_FLAGS_DEBUG} /DEBUG /OPT:REF /OPT:ICF /PDB:\"${CMAKE_SOURCE_DIR}/pdb/debug/\"")
  set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF /PDB:\"${CMAKE_SOURCE_DIR}/pdb/release/\"")

  #link_directories("${CMAKE_CURRENT_SOURCE_DIR}/../native_core/win/x64/${CMAKE_BUILD_TYPE}")
  add_library(${PROJECT_NAME} SHARED ${ALL_SOURCE} ${CMAKE_JS_SRC})
  set_target_properties(${PROJECT_NAME} PROPERTIES
        COMPILE_OPTIONS "/MT$<$<CONFIG:Debug>:d>"
    )
  set_target_properties(${PROJECT_NAME} PROPERTIES PREFIX "" SUFFIX ".node")
  target_link_libraries(${PROJECT_NAME} ${CMAKE_JS_LIB}
  win_common
  common
  libuv
  Shlwapi.lib
  Iphlpapi.lib
  Version.lib
  Dwmapi.lib
  gdi32.lib
  Winhttp.lib
    )
    target_compile_options(${PROJECT_NAME} PRIVATE 
        /wd4828
        /wd4091
    )

  if(MSVC AND CMAKE_JS_NODELIB_DEF AND CMAKE_JS_NODELIB_TARGET)
    # Generate node.lib
    #execute_process(COMMAND ${CMAKE_AR} /def:${CMAKE_JS_NODELIB_DEF} /out:${CMAKE_JS_NODELIB_TARGET} ${CMAKE_STATIC_LINKER_FLAGS})
    execute_process(COMMAND node -p "require('node-addon-api').include"
          WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
          OUTPUT_VARIABLE NODE_ADDON_API_DIR
          )
    string(REPLACE "\n" "" NODE_ADDON_API_DIR ${NODE_ADDON_API_DIR})
    string(REPLACE "\"" "" NODE_ADDON_API_DIR ${NODE_ADDON_API_DIR})
    target_include_directories(${PROJECT_NAME} PRIVATE ${NODE_ADDON_API_DIR})
    target_link_libraries(${PROJECT_NAME} ${CMAKE_JS_LIB}
    )
  endif()
endif()
