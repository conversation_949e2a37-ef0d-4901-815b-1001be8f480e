import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { mainRendererContext } from '@xunlei/node-net-ipc/dist/ipc-base';

export class CallUiOperationWithIpcClient {
  public async CallApi(name: string, ...args: any[]): Promise<{bSucc: boolean, result?: any}> {
    try {
      console.log(`>>>>>>>>>>>>  CallApi ${name} ${JSON.stringify(args)}`);
      const result = await client.callRemoteClientFunction(mainRendererContext, name, ...args);
      return {bSucc: true, result};
    } catch(e) {
      return {bSucc: false};
    }
  }

  // public AttachServerEvent(name: string, callback: (...args: any[]) => void): number {

  //   return client.attachServerEvent(name, (c: any, ...args: any[]) => {
  //     callback(...args);
  //   });
  // }

  // public DetachServerEvent(name: string, cookie: number): void {
  //   client.detachServerEvent(name, cookie);
  // }
  // public async DestroyObject(id: string): Promise<void> {
  //   client.callRemoteClientFunction('DestroyObject', id);
  // }
}