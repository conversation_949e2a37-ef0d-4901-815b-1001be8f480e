<script setup lang="ts">
import { RadioGroupIndicator, RadioGroupItem, RadioGroupRoot } from 'reka-ui'
import Button from '../button/index.vue'

// 定义操作类型
interface RadioAction {
  type: 'button' | 'select' | 'click'
  options?: { name: string; value: string }[]
  disabled?: boolean
  label?: string
  onAction?: () => void
}

export interface IRadioGroupOptions {
  label: string
  value: string
  action?: RadioAction
  tip?: string
}

const props = withDefaults(defineProps<{
  modelValue?: string
  options: IRadioGroupOptions[],
  defaultValue?: string
  orientation: 'horizontal' | 'vertical'
  title?: string
  onChange?: (value: string) => void
}>(), {
  modelValue: '',
  defaultValue: '',
  orientation: 'horizontal',
  title: ''
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const handleSelect = (value: string) => {
  props.onChange?.(value)
  emit('update:modelValue', value)
}

</script>

<template>
  <div class="RadioGroupTitle" v-if="title">{{ title }}</div>
  <RadioGroupRoot class="RadioGroupRoot" :default-value="defaultValue" :orientation="orientation" v-model="modelValue">
    <div v-for="option in options" class="RadioGroupItemContainer">
      <RadioGroupItem :id="option.value" class="RadioGroupItem" :value="option.value"
        @select="handleSelect(option.value)">
        <RadioGroupIndicator class="RadioGroupIndicator" />
      </RadioGroupItem>
      <label class="Label" :for="option.value">
        {{ option.label }}
      </label>

      <!-- 操作区域 -->
      <div v-if="option.action">
        <!-- 按钮操作 -->
        <Button v-if="option.action.type === 'button'" variant="outline" size="sm" :disabled="option.action.disabled"
          @click="option?.action?.onAction && option.action.onAction()">
          {{ option.action.label || '操作' }}
        </Button>
      </div>

      <div class="RadioGroupTip" v-if="option.tip">
        <i class="xl-icon-tips-question-circle-l"></i>
      </div>
    </div>
  </RadioGroupRoot>
</template>

<style scoped lang="scss">
.RadioGroupTitle {
  color: var(--font-font-3, #86909C);
  font-size: 13px;
  line-height: 22px;
  margin-bottom: 6px;
  height: 32px;
  display: flex;
  align-items: center;
}

.RadioGroupRoot {
  display: flex;
}

.RadioGroupRoot[data-orientation='vertical'] {
  flex-direction: column;
  gap: 6px;
}

.RadioGroupRoot[data-orientation='horizontal'] {
  flex-direction: row;
  gap: 24px;
}

.RadioGroupItemContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.RadioGroupRoot[data-orientation='horizontal'] .RadioGroupItemContainer {
  gap: 8px;
}

.RadioGroupRoot[data-orientation='vertical'] .RadioGroupItemContainer {
  display: flex;
  height: 32px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

.RadioGroupItem {
  background-color: var(--background-background-elevated);
  width: 16px;
  height: 16px;
  border-radius: 100%;
  border: 1px solid var(--border-border-1);
}

.RadioGroupItem:hover {
  border-color: var(--button-button1-hover);
  cursor: pointer;
}

.RadioGroupItem:focus,
.RadioGroupItem[data-state='checked'] {
  border-color: var(--button-button1-default);
  background-color: var(--button-button1-default);
}

.RadioGroupIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.RadioGroupIndicator::after {
  content: '';
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--background-background-elevated);
}

.Label {
  color: var(--font-font-2, #4E5769);
  font-size: 13px;
  line-height: 22px;
  min-width: fit-content;
}

.RadioGroupTip {
  color: var(--font-font-3, #86909C);
}
</style>