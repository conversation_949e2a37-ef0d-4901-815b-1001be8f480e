<script setup lang="ts">
import { onMounted, ref } from 'vue';
import CheckboxGroup, { ICheckoutGroupOptions } from '@root/common/components/ui/checkbox-group/index.vue';
import {
  CONNECT_SETTING_NAME_MAP,
  getSettingConfig,
  setSettingConfig,
  EXTEND_NAME_MAP
} from '@root/modal-renderer/src/views/setting';
import XMPMessage from '@root/common/components/ui/message';
import { usePromptDialog } from '@root/common/components/ui/Dialog/usePromptDialog';

const promptDialog = usePromptDialog()

const extendNames = ref<string>('')
const extendNamesRemoved = ref<string>('')

// 接管方式
const selectedConnectModeNames = ref<string[]>([])
const connectModeOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '接管所有下载',
    name: CONNECT_SETTING_NAME_MAP.MotitorAll,
    defaultValue: true,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
    action: {
      type: 'link',
      label: '接管不生效？点击开启浏览器支持',
      onAction: () => {
        XMPMessage({
          message: '该功能暂未实现',
          type: 'error'
        })
      }
    },
  },
  {
    label: '自动识别剪切版下载链接',
    name: CONNECT_SETTING_NAME_MAP.MonitorClipBoard,
    defaultValue: true,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
])

// 接管下载文件类型
const selectedConnectDownloadFileTypeNames = ref<string[]>([])

const handleConnectDownloadFileTypeChange = (checked: boolean, optionName: string) => {
  setSettingConfig(optionName, checked)
  if (checked) {
    extendNames.value += EXTEND_NAME_MAP[optionName]
  } else {
    extendNamesRemoved.value += EXTEND_NAME_MAP[optionName]
    extendNames.value = extendNames.value.replace(EXTEND_NAME_MAP[optionName], '')
  }

  setSettingConfig(CONNECT_SETTING_NAME_MAP.ExtendNames, extendNames.value)
  setSettingConfig(CONNECT_SETTING_NAME_MAP.ExtendNamesAdded, extendNames.value)
  setSettingConfig(CONNECT_SETTING_NAME_MAP.ExtendNamesRemoved, extendNamesRemoved.value)
}

const connectDownloadFileTypeOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '视频',
    name: CONNECT_SETTING_NAME_MAP.VideoExtendNames,
    defaultValue: false,
    onChange: handleConnectDownloadFileTypeChange
  },
  {
    label: '文档',
    name: CONNECT_SETTING_NAME_MAP.WordExtendNames,
    defaultValue: false,
    onChange: handleConnectDownloadFileTypeChange
  },
  {
    label: '图片',
    name: CONNECT_SETTING_NAME_MAP.ImageExtendNames,
    defaultValue: false,
    onChange: handleConnectDownloadFileTypeChange
  },
  {
    label: '音频',
    name: CONNECT_SETTING_NAME_MAP.MusicExtendNames,
    defaultValue: false,
    onChange: handleConnectDownloadFileTypeChange
  },
  {
    label: '压缩包',
    name: CONNECT_SETTING_NAME_MAP.ZipExtendNames,
    defaultValue: false,
    onChange: handleConnectDownloadFileTypeChange
  },
  {
    label: '软件',
    name: CONNECT_SETTING_NAME_MAP.ExeExtendNames,
    defaultValue: false,
    onChange: handleConnectDownloadFileTypeChange
  },
  {
    label: '其他',
    name: CONNECT_SETTING_NAME_MAP.ExtraExtendNames,
    defaultValue: false,
    onChange: handleConnectDownloadFileTypeChange
  },
])


// 接管下载类型
const selectedConnectDownloadTypeNames = ref<string[]>([])
const connectDownloadTypeOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '传统下载',
    name: CONNECT_SETTING_NAME_MAP.WatchTraditionLink,
    defaultValue: true,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
  {
    label: 'BT下载',
    name: CONNECT_SETTING_NAME_MAP.ConfigWatch_Bt,
    defaultValue: true,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
  {
    label: 'eMule下载',
    name: CONNECT_SETTING_NAME_MAP.EMuleWatchLink,
    defaultValue: true,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
  {
    label: '磁力链接下载',
    name: CONNECT_SETTING_NAME_MAP.MagnetWatchLink,
    defaultValue: true,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
])


onMounted(() => {
  initDefaultValue()
})

const initDefaultValue = async () => {
  const connectModeValue = await Promise.all(connectModeOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectedConnectModeNames.value = connectModeValue

  const connectDownloadFileTypeValue = await Promise.all(connectDownloadFileTypeOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectedConnectDownloadFileTypeNames.value = connectDownloadFileTypeValue

  const connectDownloadTypeValue = await Promise.all(connectDownloadTypeOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectedConnectDownloadTypeNames.value = connectDownloadTypeValue

  const extendNamesValue = await getSettingConfig(CONNECT_SETTING_NAME_MAP.ExtendNames, '')
  extendNames.value = extendNamesValue as string

  const extendNamesRemovedValue = await getSettingConfig(CONNECT_SETTING_NAME_MAP.ExtendNamesRemoved, '')
  extendNamesRemoved.value = extendNamesRemovedValue as string
}

const editFileType = async () => {
  const result = await promptDialog.prompt({
    title: '以下文件扩展名使用迅雷下载',
    placeholder: '',
    defaultValue: extendNames.value,
    hint: '',
    validateOnInput: true,
    fixedHeight: true, // 启用固定高度模式
    inputType: 'textarea', // 使用文本区域
    variant: 'thunder',
    inputClass: '',
    containerStyle: {},
    containerClass: '',
    selectAll: true,
    inputProps: {},
    inputStyle: {
      height: '66px',
      resize: 'vertical', // 允许垂直调整大小
      fontFamily: 'monospace', // 使用等宽字体
      lineHeight: '1.5',
    },
    onChange: () => { },
    validator: () => {
      return { valid: true, message: '' }
    },
    onConfirm: () => { return true },
  })

  if (result !== false) {
    extendNamesRemoved.value = extendNames.value.replace(result as string, '')
    extendNames.value = result as string

    setSettingConfig(CONNECT_SETTING_NAME_MAP.ExtendNames, extendNames.value)
    setSettingConfig(CONNECT_SETTING_NAME_MAP.ExtendNamesAdded, extendNames.value)
    setSettingConfig(CONNECT_SETTING_NAME_MAP.ExtendNamesRemoved, extendNamesRemoved.value)

    // 循环selectedConnectDownloadFileTypeNames，如果包含extendNamesRemoved.value，则设置为false
    Object.keys(EXTEND_NAME_MAP).forEach(key => {
      if (extendNamesRemoved.value.includes(EXTEND_NAME_MAP[key])) {
        selectedConnectDownloadFileTypeNames.value = selectedConnectDownloadFileTypeNames.value.filter(item => item !== key)
        handleConnectDownloadFileTypeChange(false, key)
      } else {
        selectedConnectDownloadFileTypeNames.value = [...selectedConnectDownloadFileTypeNames.value, key]
        handleConnectDownloadFileTypeChange(true, key)
      }
    })

  }
}


</script>

<template>
  <CheckboxGroup title="接管方式" :options="connectModeOptions" orientation="vertical" v-model="selectedConnectModeNames" />
  <div class="settings-content-divider"></div>
  <div class="settings-content-title">
    接管下载类型
  </div>
  <CheckboxGroup :options="connectDownloadFileTypeOptions" orientation="horizontal"
    v-model="selectedConnectDownloadFileTypeNames" />

  <div class="connect-setting-edit-file-type">
    <Button variant="outline" size="sm" @click="editFileType">
      编辑文件类型
    </Button>
  </div>

  <CheckboxGroup :options="connectDownloadTypeOptions" orientation="horizontal"
    v-model="selectedConnectDownloadTypeNames" />

  <div class="settings-content-divider"></div>

</template>

<style scoped lang="scss">
.connect-setting-edit-file-type {
  margin-top: 6px;
  margin-bottom: 12px;
}
</style>
