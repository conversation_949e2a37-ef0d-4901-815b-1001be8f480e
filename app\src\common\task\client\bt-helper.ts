import axios from 'axios';
import { ParseUrlFileNameNS } from './parse-helper';
import * as DownloadKernel from '../base';
import { ApplicationManager } from '@root/common/thunder-pan-manager/manager/application';

function arrayToMap(arr: number[]) {
  let m: object = new Object();
  for (let item of arr) {
    m[item] = true;
  }
  return m;
}

export namespace BtTaskHelper {

  export interface IBtIntelligentSelectionRequest {
    info_hash: string;
    compress: boolean;
  }

  export interface IBtRemoteSelectionResult {
    handled: boolean
    excludes: number[]
  }

  export interface IBtIntelligentSelectionResponse {
    // 过滤策略是否成功，过滤失败时不会返回文件索引，客户端需要走兜底逻辑
    success: boolean;
    // 被过滤的文件索引。对应客户端的real_index
    indices: number[];
    // 压缩后的被过滤索引数组，压缩之后将不再返回indices，
    // `-`表示连续索引区间，`,`用于分隔多个不连续区间
    // 例如`0-4,6-8`表示索引`0,1,2,3,4,6,7,8`
    compressed_indices: string;
  }

  function generateExclusionRealIndexArray(compressed_indices: string): number[] {
    let realIndexs: number[] = [];
    do {
      if (!compressed_indices) {
        break;
      }

      const sections = compressed_indices.split(',');
      for (let index: number = 0; index < sections.length; index++) {
        const section = sections[index];
        if (!section) {
          continue;
        }
        const tmp = section.split('-');
        if (tmp.length === 1) {
          const real_index = Number(tmp[0]);
          if (isNaN(real_index)) {
            break;
          } else {
            realIndexs.push(real_index);
            continue;
          }
        }

        const tmpRes = tmp.slice(0, 2);
        const begin = tmpRes[0];
        const end = tmpRes[1];
        const from = Number(begin);
        const to = Number(end);
        if (isNaN(from) || isNaN(to) || from > to) {
          realIndexs = [];
          break;
        }

        for (let i: number = 0; i <= (to - from); i++) {
          realIndexs.push(i + from);
        }
      }
    } while (0);
    return realIndexs;
  }

  /**
   * 请求服务器，获取智能选择的结果
   * 线上环境：'https://api-pan.xunlei.com/drive/v1/resource/filter'
   * 测试环境：'http://api-alpha-drive.office.k8s.xunlei.cn/drive/v1/resource/filter'
   * @param infoId Bt 种子的 infoId
   */
  export async function getRemoteSelectionResult(infoId: string): Promise<IBtRemoteSelectionResult> {
    const data: IBtIntelligentSelectionRequest = {
      info_hash: infoId,
      compress: true,
    };
    const captchaToken = ''
    // FIXME: 需要替换线上 url：'https://api-pan.xunlei.com/drive/v1/resource/filter',
    const res = await axios(
      'https://api-pan.xunlei.com/drive/v1/resource/filter',
      {
        method: 'POST',
        data: data,
        headers: {
          'content-type': 'application/json',
          'x-client-id': ApplicationManager.getCurrentDeviceClientId(),
          'x-device-id': ApplicationManager.getCurrentDeviceDeviceId(),
          'x-client-version-code': ApplicationManager.getCurrentDeviceClientVersion(),
          'x-captcha-token': captchaToken,
        }
      }
    );

    let handled: boolean = false;
    let excludes: number[] = [];
    let retryTime: number = 0;    // 总共可以尝试请求 3 次
    // 请求服务器
    const doRequest = async (): Promise<boolean> => {
      try {
        if (retryTime >= 3) return false;

        if (res.status === 200) {
          const response: IBtIntelligentSelectionResponse = res.data as IBtIntelligentSelectionResponse;

          if (response && response.success) {
            handled = true;
            // 获取不选中的 index 列表
            excludes = generateExclusionRealIndexArray(response.compressed_indices);
            return true;
          } else {
            retryTime += 1;
            return await doRequest();
          }
        } else {
          retryTime += 1;
          return await doRequest();
        }
      } catch {
        retryTime += 1;
        return await doRequest();
      }
    }

    await doRequest();

    return {
      handled,
      excludes
    }
  }

  export async function getIntelligentSelection(btFile: DownloadKernel.BtTaskInfo) {
    // FIXME: 屏蔽智能勾选
    // if (btFile.infoId && btFile.fileLists.length) {
    //   const result = await getRemoteSelectionResult(btFile.infoId);

    //   if (result.handled) {
    //     if (result.excludes.length) {
    //       const exclude_real_index_map = arrayToMap(result.excludes);
    //       for (let index: number = 0; index < btFile.fileLists.length; index++) {
    //         const item = btFile.fileLists[index];
    //         const realIndex: number = Reflect.get(item, 'realIndex');

    //         if (exclude_real_index_map[realIndex]) {
    //           Reflect.set(item, 'select', false);
    //         } else {
    //           Reflect.set(item, 'select', true);
    //         }
    //       }
    //     } else {
    //       for (let index: number = 0; index < btFile.fileLists.length; index++) {
    //         Reflect.set(btFile.fileLists[index], 'select', true);
    //       }
    //     }
    //   }
    // }

    return btFile;
  }

  export async function getBtFileInfoAw(filePath: string) {
    const res = await ParseUrlFileNameNS.parseBtTaskInfo(filePath);

    if (res) {
      if (res.fileLists.length < 1) {
        return res;
      } else {
        const result = await getIntelligentSelection(res);
        return result;
      }
    } else {
      return null;
    }
  }
}