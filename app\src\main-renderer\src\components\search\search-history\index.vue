<template>
  <div class="search-history">
    <!-- 有历史记录时显示 -->
    <div v-if="historyList.length > 0" class="history-section">
      <div class="history-header">
        <span class="history-title">搜索历史</span>
        <PopoverRoot v-model:open="showClearConfirm">
          <PopoverTrigger as-child>
            <div class="clear-btn">
              清空
            </div>
          </PopoverTrigger>
          <PopoverPortal :to="props.portalContainer">
            <PopoverContent
              side="bottom"
              :side-offset="8"
              align="end"
              class="clear-confirm-popover"
            >
              <div class="confirm-content">
                <div class="confirm-message">确认清空所有搜索历史？</div>
                <div class="confirm-actions">
                  <PopoverClose as-child>
                    <div class="cancel-btn">取消</div>
                  </PopoverClose>
                  <div class="confirm-btn" @click="handleConfirmClear">清空</div>
                </div>
              </div>
            </PopoverContent>
          </PopoverPortal>
        </PopoverRoot>
      </div>
      
      <!-- 历史记录标签列表 -->
      <TagsInputRoot
        :model-value="displayHistoryList"
        class="history-tags-container"
        :max="maxDisplayCount"
        readonly
      >
        <TagsInputItem
          v-for="(item, index) in displayHistoryList"
          :key="`history-${index}`"
          :value="item"
          class="history-tag"
          @click="handleSelectHistory(item)"
        >
          <TagsInputItemText class="history-tag-text" />
          <TagsInputItemDelete 
            class="history-tag-delete"
            @click.stop="handleConfirmDelete(index)"
          >
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
            <circle cx="6" cy="6" r="6" fill="#C9CDD4"/>
            <path d="M7.78267 3.66649C7.93784 3.56422 8.14899 3.58084 8.2856 3.71727C8.42218 3.85385 8.43864 4.06498 8.33639 4.2202L8.2856 4.28368L6.56783 6.00048L8.28463 7.71727L8.33541 7.78075C8.43777 7.93599 8.42125 8.14706 8.28463 8.28368C8.148 8.4203 7.93694 8.43682 7.7817 8.33446L7.71822 8.28368L6.00142 6.56688L4.2856 8.28368L4.22213 8.33446C4.06689 8.43682 3.85582 8.4203 3.7192 8.28368C3.58275 8.14705 3.5661 7.93592 3.66842 7.78075L3.7192 7.71727L5.43502 6.00048L3.71822 4.28368L3.66744 4.2202C3.56531 4.06499 3.58169 3.8538 3.71822 3.71727C3.85475 3.58075 4.06594 3.56436 4.22115 3.66649L4.28463 3.71727L6.00142 5.43407L7.7192 3.71727L7.78267 3.66649Z" fill="white"/>
          </svg>
          </TagsInputItemDelete>
        </TagsInputItem>
      </TagsInputRoot>
    </div>

    <!-- 无历史记录时显示 -->
    <div v-else class="empty-history">
      <span class="empty-text">输入关键词开始搜索</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  TagsInputRoot, 
  TagsInputItem, 
  TagsInputItemText, 
  TagsInputItemDelete,
  PopoverRoot,
  PopoverTrigger,
  PopoverPortal,
  PopoverContent,
  PopoverClose
} from 'reka-ui'

interface Props {
  /** 历史记录列表 */
  modelValue: string[]
  /** 最大显示条数，默认显示2行 */
  maxDisplayCount?: number
  /** 弹窗容器，用于Portal渲染 */
  portalContainer?: HTMLElement | string
}

interface Emits {
  /** 选择历史记录 */
  (e: 'select', keyword: string): void
  /** 删除单条历史记录 */
  (e: 'delete', index: number): void
  /** 清空所有历史记录 */
  (e: 'clear'): void
  /** 更新历史记录列表 */
  (e: 'update:modelValue', value: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  maxDisplayCount: 10 // 默认显示2行，每行大概5个
})

const emit = defineEmits<Emits>()

// 内部状态
const historyList = ref<string[]>([...props.modelValue])
const showClearConfirm = ref(false)

// 计算属性
const displayHistoryList = computed(() => {
  return historyList.value.slice(0, props.maxDisplayCount)
})

// 事件处理函数
const handleSelectHistory = (keyword: string) => {
  emit('select', keyword)
}

const handleConfirmClear = () => {
  emit('clear')
  showClearConfirm.value = false
}

const handleConfirmDelete = (index: number) => {
  emit('delete', index)
}

// 监听 props 变化
watch(() => props.modelValue, (newVal) => {
  historyList.value = [...newVal]
}, { deep: true })
</script>

<style lang="scss" scoped>
.search-history {
  min-height: 214px;
}

.history-section {
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    
    .history-title {
      font-size: 12px;
      color: var(--font-font-3, #86909C);
      line-height: 20px;
    }
    
    .clear-btn {
      color: var(--primary-primary-default, #226DF5);
      text-align: right;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 12px;
      line-height: 20px;
      padding: 0;
    }
  }
}

.history-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  max-width: 100%;
  max-height: 66px;
  
  .history-tag {
    position: relative;
    display: flex;
    height: 28px;
    padding: 6px 8px;
    border-radius: 6px;
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
    cursor: pointer;
    color: var(--font-font-2, #4E5769);
    transition: all 0.2s ease;
    border: none;
    
    &:hover {
      color: var(--primary-primary-default, #226DF5);
      
      .history-tag-delete {
        opacity: 1;
      }
    }
    
    .history-tag-text {
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 110px;
    }
    
    .history-tag-delete {
      opacity: 0;
      position: absolute;
      right: -4px;
      top: -2px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 12px;
      height: 12px;

      svg {
        width: 12px;
        height: 12px;
      }
    }
  }
}

.empty-history {
  width: 100%;
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32px;
  
  .empty-text {
    font-size: 13px;
    color: var(--font-font-3, #86909C);
  }
}
</style>

<style lang="scss">
/* Popover 弹窗样式 - 无scoped，用于Portal渲染的组件 */
.clear-confirm-popover,
.delete-confirm-popover {
  background: var(--background-background-elevated, #FFF);
  border-radius: var(--border-radius-M2, 10px);
  box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
  padding: 16px;
  width: 260px;
  z-index: 1000;
  
  .confirm-content {
    .confirm-message {
      font-size: 14px;
      color: var(--font-font-1, #272E3B);
      margin-bottom: 16px;
      text-align: left;
      line-height: 20px;
    }
    
    .confirm-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      
      .cancel-btn, .confirm-btn {
        width: 76px;
        height: 32px;
        border-radius: var(--border-radius-S, 6px);
        font-size: 13px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .cancel-btn {
        background: var(--button-button2-default, #F2F3F5);
        color: var(--font-font-2, #4E5769);
        
        &:hover {
          background: var(--background-bg-3, #E5E6EB);
        }
      }
      
      .confirm-btn {
        background: var(--button-button-warn-default, #FFECE8);
        color: var(--button-button-warn-font-default, #FF4D4F);
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style> 