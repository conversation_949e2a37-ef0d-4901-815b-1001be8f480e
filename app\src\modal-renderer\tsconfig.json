{
  "extends": "../../tsconfig.web.json",
  "include": [
    "src/**/*.ts",
    "src/**/*.vue",
    "types/**/*.ts",
    "../common/**/*.ts",
    "../common/**/*.vue",
    "../main-renderer/src/**/*.ts",
    "../main-renderer/src/**/*.vue",
    "../@types/global.d.ts",
    "../player-plugin/**/*.ts",
    "../player-plugin/**/*.vue",
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@root/*": ["../*"]
    },
    "skipLibCheck": true,
    "noImplicitAny": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,
    "strictNullChecks": false,
    "noEmit": true,
    "allowJs": true,
    "checkJs": false,
    "strict": false
  }
}
