#include <string>

#include "shadow_image.h"
#include "scale_helper.h"
#include "gdiplus_helper.h"
#include "../text/transcode.h"

ShadowImage::ShadowImage(int dpi_factor, std::string& bkg_image, CRect& corner)
	: bkg_image_(NULL)
	, dpi_factor_(dpi_factor)
{
	if (corner.left == 0 && corner.right == 0 && corner.top == 0 && corner.bottom == 0)
	{
		corner_.SetRect(8, 8, 8, 8);
	}
	else
	{
		corner_ = corner;
	}
	std::wstring bkgImage;
	xl::text::transcode::UTF8_to_Unicode(bkg_image.c_str(), bkg_image.length(), bkgImage);
	bkg_image_ = Gdiplus::Image::FromFile(bkgImage.c_str());
}

ShadowImage::~ShadowImage()
{
	if (bkg_image_)
	{
		delete bkg_image_;
		bkg_image_ = NULL;
	}
}

void ShadowImage::SetDPIFactor(UINT dpi_factor)
{
	if (dpi_factor_ != dpi_factor)
	{
		dpi_factor_ = dpi_factor;
	}
}

void ShadowImage::Render(
	Gdiplus::Graphics& graphics,
	const Gdiplus::Rect& item_rect,
	const Gdiplus::Rect& paint_rect,
	BYTE alpha)
{
	if (!bkg_image_)
	{
		return;
	}
	CRect dest_corner_rect = ScaleHelper::Scale(corner_, dpi_factor_);
	Gdiplus::RectF gdi_paint_rect(
		(Gdiplus::REAL)paint_rect.GetLeft(),
		(Gdiplus::REAL)paint_rect.GetTop(),
		(Gdiplus::REAL)paint_rect.Width,
		(Gdiplus::REAL)paint_rect.Height);
	Gdiplus::RectF temp_rect;
	UINT image_width = bkg_image_->GetWidth();
	UINT image_height = bkg_image_->GetHeight();

	// left-top
	if (corner_.left > 0 && corner_.top > 0)
	{
		Gdiplus::RectF dest_rect(
			(Gdiplus::REAL)item_rect.GetLeft(),
			(Gdiplus::REAL)item_rect.GetTop(),
			(Gdiplus::REAL)dest_corner_rect.left,
			(Gdiplus::REAL)dest_corner_rect.top);
		if (Gdiplus::RectF::Intersect(temp_rect, gdi_paint_rect, dest_rect))
		{
			Gdiplus::RectF rcSrc(
				0,
				0,
				(Gdiplus::REAL)corner_.left,
				(Gdiplus::REAL)corner_.top);
			GdiplusHelper::DrawImage(
				&graphics,
				bkg_image_,
				dest_rect,
				rcSrc,
				(Gdiplus::REAL)alpha);
		}
	}
	// top
	if (corner_.top > 0)
	{
		Gdiplus::RectF dest_rect(
			(Gdiplus::REAL)item_rect.GetLeft() + dest_corner_rect.left,
			(Gdiplus::REAL)item_rect.GetTop(),
			(Gdiplus::REAL)item_rect.Width - dest_corner_rect.left - dest_corner_rect.right,
			(Gdiplus::REAL)dest_corner_rect.top);
		if (Gdiplus::RectF::Intersect(temp_rect, gdi_paint_rect, dest_rect))
		{
			Gdiplus::RectF src_rect(
				(Gdiplus::REAL)corner_.left,
				(Gdiplus::REAL)0,
				(Gdiplus::REAL)image_width - corner_.left - corner_.right,
				(Gdiplus::REAL)corner_.top);
			GdiplusHelper::DrawImage(
				&graphics,
				bkg_image_,
				dest_rect,
				src_rect,
				(Gdiplus::REAL)alpha);
		}
	}
	// right-top
	if (corner_.right > 0 && corner_.top > 0)
	{
		Gdiplus::RectF dest_rect(
			(Gdiplus::REAL)item_rect.GetRight() - dest_corner_rect.right,
			(Gdiplus::REAL)item_rect.GetTop(),
			(Gdiplus::REAL)dest_corner_rect.right,
			(Gdiplus::REAL)dest_corner_rect.top);
		if (Gdiplus::RectF::Intersect(temp_rect, gdi_paint_rect, dest_rect))
		{
			Gdiplus::RectF src_rect(
				(Gdiplus::REAL)image_width - corner_.right,
				(Gdiplus::REAL)0,
				(Gdiplus::REAL)corner_.right,
				(Gdiplus::REAL)corner_.top);
			GdiplusHelper::DrawImage(
				&graphics,
				bkg_image_,
				dest_rect,
				src_rect,
				(Gdiplus::REAL)alpha);
		}
	}
	// left
	if (corner_.left > 0)
	{
		Gdiplus::RectF dest_rect(
			(Gdiplus::REAL)item_rect.GetLeft(),
			(Gdiplus::REAL)item_rect.GetTop() + dest_corner_rect.top,
			(Gdiplus::REAL)dest_corner_rect.left,
			(Gdiplus::REAL)item_rect.Height - dest_corner_rect.top - dest_corner_rect.bottom);
		if (Gdiplus::RectF::Intersect(temp_rect, gdi_paint_rect, dest_rect))
		{
			Gdiplus::RectF src_rect(
				(Gdiplus::REAL)0,
				(Gdiplus::REAL)corner_.top,
				(Gdiplus::REAL)corner_.left,
				(Gdiplus::REAL)image_height - corner_.top - corner_.bottom);
			GdiplusHelper::DrawImage(
				&graphics,
				bkg_image_,
				dest_rect,
				src_rect,
				(Gdiplus::REAL)alpha);
		}
	}
	// right
	if (corner_.right > 0)
	{
		Gdiplus::RectF dest_rect(
			(Gdiplus::REAL)item_rect.GetRight() - dest_corner_rect.right,
			(Gdiplus::REAL)item_rect.GetTop() + dest_corner_rect.top,
			(Gdiplus::REAL)dest_corner_rect.right,
			(Gdiplus::REAL)item_rect.Height - dest_corner_rect.top - dest_corner_rect.bottom);
		if (Gdiplus::RectF::Intersect(temp_rect, gdi_paint_rect, dest_rect))
		{
			Gdiplus::RectF src_rect(
				(Gdiplus::REAL)image_width - corner_.right,
				(Gdiplus::REAL)corner_.top,
				(Gdiplus::REAL)corner_.right,
				(Gdiplus::REAL)image_height - corner_.top - corner_.bottom);
			GdiplusHelper::DrawImage(
				&graphics,
				bkg_image_,
				dest_rect,
				src_rect,
				(Gdiplus::REAL)alpha);
		}
	}
	// left-bottom
	if (corner_.left > 0 && corner_.bottom > 0)
	{
		Gdiplus::RectF dest_rect(
			(Gdiplus::REAL)item_rect.GetLeft(),
			(Gdiplus::REAL)item_rect.GetBottom() - dest_corner_rect.bottom,
			(Gdiplus::REAL)dest_corner_rect.left,
			(Gdiplus::REAL)dest_corner_rect.bottom);
		if (Gdiplus::RectF::Intersect(temp_rect, gdi_paint_rect, dest_rect))
		{
			Gdiplus::RectF src_rect(
				(Gdiplus::REAL)0,
				(Gdiplus::REAL)image_height - corner_.bottom,
				(Gdiplus::REAL)corner_.left,
				(Gdiplus::REAL)corner_.bottom);
			GdiplusHelper::DrawImage(
				&graphics,
				bkg_image_,
				dest_rect,
				src_rect,
				(Gdiplus::REAL)alpha);
		}
	}
	// bottom
	if (corner_.bottom > 0)
	{
		Gdiplus::RectF dest_rect(
			(Gdiplus::REAL)item_rect.GetLeft() + dest_corner_rect.left,
			(Gdiplus::REAL)item_rect.GetBottom() - dest_corner_rect.bottom,
			(Gdiplus::REAL)item_rect.Width - dest_corner_rect.left - dest_corner_rect.right,
			(Gdiplus::REAL)dest_corner_rect.bottom);
		if (Gdiplus::RectF::Intersect(temp_rect, gdi_paint_rect, dest_rect))
		{
			Gdiplus::RectF src_rect(
				(Gdiplus::REAL)corner_.left,
				(Gdiplus::REAL)image_height - corner_.bottom,
				(Gdiplus::REAL)image_width - corner_.left - corner_.right,
				(Gdiplus::REAL)corner_.bottom);
			GdiplusHelper::DrawImage(
				&graphics,
				bkg_image_,
				dest_rect,
				src_rect,
				(Gdiplus::REAL)alpha);
		}
	}
	// right-bottom
	if (corner_.right > 0 && corner_.bottom > 0)
	{
		Gdiplus::RectF dest_rect(
			(Gdiplus::REAL)item_rect.GetRight() - dest_corner_rect.right,
			(Gdiplus::REAL)item_rect.GetBottom() - dest_corner_rect.bottom,
			(Gdiplus::REAL)dest_corner_rect.right,
			(Gdiplus::REAL)dest_corner_rect.bottom);
		if (Gdiplus::RectF::Intersect(temp_rect, gdi_paint_rect, dest_rect))
		{
			Gdiplus::RectF src_rect(
				(Gdiplus::REAL)image_width - corner_.right,
				(Gdiplus::REAL)image_height - corner_.bottom,
				(Gdiplus::REAL)corner_.right,
				(Gdiplus::REAL)corner_.bottom);
			GdiplusHelper::DrawImage(
				&graphics,
				bkg_image_,
				dest_rect,
				src_rect,
				(Gdiplus::REAL)alpha);
		}
	}
}
