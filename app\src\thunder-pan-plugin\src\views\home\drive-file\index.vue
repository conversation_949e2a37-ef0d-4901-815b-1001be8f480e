<script lang="ts" setup>
import FileList from '@/components/file-list/index.vue'

import { DriveFileManager, TypeDriveSortInfo } from '@/manager/drive-file-manager';
import { TPanRoute, useDriveRouterStore } from '@/store/drive-router-store';
import { computed, onMounted, useTemplateRef, watch, ref, reactive } from 'vue';
import { API_FILE } from '@root/common/thunder-pan-manager/pan-sdk/types';
import { GlobalEventHelper } from '../../../utils/global-event-helper';
import { SYSTEM_FOLDER_TYPE_LIST } from '@root/common/thunder-pan-manager/pan-sdk/utils/file';
import { sleep } from '@root/common/thunder-pan-manager/pan-sdk/utils/basic';
import { ETabId } from '@/manager/tabs-manager';
import { EPanPage, useHistoryStore } from '@/store/history-store';
import { IPayloadData } from '@/manager/message-center-manager';
import { FilterManager } from '@/manager/filter-manager';

interface IDynamicPickSet {
  file?: API_FILE.DriveFile
  isHighlight?: boolean
}

const currentFileData = DriveFileManager.getInstance().getCurrentData()
const { pushState, replaceState } = useHistoryStore()
const { routerList_computed, isInSafeBoxFolder, isInPrivilegeFolder, isInFavoriteFolder, currentParentFile, setRouterList } = useDriveRouterStore()

const isLoading = ref(false)
const currentHighlightIds = ref<string[]>([])
const dynamicPickSet: IDynamicPickSet = reactive<IDynamicPickSet>({})
const FileListVm = useTemplateRef('FileListVm')

const currentFileList = computed(() => currentFileData.fileList)
const currentPickedIds = computed(() => currentFileData.pickedIds)
const currentSortInfo = computed(() => currentFileData.sortInfo)

function handleCleanPicked() {
  DriveFileManager.getInstance().cleanPicked()
  handleCleanHighlight()
}

function handleSetPickedIds (ids: string[]) {
  DriveFileManager.getInstance().setPickedIds(ids)
  handleCleanHighlight()
}

function handleCleanHighlight () {
  currentHighlightIds.value.splice(0, currentHighlightIds.value.length)
}

function handlePickedIdsChange(isSelectAll: boolean, canPickIds: string[]) {
  if (!isSelectAll) {
    handleCleanPicked()
  } else {
    handleSetPickedIds(canPickIds)
  }
}

async function handleHeaderSorterClick (sortInfo: TypeDriveSortInfo) {
  DriveFileManager.getInstance().updateSortInfoByKey('all_' + currentParentFile.value.id, sortInfo)

  isLoading.value = true
  await DriveFileManager.getInstance().getCurrentFileList(currentParentFile.value.id, { reset: true })
  isLoading.value = false

  handleUpdateFileListScroller()
}

function handleCheckChange (file: API_FILE.DriveFile, isForce: boolean = false) {
  DriveFileManager.getInstance().togglePickedId(file.id!, isForce)
  handleCleanHighlight()
}

function handleSetRouterList (newList: TPanRoute[]) {
  pushState({
    page: EPanPage.HOME,
    tab: ETabId.ALL,
    routes: newList
  })

  setRouterList(newList)
}

async function handleListScrollEnd () {
  if (currentFileData.fileListPageToken) {
    await DriveFileManager.getInstance().getCurrentFileList(currentParentFile.value.id)
    handleUpdateFileListScroller()
  }
}

async function checkPickOrHighlight (file: API_FILE.DriveFile) {
  // 目标文件不是系统文件，则选中；否则高亮
  if (!SYSTEM_FOLDER_TYPE_LIST.includes(file.folder_type!) && !dynamicPickSet.isHighlight) {
    DriveFileManager.getInstance().togglePickedId(file.id!, true)
  } else {
    currentHighlightIds.value.push(file.id!)
  }

  let targetIndex = -1
  currentFileList.value.forEach((_file, index) => {
    if (_file.id === file.id && targetIndex === -1) {
      targetIndex = index
    }
  })

  await sleep(20)
  // 如果在文件数据列表中，则滚动到指定位置
  if (targetIndex > -1) {
    handleFileListScrollToItem(targetIndex)
  }
  // 不在列表中，将该文件插入到顶部，并滚动到顶部
  else {
    DriveFileManager.getInstance().setFilterFile(file)
    DriveFileManager.getInstance().appendFiles([ file ])
    await sleep(20)
    handleFileListScrollToItem(0)
  }

  // 清空
  dynamicPickSet.file = undefined
  dynamicPickSet.isHighlight = false
}

async function getCurrentFileList (needRefresh: boolean = true) {
  if (needRefresh) {
    isLoading.value = true
    await DriveFileManager.getInstance().getCurrentFileList(currentParentFile.value.id, { reset: true })
    isLoading.value = false
  }

  if (dynamicPickSet.file) {
    checkPickOrHighlight(dynamicPickSet.file)
  }
}

function handleUpdateFileListScroller() {
  if (FileListVm.value) {
    FileListVm.value.updateScroller()
  }
}

function handleFileListScrollToItem(index: number) {
  if (FileListVm.value) {
    FileListVm.value.scrollToItem(index)
  }
}

watch(routerList_computed, (newRouterList, oldRouterList) => {
  const oldParentFile = oldRouterList.slice(-1)[0]
  const newParentFile = newRouterList.slice(-1)[0]
  const needRefresh = newParentFile.id !== oldParentFile.id

  FilterManager.getInstance().resetByKey(`all_${currentParentFile.value.id}`, true)
  handleCleanPicked()
  getCurrentFileList(needRefresh)
})

watch(currentFileList, () => {
  handleUpdateFileListScroller()
}, { deep: true })

onMounted(async () => {
  getCurrentFileList()
  // 全局事件监听
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.DRIVE_LIST_REFRESH, () => {
    getCurrentFileList()
    handleCleanPicked()
    replaceState({
      page: EPanPage.HOME,
      tab: ETabId.ALL,
      routes: routerList_computed.value as any
    })
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.DRIVE_LIST_CLEAN_PICKED, () => {
    handleCleanPicked()
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.DRIVE_LIST_PICK_OR_HIGHLIGHT_FILE, (file: API_FILE.DriveFile, isHighlight: boolean, isTriggerCheck: boolean) => {
    dynamicPickSet.file = file
    dynamicPickSet.isHighlight = isHighlight

    if (isTriggerCheck) {
      checkPickOrHighlight(file)
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_TRASH, (ids: string[]) => {
    DriveFileManager.getInstance().batchDeleteFile(ids)
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_UNTRASH, () => {
    // 回收站还原文件只会还原到根目录，如果当前在根目录则刷新数据
    if (currentParentFile.value.id === '') {
      getCurrentFileList()
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_DECOMPRESS, (parentId: string) => {
    if (currentParentFile.value.id === parentId) {
      getCurrentFileList()
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_UPLOADED, (parentId: string) => {
    if (currentParentFile.value.id === parentId) {
      getCurrentFileList()
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_COPY, (data: IPayloadData) => {
    if (currentParentFile.value.id === data.params.parentid) {
      getCurrentFileList()
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_MOVE, (data: IPayloadData) => {
    if (currentParentFile.value.id === data.params.parentid) {
      getCurrentFileList()
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.CLOUD_ADD_TASK_SUCCESS, (data: IPayloadData) => {
    if (currentParentFile.value.id === data.reference_resource?.parent_id) {
      getCurrentFileList()
    }
  })
})
</script>

<template>
  <div class="drive-file-container">
    <FileList
      ref="FileListVm"
      :parent-from="ETabId.ALL"
      :is-loading="isLoading"
      :parent-file="currentParentFile"
      :file-list="currentFileList"
      :picked-ids="currentPickedIds"
      :highlight-ids="currentHighlightIds"
      :sort-info="currentSortInfo"
      :breadcrumb="routerList_computed"
      :is-in-favorite-folder="isInFavoriteFolder"
      :is-in-privilege-folder="isInPrivilegeFolder"
      :is-in-safe-box-folder="isInSafeBoxFolder"
      @header-clean-picked="handleCleanPicked"
      @header-check-change="handlePickedIdsChange"
      @header-sorter-click="handleHeaderSorterClick"
      @set-picked-ids="handleSetPickedIds"
      @item-picked-change="handleCheckChange"
      @set-route-list="handleSetRouterList"
      @list-scroll-end="handleListScrollEnd"
    />
  </div>
</template>

<style lang="scss" scoped>
.drive-file-container {
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
</style>
