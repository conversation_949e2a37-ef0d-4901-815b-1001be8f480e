<template>
  <div 
    class="search-cloud-item" 
    :class="{ 
      'search-cloud-item-focused': focused, 
      'search-item-focused': focused, 
      'search-cloud-item-error': isError
    }"
    @click="handleClick"
    @mouseenter="$emit('mouseenter')"
  >
    <!-- 文件图标 -->
    <div class="search-cloud-item-left">
      <img :src="props.file.IconLink" alt="file-icon" :class="['search-cloud-item-icon-img', { 'is-error': isError }]">
    </div>
    <!-- 文件信息 -->
    <div class="search-cloud-item-content">
      <div :class="['search-cloud-item-name', { 'is-error': isError }]">
        <span v-html="highlightText(file.Name)" v-tooltip="{ content: file.Name, maxWidth: '340px', appendTo: 'parent' }"></span>
      </div>
      <div :class="['search-cloud-item-info', { 'is-error': isError }]">
        {{ infoText }}
      </div>
    </div>
    <!-- 定位按钮 -->
    <div class="search-cloud-item-right">
      <button class="search-cloud-item-locate-btn" @click.stop="handleLocate" v-tooltip="'定位文件'">
        <i class="xl-icon-general-location-l"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { message } from '@root/common/components/ui/message/index'
import dayjs from 'dayjs'
import { CloudFile } from '../types'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'

const props = defineProps<{
  file: CloudFile
  focused: boolean
  highLightWords?: string[]
}>()

const emit = defineEmits<{
  select: [file: CloudFile]
  locate: [file: CloudFile]
  mouseenter: []
}>()

const isError = computed(() => props.file.Status === 3 || (props.file?.Audit && props.file.Audit.Status !== 1))

// 高亮搜索文本
const highlightText = (text: string) => {
  if (!props.highLightWords?.length || !text) return text
  
  // 对每个搜索词做一次全局高亮
  return props.highLightWords.reduce((str, word) => {
    if (!word) return str;
    const escaped = word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escaped})`, 'gi');
    return str.replace(regex, '<span class="highlight">$1</span>');
  }, text);
}

const infoText = computed(() => {
  if (props.file?.Audit && props.file.Audit.Status !== 1) {
    return props.file.Audit.Message
  }
  const size = formatFileSize(props.file.Size)
  const time = formatTime(props.file.UpdateAt)
  // 显示大小+时间
  return `${size}  ${time}`
})


function formatFileSize(size: number): string {
  if (size === 0 || !size) return '0B'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  let fileSize = size
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }
  return `${fileSize.toFixed(2)} ${units[index]}`
}

function formatTime(time: number | string): string {
  if (!time) return '-'
  return dayjs(time).format('YYYY-MM-DD  HH:mm')
}

function handleClick() {
  if (isError.value) {
    message({ message: '文件已失效，无法打开', type: 'warning' })
    return
  }
  if (props.file.Type === 1) {
    emit('locate', props.file)
    return
  } else {
    ThunderPanClientSDK.getInstance().consumeFileById(props.file.FileID, props.file.Space)
    emit('select', props.file)
  }
}

function handleLocate() {
  emit('locate', props.file)
}
</script>

<style lang="scss" scoped>
.search-cloud-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: var(--border-radius-M2, 10px);
  cursor: pointer;
  transition: background-color 0.2s ease;
  height: 68px;

  &:hover,
  &-focused {
    background: var(--background-bg-2, #F2F3F5);
  }

  &-left {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
  }

  &-icon-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
  }

  &-content {
    position: relative;
    flex: 1;
    margin-left: 16px;
    min-width: 0;
  }

  &-name {
    text-align: left;
    color: var(--font-font-1);
    font-size: 13px;
    line-height: 22px;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    :deep(.highlight) {
      color: var(--primary-primary-default, #226DF5);
    }

    &.is-error {
      color: var(--font-font-4);

      :deep(.highlight) {
        color: var(--primary-primary-disabled, #97C4FB);
      }
    }
  }

  &-info {
    color: var(--font-font-3, rgba(137, 142, 151, 1));
    font-size: 12px;
    line-height: 20px;

    &.is-error {
      color: var(--functional-error-default, #FF4D4F);
    }
  }

  &-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
  }

  &-locate-btn {
    width: 24px;
    height: 24px;
    display: none;
    align-items: center;
    justify-content: center;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    i {
      font-size: 20px;
      color: var(--font-font-2, #4e5769);
    }

    &:hover i {
      color: var(--primary-primary-default, #226DF5);
    }
  }

  &:hover,
  &-focused {
    .search-cloud-item-locate-btn {
      display: flex;
    }
  }
}
</style> 