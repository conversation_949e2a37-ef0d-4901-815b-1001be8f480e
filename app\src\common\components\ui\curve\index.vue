<script lang="ts" setup>
import { ref, computed } from 'vue'
import { BSpline } from './b-spline';

import { useTaskDetailStore } from '@/stores/taskDetail'

const taskDetailStore = useTaskDetailStore()
/**
 * 速度条曲线颜色
 */
interface ISpeedCurveColor {
  gradientColorEnd: string;
  gradientColorStart: string;
  lineColorEnd: string;
  lineColorStart: string;
}
interface ITransformedData {
  xValue: number;
  yValue: number;
  x: number;
  y: number;
}
interface IDataPoint  {
  x: number
  y: number
}
interface IProps {
  // dataList: IDataPoint[];
  curveColor?: string;
  pointColor?: string;
  bk1?: string;
  bk2?: string;
  scale?: number;
  isDownload?: boolean;
  step?: number
  paddingTopPercent?: number
  paddingBottomPercent?: number
}

const speedCurveColor: ISpeedCurveColor = { // 普通速度的曲线颜色
  gradientColorEnd: 'rgba(63,133,255,.1)',
  gradientColorStart: 'rgba(63,133,255,.5)',
  lineColorEnd: '#3F85FF',
  lineColorStart: '#3F85FF'
};
const props = withDefaults(defineProps<IProps>(), {
  // dataList: () => [],
  isDownload: false,
  width: 450,
  height: 178,
  curveColor: "rgba(34, 109, 245, 0.7)",
  pointColor: "#fff",
  bk1: "rgba(34, 109, 245, 0.5)",
  bk2: "rgba(34, 109, 245, 0.05)",
  scale: Math.max(window.devicePixelRatio, 2), // 分辨率 window.webkitDevicePixelRatio
  step: 1,
  paddingTopPercent: 8,
  paddingBottomPercent: 0,
})

const width = ref(0)
const height = ref(0)
const curveTop = ref(155)
const curveLeft = ref(0)
const requireAnimation = ref<number>(-1)
const options = ref<any[]>([])
const maxValue = ref(0) // 记录最大值
const userSpeedCurveColor = ref({ // 根据登录用户判断Vip加速曲线颜色
  gradientColorEnd: 'rgba(63,133,255,.1)',
  gradientColorStart: 'rgba(63,133,255,.5)',
  lineColorEnd: '#3f85ff',
  lineColorStart: '#3f85ff'
})
const progress = ref(0) // 记录当前进度
const points = ref<IDataPoint[]>([]) // 记录当前进度的点
const lastData = ref<any>([])
const xPoint = ref<number>(0)
const yPoint = ref<number>(0)
const lastPointX = ref<number>(0)
const lastPointY = ref<number>(0)
const lastPoint = ref<number>(0)
const lastMaxValue = ref<number>(0)
const curTaskIsTryTask = ref<boolean>(false)
const historyVipSpeedColorMap = ref<any>({})
const frame = ref(10)
const historyMaxValue = ref(0) // 当前节点前的历史数据中的最大值
const maxValueIncrease = ref(0)
const canvasBox = ref<HTMLCanvasElement>()
const curveCanvas = ref<HTMLCanvasElement>()

// ===============================================================

// 从data[0]开始, 画一条线
const renderPath = (ctx: any, data: any): number => {
  if (data.length === 0) return 0;
  ctx.moveTo(data[0].x, data[0].y);
  let n: number = 0;
  for (let i: number = 1; i < data.length; i++) {
    if (data[i]) {
      ctx.lineTo(data[i].x, data[i].y);
      n = i;
    }
  }
  return n;
}

// 画渐变线和渐变色填充
const drawLineAndGradient = (ctx: any, data: any, colorStart: any, colorEnd: any, gradientStart: any, gradientEnd: any, lineWidth: any, points: any): void  => {
  if (!data.length || !points.length) {
    curveTop.value = 20;
    return;
  }
  ctx.lineWidth = lineWidth * props.scale;
  // 绘制曲线
  ctx.beginPath();
  ctx.moveTo((points[0] || data[0]).x, (points[0] || data[0]).y);
  const n: number = renderPath(ctx, points);
  if (points[n]) {
    curveLeft.value = points[n].x / props.scale - 8;
    curveTop.value = (points[n].y) / props.scale - 10;
  } else if (points.length === 0) { // 只有一个点时, 恢复初始化值
    curveLeft.value = 0;
    curveTop.value = height.value / props.scale - 10;
  }
  const lineStroke: any = ctx.createLinearGradient(
    0,
    0,
    data[data.length - 1].x,
    0
  );
  lineStroke.addColorStop(0, colorStart);
  lineStroke.addColorStop(1, colorEnd);
  ctx.strokeStyle = lineStroke;

  ctx.stroke();
  // 渐变色填充
  // let n: number = this.renderPath(ctx, points);

  // let n: number = points.length - 1;
  ctx.lineTo(points[n].x, height.value);
  ctx.lineTo(points[0].x, height.value);
  ctx.lineTo(points[0].x, points[0].y);
  const gradient: any = ctx.createLinearGradient(0, 0, 0, height.value);
  gradient.addColorStop(0, gradientStart);
  gradient.addColorStop(1, gradientEnd);
  ctx.fillStyle = gradient;
  ctx.closePath();
  ctx.fill();
}

// 清空canvas
function clearCanvas (): void {
  const canvas: HTMLCanvasElement|undefined = curveCanvas.value;

  if (canvas && canvas.getContext) {
    const ctx: any = canvas.getContext('2d');
    ctx.clearRect(0, 0, width.value, height.value);
  }
}
const downloadSpeedSize = computed(() => {
  return formatSize(props.downloadSpeed)
})

const filteredData = computed(() => {
  return filterData(options.value) || [];
})
/**
 * 将下载进度转换为点集
 * 数据结构如下, 0表示总速度, 1表示加速度, x, y表示坐标, xValue,yValue分别表示下载进度,下载速度,
 * {0: {0: {x:0, xValue:0, y: 191, yValue: 1204224}, 1: {x: 12.08, xValue: 1, y: 201, yValue:861789}},
 * 1: {0: {x:0,xValue:0,y:228, yValue:0}, 1: {x:12, xValue:1, y:215, yValue:421888}}}
 */
function transformedData (): any {
  return filteredData.value.map((data: any) => transformData(data));
}
// 根据下载速度信息, 生成坐标点
function transformData (obj: any = {}): any {
  return Object.keys(obj)
    .map((item: any) => parseInt(item, 10))
    .sort((a: number, b: number) => a - b)
    .map((key: number) => {
      return {
        xValue: key,
        yValue: parseInt(obj[key], 10),
        x: width.value * key / 100,
        y:
          height.value * props.paddingTopPercent / 100 +
          height.value *
            (100 - props.paddingTopPercent - props.paddingBottomPercent) /
            100 *
            (1 - parseInt(obj[key], 10) / (maxValue.value || 1))
      };
    });
}
// 计算最大值
function getMaxValue (datas: any = []): number {
  let maxValue: number = 0;
  datas.forEach((data: any) => {
    maxValue = Math.max(maxValue, parseInt(data, 10));
  });
  return maxValue;
}

// 根据step 过滤一些点(step=1时没有用)
function filterData (lines: any): any {
  return lines.map((lineOption: any) => {
    const data: any = {};
    Object.keys(lineOption.data).forEach((percent: any, i: any, arr: any) => {
      if (percent % props.step === 0 || percent === arr.length) {
        data[percent] = lineOption.data[percent];
      }
    });
    return data;
  });
}

// 根据speedmap的值, 使用bspline算法生成一条线的点集
function bSplineRender (data: ITransformedData[]): {x: number, y: number }[] {
  const spline: BSpline = new BSpline({
    degree: 3,
    points: data,
    tessellation: Math.round(width.value * progress.value / 100 * 1.5)
  });
  return spline.curvePoints.map(([x, y]: any) => Object.freeze({
    x,
    y
  }));
}

// 初始化设置canvas
function initCanvas(): void {
  const canvas: HTMLCanvasElement|undefined = curveCanvas.value
  if (!canvasBox.value) { return }
  width.value = Math.floor(canvasBox.value.clientWidth) * props.scale
  // 40表示底部的下载进度等位置的高度
  height.value = Math.floor(canvasBox.value.clientHeight) * props.scale

  canvas && canvas.setAttribute('width', String(width.value))
  canvas && canvas.setAttribute('height', String(height.value))
  userSpeedCurveColor.value = speedCurveColor
  // if (props.curUserId === '') {
  //   this.vipInfo = {}
  // } else {
  //   this.vipInfo = LoginHelperNS.loginHelper.getVipInfo();
  //   if (this.vipInfo.isVip === '0') {
  //     return;
  //   }
  //   if (this.vipInfo.vasType === '3' || this.vipInfo.vasType === '2') {
  //     this.userSpeedCurveColor = this.whiteVipSpeedCurveColor;
  //   } else if (this.vipInfo.vasType === '5') {// 超级会员
  //     this.userSpeedCurveColor = this.superVipSpeedCurveColor;
  //   }
  // }
}

function draw (needAnimation: boolean = true): void {
  const canvas: HTMLCanvasElement|undefined = curveCanvas.value;
  clearCanvas();
  cancelAnimationFrame(requireAnimation.value);
  console.log('draw canvas: ', JSON.stringify(transformedData()));
  if (width.value === 0) {
    initCanvas();
  }
  if (canvas && canvas.getContext) {
    const ctx: any = canvas.getContext('2d');
    options.value.forEach((item: any, i: any) => {
      const data: any = transformedData()[i];
      const tempPoints: any = bSplineRender(data);
      points.value = tempPoints;
      if (!needAnimation) { // 切换任务的时候初始化
        lastData.value = data;
        lastMaxValue.value = maxValue.value;
        lastPoint.value = data[data.length - 1].xValue || 0;
        lastPointX.value = data[data.length - 1].x || 0;
        lastPointY.value = data[data.length - 1].y || 0;
      } else { // 计算是否出现进度回退, 如果出现了进度回退, 此时不再渲染动画并重新初始化
        if (lastPoint.value > (data[data.length - 1].xValue || 0)) {
          needAnimation = true;
          lastData.value = data;
          lastMaxValue.value = maxValue.value;
          lastPoint.value = data[data.length - 1].xValue || 0;
          lastPointX.value = data[data.length - 1].x || 0;
          lastPointY.value = data[data.length - 1].y || 0;
        }
      }
      ctx.setLineDash([]);
      // 正常绘制
      if (!curTaskIsTryTask.value && !needAnimation) { // 只有不需要动画时才使用draw渲染, 否则都依赖drawpoints渲染
        drawLineAndGradient(
          ctx,
          data,
          userSpeedCurveColor.value.lineColorStart,
          userSpeedCurveColor.value.lineColorEnd,
          userSpeedCurveColor.value.gradientColorStart,
          userSpeedCurveColor.value.gradientColorEnd,
          2,
          tempPoints
        );
      } else if (!needAnimation) {
        // 会员试用速度曲线绘制 只绘制一条线
        if (i === 0) {
          const changePoints: any = [];
          const typeArr: any = [];
          const typeColor: any = {
            0: {
              gradientColorEnd: 'rgba(63,133,255,.1)',
              gradientColorStart: 'rgba(63,133,255,.5)',
              lineColorEnd: '#3F85FF',
              lineColorStart: '#3F85FF'
            },
            1: {
              gradientColorEnd: 'rgba(235,121,113,.1)',
              gradientColorStart: 'rgba(240,134,127,.5)',
              lineColorEnd: '#eb7971',
              lineColorStart: '#eb7971'
            },
            2: {
              gradientColorEnd: 'rgba(235,176,73,.1)',
              gradientColorStart: 'rgba(235,176,73,.5)',
              lineColorEnd: '#ebb049',
              lineColorStart: '#ebb049'
            }
          };
          const data: any = transformedData()[0];
          const tempPoints: any = bSplineRender(data);

          // Object.keys(historyVipSpeedColorMap.value || {}).forEach((item: any) => {
          //   let vipType: any = historyVipSpeedColorMap.value[item] || 0;
          //   if (this.vipInfo.isVip === '1' && vipType === 0) {
          //     vipType = 1;
          //   }
          //   if (typeArr[typeArr.length - 1] !== vipType) {
          //     const transfromItem: any = data.find((item: any) => {
          //       return item.xValue === + item;
          //     });
          //     if (transfromItem) {
          //       changePoints.push((transfromItem || { x: 0 })['x']);
          //       typeArr.push(vipType);
          //     }
          //   }
          // });
          for (let i: number = 0 ; i < changePoints.length; i++) {
            let partPoints: any = [];
            if (i === changePoints.length - 1) {
              partPoints = tempPoints;
            } else {
              const pointIndex: any = tempPoints.findIndex((item: any) => {
                return item.x >= changePoints[i + 1];
              });
              partPoints = tempPoints.splice(0, pointIndex - 1);
              partPoints.push(tempPoints[0]);
            }

            ctx.setLineDash([]);
            drawLineAndGradient(
              ctx,
              data,
              typeColor[typeArr[i]].lineColorStart,
              typeColor[typeArr[i]].lineColorEnd,
              typeColor[typeArr[i]].gradientColorStart,
              typeColor[typeArr[i]].gradientColorEnd,
              2,
              partPoints
            );
          }
        }
      }
    });
    if (needAnimation) {
      requireAnimation.value = requestAnimationFrame(() => drawPoints(ctx, 0));
    }
  }
}

/**
 * 以动画绘制后面部分的曲线
 */
function drawPoints (ctx: any, num: number): void {
  if (num >= 60) {
    return ;
  }
  if (num % (60 / frame.value) !== 0) {
    num += 1;
    requireAnimation.value = requestAnimationFrame(() => drawPoints(ctx, num));
    return;
  } else if (num === 59) {
    maxValue.value = lastMaxValue.value;
  }
  options.value.forEach((item: any, i: any) => {
    // 分次更新maxValue, lastMaxValue 不小于historyMaxValue
    if (num === 0) {
      maxValue.value = lastMaxValue.value;
      const allDatas: any = filteredData.value[0] ? []
        .concat(Object.values(Object.values(filteredData.value[0]))) : [];
      historyMaxValue.value = getMaxValue(allDatas.slice(0, allDatas.length - 1));
      const value: number = allDatas[allDatas.length - 1]; // 当前下载速度
      if (value <= historyMaxValue.value) {
        if (lastMaxValue.value > historyMaxValue.value) { // 变小, 从lastMaxValue 变动到historyMaxValue
          maxValueIncrease.value = (historyMaxValue.value - lastMaxValue.value) / frame.value;
        } else {
          maxValueIncrease.value = 0;
        }
      } else if (value > lastMaxValue.value) { // 变大, 从lastMaxValue 变动到value
        maxValueIncrease.value = (value - lastMaxValue.value) / frame.value;
      }
      lastMaxValue.value = getMaxValue(allDatas);
    }
    maxValue.value += maxValueIncrease.value;
    // let data: any = this.transformedData()[i];
    const getFilteredData: any = Object.assign({}, filteredData.value[0]);
    getFilteredData[progress.value] = Math.min(getFilteredData[progress.value], maxValue.value);
    const data: any = transformData(getFilteredData);

    if (data.length === 0) {
      return;
    }
    const n: number = data.length - 1;
    // console.warn('#### data1: ', JSON.stringify(data[n]));
    // 动画开始时, 计算最后一个点坐标的增量, 用于动画
    if (num === 0) {
      lastData.value = data.slice(0, n);
      if (lastPoint.value === data[n].xValue || n === 0) { // 进度不变或者只有一个点
        xPoint.value = 0;
        lastData.value[n] = { x: lastPointX.value, y: lastPointY.value }; // 上次渲染的最后一个点
        yPoint.value = (data[n].y - lastPointY.value) / frame.value;
      } else {
        xPoint.value = (data[n].x - data[n - 1].x) / frame.value;
        yPoint.value = (data[n].y - data[n - 1].y) / frame.value;
        lastData.value[n] = { xValue: data[n - 1].xValue, yValue: data[n - 1].yValue, x: data[n - 1].x, y: data[n - 1].y }; // 取最后第二个点
      }
      lastPoint.value = data[n].xValue;
      lastPointX.value = data[n].x;
      lastPointY.value = data[n].y;
    } else {
      // 重新赋值data, 用于前面的点的上下移动, 否则前面的点会出现上下跳动
      data[n].x = lastData.value[n].x;
      data[n].y = lastData.value[n].y;
      lastData.value = data;
    }
    lastData.value[n].x = lastData.value[n].x + xPoint.value;
    lastData.value[n].y = lastData.value[n].y + yPoint.value;
    const tempPoints: any = bSplineRender(lastData.value);
    // ctx.setLineDash([]); 绘制虚线, 废弃
    clearCanvas();
    // 当前任务 不是试用任务
    if (curTaskIsTryTask.value === false) {
      drawLineAndGradient(
        ctx,
        lastData.value,
        userSpeedCurveColor.value.lineColorStart,
        userSpeedCurveColor.value.lineColorEnd,
        userSpeedCurveColor.value.gradientColorStart,
        userSpeedCurveColor.value.gradientColorEnd,
        2,
        tempPoints
      );
    } else {
      // 试用曲线只画一条线
      if (i === 0) {
        // 会员试用速度曲线绘制
        const changePoints: any = [];
        const typeArr: any = [];
        const typeColor: any = {
          0: {
            gradientColorEnd: 'rgba(63,133,255,.1)',
            gradientColorStart: 'rgba(63,133,255,.5)',
            lineColorEnd: '#3F85FF',
            lineColorStart: '#3F85FF'
          },
          1: {
            gradientColorEnd: 'rgba(235,121,113,.1)',
            gradientColorStart: 'rgba(240,134,127,.5)',
            lineColorEnd: '#eb7971',
            lineColorStart: '#eb7971'
          },
          2: {
            gradientColorEnd: 'rgba(235,176,73,.1)',
            gradientColorStart: 'rgba(235,176,73,.5)',
            lineColorEnd: '#ebb049',
            lineColorStart: '#ebb049'
          }
        };
        // Object.keys(this.historyVipSpeedColorMap || {}).forEach((progress: any) => {
        //   let vipType: any = this.historyVipSpeedColorMap[progress] || 0;
        //   if (this.vipInfo.isVip === '1' && vipType === 0) {
        //     vipType = 1;
        //   }
        //   if (typeArr[typeArr.length - 1] !== vipType) {
        //     const transfromItem: any = lastData.value.find((item: any) => {
        //       return item.xValue === + progress;
        //     });
        //     if (transfromItem) {
        //       changePoints.push((transfromItem || { x: 0 })['x']);
        //       typeArr.push(vipType);
        //     }
        //   }
        // });
        for (let index: number = 0 ; index < changePoints.length; index++) {
          let partPoints: any = [];
          if (index === changePoints.length - 1) {
            partPoints = tempPoints;
          } else {
            const pointIndex: any = tempPoints.findIndex((item: any) => {
              return item.x >= changePoints[index + 1];
            });
            partPoints = tempPoints.splice(0, pointIndex - 1);
            partPoints.push(tempPoints[0]);
          }
          drawLineAndGradient(
            ctx,
            lastData.value,
            typeColor[typeArr[index]].lineColorStart,
            typeColor[typeArr[index]].lineColorEnd,
            typeColor[typeArr[index]].gradientColorStart,
            typeColor[typeArr[index]].gradientColorEnd,
            2,
            partPoints
          );
        }
      }
    }
  });
  num += 1;
  requireAnimation.value = requestAnimationFrame(() => drawPoints(ctx, num));

}

function formatSize(size: number, fractionDigits?: number, addSpace: boolean = false): string {
    if (fractionDigits === 0) {
      //
    } else {
      fractionDigits = fractionDigits ? fractionDigits : 2;
    }
    let ret: string = '0B';
    if (typeof size === 'number' && size > 0) {
      const subFIx: string[] = ['B', 'KB', 'MB', 'GB', 'TB'];
      let fixIndex: number = 0;
      let remain: number = size;
      while (remain >= 1000) {
        if (fixIndex >= 4) {
          break;
        }
        remain = remain / 1024;
        fixIndex += 1;
      }

      if (String(remain).indexOf('.') === -1) {
        ret = remain + (addSpace ? ' ' : '') + subFIx[fixIndex];
      } else {
        const sizeStr: string = remain.toFixed(fractionDigits);
        ret = sizeStr + (addSpace ? ' ' : '') + subFIx[fixIndex];
      }
    }

    return ret;
  }
</script>



<template>
  <div ref="canvasBox" class="curve-chart">
    <canvas
      ref="curveCanvas"
      :style="`transform: scale(${1/scale});`"
      class="curve-canvas"
    ></canvas>
    <div v-if="isDownload" class="line" :style="{top: `${curveTop}px`, left: `${curveLeft}px`}"></div>
    <div v-if="isDownload" class="point" :style="{top: `${curveTop}px`, left: `${curveLeft}px`}"></div>
    <div v-if="isDownload" class="number" :style="{top: `${curveTop}px`, right: '2px'}">
      {{ downloadSpeedSize }}/s
    </div>
  </div>
</template>


<style lang="scss" scoped>
.curve-chart {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}
.curve-canvas {
  transform-origin: left top;
}
.line {
  position: absolute;
  right: 0;
  height: 1px;
  margin: 3px 0 0 8px;
  border-bottom: var(--border-border-primary) 1px dashed;
}
.point {
  position: absolute;
  width: 7px;
  height: 7px;
  background: var(--border-border-primary);
  border-radius: 50%;
}
.number {
  color: var(--font-font-3);
  font-size: 12px;
  line-height: 20px;
  position: absolute;
  right: 0;
  display: flex;
  align-items: center;
  text-align: right;
  margin-top: -20px;
  white-space: nowrap;
  overflow: hidden;
}
</style>
