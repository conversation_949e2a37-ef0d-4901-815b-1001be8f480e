#ifndef __7ZUNCOMPRESS_H__
#define __7ZUNCOMPRESS_H__

#include<vector>
#include<string>


const DWORD US_Success			= 0;
const DWORD US_Unknown			= 0x86180002;
const DWORD US_NotEnoughSpace	= 0x86180003;
const DWORD US_AccessDenied		= 0x86180004;
const DWORD US_PackageDamaged	= 0x86180005; 
const DWORD US_FileOccupy		= 0x86180006;
const DWORD US_FileOverMaxCount = 0x86180007;

#define MAX_RETRY_TIMEOUT 20000

#ifdef __cplusplus
extern "C" {  
#endif
typedef BOOL (*FuncIsContinue)();
DWORD GetFilesNumber(const int zipid, unsigned int *pval);
DWORD SetOutputDirectory(const int zipid, const wchar_t *pstr, size_t n);
DWORD UnCompressInitMem(const void* pdata, UINT64 size, int *pzipid, BOOL bAssignId = FALSE);
DWORD UncompressUninitMem(const int zipid);
DWORD UncompressNextFile(const int zipid, wchar_t **ppath, DWORD dwEx);
// DWORD UncompressNextFile2(const int zipid, wchar_t **ppath, DWORD dwEx, size_t& outSize);
DWORD WriteAllFile(const int zipid,FuncIsContinue pFunc, wchar_t* pErrorName,  DWORD* pErrorCode);
void Initialize7z();
void Unintialize7z();

DWORD UnCompressReInitMen(const int zipid,const void* pdata, UINT64 size );
// DWORD UncompressNextFileAndSave(const int zipid, wchar_t **ppath, DWORD dwEx);

DWORD UnCompressInitFile(const wchar_t* strPath, int *pzipid, BOOL bAssignId);
DWORD UncompressUninitFile(const int zipid);
DWORD UnCompressReInitFile(const int zipid,const wchar_t* strPath);
// DWORD UnCompressSpecifyFile(const int zipid, const std::vector<std::wstring> ppName, const int nSize);
// DWORD UnCompressSpecifyDir(const int zipid,std::wstring& tsDir);

DWORD UnCompress7z(const wchar_t * filename, const wchar_t * directroy, DWORD& errorCode, std::wstring& strMsg);

#ifdef __cplusplus
}
#endif

#endif
