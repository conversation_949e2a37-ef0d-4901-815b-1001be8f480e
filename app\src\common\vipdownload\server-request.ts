import { SpeedBody, TokenResponse, VipGlobalData } from './command';
import * as http from 'http';
import * as https from 'https';
import * as UrlParse from 'url';
import * as crypto from 'crypto';
import axios, { AxiosResponse } from 'axios'
import { AccountHelper } from '@root/common/account/client/accountHelper';

enum Protocol {
  HTTP = 'HTTP',
  HTTPS = 'HTTPS'
}

export enum VerifyType {
  Vip = 0, // 会员加速/流量卡用户加速
  NoVip = 10, // 完全意义的非会员类型，即无试用也无流量卡
}

export class ServerRequest {
  public static async querySpeedToken(globalData: VipGlobalData, body: SpeedBody): Promise<TokenResponse> {
    return ServerRequest.requestServer(false, globalData, body);
  }

  public static async queryStatus(globalData: VipGlobalData, body: SpeedBody): Promise<TokenResponse> {
    return ServerRequest.requestServer(true, globalData, body);
  }

  public static async requestServer(queryStatus: boolean, globalData: VipGlobalData, body: SpeedBody): Promise<TokenResponse> {
    console.log('requestServer boyd=', body)
    console.log('ServerRequest begin queryStatus=', queryStatus);
    let userInfo: any = await AccountHelper.getInstance().getUserInfo();
    let url = 'https://' + globalData.host;

    let verifyType = VerifyType.NoVip;
    let random: number = Date.now();
    let aesKey: string = await ServerRequest.getKey(globalData, random.toString());
    console.log('aesKey=', aesKey);
    let bodyData = await ServerRequest.aesEncrypt(aesKey, Buffer.from(JSON.stringify(body)));
    let seq: number = ++globalData.seq;

    let headers: any = {
      'accept': 'application/json; version=1.0',
      "content-type":"application/json",
      'Content-Length': bodyData.length,
    };
    let content: string = '';
    if ((await AccountHelper.getInstance().isSignIn()) && ((await AccountHelper.getInstance().isPlatinumVip()) ||
    (await AccountHelper.getInstance().isSuperVip()))) {
      verifyType = VerifyType.Vip;
      content = `${userInfo.id}:${(await AccountHelper.getInstance().getOldAccountSessionInfo()).sessionId!}`;
    } else {
      let userId: string = '0';
      if (await AccountHelper.getInstance().isSignIn()) {
        userId = userInfo.id!;
      }
      content = `${userId}:0:${globalData.clientName}:0`;
    }
    console.log('c===',content);
    headers['Authorization'] = 'Basic ' + Buffer.from(content, 'utf-8').toString('base64');
    if (await AccountHelper.getInstance().isSignIn()) {
      headers['Authorization2'] = 'Basic ' + (await AccountHelper.getInstance().getBearerAuthorization());
    }

    if (queryStatus) {
      url = url + '/speed/res_status?need_check_filter=1&need_check_sec=1&';
    } else {
      url = url + `/speed/speedup?`;
    }
    url = url +
      `client_name=${globalData.clientName}&client_version=${globalData.versionCode}&client_sequence=${seq}&r=${random}&release_version=${globalData.versionName}&verify_type=${verifyType}&isvip=${(await AccountHelper.getInstance().isVip()) ? '1' : '0'}&isgroup=${(await AccountHelper.getInstance().isSuperVip()) ? '1' : '0'}`;

    try {
      let urlObj = UrlParse.parse(url);
      let option: http.RequestOptions = {
        hostname: urlObj.host,
        port: urlObj.port,
        path: urlObj.path,
        method: 'POST',
        //auth: auth ? auth : undefined,
        headers
      };
      let httpObj: any = urlObj.protocol === Protocol.HTTPS ? https : http;
      let res = await ServerRequest.postImpl(bodyData, option, httpObj);
      console.log(res.data.length);

      // const res: AxiosResponse = await axios.request({
      //   method: 'POST',
      //   url,
      //   timeout: 10000,
      //   headers,
      //   data: bodyData
      // });
      if (res.status === 200) {
        if (res.data && res.headers && res.headers['random-num']) {
          let aesKey: string = await ServerRequest.getKey(globalData, res.headers['random-num'] as string);
          let data = await ServerRequest.aesDecrypt(aesKey, res.data);
          if (data.length > 0) {
            let jsonData: TokenResponse = JSON.parse(data.toString('utf-8'));
            console.log('ServerRequest res=', jsonData, ',headers=', JSON.stringify(headers),',url=',url);
            return jsonData;
          }
        }
      }
      console.log('ServerRequest1 res=', res, ',headers=', JSON.stringify(headers), ',url=', url);
      return {
        result: -1
      }
    } catch(e) {
      //console.log('ServerRequest failed,e=', e, ',headers=', JSON.stringify(headers), ',url=', url);
      return {
        result: -1
      }
    }
  }



  public static async postImpl(buffer: Buffer, option: http.RequestOptions, httpObj: any): Promise<any> {
    return await new Promise((v) => {
      let request: http.ClientRequest = httpObj.request(option, (res: http.IncomingMessage) => {
        let body: Buffer = Buffer.from('');
        res.on('data', (data: Buffer) => {
          body = body ? Buffer.concat([body, data]) : data;
        });
        res.on('end', () => {
          v({data: body, headers: res.headers, status: res.statusCode})
        });
      });
      request.on('error', (err: Error) => {
        v({data: Buffer.from('')})
      });
      request.on('timeout', () => {
        request.abort();
      });
      // if (this.timeout) {
      //   request.setTimeout(this.timeout);
      // }
      request.write(buffer);
      request.end();
    })
  }



  private static async getKey(globalData: VipGlobalData, random: string): Promise<string> {
    let userId = '0';
    if (await AccountHelper.getInstance().isSignIn()) {
      let userInfo: any = await AccountHelper.getInstance().getUserInfo();
      userId = userInfo.id ?? userId;
    }
    //let key: string = (clientName ? clientName : pluginHelper.clientName) + pluginHelper.thunderVersionNumber + userId + random;
    let key = globalData.clientName + globalData.versionCode + userId + random;
    let md = crypto.createHash('md5');
    md.update(key);
    return md.digest('hex').toUpperCase().substring(0, 16);
    //return Buffer.from(mdResult.data).toString('hex').toUpperCase().substring(0, 16);
  }

  private static async aesEncrypt(aesKey: string, data: Buffer): Promise<Buffer> {
    try {
      let cipher: crypto.Cipher = crypto.createCipheriv('aes-128-ecb', aesKey, '');
      let update: Buffer = cipher.update(data);
      let final: Buffer = cipher.final();

      return Buffer.concat([update, final]);
    } catch (e) {
      return Buffer.from('', 'utf-8');
    }
  }

  private static async aesDecrypt(aesKey: string, data: Buffer): Promise<Buffer> {
    try {
      let decipher: crypto.Decipher = crypto.createDecipheriv('aes-128-ecb', aesKey, '');
      let update: Buffer = decipher.update(data);
      let final: Buffer = decipher.final();
      return Buffer.concat([update, final]);
    } catch (err) {
      console.error('decryptBuffer', err);
      return Buffer.from('', 'utf-8');
    }
  }
}