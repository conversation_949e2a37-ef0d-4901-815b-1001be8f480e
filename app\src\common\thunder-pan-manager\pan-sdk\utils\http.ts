import { IRequestClassImplement, IRequestCommonOptions, IRequestCommonResponse } from "../types";
import { objectAssign } from "./basic";
import { genCaptchaSign, getCaptchaAction, IPubKey, IXbaseSDKConfig } from "./request";

export interface IHttpImplementCtorParams {
  appVersion: string
  appVersionCode: string
  deviceId: string
  userId: () => Promise<string>
  xbaseSDKConfig: IXbaseSDKConfig
  pubKeys: IPubKey[]
  md5Fn: (str: string) => string
  request: <R extends IRequestResponse>(options: IRequestOptions) => R
}

export interface IBaseRequestOptions {
  url: string
  body?: string | Object | ArrayBuffer
  headers?: Record<string, Object>
  method?: 'GET' | 'POST' | 'HEAD' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS';
  /**
   * 读取超时时间，默认为5000ms。
   */
  readTimeout?: number
  /**
   * 连接超时时间，默认为5000ms。
   */
  connectTimeout?: number
}

export interface IRequestOptions extends IBaseRequestOptions {
  withCredentials?: boolean
  withCaptcha?: boolean
  isResponseErrorTypeError?: boolean
  withCaptchaMeta?: ICaptchaMeta
  renewCaptcha?: boolean
  captchaAction?: string
}

export interface IRequestResponse {
  toJSON: () => any
  statusCode: number
  error?: any
  headers?: any
  costTime?: number
  responseError?: any
}

export interface HttpRequestOptions extends Omit<IBaseRequestOptions, 'url'> {
  data?: Record<string, Object>
  params?: Record<string, string>
}

export type CaptchaMetaType = Record<string | number, Object>;

export interface ICaptchaMeta extends CaptchaMetaType {
  package_name: string
  client_version: string
  captcha_sign: string
  timestamp: string
  user_id: string
}

export interface HttpPanServiceResponseError {
  errorCode: number
  errorDescription: string
  errorUri: string
  error: string
}

/**
 * 通用的基于云盘 sdk 实现的网络请求
 */
class HttpImplement implements IRequestClassImplement {
  private request: <R extends IRequestResponse>(options: IRequestOptions) => R
  private appVersion: string = '';
  private appVersionCode: string = '';
  private deviceId: string = '';
  private userId: () => Promise<string>;
  private pubKeys: IPubKey[] = [];
  private xbaseSDKConfig: IXbaseSDKConfig
  private md5Fn: (str: string) => string

  private static _instance: HttpImplement;
  static timestamp = String(Date.now());

  static getInstance(params?: IHttpImplementCtorParams) {
    if (HttpImplement._instance) {
      return HttpImplement._instance;
    } else {
      HttpImplement._instance = new HttpImplement(params!);
      return HttpImplement._instance;
    }
  }

  constructor(params: IHttpImplementCtorParams) {
    this.request = params.request;
    this.appVersion = params.appVersion;
    this.appVersionCode = params.appVersionCode;
    this.deviceId = params.deviceId;
    this.userId = params.userId;
    this.pubKeys = params.pubKeys;
    this.xbaseSDKConfig = params.xbaseSDKConfig;
    this.md5Fn = params.md5Fn;
  }

  private _concatSearchParamsToUrl (url: string, params: Record<string, string>) {
    const newUrl = new URL(url);
    if (params) {
      Object.keys(params).forEach(key => {
        newUrl.searchParams.append(key, params[key]);
      });
    }
    return newUrl.href;
  }

  private async _send (url: string, options: HttpRequestOptions): Promise<IRequestCommonResponse> {
    try {
      const xbaseSDKConfig = this.xbaseSDKConfig;
      // 构建 captcha meta 数据，相关签名
      const sign = genCaptchaSign({
        xbaseSDKConfig,
        pubKeys: this.pubKeys,
        timestamp: HttpImplement.timestamp,
        appVersion: this.appVersion,
        deviceId: this.deviceId,
        md5Fn: this.md5Fn,
      });
      const captchaMeta: ICaptchaMeta = {
        package_name: xbaseSDKConfig.packageName,
        client_version: this.appVersion,
        captcha_sign: sign,
        timestamp: HttpImplement.timestamp,
        user_id: await this.userId(),
      };
      const captchaAction = getCaptchaAction(options.method || 'GET', url);
      // 通用 header，允许被 options.headers 覆盖
      const commonHeaders: Record<string, string> = {
        "x-client-plugin-version": this.appVersion,
        "x-peer-id": this.deviceId,
        "x-client-version-code": this.appVersionCode,
        "x-client-id": xbaseSDKConfig.clientId,
        "x-device-id": this.deviceId,
      };

      let requestUrl = url;
      // GET 请求参数拼接
      if (options.method === 'GET' && options.params) {
        requestUrl = this._concatSearchParamsToUrl(url, options.params);
      }

      const result = await this.request({
        url: requestUrl,
        method: options.method,
        headers: objectAssign(commonHeaders, options.headers!),
        body: options.data,
        // 自动带上 Authorization:Bearer
        withCredentials: true,
        // 如下为熊盾参数
        withCaptcha: true,
        isResponseErrorTypeError: true,   // 校验错误，触发熊盾
        withCaptchaMeta: captchaMeta,
        renewCaptcha: false,              // 强制刷新 captcha
        captchaAction: captchaAction,
        // 额外超时设置，默认超时时间为 30s
        readTimeout: options.readTimeout || 30000,
        connectTimeout: options.connectTimeout || 30000,
      });

      if (result && result.statusCode === 200) {
        // 没有返回值（null），时默认返回一个空对象
        let data = result.toJSON() ?? new Object();
        // 转化 JSON 为 Object
        if (typeof data === 'string') {
          data = JSON.parse(data);
        }

        return {
          success: true,
          data: data,
        };
      } else {
        const responseError = result.responseError ? { error: result.toJSON() } : {}

        return {
          success: false,
          originResponse: result,
          ...responseError,
        };
      }
    } catch (err: any) {
      return {
        success: false,
        error: err,
        requestPromiseError: true,
      };
    }
  }

  async get (url: string, options: IRequestCommonOptions): Promise<IRequestCommonResponse> {
    const _options: HttpRequestOptions = {
      method: 'GET',
      headers: options.header,
      params: options.params,
    };

    return this._send(url, _options);
  }

  async post (url: string, options: IRequestCommonOptions): Promise<IRequestCommonResponse> {
    const _options: HttpRequestOptions = {
      method: 'POST',
      headers: options.header,
      data: options.data,
    };

    return this._send(url, _options);
  }

  async delete (url: string, options: IRequestCommonOptions): Promise<IRequestCommonResponse> {
    const _options: HttpRequestOptions = {
      method: 'DELETE',
      headers: options.header,
      data: options.data,
    };

    return this._send(url, _options);
  }

  async patch (url: string, options: IRequestCommonOptions): Promise<IRequestCommonResponse> {
    const _options: HttpRequestOptions = {
      method: 'PATCH',
      headers: options.header,
      params: options.params,
    };

    return this._send(url, _options);
  }

  /**
   * 其他业务需要走云盘接口时调用该函数进行发送请求
   * @param url 链接
   * @param options 配置
   * @returns
   */
  async useRequest (url: string, options: HttpRequestOptions) {
    return this._send(url, options);
  }
}

export default HttpImplement;
