<script setup lang="ts">
import { UserInfo } from '@root/common/account/account-type'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import { computed } from 'vue'

interface NavItem {
  name: string
  title: string
  path: string
  redirect?: string
  icon?: {
    active: string
    default: string
  }
  resident?: string
  hideInSidebar?: boolean
}

const props = defineProps<{
  items: NavItem[]
  activeItem?: NavItem
  userInfo?: UserInfo | null
  extraInfo?: Record<string, any>
}>()

const emit = defineEmits<{
  (e: 'login'): void
  (e: 'trash'): void
  (e: 'setting'): void
  (e: 'feedback'): void
  (e: 'navClick', item: NavItem): void
  (e: 'addNav'): void
  (e: 'logout'): void
}>()

const regularNavItems = computed(() => props.items.filter(item => !item.resident && !item.hideInSidebar))
const residentNavItems = computed(() => props.items.filter(item => item.resident && !item.hideInSidebar))

const handleLogout = (key: string) => {
  if (key === 'logout') {
    emit('logout')
  }
}
</script>

<template>
  <div class="app-menu-container">
    <div class="app-menu-header draggable">
      <DropdownMenu v-if="props.userInfo?.name" :align="'start'" :items="[{
        key: 'logout',
        label: '退出登录',
      }]" @select="handleLogout" :item-class="'none-draggable'" :content-class="'none-draggable'">
        <div class="app-menu-header-user none-draggable">
          <img :src="props.userInfo.picture" alt="avatar" />
          <p>{{ props.userInfo.name }}</p>
          <i class="xl-icon-direction-down-m"></i>
        </div>
      </DropdownMenu>
      <div v-else class="app-menu-header-login">
        <inline-svg :src="require('@root/common/assets/img/ic_xunlei.svg')" />
        <p @click="emit('login')">点击登录</p>
      </div>
    </div>
    <nav class="app-menu-body">
      <ul class="app-menu-body-list">
        <li v-for="item in regularNavItems" :key="item.name" class="app-menu-body-item">
          <div class="app-menu-body-item-content" :class="{ 'active': activeItem?.name === item.name }"
            @click="emit('navClick', item)">
            <div class="app-menu-body-item-content-left">
              <i v-if="item.icon"
                :class="['xl-icon', activeItem?.name === item.name ? item.icon.active : item.icon.default]"></i>
              <span class="nav-text">{{ item.title }}</span>
            </div>
            <div class="app-menu-body-item-content-right" v-if="extraInfo?.[item.name]?.downloadCount">
              <span>{{ extraInfo[item.name].downloadCount }}</span>
            </div>
          </div>
        </li>
      </ul>
      <ul class="app-menu-body-list">
        <li v-for="item in residentNavItems" :key="item.name" class="app-menu-body-item">
          <div class="app-menu-body-item-content" :class="{ 'active': activeItem?.name === item.name }"
            @click="emit('navClick', item)">
            <div class="app-menu-body-item-content-left">
              <i v-if="item.icon"
                :class="['xl-icon', activeItem?.name === item.name ? item.icon.active : item.icon.default]"></i>
              <span class="nav-text">{{ item.title }}</span>
            </div>
          </div>
        </li>
      </ul>
    </nav>
    <div class="app-menu-footer none-draggable">
      <div class="app-menu-footer-left">
        <Button variant="ghost" is-icon size="sm" @click="emit('setting')">
          <i class="xl-icon-nav-setting"></i>
        </Button>
        <Button variant="ghost" is-icon size="sm" @click="emit('feedback')">
          <i class="xl-icon-nav-feedback"></i>
        </Button>
      </div>
      <div class="app-menu-footer-right">
        <Button variant="ghost" is-icon size="sm" @click="emit('navClick', {
          name: 'trash',
          path: '/trash',
          title: '回收站'
        })" v-tooltip="{
          content: '回收站',
          placement: 'top',
        }">
          <i class="xl-icon-nav-delete"></i>
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.app-menu-container {
  width: 100%;
  height: 100%;
  position: relative;

  .app-menu-header {
    padding: 6px 8px;

    .app-menu-header-login {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;

      p {
        font-size: 13px;
        color: var(--font-font-3, #86909C);
        font-weight: 500;
      }
    }

    .app-menu-header-user {
      display: flex;
      align-items: center;
      gap: 8px;

      img {
        width: 26px;
        height: 26px;
        border-radius: 100%;
      }

      p {
        color: var(--font-font-1, #272E3B);
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }

      i {
        color: var(--font-font-3, #86909C);
      }
    }
  }

  .app-menu-body {
    margin-top: 22px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;

    &-list {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 8px
    }

    &-item {
      &-content {
        padding: 0px 16px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        text-decoration: none;
        cursor: pointer;
        font-size: 14px;
        color: var(--font-font-1, #272E3B);

        &-left {
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            font-size: 20px;
          }
        }

        &-right {
          border-radius: 30px;
          background: var(--primary-primary-default, #226DF5);
          padding: 1px 3px;
          color: var(--font-font-light, #FFF);
          text-align: center;
          font-size: 12px;
          font-weight: 700;
          line-height: 14px;
        }

        &.active {
          border-radius: var(--border-radius-M, 8px);
          background: var(--fill-fill-2, rgba(12, 24, 49, 0.06));
        }

        &:hover {
          border-radius: var(--border-radius-M, 8px);
          background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
        }
      }
    }
  }

  .app-menu-body-list-split {
    height: 1px;
    background-color: var(--color-line-1, #E5E6EB);
    margin: 8px 0px;
  }

  .app-menu-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 18px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .app-menu-footer-left,
    .app-menu-footer-right {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    button {
      color: var(--font-font-3, #86909C);
      font-size: 20px;
    }

  }
}
</style>