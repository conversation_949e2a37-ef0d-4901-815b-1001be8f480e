import { AccountHelper } from '@root/common/account/client/accountHelper'
import { AccountHelperEventKey } from '@root/common/account/account-type'
import { useUserStore } from '@/store/user-store'

export class UserHelper {

  static async initUserInfo () {
    await UserHelper.waitUserSignin()
    const { userStoreMutation } = useUserStore()
    const userInfo = await AccountHelper.getInstance().getUserInfo()
    // 设置用户信息
    userStoreMutation.setUserInfo(userInfo)
    // 监听用户身份变化，更新用户信息
    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.USER_INFO_CHANGE, (newUserInfo) => {
      userStoreMutation.setUserInfo(newUserInfo)
    })
    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_OUT, () => {
      userStoreMutation.setUserInfo({})
    })
  }

  static async waitUserSignin () {
    return new Promise(async resolve => {
      const isSignin = await AccountHelper.getInstance().isSignIn()

      if (!isSignin) {
        AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, () => {
          resolve(true)
        })
      } else {
        resolve(true)
      }
    })
  }
}
