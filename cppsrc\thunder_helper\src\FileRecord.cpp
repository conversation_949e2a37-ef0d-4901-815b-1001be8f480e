#include "./FileRecord.h"
#include <xlcommon.h>

DownloadFileRecord::DownloadFileRecord(const std::string& strDir, const std::string& strDbName, std::shared_ptr<xl::thread::ThreadAffinity> main) {
	m_mainAffinity = main;
	if (!xl::path::IsFileExist(strDir)) {
		xl::path::CreateDir(strDir);
	}

	m_strDb = xl::path::PathConcat(strDir, strDbName);
	m_t = std::thread([this]() {
		Init();
		});
	m_t.detach();
}


xl::coroutine::AsyncTask<bool> DownloadFileRecord::Upsert(const std::string& strUrl, int32_t nIndex, const std::string& strFilePath) {
	std::string strTempUrl = strUrl;
	std::string strTempFilePath = strFilePath;
	co_await m_loadLock.Lock();
	m_loadLock.UnLock();

	m_threadAffinity->GetThreadMessage()->PostTask([this, strUrl =std::move(strTempUrl), nIndex, strFilePath=std::move(strTempFilePath)]() {
		sqlite3_stmt* pStmt{ nullptr };
		do {
			std::string strSql = "INSERT OR REPLACE INTO FileRecord (Hash, Url, FileIndex, FilePath) VALUES (SHA1_HASH(?1), ?2, ?3, ?4)";
			auto nRet = sqlite3_prepare_v2(m_pDataBase, strSql.c_str(), -1, &pStmt, nullptr);
			XL_SPDLOG_INFO("DownloadFileRecord Upsert, code={:d}, sql={:s}, msg={:s}", nRet, strSql, sqlite3_errmsg(m_pDataBase));
			if (nRet != SQLITE_OK) {
				break;
			}
			std::string str = std::to_string(nIndex) + "_" + strUrl;
			sqlite3_bind_text(pStmt, 1, str.c_str(), -1, SQLITE_STATIC);
			sqlite3_bind_text(pStmt, 2, strUrl.c_str(), -1, SQLITE_STATIC);
			sqlite3_bind_int(pStmt, 3, nIndex);
			sqlite3_bind_text(pStmt, 4, strFilePath.c_str(), -1, SQLITE_STATIC);
			nRet = sqlite3_step(pStmt);
			XL_SPDLOG_INFO("DownloadFileRecord Upsert, code={:d}, sql={:s}, msg={:s}", nRet, strSql, sqlite3_errmsg(m_pDataBase));
			sqlite3_finalize(pStmt);
		} while (false);
	});
	
	co_return true;
}

xl::coroutine::AsyncTask<std::string> DownloadFileRecord::Query(const std::string& strUrl, int32_t nIndex) {
	std::string strTempUrl = strUrl;
	co_await m_loadLock.Lock();
	m_loadLock.UnLock();
	std::string strValue;
	co_await m_threadAffinity->SwitchToAffinityThread();
	sqlite3_stmt* pStmt{ nullptr };
	do {
		std::string strSql = "SELECT FilePath FROM FileRecord WHERE Hash=SHA1_HASH(?1)";
		auto nRet = sqlite3_prepare_v2(m_pDataBase, strSql.c_str(), -1, &pStmt, nullptr);
		XL_SPDLOG_INFO("DownloadFileRecord Query, code={:d}, sql={:s}, msg={:s}", nRet, strSql, sqlite3_errmsg(m_pDataBase));
		if (nRet != SQLITE_OK) {
			break;
		}
		std::string str = std::to_string(nIndex) + "_" + strTempUrl;
		sqlite3_bind_text(pStmt, 1, str.c_str(), -1, SQLITE_STATIC);
		nRet = sqlite3_step(pStmt);
		XL_SPDLOG_INFO("DownloadFileRecord Query, code={:d}, sql={:s}, msg={:s}", nRet, strSql, sqlite3_errmsg(m_pDataBase));
		if (nRet == SQLITE_ROW) {
			auto column_value = (const char*)sqlite3_column_text(pStmt, 0);
			if (column_value) {
				strValue = column_value;
			}
		}
		sqlite3_finalize(pStmt);
	} while (false);
	co_await m_mainAffinity->SwitchToAffinityThread();

	co_return strValue;
}

static void sqlite_sha1_hash(sqlite3_context* context, int argc, sqlite3_value** argv) {
	if (argc != 1 || sqlite3_value_type(argv[0]) != SQLITE_TEXT) {
		XL_SPDLOG_ERROR("DownloadFileRecord sqlite_sha1_hash failed");
		sqlite3_result_text(context, "", -1, SQLITE_STATIC);
		return;
	}

	const char* text = reinterpret_cast<const char*>(sqlite3_value_text(argv[0]));
	std::string strSign;
	xl::hash::sha1_hmac::hash_string(text, "89917368930f3fea5bafebe704d6b623", strSign);
	XL_SPDLOG_INFO("DownloadFileRecord sqlite_sha1_hash str={:s}, sign={:s}", text, strSign);
	sqlite3_result_text(context, strSign.c_str(), -1, SQLITE_STATIC);
}

void DownloadFileRecord::Init() {
	auto nRet = sqlite3_open(m_strDb.c_str(), &m_pDataBase);
	if (nRet != SQLITE_OK) {
		XL_SPDLOG_INFO("DownloadFileRecord load failed, code={:d}", nRet);
	}
	sqlite3_create_function(m_pDataBase, "SHA1_HASH", 1, SQLITE_UTF8, nullptr,
		sqlite_sha1_hash, nullptr, nullptr);
	std::string strCreateSql = "CREATE TABLE FileRecord (Hash NVARCHAR PRIMARY KEY, Url NVARCHAR, FileIndex INT, FilePath NVARCHAR)";
	std::string strCreateIndexSql = "CREATE INDEX FileRecord_Hash_Index ON FileRecord (Hash)";
    nRet = sqlite3_exec(m_pDataBase, strCreateSql.c_str(), nullptr, nullptr, nullptr);
    if (nRet != SQLITE_OK) {
		XL_SPDLOG_INFO("DownloadFileRecord create table failed, code={:d}", nRet);
    }
    nRet = sqlite3_exec(m_pDataBase, strCreateIndexSql.c_str(), nullptr, nullptr, nullptr);
    if (nRet != SQLITE_OK) {
		XL_SPDLOG_INFO("DownloadFileRecord create index failed, code={:d}", nRet);
    }

	uv_loop_init(&m_loop);
	auto threadMessage = xl::thread::LibuvThreadMessage::NewObject(&m_loop);
	m_threadAffinity = xl::thread::ThreadAffinity::NewObject(threadMessage);


	auto func = [](DownloadFileRecord* pThis) -> xl::coroutine::AsyncTask<void> {
		co_await pThis->m_mainAffinity->SwitchToAffinityThread();
		pThis->m_loadLock.UnLock();
		XL_SPDLOG_INFO("finish");
		co_await pThis->m_threadAffinity->SwitchToAffinityThread();
	};
	func(this);
	uv_run(&m_loop, UV_RUN_DEFAULT);
}

void DownloadFileRecordAddon::Init(napi_env env, napi_value exports) {
	napi_property_descriptor desc[] = {
		{ "upsert", nullptr, DownloadFileRecordAddon::Upsert, nullptr, nullptr, nullptr, napi_default, nullptr },
		{ "query", nullptr, DownloadFileRecordAddon::Query, nullptr, nullptr, nullptr, napi_default, nullptr }
	};

	napi_value constructor;
	const char szClassName[] = "NativeDownloadFileRecord";
	napi_define_class(env, szClassName, sizeof(szClassName), DownloadFileRecordAddon::JSConstructor, nullptr,
		sizeof(desc) / sizeof(napi_property_descriptor), desc, &constructor);


	napi_set_named_property(env, exports, szClassName, constructor);
}

napi_value DownloadFileRecordAddon::Upsert(napi_env env, napi_callback_info info) {
	napi_status status;
	size_t argc = 3;
	napi_value argv[3];
	napi_value _this = nullptr;
	status = napi_get_cb_info(env, info, &argc, argv, &_this, nullptr);
	if (argc < 2) {
		XL_SPDLOG_ERROR("failed, for argc < 2, count={:d}", argc);
		assert(false);
		return nullptr;
	}
	DownloadFileRecord* pObj = nullptr;
	napi_unwrap(env, _this, (void**)&pObj);

	std::string strUrl;
	AddonBaseOpt::ParseString(env, argv[0], strUrl);
	int32_t nIndex = -1;
	AddonBaseOpt::ParseInt32(env, argv[1], nIndex);
	std::string strFilePath;
	AddonBaseOpt::ParseString(env, argv[2], strFilePath);
	if (pObj) {
		pObj->Upsert(std::move(strUrl), nIndex, std::move(strFilePath));
	}
	return nullptr;
}

napi_value DownloadFileRecordAddon::Query(napi_env env, napi_callback_info info) {
	napi_status status;
	size_t argc = 3;
	napi_value argv[3];
	napi_value _this = nullptr;
	status = napi_get_cb_info(env, info, &argc, argv, &_this, nullptr);
	if (argc < 3) {
		XL_SPDLOG_ERROR("failed, for argc < 3, count={:d}", argc);
		assert(false);
		return nullptr;
	}
	DownloadFileRecord* pObj = nullptr;
	napi_unwrap(env, _this, (void**)&pObj);

	std::string strUrl;
	AddonBaseOpt::ParseString(env, argv[0], strUrl);
	int32_t nIndex = -1;
	AddonBaseOpt::ParseInt32(env, argv[1], nIndex);
	auto pFunc = NapiFunctionWarp::NewObject(env, argv[2]);

	auto f = [](DownloadFileRecord* pObj, std::string&& strUrl, int32_t nIndex, decltype(pFunc) pFunc)->xl::coroutine::AsyncTask<void> {
		std::string strValue;
		if (pObj) {
			strValue = co_await pObj->Query(strUrl, nIndex);
		}

		auto pArgv = pFunc->NewArgv(1);
		pArgv->PushString(strValue);
		pFunc->Call(std::move(pArgv), nullptr);
	};
	f(pObj, std::move(strUrl), nIndex, pFunc);

	return nullptr;
}

napi_value DownloadFileRecordAddon::JSConstructor(napi_env env, napi_callback_info info) {
	napi_status status;
	size_t argc = 2;
	napi_value argv[2];
	napi_value _this = nullptr;
	status = napi_get_cb_info(env, info, &argc, argv, &_this, nullptr);
	if (argc < 1) {
		XL_SPDLOG_ERROR("failed, for argc < 1, count={:d}", argc);
		assert(false);
		return nullptr;
	}

	std::string strDbDir = "";
	AddonBaseOpt::ParseString(env, argv[0], strDbDir);
	std::string strDbName = "";
	AddonBaseOpt::ParseString(env, argv[1], strDbName);

	uv_loop_t* pLoop{ nullptr };
	napi_get_uv_event_loop(env, &pLoop);
	auto spThreadMessage = xl::thread::LibuvThreadMessage::NewObject(pLoop);
	auto threadAffinity = xl::thread::ThreadAffinity::NewObject(spThreadMessage);

	DownloadFileRecord* pStorage = new DownloadFileRecord(strDbDir, strDbName, threadAffinity);
	status = napi_wrap(env, _this,
		reinterpret_cast<void*>(pStorage),
		[](napi_env env, void* finalize_hint, void* data) {
			delete reinterpret_cast<DownloadFileRecord*>(data);
		},
		nullptr, nullptr);

	if (status != napi_ok) return nullptr;

	return _this;
}