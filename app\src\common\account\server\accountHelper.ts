import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import {
  AccountHelper,
} from '../impl/accountHelper'
import {
  AccountHelperEventKey,
  ISignUpParams,
  ISignInWithPasswordParams,
  ISignWithVerificationParams
} from '@root/common/account/account-type'
import { IGetCredentialsParams } from '@xbase/electron_auth_types_kit'
import { ISubscribeParams } from '@xbase/electron_sync_kit';
import {
  IDeviceAuthorizeParams,
} from '@xbase/electron_account_kit'
import {
  ResponseError,
  ITraceInfoOptionLike,
} from '@xbase/electron_common_kit'
import { getThunderClient } from '../../link-hub/impl/thunder-client'
import { ILoginHistory } from '@root/common/account/impl/login-options-define'

export class AccountHelperServer {
  private static isinited_: boolean = false;
  static init() {
    if (this.isinited_) {
      return;
    }
    this.isinited_ = true;
    client.registerFunctions({
      AccountHelperInitSDK: (ctx: unknown) => {
        return AccountHelper.getInstance().initSDK();
      },
      // 是否初始化 sdk 完成
      AccountHelperIsInitReady: (ctx: unknown) => {
        return AccountHelper.getInstance().sdkInitReady;
      },
      /**
       * @description: 帐号SDK初始化完成，并且判断是否需要自动登录完成
       */
      AccountHelperWhenReady: async (ctx: unknown) => {
        return AccountHelper.getInstance().whenReady();
      },
      // 注册账号，发送验证码到手机号
      AccountHelperSendSignUpVerificationWithPhoneNumber: (ctx: unknown, phoneNumber: string) => {
        return AccountHelper.getInstance().sendSignUpVerificationWithPhoneNumber(phoneNumber);
      },
      // 注册账号
      AccountHelperSignUp: (ctx: unknown, params: ISignUpParams) => {
        return AccountHelper.getInstance().signUp(params);
      },
      // 自动登录
      AccountHelperAutoSignIn: (ctx: unknown) => {
        return AccountHelper.getInstance().autoSignIn();
      },
      // 设备授权
      AccountHelperDeviceAuthorize: (ctx: unknown, params: IDeviceAuthorizeParams) => {
        return AccountHelper.getInstance().deviceAuthorize(params);
      },
      // 结束设备授权
      AccountHelperStopDeviceAuthorization: (ctx: unknown) => {
        return AccountHelper.getInstance().stopDeviceAuthorization();
      },
      // 账号密码登录
      AccountHelperSignInWithPassword: (ctx: unknown, params: ISignInWithPasswordParams) => {
        return AccountHelper.getInstance().signInWithPassword(params);
      },
      // 账号+验证码登录
      AccountHelperSignInWithVerification: (ctx: unknown, params: ISignWithVerificationParams) => {
        return AccountHelper.getInstance().signInWithVerification(params);
      },
      // 手机号登录发送验证码
      AccountHelperSendSignInVerificationWithPhoneNumber: (ctx: unknown, phoneNumber: string) => {
        return AccountHelper.getInstance().sendSignInVerificationWithPhoneNumber(phoneNumber);
      },
      // 获取扫码登录的二维码
      AccountHelperGetSignInQrcode: (ctx: unknown) => {
        return AccountHelper.getInstance().getSignInQrcode();
      },
      // 退出登录
      AccountHelperSignOut: (ctx: unknown) => {
        return AccountHelper.getInstance().signOut();
      },
      // 登录凭证，实时有效（对应的每次请求前需要调用这个去刷新 Credentials）
      AccountHelperGetCredentials: (ctx: unknown, params: IGetCredentialsParams) => {
        return AccountHelper.getInstance().getCredentials(params);
      },
      // 获取历史登录账号列表
      AccountHelperGetLoginHistoryList: (ctx: unknown) => {
        return AccountHelper.getInstance().getLoginHistoryList();
      },
      // 获取上次登录账号
      AccountHelperGetLastLoginData: (ctx: unknown) => {
        return AccountHelper.getInstance().getLastLoginData();
      },
      // 单次删除登录历史记录
      AccountHelperDeleteLoginHistory: (ctx: unknown, history: ILoginHistory) => {
        return AccountHelper.getInstance().deleteLoginHistory(history);
      },
      // 删除所有登录历史记录
      AccountHelperClearLoginHistory: (ctx: unknown) => {
        return AccountHelper.getInstance().clearLoginHistory();
      },
      AccountHelperIsSuperVip: (ctx: unknown) => {
        return AccountHelper.getInstance().isSuperVip;
      },
      AccountHelperIsVip: (ctx: unknown) => {
        return AccountHelper.getInstance().isVip();
      },
      AccountHelperIsSignIn: (ctx: unknown) => {
        return AccountHelper.getInstance().isSignIn;
      },
      AccountHelperIsPlatinumVip: (ctx: unknown) => {
        return AccountHelper.getInstance().isPlatinumVip;
      },
      AccountHelperIsYear: (ctx: unknown) => {
        return AccountHelper.getInstance().isYear;
      },
      AccountHelperVasType: (ctx: unknown) => {
        return AccountHelper.getInstance().vasType;
      },
      AccountHelperVipLevel: (ctx: unknown) => {
        return AccountHelper.getInstance().vipLevel;
      },
      AccountHelperGetUserInfo: async (ctx: unknown) => {
        return await AccountHelper.getInstance().getUserInfo();
      },
      AccountHelperGetOldAccountSessionInfo: async (ctx: unknown) => {
        return await AccountHelper.getInstance().getOldAccountSessionInfo();
      },
      AccountHelperGetBearerAuthorization: async (ctx: unknown) => {
        return await AccountHelper.getInstance().getBearerAuthorization();
      },
      AccountHelperGetDeviceID: async (ctx: unknown) => {
        return await AccountHelper.getInstance().getDeviceID();
      },
      AccountHelperSyncClientIsConnected: async (ctx: unknown) => {
        return await AccountHelper.getInstance().syncClient.isConnected()
      },
      AccountHelperSyncClientSubscribe: async (ctx: unknown, params: ISubscribeParams) => {
        return await AccountHelper.getInstance().syncClient.subscribe(params)
      },
      AccountHelperSyncClientUnsubscribe: async (ctx: unknown, params: ISubscribeParams) => {
        return await AccountHelper.getInstance().syncClient.unsubscribe(params)
      },
      AccountHelperTranslateResponseError: async (ctx: unknown, err: any) => {
        return AccountHelper.getInstance().translateResponseError(ResponseError.fromJsonString(err));
      },
    });

    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SDK_INIT_READY, async () => {
      client.broadcastEvent(AccountHelperEventKey.SDK_INIT_READY);
    });
    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.REFRESH_CREDENTIALS, async () => {
      client.broadcastEvent(AccountHelperEventKey.REFRESH_CREDENTIALS);
    });
    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, async () => {
      client.broadcastEvent(AccountHelperEventKey.SIGN_IN_SUCCESS);
    });
    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_OUT, async () => {
      client.broadcastEvent(AccountHelperEventKey.SIGN_OUT);
    });
    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.USER_INFO_CHANGE, async (info: any) => {
      client.broadcastEvent(AccountHelperEventKey.USER_INFO_CHANGE, info);
    });
    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SYNC_CLIENT_EVENT_CONNECTED, async () => {
      client.broadcastEvent(AccountHelperEventKey.SYNC_CLIENT_EVENT_CONNECTED);
    });
    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SYNC_CLIENT_EVENT_MESSAGE_ARRIVED, async (originMessage: any) => {
      client.broadcastEvent(AccountHelperEventKey.SYNC_CLIENT_EVENT_MESSAGE_ARRIVED, originMessage);
    });
    AccountHelper.getInstance().attachEvent(AccountHelperEventKey.DEVICE_AUTHORIZATION, async (traceInfoOptionLike: ITraceInfoOptionLike, taskId: string, responseError?: ResponseError) => {
      client.broadcastEvent(AccountHelperEventKey.DEVICE_AUTHORIZATION, traceInfoOptionLike, taskId, responseError);
    });
    getThunderClient().getUserAccountProxy().init();
  }
}