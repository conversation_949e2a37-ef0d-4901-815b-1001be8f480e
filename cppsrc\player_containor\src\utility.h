#ifndef __APLAYER_UTILITY_H__
#define __APLAYER_UTILITY_H__

#include <Windows.h>
#include <atlbase.h>
#include <string>
#include "unicode.h"
#include "etwlogger.h"

namespace utility
{
	static inline bool get_file_version(const wchar_t* file_path, int& ver1, int& ver2, int& ver3, int& ver4)
	{
		bool ret = false;
		do
		{
			DWORD info_size = ::GetFileVersionInfoSize(file_path, 0);
			if (info_size <= 0)
			{
				ETW_LEVEL_ERROR(L"GetFileVersionInfoSize  info_size is 0");
				break;
			}

			void* buffer = new char[info_size + 4];
			if (::GetFileVersionInfo(file_path, 0, info_size, buffer))
			{
				UINT file_info_size = 0;
				VS_FIXEDFILEINFO* file_info = NULL;
				if (::VerQueryValue(buffer, L"\\", (void **)&file_info, &file_info_size))
				{
					ver1 = (int)(((unsigned __int64)file_info->dwFileVersionMS) >> 16);
					ver2 = (int)(((unsigned __int64)file_info->dwFileVersionMS) & 0xFFFF);
					ver3 = (int)(((unsigned __int64)file_info->dwFileVersionLS) >> 16);
					ver4 = (int)(((unsigned __int64)file_info->dwFileVersionLS) & 0xFFFF);
					ret = true;
				}
			}
			delete[] buffer;
		} while (false);
		return ret;
	}

	static inline std::wstring get_file_version(const wchar_t* file_path, wchar_t seperator = L'.')
	{
		if (NULL == file_path || '\0' == file_path[0])
		{
			return L"";
		}

		int v1 = 0;
		int v2 = 0;
		int v3 = 0;
		int v4 = 0;

		if (get_file_version(file_path, v1, v2, v3, v4))
		{
			wchar_t buf[32] = { 0 };
			_stprintf_s(buf, 32, L"%hu%c%hu%c%hu%c%hu", v1, seperator, v2, seperator,
				v3, seperator, v4);
			return buf;
		}
		else
		{
			return L"";
		}
	}

	// 根据Dll的句柄创建COM对象
	static inline HRESULT create_instance_from_handle(HINSTANCE hModuleHandle, REFCLSID rclsid, REFIID riid, LPVOID * ppv, IUnknown* pOuter = NULL)
	{
		if (hModuleHandle == NULL)
		{
			return E_FAIL;
		}

		HRESULT hr = E_FAIL;

		typedef HRESULT(STDAPICALLTYPE *_pfnDllGetClassObject)(REFCLSID, REFIID, LPVOID*);
		_pfnDllGetClassObject pfnDllGetClassObject = (_pfnDllGetClassObject)
			GetProcAddress(hModuleHandle, "DllGetClassObject");
		if (pfnDllGetClassObject == NULL)
		{
			return  hr;
		}

		CComPtr<IClassFactory> spCF;
		hr = (*pfnDllGetClassObject)(rclsid, IID_IClassFactory, (LPVOID*)&spCF);
		if (hr != S_OK)
		{
			ETW_LEVEL_ERROR(L"DllGetClassObject hr : 0x%08X", hr);
			return hr;
		}

		// Create instance
		hr = spCF->CreateInstance(pOuter, riid, ppv);
		if (FAILED(hr))
		{
			ETW_LEVEL_ERROR(L"CreateInstance hr : 0x%08X", hr);
		}
		return hr;
	}

	static inline bool bstr_to_string(BSTR src, std::string& dest)
	{
		bool ret = false;

		do
		{
			if (!src)
			{
				break;
			}

			int  src_len = (int)wcslen(src);
			if (src_len > 0)
			{
				char* new_dest = new  char[src_len * 4 + 1];
				ZeroMemory(new_dest, src_len * 4 + 1);
				int new_len = ::WideCharToMultiByte(CP_UTF8, NULL, src, src_len, new_dest, src_len * 4, 0, 0);
				new_dest[new_len] = '\0';
				dest = new_dest;
				delete[] new_dest;
				new_dest = NULL;

				ret = true;
			}
		} while (0);

		return ret;
	}

	static inline bool get_ntversion_numbers(OSVERSIONINFOEX& os_ver_Info)
	{
		bool ret = false;
		HMODULE h_mod = ::LoadLibraryW(L"ntdll.dll");
		if (h_mod)
		{
			typedef void (WINAPI *pfRTLGETNTVERSIONNUMBERS)(DWORD*, DWORD*, DWORD*);
			pfRTLGETNTVERSIONNUMBERS pfRtlGetNtVersionNumbers;
			pfRtlGetNtVersionNumbers = (pfRTLGETNTVERSIONNUMBERS)::GetProcAddress(h_mod, "RtlGetNtVersionNumbers");
			if (pfRtlGetNtVersionNumbers)
			{
				pfRtlGetNtVersionNumbers(&os_ver_Info.dwMajorVersion, &os_ver_Info.dwMinorVersion, &os_ver_Info.dwBuildNumber);
				os_ver_Info.dwBuildNumber &= 0x0ffff;
				ret = TRUE;
			}

			::FreeLibrary(h_mod);
			h_mod = NULL;
		}

		return ret;
	}

	static inline bool is_win10()
	{
		OSVERSIONINFOEX os_ver_Info;
		get_ntversion_numbers(os_ver_Info);
		return os_ver_Info.dwMajorVersion == 10 && os_ver_Info.dwMinorVersion == 0;
	}
}

#endif  //__APLAYER_UTILITY_H__
