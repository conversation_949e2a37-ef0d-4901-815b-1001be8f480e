/**
 * @description: 下载路径相关接口的模块
 */
import { config } from '@root/common/config/config';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { ThunderHelper, DriverType } from '@root/common/thunder-helper';
import { AsyncRemoteCall } from '@root/common/renderer-async-remote-call';

const historyPathsSection: string = 'PathAndCategory';
const histtoryPathsKey: string = 'historyDownloadPaths';
const lastUsedPathKey: string = 'LastUsedPath';
const maxHistoryLen: number = 8; // 最多记录的下载目录数

export namespace DownloadPathNS {
  export interface IPathMenuItem {
    dir: string; // 路径
    alias: string; // 路径别名 例如：桌面/我的文档
    canDelete: boolean; // 是否可删除
    id?: string; // 云盘路径的id
  }

  /** 获取下载历史路径 */
  async function getHistoryPaths(): Promise<string[]> {
    const dirs: string[] = [];
    const values: string[] = await config.getValue(historyPathsSection, histtoryPathsKey, []) as string[];
    if (values?.length) {
      const pathMap: { [dir: string]: boolean } = {};
      for (let index: number = 0; index < values.length; index++) {
        let value = values[index];
        if (!value?.trim()) {
          continue;
        }
        if (value.endsWith('\\')) {
          value = value.slice(0, value.length - 1);
        }
        if (!value) {
          continue;
        }
        if (pathMap[value.toLowerCase()]) {
          continue;
        }
        pathMap[value.toLowerCase()] = true;
        dirs.push(value);
      }
    }
    return dirs;
  }

  /** 获取历史下载目录列表 */
  export async function getAllPaths(): Promise<IPathMenuItem[]> {
    const items: IPathMenuItem[] = [];
    do {
      const paths: string[] = await getHistoryPaths();
      if (!paths?.length) {
        break;
      }

      paths.forEach((dir: string) => {
        items.push({ dir: dir, alias: dir, canDelete: true })
      });
    } while (0);
    return items.reverse();
  }

  /** 获取一个兜底的下载路径 */
  export async function getSafetyPath(): Promise<string> {
    let systemdir: string = ThunderHelper.getSystemDirectory();
    const drivers: string[] = ThunderHelper.getLogicalDriveStrings();
    let maxSize: number = 0;
    for (let i: number = 0; i < drivers.length; i++) {
      // DRIVE_REMOVABLE调用getDriveInfo可能耗时，这里先调用getDriveType
      const type: DriverType = ThunderHelper.getDriveType(drivers[i]);
      if (type === DriverType.DRIVE_FIXED) {
        const driverinfo = ThunderHelper.getPartitionSpace(drivers[i]);
        if (maxSize < driverinfo.free && drivers[i] !== systemdir) {
          maxSize = driverinfo.free;
          systemdir = drivers[i];
        }
      }
    }
    const dir: string = systemdir.substring(0, 1) + ':\\迅雷下载';
    return dir;
  }

  export async function getDefaultPath(): Promise<string> {
    let dir: string = '';
    do {
      // 是否使用上次下载目录
      const useLastCatalog: boolean = await config.getValue('TaskDefaultSettings', 'UseLastCatalog', true) as boolean;
      if (useLastCatalog) {
        dir = await config.getValue('PathAndCategory', 'LastUsedPath', '') as string;
        if (!dir) {
          dir = await config.getValue('TaskDefaultSettings', 'DefaultPath', '') as string;
        }
      } else {
        dir = await config.getValue('TaskDefaultSettings', 'DefaultPath', '') as string;
      }

      if (dir) {
        const valid: boolean = await FileSystemAWNS.mkdirsAW(dir);
        if (valid) {
          break;
        }
      }

      dir = await getSafetyPath();
    } while (0);
    return dir;
  }

  export async function choosePath(defaultPath?: string): Promise<string> {
    const dialog: any = await AsyncRemoteCall.GetInstance().getDialog();
    let parent: any = await AsyncRemoteCall.GetInstance().getCurrentWindow();
    let selectPath: string = '';
    if (parent && !(await parent.isDestroyed())) {
      const value: Electron.OpenDialogReturnValue = await (dialog as Electron.Dialog).showOpenDialog(
        parent,
        {
          defaultPath: defaultPath,
          properties: ['openDirectory']
        }
      );
      if (!value.canceled) {
        selectPath = value.filePaths[0];
      }
    }
    return selectPath;
  }

  /** 新增下载目录配置，可重复添加 */
  export async function addPath(newPath: string): Promise<void> {
    do {
      newPath = newPath?.trim();
      if (!newPath) {
        break;
      }
      if (newPath.endsWith('\\')) {
        newPath = newPath.slice(0, newPath.length - 1);
      }
      if (!newPath) {
        break;
      }
      const history: string[] = await getHistoryPaths();
      for (let index: number = 0; index < history.length; index++) {
        const dir = history[index];
        if (dir.toLowerCase() === newPath.toLowerCase()) {
          history.splice(index, 1);
          break;
        }
      }
      if (history.length >= maxHistoryLen) {
        history.splice(0, 1);
      }
      history.push(newPath);
      await config.setValue(historyPathsSection, lastUsedPathKey, newPath);
      await config.setValue(historyPathsSection, histtoryPathsKey, history);
    } while (0);
  }

  /** 删除指定的下载路径，仅删除配置 */
  export async function deletePath(delDir: string): Promise<void> {
    do {
      delDir = delDir?.trim();
      if (!delDir) {
        break;
      }
      if (delDir.endsWith('\\')) {
        delDir = delDir.slice(0, delDir.length - 1);
      }
      if (!delDir) {
        break;
      }

      const history: string[] = await getHistoryPaths();
      for (let index: number = 0; index < history.length; index++) {
        const dir = history[index];
        if (dir.toLowerCase() === delDir.toLowerCase()) {
          history.splice(index, 1);
          break;
        }
      }
      await config.setValue(historyPathsSection, histtoryPathsKey, history);
    } while (0);
  }
  
  export async function clearPaths(): Promise<void> {
    await config.setValue(historyPathsSection, histtoryPathsKey, []);
  }
}