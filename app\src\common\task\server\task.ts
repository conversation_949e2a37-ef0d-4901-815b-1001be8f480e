import { server } from '@xunlei/node-net-ipc/dist/ipc-server';
import * as BaseType from '../base'
import {GetTaskManager} from '../impl/task-manager'
import { P2spTask } from '../impl/p2sp-task';
import { BtTask } from '../impl/bt-task';
import { GroupTask } from '../impl/group-task';
import { EmuleTask } from '../impl/emule-task';
export class TaskManagerTaskServer {
    static init() {
            server.registerFunctions({
                TaskManagerGetTaskBaseInfo: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        return task.getTaskBase();
                    }
                    return {};
                },
                TaskManagerTaskIsSupportPlay: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (!task) {
                        return false;
                    }
                    return await task.isSupportPlay();
                },
                TaskManagerTaskDeleteTask: async (c: any, context: any, taskId: number, isDelete: boolean) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        return await task.deleteTask(isDelete);
                    }
                    return 0;
                },
                TaskManagerTaskStartTask: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        await task.start();
                    }
                },
                TaskManagerTaskStartNoWait: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        await task.startNoWait();
                    }
                },
                TaskManagerTaskStopTask: async (c: any, context: any, taskId: number, r: BaseType.TaskStopReason) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        await task.stop(r);
                    }
                },
                TaskManagerTaskRecycle: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        await task.recycle();
                    }
                },
                TaskManagerTaskRecoverFromRecycle: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        await task.recoverFromRecycle();
                    }
                },
                TaskManagerTaskReDownload: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        await task.reDownload();
                    }
                },
                TaskManagerTaskReName: async (c: any, context: any, taskId: number, name: string) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        await task.rename(name);
                    }
                },
                TaskManagerTaskSetDownloadStrategy: async (c: any, context: any, taskId: number, strategy: BaseType.DownloadStrategy, index: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        task.setDownloadStrategy(strategy, index);
                    }
                },
                TaskManagerTaskGetPlayUrl: async (c: any, context: any, taskId: number, index: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        return await task.getPlayUrl(index);
                    }
                    return '';
                },
                TaskManagerTaskGetPlayInfo: async (c: any, context: any, taskId: number, index: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let info: any = {};
                        info['url'] = await task.getPlayUrl(index);
                        if (task.getType() === BaseType.TaskType.Bt) {
                            let btInfo = await (task.toExtra<BtTask>().getBtFileInfoByIndex(index));
                            if (btInfo) {
                                info['name'] = btInfo.fileName;
                                info['cid'] = btInfo.cid;
                                info['gcid'] = btInfo.gcid;
                                info['size'] = btInfo.fileSize;
                            }
                        } else {
                            let taskBase = task.getTaskBase();
                            info['name'] = taskBase.taskName;
                            info['cid'] = taskBase.cid;
                            info['gcid'] = taskBase.gcid;
                            info['size'] = taskBase.fileSize;
                        }
                        return info;
                    }
                    return {};
                },
                TaskManagerTaskEnableDcdnWithVipCert: async (c: any, context: any, taskId: number, vipCert: string, fileIndex: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        task.enableDcdnWithVipCert(vipCert, fileIndex);
                    }
                },
                TaskManagerTaskDisableDcdnWithVipCert: async (c: any, context: any, taskId: number, fileIndex: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        task.disableDcdnWithVipCert(fileIndex);
                    }
                },
                TaskManagerTaskUpdateDcdnWithVipCert: async (c: any, context: any, taskId: number, vipCert: string, fileIndex: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        task.updateDcdnWithVipCert(vipCert, fileIndex);
                    }
                },
                TaskManagerTaskSetTaskExtStat: async (c: any, context: any, taskId: number, fileIndex: number, key: string, value: string) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        task.setTaskExtStat(fileIndex, key, value);
                    }
                },
                TaskManagerTaskSetUserData: async (c: any, context: any, taskId: number, key: string, field: string, value: string) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        task.setUserData(key, field, value);
                    }
                },
                TaskManagerTaskGetUserData: async (c: any, context: any, taskId: number, key: string, field: string, defaultValue: string) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        return task.getUserData(key, field, defaultValue);
                    }
                    return defaultValue;
                },
                TaskManagerTaskGetTaskBase: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        return task.getTaskBase();
                    }
                    return {};
                },
                TaskManagerTaskGetTaskUrl: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        return task.getUrl();
                    }
                    return '';
                },

                //for play begin////////////////////////////////////////////////////////////////////////////////////////////////////
                // 为了减少播放的时候ipc的多次调用，特意封装给播放用的，其他地方不需要用到该接口
                TaskMangerTaskGetPlayTriplet: async (c: any, context: any, taskId: number, index: number): Promise<{groupTaskId: number, playTaskId: number, playIndex: number}> => {
                    // 当taskid的task是group的时候，index指向了group里面的子任务；当taskid的任务为bt任务的时候，index指向bt里面的子index
                    let groupTaskId: number = 0;
                    let playTaskId: number = 0;
                    let playIndex: number = -1;
                    try {
                        let task = await GetTaskManager().findTaskById(taskId);
                        if (!task) {
                            return { groupTaskId, playTaskId, playIndex };
                        }

                        let taskBase = task.getTaskBase();
                        if (taskBase.groupTaskId && taskBase.groupTaskId > 0) {
                            // group里面的子任务
                            groupTaskId = taskBase.groupTaskId;
                            playTaskId = taskId;
                            let p = await GetTaskManager().findTaskById(groupTaskId);
                            if (p) {
                                p.setUserData("PlayInfo", "SubTaskId", playTaskId.toString());
                            }
                            playIndex = index;
                        } else if (task.getType() === BaseType.TaskType.Group) {
                            groupTaskId = taskId;

                            let groupTask = task.toExtra<GroupTask>();
                            let str = task.getUserData("PlayInfo", "SubTaskId", '');
                            if (str.length > 0) {
                                playTaskId = Number(str);
                            } else {
                                let ids = await groupTask.getSubTaskIds();
                                for (let i = 0; i < ids.length; i++) {
                                    let sub = await GetTaskManager().findTaskById(ids[i]);
                                    if (sub) {
                                        if (await sub.isSupportPlay()) {
                                            playTaskId = ids[i];
                                            task.setUserData("PlayInfo", "SubTaskId", playTaskId.toString());
                                            break;
                                        }
                                    }
                                }
                            }
                        } else {
                            groupTaskId = 0;
                            playTaskId = taskId;
                            playIndex = index;
                        }

                        let playTask = await GetTaskManager().findTaskById(playTaskId);
                        if (playTask && (playTask.getType() === BaseType.TaskType.Bt)) {
                            playIndex = index;
                            if (playIndex === -1) {
                                let str = playTask.getUserData("PlayInfo", "Index", '');
                                if (str.length > 0) {
                                    playIndex = Number(str);
                                }
                            }

                            if (playIndex !== -1) {
                                let btFile = await playTask.toExtra<BtTask>().getBtFileByIndex(playIndex);
                                if (!btFile || !btFile.isDownload()) {
                                    playIndex = -1;
                                }
                            }

                            if (playIndex === -1) {
                                let btFiles = await playTask.toExtra<BtTask>().getBtFiles();
                                for (let i = 0; i < btFiles.length; i++) {
                                    if (btFiles[i].isDownload() && btFiles[i].isSupportPlay()) {
                                        playIndex = btFiles[i].getFileIndex();
                                        break;
                                    }
                                }
                            }

                            playTask.setUserData("PlayInfo", "Index", playIndex.toString());
                        }

                        return { groupTaskId, playTaskId, playIndex };

                    } catch (e) {
                        return { groupTaskId, playTaskId, playIndex };
                    }
                },
                TaskMangerTaskSetDowloadStrategyWithTriplet: async (c: any, context: any, groupTaskId: number, playTaskId: number, playIndex: number, s: BaseType.DownloadStrategy): Promise<void> => {
                    try {
                        if (groupTaskId > 0) {
                            let task = await GetTaskManager().findTaskById(groupTaskId);
                            if (!task) {
                                return ;
                            }
                            let ids = await task.toExtra<GroupTask>().getSubTaskIds();
                            for (let i = 0; i < ids.length; i++) {
                                if (ids[i] === playTaskId) {
                                    task.setDownloadStrategy(s, i);
                                    break;
                                }
                            }
                            let playTask = await GetTaskManager().findTaskById(playTaskId);
                            if (playTask) {
                                playTask.setDownloadStrategy(s, playIndex);
                            }
                        } else {
                            let playTask = await GetTaskManager().findTaskById(playTaskId);
                            if (playTask) {
                                playTask.setDownloadStrategy(s, playIndex);
                            }
                        }
                    } catch(e) {
                        
                    }
                },
                TaskMangerTaskGetGroupPlayList: async (c: any, context: any, groupTaskId: number): Promise<any[]> => {
                    let items: any[] = [];
                    try {
                        let task = await GetTaskManager().findTaskById(groupTaskId);
                        if (!task) {
                            return items;
                        }
                        let ids = await task.toExtra<GroupTask>().getSubTaskIds();
                        for (let id of ids) {
                            let subTask = await GetTaskManager().findTaskById(id);
                            if (subTask && await subTask.isSupportPlay()) {
                                let subTaskBase = subTask.getTaskBase();
                                if (subTaskBase.taskType !== BaseType.TaskType.Bt) {
                                    items.push({fileIndex: -1, taskId: subTaskBase.taskId, fileSize: subTaskBase.fileSize, fileName: subTaskBase.taskName});
                                } else {
                                    let btFileInfos = await subTask.toExtra<BtTask>().getBtFileInfos();
                                    for (let btFileInfo of btFileInfos) {
                                        if (btFileInfo.supportPlay) {
                                            items.push({fileIndex: btFileInfo.realIndex, taskId: subTaskBase.taskId, fileSize: btFileInfo.fileSize, fileName: btFileInfo.fileName});
                                        }
                                    }
                                }
                            }
                        }
                    } catch(e) {
                    }
                    return items;
                },
                //for play end////////////////////////////////////////////////////////////////////////////////////////////////////

                ////p2sptask////////////////////////////////////////////////////////////////////
                TaskManagerTaskP2spTaskAddHttpHeaders: async (c: any, context: any, taskId: number, field: string, value: string) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let p2spTask = task.toExtra<P2spTask>();
                        p2spTask.addHttpHeaders(field, value);
                    }
                },

                ////EmuleTask//////////////////////////////////////////////////////////////////////
                TaskManagerTaskEmuleTaskGetFileHash: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let emuleTask = task.toExtra<EmuleTask>();
                        return emuleTask.getFileHash();
                    }

                    return '';
                },


                ////GroupTask//////////////////////////////////////////////////////////////////////
                TaskManagerTaskGroupTaskGetSubTaskIds: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.getSubTaskIds();
                    }

                    return [];
                },
                TaskManagerTaskGroupTaskDeleteSubTask: async (c: any, context: any, taskId: number, ids: number[]) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.deleteSubTask(ids);
                    }

                    return [];
                },
                TaskManagerTaskGroupTaskCancelSubTask: async (c: any, context: any, taskId: number, ids: number[]) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.cancelSubTask(ids);
                    }

                    return [];
                },
                TaskManagerTaskGroupTaskDownloadSubTask: async (c: any, context: any, taskId: number, ids: number[]) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.downloadSubTask(ids);
                    }

                    return [];
                },
                TaskManagerTaskGroupTaskUpdateSubSelectTask: async (c: any, context: any, taskId: number, ids: number[]) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.updateSubSelectTask(ids);
                    }

                    return [];
                },
                TaskManagerTaskGroupTaskGetDownloadCount: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.getDownloadCount();
                    }

                    return [];
                },
                TaskManagerTaskGroupTaskGetCompleteCount: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.getCompleteCount();
                    }

                    return [];
                },
                TaskManagerTaskGroupTaskGetTotalCount: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.getTotalCount();
                    }

                    return [];
                },
                TaskManagerTaskGroupTaskDeleteSubBtTask: async (c: any, context: any, taskId: number, info: any) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.deleteSubBtTask(info);
                    }
                },
                TaskManagerTaskGroupTaskCancelSubBtTask: async (c: any, context: any, taskId: number, info: any) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.cancelSubBtTask(info);
                    }
                },
                TaskManagerTaskGroupTaskDownloadSubBtTask: async (c: any, context: any, taskId: number, info: any) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.downloadSubBtTask(info);
                    }
                },
                TaskManagerTaskGroupTaskUpdateSubBtSelectTask: async (c: any, context: any, taskId: number, info: any) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let groupTask = task.toExtra<GroupTask>();
                        return await groupTask.updateSubBtSelectTask(info);
                    }
                },

                ////BtTask//////////////////////////////////////////////////////////////////////
                TaskManagerTaskBtTaskGetInfoHash: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        return btTask.getInfoHash();
                    }

                    return '';
                },
                TaskManagerTaskBtTaskUpdateDownloadIndex: async (c: any, context: any, taskId: number, downloadIndexs: number[]) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        btTask.updateDownloadIndex(downloadIndexs);
                    }
                },
                TaskManagerTaskBtTaskCancelSubTask: async (c: any, context: any, taskId: number, downloadIndexs: number[]) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        btTask.cancelSubTask(downloadIndexs);
                    }
                },
                TaskManagerTaskBtTaskDownloadSubTask: async (c: any, context: any, taskId: number, downloadIndexs: number[]) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        btTask.downloadSubTask(downloadIndexs);
                    }
                },
                TaskManagerTaskBtTaskDeleteSubTask: async (c: any, context: any, taskId: number, downloadIndexs: number[]) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        btTask.deleteSubTask(downloadIndexs);
                    }
                },
                TaskManagerTaskBtTaskIsOnlyOneFile: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        return await btTask.isOnlyOneFile();
                    }

                    return false;
                },
                TaskManagerTaskBtTaskGetDownloadCount: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        return await btTask.getDownloadCount();
                    }

                    return 0;
                },
                TaskManagerTaskBtTaskGetCompleteCount: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        return await btTask.getCompleteCount();
                    }

                    return 0;
                },
                TaskManagerTaskBtTaskGetTotalCount: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        return await btTask.getTotalCount();
                    }

                    return 0;
                },
                TaskManagerTaskBtTaskGetBtFileInfos: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        return await btTask.getBtFileInfos();
                    }

                    return [];
                },
                TaskManagerTaskBtTaskGetBtFileInfoByIndex: async (c: any, context: any, taskId: number, index: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        return await btTask.getBtFileInfoByIndex(index);
                    }

                    return {};
                },
                TaskManagerTaskBtTaskUpdateBtSubFileScheduler: async (c: any, context: any, taskId: number, s: any) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        await btTask.updateBtSubFileScheduler(s);
                    }
                },
                TaskManagerTaskBtTaskGetBtSubFileScheduler: async (c: any, context: any, taskId: number) => {
                    let task = await GetTaskManager().findTaskById(taskId);
                    if (task) {
                        let btTask = task.toExtra<BtTask>();
                        return await btTask.getBtSubFileScheduler();
                    }
                    return BaseType.XLBTTaskSubFileSchedulerType.XL_BTTaskSubFileInvalidValue;
                },
            });
        }
}