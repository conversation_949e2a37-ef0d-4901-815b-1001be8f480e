<script setup lang="ts">
import FileItem from './item.vue';
import FileListHeader from '@/components/file-header/index.vue';
import EmptyHolder from '@/components/empty-holder/index.vue'
import Breadcrumb from '@root/common/components/ui/breadcrumb/index.vue'
import Loading from '@root/common/components/ui/loading/index.vue'

import { computed, nextTick, onMounted, ref, useTemplateRef } from 'vue';
import { API_FILE, IExtendDriveFile } from '@root/common/thunder-pan-manager/pan-sdk/types';
import { TypeDriveSortInfo } from '@/manager/drive-file-manager';
import { isFolder, isSensitiveFile } from '@root/common/thunder-pan-manager/pan-sdk/utils/drive';
import { generateFileContextMenu, SYSTEM_FOLDER_TYPE_LIST } from '@root/common/thunder-pan-manager/pan-sdk/utils/file';
import { CreateCommonContextmenu } from '@root/common/components/ui/contextmenu';
import { FileOperationHelper } from '@/utils/file-operation';
import { BaseManager } from '@/manager/base-manager'
import { EDriveFileOperation } from '@root/common/thunder-pan-manager/pan-sdk/utils/file';
import DragSelect from '@root/common/components/utils/drag-select';
import { GlobalEventHelper } from '../../utils/global-event-helper';
import { useElementVisibility } from '@vueuse/core';

export interface IFileListComponentProps {
  isLoading?: boolean
  parentFile: IExtendDriveFile
  parentFrom: string
  fileList: IExtendDriveFile[]
  pickedIds: string[]
  highlightIds?: string[]
  sortInfo: TypeDriveSortInfo
  breadcrumb: any
  isInFavoriteFolder: boolean
  isInPrivilegeFolder: boolean
  isInSafeBoxFolder: boolean
  disabledMove?: boolean
  showBelongs?: boolean
}

const props = defineProps<IFileListComponentProps>()
const emit = defineEmits<{
  (e: 'header-clean-picked'): void
  (e: 'header-check-change', selectAll: boolean, canPickIds: string[]): void
  (e: 'header-sorter-click', type: TypeDriveSortInfo): void

  (e: 'set-picked-ids', ids: string[]): void
  (e: 'item-picked-change', file: API_FILE.DriveFile, isForce: boolean): void

  (e: 'set-route-list', list: any[]): void
  (e: 'list-scroll-end'): void
}>()

const scrollerVm = ref<any>(null)
const lastChosenIndex = ref<number>(0)

const $rootElement = useTemplateRef<HTMLDivElement>('$rootElement')
const rootElementIsVisible = useElementVisibility($rootElement)

const canPickIds = computed(() => {
  return props.fileList.filter(file => !SYSTEM_FOLDER_TYPE_LIST.includes(file.folder_type!)).map(file => file.id!)
})
const selectedFiles = computed(() => {
  const list: API_FILE.DriveFile[] = []
  props.fileList.forEach(file => {
    if (props.pickedIds.includes(file.id!)) {
      list.push(file)
    }
  })
  return list
})
const isHeaderIndeterminate = computed(() => {
  return !!props.pickedIds.length && props.pickedIds.length !== canPickIds.value.length
})
const isHeaderSelectAll = computed(() => {
  return !!props.pickedIds.length && props.pickedIds.length === canPickIds.value.length
})
const quickOperation = computed(() => {
  return FileOperationHelper.getInstance().getFileOperations(selectedFiles.value, { isInSafeBoxFolder: props.isInSafeBoxFolder })
})
const disableOps = computed(() => {
  // 单选文件或选中的文件全部是流畅播相关的文件时，disable 一些右键操作
  const isAllFavorite = selectedFiles.value.every(item => item.space === 'SPACE_FAVORITE')
  // 选中的文件是否全部都是敏感文件
  const isAllSensitive = selectedFiles.value.every(item => isSensitiveFile(item))
  // 选中的文件中没有可以操作的文件
  const canOpsFiles = selectedFiles.value.filter(item => !(isSensitiveFile(item) || item.space === 'SPACE_FAVORITE'))

  return isAllFavorite || isAllSensitive || props.isInFavoriteFolder || canOpsFiles.length === 0
})
const isShowBatchRename = computed(() => {
  // 选中的文件包含敏感文件
  const isSomeSensitive = selectedFiles.value.some(item => isSensitiveFile(item))
  return selectedFiles.value.length > 1 && !isSomeSensitive
})
const isSomeFavorite = computed(() => {
  return selectedFiles.value.some(item => item.space === 'SPACE_FAVORITE')
})
const isSomeKocRes = computed(() => {
  return selectedFiles.value.some(item => item.space === 'SPACE_KOC_RES')
})
const showPrivilegeFolderMenuItem = computed(() => {
  const file = selectedFiles.value[0];
  return selectedFiles.value.length === 1 && file.space === 'SPACE_KOC_RES' && file.folder_type !== 'KOC_RES'
})
const disableMoveOrCopy = computed(() => {
  // 在【享特权】文件夹内，或所选文件包含【享特权】
  return props.isInPrivilegeFolder || isSomeKocRes.value
})
const toolbarPrimaryOperation = computed(() => {
  return [
      {
        key: EDriveFileOperation.DOWNLOAD,
        icon: 'xl-icon-download',
        text: '下载',
        disabled: disableOps.value,
      },
      {
        key: EDriveFileOperation.DELETE,
        icon: 'xl-icon-delete',
        text: '删除',
        disabled: false,
      }
    ]
})

const toolbarDropdownMenuList = computed(() => {
  return [
      {
        key: EDriveFileOperation.SHARE,
        icon: 'xl-icon-general-share-m',
        label: '分享',
        show: true,
        disabled: props.isInSafeBoxFolder || disableOps.value,
      },
      {
        key: EDriveFileOperation.COPY,
        icon: '',
        label: '复制',
        show: true,
        disabled: disableOps.value || disableMoveOrCopy.value,
      },
      {
        key: EDriveFileOperation.MOVE,
        icon: '',
        label: '移动',
        show: true,
        disabled: disableOps.value || disableMoveOrCopy.value || props.disabledMove,
      },
      {
        key: EDriveFileOperation.MOVE_TO_SAFE,
        icon: 'xl-icon-general-supersafeBox-m',
        label: '移入保险箱',
        show: quickOperation.value.moveToSafe,
        disabled: disableOps.value || disableMoveOrCopy.value,
      },
      {
        key: EDriveFileOperation.MOVE_OUT_SAFE,
        icon: '',
        label: '移出保险箱',
        show: quickOperation.value.moveOutSafe,
        disabled: disableOps.value || disableMoveOrCopy.value,
      },
      {
        key: EDriveFileOperation.DECOMPRESS,
        icon: '',
        label: '在线解压',
        show: false,
        disabled: false,
      },
      {
        key: EDriveFileOperation.RENAME,
        icon: '',
        label: '重命名',
        show: true,
        disabled: props.pickedIds.length > 1 || !quickOperation.value.rename,
      },
      {
        key: EDriveFileOperation.REPORT,
        icon: '',
        label: '举报',
        show: false,
        disabled: false,
      },
    ].filter(item => item.show)
})

function handleToolbarItemClick (itemKey: string, files: API_FILE.DriveFile[]) {
  handleFileOperation(itemKey, files)
}

function handleHeaderCleanPicked() {
  emit('header-clean-picked')
}

function handlePickedIdsChange(isSelectAll: boolean) {
  emit('header-check-change', isSelectAll, canPickIds.value)
}

async function handleHeaderSorterClick (sortInfo: TypeDriveSortInfo) {
  emit('header-sorter-click', sortInfo)
}

function handleListScrollEnd() {
  emit('list-scroll-end')
}

async function handleFileItemConsume(file: IExtendDriveFile) {
  if (file.folder_type === 'SAFE' || props.isInSafeBoxFolder) {
    const pass = await FileOperationHelper.getInstance().checkBeforeConsumeSafeBox()
    if (!pass) return
  }

  if (isFolder(file)) {
    emit('set-route-list',
      props.breadcrumb.concat({
        id: file.id,
        title: file.name,
        ...file,
      }) as any,
    )
  } else {
    FileOperationHelper.getInstance().consumeFile(file.id!, file.space)
  }
}

function handleBreadcrumbItemClick(item: any, index: number) {
  const newList = props.breadcrumb.slice(0, index + 1)
  emit('set-route-list', newList as any)
}

async function handleContextMenu (event: MouseEvent, file: API_FILE.DriveFile) {
  if (SYSTEM_FOLDER_TYPE_LIST.includes(file.folder_type!)) {
    return
  }

  if (!props.pickedIds.includes(file.id!)) {
    emit('item-picked-change', file, true)
  }

  await nextTick()
  const menuList = generateFileContextMenu({
    currentParentFile: props.parentFile,
    selectedFiles: selectedFiles.value,
    unzipEnable: false,
    shouldSafeShow: BaseManager.getInstance().isSafeBoxExist(),
    isKol: false,
    isInSafeBoxFolder: props.isInSafeBoxFolder,
    disabledMove: props.disabledMove,
    showBelongs: props.showBelongs,
  })

  CreateCommonContextmenu({
    menuList,
    parentElement: $rootElement.value!,
    clickPosition: {
      x: event.clientX,
      y: event.clientY,
    },
    onMenuItemClick: (item) => {
      handleFileOperation(item.key, selectedFiles.value)
    }
  })
}

function handleItemClick(event: MouseEvent, index: number, file: API_FILE.DriveFile) {
  if (SYSTEM_FOLDER_TYPE_LIST.includes(file.folder_type!)) {
    return
  }

  if (event.shiftKey) {
    const start = Math.min(lastChosenIndex.value, index)
    const end = Math.max(lastChosenIndex.value, index)
    const pickedIds = props.fileList.slice(start, end + 1)
        .filter(item => !SYSTEM_FOLDER_TYPE_LIST.includes(item.folder_type!))
        .map(item => item.id!)

    emit('set-picked-ids', pickedIds)
  } else {
    emit('item-picked-change', file, !event.ctrlKey)
    lastChosenIndex.value = index
  }
}

function handleItemDblclick (file: API_FILE.DriveFile) {
  handleFileItemConsume(file)
}

function handleItemOperationClick (type: string, file: API_FILE.DriveFile) {
  handleFileOperation(type, [ file ])
}

function handleFileOperation (type: string, files: API_FILE.DriveFile[]) {
  FileOperationHelper.getInstance().handleFilesOperationWithType(type, files, {
    actionFrom: props.parentFrom,
    isInSafeBoxFolder: props.isInSafeBoxFolder,
    isInPrivilegeFolder: props.isInPrivilegeFolder,
    handleOpen: (file) => {
      handleFileItemConsume(file)
    }
  })
}

function handleCheckChange(isCheck: boolean, file: API_FILE.DriveFile) {
  emit('item-picked-change', file, false)
}

onMounted(() => {
  // 监听框选事件
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.DRAG_SELECT_END, (selectArea: HTMLElement, picked: HTMLElement[]) => {
    if (!rootElementIsVisible.value) return

    const { startIndex, lastIndex } = DragSelect.getDragSelectIndex(picked, selectArea, 56)
    // 从数据中遴选出相应被选中的数据
    const pickList = props.fileList.slice(startIndex!, lastIndex! + 1)
    const pickedIds = pickList.map(file => canPickIds.value.includes(file.id!) ? file.id! : '').filter(Boolean)

    if (pickedIds && pickedIds.length) {
      emit('set-picked-ids', pickedIds)
    }
  })
})

defineExpose({
  updateScroller() {
    // RecycleScroller 有 bug ，更新列表里面的 DOM 不更新，这里需要手动调里面的方法来更新
    if (scrollerVm.value) {
      scrollerVm.value.updateVisibleItems(true)
    }
  },
  scrollToItem (index: number) {
    if (scrollerVm.value) {
      scrollerVm.value.scrollToItem(index)
    }
  }
})
</script>

<template>
  <div ref="$rootElement" class="file-list__wrapper">
    <div class="title-bar">
      <Breadcrumb :list="breadcrumb" @item-click="handleBreadcrumbItemClick" />
    </div>

    <Loading v-if="isLoading" />

    <EmptyHolder v-else-if="!fileList.length"></EmptyHolder>

    <div v-else class="file-list-container drag-select__body">
      <FileListHeader
        class="disable-drag-select"
        :sort-info="sortInfo"
        :select-all="isHeaderSelectAll"
        :selected-files="selectedFiles"
        :indeterminate="isHeaderIndeterminate"
        :toolbar-primary-operation="toolbarPrimaryOperation"
        :toolbar-dropdown-menu-list="toolbarDropdownMenuList"
        @clean-picked="handleHeaderCleanPicked"
        @check-change="handlePickedIdsChange"
        @toolbar-item-click="handleToolbarItemClick"
        @sorter-click="handleHeaderSorterClick"
      />

      <RecycleScroller
        v-if="fileList.length"
        v-slot="{ item, index }"
        ref="scrollerVm"
        class="file-list__wrapper drag-select__content"
        key-field="id"
        data-scroll-container
        :items="fileList"
        :item-size="56"
        @scroll-end="handleListScrollEnd"
      >
        <FileItem
          class="drag-select__item"
          :file="item"
          :index="index"
          :picked-ids="pickedIds"
          :highlight-ids="highlightIds"
          :is-in-safe-box-folder="isInSafeBoxFolder"
          :is-in-favorite-folder="isInFavoriteFolder"
          :data-index="index"
          :style="{
            'margin-top': '4px',
          }"
          @consume="handleFileItemConsume"
          @right-click="handleContextMenu"
          @item-click="handleItemClick"
          @item-dblclick="handleItemDblclick"
          @checkbox-click="handleCheckChange"
          @operation-click="handleItemOperationClick"
        />
      </RecycleScroller>
    </div>
  </div>
</template>

<style scoped lang="scss">
.file-list__wrapper {
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  position: relative;

  .title-bar {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 8px 40px;
    font-size: 13px;
    flex-shrink: 0;
  }

  .file-list-container {
    flex-grow: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .file-list__wrapper {
      flex-grow: 1;
      min-height: 0;
      overflow: overlay;
    }
  }
}
</style>
