/**
 * 单例Promise工具类
 * 用于防止同时执行多个相同的异步操作
 */
export class SinglePromise {
  private promises: Map<string, Promise<any>> = new Map()

  /**
   * 运行单例Promise
   * @param key 唯一标识
   * @param fn 要执行的异步函数
   * @returns Promise结果
   */
  async run<T>(key: string, fn: () => Promise<T>): Promise<T> {
    if (this.promises.has(key)) {
      return this.promises.get(key)
    }

    const promise = fn().finally(() => {
      this.promises.delete(key)
    })

    this.promises.set(key, promise)
    return promise
  }

  /**
   * 清除指定key的Promise
   * @param key 唯一标识
   */
  clear(key: string): void {
    this.promises.delete(key)
  }

  /**
   * 清除所有Promise
   */
  clearAll(): void {
    this.promises.clear()
  }
} 