/// <reference path="../impl/thunder-client-api.d.ts" />
import {
    LinkHubCallApiProxyImplWithIpcServer,
    getLinkHubCallApiProxy
} from '../linkhub-call-api-impl'

export class Link<PERSON>ecord<PERSON>elper implements ThunderClientAPI.biz.ILinkListPresenter {
    private apiProxy: LinkHubCallApiProxyImplWithIpcServer;
    private static instance: LinkRecordHelper | null = null;

    private constructor() {
        this.apiProxy = getLinkHubCallApiProxy();
    }

    public static getInstance(): LinkRecordHelper {
        if (!LinkRecordHelper.instance) {
            if (global.LinkRecordHelperClientInstance) {
                LinkRecordHelper.instance = global.LinkRecordHelperClientInstance;
            } else {
                LinkRecordHelper.instance = new LinkRecordHelper();
                global.LinkRecordHelperClientInstance = LinkRecordHelper.instance;
            }
        }
        return LinkRecordHelper.instance!;
    }
    public async fetchFirstPage(param: ThunderClientAPI.dataStruct.common.FetchFirstPageParam)
        : Promise<ThunderClientAPI.dataStruct.common.FetchFirstPageResult> {
        let info = await this.apiProxy.CallApi('LinkRecordHelperFetchFirstPage', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.common.FetchFirstPageResult;
        }
        return {} as any;
    }
    public async fetchNextPage(param: ThunderClientAPI.dataStruct.common.FetchNextPageParam)
        : Promise<ThunderClientAPI.dataStruct.common.FetchNextPageResult> {
        let info = await this.apiProxy.CallApi('LinkRecordHelperFetchNextPage', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.common.FetchNextPageResult;
        }
        return {} as any;
    }
    public async updateLinkRecord(param: ThunderClientAPI.dataStruct.dataModals.UpdateLinkRecordParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.UpdateLinkRecordResult> {
        let info = await this.apiProxy.CallApi('LinkRecordHelperUpdateLinkRecord', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.UpdateLinkRecordResult;
        }
        return {} as any;
    }

    public async getLabels(param: ThunderClientAPI.dataStruct.dataModals.GetLabelsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.GetLabelsResult> {
        let info = await this.apiProxy.CallApi('LinkRecordHelperGetLabels', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.GetLabelsResult;
        }
        return {} as any;
    }
    public async loadLinkRecords(param: ThunderClientAPI.dataStruct.dataModals.LoadLinkRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.LoadLinkRecordsResult> {
        let info = await this.apiProxy.CallApi('LinkRecordHelperLoadLinkRecords', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.LoadLinkRecordsResult;
        }
        return {} as any;
    }

    public async removeLinkRecords(param: ThunderClientAPI.dataStruct.dataModals.RemoveLinkRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.RemoveLinkRecordsResult> {
        let info = await this.apiProxy.CallApi('LinkRecordHelperRemoveLinkRecords', param);
        if (info.bSucc) {
            return info.result as ThunderClientAPI.dataStruct.dataModals.RemoveLinkRecordsResult;
        }
        return {} as any;
    }
}