import * as DownloadKernel from './task/base';

export interface ISpeedFormat {
  speed: string;
  unit: string;
}
export namespace ThunderUtil {
  /**
   *
   * @param {Number} bytes 字节数
   * @param {Number} point 小数点保留的位置
   * @param {String} unit 指定单位
   * @param {Boolean} isSpace 是否在单位前添加空格
   */
  export function bytesToSize(bytes = 0, point = 1, unit = null, isSpace = false) {
    if (Number(bytes) <= 0) {
      return isSpace ? '0 B' : '0B';
    }

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = unit ? sizes.indexOf(unit) : Math.floor(Math.log(bytes) / Math.log(k));

    const val = bytes / Math.pow(k, i);
    let size = val.toPrecision(3);
    size = val.toFixed(point);

    if (/^\d+\.(0)+$/.test(size)) {
      size = val.toFixed(0);
    }

    return isSpace ? `${size} ${sizes[i]}` : size + sizes[i];
  }

  export function formatSpeed(speed: number): ISpeedFormat {
    let ret: ISpeedFormat = { speed: '0', unit: 'B/s' };
    if (typeof speed === 'number' && speed > 0) {
      const subFix: string[] = ['B/s', 'KB/s', 'MB/s', 'GB/s', 'TB/s'];
      let fixIndex: number = 0;
      let remain: number = speed;
      while (remain >= 1000) {
        if (fixIndex >= 4) {
          break;
        }
        remain = remain / 1024;
        fixIndex += 1;
      }

      if (String(remain).indexOf('.') === -1) {
        ret = { speed: String(remain), unit: subFix[fixIndex] };
      } else {
        const sizeStr: string = remain.toFixed(1);
        ret = { speed: sizeStr, unit: subFix[fixIndex] };
      }
    }
    return ret;
  }


  export function escapeRegExp(str: string) {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // 注意：这里用了两次转义：replace 中的 \\ 表示字符串中的 \，$&表示整个被匹配的字符串
  }

  /** 下载错误码转换为字符串 */
  export function downloadErrStr(code) {
    let ret = ''
    switch (code) {
      case 1:
        ret = '任务创建失败，无法继续下载';
        break;
      case 2:
        ret = '任务参数错误，无法继续下载';
        break;
      case 3:
        ret = '链接失效，无法继续下载';
        break;
      case 4:
        ret = '任务配置文件错误，无法继续下载';
        break;
      case 5:
        ret = '任务连接超时，无法继续下载';
        break;
      case 6:
        ret = '文件校验失败，无法继续下载';
        break;
      case 7:
        ret = '疑似包含违规内容，无法下载';
        break;
      case 8:
        ret = '下载引擎加速出错，无法继续下载';
        break;
      case 9:
        ret = '文件路径超出系统限制，无法继续下载';
        break;
      case 201:
        ret = '文件创建失败，无法继续下载';
        break;
      case 202:
        ret = '文件写入失败，无法继续下载';
        break;
      case 203:
        ret = '文件读取失败，无法继续下载';
        break;
      case 204:
        ret = '文件重命名失败，无法继续下载';
        break;
      case 205:
        ret = '磁盘空间不足，无法继续下载';
        break;
      case 211:
      case 212:
        ret = '当前下载目录无法写入数据，请尝试下载到其它目录';
        break;
      case 213:
        ret = '当前磁盘格式不支持大文件下载';
        break;
      case 401:
        ret = '权限验证未通过，无法继续下载';
        break;
      case 402:
        ret = '原始资源不存在，且未找到候选资源，无法继续下载';
        break;
      case 601:
        ret = 'BT任务已存在，无法继续下载';
        break;
      case 701:
        ret = '疑似包含违规内容，无法下载';
        break;
      case 702:
        ret = '账号存在异常，无法继续下载';
        break;
      case 703:
        ret = '根据当地法律法规，文件无法下载';
        break;
      case 704:
        ret = '应版权方要求，文件无法下载';
        break;
      case 20000:
      case DownloadKernel.TaskError.DownloadSDKCrash:
      case DownloadKernel.TaskError.DownloadSDKMissing:
        ret = '下载引擎未启动，无法继续下载';
        break;
      case 10002:
        ret = '种子文件不存在，无法继续下载';
        break;
      default:
        ret = '未知错误，无法继续下载';
        break;
    }
  }
}