<script setup lang="ts">
import { onMounted, ref } from 'vue';
import SelectFileFolder, { IModelValue } from '../SelectFileFolder.vue'
import CheckboxGroup, { ICheckoutGroupOptions } from '@root/common/components/ui/checkbox-group/index.vue'
import { THUNER_PATH_SETTING_NAME_MAP, getSettingConfig, setSettingConfig } from '@root/modal-renderer/src/views/setting';

// 添加目录
const addPath = ref<IModelValue>({
  path: '',
  defaultPath: '我的云盘',
  pathName: THUNER_PATH_SETTING_NAME_MAP.DefaultSavePath,
  checkboxValue: [],
  checkboxOptions: [
    {
      label: '自动修改为上次使用的目录',
      name: THUNER_PATH_SETTING_NAME_MAP.LastUsePath,
      defaultValue: true,
      onChange: (checked: boolean, optionName: string) => {
        setSettingConfig(optionName, checked)
      },
    },
  ],
  onChange: (path: string, pathName: string) => {
    setSettingConfig(pathName, path)
  },
})

// 下载目录
const downloadPath = ref<IModelValue>({
  path: '',
  defaultPath: 'D:\\迅雷下载',
  pathName: THUNER_PATH_SETTING_NAME_MAP.DefaultDownloadPath,
  checkboxValue: [],
  checkboxOptions: [
    {
      label: '取回时不再询问',
      name: THUNER_PATH_SETTING_NAME_MAP.UseDefault,
      defaultValue: false,
      onChange: (checked: boolean, optionName: string) => {
        setSettingConfig(optionName, checked)
      },
    },
  ],
  onChange: (path: string, pathName: string) => {
    setSettingConfig(pathName, path)
  },
})

// 缓存目录
const cachePath = ref<IModelValue>({
  path: '',
  defaultPath: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\Thunder Network\\Thunder7.9\\云盘下载\\云盘缓存',
  pathName: THUNER_PATH_SETTING_NAME_MAP.VODCachePath,
  checkboxValue: [],
  checkboxOptions: [
    {
      label: '开启智能缓存，改善云盘视频播放体验',
      name: THUNER_PATH_SETTING_NAME_MAP.SetVODCachePath,
      defaultValue: true,
      onChange: (checked: boolean, optionName: string) => {
        setSettingConfig(optionName, checked)
      },
    },
  ],
  onChange: (path: string, pathName: string) => {
    setSettingConfig(pathName, path)
  },
})

// 其他
const selectOtherNames = ref<string[]>([])
const otherOptions = ref<ICheckoutGroupOptions[]>([
  {
    label: '开始云播时自动暂停下载',
    name: THUNER_PATH_SETTING_NAME_MAP.StartCloudStopDownload,
    defaultValue: true,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
  {
    label: '本地文件右键菜单显示“上传到迅雷云盘”',
    name: THUNER_PATH_SETTING_NAME_MAP.ShellContextmenu,
    defaultValue: true,
    onChange: (checked: boolean, optionName: string) => {
      setSettingConfig(optionName, checked)
    },
  },
])

const initDefaultValue = async () => {
  const otherOptionsValue = await Promise.all(otherOptions.value.map(async (option) => {
    const value = await getSettingConfig(option.name, option.defaultValue)
    if (value) {
      return option.name
    }
    return ''
  }).filter(item => !!item))
  selectOtherNames.value = otherOptionsValue
}

onMounted(() => {
  initDefaultValue()
})


</script>

<template>
  <div class="settings-content-title">
    目录与缓存
  </div>

  <div class="cloud-setting-body">
    <SelectFileFolder label="添加目录" v-model="addPath" select-type="cloud" />

    <SelectFileFolder label="下载目录" v-model="downloadPath" select-type="local" />

    <SelectFileFolder label="缓存目录" v-model="cachePath" select-type="local" />
  </div>

  <div class="settings-content-divider"></div>

  <CheckboxGroup title="其他" :options="otherOptions" orientation="vertical" v-model="selectOtherNames" />

  <div class="settings-content-divider"></div>

</template>

<style scoped lang="scss">
.cloud-setting-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 6px;
}
</style>