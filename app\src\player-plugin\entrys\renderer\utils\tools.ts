import { arrayToObj } from './transform'

type TDict = Record<any, any>
const dictCache = new WeakMap<any, Record<string, TDict>>()

export function dictGet<T extends TDict, <PERSON><PERSON><PERSON> extends keyof T, TValueKey extends keyof T>(
  objOrArr: Record<string, T> | T[],
  key: TK<PERSON>,
  keyValue: T[TKey],
  valueKey: TValueKey,
  defaultKeyValue?: T[TValueKey],
): T[TValueKey] {
  if (Array.isArray(objOrArr)) {
    let dictObj = dictCache.get(objOrArr)
    if (!dictObj) {
      dictObj = {
        [key as string]: arrayToObj(objOrArr, key),
      }
      dictCache.set(objOrArr, dictObj)
    }
    return dictGet(
      dictObj[key as string],
      key,
      keyValue,
      valueKey,
      defaultKeyValue,
    )
  } else {
    const obj = objOrArr
    return obj[keyValue]?.[valueKey] ?? obj[defaultKeyValue || '']?.[valueKey]
  }
}

export function sleep(timestamp: number) {
  return new Promise<void>((resolve, reject) => {
    setTimeout(() => {
      resolve()
    }, timestamp)
  })
}

