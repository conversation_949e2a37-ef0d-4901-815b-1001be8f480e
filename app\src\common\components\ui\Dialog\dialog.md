# Dialog 组件使用文档

一个功能丰富的Vue3对话框组件库，支持多种对话框类型、编程式调用和高度自定义。

## 📦 组件架构

```
Dialog/
├── Dialog.vue              # 基础对话框组件
├── Prompt.vue              # 输入对话框组件
├── useAlertDialog.js        # 警告/确认对话框API
├── usePromptDialog.js       # 输入对话框API
└── example.vue             # 使用示例
```

## 🚀 快速开始

### 1. 导入组件

```javascript
// 导入基础组件
import Dialog from './Dialog.vue'
// 导入编程式API
import { useAlertDialog } from './useAlertDialog.js'
import { usePromptDialog } from './usePromptDialog.js'
```

### 2. 基本使用

```vue
<template>
  <!-- 模板方式 -->
  <Dialog
    v-model:open="dialogOpen"
    title="确认操作"
    content="您确定要执行此操作吗？"
    :show-trigger="true"
    trigger-text="点击打开对话框"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />

  <!-- 编程式调用 -->
  <button @click="showDialog">显示对话框</button>
</template>

<script setup>
import { ref } from 'vue'

import Dialog from './Dialog.vue'
import { useAlertDialog } from './useAlertDialog.js'

const dialogOpen = ref(false)
const alertDialog = useAlertDialog()

const handleConfirm = () => {
  console.log('用户确认')
}

const handleCancel = () => {
  console.log('用户取消')
}

const showDialog = async () => {
  const result = await alertDialog.confirm({
    title: '确认操作',
    content: '您确定要执行此操作吗？',
  })
  console.log('结果:', result)
}
</script>
```

## 📋 Dialog.vue - 基础对话框组件

### Props

| 属性                  | 类型    | 默认值       | 说明                                         |
| --------------------- | ------- | ------------ | -------------------------------------------- |
| `title`               | String  | ''           | 对话框标题                                   |
| `content`             | String  | ''           | 对话框内容                                   |
| `variant`             | String  | 'thunder'    | 主题变体：info/success/warning/error/thunder |
| `open`                | Boolean | false        | 对话框显示状态                               |
| `showTrigger`         | Boolean | false        | 是否显示触发按钮                             |
| `triggerText`         | String  | '打开对话框' | 触发按钮文本                                 |
| `showCancel`          | Boolean | true         | 是否显示取消按钮                             |
| `confirmText`         | String  | '确定'       | 确认按钮文本                                 |
| `cancelText`          | String  | '取消'       | 取消按钮文本                                 |
| `showTitleIcon`       | Boolean | true         | 是否显示标题图标                             |
| `showCloseButton`     | Boolean | true         | 是否显示关闭按钮                             |
| `loading`             | Boolean | false        | 加载状态                                     |
| `preventDefaultClose` | Boolean | false        | 阻止默认关闭行为                             |
| `allowHtml`           | Boolean | false        | 是否允许HTML内容                             |
| `classPrefix`         | String  | 'dialog'     | CSS类名前缀                                  |

### Events

| 事件          | 参数    | 说明         |
| ------------- | ------- | ------------ |
| `confirm`     | -       | 确认按钮点击 |
| `cancel`      | -       | 取消按钮点击 |
| `close`       | -       | 对话框关闭   |
| `update:open` | Boolean | 开关状态更新 |

### Slots

| 插槽              | 说明               |
| ----------------- | ------------------ |
| `default`         | 自定义内容区域     |
| `trigger`         | 自定义触发器       |
| `trigger-content` | 自定义触发器内容   |
| `title-icon`      | 自定义标题图标     |
| `actions`         | 自定义操作按钮     |
| `left-action`     | 左侧操作区域       |
| `confirm-content` | 自定义确认按钮内容 |

### 主题变体

```javascript
// 不同的视觉主题
variant: 'info' // 蓝色信息主题
variant: 'success' // 绿色成功主题
variant: 'warning' // 黄色警告主题
variant: 'error' // 红色错误主题
variant: 'thunder' // 默认Thunder主题
```

### 使用示例

```vue
<template>
  <!-- 基础用法 -->
  <Dialog
    v-model:open="deleteDialogOpen"
    title="删除确认"
    content="此操作不可撤销，确定要删除吗？"
    variant="error"
    :show-trigger="true"
    @confirm="handleDelete"
  />

  <!-- 自定义内容 -->
  <Dialog
    v-model:open="userDialogOpen"
    title="用户信息"
    :show-trigger="true"
  >
    <div class="user-form">
      <input
        v-model="userName"
        placeholder="用户名"
      />
      <input
        v-model="userEmail"
        placeholder="邮箱"
      />
    </div>
  </Dialog>

  <!-- 自定义按钮 -->
  <Dialog
    v-model:open="actionDialogOpen"
    title="操作选择"
    :show-trigger="true"
  >
    <template #actions>
      <button @click="handleSave">保存</button>
      <button @click="handleCancel">取消</button>
      <button @click="handleDelete">删除</button>
    </template>
  </Dialog>
</template>
```

## 🔔 useAlertDialog.js - 警告/确认对话框API

提供编程式的对话框调用方法，支持信息提示、确认对话框等。

### API方法

#### `alertDialog.info(options)`

显示信息提示对话框

```javascript
const result = await alertDialog.info({
  title: '提示',
  content: '这是一条信息',
})
```

#### `alertDialog.success(options)`

显示成功提示对话框

```javascript
const result = await alertDialog.success({
  title: '成功',
  content: '操作成功完成！',
})
```

#### `alertDialog.warning(options)`

显示警告提示对话框

```javascript
const result = await alertDialog.warning({
  title: '警告',
  content: '请注意这个重要警告！',
})
```

#### `alertDialog.error(options)`

显示错误提示对话框

```javascript
const result = await alertDialog.error({
  title: '错误',
  content: '发生了一个错误，请重试！',
})
```

#### `alertDialog.confirm(options)`

显示确认对话框

```javascript
const result = await alertDialog.confirm({
  title: '确认操作',
  content: '您确定要执行此操作吗？',
  variant: 'warning',
})

if (result) {
  console.log('用户确认了操作')
} else {
  console.log('用户取消了操作')
}
```

#### `alertDialog.open(options)`

显示自定义对话框

```javascript
const result = await alertDialog.open({
  title: '自定义对话框',
  content: '<p>支持HTML内容</p>',
  allowHtml: true,
  variant: 'thunder',
  showCancel: true,
  confirmText: '好的',
  cancelText: '算了',
})
```

### Options参数

| 参数              | 类型    | 默认值                         | 说明             |
| ----------------- | ------- | ------------------------------ | ---------------- |
| `title`           | String  | ''                             | 标题             |
| `content`         | String  | ''                             | 内容             |
| `variant`         | String  | 对应方法的默认主题             | 主题变体         |
| `showCancel`      | Boolean | confirm方法为true，其他为false | 是否显示取消按钮 |
| `confirmText`     | String  | '确定'                         | 确认按钮文本     |
| `cancelText`      | String  | '取消'                         | 取消按钮文本     |
| `showTitleIcon`   | Boolean | true                           | 是否显示标题图标 |
| `allowHtml`       | Boolean | false                          | 是否允许HTML内容 |
| `showCloseButton` | Boolean | true                           | 是否显示关闭按钮 |

### 返回值

- **info/success/warning/error**: 返回 `true`（确认）
- **confirm**: 返回 `true`（确认）或 `false`（取消）
- **open**: 返回 `true`（确认）或 `false`（取消/关闭）

### 使用示例

```javascript
import { useAlertDialog } from './useAlertDialog.js'

export default {
  setup() {
    const alertDialog = useAlertDialog()

    const handleDelete = async () => {
      const confirmed = await alertDialog.confirm({
        title: '删除确认',
        content: '删除后不可恢复，确定要删除吗？',
        variant: 'error',
      })

      if (confirmed) {
        // 执行删除操作
        try {
          await deleteItem()
          await alertDialog.success({
            title: '删除成功',
            content: '项目已成功删除',
          })
        } catch (error) {
          await alertDialog.error({
            title: '删除失败',
            content: '删除过程中发生错误，请重试',
          })
        }
      }
    }

    return { handleDelete }
  },
}
```

## ⌨️ usePromptDialog.js - 输入对话框API

提供带输入框的对话框，支持文本输入、实时验证、异步确认等功能。

### API方法

#### `promptDialog.prompt(options)`

显示输入对话框

```javascript
const result = await promptDialog.prompt({
  title: '重命名文件',
  placeholder: '请输入新的文件名',
  defaultValue: '文档.txt',
})

if (result !== false) {
  console.log('新文件名:', result)
} else {
  console.log('用户取消了输入')
}
```

### Options参数

#### 基础参数

| 参数           | 类型   | 默认值    | 说明                                     |
| -------------- | ------ | --------- | ---------------------------------------- |
| `title`        | String | '输入'    | 标题                                     |
| `placeholder`  | String | ''        | 输入框占位符                             |
| `defaultValue` | String | ''        | 默认值                                   |
| `currentValue` | String | ''        | 当前值显示                               |
| `hint`         | String | ''        | 提示信息                                 |
| `inputType`    | String | 'text'    | 输入类型：text/password/email/textarea等 |
| `variant`      | String | 'thunder' | 主题变体                                 |
| `confirmText`  | String | '确定'    | 确认按钮文本                             |
| `cancelText`   | String | '取消'    | 取消按钮文本                             |

#### 验证参数

| 参数              | 类型     | 默认值 | 说明             |
| ----------------- | -------- | ------ | ---------------- |
| `validateOnInput` | Boolean  | false  | 是否启用实时验证 |
| `validator`       | Function | null   | 验证函数         |

#### 样式参数

| 参数             | 类型          | 默认值 | 说明               |
| ---------------- | ------------- | ------ | ------------------ |
| `inputStyle`     | Object/String | null   | 输入框样式         |
| `inputClass`     | String        | ''     | 输入框CSS类        |
| `containerStyle` | Object/String | null   | 容器样式           |
| `fixedHeight`    | Boolean       | false  | 固定错误信息高度   |
| `selectAll`      | Boolean       | true   | 打开时是否全选文本 |

#### 回调参数

| 参数        | 类型     | 默认值 | 说明                 |
| ----------- | -------- | ------ | -------------------- |
| `onChange`  | Function | null   | 输入变化回调         |
| `onConfirm` | Function | null   | 确认回调（支持异步） |

### 验证函数

验证函数接收输入值，返回验证结果：

```javascript
validator: value => {
  if (!value.trim()) {
    return { valid: false, message: '不能为空' }
  }
  if (value.length > 50) {
    return { valid: false, message: '不能超过50个字符' }
  }
  return { valid: true, message: '' }
}
```

### 回调函数

#### onChange回调

```javascript
onChange: (value, event) => {
  console.log('输入内容:', value)
  console.log('输入事件:', event)
}
```

#### onConfirm回调

支持异步操作，返回false可阻止对话框关闭：

```javascript
onConfirm: async value => {
  console.log('开始验证...')

  try {
    // 模拟异步验证
    await validateInput(value)
    console.log('验证成功')
    return true // 允许关闭对话框
  } catch (error) {
    console.log('验证失败:', error)
    return false // 阻止关闭对话框
  }
}
```

### 使用示例

#### 基础输入

```javascript
const result = await promptDialog.prompt({
  title: '重命名文件',
  placeholder: '请输入新的文件名',
  defaultValue: '我的文档.txt',
  hint: '文件名不能包含特殊字符',
})
```

#### 实时验证

```javascript
const result = await promptDialog.prompt({
  title: '创建用户',
  placeholder: '请输入用户名',
  validateOnInput: true,
  validator: value => {
    if (!value.trim()) {
      return { valid: false, message: '用户名不能为空' }
    }
    if (value.length < 3) {
      return { valid: false, message: '用户名至少3个字符' }
    }
    if (!/^[a-zA-Z0-9_]+$/.test(value)) {
      return { valid: false, message: '只能包含字母、数字和下划线' }
    }
    return { valid: true, message: '' }
  },
  onChange: value => {
    console.log('用户名输入:', value)
  },
})
```

#### 异步确认

```javascript
const result = await promptDialog.prompt({
  title: '修改密码',
  inputType: 'password',
  placeholder: '请输入新密码',
  validateOnInput: true,
  validator: value => {
    if (value.length < 8) {
      return { valid: false, message: '密码至少8位' }
    }
    return { valid: true, message: '' }
  },
  onConfirm: async password => {
    console.log('正在验证密码强度...')

    // 模拟异步验证
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 可以在这里调用API验证
    const isStrong = await checkPasswordStrength(password)
    if (!isStrong) {
      alert('密码强度不够')
      return false // 阻止关闭
    }

    return true // 允许关闭
  },
})
```

#### 大文本输入（textarea）

```javascript
const result = await promptDialog.prompt({
  title: '编辑内容',
  inputType: 'textarea',
  placeholder: '请输入内容...',
  inputStyle: {
    height: '400px',
    fontFamily: 'monospace',
    lineHeight: '1.5',
  },
  fixedHeight: true, // 固定错误信息高度
  hint: '支持多行文本输入，使用 Ctrl+Enter 确认',
  validator: value => {
    const lines = value.split('\n').length
    if (lines > 20) {
      return { valid: false, message: `行数过多！最多20行，当前${lines}行` }
    }
    if (value.length > 1000) {
      return { valid: false, message: `内容过长！最多1000字符，当前${value.length}字符` }
    }
    return { valid: true, message: '' }
  },
})
```

### 高度控制

#### 平滑过渡（默认）

错误信息出现/消失时有柔和的动画：

```javascript
const result = await promptDialog.prompt({
  title: '输入验证',
  validateOnInput: true,
  // fixedHeight: false (默认)
})
```

#### 固定高度

错误信息区域始终占位，完全避免高度变化：

```javascript
const result = await promptDialog.prompt({
  title: '输入验证',
  validateOnInput: true,
  fixedHeight: true, // 启用固定高度模式
})
```

### 返回值

- **成功**: 返回用户输入的字符串
- **取消**: 返回 `false`

## 🎨 样式自定义

### CSS变量

```css
:root {
  --dialog-primary-color: #4a90e2;
  --dialog-success-color: #28a745;
  --dialog-warning-color: #ffc107;
  --dialog-error-color: #dc3545;
  --dialog-border-radius: 8px;
  --dialog-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
```

### 自定义类名前缀

```vue
<Dialog class-prefix="my-dialog" title="自定义样式" content="使用自定义CSS类名前缀" />
```

### 样式覆盖

```css
/* 自定义对话框样式 */
.dialog-content {
  border: 2px solid #ff6b6b;
}

.dialog-title {
  color: #ff6b6b !important;
}
```

## 🎯 最佳实践

### 1. 错误处理

```javascript
try {
  const result = await alertDialog.confirm({
    title: '删除确认',
    content: '确定要删除吗？',
  })

  if (result) {
    await deleteItem()
    await alertDialog.success({ title: '删除成功' })
  }
} catch (error) {
  await alertDialog.error({
    title: '操作失败',
    content: error.message,
  })
}
```

### 2. 输入验证

```javascript
const result = await promptDialog.prompt({
  title: '输入邮箱',
  inputType: 'email',
  validateOnInput: true,
  validator: value => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(value)) {
      return { valid: false, message: '请输入有效的邮箱地址' }
    }
    return { valid: true, message: '' }
  },
})
```

### 3. 异步操作

```javascript
const result = await promptDialog.prompt({
  title: '保存文件',
  placeholder: '文件名',
  onConfirm: async fileName => {
    try {
      await saveFile(fileName)
      return true
    } catch (error) {
      await alertDialog.error({
        title: '保存失败',
        content: error.message,
      })
      return false // 保持对话框打开
    }
  },
})
```

## 🔧 高级配置

### 全局配置

```javascript
// 在main.js中设置全局默认值
app.provide('dialogDefaults', {
  variant: 'thunder',
  confirmText: '确定',
  cancelText: '取消',
})
```

### TypeScript支持

```typescript
interface DialogOptions {
  title?: string
  content?: string
  variant?: 'info' | 'success' | 'warning' | 'error' | 'thunder'
  showCancel?: boolean
  confirmText?: string
  cancelText?: string
}

interface PromptOptions extends DialogOptions {
  placeholder?: string
  defaultValue?: string
  inputType?: string
  validator?: (value: string) => { valid: boolean; message: string }
  onConfirm?: (value: string) => Promise<boolean>
}
```

## 📱 响应式支持

组件完全支持响应式设计，在移动端自动适配：

```css
@media (max-width: 768px) {
  .dialog-content {
    max-width: calc(100vw - 40px);
    margin: 20px;
  }
}
```

## 🚀 性能优化

1. **懒加载**: 对话框组件按需渲染
2. **事件委托**: 优化大量对话框的事件处理
3. **内存管理**: 自动清理已关闭的对话框实例

## 🛠️ 故障排除

### 常见问题

1. **对话框不显示**

   - 检查`v-model:open`绑定
   - 确认组件正确导入

2. **样式异常**

   - 检查CSS变量是否正确设置
   - 确认没有样式冲突

3. **验证不生效**
   - 确认`validateOnInput: true`
   - 检查validator函数返回格式

### 调试技巧

```javascript
// 启用调试模式
const alertDialog = useAlertDialog({ debug: true })

// 查看对话框状态
console.log('Dialog state:', alertDialog.getState())
```

## 📄 更新日志

### v1.0.0

- 初始版本发布
- 支持基础对话框功能
- 提供编程式API

### v1.1.0

- 新增Prompt输入对话框
- 支持实时验证
- 添加异步确认功能

### v1.2.0

- 新增textarea支持
- 优化高度控制
- 改进键盘交互

---
