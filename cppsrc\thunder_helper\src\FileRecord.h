#ifndef XL_9743E712_D9F7_4DDA_B60C_292DD56A65D5
#define XL_9743E712_D9F7_4DDA_B60C_292DD56A65D5

#include <xlcommon.h>
#include <thread>
#include <map>
#include <list>
#ifdef WIN32
#include <js_native_api.h>
#include <js_native_api_types.h>
#elif defined(__APPLE__) && defined(__MACH__)
#include <node_api.h>
#include <node.h>
#include <v8.h>
#endif
#include <AddonOpt.h>
#include "./sqlite/sqlite3.h"
#include "./StorageValue.h"

class DownloadFileRecord {
public:
	DownloadFileRecord(const std::string& strDir, const std::string& strDbName, std::shared_ptr<xl::thread::ThreadAffinity> main);
	DownloadFileRecord() = delete;

	xl::coroutine::AsyncTask<bool> Upsert(const std::string& strUrl, int32_t nIndex, const std::string& strFilePath);
	xl::coroutine::AsyncTask<std::string> Query(const std::string& strUrl, int32_t nIndex);

private:
	void Init();

private:
	std::shared_ptr<xl::thread::ThreadAffinity> m_mainAffinity{ nullptr };
	std::shared_ptr<xl::thread::ThreadAffinity> m_threadAffinity{ nullptr };
	std::thread m_t;
	std::string m_strDb;
	xl::coroutine::CoroLock m_loadLock{ true };
	sqlite3* m_pDataBase{ nullptr };
	uv_loop_t m_loop;
};

class DownloadFileRecordAddon {
public:
	static void Init(napi_env env, napi_value exports);

private:
	static napi_value Upsert(napi_env env, napi_callback_info info);
	static napi_value Query(napi_env env, napi_callback_info info);
	static napi_value JSConstructor(napi_env env, napi_callback_info info);
};

#endif