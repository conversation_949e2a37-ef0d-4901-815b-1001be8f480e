# 路由配置说明

本文件夹包含了主渲染进程的路由配置，基于 Vue Router 实现页面导航和状态管理。

## 文件结构

- `index.ts` - 主路由配置文件

## 核心功能

### 1. 路由定义

路由使用 Vue Router 的 `createRouter` 和 `createMemoryHistory` 创建，支持以下页面：

- `/link` - 全部页面（默认首页）
- `/recent_play` - 最近播放
- `/download` - 下载页面  
- `/trash` - 回收站
- `/cloud` - 云盘（常驻组件）
- `/loading` - 加载页面

### 2. 特殊配置

#### NavItem 接口
每个路由支持以下配置项：
- `name` - 路由名称
- `title` - 显示标题
- `path` - 路由路径
- `icon` - 图标配置（包含激活和默认状态）
- `resident` - 常驻挂载ID（用于外部组件挂载）
- `hideInSidebar` - 是否在侧边栏中隐藏

#### 常驻组件
通过 `resident` 配置可以指定组件挂载到特定的DOM元素上，例如云盘组件挂载到 `#thunder-pan` 元素。

### 3. 缓存机制 (KeepAlive)

- 默认所有组件都启用 `keepAlive` 缓存
- 可通过 `meta.keepAlive: false` 禁用特定路由的缓存
- `routesKeepAliveNames` 导出了所有需要缓存的组件名称

### 4. 路由守卫

实现了完整的路由历史管理：

#### 功能特性
- **历史记录管理** - 自动记录用户访问的路由历史
- **前进后退控制** - 支持浏览器式的前进后退操作
- **重定向处理** - 智能识别重定向，避免重复记录
- **头部按钮状态** - 自动更新前进后退按钮的可用状态

#### 工作流程
1. 路由变化时检查是否为重定向
2. 判断是否为前进后退操作
3. 非导航操作时添加到历史记录
4. 更新头部管理器的按钮状态
5. 重置导航标志

### 5. 导出项

- `router` - Vue Router 实例
- `routes` - 路由配置数组
- `routesKeepAliveNames` - 需要缓存的组件名称
- `navItems` - 导航项配置（用于侧边栏渲染）
- `residentIds` - 所有常驻组件的ID列表

## 新增路由指南

### 1. 创建路由页面组件

在创建新的路由页面组件时，**必须**使用 `defineOptions` 定义组件名称，名称需要与路由配置中的 `name` 字段保持一致：

```vue
<script setup lang="ts">
// 必须定义组件名称，与路由的 name 字段一致
defineOptions({
  name: 'your_route_name'
})

// 其他组件逻辑...
</script>

<template>
  <!-- 组件模板 -->
</template>
```

### 2. 添加路由配置

在 `routes` 数组中添加新的路由配置：

```typescript
{
  path: '/your-path',           // 路由路径
  name: 'your_route_name',      // 路由名称（与组件名称一致）
  component: YourComponent,     // 导入的组件
  meta: {
    title: '页面标题',          // 显示标题
    icon: {                     // 图标配置（可选）
      default: 'xl-icon-default',
      active: 'xl-icon-active'
    },
    keepAlive: true,           // 是否缓存（默认true）
    hideInSidebar: false,      // 是否在侧边栏隐藏（默认false）
    resident: 'dom-id'         // 常驻挂载ID（可选）
  }
}
```

### 3. 常见路由类型

#### 普通页面路由
```typescript
{
  path: '/settings',
  name: 'settings',
  component: Settings,
  meta: {
    title: '设置',
    icon: {
      default: 'xl-icon-settings',
      active: 'xl-icon-settings-active'
    },
    keepAlive: true
  }
}
```

#### 隐藏页面路由（不显示在侧边栏）
```typescript
{
  path: '/detail/:id',
  name: 'detail',
  component: Detail,
  meta: {
    title: '详情页',
    keepAlive: true,
    hideInSidebar: true  // 不在侧边栏显示
  }
}
```

#### 常驻组件路由（挂载到外部DOM）
```typescript
{
  path: '/external',
  name: 'external',
  component: createEmptyComponent('external'),  // 使用空组件占位
  meta: {
    title: '外部组件',
    icon: {
      default: 'xl-icon-external',
      active: 'xl-icon-external-active'
    },
    keepAlive: true,
    resident: 'external-mount-point'  // 挂载到 #external-mount-point
  }
}
```

### 4. 注意事项

- **组件名称一致性**：组件的 `defineOptions.name` 必须与路由的 `name` 字段完全一致
- **KeepAlive 缓存**：如果组件名称不匹配，KeepAlive 缓存将无法正常工作
- **导入组件**：确保在文件顶部正确导入新创建的组件
- **路径唯一性**：确保新路由的 `path` 在整个路由表中是唯一的

## 使用示例

### 基本路由导航
```typescript
import { router } from './router'

// 编程式导航
router.push('/download')
router.push({ name: 'recent_play' })
```

### 历史记录操作
```typescript
import { getRouteHistoryManager } from '@/common/route-history-manager'

const historyManager = getRouteHistoryManager()

// 后退
if (historyManager.canGoBack.value) {
  historyManager.goBack()
}

// 前进
if (historyManager.canGoForward.value) {
  historyManager.goForward()
}
```

### 在组件中使用 KeepAlive
```vue
<template>
  <router-view v-slot="{ Component }">
    <keep-alive :include="routesKeepAliveNames">
      <component :is="Component" />
    </keep-alive>
  </router-view>
</template>

<script setup>
import { routesKeepAliveNames } from './router'
</script>
```

## 注意事项

1. **常驻组件** - 带有 `resident` 配置的路由会使用空组件占位，实际组件挂载在指定的DOM元素上
2. **历史记录** - 使用内存历史模式，页面刷新后历史记录会丢失
3. **缓存管理** - KeepAlive 缓存可能导致组件状态保持，需要合理处理组件的生命周期
4. **侧边栏显示** - 通过 `hideInSidebar` 控制哪些路由在侧边栏中显示

## 相关依赖

- `vue-router` - Vue 路由管理
- `@/common/route-history-manager` - 自定义路由历史管理器
- `@root/common/manage/header-manger` - 头部管理器（控制前进后退按钮） 