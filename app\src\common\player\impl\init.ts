// import * as BaseType from './base'

// export function initAddon(initParam: BaseType.PlayerControlInitParam) {
//   native.initAddon(initParam);
// }

// export function setGlobalHttpRequest(cb: (url: string, method: string, filePath: string, body: string, headers: BaseType.XlRequestHeaders, cb:(res: string)=>void)=>void) {
//   native.setGlobalHttpRequest(cb);
// }

// export function addStatCallback(cb: (eventKey: string, value: string) => void)  {
//   native.addStatCallback(cb);
// }

// export function registerPanApi(apis: ESObject[]){
//   native.registerPanApi(apis);
// }

// export function updateUserInfo(info: ESObject) {
//   native.updateUserInfo(info);
// }