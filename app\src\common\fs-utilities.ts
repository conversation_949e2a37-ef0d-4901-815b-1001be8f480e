/**
 * @description: nodejs 文件相关的 async/await 的封装, AW 后缀为 async/await 缩写
 * @author: chenguideng
 */
import fs from 'node:fs'
import path from 'node:path'
import util from 'node:util'
import readline from 'node:readline'
const promisify: any = util.promisify;

export namespace FileSystemAWNS {
  export const MAX_PATH = 260;
  /**
   * @description 读取文件<br />
   * @method readFileAW
   * @param {string} filePath 文件路径
   * @return {Buffer} 成功为 Buffer 数据对象，失败为 null
   * @example
   * let data: Buffer = await readFileAW('d:/1.log'); <br />
   * @static
   */
  export async function readFileAW(filePath: string, options?: { encoding: string; flag?: string; } | string): Promise<Buffer> {
    let ret: Buffer = new Buffer('');
    if (filePath !== undefined) {
      const readFile: Promisify = promisify(fs.readFile);
      try {
        ret = await readFile(filePath, options);
      } catch (err) {
        console.log(err);
      }
    }
    return ret;
  }

  /**
   * @description 按行读取文件<br />
   * @method readLineAw
   * @param {string} filePath 文件路径
   * @return {string[]} 成功为 string 数组，失败为 null
   * @example
   * let lines: string[] = await readLineAw('d:/1.log'); <br />
   * @static
   */
  export async function readLineAw(filePath: string): Promise<string[]> {
    let ret: string[] = [];
    do {
      if (!filePath) {
        break;
      }

      if (!await existsAW(filePath)) {
        break;
      }

      ret = await new Promise<string[]>((resolve: any): void => {
        const lines: string[] = [];
        const input: fs.ReadStream = fs.createReadStream(filePath);
        const instance: readline.ReadLine = readline.createInterface({ input });
        instance.on('line', (line: string) => {
          lines.push(line);
        });
        instance.on('close', () => {
          resolve(lines);
        });
      });
    } while (0);
    return ret;
  }

  /**
   * @description 写文件<br />
   * @method writeFileAW
   * @param {string} filePath 文件路径
   * @param {any} data 文件数据
   * @return {boolean} 成功为 true，失败为 false
   * @example
   * let result: boolean = await writeFileAW('d:/1.log', 'abc'); <br />
   * @static
   */
  export async function writeFileAW(filePath: string, data: any): Promise<boolean> {
    let ret: boolean = false;
    if (filePath !== undefined && data !== null) {
      const writeFile: Promisify = promisify(fs.writeFile);
      try {
        await writeFile(filePath, data);
        ret = true;
      } catch (err) {
        console.error(err);
      }
    }
    return ret;
  }

  /**
   * @description 判断文件是否存在<br />
   * @method existsAW
   * @param {string} filePath 文件路径
   * @return {boolean} 成功为 true，失败为 false
   * @example
   * let result: boolean = await existsAW('d:/1.log'); <br />
   * @static
   */
  export async function existsAW(filePath: string): Promise<boolean> {
    let ret: boolean = false;
    if (filePath !== undefined) {
      // 使用promisify时碰到类似'D:\xunlei\.' 这种后面带了个点的路径, 会一直认为文件存在
      const exists = fs.promises.access;
      try {
        await exists(filePath);
        ret = true;
      } catch (err) {
        // logger.information(err);
      }
    }
    return ret;
  }

  export async function dirExistsAW(filePath: string): Promise<boolean> {
    let ret: boolean = false;
    do {
      ret = await existsAW(filePath);
      if (!ret) {
        break;
      }
      const stats = await lstatAW(filePath);
      if (!stats) {
        break;
      }
      ret = stats.isDirectory();
    } while (0);
    return ret;
  }

  /**
   * @description 创建文件夹, 只能创建父文件夹存在的文件夹<br />
   * @method mkdirAW
   * @param {string} dirName 文件夹路径
   * @return {boolean} 成功为 true，失败为 false
   * @example
   * let result: boolean = await mkdirAW('d:/1/2'); <br />
   * @static
   */
  export async function mkdirAW(dirName: string): Promise<boolean> {
    dirName = getValidDirName(dirName);
    let ret: boolean = false;
    if (dirName !== undefined) {
      const mkdir: Promisify = promisify(fs.mkdir);
      try {
        await mkdir(dirName);
        ret = true;
      } catch (err) {
        console.error(err);
      }
    }
    return ret;
  }

  /**
   * @description 删除文件夹, 只能删除空文件夹<br />
   * @method rmdirAW
   * @param {string} dirName 文件夹路径
   * @return {boolean} 成功为 true，失败为 false
   * @example
   * let result: boolean = await rmdirAW('d:/1/2'); <br />
   * @static
   */
  export async function rmdirAW(dirName: string): Promise<boolean> {
    let ret: boolean = false;
    if (dirName !== undefined) {
      const rmdir: Promisify = promisify(fs.rmdir);
      try {
        await rmdir(dirName);
        ret = true;
      } catch (err) {
        console.error(err);
      }
    }
    return ret;
  }

  /**
   * @description 删除文件<br />
   * @method unlinkAW
   * @param {string} filePath 文件路径
   * @return {boolean} 成功为 true，失败为 false
   * @example
   * let result: boolean = await unlinkAW('d:/1.log'); <br />
   * @static
   */
  export async function unlinkAW(filePath: string): Promise<boolean> {
    let ret: boolean = false;
    if (filePath !== undefined) {
      const unlink: Promisify = promisify(fs.unlink);
      try {
        await unlink(filePath);
        ret = true;
      } catch (err) {
        console.error(err);
      }
    }
    return ret;
  }

  /**
   * @description 获取文件夹下子文件及子文件夹名称<br />
   * @method readdirAW
   * @param {string} dirName 文件夹路径
   * @return {string[]} 成功为 string[] 对象，失败为 null
   * @example
   * let result: string[] = await readdirAW('d:/1/'); <br />
   * @static
   */
  export async function readdirAW(dirName: string): Promise<string[]> {
    let ret: string[] = [];
    if (dirName !== undefined) {
      const readdir: Promisify = promisify(fs.readdir);
      try {
        ret = await readdir(dirName);
      } catch (err) {
        console.error(err);
      }
    }
    return ret;
  }

  /**
   * @description 获取文件属性<br />
   * @method lstatAW
   * @param {string} filePath 文件路径
   * @return {fs.Stats} 成功为 fs.Stats 对象，失败为 null
   * @example
   * let result: fs.Stats = await lstatAW('d:/1.log'); <br />
   * @static
   */
  export async function lstatAW(filePath: string): Promise<fs.Stats | undefined> {
    let ret: any;
    if (filePath !== undefined) {
      const lstat: Promisify = promisify(fs.lstat);
      try {
        ret = await lstat(filePath);
      } catch (err) {
        console.error(err);
      }
    }
    return ret;
  }

  export async function rmSubdirAW(dirName: string, fileName: string): Promise<boolean> {
    let ret: boolean = false;
    if (dirName !== undefined && fileName !== undefined) {
      const filePath: string = path.join(dirName, fileName);
      const stats = await lstatAW(filePath);
      if (stats && stats.isDirectory()) {
        ret = await rmdirsAW(filePath);
      } else {
        ret = await unlinkAW(filePath);
      }
    }
    return ret;
  }

  /**
   * @description 删除文件夹, 连带文件夹里面的子文件一并删除<br />
   * @method rmdirsAW
   * @param {string} dirName 文件夹路径
   * @return {boolean} 成功为 true，失败为 false
   * @example
   * let result: boolean = await rmdirsAW('d:/2/'); <br />
   * @static
   */
  export async function rmdirsAW(dirName: string): Promise<boolean> {
    let ret: boolean = false;
    if (dirName !== undefined) {
      const isDir: boolean = await existsAW(dirName);
      if (isDir) {
        ret = true;
        const files: string[] = (await readdirAW(dirName)) || [];
        for (let i: number = 0; i < files.length; i++) {
          ret = (await rmSubdirAW(dirName, files[i])) && ret;
        }
        if (ret || files.length === 0) {
          ret = (await rmdirAW(dirName)) && ret;
        }
      }
    }
    return ret;
  }

  /**
   * @description 创建文件夹, 当父文件夹不存在时也会创建父文件夹<br />
   * @method mkdirsAW
   * @param {string} dirName 文件夹路径
   * @return {boolean} 成功为 true，失败为 false
   * @example
   * let result: boolean = await mkdirsAW('d:/2/3/'); <br />
   * @static
   */
  export async function mkdirsAW(dirName: string): Promise<boolean> {
    let ret: boolean = false;
    dirName = getValidDirName(dirName);
    if (dirName !== undefined && dirName !== '' && dirName !== '.' && dirName !== '..') {
      if (await existsAW(dirName)) {
        ret = true;
      } else {
        if (path.dirname(dirName) === dirName) {
          ret = false;
        } else if (await mkdirsAW(path.dirname(dirName))) {
          ret = await mkdirAW(dirName);
        }
      }
    }
    return ret;
  }

  /**
   * @description 移动文件；跨分区重命名文件，会有权限问题
   * @example
   * let ret: boolean = await renameAW('old.txt', 'new.txt');
   */
  export async function renameAW(oldPath: string, newPath: string, errCallback?: (err: unknown) => void): Promise<boolean> {
    let ret: boolean = false;
    newPath = getValidDirName(newPath);
    if (oldPath !== undefined && newPath !== undefined && oldPath?.toLowerCase() !== newPath?.toLowerCase()) {
      const rename: Promisify = promisify(fs.rename);
      try {
        await rename(oldPath, newPath);
        ret = true;
      } catch (err) {
        errCallback?.(err);
        console.error(err);
      }
    }
    return ret;
  }

  /**
   * @description: 复制文件
   * @param src 要被拷贝的源文件名称
   * @param dest 拷贝操作的目标文件名
   * @returns 成功返回true，否则false
   * @example
   * let ret: boolean = await copyFileAW('src.txt', 'dest.txt',  fs.constants.COPYFILE_EXCL);
   */
  export async function copyFileAW(src: string, dest: string): Promise<boolean> {
    let result: Promise<boolean>;
    if (src.toLowerCase() !== dest.toLowerCase() && (await existsAW(src))) {
      const readStream: fs.ReadStream = fs.createReadStream(src);
      const writeStream: fs.WriteStream = fs.createWriteStream(dest);
      result = new Promise<boolean>((resolve: any): void => {
        const pipeWriteStram: fs.WriteStream = readStream.pipe(writeStream);
        pipeWriteStram.on('finish', () => {
          resolve(true);
        });
        // 需要主动捕获error事件: 可能磁盘空间不足，导致拷贝失败，这里流程不让中断先 error.code === 'ENOSPC'
        pipeWriteStram.on('error', (err: NodeJS.ErrnoException) => {
          resolve(false);
        });
      });
    } else {
      result = new Promise<boolean>((resolve: any): void => {
        resolve(false);
      });
    }

    return result;
  }

  export async function copyDirsAW(src: string, dest: string): Promise<boolean> {
    let ret: boolean = false;
    let stats = await lstatAW(src);
    if (stats) {
      if (stats.isDirectory()) {
        ret = await mkdirsAW(dest);
        const files: string[] = (await readdirAW(src)) || []; // 此处只获取第一层，不递归
        for (let i: number = 0; i < files.length; i++) {
          const filePath: string = path.join(src, files[i]);
          const newPath: string = path.join(dest, files[i]);
          if (dest?.toLowerCase()?.indexOf(filePath?.toLowerCase()) === 0) {
            ret = true;
            continue;
          }
          ret = await existsAW(filePath);
          if (ret) {
            stats = await lstatAW(filePath);
            if (stats) {
              if (stats.isDirectory()) {
                ret = await copyDirsAW(filePath, newPath);
              } else {
                ret = await copyFileAW(filePath, newPath);
              }
            }
          }
        }
      }
    }
    return ret;
  }

  export async function moveDirsAW(src: string, dest: string): Promise<boolean> {
    let ret: boolean = false;
    let stats = await lstatAW(src);
    if (stats) {
      if (stats.isDirectory()) {
        ret = await mkdirsAW(dest);
        const files: string[] = (await readdirAW(src)) || []; // 此处只获取第一层，不递归
        for (let i: number = 0; i < files.length; i++) {
          const filePath: string = path.join(src, files[i]);
          const newPath: string = path.join(dest, files[i]);
          if (dest?.toLowerCase()?.indexOf(filePath?.toLowerCase()) === 0) {
            // 移动到子目录，排除当前目录
            // src: 'd:\\123', dest: 'd:\\123\\456\\789 针对这样的目录，不移动456及其子目录（还有一种是不移动456\789，后续可根据需要调整)
            ret = true;
            continue;
          }
          ret = await existsAW(filePath);
          if (ret) {
            stats = await lstatAW(filePath);
            if (stats) {
              if (stats.isDirectory()) {
                ret = await copyDirsAW(filePath, newPath);

                if (ret) {
                  await rmdirsAW(filePath);
                }
              } else {
                ret = await copyFileAW(filePath, newPath);

                if (ret) {
                  await unlinkAW(filePath);
                }
              }
            }
          }

          if (!ret) {
            // 中途失败，退出，不做回退处理
            break;
          }
        }
      }
    }
    return ret;
  }

  /**
   * @description 在temp目录下生成一个唯一的临时目录
   * fs.mkdtemp() 方法会直接附加六位随机选择的字符串到 prefix 字符串。
   * 例如，指定一个目录 /tmp，如果目的是要在 /tmp 里创建一个临时目录，
   * 则 prefix 必须 以一个指定平台的路径分隔符
   */
  export async function mkdtempAW(): Promise<boolean> {
    let ret: boolean = false;
    const mktemp: Promisify = promisify(fs.mkdtemp);
    const os: any = await import('os');
    const tmpDir: string = os.tmpdir();
    try {
      ret = await mktemp(`${tmpDir}${path.sep}`);
    } catch (error) {
      console.error(error);
    }
    return ret;
  }

  /**
   * 删除非空的子目录
   * 例如 E:\迅雷下载\我的应用\123 删除 我的应用\123
   * deleteEmptySubDirs('E:\迅雷下载\我的应用\123', 'E:\迅雷下载\');
   */
  export async function deleteEmptySubDirs(subPath: string, rootPath: string): Promise<boolean> {
    let ret: boolean = true;
    subPath = path.normalize(subPath);
    rootPath = path.normalize(rootPath);
    if (subPath.length > 3 && subPath[subPath.length - 1] === '\\') {
      subPath = subPath.slice(0, subPath.length - 1);
    }
    if (rootPath.length > 3 && rootPath[rootPath.length - 1] === '\\') {
      rootPath = rootPath.slice(0, rootPath.length - 1);
    }

    do {
      if (subPath.indexOf(rootPath) !== 0) {
        ret = false;
        break;
      }
      let recursion: string = subPath;
      while (recursion !== rootPath) {
        if (await existsAW(recursion)) {
          if (!(await rmdirAW(recursion))) {
            ret = false;
            break;
          }
        }
        recursion = path.dirname(recursion);
      }
    } while (0);

    return ret;
  }

  /**
   * @description 获取文件或者文件夹大小
   */
  export async function getFileSize(filePath: string): Promise<number> {
    let ret: number = 0;

    do {
      if (!filePath) {
        break;
      }
      if (!(await existsAW(filePath))) {
        break;
      }
      const stats = await lstatAW(filePath);
      if (stats) {
        if (stats.isDirectory()) {
          const files: string[] = await readdirAW(filePath);
          for (let i: number = 0; i < files.length; i++) {
            const subFile: string = path.join(filePath, files[i]);
            ret += await getFileSize(subFile);
          }
        } else {
          ret = stats.size;
        }
      }
    } while (0);

    return ret;
  }

  /**
   * @description: 判断文件夹是否温控
   * @param filePath 文件路径
   * @param retValIfNoExist 指定 文件/文件夹不存在时的返回值
   * @returns 空文件夹返回true，否则false
   * @example
   * let ret: boolean = await isDirectoryEmptyAW('E:\\迅雷下载');
   */
  export async function isDirectoryEmptyAW(filePath: string, retValIfNoExist: boolean = true): Promise<boolean> {
    let ret: boolean = true;

    do {
      if (!filePath) {
        ret = false;
        break;
      }

      if (!await existsAW(filePath)) {
        ret = retValIfNoExist;
        break;
      }

      const stats = await lstatAW(filePath);
      if (stats) {
        if (stats.isDirectory()) {
          const files: string[] = await readdirAW(filePath);
          // logger.information('is directory empty', ...files);
          if (files.length > 0) {
            ret = false;
            break;
          }
        } else {
          ret = false;
          break;
        }
      } else {
        ret = false;
        break;
      }

    } while (0);

    return ret;
  }

  // 去掉目录路径 or 子目录的前后空格和末尾的.
  export function getValidDirName(str: string): string {
    let result: string = str;
    if (result) {
      result = result.trim();
      let hasBackslash = false;
      if (result.length > 3) {
        if (result.endsWith('\\')) {
          hasBackslash = true;
          // 去掉末尾的\
          result = result.replace(/(\\+$)/, '');
          if (result.length < 3) {
            result = result + '\\';
          }
        }
      }
      result = result.replace(/(\.+$)/, '');

      if (hasBackslash && !result.endsWith('\\')) {
        // 保留输入格式
        result = result + '\\';
      }
    }

    return result;
  }
}
