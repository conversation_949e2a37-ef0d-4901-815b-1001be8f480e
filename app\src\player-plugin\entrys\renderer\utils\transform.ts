import { ensureArray } from '@root/common/ensure'

export function arrayToObj<T = any>(arr: T[], key): Record<string, T> {
  const listToMapReducer = (acc, cur) => {
    acc[cur[key]] = cur
    return acc
  }
  if (Array.isArray(arr)) {
    return arr.reduce(listToMapReducer, {})
  }
  return {}
}

export function arrayToTree(source: Array<any>, { parentKey = 'parentId', currentKey = 'id', childKey = 'children', buildItemFn = (item) => item, parentId = null, needEmptyChildren = false } = {}) {
  return source
    .filter((item) => item[parentKey] === parentId)
    .map((item) => {
      let result = {
        ...item,
        ...(buildItemFn && buildItemFn(item)),
      }
      if (source.find((it) => it[parentKey] === item[currentKey])) {
        result[childKey] = arrayToTree(source, {
          parentKey,
          currentKey,
          child<PERSON>ey,
          buildItemFn,
          parentId: item[currentKey],
          needEmptyChildren,
        })
      } else {
        if (needEmptyChildren) {
          result[childKey] = []
        }
      }
      return result
    })
}

export function getTreeParentNodeList<T>(treeToArr: T[], id, { parentKey = 'parentId', currentKey = 'id' } = {}) {
  const resList: T[] = []
  while (true) {
    const currentItem = treeToArr.find((it) => it[currentKey] === id)
    if (!currentItem) break
    resList.unshift(currentItem)
    const parentItem = treeToArr.find((it) => currentItem[parentKey] === it[currentKey])
    if (!parentItem) break
    id = parentItem[currentKey]
  }
  return resList
}

export function getTreeChildNodeList<T>(treeList: T[], id, { childKey = 'children', currentKey = 'id' } = {}) {
  const resList: T[] = []
  function getChildList(treeList: T[] = []) {
    treeList.forEach((treeItem) => {
      resList.push(treeItem)
      if (Array.isArray(treeItem[childKey])) {
        getChildList(treeItem[childKey])
      }
    })
  }
  getChildList(ensureArray(treeList).filter((it) => it[currentKey] === id))
  return resList
}

export function treeToArray<T>(tree: T[], { isDeleteChildren = false, childKey = 'children', parentKey = 'parentId', currentKey = 'id', rootParentId = null } = {}): T[] {
  let parentIdList: any[] = []
  return ensureArray(tree)
    .reduce(function reducer(con, item) {
      con.push({
        ...item,
        [parentKey]: parentIdList[parentIdList.length - 1] || rootParentId,
      })
      if (item[childKey] && item[childKey].length > 0) {
        parentIdList.push(item[currentKey])
        item[childKey].reduce(reducer, con)
        parentIdList.pop()
      } else {
      }
      return con
    }, [] as T[])
    .map((it) => {
      if (isDeleteChildren) {
        delete it[childKey]
      }
      return it
    })
}

export function jsonParse<T = any>(str: string, defaultVal: T): T {
  try {
    return JSON.parse(str) || defaultVal
  } catch (e) {
    return defaultVal
  }
}
