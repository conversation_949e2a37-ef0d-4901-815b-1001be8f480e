import * as BaseType from '@root/common/task/base'
import { sortMap } from './source'
import { IBranch, ILeaf } from '@/types/taskDetails'
import { AsyncRemoteCall } from "@root/common/renderer-async-remote-call"
import { ThunderHelper } from '@root/common/thunder-helper'
import { config } from '@root/common/config/config'

/** 打开文件夹 */
export function handleOpenFile () {

}

interface IContextMenuItem {
  key: string
  name: string
  disabled?: boolean
  iconLeft?: string
  iconRight?: string
  iconLeftEmpty?: boolean
  iconRightEmpty?: boolean
  toolbarIcon?: string
  show?: boolean
  children?: IContextMenuItem[][]
}
interface IContextMenuParams {
  taskInfos: BaseType.TaskBase[]
  isSupportPlay: boolean
  isBtSubFileScheduler?: boolean
  movePathList: string[]
}

/** 获取历史保存路径 */
export async function getHistoryMovePath() {
  const pathList = await config.getValue('MoveToDir', 'LatestSavePath', [])
  return pathList || []
}
/** 设置历史保存路径 */
export async function setHistorySaveMovePath(path: string) {
  try {
    if (!path) return
    const pathList = await getHistoryMovePath()

    if (pathList.includes(path)) {
      pathList.splice(pathList.indexOf(path), 1)
    }
    pathList.unshift(path)
    pathList.splice(3)
    config.setValue('MoveToDir', 'LatestSavePath', pathList)
  } catch (error) {
    console.error('>>>> setHistorySaveMovePath', error)
  }
}

/** 下载列表菜单栏 */
export function generateTaskContextMenu (params: IContextMenuParams) {
  const { taskInfos, isSupportPlay, isBtSubFileScheduler, movePathList } = params

  const subMenu = [] as any[]
  for (let savePath of movePathList) {
    // 处理特殊符号& （&符号在菜单上有特殊含义，&&才显示为&）
    // const re: RegExp = /&/gi;
    // savePath = savePath.replace(re, '&&');
    subMenu.push({ name: savePath, key: savePath, custom: 'moveHistoryPath' });
  }
  console.log('>>>>>>>>>>>>>>>>>>>>> taskInfos', taskInfos)
  let isMulti = taskInfos.length > 1
  const taskInfo = taskInfos[0]

  const isAllDownload = taskInfos.every(item => typeof item.taskStatus === 'number' &&
    [BaseType.TaskStatus.Started, BaseType.TaskStatus.StartWaiting, BaseType.TaskStatus.StartWaiting].includes(item.taskStatus))
  const isDownload = typeof taskInfo.taskStatus === 'number' && [BaseType.TaskStatus.Started, BaseType.TaskStatus.StartWaiting, BaseType.TaskStatus.StartWaiting].includes(taskInfo.taskStatus)
  const isCompleted = taskInfo.taskStatus === BaseType.TaskStatus.Succeeded
  const isExistFailed = taskInfos.findIndex(item => item.taskStatus === BaseType.TaskStatus.Failed || item.taskStatus === BaseType.TaskStatus.Unkown) !== -1
  const isExistPanTask = taskInfos.findIndex(item => item?.isPanTask) !== -1
  const isBt = taskInfo.taskType === BaseType.TaskType.Bt
  const one = [
    {
      key: 'play',
      name: '播放',
      iconLeft: 'xl-icon-general-play-m1',
      show: (!isExistFailed && !isMulti && isSupportPlay)
    },
    {
      key: 'openFile',
      name: '打开文件',
      iconLeft: 'xl-icon-general-openfile-m',
      show: (!isExistFailed && !isMulti && !isSupportPlay)
    },
    {
      key: 'retryDownload',
      name: '重新下载',
      iconLeft: 'xl-icon-general-retry-m',
      // 已完成 非云盘 
      show: (isCompleted || isExistFailed)
    },
    {
      key: 'download',
      name: '开始下载',
      iconLeft: 'xl-icon-general-direction-arrow-down-s',
      show: (!isDownload && !isCompleted && !isMulti) || (!isAllDownload && isMulti && !isCompleted)
    },
    {
      key: 'pause',
      name: '暂停下载',
      iconLeft: 'xl-icon-general-stop-m',
      show: (isDownload && !isMulti && !isCompleted && !isExistFailed) || (isAllDownload && isMulti && !isCompleted)
    },
    {
      key: 'openFolder',
      name: '打开文件夹',
      iconLeft: 'xl-icon-general-openfolder-m',
      show: !isMulti
    },
    {
      key: 'fileDetail',
      name: '文件详情',
      iconLeft: 'xl-icon-general-details-m',
      show: !isMulti
    },
    {
      key: 'addCloud',
      name: '添加到云盘',
      show: !isExistFailed && !isExistPanTask
    },
    {
      key: 'btSubFileScheduler',
      name: `${!isBtSubFileScheduler ?'启用': '取消'}顺序下载模式`,
      show: !isCompleted && isBt && !isMulti
    },
  ].filter(item => item.show)
  const two = [
    {
      key: 'delete',
      name: '删除',
      iconLeft: 'xl-icon-general-delete-m',
      show: !isExistPanTask
    },
    {
      key: 'shiftDelete',
      name: '彻底删除',
      iconLeft: 'xl-icon-general-close-m',
      show: true
    },
    {
      key: 'rename',
      name: '重命名',
      show: isCompleted && !isExistFailed && !isMulti
    },
    {
      key: 'move',
      name: '移动到',
      iconRight: 'xl-icon-direction-right-m',
      show: true,
      children: [
        [
          ...subMenu,
          {
            key: 'otherDirectory',
            name: '其他目录',
          },
        ]
      ],
    },
    {
      key: 'copy',
      name: '复制下载链接',
      show: !isExistPanTask
    },
  ].filter(item => item.show)
  const three = [
    {
      key: 'report',
      name: '举报',
      show: false,
    },
  ].filter(item => item.show)
  const menuList:IContextMenuItem[][] = []
  if (one.length) {
    menuList.push(one)
  }
  if (two.length) {
    menuList.push(two)
  }
  if (three.length) {
    menuList.push(three)
  }
  return menuList
}

/** 获取过滤key */
export function generateSortKey (params: {attr: number, state: number}) {
  const findData = sortMap.find(item => item.attr === params.attr && item.state === params.state)
  return findData && findData.key
}

/** 设置过滤key */
export function setSortKey (val: string) {
  const findData = sortMap.find(item => item.key === val)
  return findData
}

/** 格式化百分数 */
export function divideAndFormat (a, b, decimalPlaces = 2): number {
  if (b === 0 || a === 0) return 0
  const quotient = (a / b)*100
  const toNumber = parseFloat(quotient.toFixed(decimalPlaces)) || 0
  return toNumber
}
function getFileStatus(status: number|undefined, type: number|undefined): BaseType.BtSubFileStatus | undefined {
  if (type === BaseType.TaskType.Bt) {
    return status;
  }
  // 转换成bt状态
  if (typeof status === 'number' && [BaseType.TaskStatus.Seeding, BaseType.TaskStatus.Succeeded].includes(status)) {
    return BaseType.BtSubFileStatus.Complete;
  }
  if (status === BaseType.TaskStatus.Failed) {
    return BaseType.BtSubFileStatus.Failed;
  }
  return BaseType.BtSubFileStatus.Downloading;
}
// bt不能开始下载 暂停下载  -> 删除是 移除下载任务   彻底删除  是移除下载任务 + 删除本地文件
// 
export function generateDetailTaskContextMenu (file: (ILeaf | IBranch), isAllowDel: boolean) {
  console.log('>>>>>>>>>>>>> file', file)
  const data = file.data
  // const isBranch = file.type === 'branch'
  // 是否为任务组子文件
  // const isGroupSubTask = isBranch ? (file?.children && file?.children[0]?.data?.taskId) : data?.taskId
  // const status = getFileStatus(data?.status, data?.taskType)
  const isAllowPlay = data?.isSupportPlay
  // const isDownload = status && [BaseType.BtSubFileStatus.Downloading, BaseType.BtSubFileStatus.Waiting, BaseType.BtSubFileStatus.None].includes(status)
  // const isCompleted = data.status === BaseType.BtSubFileStatus.Complete
  // const isError = data.status === BaseType.BtSubFileStatus.Failed
  const menuList:IContextMenuItem[] = [
    {
      key: 'play',
      name: '播放',
      iconLeft: 'xl-icon-general-play-m1',
      show: isAllowPlay
    },
    // {
    //   key: 'retryDownload',
    //   name: '重新下载',
    //   iconLeft: 'xl-icon-general-retry-m',
    //   show: isGroupSubTask && isError
    // },
    // {
    //   key: 'download',
    //   name: '开始下载',
    //   iconLeft: 'xl-icon-general-direction-arrow-down-s',
    //   show: isGroupSubTask && !isError && !isDownload && !isCompleted
    // },
    // {
    //   key: 'pause',
    //   name: '暂停下载',
    //   iconLeft: 'xl-icon-general-stop-m',
    //   show: isGroupSubTask && !isError && isDownload && !isCompleted
    // },
    // {
    //   key: 'download',
    //   name: '移除任务',
    //   iconLeft: 'xl-icon-general-remove-m',
    //   show: isError
    // },
    {
      key: 'delete',
      name: '删除',
      iconLeft: 'xl-icon-general-delete-m',
      show: isAllowDel
    },
    {
      key: 'shiftDelete',
      name: '彻底删除',
      iconLeft: 'xl-icon-general-close-m',
      show: isAllowDel
    },
  ].filter(item => item.show)
  return [menuList]
}


export function moveTaskMessage(inx?: BaseType.MoveTaskToNewPathResult) {
  switch (inx) {
    case BaseType.MoveTaskToNewPathResult.SameDir:
      return '移动失败，目标目录与当前目录相同'
    case BaseType.MoveTaskToNewPathResult.SubDir:
      return '移动失败，新目录是原目录的子目录'
    case BaseType.MoveTaskToNewPathResult.CreateNewPathFailed:
      return '移动失败，新目录不存在且创建失败'
    case BaseType.MoveTaskToNewPathResult.FileExistInNewPath:
      return '移动失败，新目录存在要复制的文件或者文件夹'
    case BaseType.MoveTaskToNewPathResult.Other:
      return '移动失败，位置错误'
    default:
      return '移动成功'
  }
}

/** 主窗口置顶 */
export async function bringMainWndToTop () {
  const currentWindow = await AsyncRemoteCall.GetInstance().getCurrentWindow()
  if (currentWindow) {
    currentWindow.show()
    ThunderHelper.setForegroundWindow(currentWindow)
  }
}

/** 睡眠函数 */
export function sleep (ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
