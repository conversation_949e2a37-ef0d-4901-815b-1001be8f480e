import { IDownloadModelPositionParams } from '../download-model-type'
import { CallUiOperationWithIpcClient } from '../call-api';

export class DownloadModelManagerClient {
  private static instance: DownloadModelManagerClient;
  private apiProxy: CallUiOperationWithIpcClient = new CallUiOperationWithIpcClient();

  public static GetInstance(): DownloadModelManagerClient {
    if (!DownloadModelManagerClient.instance) {
      if (global.DownloadModelManagerCLientInstance) {
        DownloadModelManagerClient.instance = global.DownloadModelManagerCLientInstance;
      } else {
        DownloadModelManagerClient.instance = new DownloadModelManagerClient();
        global.DownloadModelManagerCLientInstance = DownloadModelManagerClient.instance;
      }
    }
    return DownloadModelManagerClient.instance;
  }

  public async positionDownloadTask(params: IDownloadModelPositionParams) {
    console.log('>>>>>>>>>>>>> positionDownloadTask', params)
    return await this.apiProxy!.CallApi('DownloadModelManagerClientPositionTask', params)
  }

}
