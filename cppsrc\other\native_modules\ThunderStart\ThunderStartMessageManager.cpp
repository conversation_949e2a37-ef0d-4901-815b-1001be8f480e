#include "StdAfx.h"
#include "ThunderStartMessageManager.h"

#include "XLWin32Helper.h"

ThunderStartMessageManager::ThunderStartMessageManager(void)
{

}

ThunderStartMessageManager::~ThunderStartMessageManager(void)
{
	if (IsWindow())
	{
		DestroyWindow();
	}
}

bool ThunderStartMessageManager::Init()
{
	ChangeWindowMessageFilter(XL_WM_COMMANDQUIT, 1);

	m_hwndCommand = ::FindWindow(XL_WINDOW_THUNDERSTARTCOMMAND_NAME, XL_WINDOW_THUNDERSTARTCOMMAND_NAME);
	if (m_hwndCommand == NULL)
	{
		m_hwndCommand = Create(NULL, NULL, XL_WINDOW_THUNDERSTARTCOMMAND_NAME, WS_OVERLAPPEDWINDOW);
	}
	return true;
}

LRESULT ThunderStartMessageManager::OnQuitCommand(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& /*bHandled*/)
{
	ETW_LEVEL_INFORMATION(L"Receive quit command");

	::PostThreadMessage(GetCurrentThreadId(), WM_QUIT, 0, 0);

	return 0;
}


void ThunderStartMessageManager::ChangeWindowMessageFilter(UINT message, DWORD dwFlag)
{
	HMODULE hUser32 = GetModuleHandle(L"user32.dll");
	if (hUser32 != NULL)
	{
		typedef BOOL(WINAPI* PFNChangeWindowMessageFilter)(UINT message, DWORD dwFlag);
		PFNChangeWindowMessageFilter pfnChangeWindowMessageFilter = (PFNChangeWindowMessageFilter)GetProcAddress(hUser32, "ChangeWindowMessageFilter");
		if (pfnChangeWindowMessageFilter != NULL)
		{
			pfnChangeWindowMessageFilter(message, dwFlag);
		}
	}
}
