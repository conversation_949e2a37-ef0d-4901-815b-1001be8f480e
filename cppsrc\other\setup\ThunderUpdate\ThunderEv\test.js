function getVersionCode(v) {
    let code = 0;
    let version = v.split('.');
    if (version && version.length === 4) {
      let v1 = Number(version[0]).valueOf();
      let v2 = Number(version[1]).valueOf();
      let v3 = Number(version[2]).valueOf();
      let productType = 0x80;
      code = productType * Math.pow(2, 24) + v1 * Math.pow(2, 16) + v2 * Math.pow(2, 8) + v3;
    }
    return code;
}

// for test

getVersionCode("12.0.6.6890")


//////////////////// 以下代码需要在note环境下运行
b = {
    "app_id": "20001",
    "channel_id": "100001",
    "device_id": "509A4C0FA589J83Q",
    "version_code": 2148270086,
    "package_name": "thunder_pc",
    "version_name": "12.0.6.6890",
    "last_version_code": 2148270086
}
var { app_id, package_name, channel_id, device_id, version_code } = b;
key = 'app_id_6YCG5Z';
dataStr = `app_id=${app_id}&package_name=${package_name}&channel_id=${channel_id}&device_id=${device_id}&version_code=${version_code}`;
hmac = require('crypto').createHmac('md5', key);
console.error('data', dataStr);
sign = hmac.update(dataStr).digest('hex');
console.error('sign', sign);
////////////////////