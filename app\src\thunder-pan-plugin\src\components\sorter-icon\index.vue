<script lang="ts" setup>
const props = withDefaults(defineProps<{
  asc?: boolean
  disabled?: boolean
  tooltip?: string
}>(), {
  asc: false,
  disabled: true,
})
</script>

<template>
  <div v-tooltip="tooltip" class="sorter-icon-wrapper">
    <i
      class="xl-icon-general-direction-caret-up-m"
      :class="{
        disabled: disabled || !asc
      }"
    ></i>
    <i
      class="xl-icon-general-direction-caret-down-m"
      :class="{
        disabled: disabled || asc
      }"
    ></i>
  </div>
</template>

<style lang="scss" scoped>
.sorter-icon-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;

  i {
    color: var(--font-font-1);

    &:first-child {
      height: 4px;
    }

    &.disabled {
      color: var(--font-font-4);
    }
  }
}
</style>