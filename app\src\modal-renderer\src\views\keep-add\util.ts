import { TaskBase, TaskType, BtSubFileStatus } from '@root/common/task/base'
import { IChangeTaskInfo, ILeaf, IBranch, IPostOrderBrachRs } from './type'

export function parseFileList (subFiles: IChangeTaskInfo[], taskBase: TaskBase) {
  const filePathCache: { [filePath: string]: boolean } = {};
  let branchMap: { [key: string]: IBranch } = {};
  let leafMap: { [key: string]: ILeaf } = {};
  /** 设置节点信息 */
  function setBranch(pwd: string, branchName: string) {
    const parent: IBranch = branchMap[pwd];
    const fullPath: string = pwd ? `${pwd}\\${branchName}` : branchName;
    let branch: IBranch = branchMap[fullPath];

    if (!branch) {
      // 初始化
      branch = branchMap[fullPath] = {
        type: 'branch',
        name: branchName,
        children: [],
        key: fullPath,
        parent,
        fileSize: 0,
        downloadSize: 0,
        rootBtTaskId: 0,
        isComplete: false,
        progressStr: '0%',
        progressNum: 0,
        // realIndex: 0,
        download: false,
      };

      if (parent) {
        parent.children.push(branch);
      }
    }
  }

  function setLeaf(pwd: string, leafData: IChangeTaskInfo) {
    const fullPath: string = (pwd ? `${pwd}\\${leafData.fileName}` : leafData.fileName) || ''
    const branch: IBranch = branchMap[pwd];
    let leaf: ILeaf = leafMap[fullPath];
    if (!leaf) {
      let progressStr = ''
      let progressNum = calculateProgress(leafData.downloadSize, leafData.fileSize)
      if (leafData.fileStatus === BtSubFileStatus.Complete) {
        progressStr = '已完成'
      } else if (!leafData.download) {
        progressStr = '不下载'
      } else {
        progressStr = `${progressNum}%`
      }
      leaf = {
        type: 'leaf',
        fullPath,
        name: leafData.fileName || '',
        key: fullPath,
        parent: branch,
        fileSize: leafData.fileSize,
        downloadSize: leafData.downloadSize,
        rootBtTaskId: leafData.rootBtTaskId || 0,
        isComplete: leafData.fileStatus === BtSubFileStatus.Complete,
        progressStr,
        progressNum,
        realIndex: leafData.realIndex,
        download: leafData.download,
        taskType: leafData.taskType,
        fileStatus: leafData.fileStatus,
        taskId: leafData.taskId || 0,
      };
      // if (leafData.taskType === TaskType.Bt) {
      //   leaf.rootBtTaskId = leafData.rootBtTaskId || 0
      // }
      leafMap[fullPath] = leaf;

      branch.children.push(leaf);
    }
    // console.log('>>>>>>>>>>>> leafData', leafData)
    branch.fileSize += leafData.fileSize;
    let branchParent: any = branch.parent;
    while (branchParent) {
      branchParent.fileSize += leafData.fileSize;
      branchParent = branchParent.parent;
    }
  }

  setBranch('', '');

  subFiles.forEach((file: IChangeTaskInfo) => {
    const filePath = file.filePath || '';

    if (!filePathCache[filePath]) {
      filePathCache[filePath] = true;
      let pwd: string = '';

      filePath.split('\\').forEach((dirname: string) => {
        if (dirname !== '') {
          setBranch(pwd, dirname);
          pwd = pwd ? `${pwd}\\${dirname}` : dirname;
        }
      });
    }
    setLeaf(filePath.slice(0, -1), file);
  })

  const parent: IBranch = branchMap['']
  postOrderBranch(parent)

  console.log('>>>>>>>>>>>>>>>> branchMap', branchMap)
  console.log('>>>>>>>>>>>>>>>> branchMap', leafMap)
  return { branchMap, leafMap }
}

function postOrderBranch (branchData: IBranch) : IPostOrderBrachRs | undefined {
  if (!branchData) { return }
  const children: (IBranch | ILeaf)[] = branchData.children;
  let downloadSize: number = 0;
  let fileSize: number = 0;
  let isAllComplete: boolean = children.length > 0 ? true : false;
  let downloadTotal = 0
  for (const child of children) {
    if (isLeaf(child)) {
      // 判断文件是否需要下载
      if (child.download) {
        downloadTotal += 1
      }
      downloadSize += child.downloadSize || 0;
      fileSize += child.fileSize;
      const isComplete = child.fileStatus === BtSubFileStatus.Complete
      isAllComplete = isAllComplete && isComplete
    } else if (isBranch(child)) {
      const ret: {
        downloadSize: number,
        fileSize: number,
        isAllComplete: boolean,
        downloadTotal: number
      } | undefined = postOrderBranch(child);
      console.log('>>>>>>>>>>> ret', ret)
      downloadTotal += ret?.downloadTotal ?? 0
      downloadSize += ret?.downloadSize ?? 0;
      fileSize += ret?.fileSize ?? 0;
      isAllComplete = isAllComplete && !!ret?.isAllComplete;
    }
  }
  let progressNum: number = calculateProgress(downloadSize, fileSize);
  // if (!isAllComplete) {
  //   progressNum = progressNum >= 1 ? 0.9999 : progressNum;
  // }
  console.log('>>>>>>>>>>>> progressNum', progressNum)
  const progressStr = isAllComplete ? '已完成' : `${progressNum}%`
  branchData.data = {
    downloadSize,
    fileSize,
    isComplete: isAllComplete,
    progressNum,
    progressStr,
  };
  console.log('>>>>>>>>>>>> downloadSize, fileSize',branchData, downloadSize, fileSize)
  return { downloadSize, fileSize, isAllComplete, downloadTotal };
}

/** 计算进度 */
function calculateProgress (downloadSize, fileSize, decimalPlaces = 2) {
  if (fileSize === 0 || downloadSize === 0) return 0
  let quotient = (downloadSize / fileSize)*100
  quotient = quotient > 100 ? 100 : quotient
  return parseFloat(quotient.toFixed(decimalPlaces)) || 0
}

function isLeaf(item: ILeaf | IBranch): item is ILeaf {
  return item.type === 'leaf';
}

function isBranch(item: ILeaf | IBranch): item is IBranch {
  return item.type === 'branch';
}