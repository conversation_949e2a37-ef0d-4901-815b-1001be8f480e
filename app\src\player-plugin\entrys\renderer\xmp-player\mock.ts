import {
  PlayListItem,
  PlayListItemMediaInfo,
  PlaySpeedDisplayInfo,
  RatioItem,
  SubtitleItemDisplayInfo,
} from '@root/common/player/base'

export const mockSubtitlesList = [
  {
    id: '1',
    displayName:
      '字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1',
  },
  {
    id: '2',
    displayName: '字幕2',
  },
  {
    id: '3',
    displayName:
      '字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1',
  },
  {
    id: '4',
    displayName: '字幕2',
  },
  {
    id: '5',
    displayName:
      '字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1',
  },
  {
    id: '6',
    displayName: '字幕2',
  },
  {
    id: '7',
    displayName:
      '字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1',
  },
  {
    id: '8',
    displayName: '字幕2',
  },
  {
    id: '9',
    displayName:
      '字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1',
  },
  {
    id: '10',
    displayName: '字幕2',
  },
  {
    id: '11',
    displayName:
      '字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1字幕1',
  },
  {
    id: '12',
    displayName: '字幕2',
  },
] as SubtitleItemDisplayInfo[]

export const mockPlayList = [
  {
    id: '1', // 该item的标识
    name: '满江红.Full.River.Red.2023.1080p.WEB-DL.H264.AAC-GPTHD.mp4',
    url: 'http://aplayer.open.xunlei.com/test.mp4',
  },
  {
    id: '2', // 该item的标识
    name: '迅雷区块链.mp4',
    url: 'https://blockchain.xunlei.com/video/v2/index-banner.mp4',
  },
  {
    id: '3', // 该item的标识
    name: '满江红.Full.River.Red.2023.1080p.WEB-DL.H264.AAC-GPTHD.mp4',
    url: 'http://aplayer.open.xunlei.com/test.mp4',
  },
  {
    id: '4', // 该item的标识
    name: '迅雷区块链.mp4',
    url: 'https://blockchain.xunlei.com/video/v2/index-banner.mp4',
  },
  {
    id: '5', // 该item的标识
    name: '满江红.Full.River.Red.2023.1080p.WEB-DL.H264.AAC-GPTHD.mp4',
    url: 'http://aplayer.open.xunlei.com/test.mp4',
  },
  {
    id: '6', // 该item的标识
    name: '迅雷区块链.mp4',
    url: 'https://blockchain.xunlei.com/video/v2/index-banner.mp4',
  },
  {
    id: '7', // 该item的标识
    name: '满江红.Full.River.Red.2023.1080p.WEB-DL.H264.AAC-GPTHD.mp4',
    url: 'http://aplayer.open.xunlei.com/test.mp4',
  },
  {
    id: '8', // 该item的标识
    name: '迅雷区块链.mp4',
    url: 'https://blockchain.xunlei.com/video/v2/index-banner.mp4',
  },
  {
    id: '9', // 该item的标识
    name: '满江红.Full.River.Red.2023.1080p.WEB-DL.H264.AAC-GPTHD.mp4',
    url: 'http://aplayer.open.xunlei.com/test.mp4',
  },
  {
    id: '10', // 该item的标识
    name: '迅雷区块链.mp4',
    url: 'https://blockchain.xunlei.com/video/v2/index-banner.mp4',
  },
] as PlayListItem[]

export const mockPlayMediaInfoMap = new Map<
  PlayListItem['id'],
  PlayListItemMediaInfo
>([
  [
    '2581CF3C2CE32995239D2DC07ED566683D569DAA',
    {
      duration: 10000,
      pos: 100,
      size: 3000,
      local: true, // 是否本地播放
      snapshot: '',
    },
  ],
  [
    '1234',
    {
      duration: 19000,
      pos: 100,
      size: 3000,
      local: true, // 是否本地播放
      snapshot: '',
    },
  ],
])

export const mockSpeedList = [
  {
    id: 'play_speed_0.5',
    name: '0.5x',
    // 切换提示语的倍速显示
    showValue: '0.5',
    needYearSuperVip: 0,
    needSuperVip: 0,
    needVip: 0,
  },
  {
    id: 'play_speed_1',
    name: '1.0x',
    // 切换提示语的倍速显示
    showValue: '1.0',
    needYearSuperVip: 0,
    needSuperVip: 0,
    needVip: 0,
  },
  {
    id: 'play_speed_2',
    name: '2.0x',
    // 切换提示语的倍速显示
    showValue: '2.0',
    needYearSuperVip: 0,
    needSuperVip: 0,
    needVip: 0,
  },
] as PlaySpeedDisplayInfo[]

export const mockRatioList = [
  // {
  //   mediaId: 'origin',
  //   mediaName: '原始画质',
  //   resolutionName: '',
  //   needMoreQuota: 1, // 当前清晰度是否需要更高的身份 1：需要 0：不需要
  //   origin: 1, // 是否原画 1：原画 0：非
  //   // 当needMoreQuota为false的时候，非会员可以播放，
  //   // 当needMoreQuota为true的时候，如果vip_type==vip.super，则需要超会，否则会员
  //   vipType: 'vip.super'
  // },
  {
    mediaId: 'mediaId_4K',
    mediaName: '4K',
    resolutionName: '4K',
    needMoreQuota: 1, // 当前清晰度是否需要更高的身份 1：需要 0：不需要
    origin: 0, // 是否原画 1：原画 0：非
    // 当needMoreQuota为false的时候，非会员可以播放，
    // 当needMoreQuota为true的时候，如果vip_type==vip.super，则需要超会，否则会员
    vipType: 'vip.super',
  },
  {
    mediaId: 'mediaId_1080P',
    mediaName: '超清 1080P',
    resolutionName: '1080P',
    needMoreQuota: 1, // 当前清晰度是否需要更高的身份 1：需要 0：不需要
    origin: 0, // 是否原画 1：原画 0：非
    // 当needMoreQuota为false的时候，非会员可以播放，
    // 当needMoreQuota为true的时候，如果vip_type==vip.super，则需要超会，否则会员
    vipType: 'vip',
  },
  {
    mediaId: 'mediaId_720P',
    mediaName: '高清 720P',
    resolutionName: '720P',
    needMoreQuota: 0, // 当前清晰度是否需要更高的身份 1：需要 0：不需要
    origin: 0, // 是否原画 1：原画 0：非
    // 当needMoreQuota为false的时候，非会员可以播放，
    // 当needMoreQuota为true的时候，如果vip_type==vip.super，则需要超会，否则会员
    vipType: 'vip',
  },
  {
    mediaId: 'mediaId_360P',
    mediaName: '流畅 360P',
    resolutionName: '360P',
    needMoreQuota: 0, // 当前清晰度是否需要更高的身份 1：需要 0：不需要
    origin: 0, // 是否原画 1：原画 0：非
    // 当needMoreQuota为false的时候，非会员可以播放，
    // 当needMoreQuota为true的时候，如果vip_type==vip.super，则需要超会，否则会员
    vipType: '',
  },
] as RatioItem[]
