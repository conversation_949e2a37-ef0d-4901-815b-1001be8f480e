#pragma once
#include <string>
#include <vector>
#include <node_api.h>
#include <node.h>
#include <v8.h>
#include <AddonOpt.h>
#include "mac_native.h"
#include <stdio.h>
#include <signal.h>
#include <unistd.h>


static napi_value CreatePlayerWnd(napi_env env, napi_callback_info info) {
    
    size_t argc = 2;
    napi_value argv[2] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    int64_t h1 = 0;
    AddonBaseOpt::ParseInt64(env, argv[0], h1);
    int64_t h2 = 0;
    AddonBaseOpt::ParseInt64(env, argv[1], h2);

    // 调用实际实现
    auto playerViewHandle = createPlayerWindowImpl(static_cast<int64_t>(h2));

    // 返回新创建的 viewHandle 作为 BigInt
    napi_value result;
    status = napi_create_int64(env, static_cast<int64_t>(playerViewHandle), &result);
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to create result BigInt");
        return nullptr;
    }
    return result;
}

static napi_value CreateSnapshotWnd(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value argv[2] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    napi_value obj;
    napi_create_int64(env, (int64_t)0, &obj);
    return obj;
}


static napi_value SetPlayerWndVisible(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    bool b = false;
    AddonBaseOpt::ParseBool(env, argv[0], b);
    // ::ShowWindow(g_playerWnd, b ? SW_SHOW : SW_HIDE);

    return nullptr;
}

static napi_value InvalidPlayerWnd(napi_env env, napi_callback_info info) {
    return nullptr;
}

static napi_value GetPeerId(napi_env env, napi_callback_info info) {
    // TODO
    std::string id = getPeerId() ;// peer_id::acquire_peerid(flag::XMP);
    napi_value obj;
    napi_create_string_utf8(env, id.c_str(), id.length(), &obj);

    return obj;
}

static napi_value ReadRegString(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    
    napi_value valueObj;
    napi_create_object(env, &valueObj);
    bool bSucc = false;
    std::string strMsg = "not support";
    AddonBaseOpt::PushObjectString(env, valueObj, "msg", strMsg);
    AddonBaseOpt::PushObjectBool(env, valueObj, "succ", bSucc);

    return valueObj;
}

static napi_value SetCommandLineCallback(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    auto pFunc = NapiFunctionWarp::NewObject(env, argv[0]);
    // TODO
    //CommandLine::GetInstance()->Init(env, pFunc);
    return nullptr;
}
static napi_value BringWndToTop(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    int64_t h1 = 0;
    AddonBaseOpt::ParseInt64(env, argv[0], h1);
    
    //::BringWindowToTop((HWND)h1);
    return nullptr;
}

static napi_value SetForegroundWindow(napi_env env, napi_callback_info info) {
    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    
    return nullptr;
}

static napi_value GetFileVersion(napi_env env, napi_callback_info info) {
    v8::Isolate* isolate = v8::Isolate::GetCurrent();
    v8::HandleScope scope(isolate);

    std::string str_version = "";

    size_t argc = 3;
    napi_value argv[3] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    // TODO
    std::string strVersion = "1.1.1.1";
    napi_value obj;
    napi_create_string_utf8(env, strVersion.c_str(), strVersion.length(), &obj);
    return obj;
}

static std::string GetVersionBlockString(const char* block)
{
    static std::string str="100001";
    
    return str;
}
static napi_value GetInstallChannel(napi_env env, napi_callback_info info) {
    std::string install_channel = "100001";
    napi_value obj;
    napi_create_string_utf8(env, install_channel.c_str(), install_channel.length(), &obj);
    return obj;
}

static napi_value GetPublicUserDataPath(napi_env env, napi_callback_info info) {
    //TODO
    std::string str = "";
    napi_value obj;
    napi_create_string_utf8(env, str.c_str(), str.length(), &obj);
    return obj;
}

static napi_value ReadINI(napi_env env, napi_callback_info info) {
    // TODO
    std::string strResult = "";
    napi_value obj;
    napi_create_string_utf8(env, strResult.c_str(), strResult.length(), &obj);
    return obj;
}

static napi_value WriteINI(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value argv[4] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);
    unsigned long result = 0;
    napi_value obj;
    napi_create_int64(env, 0, &obj);
    return obj;
}

static napi_value GetDmideCode(napi_env env, napi_callback_info info) {
    std::string strDemideCode = "";
    napi_value obj;
    napi_create_string_utf8(env, strDemideCode.c_str(), strDemideCode.length(), &obj);
    return obj;
}

static napi_value GetExeCommandLine(napi_env env, napi_callback_info info) {
    std::string str="";
    napi_value obj;
    napi_create_string_utf8(env, str.c_str(), str.length(), &obj);
    return obj;
}


static napi_value ExportGetLogicalDriveStrings(napi_env env, napi_callback_info info) {
    std::vector<std::string> drivers;
    napi_value obj = nullptr;
    AddonBaseOpt::PushStringArray(env, &obj, drivers);
    return obj;
}

static napi_value ExportGetFreePartitionSpace(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value argv[4] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    int64_t total_space = -1;
    int64_t free_space = -1;
    

    napi_value obj = nullptr;
    AddonBaseOpt::PushInt64(env, &obj, free_space);
    return obj;
}

static napi_value ExportTerminateProcess(napi_env env, napi_callback_info info) {
    pid_t pid = getpid();
    kill(pid, SIGTERM);
    return nullptr;
}

static napi_value StartAutoUpdate(napi_env env, napi_callback_info info) {
   
    return nullptr;
}

static napi_value StartLocalUpdate(napi_env env, napi_callback_info info) {
    
    return nullptr;
}

static napi_value IsAutoRun(napi_env env, napi_callback_info info) {
    napi_value obj;
    AddonBaseOpt::PushBool(env, &obj, false);
    return obj;
}

static napi_value SetAutoRun(napi_env env, napi_callback_info info) {
    napi_value obj;
    AddonBaseOpt::PushBool(env, &obj, false);
    return obj;
}

void task_callback(const char* result) {
    printf("Task Info JSON: %s\n", result);
}

static napi_value ExportSetScreenSaveActive(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value argv[4] = { nullptr };
    napi_status status = napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    assert(napi_ok == status);

    bool bActive = true;
    AddonBaseOpt::ParseBool(env, argv[0], bActive);
    if (!bActive)
    {   
        preventSleep();
        // get_task_info(1,2, task_callback);
    }
    else
    {
        allowSleep();
    }
    return nullptr;
}
static napi_value StartManaulUpdate(napi_env env, napi_callback_info info) {
    return nullptr;
}
static napi_value XlShellExecEx(napi_env env, napi_callback_info info) {
    napi_value obj;
    AddonBaseOpt::PushInt64(env, &obj, 0);
    return obj;
}
static napi_value Associate(napi_env env, napi_callback_info info) {
    return nullptr;
}

static napi_value AddFloatWinodwShowRect(napi_env env, napi_callback_info info) {
    return nullptr;
}

static napi_value DelFloatWinodwShowRect(napi_env env, napi_callback_info info) {
    return nullptr;
}

void InitPlatformApi(napi_env env, napi_value exports) {
    napi_property_descriptor desc[] = {
        {"readRegString", nullptr, ReadRegString, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"createPlayerWnd", nullptr, CreatePlayerWnd, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setPlayerWndVisible", nullptr, SetPlayerWndVisible, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"createSnapshotWnd", nullptr, CreateSnapshotWnd, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getPeerId", nullptr, GetPeerId, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setCommandLineCallback", nullptr, SetCommandLineCallback, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"bringWndToTop", nullptr, BringWndToTop, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setForegroundWindow", nullptr, SetForegroundWindow, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getFileVersion", nullptr, GetFileVersion, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getInstallChannel", nullptr, GetInstallChannel, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getPublicUserDataPath", nullptr, GetPublicUserDataPath, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"readINI", nullptr, ReadINI, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"writeINI", nullptr, WriteINI, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getDmideCode", nullptr, GetDmideCode, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getCommandLine", nullptr, GetExeCommandLine, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"invalidPlayerWnd", nullptr, InvalidPlayerWnd, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getFreePartitionSpace", nullptr, ExportGetFreePartitionSpace, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getLogicalDriveStrings", nullptr, ExportGetLogicalDriveStrings, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"terminateProcess", nullptr, ExportTerminateProcess, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"startAutoUpdate", nullptr, StartAutoUpdate, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"startLocalUpdate", nullptr, StartLocalUpdate, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"isAutoRun", nullptr, IsAutoRun, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setAutoRun", nullptr, SetAutoRun, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setScreenSaveActive", nullptr, ExportSetScreenSaveActive, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"startManaulUpdate", nullptr, StartManaulUpdate, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"shellExecute", nullptr, XlShellExecEx, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"associate", nullptr, Associate, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"addFloatWinodwShowRect", nullptr, AddFloatWinodwShowRect, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"delFloatWinodwShowRect", nullptr, DelFloatWinodwShowRect, nullptr, nullptr, nullptr, napi_default, nullptr},
    };
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
}
