<script setup lang="ts">
import { ref } from 'vue'
import { PopUpNS } from '@root/common/pop-up'
import { usePositionMixinComponent } from '@/common/mixins'
import * as PopUpTypes from '@root/common/pop-up/types'
import RadioGroup from '@root/common/components/ui/radio-group/index.vue'
import { ADVANCED_SETTING_NAME_MAP, getSettingConfig, setSettingConfig } from '@root/modal-renderer/src/views/setting'
import { DkHelper } from '@root/common/task/impl/dk-helper'
import * as BaseType from '@root/common/task/base'

export interface IProxyInfo {
  name: string,
  server: string,
  port: number,
  type: string,
  username: string,
  password: string,
}

// 使用基类的逻辑
const { overridePosition } = usePositionMixinComponent();

const props = defineProps<{
  options: {
    proxyInfo: IProxyInfo,
  }
}>()

const proxyInfo = ref<IProxyInfo>(props.options.proxyInfo || {
  name: '',
  server: '',
  port: 80,
  type: 'HTTP',
  username: '',
  password: '',
})

const testProxyButtonText = ref('测试')

// 重写控制位置基类非响应式数据
overridePosition({
  relatePos: PopUpTypes.RelatePosType.CenterParent,
  autoSize: true,
  show: true,
  selector: '.add-proxy-container',
})

const handleClose = async () => {
  console.log('AddProxy: 关闭按钮被点击')
  const currentWindow = PopUpNS.getCurrentWindow()
  currentWindow.close()
}

const handleTestProxy = async () => {
  testProxyButtonText.value = '测试中...'
  try {
    const result = await DkHelper.proxyVerify(proxyInfo.value.server, proxyInfo.value.port, proxyInfo.value.username, proxyInfo.value.password, proxyInfo.value.type === 'HTTP' ? BaseType.ProxyType.Http : BaseType.ProxyType.Sock5)
    console.log('AddProxy: 测试代理结果', result)

    if (result === BaseType.ProxyVerifyResult.Sucess) {
      testProxyButtonText.value = '测试成功'
    } else {
      testProxyButtonText.value = '测试失败'
    }
  } catch (e) {
    console.log('Test Proxy 失败', e)
  }
}

const handleConfirm = async () => {
  const values = Object.values(proxyInfo.value)
  const preValues = await getSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyList, []) as string[]

  if (props.options.proxyInfo) {
    // 编辑
    const preIndex = preValues.findIndex(item => {
      const [name] = item.split(';')
      if (name && proxyInfo.value.name) {
        return true
      } else {
        return false
      }
    })

    if (preIndex > -1) {
      preValues[preIndex] = values.join(';')
      setSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyList, preValues)
    }

  } else {
    // 增加
    setSettingConfig(ADVANCED_SETTING_NAME_MAP.ProxyList, [...preValues, values.join(';')])
  }
  handleClose()
}
</script>

<template>
  <div class="add-proxy-container">
    <div class="add-proxy-header draggable">
      <span>
        添加代理/编辑代理
      </span>
      <Button variant="ghost" is-icon @click="handleClose" class="none-draggable">
        <i class="xl-icon-general-close-m"></i>
      </Button>
    </div>
    <div class="add-proxy-body">
      <div class="add-proxy-body-item">
        <div class="add-proxy-body-item-label">
          代理名称
        </div>
        <xl-input v-model="proxyInfo.name" :style="{ flex: 1, height: '32px' }" />
      </div>
      <div class="add-proxy-body-item">
        <div class="add-proxy-body-item-label">服务器</div>
        <xl-input v-model="proxyInfo.server" :style="{ flex: 1, height: '32px' }" />
      </div>
      <div class="add-proxy-body-item">
        <div class="add-proxy-body-item-label">端口</div>
        <xl-input v-model="proxyInfo.port" :style="{ width: '100px', height: '32px' }" />
      </div>
      <div class="add-proxy-body-item">
        <div class="add-proxy-body-item-label">类型</div>
        <RadioGroup v-model="proxyInfo.type"
          :options="[{ label: 'HTTP', value: 'HTTP' }, { label: 'SOCKS5', value: 'SOCKS5' }]"
          orientation="horizontal" />
      </div>
      <div class="add-proxy-body-item">
        <div class="add-proxy-body-item-label">验证</div>
        <div class="add-proxy-body-item-label-content">
          <xl-input :style="{ width: '100px', height: '32px' }" v-model="proxyInfo.username" />
          <xl-input :style="{ width: '100px', height: '32px' }" v-model="proxyInfo.password" type="password"
            :show-password-icon="false" />
          <Button variant="outline" size="sm" @click="handleTestProxy">
            {{ testProxyButtonText }}
          </Button>
        </div>
      </div>
    </div>

    <div class="add-proxy-footer">
      <Button variant="secondary" @click="handleClose">取消</Button>
      <Button @click="handleConfirm">确认</Button>
    </div>

  </div>
</template>

<style scoped lang="scss">
.add-proxy {
  &-container {
    display: flex;
    width: 572px;

    flex-direction: column;
    align-items: flex-start;
    border-radius: var(--border-radius-L, 12px);
    background: var(--background-background-elevated, #FFF);
    box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 64px;
    padding: 24px;

    span {
      color: var(--font-font-1, #272E3B);
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
    }

    i {
      color: var(--font-font-3);
    }
  }

  &-body {
    padding: 10 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;

    &-item {
      display: flex;
      align-items: center;
      gap: 8px;

      &-label {
        width: 60px;
        color: var(--font-font-2, #4E5769);
        font-size: 13px;
        font-weight: 400;
        line-height: 22px;

        &-content {
          display: flex;
          align-items: center;
          gap: 10px;

          button {
            width: 100px;
          }
        }
      }
    }
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 24px;
    width: 100%;

    button {
      width: 84px;
    }
  }
}
</style>