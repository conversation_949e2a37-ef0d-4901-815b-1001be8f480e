import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import * as BaseType from '../base'
import { AplayerStack } from '../impl/aplayer-stack';

export class AplayerServerPlayList {
    static init() {
        AplayerStack.GetInstance().getPlayList().attachPlayListPreparedEvent(() => {
            console.log('server attachPlayListPreparedEvent');
            client.broadcastEvent('AplayerPlayListPrepared');
        });
        AplayerStack.GetInstance().getPlayList().attachPlayListSelectChangeEvent((id: string) => {
            client.broadcastEvent('AplayerPlayListSelectChange', id);
        });
        AplayerStack.GetInstance().getPlayList().attachPlayListItemAddEvent((b: boolean, item?: any) => {
            client.broadcastEvent('AplayerPlayListItemAdd', b, item);
        });
        AplayerStack.GetInstance().getPlayList().attachPlayListItemDeleteEvent((id: string) => {
            client.broadcastEvent('AplayerPlayListItemDelete', id);
        });
        client.registerFunctions({
            AplayerPlayListGetPlayList: (context: any) => {
                return AplayerStack.GetInstance().getPlayList().getPlayList();
            },
            AplayerPlayListDeletePlayListItem: (context: any, id: string) => {
                AplayerStack.GetInstance().getPlayList().deletePlayListItem(id);
            },
            AplayerPlayListPlayItem: (context: any, id: string) => {
                AplayerStack.GetInstance().getPlayList().playItem(id);
            },
            AplayerPlayListPlayNext: (context: any) => {
                AplayerStack.GetInstance().getPlayList().playNext();
            },
            AplayerPlayListPlayPrev: (context: any) => {
                AplayerStack.GetInstance().getPlayList().playPrev();
            },
            AplayerPlayListGetPlayingItemId: (context: any): string => {
                return AplayerStack.GetInstance().getPlayList().getPlayingItemId();
            },
            AplayerPlayListAddLocalItem: (context: any, name: string, filePath: string)=> {
                AplayerStack.GetInstance().getPlayList().addLocalItem(name, filePath);
            },
            AplayerPlayListClear: (context: any)=> {
                AplayerStack.GetInstance().getPlayList().clear();
            },
            AplayerPlayListGetItemMediaInfo: async (context: any, id: string): Promise<BaseType.PlayListItemMediaInfo> => {
                return new Promise((v) => {
                    AplayerStack.GetInstance().getPlayList().getItemMediaInfo(id, (info: BaseType.PlayListItemMediaInfo) => {
                        v(info);
                    });
                })
            },
            AplayerPlayListIsNextLocalPlay: async (context: any): Promise<boolean> => {
                return new Promise((v) => {
                    AplayerStack.GetInstance().getPlayList().isNextLocalPlay((b: boolean) => {
                        v(b);
                    });
                })
            },
            AplayerPlayListIsPrevLocalPlay: async (context: any): Promise<boolean> => {
                return new Promise((v) => {
                    AplayerStack.GetInstance().getPlayList().isPrevLocalPlay((b: boolean) => {
                        v(b);
                    });
                })
            },
            AplayerPlayListIsLocalPlay: async (context: any, id: string): Promise<boolean> => {
                return new Promise((v) => {
                    AplayerStack.GetInstance().getPlayList().isLocalPlay(id, (b: boolean) => {
                        v(b);
                    });
                })
            }
        });
    }
}