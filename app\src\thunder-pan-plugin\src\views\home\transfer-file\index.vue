<script lang="ts" setup>
import FileList from '@/components/file-list/index.vue'

import { computed, onMounted, ref, useTemplateRef, watch } from 'vue';
import { API_FILE } from '@root/common/thunder-pan-manager/pan-sdk/types';
import { IGetFileListOptions, TransferFileManager, TypeDriveSortInfo } from '@/manager/transfer-file-manager';
import { TPanRoute, useTransferRouterStore } from '@/store/transfer-router-store'
import { GlobalEventHelper } from '@/utils/global-event-helper';
import { ETabId } from '@/manager/tabs-manager';
import { EPanPage, useHistoryStore } from '@/store/history-store';
import { IPayloadData } from '@/manager/message-center-manager';

const currentFileData = TransferFileManager.getInstance().getCurrentData()
const { pushState, replaceState } = useHistoryStore()
const { routerList_computed, isInSafeBoxFolder, isInPrivilegeFolder, isInFavoriteFolder, currentParentFile, setRouterList } = useTransferRouterStore()

const isLoading = ref(false)
const FileListVm = useTemplateRef('FileListVm')

const currentFileList = computed(() => currentFileData.fileList)
const currentPickedIds = computed(() => currentFileData.pickedIds)
const currentSortInfo = computed(() => currentFileData.sortInfo)

function handleCleanPicked() {
  TransferFileManager.getInstance().cleanPicked()
}

function handleSetPickedIds (ids: string[]) {
  TransferFileManager.getInstance().setPickedIds(ids)
}

function handlePickedIdsChange(isSelectAll: boolean, canPickIds: string[]) {
  if (!isSelectAll) {
    handleCleanPicked()
  } else {
    handleSetPickedIds(canPickIds)
  }
}

async function handleHeaderSorterClick (sortInfo: TypeDriveSortInfo) {
  TransferFileManager.getInstance().updateSortInfoByKey('transfer-file_' + currentParentFile.value.id, sortInfo)

  isLoading.value = true
  await TransferFileManager.getInstance().getCurrentFileList(currentParentFile.value.id, { reset: true })
  isLoading.value = false

  handleUpdateFileListScroller()
}

function handleCheckChange (file: API_FILE.DriveFile, isForce: boolean = false) {
  TransferFileManager.getInstance().togglePickedId(file.id!, isForce)
}

function handleSetRouterList (newList: TPanRoute[]) {
  pushState({
    page: EPanPage.HOME,
    tab: ETabId.TRANSFER_FILE,
    routes: newList
  })

  setRouterList(newList)
}

async function handleListScrollEnd () {
  if (currentFileData.fileListPageToken) {
    await TransferFileManager.getInstance().getCurrentFileList(currentParentFile.value.id)
    handleUpdateFileListScroller()
  }
}

async function getCurrentFileList (options?: IGetFileListOptions) {
  isLoading.value = true
  await TransferFileManager.getInstance().getCurrentFileList(currentParentFile.value.id, { reset: true })
  isLoading.value = false
}

function handleUpdateFileListScroller() {
  if (FileListVm.value) {
    FileListVm.value.updateScroller()
  }
}

watch(routerList_computed, () => {
  handleCleanPicked()
  getCurrentFileList()
})

watch(currentFileList, () => {
  handleUpdateFileListScroller()
}, { deep: true })

onMounted(async () => {
  getCurrentFileList()
  // 全局事件监听
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.TRANSFER_LIST_REFRESH, () => {
    getCurrentFileList()
    handleCleanPicked()
    replaceState({
      page: EPanPage.HOME,
      tab: ETabId.TRANSFER_FILE,
      routes: routerList_computed.value as any
    })
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.TRANSFER_LIST_CLEAN_PICKED, () => {
    handleCleanPicked()
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_TRASH, (ids: string[]) => {
    TransferFileManager.getInstance().batchDeleteFile(ids)
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_DECOMPRESS, (parentId: string) => {
    if (currentParentFile.value.id === parentId) {
      getCurrentFileList()
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_UPLOADED, (parentId: string) => {
    if (currentParentFile.value.id === parentId) {
      getCurrentFileList()
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_COPY, (data: IPayloadData) => {
    if (currentParentFile.value.id === data.params.parentid) {
      getCurrentFileList()
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.MESSAGE_CENTER_RECV_MOVE, (data: IPayloadData) => {
    if (currentParentFile.value.id === data.params.parentid) {
      getCurrentFileList()
    }
  })
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.CLOUD_ADD_TASK_SUCCESS, (data: IPayloadData) => {
    if (currentParentFile.value.id === data.reference_resource?.parent_id) {
      getCurrentFileList()
    }
  })
})
</script>

<template>
  <div class="transfer-file-container">
    <FileList
      ref="FileListVm"
      :parent-from="ETabId.TRANSFER_FILE"
      :is-loading="isLoading"
      :parent-file="currentParentFile"
      :file-list="currentFileList"
      :picked-ids="currentPickedIds"
      :sort-info="currentSortInfo"
      :breadcrumb="routerList_computed"
      :is-in-favorite-folder="isInFavoriteFolder"
      :is-in-privilege-folder="isInPrivilegeFolder"
      :is-in-safe-box-folder="isInSafeBoxFolder"
      :show-belongs="!currentParentFile.id"
      @header-clean-picked="handleCleanPicked"
      @header-check-change="handlePickedIdsChange"
      @header-sorter-click="handleHeaderSorterClick"
      @set-picked-ids="handleSetPickedIds"
      @item-picked-change="handleCheckChange"
      @set-route-list="handleSetRouterList"
      @list-scroll-end="handleListScrollEnd"
    />
  </div>
</template>

<style lang="scss" scoped>
.transfer-file-container {
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
</style>
