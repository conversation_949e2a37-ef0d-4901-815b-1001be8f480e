import { type InjectionKey, inject, provide } from 'vue'

// 定义注入键
export const MAGNET_PARSER_KEY: InjectionKey<MagnetParserFunction> = Symbol('magnetParser')

// 定义磁力解析函数类型
export type MagnetParserFunction = (params: {
  url: string
  forceReparse?: boolean
}) => Promise<boolean>

/**
 * 提供磁力解析函数
 * @param parseMagnetAndDownloadTorrent 磁力解析函数
 */
export function provideMagnetParser(parseMagnetAndDownloadTorrent: MagnetParserFunction) {
  console.log('[provideMagnetParser] 提供磁力解析函数')
  provide(MAGNET_PARSER_KEY, parseMagnetAndDownloadTorrent)
}

/**
 * 直接注入磁力解析函数
 * @returns 磁力解析函数，如果未找到则返回 null
 */
export function injectMagnetParser(): MagnetParserFunction | null {
  const parseMagnetAndDownloadTorrent = inject<MagnetParserFunction | null>(MAGNET_PARSER_KEY, null)

  if (!parseMagnetAndDownloadTorrent) {
    console.warn(
      '[injectMagnetParser] 未找到磁力解析函数，请确保在父组件中调用了 provideMagnetParser'
    )
  }

  return parseMagnetAndDownloadTorrent
}
