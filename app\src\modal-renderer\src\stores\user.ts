import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { AccountHelper } from '@root/common/account/client/accountHelper'
import { AccountHelperEventKey } from '@root/common/account/account-type'
import { PopUpNS } from '@root/common/pop-up'

/**
 * User Store
 * 专门管理用户登录状态和相关信息
 * 
 * @example
 * ```typescript
 * // 在组件中使用
 * import { useUserStore } from '@root/modal-renderer/src/stores/user'
 * 
 * const userStore = useUserStore()
 * 
 * // 初始化用户状态（通常在 onMounted 中调用）
 * await userStore.initializeUserState()
 * 
 * // 获取当前登录状态
 * const isLoggedIn = await userStore.getLoginStatus()
 * 
 * // 监听登录状态变化
 * watch(() => userStore.isLogged, (newStatus) => {
 *   console.log('登录状态变化:', newStatus)
 * })
 * 
 * // 用户登录成功后调用
 * userStore.onUserSignIn()
 * 
 * // 用户登出后调用
 * userStore.onUserSignOut()
 * ```
 */
export const useUserStore = defineStore('user', () => {
  // ===== 状态管理 =====

  /**
   * 用户是否已登录
   * @description 通过 AccountHelper.isSignIn() 获取的登录状态
   */
  const isLogged = ref<boolean>(false)

  /**
   * 是否正在检查登录状态
   * @description 防止重复调用检查函数
   */
  const isCheckingLoginStatus = ref<boolean>(false)

  /**
   * 最后一次检查登录状态的时间
   * @description 用于判断是否需要重新检查状态
   */
  const lastCheckTime = ref<number>(0)

  /**
   * 登录状态检查间隔（毫秒）
   * @description 默认5分钟检查一次，避免频繁调用
   */
  const CHECK_INTERVAL = 5 * 60 * 1000

  // 保存事件监听器引用，用于后续清理
  const signOutListener = ref<(() => void) | null>(null)
  const signInSuccessListener = ref<(() => void) | null>(null)
  const userInfoChangeListener = ref<((userInfo: any) => void) | null>(null)

  // ===== 计算属性 =====

  /**
   * 是否需要重新检查登录状态
   * @description 基于最后检查时间和检查间隔判断
   */
  const needsRefresh = computed(() => {
    const now = Date.now()
    return now - lastCheckTime.value > CHECK_INTERVAL
  })

  // ===== Actions =====

  /**
   * 检查用户登录状态
   * @description 调用 AccountHelper.isSignIn() 获取最新状态
   * @returns Promise<boolean> 返回用户登录状态
   */
  const checkLoginStatus = async (): Promise<boolean> => {
    if (isCheckingLoginStatus.value) {
      // 如果正在检查中，返回当前状态
      return isLogged.value
    }

    try {
      isCheckingLoginStatus.value = true
      console.log('[UserStore] 开始检查用户登录状态...')

      const accountHelper = AccountHelper.getInstance()
      const loginStatus = await accountHelper.isSignIn()

      isLogged.value = loginStatus
      lastCheckTime.value = Date.now()

      console.log('[UserStore] 用户登录状态:', loginStatus)
      return loginStatus
    } catch (error) {
      console.error('[UserStore] 检查登录状态失败:', error)
      // 出错时保持当前状态不变
      return isLogged.value
    } finally {
      isCheckingLoginStatus.value = false
    }
  }

  /**
   * 强制刷新登录状态
   * @description 忽略检查间隔，立即获取最新状态
   * @returns Promise<boolean> 返回最新的用户登录状态
   */
  const forceRefreshLoginStatus = async (): Promise<boolean> => {
    console.log('[UserStore] 强制刷新用户登录状态')
    lastCheckTime.value = 0 // 重置检查时间，强制刷新
    return await checkLoginStatus()
  }

  /**
   * 获取当前登录状态
   * @description 如果需要刷新或从未检查过，则先检查状态
   * @returns Promise<boolean> 返回用户登录状态
   */
  const getLoginStatus = async (): Promise<boolean> => {
    if (needsRefresh.value || lastCheckTime.value === 0) {
      return await checkLoginStatus()
    }
    return isLogged.value
  }

  /**
   * 设置登录状态
   * @description 用于在登录/登出操作后手动更新状态
   * @param status - 登录状态
   */
  const setLoginStatus = (status: boolean): void => {
    console.log('[UserStore] 手动设置登录状态:', status)
    isLogged.value = status
    lastCheckTime.value = Date.now()
  }

  /**
   * 用户登录成功后调用
   * @description 更新登录状态为 true
   */
  const onUserSignIn = (): void => {
    console.log('[UserStore] 用户登录成功')
    setLoginStatus(true)
  }

  /**
   * 用户登出后调用
   * @description 更新登录状态为 false
   */
  const onUserSignOut = (): void => {
    console.log('[UserStore] 用户登出')
    setLoginStatus(false)
  }

  /**
   * 设置用户相关事件监听
   * @description 监听用户登录、退出等事件
   */
  const setupUserEventListeners = (): void => {
    const accountHelper = AccountHelper.getInstance()
    
    // 创建监听器函数
    const signOutHandler = () => {
      console.log('[UserStore] 检测到用户退出事件')
      onUserSignOut()
      // 在这里可以添加关闭所有弹窗的逻辑
      closeAllDialogs()
    }
    
    const signInSuccessHandler = () => {
      console.log('[UserStore] 检测到用户登录成功事件')
      onUserSignIn()
    }
    
    const userInfoChangeHandler = (userInfo: any) => {
      console.log('[UserStore] 检测到用户信息变化事件', userInfo)
      // 可以在这里更新用户信息
    }
    
    // 保存监听器引用
    signOutListener.value = signOutHandler
    signInSuccessListener.value = signInSuccessHandler
    userInfoChangeListener.value = userInfoChangeHandler
    
    // 监听用户退出事件
    accountHelper.attachEvent(AccountHelperEventKey.SIGN_OUT, signOutHandler)
    
    // 监听用户登录成功事件
    accountHelper.attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, signInSuccessHandler)
    
    // 监听用户信息变化事件
    accountHelper.attachEvent(AccountHelperEventKey.USER_INFO_CHANGE, userInfoChangeHandler)
    
    console.log('[UserStore] 用户事件监听器设置完成')
  }

  /**
   * 关闭所有相关弹窗
   * @description 用户退出时关闭所有弹窗
   */
  const closeAllDialogs = (): void => {
    console.log('[UserStore] 用户退出，关闭所有弹窗')
    
    try {
      // 获取当前窗口并关闭
      const currentWindow = PopUpNS.getCurrentWindow()
      if (currentWindow) {
        currentWindow.close().catch((error) => {
          console.warn('[UserStore] 关闭当前窗口失败:', error)
        })
      }
      
      // 可以在这里添加关闭其他特定弹窗的逻辑
      // 例如：关闭登录弹窗、设置弹窗等
      
      console.log('[UserStore] 所有弹窗关闭完成')
    } catch (error) {
      console.error('[UserStore] 关闭弹窗时出错:', error)
    }
  }

  /**
   * 清理事件监听器
   * @description 组件卸载时调用，清理事件监听器
   */
  const cleanupEventListeners = (): void => {
    const accountHelper = AccountHelper.getInstance()
    
    // 移除所有事件监听器，使用保存的监听器引用
    if (signOutListener.value) {
      accountHelper.detachEvent(AccountHelperEventKey.SIGN_OUT, signOutListener.value)
      signOutListener.value = null
    }
    if (signInSuccessListener.value) {
      accountHelper.detachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, signInSuccessListener.value)
      signInSuccessListener.value = null
    }
    if (userInfoChangeListener.value) {
      accountHelper.detachEvent(AccountHelperEventKey.USER_INFO_CHANGE, userInfoChangeListener.value)
      userInfoChangeListener.value = null
    }
    
    console.log('[UserStore] 事件监听器已清理')
  }

  /**
   * 初始化用户状态
   * @description 组件挂载时调用，获取初始登录状态并设置事件监听
   * @returns Promise<void>
   */
  const initializeUserState = async (): Promise<void> => {
    console.log('[UserStore] 初始化用户状态...')
    
    // 等待 SDK 初始化完成
    const accountHelper = AccountHelper.getInstance()
    await accountHelper.whenReady()
    console.log('[UserStore] SDK 初始化完成')
    
    await checkLoginStatus()
    
    // 设置用户退出事件监听
    setupUserEventListeners()
  }

  /**
   * 重置 Store 状态
   * @description 清空所有状态，恢复到初始值
   */
  const resetStore = (): void => {
    isLogged.value = false
    isCheckingLoginStatus.value = false
    lastCheckTime.value = 0
    console.log('[UserStore] Store 状态已重置')
  }

  // ===== 导出 =====
  return {
    // 状态
    isLogged,
    isCheckingLoginStatus,
    lastCheckTime,

    // 计算属性
    needsRefresh,

    // 方法
    checkLoginStatus,
    forceRefreshLoginStatus,
    getLoginStatus,
    setLoginStatus,
    onUserSignIn,
    onUserSignOut,
    setupUserEventListeners,
    closeAllDialogs,
    cleanupEventListeners,
    initializeUserState,
    resetStore,
  }
}) 