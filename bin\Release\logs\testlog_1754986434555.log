[2025-08-12 16:13:54.555][26148:16380][error][xl::dk::CommonThunderStorage::Init]: CommonThunderStorage, begin to open taskdb, path=C:\xunlei\project\thunder_2025\bin\profiles\NewTaskDb.dat (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:30)
[2025-08-12 16:13:54.558][26148:16380][info][xl::dk::DkProxyImpl::Init]: XDLInterface::Load nRet=0, appkey=xzcGMuWE1QX1A7MA^^SDK==26bfbf7a346bcf2ac776d5b7e1cb1c66 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:30)
[2025-08-12 16:13:54.558][26148:7672][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE TaskBase(TaskId BIGINT PRIMARY KEY,Type INT,Status INT,StatusChangeTime BIGINT,SavePath NVARCHAR,TotalReceiveSize BIGINT,TotalSendSize BIGINT,TotalReceiveValidSize BIGINT,TotalUploadSize BIGINT,CreationTime BIGINT,FileCreated INT,CompletionTime BIGINT,DownloadingPeriod BIGINT,RemovingToRecycleTime BIGINT,FailureErrorCode INT,Url NVARCHAR,ReferenceUrl NVARCHAR,ResourceSize BIGINT,Name NVARCHAR,Cid NVARCHAR,Gcid NVARCHAR,Description NVARCHAR,CategoryId INT,ResourceQueryCid NVARCHAR,CreationRequestType INT,StartMode INT,NamingType INT,StatisticsReferenceUrl NVARCHAR,UserRead INT,FileSafetyFlag INT,Playable INT,BlockInfo BLOB,OpenOnComplete INT,SpecialType INT,Proxy BLOB,OriginReceiveSize BIGINT,P2pReceiveSize BIGINT,P2sReceiveSize BIGINT,OfflineReceiveSize BIGINT,VipReceiveSize BIGINT,VipResourceEnableNecessary INT,ConsumedVipSize BIGINT,Forbidden INT,OptionalChannelDataSize BLOB,OwnerProductId INT,UserData Text,UrlCodePage INT,ReferenceUrlCodePage INT,StatisticsReferenceUrlCodePage INT,GroupTaskId BIGINT,DownloadSubTask INT,TagValue INT,InnerNatReceiveSize BIGINT,AdditionFlag INT,ProductInfo NVARCHAR,Origin NVARCHAR,FreeDcdnReceiveSize BIGINT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 16:13:54.558][26148:7672][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE Category(CategoryId INT PRIMARY KEY,Name NVARCHAR,Description NVARCHAR,UserName NVARCHAR,Password NVARCHAR) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 16:13:54.558][26148:7672][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE P2spTask(TaskId BIGINT PRIMARY KEY,Cookie NVARCHAR,UseOriginResourceOnly INT,OriginResourceThreadCount INT,FileNameFixed INT,DisplayUrl NVARCHAR,UserAgent NVARCHAR) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 16:13:54.558][26148:7672][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE BtTask(TaskId BIGINT PRIMARY KEY,SeedFile NVARCHAR,InfoId NVARCHAR,DownloadFileOrderData BLOB,Tracker NVARCHAR,DisplayName NVARCHAR) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 16:13:54.558][26148:7672][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE BtFile(BtFileId INTEGER PRIMARY KEY,BtTaskId BIGINT,Status INT,FileIndex INT,Visible INT,Download INT,FilePath NVARCHAR,FileName NVARCHAR,FileSize BIGINT,FileOffset BIGINT,ReceivedSize BIGINT,Cid NVARCHAR,Gcid NVARCHAR,OptionalChannelDataSize BLOB,VideoHeadFirstTime INT,VideoHeadFirstStatus INT,FailureErrorCode INT,UserRead INT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 16:13:54.558][26148:7672][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=ALTER TABLE BtFile ADD COLUMN UserRead INT DEFAULT 0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 16:13:54.558][26148:7672][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE EmuleTask(TaskId BIGINT PRIMARY KEY,FileHash NVARCHAR,ConfigureFilePath NVARCHAR,VideoHeadFirstTime INT,VideoHeadFirstStatus INT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 16:13:54.558][26148:7672][info][xl::dk::CommonThunderStorage::ExecuteImpl]: CommonThunderStorage, Execute, nRet=1, sql=CREATE TABLE GroupTask(TaskId BIGINT PRIMARY KEY,SubTaskOrder BLOB,DownloadInOrder INT,DownloadingSubTaskCount INT,SmartMoveTimeout INT) (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\db\CommonStorage.cpp:170)
[2025-08-12 16:13:54.694][26148:16380][info][xl::dk::DkProxyImpl::Init]: XL_Init nRet=0, appkey=xzcGMuWE1QX1A7MA^^SDK==26bfbf7a346bcf2ac776d5b7e1cb1c66 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:40)
[2025-08-12 16:13:54.694][26148:16380][info][xl::dk::CategoryManager::Load]: CategoryManager::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\CategoryManager.cpp:10)
[2025-08-12 16:13:54.902][26148:16380][info][xl::dk::TaskManager::LoadTask]: TaskManager begin load taskbase from storage (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:126)
[2025-08-12 16:13:54.906][26148:16380][info][xl::dk::TaskManager::LoadTask]: TaskManager end load taskbase from storage (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:129)
[2025-08-12 16:13:54.910][26148:16380][info][xl::dk::BtTask::Load]: BtTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:103)
[2025-08-12 16:13:54.911][26148:16380][info][xl::dk::BtTask::Load::<lambda_1>::operator ()]: BtTask::Load LoadBtFile before switch to storage thread (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:115)
[2025-08-12 16:13:54.911][26148:16380][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 16:13:54.912][26148:16380][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 16:13:54.912][26148:16380][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 16:13:54.912][26148:16380][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 16:13:54.912][26148:16380][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 16:13:54.913][26148:16380][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 16:13:54.913][26148:16380][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 16:13:54.913][26148:16380][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 16:13:54.913][26148:16380][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 16:13:54.913][26148:16380][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 16:13:54.913][26148:16380][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 16:13:54.914][26148:16380][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 16:13:54.914][26148:16380][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 16:13:54.914][26148:16380][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 16:13:54.915][26148:16380][info][xl::dk::BtTask::Load]: BtTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:103)
[2025-08-12 16:13:54.915][26148:16380][info][xl::dk::BtTask::Load::<lambda_1>::operator ()]: BtTask::Load LoadBtFile before switch to storage thread (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:115)
[2025-08-12 16:13:54.915][26148:16380][info][xl::dk::BtTask::Load]: BtTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:103)
[2025-08-12 16:13:54.915][26148:16380][info][xl::dk::BtTask::Load::<lambda_1>::operator ()]: BtTask::Load LoadBtFile before switch to storage thread (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\BtTask.cpp:115)
[2025-08-12 16:13:54.915][26148:16380][info][xl::dk::P2spTask::Load]: P2spTask::Load (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:82)
[2025-08-12 16:13:54.916][26148:16380][info][xl::dk::P2spTask::Load]: finish (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\P2spTask.cpp:96)
[2025-08-12 16:13:54.916][26148:16380][info][xl::dk::TaskManager::LoadTask]: load finish, task count=12 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:231)
[2025-08-12 16:13:55.917][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:13:56.833][26148:16380][info][xl::dk::DkProxyImpl::SetCacheSize]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:1561)
[2025-08-12 16:13:56.833][26148:16380][info][xl::dk::DownloadDispatcher::SetMaxDownloadTask]: SetMaxDownloadTask nMax=5 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\DownloadDispatcher.cpp:174)
[2025-08-12 16:13:56.833][26148:16380][info][xl::dk::DkProxyImpl::UpdateNetDiscVODCachePath]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:1543)
[2025-08-12 16:13:56.834][26148:16380][info][xl::dk::DkProxyImpl::SetGlobalConnectionLimit]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:303)
[2025-08-12 16:13:56.834][26148:16380][info][xl::dk::DkProxyImpl::SetDownloadSpeedLimit]: enter nKBps=4294967295 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:142)
[2025-08-12 16:13:56.834][26148:16380][info][xl::dk::DkProxyImpl::SetDownloadSpeedLimit]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:144)
[2025-08-12 16:13:56.834][26148:16380][info][xl::dk::DkProxyImpl::SetUploadSpeedLimit]: enter nKBps=4294967295 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:192)
[2025-08-12 16:13:56.834][26148:16380][info][xl::dk::DkProxyImpl::SetUploadSpeedLimit]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:194)
[2025-08-12 16:13:56.919][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:13:57.920][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:13:58.920][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:13:59.920][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:00.920][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:01.921][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:02.921][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:03.936][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:04.939][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:05.947][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:06.952][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:07.963][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:08.977][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:09.987][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:10.995][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:12.005][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:13.018][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:14.026][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:15.026][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:15.981][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:937)
[2025-08-12 16:14:15.981][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498.torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498.torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.981][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(1).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(1).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.982][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(2).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(2).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.982][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(3).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(3).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.982][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(4).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(4).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.983][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(5).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(5).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.983][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(6).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(6).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.984][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(7).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(7).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.984][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(8).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(8).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.984][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(9).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(9).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.984][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(10).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(10).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.985][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(11).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(11).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.985][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(12).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(12).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.985][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(13).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(13).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.985][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(14).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(14).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.986][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(15).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(15).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.986][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(16).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(16).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.986][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(17).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(17).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.986][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(18).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(18).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.986][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(19).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(19).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.987][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(20).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(20).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.987][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(21).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(21).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.987][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(22).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(22).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.987][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(23).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(23).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.988][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(24).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(24).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.988][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(25).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(25).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.988][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(26).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(26).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.988][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(27).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(27).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.988][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(28).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(28).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.989][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(29).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(29).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.989][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(30).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(30).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.989][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(31).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(31).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.989][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(32).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(32).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.989][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(33).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(33).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.989][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(34).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(34).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.990][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(35).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(35).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.990][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(36).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(36).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.990][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(37).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(37).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.990][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(38).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(38).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.990][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(39).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(39).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.990][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(40).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(40).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.990][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(41).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(41).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.990][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(42).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(42).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.991][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(43).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(43).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.991][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(44).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(44).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.991][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(45).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(45).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.991][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(46).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(46).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.991][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(47).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(47).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.991][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(48).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(48).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.991][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(49).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(49).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.991][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(50).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(50).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(51).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(51).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(52).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(52).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(53).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(53).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(54).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(54).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(55).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(55).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(56).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(56).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(57).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(57).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(58).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(58).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(59).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(59).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.992][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(60).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(60).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(61).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(61).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(62).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(62).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(63).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(63).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(64).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(64).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(65).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(65).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(66).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(66).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(67).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(67).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(68).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(68).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(69).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(69).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(70).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(70).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(71).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(71).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.993][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(72).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(72).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.994][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(73).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(73).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.994][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(74).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(74).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.994][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(75).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(75).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.994][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(76).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(76).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.994][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(77).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(77).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.994][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(78).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(78).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(79).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(79).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(80).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(80).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(81).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(81).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(82).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(82).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(83).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(83).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(84).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(84).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(85).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(85).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(86).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(86).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(87).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(87).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(88).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(88).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(89).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(89).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.995][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(90).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(90).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.996][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(91).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(91).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.996][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(92).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(92).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.996][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(93).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(93).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.996][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(94).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(94).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.996][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(95).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(95).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.996][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(96).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(96).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.996][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(97).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(97).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.996][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(98).torrent, p1Exist=1, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(98).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.996][26148:16380][error][xl::dk::Task::CheckRepeatTaskName]: CheckRepeatTaskName, p1=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(99).torrent, p1Exist=0, p2=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(99).torrent.xltd.cfg, p2Exist=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:947)
[2025-08-12 16:14:15.998][26148:16380][info][xl::dk::MagnetTask::CreateDkTask]: begin to create task, url=magnet:?xt=urn:btih:5EAC600C2133FF4B0A3390DDA77B9AE7440B7498&size=55073188451&biz=btbird221101.link, savePath=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents, taskName=5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(99).torrent (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\MagnetTask.cpp:54)
[2025-08-12 16:14:15.998][26148:16380][info][xl::dk::DkProxyImpl::CreateMagnetTask]: enter strUrl=magnet:?xt=urn:btih:5EAC600C2133FF4B0A3390DDA77B9AE7440B7498&size=55073188451&biz=btbird221101.link, strPath=C:\Users\<USER>\AppData\Local\Temp\Thunder Network\Xmp6\xunlei\torrents\5EAC600C2133FF4B0A3390DDA77B9AE7440B7498(99).torrent (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:548)
[2025-08-12 16:14:15.999][26148:16380][info][xl::dk::DkProxyImpl::CreateMagnetTask]: nRet=0, dktaskid=1 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:559)
[2025-08-12 16:14:15.999][26148:16380][info][xl::dk::Task::AddTaskInfo]: begin to set stat to task, id=1 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:523)
[2025-08-12 16:14:15.999][26148:16380][info][xl::dk::DkProxyImpl::SetTaskExtInfo]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:1229)
[2025-08-12 16:14:15.999][26148:16380][info][xl::dk::DkProxyImpl::SetTaskStrategy]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:1301)
[2025-08-12 16:14:15.999][26148:16380][info][xl::dk::DkProxyImpl::StartTask]: enter dktaskid=1 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:579)
[2025-08-12 16:14:15.999][26148:16380][info][xl::dk::DkProxyImpl::StartTask]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:581)
[2025-08-12 16:14:16.027][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:16.027][26148:16380][info][xl::dk::DkProxyImpl::QueryP2spTaskInfo]: enter dktaskid=1 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:670)
[2025-08-12 16:14:16.027][26148:16380][info][xl::dk::DkProxyImpl::QueryP2spTaskInfo]: XL_QueryTaskInfo dktaskid=1, nRet=0,downloadsize=0, filesize=916, status=3 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:676)
[2025-08-12 16:14:16.027][26148:16380][warning][xl::dk::DkProxyImpl::QueryP2spTaskInfo]: XL_QueryTaskIndex nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:681)
[2025-08-12 16:14:16.027][26148:16380][info][xl::dk::Task::CalcDownloadSpeed]: CalcDownloadSpeed nDownloadSize=0, pre=0, calc speed=false, taskId=103587283 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:818)
[2025-08-12 16:14:16.027][26148:16380][info][xl::dk::Task::CalcDownloadSpeed]: CalcDownloadSpeed nDownloadSize=0, pre=0, speed=0, taskId=103587283 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:833)
[2025-08-12 16:14:17.038][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:17.038][26148:16380][info][xl::dk::DkProxyImpl::QueryP2spTaskInfo]: enter dktaskid=1 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:670)
[2025-08-12 16:14:17.038][26148:16380][info][xl::dk::DkProxyImpl::QueryP2spTaskInfo]: XL_QueryTaskInfo dktaskid=1, nRet=0,downloadsize=0, filesize=916, status=7 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:676)
[2025-08-12 16:14:17.038][26148:16380][warning][xl::dk::DkProxyImpl::QueryP2spTaskInfo]: XL_QueryTaskIndex nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:681)
[2025-08-12 16:14:17.038][26148:16380][info][xl::dk::Task::CalcDownloadSpeed]: CalcDownloadSpeed nDownloadSize=0, pre=0, calc speed=false, taskId=103587283 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:818)
[2025-08-12 16:14:17.038][26148:16380][info][xl::dk::Task::CalcDownloadSpeed]: CalcDownloadSpeed nDownloadSize=0, pre=0, speed=0, taskId=103587283 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:833)
[2025-08-12 16:14:17.039][26148:16380][info][xl::dk::Task::OnTaskStatusChange::<lambda_1>::operator ()]: DeleteTask taskId=103587283, virtualTaskId=1, status=8, errcode=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:878)
[2025-08-12 16:14:17.039][26148:16380][info][xl::dk::DkProxyImpl::DeleteTask]: enter dktaskid=1 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:629)
[2025-08-12 16:14:17.039][26148:16380][info][xl::dk::DkProxyImpl::DeleteTask]: nRet=0 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\dk\DkProxyImpl.cpp:631)
[2025-08-12 16:14:17.043][26148:16380][info][xl::dk::Task::DisableDcdnWithVipCert]: DisableDcdnWithVipCert, taskId=103587283, index=-1 (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\task\Task.cpp:148)
[2025-08-12 16:14:18.050][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:19.057][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:20.065][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:21.076][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:22.088][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:23.090][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:24.095][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:25.109][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:26.115][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:27.120][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:28.120][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:29.127][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:30.130][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:31.135][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:32.139][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:33.153][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:34.167][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:35.166][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:36.221][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:37.227][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:38.227][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:39.239][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:40.246][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:41.250][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:42.255][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:43.270][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:44.285][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:45.293][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:46.304][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:47.319][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:48.333][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:49.333][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:50.339][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:51.348][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:52.358][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:53.365][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:54.380][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:55.388][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:56.398][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:57.407][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:58.419][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:14:59.431][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:00.431][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:01.444][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:02.459][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:03.469][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:04.481][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:05.491][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:06.501][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:07.507][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:08.517][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:09.526][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:10.538][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:11.550][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:12.566][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:13.574][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:14.577][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:15.590][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:16.590][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:17.605][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:18.609][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:19.613][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:20.628][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:21.641][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:22.650][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:23.665][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:24.666][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:25.672][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:26.675][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:27.679][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:28.689][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
[2025-08-12 16:15:29.703][26148:16380][info][xl::dk::TaskManager::RunQueryLoop]: ================= (D:\work\xl\thunder_2025\cppsrc\native_core\src\main\cpp\download_kernel\src\TaskManager.cpp:625)
