import { TPrivilegeString } from "../services/drive";
import { API_DECOMPRESS } from "./api/API_DECOMPRESS";
import { API_EVENT } from "./api/API_EVENT";
import { API_FILE } from "./api/API_FILE";
import { API_PASSWORD } from "./api/API_PASSWORD";
import { API_SHARE } from "./api/API_SHARE";
import { API_TASK } from "./api/API_TASK";
import { HTTP_COMMON } from './http';
import * as User from './user';

import DriveApis from '../services/drive';
import FileApis from '../services/file';
import SafeApis from '../services/safe';
import ShareApis from '../services/share';
import UnzipApis from '../services/unzip';

export {
  API_FILE,
  API_DECOMPRESS,
  API_EVENT,
  API_PASSWORD,
  API_SHARE,
  API_TASK,
  HTTP_COMMON,
  User,
}

export interface ReferenceResource {
  ['@type']: string
  audit: API_FILE.DriveAudit
  hash: string
  icon_link: string
  id: string
  kind: string
  medias: Array<any>
  mime_type: string
  name: string
  params: Dictionary
  parent_id: string
  phase:string
  size: string
  space: string
  starred: boolean
  tags: Array<{
    id: string
    name: string
    type: number
  }>
  thumbnail_link: string
}

export type Dictionary<T = any> = Record<string | number, T>;

export interface IExtendDriveFile extends API_FILE.DriveFile {
  __order__?: number
  __mode__?: string
  __ext__?: any
}

export interface IPrivilegeData {
  user: number | string
  platinum: number | string
  superV: number | string
  superVYear: number | string
  [key: string]: number | string
}
export type TPrivilege = {
  [key in TPrivilegeString]: IPrivilegeData | null
};

export interface IResponseError {
  error: string
  error_url: string
  error_code: number
  error_description: string
  error_details: any[]
}

export interface IResponseFail {
  success: boolean
  error: IResponseError
  originResponse: any
}

export interface IRequestCommonResponse {
    success: boolean;
    data?: Object | string | ArrayBuffer;
    error?: Object | string;
    requestPromiseError?: boolean;
    originResponse?: any;
}
export interface IRequestCommonResponseT<T> {
    success: boolean;
    data?: T;
    error?: Object | string;
    requestPromiseError?: boolean;
    originResponse?: any;
}
export interface IRequestCommonOptions {
    header?: Record<string, any>;
    params?: Record<string, any>;
    data?: Record<string, any>;
}

/**
 * 基于本类型声明实现网络请求实例
 * 所以 Service 接口都是基于此发送请求
 */
export interface IRequestClassImplement {
    get: (url: string, options: IRequestCommonOptions) => Promise<IRequestCommonResponse>;
    post: (url: string, options: IRequestCommonOptions) => Promise<IRequestCommonResponse>;
    delete: (url: string, options: IRequestCommonOptions) => Promise<IRequestCommonResponse>;
    patch: (url: string, options: IRequestCommonOptions) => Promise<IRequestCommonResponse>;
    useRequest: (url: string, options: HttpRequestOptions) => Promise<IRequestCommonResponse>;
}
export type TEnv = 'test' | 'prod';
export interface IRequestCommonConfig {
    env?: TEnv;
    headers?: IRequestHeader;
}
export interface IRequestHeader {
    "x-captcha-token"?: string;
    "x-client-plugin-version"?: string;
    'Authorization'?: string;
    "x-peer-id"?: string;
    "x-client-version-code"?: string;
    "x-client-id"?: string;
    "x-device-id"?: string;
    "x-request-id"?: string;
    'Cookie'?: string;
    'content-type'?: HTTP_COMMON.ContentType;
    [key: string]: string | string[] | number | boolean | null | undefined;
}
export declare class PanSDKManager {
    config: IRequestCommonConfig;
    requestFn: IRequestClassImplement;
    constructor(requestFn: IRequestClassImplement, config: IRequestCommonConfig);
    getDriveApisForServices(config?: IRequestCommonConfig): DriveApis;
    getFileApisForServices(config?: IRequestCommonConfig): FileApis;
    getShareApisForServices(config?: IRequestCommonConfig): ShareApis;
    getSafeApisForServices(config?: IRequestCommonConfig): SafeApis;
    getUnzipApisForServices(config?: IRequestCommonConfig): UnzipApis;
}
