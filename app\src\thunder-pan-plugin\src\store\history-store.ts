import { ETabId, TabsManager } from '@/manager/tabs-manager'
import { createGlobalState } from '@vueuse/core'
import { computed, ref, unref } from 'vue'
import { TPanRoute, useDriveRouterStore } from './drive-router-store'
import { useCloudAddRouterStore } from './cloud-add-router-store'
import { useTransferRouterStore } from './transfer-router-store'

export enum EPanPage {
  HOME = 'HOME',
  SHARE = 'SHARE',
}

export interface IHistoryState {
  page: EPanPage
  tab?: ETabId
  routes?: TPanRoute[]
}

export const useHistoryStore = createGlobalState(() => {
  function getRootState () {
    return {
      tab: ETabId.ALL,
      page: EPanPage.HOME,
      routes: [{
        id: '',
        title: '全部文件',
        writable: true,
      }]
    }
  }

  const history = ref<IHistoryState[]>([
    getRootState()
  ])
  const currentIndex = ref(0)

  const canGoBack = computed(() => currentIndex.value > 0)
  const canGoForward = computed(() => currentIndex.value < history.value.length - 1)
  const currentHistoryState = computed(() => history.value[currentIndex.value])
  const currentHistoryPage = computed(() => history.value[currentIndex.value].page)

  function pushState(state: IHistoryState) {
    // 如果当前位置不是最后，则截断后面的历史记录
    if (currentIndex.value < history.value.length - 1) {
      history.value = history.value.slice(0, currentIndex.value + 1)
    }

    history.value.push(state)
    currentIndex.value = history.value.length - 1
  }

  function replaceState(state: IHistoryState) {
    if (history.value.length === 0) {
      pushState(state)
    } else {
      history.value[currentIndex.value] = state
    }
  }

  function goBack() {
    if (canGoBack.value) {
      currentIndex.value--
      triggerViewChange()
      return history.value[currentIndex.value]
    }
    return null
  }

  function goForward() {
    if (canGoForward.value) {
      currentIndex.value++
      triggerViewChange()
      return history.value[currentIndex.value]
    }
    return null
  }

  function go(n) {
    const newIndex = currentIndex.value + n
    if (newIndex >= 0 && newIndex < history.value.length) {
      currentIndex.value = newIndex
      triggerViewChange()
      return history.value[currentIndex.value]
    }
    return null
  }

  function clear() {
    history.value = [ getRootState() ]
    currentIndex.value = 0
    triggerViewChange()
  }

  function triggerViewChange () {
    const state = unref(currentHistoryState)
    // 首页处理
    if (state.page === EPanPage.HOME) {
      // 切换 tab
      if (state.tab) {
        TabsManager.getInstance().setCurrentTab(state.tab)
      }
      // 切换对应 tab 的路由
      if (state.routes && state.routes.length) {
        const newParentFile = state.routes.slice(-1)[0]

        switch (state.tab) {
          case ETabId.ALL: {
            const { setRouterList, currentParentFile } = useDriveRouterStore()
            const oldParentFile = currentParentFile.value
            // 有变更才更新路由
            if (newParentFile.id !== oldParentFile.id) {
              setRouterList(state.routes)
            }
            break
          }
          case ETabId.CLOUD_ADD: {
            const { setRouterList, currentParentFile } = useCloudAddRouterStore()
            const oldParentFile = currentParentFile.value
            // 有变更才更新路由
            if (newParentFile.id !== oldParentFile.id) {
              setRouterList(state.routes)
            }
            break
          }
          case ETabId.TRANSFER_FILE: {
            const { setRouterList, currentParentFile } = useTransferRouterStore()
            const oldParentFile = currentParentFile.value
            // 有变更才更新路由
            if (newParentFile.id !== oldParentFile.id) {
              setRouterList(state.routes)
            }
            break
          }
        }
      }
    }
  }

  return {
    history,
    currentIndex,
    currentHistoryState,
    currentHistoryPage,
    canGoBack,
    canGoForward,
    pushState,
    replaceState,
    goBack,
    goForward,
    go,
    clear,
  }
})
