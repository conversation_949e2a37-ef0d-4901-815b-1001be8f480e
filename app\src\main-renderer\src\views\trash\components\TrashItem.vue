<script setup lang="ts">
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import ContextMenu, { MenuItem } from '@root/common/components/ui/context-menu/index.vue'
import { API_FILE } from "@root/common/thunder-pan-manager/pan-sdk/types"
import { ThunderUtil } from '@root/common/utils'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
dayjs.extend(relativeTime)

const props = defineProps<{
  type: string
  contextMenu: MenuItem[]
  itemData: API_FILE.DriveFile
  selected: boolean
}>()

const emit = defineEmits<{
  (e: 'operation', action: string): void
  (e: 'doubleClick'): void
}>()

const handleEmitOperation = (action: string) => {
  emit('operation', action)
}

const handleItemClick = () => {
  handleEmitOperation('select')
}

const handleItemDoubleClick = () => {
  emit('doubleClick')
}

const handleItemDeleteOperation = () => {
  if (props.type === 'download') {
    handleEmitOperation('deleteDownload')
  }

  if (props.type === 'cloud') {
    handleEmitOperation('deleteCloud')
  }
}

const handleItemResetOperation = () => {
  if (props.type === 'download') {
    handleEmitOperation('resetDownload')
  }

  if (props.type === 'cloud') {
    handleEmitOperation('resetCloud')
  }
}


const handleMenuSelect = (key: string) => {
  handleEmitOperation(key)
}

const formatDeleteTime = (deleteTime: string) => {
  const now = dayjs();
  const date = dayjs(deleteTime);
  if (now.diff(date, 'second') < 60) {
    return '刚刚';
  }
  if (now.isSame(date, 'day')) {
    return `今天 ${date.format('HH:mm')}`;
  }
  return date.format('YYYY-MM-DD HH:mm');
}
</script>

<template>
  <ContextMenu :items="contextMenu" @select="handleMenuSelect" :enable-scroll-close="true">
    <div class="trash-item" :class="{ 'trash-item-selected': selected }" @click="handleItemClick"
      @dblclick="handleItemDoubleClick">
      <img v-if="type === 'cloud'" :src="itemData?.icon_link" class="trash-item-icon" />
      <div v-else class="trash-item-icon file-icon-type" :class="{ [itemData?.icon_link ?? '']: itemData?.icon_link }">
      </div>
      <div class="trash-item-middle">
        <Tooltip :side-offset="25" :max-width="398" :align="'start'" :trigger-by-pointer="true" :delay-duration="1000"
          trigger-class="trash-item-middle-title">
          <template #trigger>
            <span class="trash-item-middle-title">{{ itemData?.name }}</span>
          </template>
          <template #content>
            {{ itemData?.name }}
          </template>
        </Tooltip>
        <span class="trash-item-middle-info">{{ `${formatDeleteTime(itemData?.user_modified_time ?? '')} 删除 ·
          ${ThunderUtil.bytesToSize(Number(itemData?.size), 2)}${type === 'cloud' ? ` ·
          ${dayjs(itemData?.delete_time).diff(dayjs(), 'day')
            }天后将自动清除` :
            ''}`
        }}</span>
      </div>
      <div class="trash-item-right">
        <Tooltip>
          <template #trigger>
            <Button variant="ghost" is-icon size="sm" @click="handleItemDeleteOperation">
              <i class="xl-icon-general-close-l"></i>
            </Button>
          </template>
          <template #content>
            彻底删除
          </template>
        </Tooltip>

        <Tooltip>
          <template #trigger>
            <Button variant="ghost" is-icon size="sm" @click="handleItemResetOperation">
              <i class="xl-icon-general-revoke-l"></i>
            </Button>
          </template>
          <template #content>
            还原
          </template>
        </Tooltip>
      </div>
    </div>
  </ContextMenu>


</template>

<style scoped lang="scss">
.trash-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 72px;
  border-radius: var(--border-radius-L, 12px);
  padding: 0px 18px;
  gap: 14px;

  &-selected {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
  }

  &:hover {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
    cursor: pointer;
  }

  &-icon {
    width: 40px;
    height: 40px;
  }

  &-middle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    min-width: 0;

    &-info {
      color: var(--font-font-3, #86909C);
      font-size: 12px;
      line-height: 20px;
    }

  }

  &-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16px;
  }
}
</style>

<style lang="scss">
.trash-item-middle-title {
  font-size: 13px;
  color: var(--font-font-1, #272E3B);
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: fit-content;
  max-width: 100%;

  &:hover {
    color: var(--primary-primary-default, #226DF5);
  }
}
</style>