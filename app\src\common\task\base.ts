export const FaultCategoryId: number = -1000000;
export const DefaultCategoryId: number = -1;
export namespace UserDataConst {
  export const TaskPlayInfoKey: string = 'PlayInfo';
  export const TaskPlayInfoField: string = 'Index';
}

export enum ErrorType {
  Download = 'download', // 下载
  Speed = 'speed', // 会员加速服务器
  Dcdn = 'dcdn' // DCDN加速
}

// 错误码
export enum TaskError {
  Unkown = 0, // 未知错误

  // TaskErrorTask
  Create, // 任务创建失败(独立进程)
  InvaldParam, // 任务参数错误
  InvaldLink, // 任务链接失效
  InvaldConfig, // 任务配置文件错误
  Timeout, // 任务超时
  VerifyData, // 任务校验失败
  Forbidden, // 任务被禁止下载
  RangeDispatch, // 多线程加速出错
  FilePathOverRanging, // 文件路径超出系统限制

  // TaskErrorDISK                  = 200+
  FileCreate = 201, // 文件创建失败
  FileWrite, // 文件写入失败
  FileRead, // 文件读取失败
  FileRename, // 文件重命名失败
  FileFull, // 磁盘空间不足

  FileOccupied = 211, // 无法创建文件(文件被占用)
  FileAccessDenied, // 无法创建文件(权限不足)

  // TaskErrorP2sp                  = 400+

  // TaskErrorEmule                 = 500+

  // TaskErrorBt                    = 600+
  BtUploadExist = 601, // BT任务已存在

  // Forbindden                        = 700+
  ForbinddenResource = 701, // 敏感资源
  ForbinddenAccount, // 账号异常无法下载
  ForbinddenArea, // 所在区域无法下载
  ForbinddenCopyright, // 应版权方要求无法下载
  ForbinddenReaction,
  ForbinddenPorn,

  DownloadSDKCrash = 10001, // 下载引擎未启动
  torrentFileNotExist = 10002, // 种子文件不存在

  DownloadSDKMissing = 65662, // 下载引擎丢失  载库构造错误码的实现：(((1 & 0x000000FF) << 16) | (126 & 0x0000FFFF)) : LoadLibrary dll不存在的错误码是126；
}
export interface DownloadKernelInitParam {
  dbDir: string; // ���ݿ�dir
  dbName: string;
  logDir: string;
  torrentsCacheDir: string;
  tempDir: string;
  versionName: string;
  versionCode: string;
  appName: string;
  appKey: string;
  dkCfgDir: string;
}

export enum TaskType {
  Unkown = 0,
  P2sp, // 根据文件名后缀展示
  Bt, // bt文件夹
  Emule, // 根据文件名后缀展示
  Group, // 文件夹
  Magnet // 不会在下载列表展示
}

export enum TaskDetailChangedFlags {
  UnKnown = 0, // 未知（无变化）
  SrcTotal = 1, // 资源总数
  SrcUsing = 1 << 1, // 正在使用的资源数
  FileSize = 1 << 2, // 文件大小(单位 字节)
  ReceivedSize = 1 << 3, // 以接收的总大小包括无效的(曾经被废弃的也算)(单位 字节)
  DownloadSize = 1 << 4, // (已校验 + 未校验)(单位 字节)
  CompletionTime = 1 << 5, // 任务完成时间
  DownloadingPeriod = 1 << 6, // 下载历时(单位秒)
  Progress = 1 << 7, // 任务进度
  RecycleTime = 1 << 8, // 删除到垃圾箱的时间属性
  FileCreated = 1 << 9, // 是否已创建文件
  Forbidden = 1 << 10, // 是否禁用
  UserRead = 1 << 11, // 用户是否已读属性
  OpenOnComplete = 1 << 12, // 是否设置已完成打开
  DownloadSubTask = 1 << 13, // 任务组子任务是否下载
  TaskName = 1 << 14, // 任务名称
  SavePath = 1 << 15, // 保存的路径
  Cid = 1 << 16, // cid
  Gcid = 1 << 17, // gcid
  UserData = 1 << 18, // 用户自定义数据
  CategoryViewId = 1 << 19, // 任务视图id
  ErrorCode = 1 << 20, // 错误码
  TaskSpeed = 1 << 21, // 速度
  ChannelInfo = 1 << 22, // 通道信息
  ValidDownloadSize = 1 << 23, // 有效下载字节数(已校验)
  OriginName = 1 << 24, // sdk从http响应头分析到的文件名
  HTTPContentType = 1 << 25, // sdk从http响应头得到的ContentType字段
  BtSubIndex = 1 << 28,
}

export enum TaskStatus {
  Begin = -1,
  Unkown, // 未知错误
  StandBy,
  PreDownloading,
  StartWaiting, // 排队等待启动
  StartPending, // 正在开始（即已经调用异步的开始操作，但还没有完成操作）
  Started, // 正在下载
  StopPending, // 正在停止（即已经调用异步的停止操作，但还没有完成操作）
  Stopped, // 已暂停
  Succeeded, // 下载完成
  Failed, // 下载失败
  Seeding, // 正在做种上传(下载完成)
  DestroyPending, // 正在销毁任务并删除文件（即已经调用异步的销毁操作，但还没有完成操作）
  End
}

// 任务状态改变的原因
export enum TaskStopReason {
  Unknown = -1,
  Manual, // 用户手动暂停（含手机遥控、批量）
  PauseAll, // 用户手动暂停批量（仅用户点击全部停止，其他情况，都算Manual
  DeleteTask, // 用户删除暂停（含彻底删除）
  TaskJammed, // 任务卡在99%，需要暂停重启,
  LowSpeed, // 低速任务被移动至队尾而暂停
  MaxDownloadReduce, // 最大任务个数减少引起的任务暂停
  MoveTask, // 任务移动重启任务而暂停任务
  SelectDownloadLists, // 选择下载列表（任务组或bt任务)
  PrivateLoginOut, // 私人空间的任务由于退出登录而停止
  FreeDownload, // 开启空闲下载前先暂停当前所有任务
  Exit // 用户退出客户端暂停
}

export enum TaskInsertReason {
  UnKnown = 0,
  LoadTaskBasic,
  Create,
  Complete,
  Recycle,
  Recover,
  ReDownload,
}

export enum BtSubFileStatus {
  None = 0,
  Waiting = 1,
  Downloading = 2,
  Complete = 3,
  Failed = 4,
}

export enum BtSubFileDetailChangeFlags {
  None,
  Status = 1,
  IsDownload = 1 << 1,
  FileSize = 1 << 2,
  FileName = 1 << 3,
  ReceivedSize = 1 << 4,
  ErrorCode = 1 << 5,
  Cid = 1 << 6,
  Gcid = 1 << 7,
  UserRead = 1 << 8,

  FileOffset = 1 << 9,
  FilePath = 1 << 10,
}

export interface NewTaskBaseInfo {
  openOnComplete?: number; // 下载完成后是否自动运行
  fileSize?: number; // 文件大小
  savePath: string; // 保存路径
  taskName: string; // 任务名称
  description?: string; // 任务描述，2025版本里面已经不需要显示了
  origin?: string; // 创建任务的来源
  cid?: string; // 任务的cid
  gcid?: string; // 任务的gcid
}

export interface NewTaskInfo {
  background?: boolean; // 是否后台任务,一般后台任务是指不需要显示在界面里面的任务
  taskType: TaskType; //类型
  categoryId?: number; // 任务分类
  panFileId?: string; // 新建云盘取回任务的时候的fileid
  panUserId?: string;
  panSpace?: string;
  taskBaseInfo: NewTaskBaseInfo;
}

export interface NewP2spTaskInfo {
  downloadSubTask?: number;
  url: string; // 下载地址
  origin?: string; // 暂时先忽略
  useOriginResourceOnly?: boolean; // 是否从原始地址下载
  nameFixed?: boolean; // 是否固定名称
  originResourceThreadCount?: number; // 开始线程数
  refUrl?: string; // 下载地址的refer
  cookie?: string; // cookie信息
  userAgent?: string; // ua信息
  httpHeaderField?: string; // 设置http头的 Authorization
  loginFtp?: boolean; //是否启用ftp登录，
  ftpUserName?: string;
  ftpPassword?: string;
}

export enum XLBTTaskSubFileSchedulerType {
  XL_BTTaskSubFileInvalidValue = 0,
  XL_BTTaskSubFileDefaultScheduler = 1, // 表示采用内部默认的调度顺序.
  XL_BTTaskSubFileSequnecialScheduler = 2, // 表示顺序调度子任务.
}
// 从种子里面解析出来的bt文件的信息
export interface BtFileInfo {
  fileSize: number;
  realIndex: number;
  fileOffset: number;
  fileName: string;
  filePath: string;
}
// 从种子解析出来的bt的信息
export interface BtTaskInfo {
  title: string;
  infoId: string; // BT的infohash
  trackerUrls: string[];
  fileLists: BtFileInfo[];
}
export interface NewBtTaskInfo {
  refUrl?: string; // 磁力链接拉起来的refer
  origin?: string; // 原始连接，如果是通过磁力下载种子的，磁力连接传递到这里
  seedFile: string; // 种子路径
  displayName: string; // 显示名称
  fileRealIndexLists: number[]; // 需要下载的子file的realindex列表
  fileLists: BtFileInfo[]; // 种子里面的文件信息
  infoId: string; // bt的infohash
  tracker: string; // tracker信息，传空就行
  subFileScheduler?: XLBTTaskSubFileSchedulerType; // 是否启动顺序下载
}

export interface FindRepeatTaskResultItem {
  index: number;
  taskId: number;
}

export enum TaskAttribute {
  UserRead = 0,
  OpenOnComplete,
  TaskStatus,
  DownloadSpeed,
  VipSpeed,
  DownloadSize,
  FileSize,
  Gcid,
  Cid,
  TaskName,
  SavePath,
  ErrorCode,
  RecycleTime,
  CompletionTime,
  DownloadingPeriod,
  Url,
  CreateTime,
  Origin,
  CategoryId,
  IsBackground
}

export enum CategoryViewID {
  UnKnown = 0,
  Downloading,
  Completed,
  Recycle,
}

export enum DownloadStrategy {
  NormalDownload = 0,
  DownloadingPlaying,
  OnlinePlaying
};

export interface Ed2kLinkParseResult {
  fileSize: number;
  fileName: string;
  fileHash: string;
}

// �½���������Ϣ
export interface NewGroupTaskInfo {
  baseInfo: NewTaskInfo;
  taskType: TaskType;
  p2spInfo?: NewP2spTaskInfo;
  emuleInfo?: NewEmuleTaskInfo;
  btInfo?: NewBtTaskInfo;
}

// �½�emule������Ϣ
export interface NewEmuleTaskInfo {
  url: string;
  origin?: string; // 先忽略
}

// �½�emule������Ϣ
export interface NewMagnetTaskInfo {
  url: string; // 磁力的地址
  torrentFilePath: string; // 种子保存的路径
}

export interface MagnetParseResult {
  url?: string;
  infoHash?: string;
  displayName?: string;
  trackerUrls?: string[];
}

export interface P2spUrlParseResult {
  schema?: string;
  userName?: string;
  password?: string;
  hostName?: string;
  port?: number;
  fullPath?: string;
}

export enum ReNameResult {
  Succ = 0,
  StatusError, // 只能下载完成的任务允许改名
  NameFileExist, // 该名称的文件存在
  ChangeFileNameFailed, // 修改文件的名称失败
  SameName, // 修改前后一致
  NameEmpty, // 名称为空
  NameSpecialChar, // 包含特殊字符
}

export interface TaskPanFileInfo {
  fileId: string;
  space: string;
}

export interface NewTaskSet {
  taskInfo: NewTaskInfo; // 任务基本信息
  p2spInfo?: NewP2spTaskInfo; // p2sp信息信息，详细见NewP2spTaskInfo
  btInfo?: NewBtTaskInfo; // bt的信息，详细见NewBtTaskInfo
  emuleInfo?: NewEmuleTaskInfo; // 电驴任务信息，详细见NewEmuleTaskInfo
  groupInfo?: NewGroupTaskInfo[]; // 任务组信息，详细见NewGroupTaskInfo
  magnetInfo?: NewMagnetTaskInfo; // 磁力任务信息，详细见NewMagnetTaskInfo
}

export interface IDownSimpleTaskInfo {
  taskId: number;
  taskName: string;
  taskStatus?: TaskStatus;
  taskType?: TaskType;
  fileSize?: number,
  downloadSize?: number,
  downloadSpeed?: number,
  createTime?: number,
  completionTime?: number,
  errorCode?: number,
  savePath?: string,
  url?: string,
  isPanTask?: boolean,
  downloadSubTask: boolean,
}

export interface TaskBase {
  taskId: number,
  taskType?: TaskType,
  taskStatus?: TaskStatus,
  recycleTime?: number,
  fileSize?: number,
  downloadSize?: number,
  downloadSpeed?: number,
  srcTotal?: number,
  srcUsing?: number,
  createTime?: number,
  completionTime?: number,
  downloadPeriod?: number, //下载用时
  errorCode?: number,
  openOnComplete?: number,
  categoryId?: number,
  groupTaskId?: number,
  taskName?: string,
  savePath?: string,
  url?: string,
  refUrl?: string,
  cid?: string,
  gcid?: string,
  origin?: string,
  background?: boolean,
  isPanTask?: boolean,
  downloadSubTask: boolean,
}

export interface BtFileDownloadInfo {
  realIndex: number,
  downloadSize: number,
  fileSize: number,
  fileStatus: BtSubFileStatus,
  errorCode: number,
  filePath: string,
  fileName: string,
  cid: string,
  gcid: string,
  download: boolean, //该子文件是否下载
  supportPlay: boolean, //是否支持播放
}

export enum MoveTaskToNewPathResult {
  Succ = 0,
  SameDir, // 相同目录
  SubDir, // 新目录是原目录的子目录
  CreateNewPathFailed, // 新目录不存在且创建失败
  FileExistInNewPath, // 新目录存在要复制的文件或者文件夹
  Other, //其他错误
};

export enum ProxyType {
  None = 0,
  Sock5,
  Http
}

export enum ProxyVerifyResult {
  Sucess = 0,
  Fall,
  AuthFall,
  ConnectFall,
  Timeout,
  ParamError
}