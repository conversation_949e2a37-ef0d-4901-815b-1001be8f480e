#pragma once
#include "HttpDownloader.h"

class CFrameWnd: public WindowImplBase,
	public IHttpDownloadEvent
{
public:
	explicit CFrameWnd(LPCTSTR pszXMLPath);

	LPCTSTR GetWindowClassName() const;
	CDuiString GetSkinFile();
	CDuiString GetSkinFolder();

	LRESULT OnDPIChanged(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled);
     void InitWindow();
     void Notify(TNotifyUI& msg);
	 LRESULT HandleCustomMessage(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled);
	 LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam);
	 LRESULT OnClose(UINT /*uMsg*/, WPARAM /*wParam*/, LPARAM /*lParam*/, BOOL& bHandled);
	 LRESULT OnTimer(UINT id);
	 void setDPI(int DPI);
	 std::wstring GetOnlinePicPath();

	 void OnThunderUpdate();
	 void OnThunderRepair();
	 void OnThunderUninstall();

private:

	// IDownloadEvent
	virtual long OnDownloadBegin(void* pUserData);
	virtual long OnDownloadProgressChanged(DWORD dwProgress, DWORD dwSpeed, void* pUserData);
	virtual long OnDownloadFinish(long hrStatus, const wchar_t* pszSavePath, void* pUserData);

private:
	typedef enum enumDownloadType
	{
		DT_ONLINE_PIC,
		DT_SEND_MSG
	}DownloadType;
	CDuiString		m_strXMLPath;
	BOOL			m_bDownloadFinish;
	CHttpDownloader m_httpDownloader;

	LONG			m_nSendMessageCountDown;
	CHttpDownloader m_httpSendMsg;
};