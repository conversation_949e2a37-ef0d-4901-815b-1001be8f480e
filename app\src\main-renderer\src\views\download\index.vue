<script lang="ts" setup>
import { computed, nextTick, onBeforeMount, ref, useTemplateRef, onUnmounted } from 'vue'
import { useMatchDownloads } from './index'
import { useSaveHistorySpeed } from './saveHistorySpeed'
import DefaultPage from './components/DefaultPage.vue'
import { cloneDeep } from 'lodash-es'
import { ThunderUtil } from '@root/common/utils'
import { PopUpNS } from '@root/common/pop-up'
import { getSettingConfig, setSettingConfig, DOWNLOAD_SPEED_SETTING_NAME_MAP } from '@root/modal-renderer/src/views/setting'
import FilterPop, { IFiltersCategoryData } from '@root/common/components/ui/filter-pop/index.vue'
import RenamePop from '@root/common/components/ui/rename-pop/index.vue'
import SortPop from './components/SortPop.vue'
import TaskDrawer from './components/task-drawer/index.vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import MessageBox from '@root/common/components/ui/message-box'
import { taskExtraFunc } from '@/common/task-extra'
import { config } from '@root/common/config/config'
import ScheduleTaskManager from './components/ScheduleTaskManager.vue'
import { ScheduleTaskNS, ScheduleTaskType, ScheduleTaskEventType } from '@/common/schedule-task'

import { useUserStore } from '@/stores/user'
import { useTaskErrorCode } from '@/stores/taskErrorCode'
import { useTaskDetailStore } from '@/stores/taskDetail'

import { dropdownMenuList, sortList, filterSource, filterCategoryData, tabMap, userDataPan } from './source'
import { SortType, ISourceFilter } from './types'
import { generateSortKey, setSortKey, bringMainWndToTop } from './utils'
import { ThunderHelper } from '@root/common/thunder-helper'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import { FileOperationHelper } from '@root/common/helper/file-operation-helper'
import XMPMessage from '@root/common/components/ui/message/index'
import TaskList from './components/TaskList.vue'
import * as BaseType from '@root/common/task/base'
import { useRouter } from 'vue-router'

import { DownloadModelManagerImpl } from '@root/common/ui-operation/impl/download-model'
import { IDownloadModelPositionParams } from '@root/common/ui-operation/download-model-type'

import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { IAddUrlToDriveTaskData } from '@root/thunder-pan-plugin/src/utils/business-helper'

import { debounce } from 'lodash-es';
import { watchThrottled } from '@vueuse/core'

import { setUserDataPanByKey, getUserDataPan, selectPanPath } from '@/common/thunder-pan-task'
import { sleep } from './utils'
import { useSidebarInfoStore } from '@/stores/sidebarInfo';

import { IDownloadCountInfo, IDownloadCountInfoType } from '@/types/sidebarInfo'
// const dragSelectInstance = new DragSelect(
//   '.drag-select__body',
//   '.drag-select__item',
//   '.drag-select__content',
//   onDraggingSelect,
//   onDragSelectEnd,
//   {
//     top: 40,        // 列表头部的高度，选择框的范围不包括该位置
//   }
// )

defineOptions({
  name: 'download'
})

const userStore = useUserStore();
const taskErrorCode = useTaskErrorCode()
const taskDetailStore = useTaskDetailStore()
const sidebarInfoStore = useSidebarInfoStore();
const router = useRouter()

const {
  finishTasksList,
  downloadTasksList,
  createTask,
  startAllTask,
  stopAllTask,
} = useMatchDownloads()

useSaveHistorySpeed()

const isIdleDownloadFeatureEnabled = ScheduleTaskNS.getIsIdleDownloadFeatureEnabled()
const currentFilters = ref(JSON.parse(JSON.stringify(filterCategoryData)))
const fileSource = ref<any[]>([])
const fileType = ref<any[]>([])

const downloadTaskListRef = useTemplateRef('downloadTaskListRef')
const completedTaskListRef = useTemplateRef('completedTaskListRef')

/** 下载tab */
const tab = ref<'download' | 'finish'>(tabMap.download)
/** 过滤弹窗 */
const isShowFilterPop = ref(false)
/** 重命名窗口 */
const renameDialogVisible = ref(false)
/** 准备重命名 */
const realRename = ref<string>('')
/** 当前调整的单任务id */
const singleChangeTaskId = ref<number>(0)
/** 任务详情侧边栏 */
const isShowSideDetail = ref(false)
const isLoading = ref(true)

/** 过滤弹窗 */
const isShowSortPop = ref(false)
const selectSort = ref({ download: 'downNewest', completed: 'completedNewest' })

/** 是否选中任务 */
const isSelectTask = ref(false)
/** 计划任务管理器 */
const isShowScheduleTaskManager = ref(false)
/** 计划任务数量 */
const scheduleTaskCount = ref(0)

/** 是否限速 */
const isSpeedLimit = ref(false)
/** 是否存在任务开始 */
const isExistTaskStart = ref(false)
/** 过滤任务是否存在任务开始 */
const filterTaskIsExistStart = ref(false)
/** 是否存在选中文件暂停 */
const isExistSelectPause = ref(false)

const autoSelectTab = ref<boolean>(true)

const viewTaskDetail = async (task: BaseType.IDownSimpleTaskInfo) => {
  console.log('>>>>>>>>>>>>>>>>>> viewTaskDetail', task)
  await taskDetailStore.initTaskDetail(task.taskId)
  isShowSideDetail.value = true
}

// 更新计划任务数量
const updateScheduleTaskCount = () => {
  scheduleTaskCount.value = ScheduleTaskNS.getScheduleTaskCount()
}

const getSpeedLimit = async () => {
  const speedLimit = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.DownloadSpeedChk)
  isSpeedLimit.value = !!speedLimit
}

const handleDropdownMenuOpen = () => {
  getSpeedLimit()
}

const downloadCompleteTaskType = ref<ScheduleTaskType>(ScheduleTaskNS.getDownloadCompleteTaskType())

const dropdownMenuListShow = computed(() => {
  // Deep copy to prevent side-effects on the source array
  const menuList = cloneDeep(dropdownMenuList);

  const filteredList = menuList.filter((item: any) => {
    if (isDownload.value) {
      return item.key !== 'delInvalidTasks';
    }
    return true;
  });
  
  const downFinishItem = filteredList.find((item: any) => item.key === 'downFinish');
  if (downFinishItem && downFinishItem.children) {
    downFinishItem.children.forEach((child: any) => {
      if ((child.key === 'powerOff' && downloadCompleteTaskType.value === ScheduleTaskType.ShutDown) ||
        (child.key === 'sleep' && downloadCompleteTaskType.value === ScheduleTaskType.Sleep) ||
        (child.key === 'exit' && downloadCompleteTaskType.value === ScheduleTaskType.Exit)) {
        child.rightIcon = 'xl-icon-general-check-m'
      } else {
        child.rightIcon = ''
      }
    })
  }

  const downPlanItem = filteredList.find((item: any) => item.key === 'downPlan');

  if (downPlanItem && downPlanItem.children) {
    // 1. Handle Idle Download icon
    const idleItem = downPlanItem.children.find((c: any) => c.key === 'downIdle');
    if (idleItem) {
      if (isIdleDownloadFeatureEnabled.value) {
        // The component places this icon on the left.
        idleItem.rightIcon = 'xl-icon-general-check-m';
      }
    }

    // 2. Handle Schedule Task count
    const scheduleItem = downPlanItem.children.find((c: any) => c.key === 'scheduleTask');
    if (scheduleItem) {
      scheduleItem.rightText = scheduleTaskCount.value > 0 ? scheduleTaskCount.value.toString() : '';
    }

    // 3. Handle Speed Limit
    const speedItemIndex = downPlanItem.children.findIndex((c: any) => c.key === 'downSpeed');
    if (isSpeedLimit.value) {
      if (speedItemIndex !== -1) {
        downPlanItem.children.splice(speedItemIndex, 1,
          { key: 'downSpeeding', label: '限速下载中' },
          { key: 'downSpeedMax', label: '全速下载' }
        );
      }
    }
  }

  return filteredList;
});

/** 顺序 */
const sourceSortMap = computed(() => {
  if (isDownload.value) return sortList.filter(item => ![SortType.ByTime_Completion].includes(item.key))
  return sortList.filter(item => ![SortType.ByTime_Creation, SortType.ByProgress].includes(item.key))
})

const isDownload = computed(() => {
  return tab.value === 'download'
})
/** 下载状态聚合  */
const isShowAllStatus = computed(() => {
  return isDownload.value && isExitDownloadTask.value
})
const finishTabText = computed(() => {
  const len = finishTasksList?.value.length
  return len ? `· ${len}${len > 999 ? '+' : ''}` : '· 0'
})
const downloadTabText = computed(() => {
  const len = downloadTasksList?.value.length
  return len ? `· ${len}${len > 999 ? '+' : ''}` : '· 0'
})

// function onDraggingSelect () {
//   // stopClearPicked.value = true
//   console.log('onDraggingSelect')
// }

// async function onDragSelectEnd (selectArea: HTMLElement, picked?: HTMLElement[]) {
//   if (!selectArea || !picked || !picked.length) return
//   console.log('onDragSelectEnd', selectArea, picked)

//   // GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.DRAG_SELECT_END, selectArea, picked)
//   // await sleep(150)
//   // stopClearPicked.value = false
// }

function judgeIsShow(task: BaseType.IDownSimpleTaskInfo) {
  // 过滤文件类型
  if (!fileType.value.length && !fileSource.value.length) { return true }
  const sourceType = TaskUtilHelper.getFileType(task.taskName || '')
  let type: number = ISourceFilter.Unkown
  switch (sourceType) {
    case 'video':
      type = ISourceFilter.Video
      break
    case 'audio':
      type = ISourceFilter.Music
      break
    case 'img':
      type = ISourceFilter.Pic
      break
    case 'zip':
      type = ISourceFilter.Zip
      break
    case 'software':
      type = ISourceFilter.Software
      break
    case 'doc':
      type = ISourceFilter.Doc
      break
    case 'bt':
      type = ISourceFilter.Bt
      break
    default:
      type = ISourceFilter.Unkown
      break
  }
  if (task.taskType === BaseType.TaskType.Group) {
    type = ISourceFilter.taskGroup
  } else if (task.taskType === BaseType.TaskType.Bt) {
    type = ISourceFilter.Bt
  }
  // 判断来源
  let judgeSource = true
  let judgeType = true
  if (fileType.value.length) {
    judgeType = fileType.value.includes(type)
  }
  if (fileSource.value.length) {
    let sourceType = ISourceFilter.Unkown
    if (task.isPanTask) {
      sourceType = ISourceFilter.yunpan
    }
    judgeSource = fileSource.value.includes(sourceType)
  }
  return judgeType && judgeSource
}

/** 展示完成任务 */
const showFinishTasksList = computed(() => {
  const filterFinishTasks = finishTasksList.value.filter(item => {
    return judgeIsShow(item)
  })
  if (['completedNewest', 'completedOldest'].includes(selectSort.value.completed)) { // 完成时间
    if ('completedNewest' === selectSort.value.completed) {
      return filterFinishTasks.sort((a, b) => (b.completionTime || 0) - (a.completionTime || 0))
    }
    return filterFinishTasks.sort((a, b) => (a.completionTime || 0) - (b.completionTime || 0))
  }
  if (['asc', 'desc'].includes(selectSort.value.completed)) { // 按文件名称排序
    if ('asc' === selectSort.value.completed) {
      return filterFinishTasks.sort((a, b) => {
        return ThunderHelper.compareStr((a.taskName || ''), (b.taskName || ''))
      });
    } else {
      return filterFinishTasks.sort((a, b) => {
        return ThunderHelper.compareStr((b.taskName || ''), (a.taskName || ''))
      });
    }
  }
  if (['smallToBig', 'BigToSmall'].includes(selectSort.value.completed)) { // 按文件大小排序 按大小排序
    if ('smallToBig' === selectSort.value.completed) {
      return filterFinishTasks.sort((a, b) => {
        return (a.fileSize || 0) - (b.fileSize || 0)
      });
    } else {
      return filterFinishTasks.sort((a, b) => {
        return (b.fileSize || 0) - (a.fileSize || 0)
      });
    }
  }

  return filterFinishTasks
})

const showDownloadTasksList = computed(() => {
  const filterDownloadTasks = downloadTasksList.value.filter(item => {
    return judgeIsShow(item)
  })
  if (['downNewest', 'downOldest'].includes(selectSort.value.download)) { // 创建时间
    if ('downNewest' === selectSort.value.download) {
      return filterDownloadTasks.sort((a, b) => (b.createTime || 0) - (a.createTime || 0));
    }
    return filterDownloadTasks.sort((a, b) => (a.createTime || 0) - (b.createTime || 0));
  }

  if (['slowToFast', 'FastToSlow'].includes(selectSort.value.download)) { // 按当前进度排序
    if ('slowToFast' === selectSort.value.download) {
      return filterDownloadTasks.sort((a, b) => {
        return (a.downloadSize || 0) - (b.downloadSize || 0)
      });

    } else {
      return filterDownloadTasks.sort((a, b) => {
        return (b.downloadSize || 0) - (a.downloadSize || 0)
      });
    }
  }
  if (['smallToBig', 'BigToSmall'].includes(selectSort.value.download)) { // 按文件大小排序 按大小排序
    if ('smallToBig' === selectSort.value.download) {
      return filterDownloadTasks.sort((a, b) => {
        return (a.fileSize || 0) - (b.fileSize || 0)
      });
    } else {
      return filterDownloadTasks.sort((a, b) => {
        return (b.fileSize || 0) - (a.fileSize || 0)
      });
    }
  }
  if (['asc', 'desc'].includes(selectSort.value.download)) { // 按文件名称排序
    if ('asc' === selectSort.value.download) {
      return filterDownloadTasks.sort((a, b) => {
        return ThunderHelper.compareStr((a.taskName || ''), (b.taskName || ''))
      });
    } else {
      return filterDownloadTasks.sort((a, b) => {
        return ThunderHelper.compareStr((b.taskName || ''), (a.taskName || ''))
      });
    }
  }
  // 过滤
  console.log('>>>>>>>>>>>> filterDownloadTasks', filterDownloadTasks)
  return filterDownloadTasks
})

const zeroSpeed = computed(() => {
  if (!isExistTaskStart.value) {
    return '已暂停'
  }
  const { speed, unit } = ThunderUtil.formatSpeed(sumSpeed.value)
  return speed + unit
})

/** 是否存在下载tab任务 */
const isExitDownloadTask = computed(() => {
  return showDownloadTasksList.value.length
})

/** 是否存在已完成tab任务 */
const isExitFinishTask = computed(() => {
  return showFinishTasksList.value.length
})

/** 是否展示全部开始暂停按钮 */
const isShowAllPlayPauseBtn = computed(() => {
  return isDownload.value && isExitDownloadTask.value
})
/** 是否展示缺省页 */
const isShowDefaultPage = computed(() => {
  return (isDownload.value && !isExitDownloadTask.value) || (!isDownload.value && !isExitFinishTask.value)
})
/** 是否展示操作栏 */
const isShowTabsOperation = computed(() => {
  return (isDownload.value && downloadTasksList.value.length) || (!isDownload.value && finishTasksList.value.length)
})

const filterCount = computed(() => {
  return (fileSource.value.length + fileType.value.length) || 0
})


/** 选择tab */
const handleChangeTab = (val: 'download' | 'finish') => {
  if (val === tab.value) { return }
  tab.value = val
  if (val === 'download') {
    completedTaskListRef.value && completedTaskListRef.value.clearSelectId()
  } else {
    downloadTaskListRef.value && downloadTaskListRef.value.clearSelectId()
  }
}

const handleShowFilterPop = () => {
  isShowFilterPop.value = !isShowFilterPop.value
}

const handleFilterPopClose = () => {
  isShowFilterPop.value = false
}

const handleFilterPopConfirm = (result: IFiltersCategoryData[]) => {
  console.log('>>>>>>>>>>>>>>>>>> result', result)
  result.forEach(item => {
    if (item.type === 'fileSource') {
      fileSource.value = item.filters
      item.isCheckAll = item.filters.length === 0
    } else if (item.type === 'fileType') {
      fileType.value = item.filters
      item.isCheckAll = item.filters.length === 0
    }
  })
  // 保存过滤条件
  config.setValue('TaskList', `CategoryViewId1_FilterSource`, fileSource.value)
  config.setValue('TaskList', `CategoryViewId1_FilterType`, fileType.value)
  currentFilters.value = result
  isShowFilterPop.value = false
}

const handleFilterReset = () => {
  config.setValue('TaskList', `CategoryViewId1_FilterSource`, [])
  config.setValue('TaskList', `CategoryViewId1_FilterType`, [])
  fileSource.value = []
  fileType.value = []
  currentFilters.value = JSON.parse(JSON.stringify(filterCategoryData))
}

/** 设置排序 */
const handleConfirmSort = (val: string) => {
  console.log('>>>>>>>>>>> handleConfirmSort', val)
  // D:\App\thunder_2025\bin\profiles
  if (isDownload.value) {
    selectSort.value.download = val
    const source = setSortKey(val)
    if (source) {
      const { attr, state } = source
      config.setValue('TaskList', `CategoryViewId1_SortAttr`, attr)
      config.setValue('TaskList', `CategoryViewId1_SortState`, state)
      console.log('>>>>>>>>>>>>>>>>>> attr, state', attr, state)
    }
  } else {
    selectSort.value.completed = val
    const source = setSortKey(val)
    if (source) {
      const { attr, state } = source
      console.log('>>>>>>>>>>>>>>>>>> attr, state', attr, state)
      config.setValue('TaskList', `CategoryViewId2_SortAttr`, attr)
      config.setValue('TaskList', `CategoryViewId2_SortState`, state)
    }
  }
}
const handleShowSortPop = () => {
  console.log('>>>>>>>>>>> handleShowSortPop')
  isShowSortPop.value = !isShowSortPop.value
}

/** 初始配置 */
const initConfig = async () => {
  isLoading.value = true
  try {
    const downloadAttr = await config.getValue('TaskList', 'CategoryViewId1_SortAttr', SortType.ByTime_Creation) as number
    const downloadState = await config.getValue('TaskList', 'CategoryViewId1_SortState', -1) as number
    selectSort.value.download = generateSortKey({ attr: downloadAttr, state: downloadState }) || 'downNewest'
    const completedAttr = await config.getValue('TaskList', 'CategoryViewId2_SortAttr', SortType.ByTime_Completion) as number
    const completedState = await config.getValue('TaskList', 'CategoryViewId2_SortState', -1) as number
    selectSort.value.completed = generateSortKey({ attr: completedAttr, state: completedState }) || 'completedNewest'

    const FilterType = await config.getValue('TaskList', 'CategoryViewId1_FilterType', []) as any[]
    const FilterSource = await config.getValue('TaskList', 'CategoryViewId1_FilterSource', []) as any[]
    const filterParams = [
      {
        type: 'fileSource',
        isCheckAll: FilterSource.length === 0,
        filters: FilterSource,
      },
      {
        type: 'fileType',
        isCheckAll: FilterType.length === 0,
        filters: FilterType,
      },
    ]
    fileSource.value = FilterSource
    fileType.value = FilterType
    console.log('>>>>>>>>>>> filterParams', filterParams)
    currentFilters.value = filterParams
  } catch (error) {
    console.log('>>>>>>>>>> error', error)
  }
  isLoading.value = false
}

/** 重命名确认 */
const handleRenameConfirm = async (newName: string) => {
  console.log('newName', newName)
  const code = await taskExtraFunc.rename(singleChangeTaskId.value, newName)
  console.log('>>>>>>>>>>>>>>> code', code)
  if (code === 0) {
    XMPMessage({
      message: '重命名成功',
      type: 'success'
    })
  } else {
    XMPMessage({
      message: '重命名失败',
      type: 'error'
    })
  }
  realRename.value = ''
  renameDialogVisible.value = false
}
/** 打开重命名弹窗 */
const openRenameDialog = (task: BaseType.IDownSimpleTaskInfo) => {
  console.log('open rename dialog', task)
  const { taskId, taskName } = task
  singleChangeTaskId.value = taskId || 0
  realRename.value = taskName || ''
  renameDialogVisible.value = true
}

const handleCloseRename = () => {
  renameDialogVisible.value = false
  realRename.value = ''
}

const handleDeleteTask = () => {
  if (isDownload) {
    downloadTaskListRef.value && downloadTaskListRef.value.exposeRecycle()
  } else {
    completedTaskListRef.value && completedTaskListRef.value.exposeRecycle()
  }
}

const selectTaskIds = ref<number[]>([])

const operationDownBtn = computed(() => {
  if (isSelectTask.value) {
    return {
      text: isExistSelectPause.value ? '开始下载' : '暂停下载',
      icon: !isExistSelectPause.value ? 'xl-icon-general-stop-m ' : 'xl-icon-general-download-m'
    }
  }
  return {
    text: filterTaskIsExistStart.value ? '全部暂停' : '全部开始',
    icon: filterTaskIsExistStart.value ? 'xl-icon-general-stop-m ' : 'xl-icon-general-download-m'
  }
})

const handleChangeAllTaskStatus = () => {
  console.log('> handleChangeAllTaskStatus', isSelectTask.value, isExistSelectPause.value, selectTaskIds.value)
  if (isSelectTask.value) {
    const selectIds = JSON.parse(JSON.stringify(selectTaskIds.value))
    console.log('>>>>>>>>>>>>> selectIds', selectIds)
    if (isExistSelectPause.value) {
      taskExtraFunc.batchStartTasks(selectIds)
    } else {
      taskExtraFunc.batchStopTasks(selectIds, BaseType.TaskStopReason.Manual)
    }
  } else {
    const ids = showDownloadTasksList.value.map(item => item.taskId)
    console.log('>>>>>>>>> ids', ids)
    if (filterTaskIsExistStart.value) {
      taskExtraFunc.batchStopTasks(ids, BaseType.TaskStopReason.Manual)
    } else {
      taskExtraFunc.batchStartTasks(ids)
    }
  }
}

/** 处理按钮是否展示 */
const updateSelectedId = (ids: number[]) => {
  console.log('>>>>>>>>>>>> ids', ids, ids.length > 0)
  selectTaskIds.value = ids
  const isSelected = ids.length > 0
  isSelectTask.value = isSelected
  if (isSelected && isDownload.value) {
    const existSelectPause = showDownloadTasksList.value.find(item => {
      if (!selectTaskIds.value.includes(item.taskId)) return false
      if (typeof item.taskStatus !== 'number') return false
      console.log('>>>>>>>>>>>>>>>>>> existSelectPause', item)
      const { Stopped, StopPending, Failed, Unkown } = BaseType.TaskStatus
      return [Stopped, StopPending, Failed, Unkown].includes(item.taskStatus)
    })
    isExistSelectPause.value = !!existSelectPause
  } else {
    isExistSelectPause.value = false
  }
}

/** 处理计划任务管理器 */
const handleScheduleTaskManager = () => {
  console.log('>>>>>>>>>>>>>>>>>> handleScheduleTaskManager')
  isShowScheduleTaskManager.value = true
}

/** 处理删除所有无效任务 */
const handleDelAllInvalidTask = () => {
  PopUpNS.showDelAllInvalidTaskDlg()
}

/** 处理设置空闲下载 */
const handleSetIdleDownload = () => {
  if (isIdleDownloadFeatureEnabled.value) {
    ScheduleTaskNS.closeFreeDownLoad()
    XMPMessage({
      message: '已关闭空闲下载',
      type: 'success'
    })
  } else {
    ScheduleTaskNS.startFreeDownLoad()
    XMPMessage({
      message: '已开启空闲下载',
      type: 'success'
    })
  }
}

// 已移动到下方的完整实现

// 处理下拉菜单点击
const handleSelectClick = async (item: any) => {
  console.log('>>>>>>>>>>>>>>>>>> handleSelectClick', item)
  if (item === 'downHistory') {
    router.push('/download-history')
  } else if (item === 'delInvalidTasks') {
    handleDelAllInvalidTask()
  } else if (item === 'scheduleTask') {
    handleScheduleTaskManager()
  } else if (item === 'downIdle') {
    if (isIdleDownloadFeatureEnabled.value) {
      handleSetIdleDownload()
    } else {
      MessageBox.confirm({
        title: '提示',
        message: '是否开启空闲下载？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
      }).then(() => {
        handleSetIdleDownload()
      })
    }
    
  } else if (item === 'downSpeeding') {
    // Already in speed-limit mode: open settings
    PopUpNS.showLimitSpeedSettingDlg()
  } else if (item === 'downSpeedMax') {
    // Switch to full-speed download
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.DownloadSpeedChk, false)
    XMPMessage({ message: '已恢复全速下载', type: 'success' })
    isSpeedLimit.value = false
  } else if (item === 'downSpeed') {
    // Enable speed-limit mode
    PopUpNS.showLimitSpeedSettingDlg()
  } else if (item === 'powerOff') {
    ScheduleTaskNS.setDownloadCompleteTaskType(ScheduleTaskType.ShutDown)
    downloadCompleteTaskType.value = ScheduleTaskType.ShutDown
    XMPMessage({
      message: '已设置下载完成后关机',
      type: 'success'
    })
  } else if (item === 'sleep') {
    ScheduleTaskNS.setDownloadCompleteTaskType(ScheduleTaskType.Sleep)
    downloadCompleteTaskType.value = ScheduleTaskType.Sleep
    XMPMessage({
      message: '已设置下载完成后睡眠',
      type: 'success'
    })
  } else if (item === 'exit') {
    ScheduleTaskNS.setDownloadCompleteTaskType(ScheduleTaskType.Exit)
    downloadCompleteTaskType.value = ScheduleTaskType.Exit
    XMPMessage({
      message: '已设置下载完成后退出',
      type: 'success'
    })
  }
}

// 关闭计划任务管理器
const handleCloseScheduleTaskManager = () => {
  isShowScheduleTaskManager.value = false
  updateScheduleTaskCount()
}


/** 校验路由是否为当前路由，否则跳转到对应路由 */
const checkRouter = () => {
  const currentRoute = router.currentRoute.value
  const currentPath = currentRoute.path
  console.log('>>>>>>>>>>>  currentPath', currentPath)
  if (currentPath !== '/download') {
    router.push('/download')
  }
}

const scrollIntoView = async (taskId: number, jumpTab: 'download' | 'finish') => {
  console.log('>>>>>>>>>>> 执行', taskId, jumpTab)
  // 获取task判断是否完成
  if (jumpTab === tabMap.download) {
    // 跳转对应位置
    const index = showDownloadTasksList.value.findIndex(item => {
      return item.taskId === taskId
    })
    await sleep(300)
    downloadTaskListRef.value && downloadTaskListRef.value.scrollToItem(index, taskId)
  } else if (jumpTab === tabMap.finish) {
    const index = showFinishTasksList.value.findIndex(item => {
      return item.taskId === taskId
    })
    await sleep(300)
    completedTaskListRef.value && completedTaskListRef.value.scrollToItem(index, taskId)
  }
}

const debouncedScrollIntoView = debounce(
  (taskId: number, jumpTab: 'download' | 'finish') => scrollIntoView(taskId, jumpTab),
  100, // 延迟300ms
)

/** 定位到任务 */
async function positionTask(params: IDownloadModelPositionParams) {
  console.log('>>>>>　positionTask', params)
  const { taskId, isPassive } = params
  checkRouter()

  bringMainWndToTop()
  if (!taskId) {
    return
  }

  const taskBase = await taskExtraFunc.getTaskBase(taskId)
  tab.value = taskBase?.taskStatus === BaseType.TaskStatus.Succeeded ? 'finish' : 'download'
  if (isPassive) { // 主动触发直接执行， 否则防抖处理
    scrollIntoView(taskId, tab.value)
  } else {
    debouncedScrollIntoView(taskId, tab.value)
  }
}

/** 云添加 */
const handleAddCloud = async (tasks: BaseType.IDownSimpleTaskInfo[]) => {
  autoSelectTab.value = true
  if (!userStore.uid) {
    PopUpNS.showLoginDlg()
    return
  }
  ThunderPanClientSDK.getInstance().openPathSelectorDialog({}).then(async (res) => {
    console.log('>>>>> res', res)
    if (!(res && res.success && res.data)) {
      return;
    }
    const savePanPath = selectPanPath(res)

    if (!res.data?.selectedItem) { return }

    const panSavePathId = res.data.selectedItem.id || ''
    console.log('>>>>>>>>>>>>>>>>>>>> tasks', tasks)
    // let isToastHint = false
    for (const task of tasks) {

      const { taskId, taskType, taskName, savePath = '' } = task

      if (taskType === BaseType.TaskType.Group) {
        changeGroupAddPan(task, panSavePathId, savePanPath)

      } else if (task.url) {

        const params = {
          files: [] as string[],
          name: task.taskName,
          url: task.url,
          parentId: panSavePathId,
          path: ''
        }
        // 判断是否为bt文件
        if (task.taskType === BaseType.TaskType.Bt) {
          const btFileInfos = await taskExtraFunc.getBtFileInfos(taskId)
          console.log('>>>>>>>>>>>>>>> btFileInfos', btFileInfos)
          btFileInfos.forEach(item => {
            if (item.download) {
              params.files.push(String(item.realIndex))
            }
          })
        }
        const res = await ThunderPanClientSDK.getInstance().batchAddUrlsToDrive([params], {
          autoSelectTab: autoSelectTab.value
        })
        if (res.success && res.data) {
          autoSelectTab.value = false
          XMPMessage({
            type: 'success',
            message: '已加入云添加列表'
          })
        }
        // changeResPanInfo(res, savePanPath, taskId)
      }
      // 存储路径
    }
  })
}

const changeGroupAddPan = async (task, panSavePathId: string, savePanPath: string) => { // 组任务
  const { taskId, taskName, savePath = '' } = task
  const rootPath = savePath + '\\'
  const escapedRootPath = ThunderUtil.escapeRegExp(rootPath)
  const regexp = new RegExp(`^${escapedRootPath}`)
  const params: IAddUrlToDriveTaskData[] = []
  const groupTaskIds = await taskExtraFunc.getGroupSubTaskIds(taskId)

  for (const subTaskId of groupTaskIds) {

    const subTask = await taskExtraFunc.getTaskBase(subTaskId)

    // 判断是否勾选
    if (!subTask?.downloadSubTask) { continue }
    console.log('>>>>>>>>>>>>>> subTask', subTask)

    const subPath = subTask?.savePath ?? ''
    const changeSubPath = subPath.replace(regexp, '')
    console.log('>>>>>>>>>>>>>> changeSubPath', changeSubPath)
    // 判断是否为bt文件
    if (subTask.taskType === BaseType.TaskType.Bt) {
      const btFileInfos = await taskExtraFunc.getBtFileInfos(subTask.taskId)
      // 处理bt子文件
      const name = subTask.taskName || ''
      const addInfo = {
        name,
        url: subTask.url || '',
        path: changeSubPath,
        parentId: panSavePathId,
        files: [] as string[],
      }
      btFileInfos.forEach(item => {
        if (item.download) {
          addInfo.files.push(String(item.realIndex))
        }
      })
      params.push(addInfo)
    } else if (subTask && subTask.url) {
      params.push({
        name: subTask.taskName,
        url: subTask.url,
        parentId: panSavePathId,
        path: changeSubPath
      })
    }
  }
  console.log('>>>>>>>>>>>>> params', params)
  if (params.length > 0) {
    const res = await ThunderPanClientSDK.getInstance().batchAddUrlsToDrive(params, {
      groupName: taskName,
      autoSelectTab: autoSelectTab.value
    })
    if (res.success && res.data) {
      autoSelectTab.value = false
      XMPMessage({
        type: 'success',
        message: '已加入云添加列表'
      })
    }
    // changeResPanInfo(res, savePanPath, taskId, panSavePathId)
  }
}

const changeResPanInfo = (res: any, savePanPath: string, taskId: number, panSavePathId: string) => {
  console.log('>>>>>>>>>>>>>> changeResPanInfo', res)
  if (res.success && res.data) {
    const resDataList = res.data || []
    let panInfo = {
      path: savePanPath || '',
      panFileId: '',
      panTaskIds: [] as number[],
      panSavePathId
    }
    resDataList.forEach(item => {
      const panTaskId = item.task?.id
      panInfo.panTaskIds.push(panTaskId)
    })
    setUserDataPanByKey(taskId, userStore.uid, panInfo)
    setTimeout(async () => {
      const panInfo = await getUserDataPan(taskId, userStore.uid)
      console.log('>>>>>>>>>>>>>>>>>>> panInfo', panInfo)
    }, 1000)
  }
}

const sumSpeed = ref(0)

watchThrottled(downloadTasksList, (newVal) => {
  nextTick(async () => {
    const isExistStart = newVal.find(item => {
      if (typeof item.taskStatus !== 'number') return false
      const { StartWaiting, StartPending, Started } = BaseType.TaskStatus
      return [StartWaiting, StartPending, Started].includes(item.taskStatus)
    })
    const filterIsExistStart = showDownloadTasksList.value.find(item => {
      if (typeof item.taskStatus !== 'number') return false
      const { StartWaiting, StartPending, Started } = BaseType.TaskStatus
      return [StartWaiting, StartPending, Started].includes(item.taskStatus)
    })
    isExistTaskStart.value = !!isExistStart
    filterTaskIsExistStart.value = !!filterIsExistStart
    sumSpeed.value = newVal.reduce((acc, cur) => {
      if (typeof cur.downloadSpeed === 'number') {
        acc += cur.downloadSpeed
      }
      return acc
    }, 0)
    if (selectTaskIds.value.length) {
      const existSelectPause = newVal.find(item => {
        if (!selectTaskIds.value.includes(item.taskId)) return false
        if (typeof item.taskStatus !== 'number') return false
        console.log('>>>>>>>>>>>>>>>>>> existSelectPause', item)
        const { Stopped, StopPending, Failed, Unkown } = BaseType.TaskStatus
        return [Stopped, StopPending, Failed, Unkown].includes(item.taskStatus)
      })
      isExistSelectPause.value = !!existSelectPause
    }
    console.log('>>>>>>>>>>>>>> isExistSelectPause.value', isExistSelectPause.value)
  })
}, { deep: true, immediate: true, throttle: 200 })


watchThrottled(downloadTabText, (newVal) => {
  sidebarInfoStore.setDownloadByKey(IDownloadCountInfoType.downCount, downloadTasksList?.value.length || 0)
}, { throttle: 200, immediate: true });

onBeforeMount(async () => {
  await taskErrorCode.initTaskConfigs()
  await initConfig()
  getSpeedLimit()
  // 初始化计划任务数量
  updateScheduleTaskCount()

  // 添加计划任务数量变化事件监听
  const eventEmitter = ScheduleTaskNS.getEventEmitter()
  eventEmitter.on(ScheduleTaskEventType.ScheduleTaskCountChange, (count: number) => {
    scheduleTaskCount.value = count
  })
  DownloadModelManagerImpl.GetInstance().attachDownloadModelPositionTaskEvent(positionTask)
});

onUnmounted(() => {
  DownloadModelManagerImpl.GetInstance().detachDownloadModelPositionTaskEvent(positionTask)
  debouncedScrollIntoView.cancel()
})
</script>

<template>
  <div class="download-tab">
    <div class="tab-header">
      <div class="tab-title">下载</div>
      <div class="header-tab-btn">
        <div class="more-btn-wrapper">
          <DropdownMenu :items="dropdownMenuListShow" side="bottom" align="end" @select="handleSelectClick"
            :content-class="`download-dropdown-menus`"
            :sub-content-class="`${isSpeedLimit ? 'speed-limit-active' : ''}`"
            @open="handleDropdownMenuOpen" :enable-scroll-close="true" :item-class="'download-dropdown-menu-item'">
            <Button variant="ghost" is-icon size="sm">
              <i class="xl-icon-more"></i>
            </Button>
          </DropdownMenu>
        </div>
      </div>
    </div>
    <div class="tabs-wrapper">
      <div class="tabs-left">
        <div class="tab-btn" :class="{ 'is-active': isDownload }" @click="handleChangeTab('download')">
          下载中 {{ downloadTabText }}
        </div>
        <div class="tab-btn" :class="{ 'is-active': !isDownload }" @click="handleChangeTab('finish')">
          已完成 {{ finishTabText }}
        </div>
      </div>
      <div class="tabs-right" v-if="isShowTabsOperation">
        <div v-if="isShowAllPlayPauseBtn" class="tabs-right-btn">
          <Button variant="outline" size="sm" @click="handleChangeAllTaskStatus">
            <i :class="operationDownBtn.icon"></i>
            {{ operationDownBtn.text }}
          </Button>
        </div>
        <div class="tabs-right-btn" v-if="isSelectTask">
          <Button variant="outline" size="sm" @click="handleDeleteTask">
            <i class="xl-icon-general-delete-m"></i>
            删除
          </Button>
        </div>
        <div class="tabs-right-btn filter-btn-wrapper">
          <Button variant="outline" is-icon size="sm" @click="handleShowSortPop">
            <i class="xl-icon-sort"></i>
          </Button>
          <SortPop v-model:show="isShowSortPop" :selectSort="selectSort" :isDownload="isDownload"
            :sourceSortMap="sourceSortMap" @confirm="handleConfirmSort" />
        </div>
        <div class="tabs-right-btn">
          <Button @click="handleShowFilterPop" variant="outline" :is-icon="filterCount <= 0"
            right-icon="xl-icon-general-close-m" :has-right-icon="filterCount > 0" @right-icon-click="handleFilterReset"
            size="sm">
            <div class="filter-button-content" :class="{
              'is-active': filterCount > 0
            }">
              <i class="xl-icon-filter"></i>
              <span v-if="filterCount > 0">筛选 · {{ filterCount }}</span>
            </div>
          </Button>

          <FilterPop v-if="isShowFilterPop" :filters-source="filterSource" :filters-category-data="currentFilters"
            @close="handleFilterPopClose" @confirm="handleFilterPopConfirm" />
        </div>
      </div>
    </div>
    <!-- 下载状态栏 -->
    <div v-if="isShowAllStatus" class="status-wrapper">
      <span class="status-speed">
        {{ zeroSpeed }}
      </span>
    </div>
    <!-- 下载列表 -->
    <div class="download-content-wrapper" v-show="!isLoading">
      <TaskList ref="downloadTaskListRef" data-scroll-container v-show="isDownload && !isShowDefaultPage"
        :taskList="showDownloadTasksList" @viewTaskDetail="viewTaskDetail" @openRenameDialog="openRenameDialog"
        @updateSelectedId="updateSelectedId" @handleAddCloud="handleAddCloud" />
      <TaskList ref="completedTaskListRef" data-scroll-container v-show="!isDownload && !isShowDefaultPage"
        :taskList="showFinishTasksList" @viewTaskDetail="viewTaskDetail" @openRenameDialog="openRenameDialog"
        @updateSelectedId="updateSelectedId" @handleAddCloud="handleAddCloud" completed />
      <DefaultPage v-show="isShowDefaultPage" :isDownload="isDownload" @createTask="createTask" />
    </div>
    <RenamePop :open="renameDialogVisible" :name="realRename" @close="handleCloseRename" @cancel="handleCloseRename"
      @confirm="handleRenameConfirm" />
    <TaskDrawer v-if="isShowSideDetail" v-model:show="isShowSideDetail" />
    <ScheduleTaskManager :open="isShowScheduleTaskManager" @close="isShowScheduleTaskManager = false" />
  </div>
</template>

<style lang="scss" scoped>
.download-tab {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .tab-header {
    height: 64px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    box-sizing: border-box;
  }

  .tab-title {
    font-weight: 700;
    font-size: 26px;
    line-height: 64px;
    color: var(--font-font-1, rgba(39, 46, 59, 1));
  }

  .header-tab-btn {
    display: flex;
    align-items: center;
  }

  .more-btn-wrapper {
    margin-left: 12px;
    position: relative;

    .task-badge {
      position: absolute;
      top: -6px;
      right: -6px;
      min-width: 16px;
      height: 16px;
      background-color: var(--primary-primary-default, #226DF5);
      color: #fff;
      border-radius: 8px;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
      padding: 0 4px;
    }
  }

  .tabs-wrapper {
    flex-shrink: 0;
    height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    box-sizing: border-box;
  }

  .filter-btn-wrapper {
    position: relative;
  }

  .tabs-right {
    display: flex;
    align-items: center;

    .tabs-right-btn {
      position: relative;
      margin-left: 12px;

      &:first-of-type {
        margin-left: 0;
      }
    }
  }

  .tab-btn {
    font-size: 13px;
    line-height: 22px;
    background: var(--button-button2-default, rgba(242, 243, 245, 1));
    color: var(--font-font-2, rgba(78, 87, 105, 1));
    border-radius: var(--border-radius-S, 6px);
    padding: 5px 12px;
    margin-left: 12px;
    cursor: pointer;

    &:first-of-type {
      margin-left: 0;
    }

    &.is-active {
      background: var(--button-button1-default, rgba(39, 46, 59, 1));
      color: var(--button-button1-font-default, rgba(255, 255, 255, 1));
    }
  }

  .tabs-left {
    display: flex;
    align-items: center;
  }

  .status-wrapper {
    flex-shrink: 0;
    height: 60px;
    padding: 0 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }

  .status-speed {
    font-weight: 700;
    font-size: 16px;
    line-height: 30px;
    color: var(--font-font-1, rgba(39, 46, 59, 1));
  }

  .download-content-wrapper {
    flex: 1;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .filter-button-content {
    display: flex;
    align-items: center;
    gap: 6px;

    &.is-active {
      color: var(--primary-primary-default);
    }
  }
}
</style>

<style lang="scss">
.download-dropdown-menu-item {
  .RightSlot span {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 16px;
    height: 16px;
    margin-right: 4px;
    border-radius: 24px;
    border: 1px solid var(--primary-primary-border, #226DF5);
    background: var(--button-button1-font-default, #FFF);
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    padding: 0 3px;
    color: var(--primary-primary-font-default, #226DF5);
  }
}
 

/* 限速下载中菜单项文字变红 */
.speed-limit-active.DropdownMenuSubContent-downPlan .DropdownMenuItem:first-child {
  color: var(--functional-error-default, #FF4D4F);
}

</style>
