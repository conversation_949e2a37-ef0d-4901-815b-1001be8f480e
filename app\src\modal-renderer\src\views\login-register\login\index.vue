<script setup lang="ts">
import Tabs from '@root/common/components/ui/tabs/index.vue'
import Checkbox from '@root/common/components/ui/checkbox/index.vue'
import type { ISelectItem } from '@root/common/components/ui/select/index.vue'
import Select from '@root/common/components/ui/select/index.vue'
import { computed, ref, onUnmounted, onMounted } from 'vue'
import XMPMessage from '@root/common/components/ui/message/index'
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import LoadingSpinner from '@root/common/components/ui/loading-spinner/index.vue'
import ScanLogin from './ScanLogin.vue'
import { AccountHelper } from '@root/common/account/client/accountHelper';
import { PopUpNS } from '@root/common/pop-up';
import { ThunderHelper } from '@root/common/thunder-helper'
import { IGetVerificationResponseData } from '@xbase/electron_auth_kit'
import { ILoginHistory } from '@root/common/account/impl/login-options-define'

const emit = defineEmits<{
  (e: 'toRegister'): void
}>()

const tabs = [
  { name: '账号密码登录', value: 'account' },
  { name: '手机验证登录', value: 'phone' },
]

const curTab = ref(localStorage.getItem('XLLoginPrompt.lastLoginMode') || 'account')
// 账号密码登录
const username = ref('')
const password = ref('')
const historyUserList = ref<ISelectItem[]>([])

// 验证码登录
const phoneNumber = ref('')
const countryCode = ref('+86')
const verificationCode = ref('')
const countdown = ref(0)
const countdownTimer = ref<number | null>(null)
const verificationInfo = ref<IGetVerificationResponseData>()

// 登录公共状态
const autoLogin = ref(localStorage.getItem('XLLoginPrompt.autoLogin') === 'true')
const isScanLogin = ref(false)
const errorMessage = ref('')

const isLoading = ref(false)


const phoneNumberError = computed(() => {
  // 验证中国手机号码的正则表达式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(phoneNumber.value)) {
    return '手机格式不正确'
  }
  return ''
})

const canLogin = computed(() => {
  if (isLoading.value) {
    return false
  }
  if (curTab.value === 'account') {
    return username.value && password.value
  } else {
    return phoneNumber.value && verificationCode.value && !phoneNumberError.value
  }
})

const handleTabChange = (value: string) => {
  curTab.value = value
}

const handleClearAllHistory = async () => {
  await AccountHelper.getInstance().clearLoginHistory()
  historyUserList.value = []
}

const handleDeleteHistory = async (history: ISelectItem) => {
  await AccountHelper.getInstance().deleteLoginHistory({
    userName: history.value
  })
  historyUserList.value = historyUserList.value.filter((item: ISelectItem) => item.value !== history.value)
}

const handleForgetPassword = () => {
  ThunderHelper.openURLByDefault('https://i.xunlei.com/xluser/validate/findpwd_acc.html')
}

const handleUpdateAutoLogin = (value: boolean) => {
  console.log('handleUpdateAutoLogin', value)
  localStorage.setItem('XLLoginPrompt.autoLogin', value ? 'true' : 'false')
}

const handleLogin = async () => {
  if (!verificationInfo.value?.verification_id && curTab.value === 'phone') {
    errorMessage.value = '请先获取验证码'
    return
  }

  isLoading.value = true
  try {
    if (curTab.value === 'account') {
      const res = await AccountHelper.getInstance().signInWithPassword({
        password: password.value,
        username: username.value,
      })
      console.log('账号密码登录成功', res)
    } else {

      console.log('验证码登录参数', {
        verificationCode: verificationCode.value,
        verification_id: verificationInfo.value?.verification_id ?? '',
        isUser: verificationInfo?.value?.is_user ?? false
      })

      const res = await AccountHelper.getInstance().signInWithVerification({
        verificationCode: verificationCode.value,
        verification_id: verificationInfo.value?.verification_id ?? '',
        isUser: verificationInfo?.value?.is_user ?? false,
        username: `${countryCode.value} ${phoneNumber.value}`
      })
      console.log('验证码登录成功', res)
    }

    localStorage.setItem('XLLoginPrompt.lastLoginMode', curTab.value)

    const currentWindow = PopUpNS.getCurrentWindow();
    await currentWindow.close();
  } catch (e) {
    console.log('Login failed', JSON.stringify(e))
    const errorDesc: string = await AccountHelper.getInstance().translateResponseError(e as any);
    errorMessage.value = errorDesc
  } finally {
    isLoading.value = false
  }
}

const handleRegister = () => {
  emit('toRegister')
}

const handleLoginProblem = () => {
  ThunderHelper.openURLByDefault('https://help.xunlei.com/')
}

const handleLoginOther = (type: string) => {
  console.log('handleLoginOther', type)
  XMPMessage({
    type: 'warning',
    message: `暂未开放${type}登录功能`,
  })
}

const handleUserAgreement = () => {
  ThunderHelper.openURLByDefault('https://i.xunlei.com/xluser/legal/agreement.html')
}

const handlePrivacyAgreement = () => {
  ThunderHelper.openURLByDefault('https://i.xunlei.com/xluser/privacy.html')
}

const handleSwitchScan = () => {
  isScanLogin.value = !isScanLogin.value
}

const handleUpdateCountryCode = (value: string) => {
  countryCode.value = value
}

const handleUpdatePhoneNumber = (value: string) => {
  phoneNumber.value = value
}

const handleUpdateVerificationCode = (value: string) => {
  verificationCode.value = value
}

const handleGetVerificationCode = async () => {
  console.log('handleGetVerificationCode', phoneNumber.value)

  if (countdown.value > 0) {
    return
  }

  if (!phoneNumber.value) {
    errorMessage.value = '请先输入手机号'
    return
  }

  if (phoneNumberError.value) {
    errorMessage.value = phoneNumberError.value
    return
  }

  errorMessage.value = ''

  try {
    // 调用发送验证码接口
    verificationInfo.value = await AccountHelper.getInstance().sendSignInVerificationWithPhoneNumber(`${countryCode.value} ${phoneNumber.value}`)
    console.log('handleGetVerificationCode res', verificationInfo.value)
    // 开始倒计时
    countdown.value = 60 // 设置60秒倒计时
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
    }
    countdownTimer.value = window.setInterval(() => {
      if (countdown.value > 0) {
        countdown.value--
      } else {
        if (countdownTimer.value) {
          clearInterval(countdownTimer.value)
          countdownTimer.value = null
        }
      }
    }, 1000)
  } catch (e) {
    console.error('Failed to send verification code:', e)
    errorMessage.value = '发送验证码失败，请稍后重试'
  }
}

onMounted(async () => {
  const loginHistoryList = await AccountHelper.getInstance().getLoginHistoryList()
  console.log('loginHistoryList', loginHistoryList)

  if (loginHistoryList.length > 0) {
    const accountHistory = loginHistoryList.filter(item => item.mode === 'account')
    const phoneHistory = loginHistoryList.filter(item => item.mode === 'phone')

    if (accountHistory.length > 0) {
      username.value = accountHistory[0].userName
    }

    if (phoneHistory.length > 0) {
      const [code, phone] = phoneHistory[0].userName.split(' ')
      countryCode.value = code
      phoneNumber.value = phone
    }

    historyUserList.value = accountHistory.map((item: ILoginHistory) => ({
      name: item.userName,
      value: item.userName
    }))
  }
})

// 组件卸载时清除定时器 
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
})
</script>

<template>
  <div v-if="!isScanLogin" class="login-container">
    <div class="login-container-header draggable">
      <h1>欢迎登录迅雷</h1>
    </div>
    <Tabs :tabs="tabs" :defalutValue="curTab" @update:modelValue="handleTabChange" />
    <div class="login-container-body">
      <div v-if="curTab === 'account'" class="login-container-body-input ">
        <Select v-model="username" :clearable="true" :options="historyUserList" @on-clear="handleDeleteHistory"
          @on-clear-all="handleClearAllHistory" placeholder="请输入手机号/邮箱/账号" />
        <xl-input type="password" v-model="password" placeholder="请输入密码" @keydown.enter="handleLogin" />
      </div>
      <div v-if="curTab === 'phone'" class="login-container-body-input">
        <xl-input placeholder="请输入手机号" type="phone" v-model="phoneNumber" @update:countryCode="handleUpdateCountryCode"
          :countryCodes="[{ code: '+86', name: '中国大陆' }]" @update:modelValue="handleUpdatePhoneNumber" />
        <xl-input placeholder="请输入验证码" v-model="verificationCode" @update:modelValue="handleUpdateVerificationCode"
          @keydown.enter="handleLogin">
          <template #right>
            <span class="login-container-input-right-text" :class="{ 'login-container-text-click': countdown === 0 }"
              @click="handleGetVerificationCode">
              {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
            </span>
          </template>
        </xl-input>
      </div>
    </div>
    <div class="login-container-wrapper">
      <Checkbox v-model="autoLogin" label="pan" @update:modelValue="handleUpdateAutoLogin"><span
          class="login-container-text">下次自动登录</span></Checkbox>
      <span v-if="curTab === 'account'" class="login-container-text-click login-container-text"
        @click="handleForgetPassword">忘记密码？</span>
    </div>
    <div class="login-container-button">
      <div class="login-container-button-error" v-if="errorMessage">
        <i class="xl-icon-tips-warning"></i>
        <span>{{ errorMessage }}</span>
      </div>
      <Button style="width: 100%;" @click="handleLogin" variant="primary" size="lg" :disabled="!canLogin">
        <LoadingSpinner v-if="isLoading" size="small" color="var(--font-font-light, #FFFFFF)" />
        <span v-else>登录</span>
      </Button>
    </div>

    <div class="login-container-wrapper">
      <span class="login-container-text login-container-text-click" @click="handleLoginProblem">登录遇到问题？</span>
      <span class="login-container-text">没有账号？<span class="login-container-text-link"
          @click="handleRegister">立即注册</span></span>
    </div>

    <!-- <div class="login-container-login-other">
      <Tooltip side="top" showArrow contentClass="login-container-tip-content" arrowClass="login-container-tip-arrow">
        <template #trigger>
          <span class="login-container-login-other-icon" @click="handleLoginOther('weixin')">
            <inline-svg :src="require('@root/common/assets/img/ic_weixin.svg')" />
          </span>
        </template>
<template #content>
          <span>微信登录</span>
        </template>
</Tooltip>
<Tooltip side="top" showArrow contentClass="login-container-tip-content" arrowClass="login-container-tip-arrow">
  <template #trigger>
          <span class="login-container-login-other-icon" @click="handleLoginOther('qq')">
            <inline-svg :src="require('@root/common/assets/img/ic_qq.svg')" />
          </span>
        </template>
  <template #content>
          <span>QQ登录</span>
        </template>
</Tooltip>
<Tooltip side="top" showArrow contentClass="login-container-tip-content" arrowClass="login-container-tip-arrow">
  <template #trigger>
          <span class="login-container-login-other-icon" @click="handleLoginOther('weibo')">
            <inline-svg :src="require('@root/common/assets/img/ic_weibo.svg')" />
          </span>
        </template>
  <template #content>
          <span>微博登录</span>
        </template>
</Tooltip>
</div> -->

  </div>

  <ScanLogin v-else />

  <div :class="['login-footer', isScanLogin && 'login-footer-scan']">
    <span>登录即表示同意</span>
    <span class="login-footer-text-link login-container-text-click" @click="handleUserAgreement">《用户协议》</span>
    <span>及</span>
    <span class="login-footer-text-link login-container-text-click" @click="handlePrivacyAgreement">《隐私协议》</span>
  </div>

  <div class="login-switch">
    <Tooltip side="left" showArrow contentClass="login-container-tip-content" arrowClass="login-container-tip-arrow"
      v-if="isScanLogin">
      <template #trigger>
        <div class="login-switch-icon" @click="handleSwitchScan">
          <inline-svg :src="require('@root/common/assets/img/ic_switch_account.svg')" />
        </div>
      </template>
      <template #content>
        <span>账号登录</span>
      </template>
    </Tooltip>
    <Tooltip side="left" showArrow contentClass="login-container-tip-content" arrowClass="login-container-tip-arrow"
      v-else>
      <template #trigger>
        <div class="login-switch-icon" @click="handleSwitchScan">
          <inline-svg :src="require('@root/common/assets/img/ic_switch_scan.svg')" />
        </div>
      </template>
      <template #content>
        <span>扫码登录</span>
      </template>
    </Tooltip>
  </div>

</template>

<style scoped lang="scss">
.login-container {
  padding: 0 32px;

  &-header {
    height: 64px;
    position: relative;
    margin-bottom: 8px;
    margin-right: 16px;

    h1 {
      font-size: 24px;
      line-height: 32px;
      padding: 27px 32px 5px 0;
      color: var(--font-font-1, #272E3B);
      margin: 0;
    }
  }

  &-body {
    margin-top: 18px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &-body-input {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }


  &-input-right-text {
    color: var(--font-font-2, #4E5769);
    font-size: 14px;
    line-height: 22px;

    &:not(.login-container-text-click) {
      cursor: not-allowed;
      color: var(--font-font-4, #C9CDD4);
    }
  }

  &-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
  }

  &-text {
    font-size: 13px;
    line-height: 22px;
    color: var(--font-font-3, #86909C);
  }

  &-text-click {
    cursor: pointer;

    &:hover {
      color: var(--primary-primary-hover, #488BF7);
    }
  }

  &-text-link {
    color: var(--primary-primary-default, #226DF5);
    cursor: pointer;
  }

  &-button {
    padding-top: 32px;
    position: relative;
    width: 100%;

    &-error {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--functional-error-default, #FF4D4F);
      font-size: 11px;
      line-height: 14px;
      position: absolute;
      top: 9px;
      left: 0;
    }
  }

  &-login-other {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-top: 28px;

    &-icon {
      width: 36px;
      height: 36px;
      cursor: pointer;
    }
  }
}

.login-footer {
  position: absolute;
  bottom: 12px;
  left: 32px;
  width: 100%;
  display: flex;
  align-items: center;

  color: #949BA5;
  font-size: 12px;
  line-height: 20px;

  &-text-link {
    color: #6A707C;
  }

  &-scan {
    justify-content: center;
    left: 0;
  }
}

.login-switch {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60px;
  height: 60px;

  &-icon {
    width: 60px;
    height: 60px;
    cursor: pointer;
    opacity: 0.5;

    &:hover {
      opacity: 1;
    }
  }
}
</style>
<style lang="scss">
.login-container-tip-content {
  background: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
  height: 32px !important;
  padding: 0 12px !important;
  color: var(--white-white-900, #FFF) !important;
  line-height: 20px !important;
}

.login-container-tip-arrow {
  fill: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
}
</style>
