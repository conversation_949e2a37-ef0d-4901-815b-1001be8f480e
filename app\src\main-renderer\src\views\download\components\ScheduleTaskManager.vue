<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, computed, nextTick, reactive } from 'vue'
import Button from '@root/common/components/ui/button/index.vue'
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import { ScheduleTaskNS, ScheduleTaskType, ScheduleTaskEventType } from '@/common/schedule-task'
import XMPMessage from '@root/common/components/ui/message/index'

interface Props {
  open: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

const hours = ref<number>(0)
const minutes = ref<number>(0)
const selectedTask = ref<ScheduleTaskType>(ScheduleTaskType.StartAll)
const scheduleTasks = reactive<any[]>([])

// 倒计时定时器句柄
let countdownTimer: number | null = null

const taskOptions = [
  { label: '开始全部任务', value: ScheduleTaskType.StartAll },
  { label: '暂停全部任务', value: ScheduleTaskType.StopAll },
  { label: '关机', value: ScheduleTaskType.ShutDown },
  { label: '睡眠', value: ScheduleTaskType.Sleep },
  { label: '退出迅雷', value: ScheduleTaskType.Exit }
]

const selectedTaskLabel = computed(() => {
  const option = taskOptions.find(opt => opt.value === selectedTask.value)
  return option ? option.label : '开始全部任务'
})

// 监听计划任务数量变化
const eventEmitter = ScheduleTaskNS.getEventEmitter()
// 添加事件监听
const taskEmitListener  = (task: any) => {
  console.log('>>>>>>>>>>>>>>>>>> taskEmitListener', task)
}

onMounted(() => {
  // 获取当前的计划任务列表
  refreshTaskList()
  
  eventEmitter.on(ScheduleTaskEventType.ScheduleTaskEmit, taskEmitListener)

  // 启动倒计时定时器，每秒更新剩余时间
  countdownTimer = window.setInterval(() => {
    refreshTaskList()
  }, 1000)
})

onBeforeUnmount(() => {
  // 移除事件监听
  if (taskEmitListener) {
    eventEmitter.off(ScheduleTaskEventType.ScheduleTaskEmit, taskEmitListener)
  }
  // 清除倒计时定时器
  if (countdownTimer !== null) {
    clearInterval(countdownTimer)
  }
})

const refreshTaskList = async () => {
  let t = ScheduleTaskNS.getPlanTasks();
  scheduleTasks.splice(0, scheduleTasks.length)
  scheduleTasks.push(...t)
}

const handleClose = () => {
  emit('close')
}

const handleAddTask = () => {
  if (hours.value === 0 && minutes.value === 0) {
    return
  }
  
  const task = {
    hours: hours.value,
    minutes: minutes.value,
    seconds: 0,
    optType: selectedTask.value,
    id: 0, // 会被覆盖
  }
  
  ScheduleTaskNS.addPlanTask(task)

  refreshTaskList()

  // 重置表单，但不关闭弹窗
  resetForm()
  
  XMPMessage({
    message: '计划任务添加成功',
    type: 'success'
  })
}

const resetForm = () => {
  hours.value = 0
  minutes.value = 0
  selectedTask.value = ScheduleTaskType.StartAll
}

const handleSelectTask = (task: ScheduleTaskType) => {
  selectedTask.value = task
}

const isValid = computed(() => {
  return hours.value > 0 || minutes.value > 0
})

const handleHoursChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  const value = parseInt(target.value)
  if (!isNaN(value) && value >= 0 && value <= 999) {
    hours.value = value
  }
}

const handleMinutesChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  const value = parseInt(target.value)
  if (!isNaN(value) && value >= 0 && value <= 59) {
    minutes.value = value
  }
}

const handleDeleteTask = (id: number) => {
  ScheduleTaskNS.deletePlanTask(id)
  refreshTaskList()
  // nextTick(() => {
  //   refreshTaskList()
  // })
  
  XMPMessage({
    message: '计划任务已删除',
    type: 'success'
  })
}

const getTaskTypeName = (type: ScheduleTaskType): string => {
  switch (type) {
    case ScheduleTaskType.StartAll:
      return '开始全部任务'
    case ScheduleTaskType.StopAll:
      return '暂停全部任务'
    case ScheduleTaskType.ShutDown:
      return '关机'
    case ScheduleTaskType.Sleep:
      return '睡眠'
    case ScheduleTaskType.Exit:
      return '退出迅雷'
    default:
      return '未知操作'
  }
}

const handleRightIconClick = (e: MouseEvent) => {
  // 打开下拉菜单, 右侧icon点击时被阻止了
  console.log('>>>>>>>>>>>>>>>>>> handleRightIconClick')
  const parentElement = (e.target as HTMLElement).parentElement
  parentElement?.click()
}

</script>

<template>
  <Dialog :open="props.open" @close="handleClose" title="计划任务" width="680px" :showActions="false" :draggable="false">
    <div class="schedule-task-dialog">
      <div class="schedule-task-header">
        <div class="schedule-task-time">
          <div class="time-input-wrapper">
            <input 
              type="number"
              v-model="hours"
              @input="handleHoursChange"
              min="0"
              max="999"
              class="time-input"
            />
          </div>
          <span class="time-label">时</span>
          <div class="time-input-wrapper">
            <input 
              type="number" 
              v-model="minutes" 
              @input="handleMinutesChange" 
              min="0" 
              max="59" 
              class="time-input"
            />
          </div>
          <span class="time-label">分</span>
          <span class="time-label after-label">后</span>
        </div>
        
        <div class="schedule-task-action">
          <div class="dropdown-wrapper">
            <DropdownMenu
              :items="taskOptions.map(opt => ({ key: opt.value.toString(), label: opt.label }))"
              side="bottom"
              align="start"
              @select="(key) => handleSelectTask(Number(key))"
            >
              <!-- 点击按钮时，都触发下拉菜单 -->
              <Button variant="outline" size="default" has-right-icon @right-icon-click="handleRightIconClick">
                {{ selectedTaskLabel }}
              </Button>
            </DropdownMenu>
          </div>
          
          <Button
            class="add-task-btn"
            variant="default" 
            size="default" 
            has-left-icon
            left-icon="xl-icon-add"
            @click="handleAddTask"
            :disabled="!isValid"
          >
            添加计划
          </Button>
        </div>
      </div>
      
      <!-- 任务列表 -->
      <div  v-if="scheduleTasks.length > 0" class="schedule-task-list">
          <div class="schedule-task-item" v-for="task in scheduleTasks" :key="task.id">
            <div class="task-info">
              <div class="task-time-info">
                <span class="task-time-value">{{ task.hours }}</span>
                <span class="task-time-unit">小时</span>
                <span class="task-time-value">{{ task.minutes }}</span>
                <span class="task-time-unit">分钟</span>
                <span class="task-time-value">{{ task.seconds }}</span>
                <span class="task-time-unit">秒</span>
                <span class="task-time-label">后</span>
                <span class="task-action-name">{{ getTaskTypeName(task.optType) }}</span>
              </div>
            </div>
            <Button 
              variant="ghost" 
              is-icon 
              size="sm" 
              @click="handleDeleteTask(task.id)"
              class="delete-btn"
            >
              <i class="xl-icon-general-delete-l"></i>
            </Button>
          </div>
        </div>
    </div>
  </Dialog>
</template>

<style lang="scss" scoped>

.schedule-task-dialog {
  width: 632px;
  display: flex;
  flex-direction: column;
}

.schedule-task-header {
  padding: 10px 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.schedule-task-time {
  display: flex;
  align-items: center;
}

.time-input-wrapper {
  width: 52px;
  height: 32px;
  border-radius: var(--border-radius-S, 6px);
  background: var(--button-button2-default, #F2F3F5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-input {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  font-size: 13px;
  color: var(--font-font-2, #4E5769);
  text-align: center;
  
  &:focus {
    outline: none;
  }
  
  /* 移除输入框上下箭头 */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  
  /* Firefox */
  -moz-appearance: textfield;
}

.time-label {
  color: var(--font-font-3, #86909C);
  font-size: 14px;
  margin: 0 20px;
}

.after-label {
  margin: 0 20px;
  color: var(--font-font-2, #4E5769);
}

.schedule-task-action {
  display: flex;
  align-items: center;
}

.dropdown-wrapper {
  width: 150px;
  height: 40px;
  margin-right: 12px;
  
  :deep(.dropdown-menu-trigger) {
    width: 100%;
  }
  
  :deep(.button) {
    width: 100%;
    justify-content: space-between;
  }
}


.schedule-task-list {
  width: 680px;
  margin: 0 -24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 240px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 24px;
  box-sizing: border-box;
}

.schedule-task-item {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.task-info {
  width: 580px;
  height: 40px;
  border-radius: var(--border-radius-M, 8px);
  border: 1px solid var(--border-border-2, #E5E6EB);
  display: flex;
  align-items: center;
}

.task-time-info {
  display: flex;
  align-items: center;
  gap: 9px;
  color: var(--font-font-2, #4E5769);
  font-size: 14px;
}

.task-time-value {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 22px;
}

.task-time-unit {
  text-align: center;
  color: var(--font-font-3, #86909C);
  font-size: 13px;
}
.task-time-label {
  display: flex;
  width: 28px;
  margin-left: 27px;
  margin-right: 0;
  color: var(--font-font-2, #4E5769);
  font-size: 14px;
}

.task-action-name {
  margin-left: 108px;
  color: var(--font-font-2, #4E5769);
}

.delete-btn {
  width: 20px;
  height: 20px;
  color: var(--font-font-2, #4E5769);
}

:deep(.add-task-btn) {
  width: 106px;
  height: 40px;
}
</style> 