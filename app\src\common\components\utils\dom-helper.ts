
export class DomHelper {
  /**
 * 判断触发事件时，触发路径上是否包含指定类名
 * @param event 事件
 * @param cls 类名
 * @returns true / false
 */
  static eventContainClass (event: Event, cls: string) {
    const path = event.composedPath().map((el: any) => el.className).filter(p => typeof p === 'string').filter(Boolean)
    const isContain = (cls: string) => path.some(p => p.split(' ').includes(cls))

    return isContain(cls)
  }
}
