/**
 * @description: icon管理模块
 */

import { ApplicationManager } from '@root/common/thunder-pan-manager/manager/application';

class IconManager {
  private hasInit: boolean = false;
  private iconData: { [ext: string]: string } = {};

  private async init() {
    if (!this.hasInit) {
      await this.request();
      this.hasInit = true;
    }
  }

  private async request() {
    // 后面可能需要加熊盾
    const url = 'https://api-drive-dev.office.k8s.xunlei.cn/iconhub/v1/config';

    const input: RequestInit = {
      mode: 'cors',
      headers: {
        'content-type': 'application/json',
        'x-client-id': ApplicationManager.getCurrentDeviceClientId(),
      }
    };

    let res: Response | null = null;
    try {
      res = await fetch(url, input);
    } catch (error) {
      //
    }

    if (res?.ok && res?.status == 200) {
      const res_data: {
        icons: { [ext: string]: string};
        file_categories: any
      } = await res.json();
      if (res_data) {
        this.iconData = res_data?.icons;
      }
    }
  }

  public async getLinkFileIcon(fileType: string): Promise<string> {
    await this.init();
    if (!fileType)
      return '';
    return this.iconData?.[fileType] ?? '';
  }
}

const iconManager: IconManager = new IconManager();
export default iconManager;