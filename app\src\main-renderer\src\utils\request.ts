import { AccountHelper } from '@root/common/account/impl/accountHelper'
import { ApplicationManager } from '@root/common/thunder-pan-manager/manager/application'
import { CryptoUtility } from '@xbase/electron_base_kit'
// import { useUserStore } from '@root/main-renderer/src/stores/user'

export class RequestHelper {
  private static _instance: RequestHelper
  private deviceId = ''
  private appVersionCode = ''
  private userId = ''
  // private userStore = useUserStore()
  private commonData: Record<string, any> = {}

  private constructor() {}

  static getInstance() {
    if (!RequestHelper._instance) {
      RequestHelper._instance = new RequestHelper()
    }
    return RequestHelper._instance
  }

  async ensureUserInfo() {
    // 优先从pinia拿
    // this.userId = this.userStore.userInfo?.sub || ''
    if (!this.userId) {
      const userInfo = await AccountHelper.getInstance().getUserInfo()
      this.userId = userInfo?.sub || ''
      // this.userStore.setUserInfo(userInfo)
    }
    if (!this.deviceId) {
      this.deviceId = await AccountHelper.getInstance().getDeviceID()
    }
    if (!this.appVersionCode) {
      this.appVersionCode = ApplicationManager.getCurrentDeviceClientVersion()
    }
    this.updateCommonData()
  }

  updateCommonData() {
    const secret = "!@#$%^&*()QAZ"
    const method = 'GET'
    const client_name = 'xl_pc_web'
    const client_version = 0
    const ts = Date.now()
    const r = this.getRandomNum(1, 100) + ''
    const str = secret + method.toUpperCase() + client_name + client_version + ts + r
    const key = CryptoUtility.createMDAlgorithms().MD5StringSync(str)
    this.commonData = {
      device_id: this.deviceId,
      client_name,
      client_version,
      ts,
      r,
      key
    }
  }

  getCommonData() {
    return this.commonData
  }

  getUserId() {
    return this.userId
  }

  getDeviceId() {
    return this.deviceId
  }

  getAppVersionCode() {
    return this.appVersionCode
  }

  getRandomNum(Min: number, Max: number) {
    const Range = Max - Min
    const Rand = Math.random()
    return (Min + Math.round(Rand * Range))
  }

  concatSearchParamsToUrl(url: string, params: Record<string, string>) {
    const newUrl = new URL(url)
    if (params) {
      Object.keys(params).forEach(key => {
        newUrl.searchParams.append(key, params[key])
      })
    }
    return newUrl.href
  }

  async request(options: any) {
    await this.ensureUserInfo()
    let { method, url, body, headers, withCredentials } = options
    if (method === 'GET') {
      url = this.concatSearchParamsToUrl(url, body)
      console.log('>>>>>>>  url', url)
    }
    const params = {
      method,
      url,
      body: body,
      headers,
      withCredentials
    }
    const res = await AccountHelper.getInstance().request(params)
    return res.toJSON()
  }
}

export const requestHelper = RequestHelper.getInstance() 