// 版本号-> version_code

function getVersionCode(v) {
    let code = 0;
    let version = v.split('.');
    if (version && version.length === 4) {
      let v1 = Number(version[0]).valueOf();
      let v2 = Number(version[1]).valueOf();
      let v3 = Number(version[2]).valueOf();
      let v4 = Number(version[3]).valueOf();
      let productType = 0x80;
      // code = productType * Math.pow(2, 24) + v1 * Math.pow(2, 16) + v2 * Math.pow(2, 8) + v3;
      code = productType * Math.pow(2, 40) + v1 * Math.pow(2, 32) + v2 * Math.pow(2, 24) + v3 * Math.pow(2, 16) + v4;
    }
    return code;
}

getVersionCode('12.0.8.4500');

// 灰度请求
curl --location --request POST 'https://sl-canary-m.xunlei.com/app/upgrade/v2/check' \
--header 'Content-Type: application/json' \
--data-raw '{
    "app_id": 20001,
    "channel_id": "100001",
    "device_id": "509A4C0FA589J83Q",
    "version_code": 2148270086,
    "package_name": "thunder_pc",
    "version_name": "12.0.6.6890",
    "last_version_code": 2148270086,
    "sign": "a3a94ced309e38cd436c3d40ca4eafc6"
}'


curl --location --request POST 'https://sl-canary-m.xunlei.com/app/upgrade/v2/check' \
--header 'Content-Type: application/json' \
--data-raw '{"app_id":20001,"channel_id":"11","device_id":"509A4C0FA589J83Q","version_code":2148270086,"package_name":"thunder_pc","version_name":"12.0.6.6890","last_version_code":2148270086,"sign":"69fcb1c6411493bee3c06d5a7f9bd101"}'


// 获取升级压缩包的文件md5信息
```js
fs = require('fs');
path = require('path');
async function readdirAW(dirName) {
  let ret = null;
  if (dirName !== undefined) {
    try {
      ret = await fs.promises.readdir(dirName);
    } catch (err) {
      console.warn(err);
    }
  }
  return ret;
}

async function readdirsAW(dirName, pre) {
  let ret = [];
  if (dirName) {
    let files = await readdirAW(dirName);
    for (let i = 0; i < files.length; i++) {
      let file = files[i];
      let fileName = path.join(dirName, file);
      let stats = await fs.promises.stat(fileName)
      if (stats.isFile() || fileName.endsWith('.asar')) {
        if (pre) {
          ret.push(path.join(pre, file));
        } else {
          ret.push(file);
        }
      } else if (stats.isDirectory()) {
        if (pre) {
          let subs = await readdirsAW(fileName, path.join(pre, file));
          ret.push(...subs);
        } else {
          let subs = await readdirsAW(fileName, file);
          ret.push(...subs);
        }
      }
    }
  }

  return ret;
}

function needSign(name) {
  let need = true;
  do {
    if (name.endsWith('.dll')) {
      break;
    }

    if (name.endsWith('.node')) {
      break;
    }

    if (name.endsWith('.exe')) {
      break;
    }

    if (name.endsWith('.bin')) {
      break;
    }

    if (name.endsWith('.xta')) {
      break;
    }

    need = false;
  } while (0);
  return need;
}


dirName = await __xdasIPCClienInstance.callServerFunction('ShowOpenDialog');

if (dirName?.[0]) {
  const saveName = 'manifest.json';
  let files = await readdirsAW(dirName[0]);
  let md5Arr = []
  for (let i = 0; i < files.length; i++) {
    const name = files[i]?.toLowerCase();
    if (!name || name === saveName) {
      continue;
    }

    if (!needSign(name)) {
      continue;
    }

    let file = path.join(dirName[0], files[i]);
    const md5 = await __xdasIPCClienInstance.callServerFunction('GetFileMd5', file);
    if (md5) {
      md5Arr.push({ file: files[i], hash: md5 });
    }
  }

  // 弹出保存对话框
  // const savePath = await __xdasIPCClienInstance.callServerFunction('ShowSaveDialog', {
  //  defaultPath: 'sign',
  //  filters: [{ name: 'JSON(*.json）', extensions: ['json'] }]
  // });
  const savePath = path.join(dirName[0], saveName);
  if (savePath) {
    await fs.promises.writeFile(savePath, JSON.stringify(md5Arr, undefined, 2));

    window?.alert?.('done');
  }
}

```js



