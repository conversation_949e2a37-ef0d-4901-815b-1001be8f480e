<script setup lang="ts">
import { ref } from 'vue'
import Select from '@root/common/components/ui/select/index.vue'
import Tabs from '@root/common/components/ui/tabs/index.vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import RadioGroup from '@root/common/components/ui/radio-group/index.vue'
import CheckboxGroup from '@root/common/components/ui/checkbox-group/index.vue'

const tabs = [{
  name: '账号密码登录',
  value: 'account'
}, {
  name: '手机验证码登录',
  value: 'phone'
}]


const dropdownMenuList2 = [
  {
    key: 'play',
    label: '播放',
  },
  {
    key: 'download',
    label: '下载',
  },
]

const radioOptions = ref([{ label: '播放', value: 'play' }, { label: '下载', value: 'download' }])

const radioOptions2 = ref([
  {
    label: 'tt',
    value: 'tt',
    tip: 'tt',
    action: { type: 'button' as const, label: '修改配置' }
  },
  {
    label: 'tt2',
    value: 'tt2',
    tip: 'tt2',
    action: { type: 'button' as const, label: 'tt2' }
  }
])

const checkboxOptions = ref([
  { label: 'Soccer', value: 'soccer', },
  { label: 'Badminton', value: 'badminton', action: { type: 'input' as const, width: 160, value: '123' } },
  { label: 'Basketball', value: 'basketball', action: { type: 'input' as const, width: 100, value: '123' } },
])

const checkboxValue = ref([])

const password = ref('')
const select = ref('')
const selectOptions = ref([{ name: 'apple', value: 'apple' }, { name: 'apple1', value: 'apple1' }, { name: 'apple2', value: 'apple2' }])
const handlePassword = (value: string) => {
  console.log(value)
  password.value = value
}

const phoneNumber = ref('')
const countryCode = ref('+86')

const radioValue = ref('')
const radioValue2 = ref('')

const handleSelect = (value: string) => {
  console.log(value)
  select.value = value
}

const handleClear = (option) => {
  selectOptions.value = selectOptions.value.filter(item => item.value !== option.value)
}

const handleUpdateCountryCode = (value: string) => {
  console.log('handleUpdateCountryCode', value)
  countryCode.value = value
}

const handleUpdatePhoneNumber = (value: string) => {
  console.log('handleUpdatePhoneNumber', value)
  phoneNumber.value = value
}

const handleUpdateRadioValue = (value: string) => {
  console.log('handleUpdateRadioValue', value)
  radioValue.value = value
}

const handleUpdateRadioValue2 = (value: string) => {
  console.log('handleUpdateRadioValue2', value)
  radioValue2.value = value
}

const handleAction = (actionType: string, optionValue: string, actionValue?: any) => {
  console.log('Action triggered:', { actionType, optionValue, actionValue })
}

</script>

<template>
  <xl-input />
  <xl-input type="password" v-model="password" placeholder="请输入密码" @update:modelValue="handlePassword" />

  <xl-input>
    <template #right>
      <i class="xl-icon-general-direction-caret-down-s"></i>
    </template>
  </xl-input>



  <Select v-model="select" :clearable="true" :options="selectOptions" @update:modelValue="handleSelect"
    @on-clear="handleClear" @on-clear-all="selectOptions = []" />

  <Tabs :tabs="tabs" :defalutValue="tabs[1].value" />
  <xl-input type="phone" v-model="phoneNumber" @update:countryCode="handleUpdateCountryCode"
    :countryCodes="[{ code: '+86', name: '中国大陆' }, { code: '+852', name: '中国香港' }]"
    @update:modelValue="handleUpdatePhoneNumber" />

  <RadioGroup :options="radioOptions" defaultValue="play" orientation="horizontal" v-model="radioValue" title="登录方式" />

  {{ radioValue }}

  <RadioGroup :options="radioOptions2" defaultValue="tt" orientation="vertical" v-model="radioValue2"
    @action="handleAction" title="目录与缓存" />

  <CheckboxGroup :options="checkboxOptions" orientation="vertical" v-model="checkboxValue" title="选择" />

  {{ checkboxValue.join(',') }}

</template>