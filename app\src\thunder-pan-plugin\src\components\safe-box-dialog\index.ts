import { ComponentInternalInstance, createVNode, render, shallowReactive, VNode } from 'vue'
import DialogConstructor from './component.vue'

let seed = 1

export interface DialogHandler {
  close: () => void
}

export type DialogContext = {
  id: string
  vnode: VNode
  handler: DialogHandler
  vm: ComponentInternalInstance
  props: any
}

export interface ISafeBoxDialogOptions {
  showType?: 'init' | 'reset'
  phoneNumber: string
  onResolve: () => void
  onClose?: () => void
}

export const instances: any[] = shallowReactive([])

export const getInstance = (id: string) => {
  const idx = instances.findIndex((instance) => instance.id === id)
  const current = instances[idx]
  let prev: DialogContext | undefined
  if (idx > 0) {
    prev = instances[idx - 1]
  }
  return { current, prev }
}

const closeDialog = (instance: DialogContext) => {
  const idx = instances.indexOf(instance)
  if (idx === -1) return

  instances.splice(idx, 1)
  const { handler } = instance
  handler.close()
}

const createDialog = (options: ISafeBoxDialogOptions): DialogContext => {
  const id = `safe-box_dialog_${seed++}`
  const container = document.createElement('div')

  const props = {
    ...options,
    id,
    onClose: () => {
      if (options.onClose) {
        options.onClose()
      }
      closeDialog(instance)
    },
    onDestroy: () => {
      render(null, container)
    },
  }

  const vnode = createVNode(DialogConstructor, props)
  render(vnode, container)
  document.body.appendChild(container.firstElementChild!)

  const vm = vnode.component!
  const handler = {
    close: () => {
      vm.exposed!.visible.value = false
    },
  }

  const instance = {
    id,
    vnode,
    vm,
    handler,
    props: (vnode.component as any).props,
  }

  return instance
}

export const CreateSafeBoxDialog = (options: ISafeBoxDialogOptions) => {
  const instance = createDialog(options)

  instances.push(instance)
  return instance.handler
}

export const closeAllSafeBoxDialog = () => {
  instances.forEach(instance => {
    instance.handler.close()
  })
}
