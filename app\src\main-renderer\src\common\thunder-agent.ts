/**
 * 处理命令行，协议等
 */
import * as path from 'path';
import { Logger } from '@root/common/logger';
import { config } from '@root/common/config/config';
import * as DownloadKernel from '@root/common/task/base';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { ThunderHelper } from '@root/common/thunder-helper';
import { URLHelperNS } from '@root/common/task/client/url-helper';
import { DownloadPathNS } from '@root/common/config/download-path';
import { ParseUrlFileNameNS } from "@root/common/task/client/parse-helper";
import { DkThunderHelper } from '@root/common/task/client/dk-thunder-helper';
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper';

const logger = new Logger({ tag: 'command_line' })

/** 去掉首尾的引号 */
function padQuot(temp: string): string {
  let ret: string = temp;
  if (temp.indexOf('"') === 0 && temp.lastIndexOf('"') === temp.length - 1) {
    ret = temp.substring(1, temp.length - 1);
  } else if (temp.indexOf('\'') === 0 && temp.lastIndexOf('\'') === temp.length - 1) {
    ret = temp.substring(1, temp.length - 1);
  }
  return ret;
}

/**
 * @description 解析命令行，类似于CommandLineToArgvW
 */
export function parseCommandLine(cmdline: string): string[] {
  let tokenBegin: number = 0;
  let quot: string = '';
  let bQuotING: boolean = false; // 是否已经遍历到一个引号
  const ret: string[] = [];
  const len: number = cmdline.length;
  for (let i: number = 0; i < len; i++) {
    const char: string = cmdline[i];
    if (char === '"' || char === '\'') {
      if (quot === '') {
        bQuotING = true;
        quot = char;
      } else if (quot === char) {
        bQuotING = false;
        quot = '';
      }
    }

    // 如果没有遇到引号，遇到了空格，则分隔参数
    if (char === ' ' && !bQuotING && i !== len - 1) {
      const temp: string = cmdline.substring(tokenBegin, i);
      if (temp.trim() !== '') {
        ret.push(padQuot(temp));
      }
      tokenBegin = i + 1;
    } else if (i === len - 1) {
      const temp: string = cmdline.substring(tokenBegin);
      if (temp.trim() !== '') {
        ret.push(padQuot(temp));
      }
    }
  }
  return ret;
}

async function isWhiteList(url: string): Promise<boolean> {
  let bis: boolean = false;
  do {
    if (!url) {
      break;
    }

    let urlObj: URL | null = null;
    try {
      urlObj = new URL(url);
    } catch (error) {
      //
    }

    if (!urlObj) {
      break;
    }

    const hostList: string[] = await config.getStaticConfigValue('thunderLink', 'whiteList');
    if (!hostList?.length) {
      break;
    }
    for (let host of hostList) {
      if (host.toLowerCase() === urlObj?.host.toLowerCase()) {
        bis = true;
        break;
      }
    }
  } while (0);
  return bis;
}

// 拼装新建面板的 ExtendData
async function getExtendData(taskExtendInfo: {
  taskgroupname?: string;
  taskgroupicon?: string;
  downloaddir?: string;
  installfile?: string;
  excludepath?: string;
  runparams?: string;
  useragent?: string;
  threadcount?: string;
}, hideYunPan: string, installFileUrl: string, createShortcut: ThunderNewTaskHelperNS.ICreateShortcut | null, shortcutTargetFileUrl: string): Promise<any> {
  const userData: any = {};
  if (taskExtendInfo.downloaddir) {
    // 获取一个默认最大磁盘空间的磁盘
    let driver: string = await DownloadPathNS.getSafetyPath(); // todo GetMaxFreeDriver()
    driver = driver.substring(0, 1);
    driver += ':';
    const downloadDir = path.join(driver, taskExtendInfo.downloaddir);
    userData.isNotSavePath = true;
    userData.notSavePath = downloadDir;
  }
  if (taskExtendInfo.threadcount) {
    let threadCount = Number(taskExtendInfo.threadcount);
    if (!isNaN(threadCount)) {
      if (threadCount > 10) {
        threadCount = 10;
      } else if (threadCount < 1) {
        threadCount = 1;
      }
      userData.threadCount = threadCount;
    }
  }

  if (taskExtendInfo.useragent) {
    userData.userAgent = taskExtendInfo.useragent;
  }
  if (taskExtendInfo.excludepath) {
    userData.excludePath = taskExtendInfo.excludepath;
  }
  userData.hideYunPan = hideYunPan;
  // 这里在做层限制，假如taskGroupName没有设置值的话，直接不做安装操做
  if (taskExtendInfo.taskgroupname) {
    if (taskExtendInfo.installfile) {
      // 这里再判断下是否在白名单里面
      if (installFileUrl && (await isWhiteList(installFileUrl))) {
        userData.installFile = taskExtendInfo.installfile.replace(/[*?:|<>"]/g, '_');
        if (taskExtendInfo.taskgroupicon) {
          userData.taskGroupIcon = taskExtendInfo.taskgroupicon;
        }
        userData.runParams = taskExtendInfo.runparams;
      }
    } else if (taskExtendInfo.taskgroupicon && (await isWhiteList(taskExtendInfo.taskgroupicon))) {
      userData.taskGroupIcon = taskExtendInfo.taskgroupicon;
    }

    if (createShortcut?.name && createShortcut.targetFile) {
      if (await isWhiteList(shortcutTargetFileUrl)) {
        userData.createShortcut = createShortcut;
      }
    }
  }
  return userData;
}

function getRealDir(dir: string): string {
  let realDir: string = path.normalize(dir);
  realDir = realDir.replace(/[*?/:|<>"]/g, '');
  realDir = path.normalize(realDir);
  return realDir;
}

/** 用于联盟过滤url获取下载路径 */
async function getSubDir(url: string, excludePath: string): Promise<string> {
  let subDir: string = '';
  do {
    if (!url || !excludePath) {
      break;
    }

    let parseUrl: string = url;
    if (await ParseUrlFileNameNS.isThunderPrivateUrl(url)) {
      parseUrl = await ParseUrlFileNameNS.parseThunderPrivateUrl(url);
    }

    if (!parseUrl) {
      break;
    }

    // 先查找对应的截断点
    const npos: number = parseUrl.indexOf(excludePath);
    if (npos === -1) {
      break;
    }

    // 截断前面的
    const destStr: string = parseUrl.substring(npos + excludePath.length);
    if (!destStr) {
      break;
    }
    // 去掉文件名
    const dirname: string = path.dirname(destStr);
    // 把dirname 通过/分割，再把分割后的特俗字符干掉，然后再组装
    subDir = getRealDir(dirname);
  } while (0);
  return subDir;
}

async function getInstallFileUrl(url: string, installFile: string, taskName: string): Promise<string> {
  let resultUrl: string = '';
  do {
    if (!url || !installFile) {
      break;
    }

    // 调整代码实现，如果已经传递了文件名则匹配后再解析磁力链：针对大型游戏任务组，耗时有提升
    if (taskName) {
      if (taskName.toLowerCase() === path.basename(installFile)?.toLowerCase()) {
        if (await ParseUrlFileNameNS.isThunderPrivateUrl(url)) {
          resultUrl = await ParseUrlFileNameNS.parseThunderPrivateUrl(url);
        } else {
          resultUrl = url;
        }
      }
      break;
    }

    let parseUrl: string = url;
    if (await ParseUrlFileNameNS.isThunderPrivateUrl(url)) {
      parseUrl = await ParseUrlFileNameNS.parseThunderPrivateUrl(url);
    }
    if (!parseUrl) {
      break;
    }
    // 解析的url里面是否含有对应的文件名
    if (path.basename(parseUrl) === path.basename(installFile)) {
      resultUrl = parseUrl;
    }
  } while (false);
  return resultUrl;
}

class ThunderAgentHanlder {
  private uploadfiles: string[] = [];
  private delayUploadTimerId: number | undefined = undefined;
  
  private async onCommand(key: string, value: string, source: string, commandsObj?: { [key: string]: string }): Promise<void> {
    logger.log('key', key, 'value', value);
    if (key === 'path') {
      let ext: string = path.extname(value);
      ext = ext.toLowerCase();
      if (ext === '.torrent') {
        source = source === 'command' ? 'Torrent' : source;
        // todo 创建BT任务
      } else if (ext === '.td' || ext === '.bc!' || ext === '.part' || ext === '.xltd') {
        // todo 导入未完成任务
        source = source === 'command' ? 'TD' : source;
      } else if (ext === 'xlb') {
        // ViewFile
      } else if (commandsObj && ((commandsObj['hunderx'] && commandsObj['hunderx'].length > 2) || (commandsObj['StartType'] && commandsObj['StartType'].toLowerCase() === 'thunderx'))) { // 这个逻辑在最后
        // 外部通过thunderx协议拉起迅雷并进行相应操作
        do {
          let thunderxData = commandsObj['hunderx']; // 注意这里就是hunder，因为上面在解析的时候把thunderx的t当分隔符了
          if (!thunderxData || thunderxData.length <= 2) { // 至少里面有双斜杠
            break;
          }

          await this.onThunderxOpt('thunderx:' + thunderxData);
        } while (false)
      }
    } else if (key === 'task') {
      if (value === 'newtask') {
        // todo 拉起新建面板
      } else if (value === 'startall') {
        // todo 开始全部任务
      } else if (value === 'pauseall') {
        // todo 暂停全部任务
      }
    } else if (key === 'shell') {
      if (value === 'exit') {
        // todo 退出迅雷
      }
    } else if (key === 'speedMode') {
      // todo 限速/取消限速 value = 1 / 2
    } else if (key === 'unionfile') {
      // 联盟创建任务
      const tempFilePath: string = value;
      this.createUnionTask(tempFilePath);
    } else if (key.toLowerCase() === 'shellupload') {
      // 由于多选会分开多次命令行拉起迅雷，所以这里做个防抖
      this.delayUpload(value);
    }
  }

  private async onThunderxOpt(thunderxUrl: string): Promise<void> {
  }

  private delayUpload(file: string): void {
    this.uploadfiles.push(file);
    if (this.delayUploadTimerId) {
      clearInterval(this.delayUploadTimerId);
      this.delayUploadTimerId = undefined;
    }

    this.delayUploadTimerId = setTimeout(() => {
      // todo 上传到云盘
      this.uploadfiles = [];
    }, 300) as any;
  }

  private async executeCmdLine(args: string[]): Promise<void> {
    let play: boolean = false;
    let unionfile: string | undefined = undefined;
    let yunFetchBack: boolean = false;
    for (let i: number = 0; i < args.length; ++i) {
      if (!args[i]) {
        continue;
      }
      if (args[i].indexOf('play:true') > -1) {
        play = true;
      } else if (args[i].indexOf('yunfetchback') > -1) {
        yunFetchBack = true;
      } else if (args[i].indexOf('unionfile') > -1) {
        unionfile = args[i];
      }
      
      logger.log('executeCmdLine', play, yunFetchBack, unionfile);
      if (play && unionfile) {
        // todo createUnionPlayTasks
        return;
      } else if (yunFetchBack && unionfile) {
        // todo createYunFetchBackTask
        return;
      }

      const commandsObj: { [key: string]: string } = {};
      for (const arg of args) {
        if (arg === '') {
          continue;
        }

        let key: string = '';
        let value: string = '';

        // 判断是否路径
        if (await FileSystemAWNS.existsAW(arg)) {
          key = 'path';
          value = arg;
        } else if (URLHelperNS.isSupportUrl(arg)) {
          // 判断是否url
          key = 'url';
          value = arg;
        } else {
          //  形如-task:startall，得到key: task value: startall
          const index: number = arg.indexOf(':');
          if (index === -1) {
            key = arg;
          } else if (index > 1) {
            key = arg.substring(1, index);
            if (index !== arg.length - 1) {
              value = arg.substring(index + 1);
              const regExp: RegExpMatchArray | null = value.match(/^"(.*)"$/);
              if (regExp && regExp.length > 0) {
                value = regExp[1];
              }
            }
          }
        }
        commandsObj[key] = value;
      }

      for (let key in commandsObj) {
        let value: string = commandsObj[key];
        this.onCommand(key, value, 'command', commandsObj).catch();
      }
    }
  }

  private commandHandler(command: string): void {
    logger.log('on command handler', command);
    do {
      command = command?.trim();
      if (!command) {
        break;
      }

      const args: string[] = parseCommandLine(command);
      if (!args?.length) {
        break;
      }

      this.executeCmdLine(args);
    } while (0);
  }

  private async taskHandler(newtasklist: ThunderNewTaskHelperNS.INewTaskData[]): Promise<void> {
    do {
      if (!newtasklist?.length) {
        break;
      }
      if (newtasklist?.length === 1) {
        const task = newtasklist[0];
        if (task?.url?.toLowerCase()?.indexOf('thunderx://') === 0) {
          this.onThunderxOpt(task?.url);
          break;
        }
      }
      // 过滤出p2sp的任务 / 磁力链
      const filterNewTaskList: ThunderNewTaskHelperNS.INewTaskData[] = [];
      for (const item of newtasklist) {
        if (!item?.url) {
          continue;
        }

        const url: string = item.url;
        if (item.fileSize) {
          item.fileSize = Number(item.fileSize);
        }
        if (item.userdata && typeof item.userdata === 'string') {
          let userdata: { callbackId: number; processId: number } | null = null;
          try {
            userdata = JSON.parse(item.userdata)
          } catch (error) {
            userdata = null;
          }
          if (userdata) {
            item.userdata = userdata;
          }
        }

        const taskType: DownloadKernel.TaskType = await DkThunderHelper.getTaskTypeFromUrl(url);
        if (taskType === DownloadKernel.TaskType.P2sp || taskType === DownloadKernel.TaskType.Emule || taskType === DownloadKernel.TaskType.Magnet) {
          filterNewTaskList.push(item);
        }
      }

      if (filterNewTaskList.length) {
        logger.log('✅自动弹出不可编辑新建面板 all', filterNewTaskList);
        ThunderNewTaskHelperNS.showPreCreateTaskWindow(filterNewTaskList);
      }
    } while (0);
  }

  public init(): void {
    ThunderHelper.setCommandLineCallback(this.commandHandler.bind(this), this.taskHandler.bind(this))
  }

  /** 解析thunderx协议，该数据是前端写在剪贴板的数据，或用户传入类似格式的数据 */
  public async createThunderxTask(text: string, clickFrom: string = 'ThunderUnion', emptyClipBoard: boolean = true): Promise<void> {
    do {
      if (!text) {
        break;
      }

      let data: any = null;
      try {
        data = JSON.parse(text);
      } catch (error) {
        data = null;
      }
      if (!data) {
        break;
      }

      // 判断下是否是需要读配置信息
      const url = data.json;
      if (url) {
        const res: Response = await fetch(url);
        if (res?.ok && res?.status === 200) {
          const jsonData = await res.json();
          if (!jsonData || !jsonData?.tasks) {
            // 下载配置失败
            // todo 上报
            break;
          }

          data = jsonData;
        }
      }

      const tasksInfo = data.tasks;
      if (!tasksInfo?.length) {
        break;
      }

      const taskGroupName: string = data.taskGroupName;
      const taskGroupIcon: string = data.taskGroupIcon;
      let downloadDir: string = data.downloadDir;
      const installFile: string = data.installFile;
      const excludePath: string = data.excludePath;
      const runParams: string = data.runParams;
      const userAgent: string = data.userAgent;
      const isUseHideYunPanParam: boolean = await config.getRemoteGlobalConfigValue('main', 'isUseHideYunPanParam', false);
      const hideYunPan: string = isUseHideYunPanParam ? data.hideYunPan : '0';
      const createShortcut: ThunderNewTaskHelperNS.ICreateShortcut = data.createShortcut;
      let threadCount: string | undefined = undefined;
      if (data.threadCount) {
        threadCount = data.threadCount;
      }

      const tasks: ThunderNewTaskHelperNS.INewTaskData[] = [];
      let installFileUrl: string = '';
      let shortcutTargetFileUrl: string = '';
      for (let index: number = 0; index < tasksInfo.length; index++) {
        const taskInfo = tasksInfo[index];
        const url: string = taskInfo.url;
        const name: string = taskInfo.name;
        const refUrl: string = taskInfo.referer || '';
        let size: number = 0;
        let isInstallFile: boolean = false;
        const taskType: DownloadKernel.TaskType = clickFrom?.toLowerCase() === 'ThunderUnion'.toLowerCase() ? DownloadKernel.TaskType.P2sp : (await DkThunderHelper.getTaskTypeFromUrl(url));
        // 获取指定安装文件的url
        if (installFile && !installFileUrl) {
          const resultUrl: string = await getInstallFileUrl(url, installFile, name);
          if (resultUrl) {
            isInstallFile = true;
            installFileUrl = resultUrl
          }
        }

        if (createShortcut && createShortcut.targetFile && !shortcutTargetFileUrl) {
          const resultUrl: string = await getInstallFileUrl(url, createShortcut.targetFile, name);
          if (resultUrl) {
            shortcutTargetFileUrl = resultUrl;
          }
        }

        if (taskInfo.size) {
          size = taskInfo.size;
        }
        let subDir: string = '';
        if (taskInfo.dir) {
          subDir = getRealDir(taskInfo.dir);
        } else if (excludePath) {
          subDir = await getSubDir(url, excludePath);
        }

        const task: ThunderNewTaskHelperNS.INewTaskData = {
          url: url,
          referer: refUrl,
          fileName: name,
          fileSize: size,
          subDir: subDir,
          statClick: clickFrom,
          nameFixedFlag: ThunderNewTaskHelperNS.taskOptFileNameFixed,
          taskType,
        };
        if (isInstallFile) {
          tasks.unshift(task); // 优先下载安装文件
        } else {
          tasks.push(task);
        }
      }

      if (!tasks.length) {
        break;
      }

      const userData = await getExtendData({
        taskgroupname: taskGroupName,
        taskgroupicon: taskGroupIcon,
        downloaddir: downloadDir,
        installfile: installFile,
        excludepath: excludePath,
        runparams: runParams,
        useragent: userAgent,
        threadcount: threadCount,
      }, hideYunPan, installFileUrl, createShortcut, shortcutTargetFileUrl);


      // console.warn('createThunderxTask 22', tasks, userData);
      // todo 弹出不可编辑新建面板
      // popNewTask(tasks, source, downloadDir, taskGroupName, userData);
      // 清空剪贴板
      if (emptyClipBoard) {
        ThunderHelper?.emptyClipBoard();
      }
    } while (0);
  }

  /** 解析联盟任务数据 */
  public async createUnionTask(filePath: string): Promise<void> {
    do {
      // 第一步对文件路径进行url解码
      filePath = decodeURIComponent(filePath);
      // 读取文件数据后并删除改文件
      const lines: string[] = await FileSystemAWNS.readLineAw(filePath);
      await FileSystemAWNS.unlinkAW(filePath);
      if (!lines?.length || lines.length <= 1) {
        break;
      }

      // 用于控制是读配置信息还是直接用数据
      let isReadConfig: boolean = false;
      // 配置文件url
      let jsonUrl: string = '';
      const urlList: string[] = [];
      const refererList: string[] = [];
      const tasksInfo: {
        [index: string]: {
          url?: string;
          referer?: string;
          name?: string;
          size?: number;
          dir?: string;
        }
      } = {};
      const taskExtendInfo: {
        taskgroupname?: string;
        taskgroupicon?: string;
        downloaddir?: string;
        installfile?: string;
        excludepath?: string;
        runparams?: string;
        useragent?: string;
        threadcount?: string;
      } = {};
      let hideYunPan: string = '0';
      const isUseHideYunPanParam: boolean = await config.getRemoteGlobalConfigValue('main', 'isUseHideYunPanParam', false);
      let createShortcut: ThunderNewTaskHelperNS.ICreateShortcut | null = null;
      type shortcutKeys = keyof ThunderNewTaskHelperNS.ICreateShortcut;
      for (let i: number = 0; i < lines.length; i++) {
        const line = lines[i].toLowerCase();;
        const parts: string[] = line.split('=');
        // 这里有些新版本批量下载任务组的一些处理逻辑，先不实现这个功能
        if (parts !== null && parts.length === 2) {
          const key: string = parts[0];
          let value: string = parts[1];
          if (!key) {
            continue;
          }

          // 对value进行解码
          value = value.replace(/\+/g, '%20');
          value = decodeURIComponent(value);
          if (key === 'json') {
            isReadConfig = true;
            jsonUrl = value;
            break;
          }
          if (key.indexOf('createshortcut') === 0) {
            // 形如 createShortcut[name]=12134
            const matcher: RegExpMatchArray | null = key.match(/createshortcut\[(.+)\]/);
            if (matcher?.[1]?.length) {
              createShortcut = createShortcut ?? {};
              createShortcut[matcher[1] as shortcutKeys] = value;
            }
            continue;
          }
          if (key === 'hideyunpan') {
            hideYunPan = isUseHideYunPanParam ? value : '0';
            continue;
          }
          // 这里处理下载链接，只要key值中含有url字符串，都是下载链接
          // 联盟新协议形如： 
          // tasks[0][url]=xxx
          // tasks[0][referer]=xxx
          // tasks[0][name]=122.dll
          // 旧协议形如
          // url=
          // referer=
          const subKey: RegExpMatchArray | null = key.match(/^tasks\[(\d+)\]\[(\w+)\]/);
          if (subKey?.[1] && subKey?.[2]) {
            const index: string = subKey[1];
            const prop: string = subKey[2];
            if (!tasksInfo[index]) {
              tasksInfo[index] = {};
            }
            if (prop === 'size') {
              tasksInfo[index][prop] = Number(value);
            } else {
              tasksInfo[index][prop] = value;
            }
            
            continue;
          }

          if (line.indexOf('url=') === 0) {
            urlList.push(value);
            continue;
          }
          if (line.indexOf('referer=') === 0) {
            refererList.push(value);
            continue;
          }

          taskExtendInfo[key] = value;
        }
      }

      if (isReadConfig) {
        const data = { json: jsonUrl };
        await this.createThunderxTask(JSON.stringify(data));
        break;
      }

      const tasks: ThunderNewTaskHelperNS.INewTaskData[] = [];
      const magnetTasks: ThunderNewTaskHelperNS.INewTaskData[] = [];
      let installFileUrl: string = '';
      let shortcutTargetFileUrl: string = '';
      for (let index in tasksInfo) {
        const taskInfo = tasksInfo[index];
        let isInstallFile: boolean = false;
        // 获取指定安装文件的url
        if (taskExtendInfo.installfile && installFileUrl === '') {
          const resultUrl: string = await getInstallFileUrl(taskInfo.url!, taskExtendInfo.installfile, taskInfo.name!);
          if (resultUrl) {
            installFileUrl = resultUrl;
            isInstallFile = true;
          }
        }

        if (createShortcut?.targetFile && shortcutTargetFileUrl === '') {
          const resultUrl: string = await getInstallFileUrl(taskInfo.url!, createShortcut.targetFile, taskInfo.name!);
          if (resultUrl) {
            shortcutTargetFileUrl = resultUrl;
          }
        }

        let subDir: string = '';
        if (taskInfo.dir) {
          subDir = getRealDir(taskInfo.dir);
        } else if (taskExtendInfo.excludepath) {
          subDir = await getSubDir(taskInfo.url!, taskExtendInfo.excludepath);
        }

        if (subDir) {
          // 测试链接： https://h5.yingtaoyun.com/xunlei.html
          subDir = subDir.replace(/[*?:|<>"\n\r\n]/g, ' ');
        }

        const taskType: DownloadKernel.TaskType =
          taskExtendInfo?.taskgroupname ? DownloadKernel.TaskType.P2sp : (await DkThunderHelper.getTaskTypeFromUrl(taskInfo.url!));
        const newtaskdata: ThunderNewTaskHelperNS.INewTaskData = {
          url: taskInfo.url!,
          referer: taskInfo.referer,
          fileName: taskInfo.name,
          fileSize: taskInfo.size,
          subDir: subDir,
          statClick: 'ThunderUnion',
          nameFixedFlag: ThunderNewTaskHelperNS.taskOptFileNameFixed,
          taskType,
        }
        if (taskType === DownloadKernel.TaskType.P2sp || taskType === DownloadKernel.TaskType.Emule) {
          if (isInstallFile) {
            tasks.unshift(newtaskdata);
          } else {
            tasks.push(newtaskdata);
          }
        } else if (taskType === DownloadKernel.TaskType.Bt || taskType === DownloadKernel.TaskType.Magnet) {
          magnetTasks.push(newtaskdata);
        }
      }

      if (tasks.length) {
        const userData = await getExtendData(taskExtendInfo, hideYunPan, installFileUrl, createShortcut, shortcutTargetFileUrl);
      }

      // todo 弹出不可编辑新建面板

      logger.log('createUnionTask 11：旧协议拉起新建面板');

      if (urlList.length) {
        logger.log('createUnionTask：旧协议拉起新建面板');
        const newTaskDatas: ThunderNewTaskHelperNS.INewTaskData[] = [];
        for (let i: number = 0; i < urlList.length; i++) {
          newTaskDatas.push({
            url: urlList[i],
            referer: refererList[i] ?? '',
          })
        };
        // todo 弹出不可编辑新建面板
        console.warn('createUnionTask：旧协议拉起新建面板', newTaskDatas);
      }
    } while (0);
  }
}

export const thunderAgent: ThunderAgentHanlder = new ThunderAgentHanlder();
