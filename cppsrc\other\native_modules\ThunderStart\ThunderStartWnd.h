#pragma once

#include <atlcrack.h>
#pragma warning(disable: 4458)
#include "Gdiplus.h"
#pragma warning(default: 4458)
using namespace Gdiplus;
#pragma comment(lib, "Gdiplus.lib")

#include "XLayeredWindow.h"


class CThunderStartWnd :
	public CWindowImpl<CThunderStartWnd, CWindow, CControlWinTraits>,
	public XAlphaLayeredWindow<CThunderStartWnd>
{
public:
	CThunderStartWnd(void);
	~CThunderStartWnd()
	{
		if (m_hWnd)
			DestroyWindow();
	}

	DECLARE_WND_CLASS(_T("ThunderStartWnd")) 
	BEGIN_MSG_MAP_EX(CThunderStartWnd)
		CHAIN_MSG_MAP(XAlphaLayeredWindow<CThunderStartWnd>)
	END_MSG_MAP()

private:
	ULONG_PTR m_gdiplusToken;

protected:
	void OnDrawBkgnd(HDC hDC);
	void OnDraw(HDC hDC);

public:	
	STDMETHOD(Create)(HWND hWndParent, _U_RECT rect = NULL, LPCTSTR szWindowName = NULL, DWORD dwStyle = 0, DWORD dwExStyle = 0,_U_MENUorID MenuOrID = 0U, LPVOID lpCreateParam = NULL);
	STDMETHOD(GetHWND)(HWND* phWnd);
	STDMETHOD(Invalidate)(BOOL bErase = TRUE);
	STDMETHOD(InvalidateRect)(CONST LPRECT lpRect, BOOL bErase = TRUE);
	STDMETHOD(DestroyWindow)();

};

