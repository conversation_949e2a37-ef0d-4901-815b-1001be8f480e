set(name OPEN<PERSON><PERSON>)
string(TOLOWER ${name} name_lower)
if(ARCHITECTURE STREQUAL "x64")
    list(APPEND ${name}_INCLUDE ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/include_x64)
    list(APPEND ${name}_LIBRARY_DIRS ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/lib/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE})
    list(APPEND ${name}_LIBS ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/lib/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE}/libcrypto.lib)
    list(APPEND ${name}_LIBS ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/lib/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE}/libssl.lib)
else()
    list(APPEND ${name}_INCLUDE ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/include)
    list(APPEND ${name}_LIBRARY_DIRS ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/lib/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE})
    list(APPEND ${name}_LIBS ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/lib/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE}/libeay32.lib)
    list(APPEND ${name}_LIBS ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/lib/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE}/ssleay32.lib)
endif()

set(${name}_BINS_PATH ${CMAKE_SOURCE_DIR}/3rdparty/${name_lower}/bin/${CMAKE_GENERATOR_PLATFORM}/${CMAKE_BUILD_TYPE}/)
message("     [Check 3rd-Party] ${name}_BINS_PATH: ${${name}_BINS_PATH}")
# 判断目录是否存在
if(EXISTS ${${name}_BINS_PATH})
    file(GLOB_RECURSE ${name}_BINS ${name}_BINS_PATH "${${name}_BINS_PATH}/*.*")
    message("                       ${name}_BINS: ${${name}_BINS}")    
else()
#    message("--------------Directory is NOT exist.")
endif()