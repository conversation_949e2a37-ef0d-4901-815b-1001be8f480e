.TooltipContent {
  padding: 4px 8px;
  font-size: 12px;
  line-height: 20px;
  color: var(--font-font-1, #272E3B);
  user-select: none;
  animation-duration: 400ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;
  border-radius: var(--border-radius-S, 6px);
  background: var(--background-background-elevated, #FFF);
  display: flex;
  align-items: center;
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.30), 4px 0px 6px 0px rgba(0, 0, 0, 0.10);
  word-break: break-all;
}

.TooltipContent[data-state='delayed-open'][data-side='top'] {
  animation-name: slideDownAndFade;
  margin-bottom: 4px;
}

.TooltipContent[data-state='delayed-open'][data-side='right'] {
  animation-name: slideLeftAndFade;
  margin-left: 4px;
}

.TooltipContent[data-state='delayed-open'][data-side='bottom'] {
  animation-name: slideUpAndFade;
  margin-top: 4px;
}

.TooltipContent[data-state='delayed-open'][data-side='left'] {
  animation-name: slideRightAndFade;
  margin-right: 4px;
}

.TooltipArrow {
  fill: var(--background-background-elevated, #FFF);
}

.TooltipTrigger {
  cursor: pointer;
}

@keyframes slideUpAndFade {
  from {
    opacity: 0;
    transform: translateY(2px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideRightAndFade {
  from {
    opacity: 0;
    transform: translateX(-2px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeftAndFade {
  from {
    opacity: 0;
    transform: translateX(2px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}