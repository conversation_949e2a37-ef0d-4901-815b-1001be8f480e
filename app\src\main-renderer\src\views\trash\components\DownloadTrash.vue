<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import TrashItem from './TrashItem.vue'
import XMPMessage from '@root/common/components/ui/message/index'
import Checkbox from "@root/common/components/ui/checkbox/index.vue"
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import { clipboard } from 'electron'
import { TaskManager } from '@root/common/task/impl/task-manager';
import * as BaseType from '@root/common/task/base';
import Empty from './Empty.vue'
import { TaskUtilHelper } from '@root/common/helper/task-util-helper'
import Loading from '@root/common/components/ui/loading/index.vue'
import { taskExtraFunc } from '@/common/task-extra'
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'


const emit = defineEmits<{
  (e: 'downloadTrashUpdate', downloadTrashLength: number): void
}>()

const categoryManager = TaskManager.GetInstance().GetCategoryManager();

const contextMenuList = [
  {
    key: 'resetDownload',
    label: '还原',
  },
  {
    key: 'cleanAllDownload',
    label: '清空',
  },
  {
    key: 'openFileFolder',
    label: '打开文件夹',
    icon: 'xl-icon-general-openfolder-m',
    hasSeparator: true
  },
  {
    key: 'deleteDownload',
    label: '彻底删除',
    icon: 'xl-icon-general-close-l',
  },
  {
    key: 'reDownload',
    label: '重新下载',
    icon: 'xl-icon-general-revoke-l',
    hasSeparator: true
  },
  {
    key: 'copyDownloadLink',
    label: '复制下载链接',
  },
]


const showDeleteDialog = ref(false)
const clearLocalFile = ref(false)
const downloadFromOrigin = ref(false)

const curOperation = ref('')


const initialLoading = ref(true)

interface IBriefTask {
  name: string
  size: number
  user_modified_time: number
  icon_link: string
  id: number
}

// 列表数据
const downloadTrashList = ref<IBriefTask[]>([])

const currentOperationTask = ref<IBriefTask | null>(null)

// 获取数据的方法
const fetchData = async () => {
  initialLoading.value = true
  try {
    let category = await categoryManager.getCategoryById(-1);
    let downloadView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Recycle);
    const downloadIds = downloadView!.getTasks();
    for (let id of downloadIds) {
      let task = await TaskManager.GetInstance().findTaskById(id);
      if (!task) continue
      const taskBase = task.getTaskBase();
      downloadTrashList.value.push({
        name: taskBase.taskName,
        size: taskBase.fileSize,
        user_modified_time: taskBase.recycleTime,
        icon_link: taskBase.taskName ? TaskUtilHelper.getTaskIcon(taskBase.taskName, taskBase.taskType) : '',
        id: taskBase.taskId
      })
    }

    downloadTrashList.value = downloadTrashList.value.sort((a, b) => b.user_modified_time - a.user_modified_time)

    console.log('获取到的下载列表', downloadTrashList.value)
    emit('downloadTrashUpdate', downloadTrashList.value.length)
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    initialLoading.value = false
  }

}

const updateTrashList = () => {
  downloadTrashList.value = downloadTrashList.value.filter(item => item.id !== currentOperationTask.value?.id)
  emit('downloadTrashUpdate', downloadTrashList.value.length)
}

const handleOperation = (key: string, item: IBriefTask) => {
  currentOperationTask.value = item
  curOperation.value = key
  console.log(';>>>>>>>>>>>>>>> updateTrashList', key)
  switch (key) {
    case 'resetDownload':
      handleRecoverTask()
      break
    case 'cleanAllDownload':
    case 'deleteDownload':
    case 'reDownload':
      showDeleteDialog.value = true
      break
    case 'openFileFolder':
      ConsumeManagerNs.openTaskFolder(item.id)
      break
    case 'copyDownloadLink':
      clipboard.writeText('hello i am a bit of text111122233!')
      XMPMessage({
        message: '链接已复制',
        type: 'success'
      })
      break
    default:
      break
  }
}

const dialogInfo = computed(() => {
  if (curOperation.value === 'cleanAllDownload') {
    return {
      title: '确定清空回收站所有的下载文件吗？',
      confirmText: '清空'
    }
  }

  if (curOperation.value === 'deleteDownload') {
    return {
      title: '确定要删除吗？',
      confirmText: '确认删除'
    }
  }

  if (curOperation.value === 'reDownload') {
    return {
      title: '重新下载',
      confirmText: '确认'
    }
  }
  return {}
})


const handleDiaglogConfirm = () => {
  console.log(';>>>>>>>>>>>>>>> handleDiaglogConfirm')
  if (curOperation.value === 'cleanAllDownload') {
    handleClearAll()
  }

  if (curOperation.value === 'deleteDownload') {
    XMPMessage({
      message: '彻底删除成功',
      type: 'success'
    })
    handleDelTask()
  }

  if (curOperation.value === 'reDownload') {
    handleReDownloadTask()
    XMPMessage({
      message: '重新下载成功',
      type: 'success'
    })
  }
}

const handleRecoverTask = () => {
  currentOperationTask.value && taskExtraFunc.recoverFromRecycleTask(currentOperationTask.value.id)
  updateTrashList()
  XMPMessage({
    message: '下载文件已还原',
    type: 'success'
  })
}

const handleDelTask = () => {
  currentOperationTask.value && taskExtraFunc.delTaskById(currentOperationTask.value.id)
  updateTrashList()
}

const handleClearAll = () => {
  taskExtraFunc.recycleDownloadEmpty(true)
  downloadTrashList.value = []
  emit('downloadTrashUpdate', 0)
  XMPMessage({
    message: '清空成功',
    type: 'success'
  })
}

const handleReDownloadTask = () => {
  currentOperationTask.value && taskExtraFunc.reDownloadTaskById(currentOperationTask.value.id)
  updateTrashList()
}

const handleDoubleClick = () => {
  XMPMessage({
    message: '回收站暂时不支持查看，请还原后再试',
    type: 'info'
  })
}

onMounted(() => {
  fetchData()
})

defineExpose({
  handleClearAll
})

</script>

<template>
  <Loading v-if="initialLoading" />
  <RecycleScroller v-slot="{ item }" class="download-trash-wrapper" key-field="id" :items="downloadTrashList"
    :item-size="80" data-scroll-container>
    <TrashItem :key="item.ids" type="download" :context-menu="contextMenuList"
      @operation="(key: string) => handleOperation(key, item)" :item-data="item"
      :selected="currentOperationTask?.id === item.id" @double-click="handleDoubleClick" />
  </RecycleScroller>

  <Empty v-if="!initialLoading && downloadTrashList.length === 0" type="download" />

  <Dialog :title="dialogInfo.title" :variant="curOperation === 'reDownload' ? 'thunder' : 'error'" :show-trigger="false"
    v-model:open="showDeleteDialog" @confirm="handleDiaglogConfirm" @cancel="showDeleteDialog = false"
    :confirm-text="dialogInfo.confirmText">
    <template v-if="curOperation === 'reDownload'">
      <p class="download-trash-dialog-tips">
        重新下载会删除本地文件，确定要重新下载么？
      </p>
      <Checkbox v-model="downloadFromOrigin" label="downloadFromOrigin">
        <span class="download-trash-checkbox-label">从原始地址重新下载</span>
      </Checkbox>
    </template>
    <Checkbox v-else v-model="clearLocalFile" label="clearLocalFile">
      <span class="download-trash-checkbox-label">同时删除本地文件</span>
    </Checkbox>
  </Dialog>



</template>

<style scoped lang="scss">
.download-trash {
  &-wrapper {
    padding: 8px 22px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}
</style>

<style lang="scss">
.download-trash-checkbox-label {
  color: var(--font-font-3, #86909C);
  font-size: 13px;
}

.download-trash-dialog-tips {
  color: var(--font-font-2, #4E5769);
  font-size: 14px;
  margin-bottom: 8px;
}
</style>