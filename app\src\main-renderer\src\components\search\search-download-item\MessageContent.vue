<template>
  <div>
    <p class="message-text">{{ messageText }}</p>
    <div class="file-info-container">
      <div :class="['file-icon-type', fileIconClass]"></div>
      <div class="file-details">
        <Tooltip :show-arrow="false" :auto-open="false" to=".file-details" as-child :max-width="420">
          <template #trigger>
            <div class="file-name">{{ taskName }}</div>
          </template>
          <template #content>
            <div>{{ taskName }}</div>
          </template>
        </Tooltip>
        <div class="file-size">{{ taskSize }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
defineProps<{
  taskName: string
  taskSize: string
  fileIconClass: string
  messageText: string
}>()
</script>

<style>
/* MessageBox内容样式 */
.message-text {
  color: var(--font-font-2);
  font-size: 13px;
  line-height: 38px;
  height: 38px;
}

.file-info-container {
  display: flex;
  align-items: center;
  height: 72px;
  padding: 0 20px;
  margin-top: 8px;
  box-sizing: border-box;
  border: 1px solid rgba(229, 230, 235, 1);
  border-radius: var(--border-radius-M);
}

.file-info-container .file-icon-type {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  position: relative;
  border-radius: 4px;
}

.file-info-container .file-details {
  max-width: 320px;
  margin-left: 12px;
}

.file-info-container .file-name {
  max-width: 320px;
  color: var(--font-font-1);
  font-size: 13px;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-info-container .file-size {
  color: var(--font-font-3);
  font-size: 12px;
  line-height: 20px;
  margin-top: 2px;
}
</style> 