import { PopUpNS } from '@root/common/pop-up'
import * as PopUpTypes from '@root/common/pop-up/types'
import { useTaskSettingStore } from '@root/modal-renderer/src/stores/task-setting'
import type { TaskSettings } from '@root/modal-renderer/types/new-task.type'

/**
 * 任务设置对话框选项
 */
interface TaskSettingDialogOptions {
  /** 是否使用自定义位置，默认为 true */
  useCustomPosition?: boolean
  /** 自定义位置坐标（当 useCustomPosition 为 false 时会被忽略） */
  position?: { x: number; y: number } | Promise<{ x: number; y: number } | null>
  /** 用于获取坐标的元素选择器，如果提供则优先使用此元素的坐标 */
  selector?: string
}

/**
 * 任务设置 Composable 函数
 * 提供统一的任务设置处理逻辑，支持多个组件复用
 */
export function useTaskSetting() {
  const taskSettingStore = useTaskSettingStore()

  /**
   * 获取指定选择器元素的屏幕坐标
   * @param selector 元素选择器
   * @returns Promise<{x: number, y: number} | null> 返回元素的屏幕坐标，获取失败时返回 null
   */
  const getElementPosition = async (selector: string): Promise<{ x: number; y: number } | null> => {
    try {
      console.log(`[useTaskSetting] 开始获取元素坐标，选择器: ${selector}`)

      // 查找指定的元素
      const element = document.querySelector(selector)

      if (!element) {
        console.warn(`[useTaskSetting] 未找到元素 (selector: ${selector})`)
        return null
      }

      // 获取元素的客户端边界矩形
      const rect = element.getBoundingClientRect()

      // 获取元素左上角的相对坐标点
      const elementPoint = {
        x: rect.left,
        y: rect.top,
      }

      console.log(`[useTaskSetting] 元素相对坐标:`, elementPoint)

      // 使用 PopUpNS.toWindowPoint 转换为屏幕绝对坐标
      const screenPoint = await PopUpNS.toWindowPoint(elementPoint)

      if (!screenPoint) {
        console.warn('[useTaskSetting] 无法获取屏幕坐标')
        return null
      }

      console.log('[useTaskSetting] 元素屏幕坐标:', screenPoint)

      // 返回包含 x, y 坐标的对象（在 y 方向添加 156 偏移量）
      return {
        x: screenPoint.x + 1,
        y: screenPoint.y + 156 + 10 + 50,
      }
    } catch (error) {
      console.error('[useTaskSetting] 获取元素坐标失败:', error)
      return null
    }
  }

  /**
   * 获取当前 Dialog 弹窗的屏幕坐标
   * @returns Promise<{x: number, y: number} | null> 返回弹窗的屏幕坐标，获取失败时返回 null
   */
  const getDialogWindowPosition = async (): Promise<{ x: number; y: number } | null> => {
    try {
      console.log('[useTaskSetting] 开始获取 Dialog 弹窗坐标')

      // 查找 Dialog 元素
      const dialogElement = document.querySelector('.dialog-content')

      if (!dialogElement) {
        console.warn('[useTaskSetting] 未找到 Dialog 元素')
        return null
      }

      // 获取元素的客户端边界矩形
      const rect = dialogElement.getBoundingClientRect()

      // 获取 Dialog 左上角的相对坐标点
      const elementPoint = {
        x: rect.left,
        y: rect.top,
      }

      console.log('[useTaskSetting] Dialog 元素相对坐标:', elementPoint)

      // 使用 PopUpNS.toWindowPoint 转换为屏幕绝对坐标
      const screenPoint = await PopUpNS.toWindowPoint(elementPoint)

      if (!screenPoint) {
        console.warn('[useTaskSetting] 无法获取屏幕坐标')
        return null
      }

      console.log('[useTaskSetting] Dialog 弹窗屏幕坐标:', screenPoint)

      // 返回包含 x, y 坐标的对象（添加默认偏移量）
      return {
        x: screenPoint.x + 24,
        y: screenPoint.y + 50,
      }
    } catch (error) {
      console.error('[useTaskSetting] 获取 Dialog 弹窗坐标失败:', error)
      return null
    }
  }

  /**
   * 显示任务设置对话框
   * @param taskUrl 任务URL
   * @param taskSetting 当前任务的设置对象
   * @param options 对话框选项
   */
  const showTaskSettingDialog = async (
    taskUrl: string,
    taskSetting: TaskSettings,
    options: TaskSettingDialogOptions = {}
  ) => {
    try {
      // 设置默认值
      const { useCustomPosition = true, position, selector } = options

      // 使用传递进来的 taskSetting，如果没有则使用默认值或从 store 获取
      const currentTaskSetting = taskSetting

      // 确定弹窗位置相关配置
      let popupOptions: any = {
        // parentId: -1, // 传递-1，表示不以主窗口为父窗口
        parentId: PopUpNS.getCurrentWindow()?.id,
        relatePos: PopUpTypes.RelatePosType.CenterParent,
        replaceView: true, // 该值设置为true，则如果已经存在窗口，则更新数据到当前窗口
        singleton: true, // 关键：确保只有一个创建任务窗口
        title: '',
        windowWidth: 632,
        windowHeight: 334,
        taskSetting: currentTaskSetting,
        url: taskUrl,
      }

      let dialogConf: any = {}

      // 处理位置相关逻辑
      if (selector) {
        // 如果传入了 selector，使用 getElementPosition 获取坐标
        console.log('[useTaskSetting] 使用 selector 获取元素位置:', selector)
        const elementPosition = await getElementPosition(selector)

        if (elementPosition) {
          console.log('[useTaskSetting] 使用元素位置:', elementPosition)
          popupOptions.relatePos = PopUpTypes.RelatePosType.Custom
          dialogConf.x = elementPosition.x
          dialogConf.y = elementPosition.y
        } else {
          console.warn('[useTaskSetting] 无法获取元素位置，使用默认居中')
          popupOptions.relatePos = PopUpTypes.RelatePosType.CenterParent
        }
      } else if (useCustomPosition) {
        // 如果使用自定义位置但没有 selector
        let customPosition: { x: number; y: number } | null = null

        if (position) {
          // 如果 position 是 Promise，等待其解析
          if (position && typeof position === 'object' && 'then' in position) {
            customPosition = await position
          } else {
            customPosition = position as { x: number; y: number }
          }
        } else {
          // 如果没有传入 position，则自动获取当前弹窗位置
          customPosition = await getDialogWindowPosition()
        }

        if (customPosition) {
          console.log('[useTaskSetting] 使用自定义位置:', customPosition)
          popupOptions.relatePos = PopUpTypes.RelatePosType.Custom
          dialogConf.x = customPosition.x
          dialogConf.y = customPosition.y
        } else {
          console.warn('[useTaskSetting] 无法获取自定义位置，使用默认居中')
          popupOptions.relatePos = PopUpTypes.RelatePosType.CenterParent
        }
      } else {
        console.log('[useTaskSetting] 使用默认居中位置')
        popupOptions.relatePos = PopUpTypes.RelatePosType.CenterParent
      }

      // 打开任务设置弹窗
      const payload = await PopUpNS.popup('task-setting', popupOptions, {
        ...dialogConf,
        alwaysOnTop: true,
      })

      // 处理用户操作结果
      if (payload.action === PopUpTypes.Action.OK) {
        const args = payload.args as any
        const picked = args.picked

        console.log('[useTaskSetting] 用户选择的操作:', picked)

        if (picked === 'apply-all') {
          // 应用到所有任务
          const newTaskSetting = args.newTaskSetting
          taskSettingStore.handleTaskSettingApplyAll(newTaskSetting)
          console.log('[useTaskSetting] 应用设置到所有任务:', newTaskSetting)
        } else if (picked === 'submit') {
          // 只应用到当前任务
          const newTaskSetting = args.newTaskSetting
          taskSettingStore.setTaskSettings(taskUrl, newTaskSetting)
          console.log('[useTaskSetting] 应用设置到当前任务:', taskUrl, newTaskSetting)
        }
      } else {
        console.log('[useTaskSetting] 用户取消了任务设置')
      }
    } catch (error) {
      console.error('[useTaskSetting] 任务设置处理失败:', error)
      // 可以在这里添加错误提示逻辑
    }
  }

  return {
    showTaskSettingDialog,
  }
}
