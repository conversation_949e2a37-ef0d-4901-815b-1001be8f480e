<script lang="ts" setup>
import { ref, computed, type HTMLAttributes } from 'vue'

const props = withDefaults(defineProps<{
  label?: string | number;
  modelValue: Array<any> | boolean;
  disabled?: boolean;
  indeterminate?: boolean;
  labelClass?: HTMLAttributes['class'];
}>(), {
  label: '',
  modelValue: false,
  disabled: false,
  indeterminate: false,
  labelClass: ''
});

const checkboxRef = ref<HTMLInputElement>();

const emits = defineEmits<{ (event: 'update:modelValue', value: any): void }>();

const checked = computed(() => {
  if (typeof props.modelValue === 'boolean') {
    return props.modelValue
  } else {
    return props.modelValue.includes(props.label)
  }
})

const handleChange = () => {
  if (typeof props.modelValue === 'boolean') {
    emits('update:modelValue', !props.modelValue)
  } else if (checked.value) {
    emits('update:modelValue', props.modelValue.filter(item => item !== props.label))
  } else {
    emits('update:modelValue', [...props.modelValue, props.label])
  }
}

const cancelSelected = () => {
  checkboxRef.value && (checkboxRef.value.checked = false)
}

defineExpose<{
  cancelSelected: () => void;
}>({
  cancelSelected
})
</script>

<template>
  <div class="td-checkbox">
    <label class="td-checkbox" :class="{
      'is-checked': checked,
      'is-disabled': disabled,
      'is-indeterminate': !checked && indeterminate
    }">
      <input ref="checkboxRef" class="td-checkbox__inner" type="checkbox" :checked="checked" :disabled="disabled" @change="handleChange" />
      <span class="td-checkbox__label" :class="labelClass">
        <slot></slot>
      </span>
    </label>
  </div>
</template>

<style>
.td-checkbox {
  display: inline-flex;
  align-items: center;
}

.td-checkbox__inner {
  position: relative;
  flex: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 14px;
  height: 14px;
  background: transparent;
  border: 1px solid var(--button-button2-hover);
  border-radius: var(--border-radius-XS);
  -webkit-appearance: none;
  cursor: pointer;
}

.td-checkbox__inner:focus {
  outline: none;
}

.td-checkbox__inner:before {
  text-align: center;
  color: var(--button-button1-font-default);
  font-size: 12px;
  font-family: "iconfont";
  line-height: 12px;
  transform: scale(0);
  transition: 0.1s;
  content: "\e629";
}

.td-checkbox__inner:checked {
  background: var(--button-button1-default);
  border-color: var(--button-button1-default);
}

.td-checkbox__inner:checked:before {
  transform: scale(1);
}

.td-checkbox__inner:hover {
  border-color: var(--button-button1-default);
}

.td-checkbox__inner:focus {
  outline: none;
}

.td-checkbox__label {
  margin-left: 6px;
  font-weight: 400;
  font-size: 13px;
  line-height: 22px;
  color: var(--font-font-1);
}

.td-checkbox.is-indeterminate .td-checkbox__inner {
  background-color: var(--button-button1-default);
  border-color: var(--button-button1-default);
}

.td-checkbox.is-indeterminate .td-checkbox__inner:before {
  transform: rotate(0deg);
  content: "\e62b";
}

.td-checkbox.is-checked .td-checkbox__inner:hover {
  background-color: var(--button-button1-hover);
  border-color: var(--button-button1-hover);
}

.td-checkbox.is-indeterminate .td-checkbox__inner:hover {
  background-color: var(--button-button1-hover);
  border-color: var(--button-button1-hover);
}

.td-checkbox.is-disabled {
  cursor: not-allowed;
}

.td-checkbox.is-disabled .td-checkbox__inner {
  cursor: not-allowed;
}


.td-checkbox.is-disabled .td-checkbox__inner {
  color: var(--font-font-4);
  background-color: var(--fill-fill-2);
  border-color: var(--button-button2-hover);
}

.td-checkbox.is-disabled .td-checkbox__inner:checked {
  background-color: var(--fill-fill-1);
  border-color: transparent;
}

.td-checkbox.is-disabled .td-checkbox__inner:before {
  color: var(--font-font-4);
}

.td-checkbox.is-disabled .td-checkbox__label {
  color: var(--font-font-4);
}

.td-checkbox.is-disabled.is-indeterminate .td-checkbox__inner {
  background-color: var(--fill-fill-1);
  border-color: transparent;
}
</style>
