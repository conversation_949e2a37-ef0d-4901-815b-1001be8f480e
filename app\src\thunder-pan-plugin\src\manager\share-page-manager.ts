import { User<PERSON><PERSON>per } from '@/utils/user-helper'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { API_SHARE } from '@root/common/thunder-pan-manager/pan-sdk/types'
import { reactive } from 'vue'

export interface ISharePageCurrentData {
  list: API_SHARE.DriveShareMgrData[]
  listPageToken: string
  isFetching: boolean
  pickedIds: string[]
}

export interface IGetListOptions {
  reset?: boolean
}

function genDefaultCurrentData(): ISharePageCurrentData {
  return {
    list: [],
    listPageToken: '',
    isFetching: false,
    pickedIds: [],
  }
}

export class SharePageManager {
  private static _instance: SharePageManager

  static getInstance() {
    if (SharePageManager._instance) {
      return SharePageManager._instance
    } else {
      SharePageManager._instance = new SharePageManager()
      return SharePageManager._instance
    }
  }

  private current: ISharePageCurrentData = reactive<ISharePageCurrentData>(genDefaultCurrentData())

  reset() {
    this.current = reactive<ISharePageCurrentData>(genDefaultCurrentData())
  }

  getCurrentData () {
    return this.current
  }

  async getList (options: IGetListOptions = {}) {
    if (this.current.isFetching) return
    // 重置数据
    if (options.reset) {
      this.current.list = []
      this.current.listPageToken = ''
    }
    this.current.isFetching = true

    try {
      await UserHelper.waitUserSignin()

      const res = await ThunderPanClientSDK.getInstance().getShareList({
        params: {
          page_token: this.current.listPageToken,
        }
      })

      if (res.success && res.data && res.data.data) {
        this.current.list.push(...res.data.data)
        this.current.listPageToken = res.data.next_page_token ?? ''
      }
    } catch (err) {

    } finally {
      this.current.isFetching = false
    }
  }

  batchDeleteItems(ids: string[]) {
    this.current.list = this.current.list.filter(item => { return !ids.includes(item.share_id!) })
  }

  cleanPicked() {
    this.current.pickedIds = []
  }

  setPickedIds(ids: string[]) {
    this.current.pickedIds = ids
  }

  togglePickedId(shareId: string, forceCurrent?: boolean) {
    // 强制选中当前指定项
    if (forceCurrent) {
      this.current.pickedIds = [shareId]
      return
    }

    if (!this.current.pickedIds.includes(shareId)) {
      this.current.pickedIds.push(shareId)
    } else {
      const index = this.current.pickedIds.indexOf(shareId)
      if (index > -1) {
        this.current.pickedIds.splice(index, 1)
      }
    }
  }
}
