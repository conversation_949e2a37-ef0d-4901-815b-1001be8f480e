<script setup lang="ts">
import { ref } from 'vue'
import Checkbox from "@root/common/components/ui/checkbox/index.vue"


const source = ref(['pan'])
const demo1 = ref(false)
const demo2 = ref(true)
const demo3 = ref(true)
const demo4 = ref(false)
const demo5 = ref(false)

import { PopoverRoot, PopoverPortal, PopoverTrigger, PopoverContent, type DropdownMenuRootEmits, type DropdownMenuRootProps, useForwardPropsEmits } from 'reka-ui'

const props = defineProps<DropdownMenuRootProps>()
const emits = defineEmits<DropdownMenuRootEmits>()
const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <div>
    <h1>Checkbox</h1>

    <Checkbox v-model="source" label="pan">百度网盘</Checkbox>
    <Checkbox v-model="demo1" label="pan" disabled>百度网盘</Checkbox>
    <Checkbox v-model="demo2" label="pan" indeterminate>百度网盘</Checkbox>
    <Checkbox v-model="demo3" label="pan" indeterminate disabled>百度网盘</Checkbox>
    <Checkbox v-model="demo4" label="pan" indeterminate>百度网盘</Checkbox>
    <Checkbox v-model="demo5" label="pan" indeterminate disabled>百度网盘</Checkbox>
    
    <PopoverRoot>
    <PopoverTrigger
      aria-label="Update dimensions"
    >
      123
    </PopoverTrigger>
    <PopoverPortal>
      <PopoverContent
        side="bottom"
        :side-offset="5"
      >
        3213
      </PopoverContent>
    </PopoverPortal>
  </PopoverRoot>
      
  </div>
</template>