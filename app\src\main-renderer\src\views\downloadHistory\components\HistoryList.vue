<script lang="ts" setup>
import { onMounted, ref, watch, computed, nextTick, useTemplateRef } from 'vue'
import TaskItem from './TaskItem.vue'
import DefaultPage from './DefaultPage.vue'
import Loading from '@root/common/components/ui/loading/index.vue'
import TDCheckbox from '@root/common/components/ui/checkbox/index.vue'
import { IHistoryTask } from '../type'
import { CreateCommonContextmenu } from '@root/common/components/ui/contextmenu'
import Toolbar, { IMenuItem, IToolbarOperationType } from './toolbar.vue'
import { ContextmenuKey } from '../source'
import XMPMessage from '@root/common/components/ui/message'
import { useElementVisibility } from '@vueuse/core';

const emits = defineEmits<{
  (e: 'loadingMore'): void // 加载更多资源
  (e: 'delete', data: IHistoryTask[]): void // 删除任务
  (e: 'collection', data: IHistoryTask[]): void // 收藏任务
  (e: 'unCollection', data: IHistoryTask[]): void // 取消收藏任务
  (e: 'rename', data: IHistoryTask): void // 重命名任务
  (e: 'downloadTask', data: IHistoryTask[]): void // 下载任务
  (e: 'copyLink', data: IHistoryTask[]): void // 复制任务
}>()

const props = withDefaults(defineProps<{
  fileList: IHistoryTask[]
  loading: boolean
  total: number
  expiresTime: number
  isCollection?: boolean
}>(), {})

const $rootElement = useTemplateRef<HTMLDivElement>('$rootElement')
const rootElementIsVisible = useElementVisibility($rootElement)

const selectAllRef = useTemplateRef('selectAllRef')

const scrollerHistoryVm = ref<any>(null)

const selectedFiles = ref<string[]>([])
const lastItem = ref<IHistoryTask|null>(null)

const selectAll = ref(false)
const indeterminate = ref(false)


// const indeterminate = computed(() => {
//   return selectedFiles.value.length > 0 && selectedFiles.value.length < props.total
// })
// const selectAll = computed(() => {
//   return selectedFiles.value.length > 0 && selectedFiles.value.length === props.total
// })

watch(selectedFiles, (newVal)=> {
  indeterminate.value = newVal.length > 0 && newVal.length < props.total
  selectAll.value = newVal.length > 0 && newVal.length === props.total
}, { deep: true, immediate: true })

const toolbarPrimaryOperation = computed(() => {
  const params = [
    {
      key: 'batchDownload',
      icon: 'xl-icon-general-download-m',
      label: '下载',
      text: '下载',
    },
    {
      key: 'batchCopy',
      icon: 'xl-icon-general-copy-m',
      label: '复制下载链接',
      text: '复制下载链接',
    },
  ]
  if (!props.isCollection) {
    params.splice(1, 0, {
      key: 'batchDelete',
      icon: 'xl-icon-general-delete-m',
      label: '删除',
      text: '删除',
    })
  }
  return params
})

const handleListScrollEnd = () => {
  if (props.total && props.total > props.fileList.length) {
    emits('loadingMore')
  } else {
    console.log('没有更多资源了')
  }
}

const handleCleanPicked = () => {
  selectedFiles.value = []
  selectAllRef.value && selectAllRef.value.cancelSelected()
}

const handleToolbarMenuItemClick = (val) => {
  console.log('handleToolbarMenuItemClick', val)
  const { isExistExpiry } = judgeTaskData()
  if (['batchDownload', 'batchCopy'].includes(val) && isExistExpiry) {
    XMPMessage({
      message: '请移除失效记录后重试',
      type: 'warning'
    })
    return
  }
  switch (val) {
    case 'batchDownload':
      console.log('下载')
      emits('downloadTask', props.fileList.filter(item => selectedFiles.value.includes(item.key)))
      handleCleanPicked()
      break
    case 'batchCopy':
      console.log('复制下载链接')
      emits('copyLink', props.fileList.filter(item => selectedFiles.value.includes(item.key)))
      handleCleanPicked()
      break
    case 'batchDelete':
      console.log('删除')
      emits('delete', props.fileList.filter(item => selectedFiles.value.includes(item.key)))
      break
  }
}

/** 顶部checkbox操作 */
const handleCheckChange = (value: boolean) => {
  console.log('handleCheckChange', value)
  if (selectAll.value || (indeterminate.value && selectedFiles.value.length === props.fileList.length)) {
    handleCleanPicked()
  } else {
    selectedFiles.value = props.fileList.map(item => item.key)
  }
}

const handleCheckboxClick = (value: boolean, item: IHistoryTask) => {
  console.log('handleCheckItemChange', value, item)
  const index = selectedFiles.value.findIndex(id => id === item.key)
  console.log('index', index)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(item.key)
  }
  console.log('selectedFiles.value', selectedFiles.value)
}

const updateScroller = () => {
    // RecycleScroller 有 bug ，更新列表里面的 DOM 不更新，这里需要手动调里面的方法来更新
  scrollerHistoryVm.value && scrollerHistoryVm.value.updateVisibleItems(true)
}

const judgeTaskData = () => {
  const filterList = props.fileList.filter(item => selectedFiles.value.includes(item.key))
  // 校验数据是否存在喜欢资源
  let isExistUnCollectionTask = false
  let isExistCollectionTask = false
  const isBatchOperation = selectedFiles.value.length > 1
  let isExistExpiry = false // 是否存在超时资源
  const isShowOnlyDel = !isBatchOperation && isExistExpiry
  filterList.forEach(item => {
    if (props.expiresTime !== -1 && (new Date(item?.create_time ?? 0).getTime() + props.expiresTime) <= Date.now()) {
      isExistExpiry = true
    }
    if (item.collection_id) {
      isExistCollectionTask = true
    } else {
      isExistUnCollectionTask = true
    }
  })
  return { isExistUnCollectionTask ,isExistCollectionTask, isShowOnlyDel, isBatchOperation, isExistExpiry, filterList }
}

const handleContextMenu = (event: MouseEvent, fileItem: IHistoryTask) => {
  if (!selectedFiles.value.includes(fileItem.key)) {
    selectedFiles.value = [ fileItem.key ]
  }

  if (selectedFiles.value.length > 20) {
    XMPMessage({
      message: '最多只能选择20个文件',
      type: 'warning'
    })
    return
  }
  const { isExistUnCollectionTask ,isExistCollectionTask, isShowOnlyDel, isBatchOperation, isExistExpiry, filterList } = judgeTaskData()
  // 校验数据是否可以删除
  console.log('handleContextMenu', event, fileItem)
  const contextmenuList = [
    {
      key: ContextmenuKey.download,
      name: '下载',
      iconLeft: 'xl-icon-general-download-m',
      isShow: !isShowOnlyDel && !isExistExpiry,
    },
    {
      key: ContextmenuKey.collection,
      name: '标记喜欢',
      iconLeft: '',
      isShow: isExistUnCollectionTask && !isShowOnlyDel && !isExistExpiry,
    },
    {
      key: ContextmenuKey.unCollection,
      name: '取消喜欢',
      iconLeft: '',
      isShow: !isExistUnCollectionTask && !isShowOnlyDel,
    },
    {
      key: ContextmenuKey.delete,
      name: '删除',
      iconLeft: 'xl-icon-general-delete-m',
      isShow: !isExistCollectionTask || isShowOnlyDel,
    },
    {
      key: ContextmenuKey.copy,
      name: '复制下载链接',
      iconLeft: 'xl-icon-general-copy-m',
      isShow: !isShowOnlyDel && !isExistExpiry
    },
    {
      key: ContextmenuKey.rename,
      name: '重命名',
      iconLeft: 'xl-icon-general-edit-m',
      isShow: !isExistExpiry && !isShowOnlyDel && !isBatchOperation,
    },
  ].filter(item => item.isShow)
  nextTick(() => {
    CreateCommonContextmenu({
      menuList: [contextmenuList],
      parentElement: $rootElement.value!,
      clickPosition: {
        x: event.clientX,
        y: event.clientY,
      },
      onMenuItemClick: (item) => {
        handleOperationWithType(item.key, filterList)
      }
    })
  })
}

async function handleOperationWithType (type: string, tasks: IHistoryTask[]) {
  console.log('handleOperationWithType', type, tasks)
  let clear = true
  switch (type) {
    case ContextmenuKey.download:
      console.log('下载')
      emits('downloadTask', tasks)
      break
    case ContextmenuKey.collection:
      console.log('标记喜欢')
      emits('collection', tasks)
      break
    case ContextmenuKey.unCollection:
      console.log('取消喜欢')
      emits('unCollection', tasks)
      break
    case ContextmenuKey.delete:
      console.log('删除')
      emits('delete', tasks)
      clear = false
      break
    case ContextmenuKey.copy:
      console.log('复制下载链接')
      emits('copyLink', tasks)
      break
    case ContextmenuKey.rename:
      console.log('重命名')
      emits('rename', tasks[0])
      clear = false
      break
    default:
      break
  }
  if (clear) {
    handleCleanPicked()
  }
}


const handleItemClick = (event: MouseEvent, item: IHistoryTask) => {
  console.log('handleItemClick',event, event.ctrlKey, item)
  const itemId = item.key
  if (event.ctrlKey) {
    if (itemId && selectedFiles.value.includes(itemId)) {
      selectedFiles.value = selectedFiles.value.filter(id => id !== itemId)
    } else {
      selectedFiles.value.push(itemId)
    }
  } else if (event.shiftKey) {
    selectedFiles.value = []
    let lastIndex = props.fileList.findIndex(item => lastItem.value && item.key === lastItem.value.key)
    let index = props.fileList.indexOf(item)
    if (index >= lastIndex) {
      for (let i = lastIndex; i <= index; i++) {
        selectedFiles.value.push(props.fileList[i].key)
      }
    } else {
      for (let i = index; i <= lastIndex; i++) {
        selectedFiles.value.push(props.fileList[i].key)
      }
    }
  } else {
    selectedFiles.value = [ itemId ]
  }
  lastItem.value = item
}

const scrollToItem = (index: number) => {
  scrollerHistoryVm.value && scrollerHistoryVm.value.scrollToItem(index)
}

watch(() => props.fileList, () => {
  console.log('>>>>>>>>>>>  执行')
  updateScroller()
}, { deep: true })

onMounted(() => {
  // emits('loadingMore')
  console.log('>>>>>>>>>>>> props.fileList ', props.fileList)
})

defineExpose<{
  handleCleanPicked: () => void;
  scrollToItem: (index: number) => void;
}>({
  handleCleanPicked,
  scrollToItem
});

</script>

<template>
  <div ref="$rootElement" class="history-list-wrapper">
    <div class="history-list-header">
      <div class="history-list-header-left">
        <div class="checkbox" v-if="fileList.length">
          <TDCheckbox
            ref="selectAllRef"
            :model-value="selectAll"
            :indeterminate="indeterminate"
            @update:model-value="handleCheckChange" />
        </div>
        <span class="history-list-header-title" v-if="!selectedFiles.length">文件名</span>
        <div v-else class="selected-status">
          <span>已选中 {{ selectedFiles.length }} 项</span>
          <span class="cancel-selected" @click="handleCleanPicked">取消选择</span>
        </div>
        <Toolbar
          :is-show="!!selectedFiles.length"
          :primary-operation="toolbarPrimaryOperation"
          @menu-item-click="handleToolbarMenuItemClick"
        />
      </div>
      <div class="history-list-header-size">大小</div>
      <div class="history-list-header-time">时间</div>
    </div>
    <Loading v-if="loading && !fileList.length" />
    <DefaultPage v-else-if="!fileList.length"></DefaultPage>
    <RecycleScroller
      v-else
      v-slot="{ item, index }"
      ref="scrollerHistoryVm"
      key-field="key"
      :items="fileList"
      :item-size="60"
      @scroll-end="handleListScrollEnd"
      class="history-list"
      data-scroll-container
    >
      <TaskItem
        :fileItem="item"
        :index="index"
        :selectedFiles="selectedFiles"
        :expiresTime="expiresTime"
        @contextmenu="handleContextMenu"
        @delete="() => emits('delete', item)"
        @collection="() => emits('collection', item)"
        @unCollection="() => emits('unCollection', item)"
        @singleClick="handleItemClick"
        @checkboxClick="handleCheckboxClick"
        @operationWithType="handleOperationWithType"
      />
    </RecycleScroller>
  </div>
</template>


<style lang="scss" scoped>
.history-list-wrapper {
  flex-direction: column;
  flex-grow: 1;
  min-height: 0;
  display: flex;
  position: relative;
  height: 100%;

  .history-list {
    margin-top: 4px;
    overflow-y: overlay !important;
    &:first-of-type {
      margin-top: 0;
    }
  }
}
.history-list-header {
  height: 40px;
  color: var(--font-font-1);
  flex-shrink: 0;
  align-items: center;
  margin: 0 40px;
  font-size: 12px;
  line-height: 22px;
  display: flex;
  gap: 32px;
  .selected-status {
    margin-left: 12px;
    color: var(--font-font-1);

  }
  .cancel-selected {
    margin-left: 12px;
    color: var(--primary-primary-default);
    cursor: pointer;
  }
}
.history-list-header-title {
  margin-left: 12px;
}
.history-list-header-left {
  flex-grow: 1;
  align-items: center;
  display: flex;
}
.history-list-header-size {
  width: 65px;
}
.history-list-header-time {
  width: 120px;
}
</style>