macro(find_and_include_config lib_name)
    string(COMPARE EQUAL "${${lib_name}_INCLUDE}" "" result)
    if(result)
        string(TOLOWER ${lib_name} lib_name_lower)
        list(APPEND CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR}/3rdparty/${lib_name_lower})
        include(${lib_name_lower}Config)
    endif()
    link_directories(${${lib_name}_LIBRARY_DIRS})
endmacro()


MACRO(USE_3RD_PARTY_LIBRARY lib_name)
    find_and_include_config(${lib_name})
    target_include_directories (${PROJECT_NAME} PRIVATE 
        ${${lib_name}_INCLUDE}
    )
    target_link_libraries( ${PROJECT_NAME} 
        PRIVATE ${${lib_name}_LIBS}
    )
    IF (MSVC)  
        list(APPEND ${PROJECT_NAME}_dependencies ${${lib_name}_BINS})
    ELSEIF (APPLE)
        #list(APPEND ${PROJECT_NAME}_dependencies XL_LIB_STATIC_BINS)
    ELSE ()
        message(FATAL_ERROR "Now is '${CMAKE_SYSTEM_NAME}' OS. not supported yet.")
    ENDIF ()
ENDMACRO()
