﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0C58C6DB-B666-4C7F-8900-7939B3BEE7D0}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>InstallEntry</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)OnlineResource\</OutDir>
    <IntDir>..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)OnlineResource\</OutDir>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
    <IntDir>..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;INSTALLENTRY_EXPORTS;ETW_LOGGER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>false</SDLCheck>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <AdditionalIncludeDirectories>..\..\..\thunder\inc;..\..\..\thunder\3rd\inc\SDK;..\..\..\thunder\3rd\inc;..\..\..\thunder\3rd\inc\WTL;..\..\ThunderInstall\7zUncompress;..\..\..\thunder\inc\XDL;..\..\3rd\inc;..\..\ThunderInstall\Common;..\..\ThunderInstall\Duilib\include;..\..\ThunderInstall\ThunderInstallInfo</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>InstallEntry.def</ModuleDefinitionFile>
      <AdditionalLibraryDirectories>..\..\..\thunder\lib\$(Configuration);..\..\..\thunder\3rd\lib\$(Configuration);..\..\lib\$(Configuration);..\..\3rd\lib\$(Configuration);</AdditionalLibraryDirectories>
      <AdditionalDependencies>Wininet.lib;Gdiplus.lib;peer_id.lib;DuiLib.lib;crypt32.lib;ws2_32.lib;winmm.lib;wldap32.lib;libeay32.lib;libcurl.lib;7zUncompress.lib;ThunderInstallInfo.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ImportLibrary>$(IntDir)$(TargetName).lib</ImportLibrary>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <ProgramDatabaseFile>..\..\pdb\$(Configuration)\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;_USRDLL;INSTALLENTRY_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>InstallEntry.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MinSpace</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;INSTALLENTRY_EXPORTS;ETW_LOGGER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>false</SDLCheck>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <AdditionalIncludeDirectories>..\..\..\thunder\inc;..\..\..\thunder\3rd\inc\SDK;..\..\..\thunder\3rd\inc;..\..\..\thunder\3rd\inc\WTL;..\..\ThunderInstall\7zUncompress;..\..\..\thunder\inc\XDL;..\..\3rd\inc;..\..\ThunderInstall\Common;..\..\ThunderInstall\Duilib\include;..\..\ThunderInstall\ThunderInstallInfo</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\..\thunder\lib\$(Configuration);..\..\..\thunder\3rd\lib\$(Configuration);..\..\lib\$(Configuration);..\..\3rd\lib\$(Configuration)</AdditionalLibraryDirectories>
      <AdditionalDependencies>Wininet.lib;Gdiplus.lib;peer_id.lib;DuiLib.lib;crypt32.lib;ws2_32.lib;winmm.lib;wldap32.lib;libeay32.lib;libcurl.lib;7zUncompress.lib;ThunderInstallInfo.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ModuleDefinitionFile>InstallEntry.def</ModuleDefinitionFile>
      <ProgramDatabaseFile>..\..\pdb\$(Configuration)\$(TargetName).pdb</ProgramDatabaseFile>
      <ImportLibrary>$(IntDir)$(TargetName).lib</ImportLibrary>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;INSTALLENTRY_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>InstallEntry.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AutoHandle.h" />
    <ClInclude Include="BasePage.h" />
    <ClInclude Include="BaseTool.h" />
    <ClInclude Include="BindSoftware.h" />
    <ClInclude Include="DownloadSDK.h" />
    <ClInclude Include="FileHelper.h" />
    <ClInclude Include="HttpDownloader.h" />
    <ClInclude Include="HttpSession.h" />
    <ClInclude Include="HttpStat.h" />
    <ClInclude Include="InstallApplication.h" />
    <ClInclude Include="InstallFailPage.h" />
    <ClInclude Include="InstallFinishPage.h" />
    <ClInclude Include="InstallFollowConfig.h" />
    <ClInclude Include="InstallPage.h" />
    <ClInclude Include="InstallRemoteConfig.h" />
    <ClInclude Include="MainWindow.h" />
    <ClInclude Include="MD5Checksum.h" />
    <ClInclude Include="MD5ChecksumDefines.h" />
    <ClInclude Include="OnlineLoopPic.h" />
    <ClInclude Include="RegHelper.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="sha256.h" />
    <ClInclude Include="ShellHelper.h" />
    <ClInclude Include="StartPage.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="SyncHttps.h" />
    <ClInclude Include="SyncHttpsClient.h" />
    <ClInclude Include="SystemHelper.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="unicode.h" />
    <ClInclude Include="version.h" />
    <ClInclude Include="VersionProxy.h" />
    <ClInclude Include="xlstat_helper.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="BaseTool.cpp" />
    <ClCompile Include="BindSoftware.cpp" />
    <ClCompile Include="dllmain.cpp">
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
      </PrecompiledHeader>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </PrecompiledHeader>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="DownloadSDK.cpp" />
    <ClCompile Include="FileHelper.cpp" />
    <ClCompile Include="HttpDownloader.cpp" />
    <ClCompile Include="HttpSession.cpp" />
    <ClCompile Include="HttpStat.cpp" />
    <ClCompile Include="InstallApplication.cpp" />
    <ClCompile Include="InstallEntry.cpp" />
    <ClCompile Include="InstallFailPage.cpp" />
    <ClCompile Include="InstallFinishPage.cpp" />
    <ClCompile Include="InstallFollowConfig.cpp" />
    <ClCompile Include="InstallPage.cpp" />
    <ClCompile Include="InstallRemoteConfig.cpp" />
    <ClCompile Include="MainWindow.cpp" />
    <ClCompile Include="MD5Checksum.cpp" />
    <ClCompile Include="OnlineLoopPic.cpp" />
    <ClCompile Include="RegHelper.cpp" />
    <ClCompile Include="ShellHelper.cpp" />
    <ClCompile Include="StartPage.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="SyncHttps.cpp" />
    <ClCompile Include="SyncHttpsClient.cpp" />
    <ClCompile Include="SystemHelper.cpp" />
    <ClCompile Include="VersionProxy.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="InstallEntry.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="InstallEntry.def" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>