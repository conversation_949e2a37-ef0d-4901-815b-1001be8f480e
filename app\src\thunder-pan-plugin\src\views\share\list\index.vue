<script setup lang="ts">
import FileListHeader from './header.vue';
import ShareItem from './item.vue';

import { computed, nextTick, onMounted, ref, useTemplateRef } from 'vue';
import { API_SHARE } from '@root/common/thunder-pan-manager/pan-sdk/types';
import { SharePageManager } from '@/manager/share-page-manager';
import { FileOperationHelper } from '@/utils/file-operation';
import { CreateCommonContextmenu } from '@root/common/components/ui/contextmenu';
import { EShareRecordOperation } from '@root/common/thunder-pan-manager/pan-sdk/utils/file';
import { GlobalEventHelper } from '@/utils/global-event-helper';
import DragSelect from '@root/common/components/utils/drag-select';
import { useElementVisibility } from '@vueuse/core';

const tailText = '复制这段内容后打开手机迅雷App，查看更方便'

const $rootElement = useTemplateRef<HTMLDivElement>('$rootElement')
const rootElementIsVisible = useElementVisibility($rootElement)
const scrollerVm = ref<any>(null)

const props = withDefaults(defineProps<{
  fileList: API_SHARE.DriveShareMgrData[]
  pickedIds: string[]
}>(), {
  fileList: () => [],
  pickedIds: () => []
});

const emit = defineEmits<{
  (e: 'scrollEnd'): void
}>()

const canPickIds = computed(() => props.fileList.map(file => file.share_id!))

const selectedFiles = computed(() => {
  const list: API_SHARE.DriveShareMgrData[] = []
  props.fileList.forEach(file => {
    if (props.pickedIds.includes(file.share_id!)) {
      list.push(file)
    }
  })
  return list
})

const isHeaderIndeterminate = computed(() => {
  return !!props.pickedIds.length && props.pickedIds.length !== canPickIds.value.length
})

const isHeaderSelectAll = computed(() => {
  return !!props.pickedIds.length && props.pickedIds.length === canPickIds.value.length
})

function handleScrollEnd() {
  emit('scrollEnd')
}

function handleHeaderCleanPicked() {
  SharePageManager.getInstance().cleanPicked()
}

function handlePickedIdsChange(isSelectAll: boolean) {
  if (!isSelectAll) {
    SharePageManager.getInstance().cleanPicked()
  } else {
    SharePageManager.getInstance().setPickedIds(canPickIds.value)
  }
}

function handleCopyLink(files: API_SHARE.DriveShareMgrData[]) {
  FileOperationHelper.getInstance().batchCopyShareData(files, tailText)
}

function handleCancelShare(files: API_SHARE.DriveShareMgrData[]) {
  FileOperationHelper.getInstance().batchCancelShare(files.map(f => f.share_id!)).then(ids => {
    if (ids.length) {
      SharePageManager.getInstance().cleanPicked()
      SharePageManager.getInstance().batchDeleteItems(ids)
      updateScroller()
    }
  })
}

async function handleContextMenu (event: MouseEvent, file: API_SHARE.DriveShareMgrData) {
  if (!props.pickedIds.includes(file.share_id!)) {
    SharePageManager.getInstance().togglePickedId(file.share_id!, true)
  }
  await nextTick()

  CreateCommonContextmenu({
    parentElement: $rootElement.value!,
    menuList:  [
      [
        {
          key: EShareRecordOperation.COPY_LINK,
          name: '复制链接',
          iconLeft: 'xl-icon-general-copy-m',
          disabled: selectedFiles.value.some(file => (file.title ?? '') === '')
        },
        {
          key: EShareRecordOperation.DELETE,
          name: '取消分享',
          iconLeft: 'xl-icon-general-cancel-m',
        },
      ]
    ],
    clickPosition: {
      x: event.clientX,
      y: event.clientY,
    },
    onMenuItemClick: (item) => {
      handleContextmenuItemClick(item.key)
    }
  })
}

function handleContextmenuItemClick (key: string) {
  switch (key) {
    case EShareRecordOperation.COPY_LINK: {
      FileOperationHelper.getInstance().batchCopyShareData(selectedFiles.value, tailText)
      break
    }
    case EShareRecordOperation.DELETE: {
      FileOperationHelper.getInstance().batchCancelShare(props.pickedIds).then(ids => {
        if (ids.length) {
          SharePageManager.getInstance().cleanPicked()
          SharePageManager.getInstance().batchDeleteItems(ids)
          updateScroller()
        }
      })
      break
    }
  }
}

function updateScroller() {
  // RecycleScroller 有 bug ，更新列表里面的 DOM 不更新，这里需要手动调里面的方法来更新
  if (scrollerVm.value) {
    scrollerVm.value.updateVisibleItems(true)
  }
}

onMounted(() => {
  // 监听框选事件
  GlobalEventHelper.getInstance().on(GlobalEventHelper.EventKey.DRAG_SELECT_END, (selectArea: HTMLElement, picked: HTMLElement[]) => {
    if (!rootElementIsVisible.value) return

    const { startIndex, lastIndex } = DragSelect.getDragSelectIndex(picked, selectArea, 56)
    // 从数据中遴选出相应被选中的数据
    const pickList = props.fileList.slice(startIndex!, lastIndex! + 1)
    const pickedIds = pickList.map(file => canPickIds.value.includes(file.share_id!) ? file.share_id! : '').filter(Boolean)

    if (pickedIds && pickedIds.length) {
      SharePageManager.getInstance().setPickedIds(pickedIds)
    }
  })
})

defineExpose({
  updateScroller
})
</script>

<template>
  <div ref="$rootElement" class="share-file-list-container drag-select__body">
    <FileListHeader
      class="disable-drag-select"
      :select-all="isHeaderSelectAll"
      :selected-files="selectedFiles"
      :indeterminate="isHeaderIndeterminate"
      @copy-link="handleCopyLink"
      @cancel-share="handleCancelShare"
      @clean-picked="handleHeaderCleanPicked"
      @check-change="handlePickedIdsChange"
    />

    <RecycleScroller
      v-if="fileList.length"
      v-slot="{ item, index }"
      ref="scrollerVm"
      class="share-file-list__wrapper drag-select__content"
      key-field="share_id"
      data-scroll-container
      :items="fileList"
      :item-size="56"
      @scroll-end="handleScrollEnd"
    >
      <ShareItem
        class="drag-select__item"
        :file="item"
        :picked-ids="pickedIds"
        :data-index="index"
        :style="{
          'margin-top': '4px',
        }"
        @copy-link="handleCopyLink"
        @cancel-share="handleCancelShare"
        @right-click="handleContextMenu"
      />
    </RecycleScroller>
  </div>
</template>

<style scoped lang="scss">
.share-file-list-container {
  flex-grow: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  .share-file-list__wrapper {
    flex-grow: 1;
    min-height: 0;
    overflow: overlay !important;
  }
}
</style>
