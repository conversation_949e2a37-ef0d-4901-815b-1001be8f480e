import {
    Dialog,
    OpenDialogReturnValue,
    ipc<PERSON><PERSON><PERSON>
} from 'electron';
import { TaskManager } from "@root/common/task/impl/task-manager";
import * as TaskBaseType from '@root/common/task/base';
import { AplayerStack } from "@root/common/player/client/aplayer-stack";
import * as PlayerBaseType from '@root/common/player/base'
import { GroupTask } from "@root/common/task/impl/group-task";
import { BtTask } from "@root/common/task/impl/bt-task";
import { FileSystemAWNS } from '@root/common/fs-utilities'
import { ThunderHelper } from '@root/common/thunder-helper'
import * as path from 'path'
import { AsyncRemoteCall } from '@root/common/renderer-async-remote-call';
import { LinkHubHelper } from '@root/common/link-hub/impl/link-hub-helper'
import { LinkRecordHelper } from '@root/common/link-hub/impl/link-record-helper'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper';
import { CmdShow } from '@root/common/window-define';
import { DkHelper } from '@root/common/task/impl/dk-helper';
import { DownloadFileRecord } from '@root/common/download-file-record/impl/download-file-record'
import { server } from '@xunlei/node-net-ipc/dist/ipc-server'
import { Channels } from '@root/common/constant';

export namespace ConsumeManagerNs {
    export async function init() {
        server.registerFunctions({
            ConsumeManagerPlayTask: async (c: any, context: any, taskId: number, fileIndex: number): Promise<boolean> => {
                return await playTask(taskId, fileIndex);
            },
            ConsumeManagerConsumeFile: async (c: any, context: any, filePath: string): Promise<boolean> => {
                return await consumeFile(filePath);
            },
            ConsumeManagerOpenTaskFolder: async (c: any, context: any, taskId: number): Promise<boolean> => {
                return await openTaskFolder(taskId);
            },
            ConsumeManagerShowItemInFolder: async (c: any, context: any, filePath: string) => {
                return await showItemInFolder(filePath);
            },

            ConsumeManagerConsumeTask: async (c: any, context: any, taskId: number, fileIndex: number): Promise<boolean> => {
                return await consumeTask(taskId, fileIndex);
            },
            ConsumeManagerConsumeLink: async (c: any, context: any, params: ThunderClientAPI.dataStruct.dataModals.LinkRecord): Promise<boolean> => {
                return await consumeLink(params);
            },

            ConsumeManagerConsumePlaybackInfo: async (c: any, context: any, info: ThunderClientAPI.dataStruct.dataModals.PlaybackRecord) => {
                return await consumePlaybackInfo(info);
            }
        });
    }

    export async function playTask(taskId: number, fileIndex: number): Promise<boolean> {
        let task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) {
            return false;
        }

        if (!await task.isSupportPlay()) {
            return false;
        }

        let taskBase = task.getTaskBase();
        AplayerStack.GetInstance().openMedia({
            name: taskBase.taskName!,
            gcid: taskBase.gcid!,
            playUrl: '',
            playFrom: '',
            zipPlay: 0,
            dlnaPlay: 0,
            mediaType: PlayerBaseType.MediaType.MtDownload,
            task: {
                taskId,
                fileIndex,
                url: taskBase.url
            }
        });
        return true;
    }

    export async function consumeFile(filePath: string): Promise<boolean> {
        if (!await FileSystemAWNS.existsAW(filePath)) {
            return false;
        }

        const ext: string = path.extname(filePath);
        if (ext.toLowerCase() === '.torrent') {
            // TODO 创建bt任务
        }

        let answerName = Channels.ShellExecuteAnswer + (new Date).getTime().toString() + Math.random().toString();
        ipcRenderer.send(Channels.ShellExecute, 'open', filePath, '', '', CmdShow.SW_SHOW, answerName);
        return await new Promise((v) => {
            ipcRenderer.once(answerName, (event, ret: number) => {
                v(ret != undefined && ret >32);
            });
        });
    }

    export async function openTaskFolder(taskId: number): Promise<boolean> {
        let task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) {
            return false;
        }

        let taskBase = task.getTaskBase();
        let filePath = path.join(task.getSavePath(), task.getTaskName());
        if (taskBase.taskType === TaskBaseType.TaskType.Bt || taskBase.taskType === TaskBaseType.TaskType.Group) {
            return await showItemInFolder(filePath);
        }

        if (taskBase.taskStatus === TaskBaseType.TaskStatus.Succeeded) {
            return await showItemInFolder(filePath);
        }

        if (taskBase.taskType === TaskBaseType.TaskType.P2sp) {
            if (await FileSystemAWNS.existsAW(filePath + '.td')) {
                return await showItemInFolder(filePath + '.td');
            }

            if (await FileSystemAWNS.existsAW(filePath + '.xltd')) {
                return await showItemInFolder(filePath + '.xltd');
            }

            return await showItemInFolder(filePath);
        } else if (taskBase.taskType == TaskBaseType.TaskType.Emule) {
            if (await FileSystemAWNS.existsAW(filePath + '.emule.td')) {
                return await showItemInFolder(filePath + '.emule.td');
            }

            if (await FileSystemAWNS.existsAW(filePath + '.xltd')) {
                return await showItemInFolder(filePath + '.xltd');
            }

            if (await FileSystemAWNS.existsAW(filePath + '.emule.xltd')) {
                return await showItemInFolder(filePath + '.emule.xltd');
            }

            return await showItemInFolder(filePath);
        }

        return false;
    }

    export async function showItemInFolder(filePath: string) {
        const shell: any = await AsyncRemoteCall.GetInstance().getShell();
        if (!await FileSystemAWNS.existsAW(filePath)) {
            filePath = path.dirname(filePath);
            if (!await FileSystemAWNS.existsAW(filePath)) {
                return false;
            }
            await shell.openPath(filePath);
            return true;
        }

        await shell.showItemInFolder(filePath);
        return true;
    }

    export async function showOpenDialog(): Promise<string> {
        const parent: any = await await AsyncRemoteCall.GetInstance().getCurrentWindow();
        const dialog: any = await await AsyncRemoteCall.GetInstance().getDialog();
        if (parent && !(await parent.isDestroyed())) {
            let res: OpenDialogReturnValue = await (dialog as Dialog).showOpenDialog(parent, { properties: ['openDirectory'] });
            if (!res.canceled) {
                return res.filePaths[0];
            }
        }

        return '';
    }

    export async function consumeTask(taskId: number, fileIndex: number): Promise<boolean> {
        let task = await TaskManager.GetInstance().findTaskById(taskId);
        if (!task) {
            return false;
        }

        let bRet: boolean = false;
        let taskBase = task.getTaskBase();
        if (taskBase.taskType === TaskBaseType.TaskType.Group) {
            do {
                let groupTask = task.toExtra<GroupTask>();
                if (fileIndex === -1) {
                    if (await task.isSupportPlay() && await playTask(taskId, fileIndex)) {
                        bRet = true;
                        break;
                    }

                    if (await groupTask.getDownloadCount() > 1) {
                        bRet = await showItemInFolder(taskBase.savePath!);
                        break;
                    }

                    fileIndex = 0;
                }

                let subTaskId = await groupTask.getSubTaskIdByIndex(fileIndex);
                if (subTaskId === 0) {
                    break;
                }
                let subTask = await TaskManager.GetInstance().findTaskById(subTaskId);
                if (await subTask!.isSupportPlay()) {
                    bRet = await playTask(taskId, fileIndex);
                    break;
                }

                let filePath = path.join(subTask!.getSavePath(), subTask!.getTaskName());
                if (subTask!.getTaskStatus() === TaskBaseType.TaskStatus.Succeeded) {
                    if (await consumeFile(filePath)) {
                        bRet = true;
                        break;
                    }
                }

                bRet = await showItemInFolder(filePath);
            } while (false);
        } else if (taskBase.taskType === TaskBaseType.TaskType.Bt) {
            do {
                let btTask = task.toExtra<BtTask>();
                if (fileIndex === -1) {
                    if (await task.isSupportPlay() && await playTask(taskId, fileIndex)) {
                        bRet = true;
                        break;
                    }

                    if (await btTask.getDownloadCount() > 1) {
                        bRet = await showItemInFolder(path.join(taskBase.savePath!, taskBase.taskName!));
                        break;
                    }

                    // 消费第一个
                    let btFileInfos = await btTask.getBtFileInfos();
                    fileIndex = btFileInfos[0].realIndex;
                }

                let btSubFile = await btTask.getBtFileByIndex(fileIndex);
                if (btSubFile!.isSupportPlay() && await playTask(taskId, fileIndex)) {
                    bRet = true;
                    break;
                }

                let filePath = path.join(task.getSavePath(), btSubFile!.getFilePath());
                if (btSubFile!.getStatus() === TaskBaseType.BtSubFileStatus.Complete) {
                    if (await consumeFile(filePath)) {
                        bRet = true;
                        break;
                    }
                }

                bRet = await showItemInFolder(filePath);
            } while (false);
        } else {
            do {
                if (await task.isSupportPlay() && await playTask(taskId, fileIndex)) {
                    bRet = true;
                    break;
                }

                let filePath = path.join(task.getSavePath(), task.getTaskName());
                if (task.getTaskStatus() === TaskBaseType.TaskStatus.Succeeded) {
                    if (await consumeFile(filePath)) {
                        bRet = true;
                        break;
                    }
                }

                bRet = await showItemInFolder(filePath);
            } while (false)
        }
        return bRet;
    }


    export async function consumeLink(params: ThunderClientAPI.dataStruct.dataModals.LinkRecord): Promise<boolean> {
        console.log('consumeLink, params=', params)
        let t1 = (new Date()).getTime();
        if (params && params.can_video_play) {
            let subFileInfos: PlayerBaseType.LinkSubFileInfo[] = [];
            if (params.url_type === 'magnet') {
                // TODO 先不获取，有性能问题
                // const getLinkParam: ThunderClientAPI.dataStruct.dataModals.GetLinkRecordsParam = {
                //     config: {
                //         dataSrcConfig: {
                //             dataSourceType: 'MEDIA_TYPE',
                //             dataSourceKey: 'MEDIA_VIDEO,MEDIA_AUDIO',
                //             reserved: params.url_hash
                //         },
                //     },
                //     // TODO 底层写数据库有性能问题，先只拉少量
                //     limitCount: 20,
                //     reload: false
                // }

                // const res: ThunderClientAPI.dataStruct.dataModals.GetLinkRecordsResult = await LinkHubHelper.getInstance().getLinks(getLinkParam);
                // // TODO 先不判断resutl
                // if (res && res.records && res.records.links) {
                //     res.records.links.forEach((link) => {
                //         subFileInfos.push({ index: link.file_index, name: link.name });
                //     });
                // }
                // console.log('consumeLink, time=', (new Date()).getTime() - t1, ',subFileInfos=', subFileInfos, ',res=', res)
            }


            AplayerStack.GetInstance().openMedia({
                name: params.name,
                gcid: params.gcid,
                playUrl: '',
                playFrom: '',
                zipPlay: 0,
                dlnaPlay: 0,
                mediaType: PlayerBaseType.MediaType.MtUniversal,
                universal: {
                    links: [
                        {
                            name: params.name,
                            url: params.url,
                            index: params.is_dir ? -1 : params.file_index,
                            subFileInfos,
                        }
                    ],
                    index: 0
                }
            });

            return true;
        }

        let index = params.url_type === 'magnet' ? params.file_index : -1
        let locaFilePath = await DownloadFileRecord.GetInstance().query(params.url, index);
        if (locaFilePath && locaFilePath.length > 0 && await FileSystemAWNS.existsAW(locaFilePath)) {
            return await consumeFile(locaFilePath);
        }

        // toast 提示
        window.__VueGlobalProperties__.$message({
            message: '当前文件不支持预览，请下载后查看',
             type: 'warning'
        })

        // 拉起新建面板
        // let taskType = DkHelper.getTaskTypeFromUrl(params.url);
        // let data: ThunderNewTaskHelperNS.INewTaskData = {
        //     url: params.url,
        //     fileName: params.name,
        //     taskType,
        // }
        // ThunderNewTaskHelperNS.showPreCreateTaskWindow([data], {
        //     title: '当前文件不支持预览，请下载后查看',
        //     defaultCheckedFileIndexes: [`${params.url}-${params.file_index}`],
        //     showLaterButton: false,
        //     showPlayButton: false,
        // });
        return false;
    }

    export async function consumePlaybackInfo(info: ThunderClientAPI.dataStruct.dataModals.PlaybackRecord) {
        console.log('consumePlaybackInfo, params=', info)
        if ((info as any).source === 'drive') {
            AplayerStack.GetInstance().openMedia({
                name: info.name,
                gcid: info.gcid,
                playUrl: '',
                playFrom: '',
                zipPlay: 0,
                dlnaPlay: 0,
                mediaType: PlayerBaseType.MediaType.MtPan,
                pan: {
                    panFileId: (info as any).source_id,
                    panRatioId: '',
                    panSpace: info.space,
                }
            });
        } else {
            AplayerStack.GetInstance().openMedia({
                name: info.name,
                gcid: info.gcid,
                playUrl: '',
                playFrom: '',
                zipPlay: 0,
                dlnaPlay: 0,
                mediaType: PlayerBaseType.MediaType.MtUniversal,
                universal: {
                    links: [
                        {
                            name: info.name,
                            url: info.params.url ?? '',
                            index: info.file_index,
                            subFileInfos: [{index: info.file_index, name: info.name}],
                        }
                    ],
                    index: 0
                }
            });
        }

        return true;
    }
}