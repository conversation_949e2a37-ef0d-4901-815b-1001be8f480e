/**
 * Automatic Generate
 * 自动生成不需要手动修改
 */

interface IPubKey {
  alg: string
  salt: string
}

const WinPubKeys: IPubKey[] = [
	{
		"alg": "md5",
		"salt": "DIdtwvn3jtzt3leuloiH"
	},
	{
		"alg": "md5",
		"salt": "Wc5k9u9HDsb1H1nXJe91oWy5x8lF4tcDcGunJX0p5Jq+F"
	},
	{
		"alg": "md5",
		"salt": "JNYVgzhYaiJihX/XPT3fFstlqlKr4OiDnB+tdDcuaoLJUxO95a08Vcy"
	},
	{
		"alg": "md5",
		"salt": "yAFQfPw+6EXbjBx9bi+"
	},
	{
		"alg": "md5",
		"salt": "jl/iJ+P1O8bGpww6VbO2c82wvbT9yaf2yjmSh1x6N9e5IgaHprad9dm9PWrOH9Z"
	},
	{
		"alg": "md5",
		"salt": "dDGNQ6qvqcgyDd/k+FBO+yp9"
	},
	{
		"alg": "md5",
		"salt": "uac2oUpDJIfibCqw"
	},
	{
		"alg": "md5",
		"salt": "XxOFK"
	},
	{
		"alg": "md5",
		"salt": "j80h4"
	}
];
const MacPubKeys: IPubKey[] = [
	{
		"alg": "md5",
		"salt": "i"
	},
	{
		"alg": "md5",
		"salt": "S2Zsx55z3y7RIyjKRNAe"
	},
	{
		"alg": "md5",
		"salt": "BTrSqw"
	},
	{
		"alg": "md5",
		"salt": "y2j7JIq4l/mOj0Op1G"
	},
	{
		"alg": "md5",
		"salt": "IkVQyq4K4BVy0xqe"
	},
	{
		"alg": "md5",
		"salt": "f24BJB/ngYlW+Se7eIjuSIaxaQ"
	},
	{
		"alg": "md5",
		"salt": "U3v8GDOXH7JfG1BbiKpr0DCr0"
	},
	{
		"alg": "md5",
		"salt": "xmjSygWB97/9C9m17Y08/ZwgCvb"
	},
	{
		"alg": "md5",
		"salt": "Q/b7jisZxxyKAva9EESZKSAHntB"
	},
	{
		"alg": "md5",
		"salt": "uSo5sVL7n"
	},
	{
		"alg": "md5",
		"salt": "l+MQQEO8Qj3nj2cKi"
	},
	{
		"alg": "md5",
		"salt": "xrvZkpEkMbLtLeXaB9dsPwc97F"
	},
	{
		"alg": "md5",
		"salt": "NVfGAasF2kLzFQy3yZGzX3fzo1fLJWd"
	}
];

export function getPubKeys () {
  return process.platform === 'darwin' ? MacPubKeys : WinPubKeys;
}
