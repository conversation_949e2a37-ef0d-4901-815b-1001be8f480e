#include "aplayer_ipc_client.h"
#include "XLIPC/xlipc_factory.h"
#include <xlcommon.h>
#define CHECK_APLAYER() \
	if (!aplayer_container_) \
	{ \
		break; \
	}

APlayerIPCClient::APlayerIPCClient()
	: connect_session_(NULL)
	, ipc_client_(NULL)
{
}

APlayerIPCClient::~APlayerIPCClient()
{
}

long APlayerIPCClient::Connect(const wchar_t* server_id, const wchar_t* client_id)
{
	long ret = -1;
	server_id_ = server_id;
	client_id_ = client_id;
	XLIPCFactory* xlipc_factory = XLIPCFactory::GetInstance();
	ipc_client_ = xlipc_factory->CreateIPCClient(server_id, client_id);
	/*std::string strServerId;
	xl::text::transcode::Unicode_to_UTF8(server_id, wcslen(server_id), strServerId);*/
	//ipc_client_ = new XLIPCClient(server_id, client_id);
	if (ipc_client_)
	{
		ipc_client_->SetCallback(this);
		ret = ipc_client_->Connect();
	}
	return ret;
}

void APlayerIPCClient::OnStateChanged(LONG nOldState, LONG nNewState)
{
	do
	{
		if (nOldState == 5 && nNewState == 6) 
		{
			/*::MessageBoxA(NULL, "压制完成", MB_OK, NULL);
			::exit(0);*/
		}
		if (!connect_session_)
		{
			break;
		}
		const char* event_name = "OnStateChanged";
		XLIPCStream* stream = new XLIPCStream();
		stream->WriteLong(nOldState);
		stream->WriteLong(nNewState);
		connect_session_->AsynCall(event_name, stream);
		stream->Release();
	} while (0);
}

void APlayerIPCClient::OnOpenSucceeded(void)
{
	do
	{
		/*CHECK_APLAYER();
		long l = 0;
		long ret = aplayer_container_->SetVolume(50,&l);
		ret = aplayer_container_->Play();*/
		if (!connect_session_)
		{
			break;
		}
		
		const char* event_name = "OnOpenSucceeded";
		connect_session_->AsynCall(event_name, NULL);
	} while (0);
}

void APlayerIPCClient::OnSeekCompleted(LONG nPosition)
{
	do
	{
		if (!connect_session_)
		{
			break;
		}
		const char* event_name = "OnSeekCompleted";
		XLIPCStream* stream = new XLIPCStream();
		stream->WriteLong(nPosition);
		connect_session_->AsynCall(event_name, stream);
		stream->Release();
	} while (0);
}

void APlayerIPCClient::OnBuffer(LONG nPercent)
{
	do
	{
		if (!connect_session_)
		{
			break;
		}
		const char* event_name = "OnBuffer";
		XLIPCStream* stream = new XLIPCStream();
		stream->WriteLong(nPercent);
		connect_session_->AsynCall(event_name, stream);
		stream->Release();
	} while (0);
}

void APlayerIPCClient::OnVideoSizeChanged(void)
{
	do
	{
		if (!connect_session_)
		{
			break;
		}
		const char* event_name = "OnVideoSizeChanged";
		connect_session_->AsynCall(event_name, NULL);
	} while (0);
}

void APlayerIPCClient::OnDownloadCodec(BSTR strCodecPath)
{
	do
	{
		if (!connect_session_)
		{
			break;
		}
		const char* event_name = "OnDownloadCodec";
		XLIPCStream* stream = new XLIPCStream();
		stream->WriteUnicode(strCodecPath);
		connect_session_->AsynCall(event_name, stream);
		stream->Release();
	} while (0);
}

void APlayerIPCClient::OnTrackLoaded(LONG type, LONG errorCode, const std::string& extra)
{
	do
	{
		if (!connect_session_)
		{
			break;
		}
		const char* event_name = "OnTrackLoaded";
		XLIPCStream* stream = new XLIPCStream();
		stream->WriteLong(type);
		stream->WriteLong(errorCode);
		stream->WriteUtf8(extra.c_str());
		connect_session_->AsynCall(event_name, stream);
		stream->Release();
	} while (0);
}

void APlayerIPCClient::OnEvent(LONG nEventCode, LONG nEventParam)
{
	do
	{
		if (!connect_session_)
		{
			break;
		}
		const char* event_name = "OnEvent";
		XLIPCStream* stream = new XLIPCStream();
		stream->WriteLong(nEventCode);
		stream->WriteLong(nEventParam);
		connect_session_->AsynCall(event_name, stream);
		stream->Release();
	} while (0);
}

void APlayerIPCClient::OnDropFiles(const std::wstring& files)
{
	do
	{
		if (!connect_session_)
		{
			break;
		}
		const char* event_name = "OnDropFiles";
		XLIPCStream* stream = new XLIPCStream();
		stream->WriteUnicode(files.c_str());
		connect_session_->AsynCall(event_name, stream);
		stream->Release();
	} while (0);
}

bool APlayerIPCClient::FilterWindowMessage(LONG msg)
{
	bool ret = false;
	MapWindowMsgFilter::iterator it = map_window_msg_filter_.find(msg);
	if (it != map_window_msg_filter_.end())
	{
		ret = true;
	}
	return ret;
}

long APlayerIPCClient::OnConnect(XLIPCConnectSession* connect_session)
{
	XL_SPDLOG_INFO("APlayerIPCClient::OnConnect");
	connect_session_ = connect_session;
	connect_session_->AddRef();

	m_playerImpl.SetConnection(connect_session_);
	m_interface.SetDelegate(&m_playerImpl);
	m_interface.Register(connect_session);
	return 0;
}

long APlayerIPCClient::OnConnectFailed(int error_code)
{
	/*if (ipc_client_)
	{
		XLIPCFactory* xlipc_factory = XLIPCFactory::GetInstance();
		xlipc_factory->DestroyIPCClient(ipc_client_);
		ipc_client_ = NULL;
	}*/
	// 不反初始化aplayer，直接强杀进程
	// ::PostQuitMessage(error_code);
	::TerminateProcess(GetCurrentProcess(), error_code);
	return 0;
}

long APlayerIPCClient::OnDisconnect(int error_code)
{
	//APlayerInterface::SetDelegate(NULL);
	//if (connect_session_)
	//{
	//	connect_session_->Release();
	//	connect_session_ = NULL;
	//}
	//if (ipc_client_)
	//{
	//	XLIPCClient* ipc_client = ipc_client_;
	//	ipc_client->AddRef();
	//	ipc_client_->Release();
	//	ipc_client_ = NULL;
	//	XLIPCFactory* xlipc_factory = XLIPCFactory::GetInstance();
	//	xlipc_factory->DestroyIPCClient(ipc_client);
	//}
	//// 不反初始化aplayer，直接强杀进程
	//// DestroyAPlayer();
	//// ::PostQuitMessage(error_code);
	//::TerminateProcess(GetCurrentProcess(), error_code);
	//ETW_LEAVE();
	return 0;
}