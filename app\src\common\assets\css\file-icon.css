.file-icon-type {
  display: inline-block;
  width: 40px;
  height: 40px;
  vertical-align: top;
  border-radius: 6px;
  background: url('@root/common/assets/img/icons/file-icon.png') -80px -560px no-repeat;
  background-size: 840px auto;
  vertical-align: top;

  &.is-middle {
    zoom: 0.8;
  }

  &.is-small-middle {
    zoom: 0.6;
  }

  &.is-small {
    zoom: 0.5333;
  }

  &.is-large {
    zoom: 1.5;
  }

  &.file-type-folder {
    background-position: 0px 0px;
  }

  &.file-type-folder-restore {
    background-position: -80px 0px;
  }

  &.file-type-folder-download {
    background-position: -160px 0px;
  }

  &.file-type-folder-collection {
    background-position: -240px 0px;
  }

  &.file-type-folder-safe {
    background-position: -320px 0px;
  }

  &.file-type-folder-bt {
    background-position: -400px 0px;
  }

  &.file-type-folder-compress {
    background-position: 0px -80px;
  }

  &.file-type-folder-thunder-box {
    background-position: -80px -80px;
  }

  &.file-type-folder-smb {
    background-position: -160px -80px;
  }

  &.file-type-folder-fluent {
    background-position: -240px -80px;
  }

  &.file-type-folder-privilege {
    background-position: -320px -80px;
  }

  &.file-type-video {
    background-position: 0px -160px;
  }

  &.file-type-m3u8 {
    background-position: -80px -160px;
  }

  &.file-type-img {
    background-position: -160px -160px;
  }

  &.file-type-bt {
    background-position: -240px -160px;
  }

  &.file-type-link {
    background-position: -320px -160px;
  }

  &.file-type-zip {
    background-position: -400px -160px;
  }

  &.file-type-music {
    background-position: -480px -160px;
  }

  &.file-type-subtitle {
    background-position: -560px -160px;
  }

  &.file-type-html {
    background-position: 0px -240px;
  }

  &.file-type-code {
    background-position: -80px -240px;
  }

  &.file-type-gif {
    background-position: -160px -240px;
  }

  &.file-type-text {
    background-position: 0px -320px;
  }

  &.file-type-document {
    background-position: -80px -320px;
  }

  &.file-type-pdf {
    background-position: -160px -320px;
  }

  &.file-type-word {
    background-position: -240px -320px;
  }

  &.file-type-ppt {
    background-position: -320px -320px;
  }

  &.file-type-excel {
    background-position: -400px -320px;
  }

  &.file-type-keynote {
    background-position: -480px -320px;
  }

  &.file-type-epub {
    background-position: -560px -320px;
  }

  &.file-type-mobi {
    background-position: -640px -320px;
  }

  &.file-type-chm {
    background-position: -720px -320px;
  }

  &.file-type-numbers {
    background-position: -480px -320px;
  }

  &.file-type-apk {
    background-position: 0px -400px;
  }

  &.file-type-iso {
    background-position: -80px -400px;
  }

  &.file-type-exe {
    background-position: -160px -400px;
  }

  &.file-type-dmg {
    background-position: -240px -400px;
  }

  &.file-type-ipa {
    background-position: -320px -400px;
  }

  &.file-type-ipsw {
    background-position: 0px -480px;
  }

  &.file-type-dll {
    background-position: -80px -480px;
  }

  &.file-type-pkg {
    background-position: -160px -480px;
  }

  &.file-type-unknown {
    background-position: 0px -560px;
  }

  &.file-type-default {
    background-position: -80px -560px;
  }
}
