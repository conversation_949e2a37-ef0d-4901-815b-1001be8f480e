import { Account<PERSON><PERSON><PERSON> } from '@root/common/account/impl/accountHelper'
import { AccountHelperEventKey } from '@root/common/account/account-type'
import { AccountHelperServer } from '@root/common/account/server/accountHelper'
import { configServer } from '@root/common/config/server/config'
import { ApplicationManager } from '@root/common/thunder-pan-manager/manager/application'
import { ThunderPanServerSDK } from '@root/common/thunder-pan-manager/server'
import { GetResourceAppPath, GetXxxNodePath, GetProfilesPath, GetDkAddonNodeName, GetLogsPath } from '@root/common/xxx-node-path'
import { CryptoUtility } from '@xbase/electron_base_kit'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { server } from '@xunlei/node-net-ipc/dist/ipc-server'
import { mainRendererContext } from '@xunlei/node-net-ipc/dist/ipc-base';
import requireNodeFile from '@root/common/require-node-file'
import * as path from 'path'
import { PopUpNS } from '@root/common/pop-up'
import { AplayerStack } from '@root/common/player/client/aplayer-stack'
import * as BaseType from '@root/common/player/base'
import { DkServer } from '@root/common/task/server/server';
import { VipManager } from '@root/common/vipdownload/vip-manager'

import { LinkHubServer } from '@root/common/link-hub/server/linkhub-server'
import { FileAssociationNS } from '@root/common/config/server/file-association'
import { initClipboard } from '@root/main-renderer/src/common/clipboard'
import { TaskManager as TaskManagerClient } from '@root/common/task/client/task-manager';
import { DownloadFileRecord } from '@root/common/download-file-record/impl/download-file-record'
import { UiOperationServer } from '@root/common/ui-operation/server/server';
import { DownloadModelManagerImpl } from '@root/common/ui-operation/impl/download-model'
import { ConsumeManagerNs } from '@root/common/consume/impl/consume'
import { DownloadAssistNs } from '@/common/download-assist'

let thunderHelper = requireNodeFile(
  path.join(GetXxxNodePath(), 'thunder_helper.node')
)
global.__xdasObjectLiftMonitor = thunderHelper;
PopUpNS.enableDevTools();

if (process.platform === 'darwin') {
  server.start({}, 'thunder')
  client.start({ name: mainRendererContext }, 'thunder', false, (...args) => {
    console.log('client callback', ...args)
  })
} else {
  server.start({})
  client.start({ name: mainRendererContext })
}
const dk: any = requireNodeFile(path.join(GetXxxNodePath(), GetDkAddonNodeName()));
dk.initAddon({
  dbDir: GetProfilesPath(),
  dbName: 'NewTaskDb.dat',
  logDir: GetLogsPath(),
  dkCfgDir: path.join(GetProfilesPath(), 'dkcfg'),
  torrentsCacheDir: path.join(GetProfilesPath(), 'Torrents'),
  tempDir: path.join(GetProfilesPath(), 'temp'),
  versionName: '********',//xmpHelper.getFileVersion(process.execPath),
  versionCode: '1203000000',
  appName: process.platform === 'darwin' ? 'mac_aplayer' : 'xmp', // pc下载库没用到
  appKey: process.platform === 'darwin' ? 'bWFjX2FwbGF5ZXIAsBcB' : 'xzcGMuWE1QX1A7MA^^SDK==26bfbf7a346bcf2ac776d5b7e1cb1c66',
});
DkServer.init();
UiOperationServer.init();
DownloadModelManagerImpl.GetInstance().init()
AplayerStack.GetInstance().initApiProxy();

// 通知browser进程ipc就绪（browser进程需要在此时机后启动ipc连接）
ipcRenderer.send('ipc-server-ready');
initModules().catch();

async function initModules(): Promise<void> {
  // 初始化配置模块
  configServer.init().catch();
  // 初始化登录模块
  AccountHelper.getInstance().initSDK();
  AccountHelperServer.init();
  LinkHubServer.init();
  initClipboard();
  TaskManagerClient.GetInstance().init();
  // 检查文件关联
  FileAssociationNS.asynCheckAndAssociate();

  // 云盘服务模块初始化
  const ThunderPanServerSDKConfig = {
    deviceId: await AccountHelper.getInstance().getDeviceID(),
    appVersion: ApplicationManager.getCurrentDeviceClientVersion(),
    appVersionCode: ApplicationManager.getCurrentDeviceClientVersion(),
    userId: async () => {
      const user = await AccountHelper.getInstance().getUserInfo()
      return user.sub!
    },
    md5: (str) => CryptoUtility.createMDAlgorithms().MD5StringSync(str),
    request: (options) => AccountHelper.getInstance().request(options)
  }
  ThunderPanServerSDK.getInstance().init(ThunderPanServerSDKConfig)

  ConsumeManagerNs.init();
}

// import { server } from '@xunlei/node-net-ipc/dist/ipc-server'
// server.start({})

// import { TaskManager } from '@root/common/task/impl/task-manager';
// import * as BaseType from '@root/common/task/base';
// import { DkHelper } from '@root/common/task/impl/dk-helper'
// // 任务详情变更
// TaskManager.GetInstance().attachTaskDetailChangeEvent(async (taskId: number, flags: number) => {
//     let task = await TaskManager.GetInstance().findTaskById(taskId);
//     console.log('task detail change, taskid=', taskId, ',dowloadsize=', task?.getDownloadSize(), ',speed=', task?.getDownloadSpeed());
// });

// // 任务状态变更
// TaskManager.GetInstance().attachTaskStatusChangeEvent((taskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
//     console.log('task status change, taskid=', taskId, ',old=', eOld, ',new=', eNew);
// });

// let categoryManager = TaskManager.GetInstance().GetCategoryManager();
// categoryManager.attachInsertEvent((categoryId: number, viewId: BaseType.CategoryViewID, reason: BaseType.TaskInsertReason, ids: number[]) => {
//     // 新建任务、从新下载、任务下载完成、任务删除到回收站、从回收站恢复任务，都会触发此事件
//     console.log('task insert to some category view categoryid=', categoryId, ',viewId=', viewId, ',reason=', reason, ',ids=', ids);
// });
// categoryManager.attachRemoveEvent((categoryId: number, viewId: BaseType.CategoryViewID, ids: number[]) => {
//     // 新建任务、从新下载、任务下载完成、任务删除到回收站、从回收站恢复任务，都会触发此事件
//     console.log('task remove from some categroy view categoryid=', categoryId, ',viewId=', viewId, ',ids=', ids);
// });

// setTimeout(async () => {
//     // 加载任务
//     let category = await categoryManager.getCategoryById(-1);
//     // 下载tab中的任务
//     let downloadView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Downloading);
//     let ids = downloadView!.getTasks();
//     for (let id of ids) {
//         let task = await TaskManager.GetInstance().findTaskById(id);
//         let b = task!.isBackground(); // 判断是否是是后台任务来决定是否要显示该任务
//         console.log('load task, download view,task name=', task!.getTaskName());
//     }
//     // 已完成tab中的任务
//     let completeView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Completed);
//     ids = completeView!.getTasks();
//     for (let id of ids) {
//         let task = await TaskManager.GetInstance().findTaskById(id);
//         console.log('load task, complete view, task name=', task!.getTaskName());
//     }
//     // 已删除到回收站的任务
//     let recycleView = category!.getCategoryViewFromId(BaseType.CategoryViewID.Recycle);
//     ids = recycleView!.getTasks();
//     for (let id of ids) {
//         let task = await TaskManager.GetInstance().findTaskById(id);
//         console.log('load task, recycle view, task name=', task!.getTaskName());
//     }



//     let taskSavePath: string = 'D:\\work\\xl\\thunder_2025\\bin\\download';
//     // let task = await TaskManager.GetInstance().createTask({
//     //     taskInfo: {
//     //         background: false,
//     //         taskType: BaseType.TaskType.P2sp,
//     //         taskBaseInfo: {
//     //             savePath: taskSavePath,
//     //             taskName: '12311111.mp4'
//     //         }
//     //     },
//     //     p2spInfo: {
//     //         url: 'http://aplayer.open.xunlei.com/test.mp4'
//     //     }
//     // });
//     // task!.start();

//     // let magnetTask = await TaskManager.GetInstance().createTask({
//     //     taskInfo: {
//     //         background: true, // 磁力换种子任务一般不显示在下载列表
//     //         taskType: BaseType.TaskType.Magnet,
//     //         taskBaseInfo: {
//     //             savePath: taskSavePath,
//     //             taskName: 'test.torrent'
//     //         }
//     //     },
//     //     magnetInfo: {
//     //         url: 'magnet:?xt=urn:btih:6594021d90991d9f527dfc6ae2b1d9b4092ef72e&dn=%e9%98%b3%e5%85%89%e7%94%b5%e5%bd%b1dygod.org.%e4%b8%8d%e8%af%b4%e8%af%9d%e7%9a%84%e7%88%b1.2025.HD.1080P.%e5%9b%bd%e8%af%ad%e4%b8%ad%e8%8b%b1%e5%8f%8c%e5%ad%97.mkv&tr=udp%3a%2f%2ftracker.opentrackr.org%3a1337%2fannounce&tr=udp%3a%2f%2fexodus.desync.com%3a6969%2fannounce',
//     //         torrentFilePath: taskSavePath
//     //     }
//     // });
//     // magnetTask!.start();

//     // //等待种子下载完成后，解析bt种子【例子里面可以先保证种子存在】
//     // let info = DkHelper.parseBtTaskInfo(path.join(taskSavePath, 'test.torrent'));
//     // console.log('bt info=', info);
//     // let btTask = await TaskManager.GetInstance().createTask({
//     //     taskInfo: {
//     //         background: false,
//     //         taskType: BaseType.TaskType.Bt,
//     //         taskBaseInfo: {
//     //             savePath: taskSavePath,
//     //             taskName: info.title
//     //         }
//     //     },
//     //     btInfo: {
//     //         origin: 'magnet:?xt=urn:btih:6594021d90991d9f527dfc6ae2b1d9b4092ef72e&dn=%e9%98%b3%e5%85%89%e7%94%b5%e5%bd%b1dygod.org.%e4%b8%8d%e8%af%b4%e8%af%9d%e7%9a%84%e7%88%b1.2025.HD.1080P.%e5%9b%bd%e8%af%ad%e4%b8%ad%e8%8b%b1%e5%8f%8c%e5%ad%97.mkv&tr=udp%3a%2f%2ftracker.opentrackr.org%3a1337%2fannounce&tr=udp%3a%2f%2fexodus.desync.com%3a6969%2fannounce',
//     //         seedFile: path.join(taskSavePath, 'test.torrent'),
//     //         displayName: info.title,
//     //         fileRealIndexLists: [0],//选中的需要下载的bt文件的index
//     //         fileLists: info.fileLists,
//     //         infoId: info.infoId,
//     //         tracker: '',
//     //     }
//     // });
//     // btTask!.start();
// }, 5000);

import { initialVueApp } from '@root/common/vue-app-initial'
import { ipcRenderer } from 'electron'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
import App from './App.vue'
import { router } from './router'
import { TaskManager } from '@root/common/task/impl/task-manager'

const pinia = createPinia()
const app = createApp(App).use(router)
initialVueApp(app)

app.use(pinia)
app.mount('#app')

// AplayerStack.GetInstance().openMedia({
//   name: 'test',
//   gcid: '',
//   playUrl: "http://aplayer.open.xunlei.com/test.mp4",
//   playFrom: '',
//   zipPlay: 0,
//   dlnaPlay: 0,
//   mediaType: BaseType.MediaType.MtNewXmpLocal,
//   // task: {
//   //   taskId: 72980852,
//   //   fileIndex: -1,
//   // }
// });
VipManager.getInstance().init({
  peerid: '123456',
  versionName: '********',
  versionCode: **********,
  clientName: process.platform === 'darwin' ? 'xl_xmp_mac' : 'xl_xmp_win',
  host: process.platform === 'darwin' ? 'xmp-mac-speed-auth-vip.xunlei.com' : 'xmp-win-speed-auth-vip.xunlei.com',
  //clientName: process.platform === 'darwin' ? 'xl_xmp_mac' : 'xl_xdas',
  //host: 'ali-pc-x-speed-auth-vip.xunlei.com',
  seq: 1
})

let onLoginSucc = async() => {
  if (AccountHelper.getInstance().isSignIn) {
    let userInfo = await AccountHelper.getInstance().getUserInfo();
    TaskManager.GetInstance().GetCategoryManager().setCurrentPanUserId((userInfo as any).id!)
  }
}
AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, () => {
  onLoginSucc()
})
if (AccountHelper.getInstance().isSignIn) {
  onLoginSucc();
}
AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_OUT, () => {
  AplayerStack.GetInstance().closePlayWindow();
})
DownloadFileRecord.GetInstance().init();
DownloadAssistNs.init();