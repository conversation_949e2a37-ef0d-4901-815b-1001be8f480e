<script setup lang="ts">
import { PopUpNS } from '@root/common/pop-up'
import Login from './login/index.vue'
import Register from './register/index.vue'
import { ref, onMounted } from 'vue'
import * as PopUpTypes from '@root/common/pop-up/types'
import { usePositionMixinComponent } from '@root/modal-renderer/src/common/mixins';
import { AccountHelper } from '@root/common/account/client/accountHelper'
import { LottieAnimation } from 'lottie-web-vue'
import { quitApp } from '@root/common/quit-promises'


// 使用基类的逻辑
const { overridePosition } = usePositionMixinComponent();

// 重写控制位置基类非响应式数据
overridePosition({
  relatePos: PopUpTypes.RelatePosType.CenterParent,
  autoSize: true,
  show: true,
  selector: '.login-register-container',
})

const curStatus = ref('login')
const isLogging = ref(false)

const handleClose = async () => {
  console.log('login dialog closed')
  const currentWindow = PopUpNS.getCurrentWindow()
  currentWindow.close()

  if (!await AccountHelper.getInstance().isSignIn()) {
    quitApp()
  }
}

const handleAutoLogin = async () => {
  isLogging.value = true
  try {
    await AccountHelper.getInstance().autoSignIn();

    console.log('ready 自动登录成功')
    handleClose()
  } catch (e) {
    console.log('自动登录失败', e)
  } finally {
    isLogging.value = false
  }
}


onMounted(() => {
  console.log('登录组件挂载完成');
  if (localStorage.getItem('XLLoginPrompt.autoLogin') === 'true') {
    handleAutoLogin()
  }
});

</script>

<template>

  <div class="login-register-container draggable">
    <Button variant="ghost" is-icon size="sm" class="login-register-container-close none-draggable"
      @click="handleClose"> <i class="xl-icon-general-close-m"></i> </Button>

    <div v-if="isLogging" class="login-register-container-body login-register-container-loading draggable">
      <span>正在登录</span>

      <div class="login-register-container-loading-logo">
        <inline-svg :src="require('@root/common/assets/img/ic_xunlei_logo.svg')" />
      </div>

      <div class="login-register-container-loading-body">
        <LottieAnimation class="magnet-parsing-loading"
          :animation-data="require('@root/common/assets/lottie/login-loading.json')" :auto-play="true" :loop="true" />
      </div>
    </div>

    <template v-else>
      <div v-show="curStatus === 'login'" class="login-register-container-body none-draggable">
        <Login @toRegister="curStatus = 'register'" />
      </div>

      <div v-show="curStatus === 'register'" class="login-register-container-body none-draggable">
        <Register @to-login="curStatus = 'login'" />
      </div>
    </template>



  </div>

</template>

<style scoped lang="scss">
.login-register-container {
  position: relative;
  margin: 0 auto;
  width: 400px;
  height: 490px;
  box-sizing: border-box;
  overflow: hidden;
  user-select: none;
  background: var(--background-background-elevated, #FFF);
  box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);

  &-body {
    height: 100%;
  }

  &-loading {
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-top: 64px;

    &-logo {
      margin: 68px auto 48px;
    }

    &-body {
      width: 160px;
    }

    span {
      display: inline-block;
      margin-top: 18px;
      color: var(--font-font-1, #272E3B);
      font-size: 24px;
      line-height: 32px;
    }
  }

  &-close {
    position: absolute;
    top: 18px;
    right: 18px;
    z-index: 10;
    color: var(--font-font-3, #86909C);
  }
}
</style>
