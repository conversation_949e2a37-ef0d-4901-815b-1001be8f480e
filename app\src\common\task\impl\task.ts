import * as BaseType from '../base'
import { BtTask } from './bt-task'
import { EmuleTask } from './emule-task'
import { GroupTask } from './group-task'
import { P2spTask } from './p2sp-task'

export class GlobalTaskStartHook {
  static allowDownload: (() => Promise<boolean>) | null = null;
}

interface TaskCache {
  taskId: number
  taskType: BaseType.TaskType
  isPanTask: boolean | undefined
  gcid: string
  cid: string
}
export class Task {
  private taskCache: TaskCache = {
    taskId: 0,
    taskType: BaseType.TaskType.Unkown,
    isPanTask: undefined,
    gcid: '',
    cid: '',
  };
  private nativeTask: any
  private extra: P2spTask | GroupTask | EmuleTask | BtTask | null = null;

  constructor(taskId: number, nativeTask: any) {
    this.nativeTask = nativeTask
    this.taskCache.taskId = taskId
    this.taskCache.taskType = this.nativeTask.getType()
  }

  public getId (): number {
    return this.taskCache.taskId
  }

  public getType (): BaseType.TaskType {
    return this.taskCache.taskType
  }

  public isPanTask (): boolean {
    if (this.taskCache.isPanTask !== undefined) {
      return this.taskCache.isPanTask
    }
    this.taskCache.isPanTask = this.nativeTask.isPanTask() === 1 ? true : false
    return this.taskCache.isPanTask
  }

  public getPanFileIdInfo (): BaseType.TaskPanFileInfo {
    return this.nativeTask.getPanFileIdInfo()
  }

  public isUserRead (): boolean {
    return (this.nativeTask.getAttribute(BaseType.TaskAttribute.UserRead) as number) === 1 ? true : false
  }

  public setUserRead (bRead: boolean) {
    // TODO
  }

  public isOpenOnComplete (): boolean {
    return (this.nativeTask.getAttribute(BaseType.TaskAttribute.OpenOnComplete) as number) === 1 ? true : false
  }

  public setOpenOnComplete (bOpen: boolean) {
    // TODO
  }

  public getTaskStatus (): BaseType.TaskStatus {
    return (this.nativeTask.getAttribute(BaseType.TaskAttribute.TaskStatus) as number) as BaseType.TaskStatus
  }

  public getDownloadSpeed (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.DownloadSpeed) as number
  }

  public getVipSpeed (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.VipSpeed) as number
  }

  public getDownloadSize (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.DownloadSize) as number
  }

  public getFileSize (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.FileSize) as number
  }

  public getGcid (): string {
    if (this.taskCache.gcid.length > 0) {
      return this.taskCache.gcid
    }
    this.taskCache.gcid = this.nativeTask.getAttribute(BaseType.TaskAttribute.Gcid) as string
    return this.taskCache.gcid
  }

  public getCid (): string {
    if (this.taskCache.cid.length > 0) {
      return this.taskCache.cid
    }
    this.taskCache.cid = this.nativeTask.getAttribute(BaseType.TaskAttribute.Cid) as string
    return this.taskCache.cid
  }

  public getTaskName (): string {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.TaskName) as string
  }

  public getSavePath (): string {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.SavePath) as string
  }

  public getErrorCode (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.ErrorCode) as number
  }

  public getRecycleTime (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.RecycleTime) as number
  }

  public getCompletionTime (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.CompletionTime) as number
  }

  public getDownloadingPeriod (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.DownloadingPeriod) as number
  }

  public getUrl (): string {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.Url) as string
  }

  public setSavePath (path: string): void {
    return this.nativeTask.setAttribute(BaseType.TaskAttribute.SavePath, path)
  }

  public getCreateTime (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.CreateTime) as number
  }

  public getOrigin (): string {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.Origin) as string
  }

  public isBackground (): boolean {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.IsBackground) as boolean
  }

  public async start (): Promise<void> {
    if (!this.isBackground() && GlobalTaskStartHook.allowDownload && (!await GlobalTaskStartHook.allowDownload())) {
      return
    }

    await new Promise((v: (p: boolean) => void) => {
      this.nativeTask.start(() => {
        v(true)
      })
    })
  }

  // 直接开始任务，不排队
  public async startNoWait (): Promise<void> {
    if (!this.isBackground() && GlobalTaskStartHook.allowDownload && (!await GlobalTaskStartHook.allowDownload())) {
      return
    }

    await new Promise((v: (p: boolean) => void) => {
      this.nativeTask.startNoWait(() => {
        v(true)
      })
    })
  }

  public async stop (reason: BaseType.TaskStopReason): Promise<void> {
    await new Promise((v: (p: boolean) => void) => {
      this.nativeTask.stop(reason, () => {
        v(true)
      })
    })
  }

  public toExtra<T> (): T {
    if (!this.extra) {
      let extra = this.nativeTask.toTaskExtra()
      let eType = this.getType()
      if (eType === BaseType.TaskType.P2sp) {
        this.extra = new P2spTask(extra)
      } else if (eType === BaseType.TaskType.Group) {
        this.extra = new GroupTask(extra)
      } else if (eType === BaseType.TaskType.Emule) {
        this.extra = new EmuleTask(extra)
      } else if (eType === BaseType.TaskType.Bt) {
        this.extra = new BtTask(extra)
      } else {
        throw Error()
      }
    }
    return this.extra as T
  }

  public setDescription (desc: string): void {
    this.nativeTask.setDescription(desc)
  }

  public async recycle (): Promise<number> {
    return await new Promise((resolve) => {
      this.nativeTask.recycle((res: number) => {
        resolve(res)
      })
    })
  }

  public async recoverFromRecycle (): Promise<number> {
    return await new Promise((resolve) => {
      this.nativeTask.recoverFromRecycle((res: number) => {
        resolve(res)
      })
    })
  }

  public async deleteTask (isDelete: boolean): Promise<number> {
    return await new Promise((resolve) => {
      this.nativeTask.deleteTask(isDelete, (res: number) => {
        resolve(res)
      })
    })
  }

  public async reDownload (): Promise<number> {
    return await new Promise((resolve) => {
      this.nativeTask.reDownload((res: number) => {
        resolve(res)
      })
    })
  }

  public async rename (name: string): Promise<BaseType.ReNameResult> {
    return await new Promise((resolve) => {
      this.nativeTask.rename(name, (res: number) => {
        resolve(res as BaseType.ReNameResult)
      })
    })
  }

  public async move (name: string): Promise<BaseType.MoveTaskToNewPathResult> {
    return await new Promise((resolve) => {
      this.nativeTask.move(name, (res: number) => {
        resolve(res as BaseType.MoveTaskToNewPathResult)
      })
    })
  }

  public isOriginOnlyStrategy (): boolean {
    return this.nativeTask.isOriginOnlyStrategy() ? true : false
  }

  public setDownloadStrategy (strategy: BaseType.DownloadStrategy, index: number): void {
    this.nativeTask.setDownloadStrategy(strategy, index)
  }

  public async isSupportPlay (): Promise<boolean> {
    if (this.getType() === BaseType.TaskType.Bt) {
      let btTask = this.toExtra<BtTask>()
      await btTask.waitSubLoadFinish()
    }
    return (this.nativeTask.isSupportPlay() === 1)
  }

  public getVideoFileCount (): number {
    return this.nativeTask.getVideoFileCount()
  }

  public async getPlayUrl (fileIndex: number): Promise<string> {
    return await new Promise((resolve) => {
      this.nativeTask.getPlayUrl(fileIndex, (url: string) => {
        resolve(url)
      })
    })
  }

  public enableDcdnWithVipCert (vipCert: string, fileIndex: number): void {
    return this.nativeTask.enableDcdnWithVipCert(vipCert, fileIndex)
  }

  public disableDcdnWithVipCert (fileIndex: number): void {
    return this.nativeTask.disableDcdnWithVipCert(fileIndex)
  }

  public updateDcdnWithVipCert (vipCert: string, fileIndex: number): void {
    return this.nativeTask.updateDcdnWithVipCert(vipCert, fileIndex)
  }

  public redirectOriginalResource (url: string): void {
    return this.nativeTask.redirectOriginalResource(url)
  }

  public setTaskDownloadSpeedLimit (unSpeed: number): void {
    return this.nativeTask.setTaskDownloadSpeedLimit(unSpeed)
  }

  public setPanTaskIdcSpeedLimit (unSpeed: number): void {
    return this.nativeTask.setPanTaskIdcSpeedLimit(unSpeed)
  }

  public setTaskUrl (url: string): void {
    return this.nativeTask.setTaskUrl(url)
  }

  public setTaskExtStat (fileIndex: number, key: string, value: string): void {
    return this.nativeTask.setTaskExtStat(fileIndex, key, value)
  }

  public setUserData (key: string, field: string, value: string): void {
    return this.nativeTask.setUserData(key, field, value)
  }

  getUserData (key: string, field: string, defaultValue: string): string {
    return this.nativeTask.getUserData(key, field, defaultValue)
  }

  public setStopReason (reason: BaseType.TaskStopReason): void {
    return this.nativeTask.setStopReason(reason)
  }

  public getStopReason (): BaseType.TaskStopReason {
    return this.nativeTask.getStopReason()
  }

  public getCategoryViewId (): BaseType.CategoryViewID {
    return this.nativeTask.getCategoryViewId() as BaseType.CategoryViewID
  }

  public getCategoryId (): number {
    return this.nativeTask.getAttribute(BaseType.TaskAttribute.CategoryId) as number
  }

  public getTaskBase (): BaseType.TaskBase {
    return this.nativeTask.getTaskBase() as BaseType.TaskBase
  }
}