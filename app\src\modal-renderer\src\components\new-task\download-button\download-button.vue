<!--
使用示例 (Usage Examples):

1. 默认模式 - 使用 store 中的下载类型
<DownloadButton 
  scene="default"
  :disabled="false"
  @submit="handleDownloadSubmit"
/>

2. 强制本地下载模式 - 父组件指定为本地下载，不会影响 store
<DownloadButton 
  scene="local-only"
  :disabled="false"
  :selected-path-type="DownloadPathType.Local"
  @submit="handleDownloadSubmit"
/>

3. 强制云盘下载模式 - 父组件指定为云盘下载，不会影响 store
<DownloadButton 
  scene="cloud-only"
  :disabled="false"
  :selected-path-type="DownloadPathType.Cloud"
  @submit="handleDownloadSubmit"
/>

注意事项:
- 当 selectedPathType 被指定时，用户的路径选择操作不会更新全局 store 中的下载类型
- 但仍会更新对应的路径 store (本地路径或云盘路径)
- setButtonType() 方法在 selectedPathType 被指定时会被忽略
-->

<template>
  <div
    class="download-button"
    ref="downloadButtonRef"
  >
    <!-- 下载到本地 -->
    <div
      class="download-button-wrapper"
      v-tooltip="{
        content: fullPathTooltipText,
        arrow: true,
        theme: 'new-task',
      }"
    >
      <Button
        class="download-btn"
        v-if="buttonType === DownloadPathType.Local"
        size="lg"
        :style="{
          width: '100%',
        }"
        :has-right-icon="true"
        right-icon="xl-icon-triangle-down"
        :disabled="disabled"
        @click="handleConfirm(DownloadPathType.Local)"
        @right-icon-click="handleRightIconClick"
      >
        下载到（
        <span class="path-text">{{ currentTaskSavePath }}</span>
        ）
      </Button>
      <Button
        v-if="buttonType === DownloadPathType.Cloud"
        :style="{
          width: '100%',
        }"
        size="lg"
        hasRightIcon
        :disabled="disabled"
        @click="handleConfirm(DownloadPathType.Cloud)"
        rightIcon="xl-icon-triangle-down"
        @rightIconClick="handleRightIconClick"
      >
        添加到（
        <span class="path-text">{{ currentCloudSavePath }}</span>
        ）
      </Button>
    </div>

    <!-- 新增：磁盘或云盘空间不足提示 -->
    <div class="space-warning">
      <div
        class="download-tip-container"
        v-if="buttonType === DownloadPathType.Cloud"
      >
        <!-- 下载到云盘，领免费云添加次数 -->
        <div class="download-tip-cloud">
          <p
            class="download-tip-cloud-content content"
            @click.stop="gotoHomePage"
          >
            <SvgIcon
              class="warning-icon"
              type="warning2"
            />
            <span>云添加剩余次数：</span>
            <span class="free-cloud-add-count warning-tip">{{ freeCloudAddCount }}次</span>
            <span class="arrow-right">></span>
          </p>
        </div>

        <!-- <div
                class="download-tip-local"
                v-if="downloadType === 'local'"
              >
                <p class="download-tip-local-content content">
                  <span>扫码看广告，</span>
                  <span class="highlight">领免费云添加次数</span>
                  <span class="arrow-right">></span>
                </p>
              </div> -->
      </div>
      <div
        class="download-tip-container"
        v-if="buttonType === DownloadPathType.Local && isDiskSpaceInsufficient"
      >
        <div class="download-tip-cloud">
          <p class="download-tip-cloud-content content">
            <SvgIcon
              class="warning-icon"
              type="warning2"
            />
            <span>磁盘空间不足</span>
            <!-- <span class="free-cloud-add-count highlight">查看解决方案</span>
                  <span class="arrow-right highlight">></span> -->
          </p>
        </div>
      </div>
    </div>

    <Transition name="dropdown-fade">
      <div
        class="select-path-type"
        v-show="selectPathVisible && !disabled"
        ref="selectPathTypeRef"
        @click="handleSelectPathClick"
      >
        <div
          class="path-type-item"
          @click="handleSelectDownloadPath"
        >
          <span class="path-type-text">本地</span>
          <i class="xl-icon-arrow-right path-type-icon"></i>
        </div>
        <div
          class="path-type-item"
          @click="handleSelectCloudPath"
        >
          <span class="path-type-text">云盘</span>
          <i class="xl-icon-arrow-right path-type-icon"></i>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, type Ref } from 'vue'
import SvgIcon from '@root/common/components/ui/Dialog/svg-icon.vue'
import { onClickOutside, onKeyStroke } from '@vueuse/core'
import { useDownloadPathStore } from '@root/modal-renderer/src/stores/download-path'
import { useDownloadCloudPathStore } from '@root/modal-renderer/src/stores/download-cloud-path'
import { useDownloadPathTypeStore } from '@root/modal-renderer/src/stores/download-path-type'
import { useDriveStore } from '@root/modal-renderer/src/stores/drive'
import { useUserStore } from '@root/modal-renderer/src/stores/user'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { PopUpNS } from '@root/common/pop-up'
import * as PopUpTypes from '@root/common/pop-up/types'
import { DownloadPathNS } from '@root/common/config/download-path'
import { DownloadPathType } from '@root/modal-renderer/types/new-task.type'
import type { DownloadButtonSubmitParams } from '@root/modal-renderer/types/new-task.type'
import { ThunderHelper } from '@root/common/thunder-helper'

import {
  getDistanceToScreenRight,
  isDistanceToRightGreaterThan,
} from '@root/modal-renderer/src/screen.help'

// 导入 inject
import { inject } from 'vue'
import { config } from '@root/common/config/config'

// 新增：注入窗口失焦状态
const isWindowBlurred = inject<Ref<boolean>>('isWindowBlurred', ref(false))

// 新增：注入磁盘空间不足状态
const isDiskSpaceInsufficient = inject<Ref<boolean>>('isDiskSpaceInsufficient', ref(false))

// 新增：注入云添加剩余次数
const freeCloudAddCount = inject<Ref<number>>('freeCloudAddCount', ref(0))

function gotoHomePage() {
  ThunderHelper.openURLByDefault('https://www.xunlei.com')
}
// 新增：监听窗口失焦状态变化
watch(
  () => isWindowBlurred.value,
  isBlurred => {

    if (isBlurred) {
      // 窗口失焦时，关闭下拉菜单
      selectPathVisible.value = false
    }
  }
)

// 新增：监听磁盘空间不足状态变化
watch(
  () => isDiskSpaceInsufficient.value,
  isInsufficient => {
    console.log('[DownloadButton] 磁盘空间不足状态变化:', isInsufficient)

    if (isInsufficient) {
      console.log('[DownloadButton] 检测到磁盘空间不足，可能需要禁用下载按钮或显示警告')
    }
  }
)

// 新增：监听云添加剩余次数变化
watch(
  () => freeCloudAddCount.value,
  count => {
    console.log('[DownloadButton] 云添加剩余次数变化:', count)
  }
)

// 定义 Props
interface Props {
  disabled?: boolean
  /**
   * 场景编号 - 用于标识不同使用场景
   */
  scene?: string
  /**
   * 选中的下载路径类型 - 父组件指定的下载路径类型
   * 当父组件传递此参数时，将优先使用指定的类型，且不会更改 store 中的下载类型
   * @optional
   */
  selectedPathType?: DownloadPathType
}

const fullPathTooltipText = computed(() => {
  if (buttonType.value === DownloadPathType.Local) {
    return currentTaskSavePath.value
  } else if (buttonType.value === DownloadPathType.Cloud) {
    return currentCloudSavePath.value
  } else {
    return ''
  }
})

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  scene: 'default',
  selectedPathType: undefined,
})

// 定义事件
const emit = defineEmits<{
  submit: [params: DownloadButtonSubmitParams]
}>()

// 定义常量
const DRIVELIST_POPUP_WIDTH = 376 // drivelist 弹窗宽度
const CLOUD_PATH_POPUP_WIDTH = 360 // cloud-path 弹窗宽度

// 引入 download-path store
const downloadPathStore = useDownloadPathStore()

// 引入 download-cloud-path store
const downloadCloudPathStore = useDownloadCloudPathStore()

// 引入 download-path-type store
const downloadPathTypeStore = useDownloadPathTypeStore()

// 引入 drive store
const driveStore = useDriveStore()

// 引入 user store
const userStore = useUserStore()

// 直接通过 store 访问属性，不解构，保持响应性
// 计算当前任务保存路径用于显示
const currentTaskSavePath = computed(() => {
  const path = downloadPathStore.currentDownloadPath || ''
  console.log('[DownloadButton] currentTaskSavePath 计算:', path)
  return path
})

// 计算当前云盘路径用于显示
const currentCloudSavePath = computed(() => {
  console.log(
    '[DownloadButton] currentCloudSavePath 计算:',
    downloadCloudPathStore.currentCloudPath
  )
  return downloadCloudPathStore.currentPathDisplayName
})

const selectPathVisible = ref<boolean>(false)

// 按钮类型控制变量，优先使用父组件传递的类型，否则默认本地下载
const buttonType = ref<DownloadPathType>(DownloadPathType.Local)

// 监听 selectedPathType prop 的变化，如果父组件有传递类型，则使用父组件的类型
watch(
  () => props.selectedPathType,
  newSelectedPathType => {
    if (newSelectedPathType !== undefined) {
      console.log('[DownloadButton] 父组件指定下载路径类型:', newSelectedPathType)
      buttonType.value = newSelectedPathType
    } else {
      // 如果父组件没有指定类型，则使用 store 中的类型
      const storeType = downloadPathTypeStore.currentPathType
      console.log('[DownloadButton] 使用 store 中的下载路径类型:', storeType)
      buttonType.value = storeType
    }
  },
  { immediate: true }
)

// 监听 selectPathVisible 不展示时， 隐藏所有路径选择弹窗
watch(selectPathVisible, newValue => {
  if (newValue === false) {
    console.log('[DownloadButton] watch selectPathVisible: 路径选择面板已隐藏')
    hideCloudPathPopup()
    hideDrivelistPopup()
  }
})

// 下拉菜单容器的引用
const selectPathTypeRef = ref<HTMLElement>()

// 添加 download-button 元素的引用
const downloadButtonRef = ref<HTMLElement>()

// 使用 VueUse 的 onClickOutside 来处理点击外部区域
onClickOutside(selectPathTypeRef, () => {
  if (selectPathVisible.value) {
    console.log('[DownloadButton] onClickOutside: 点击外部区域，隐藏路径选择面板')
    selectPathVisible.value = false
  }

  console.log('onClickOutside', selectPathVisible.value)
})

const toLogin = async () => {
  const resp = await PopUpNS.showLoginDlg()
  console.log('[DownloadButton] 登录结果:', resp)
}

// 使用 VueUse 的 onKeyStroke 来处理 ESC 键
onKeyStroke('Escape', () => {
  if (selectPathVisible.value) {
    console.log('[DownloadButton] onKeyStroke Escape: 按下ESC键，隐藏路径选择面板')
    selectPathVisible.value = false
  }
})

/**
 * 初始化下载路径
 * 确保路径信息已初始化，store会自动处理默认路径和当前路径的设置
 */
async function initializeDownloadPath(): Promise<void> {
  try {
    console.log('[DownloadButton] 开始初始化下载路径...')

    // 调用 store 的初始化方法，store 会自动处理路径设置逻辑
    await downloadPathStore.initializePathInfoList()

    console.log('[DownloadButton] 下载路径初始化完成')
    console.log('[DownloadButton] 当前下载路径:', downloadPathStore.currentDownloadPath)
    console.log('[DownloadButton] 默认下载路径:', downloadPathStore.defaultDownloadPath)
  } catch (error) {
    console.error('[DownloadButton] 初始化下载路径失败:', error)
  }
}

/**
 * 处理确认按钮点击事件（统一的确认事件）
 * @param type 按钮类型，用于区分是下载到本地还是添加到云盘
 */
function handleConfirm(type: DownloadPathType): void {
  // 如果组件被禁用，则不执行任何操作
  if (props.disabled) {
    return
  }

  let pathToSubmit = ''

  if (type === DownloadPathType.Local) {
    // 下载到本地，传递本地路径
    pathToSubmit = currentTaskSavePath.value
  } else if (type === DownloadPathType.Cloud) {
    // 添加到云盘，传递云盘路径ID
    const cloudPath = downloadCloudPathStore.getCurrentCloudPath()
    pathToSubmit = cloudPath?.id || ''
  }

  console.log(
    `[DownloadButton] 确认事件触发，场景: ${props.scene}, 类型: ${type}, 路径: ${pathToSubmit}`
  )

  // 发出事件给父组件，传递对象格式的参数
  emit('submit', {
    type,
    path: pathToSubmit,
    scene: props.scene,
  })
}

/**
 * 设置按钮类型
 * @param type 按钮类型
 */
function setButtonType(type: DownloadPathType): void {
  // 如果父组件指定了路径类型，则不允许通过此方法修改
  if (props.selectedPathType !== undefined) {
    console.log('[DownloadButton] 父组件已指定路径类型，忽略 setButtonType 调用')
    return
  }

  buttonType.value = type
}

async function initButtonType() {
  if (props.selectedPathType !== undefined) {
    buttonType.value = props.selectedPathType
    return
  }

  const isLogicChoosed = (await config.getValue(
    'TaskDefaultSettings',
    'LogicChoosed',
    false
  )) as boolean
  const isCloudChoosed = (await config.getValue(
    'TaskDefaultSettings',
    'CloudChoosed',
    false
  )) as boolean
  if (isLogicChoosed) {
    buttonType.value = DownloadPathType.Local
  } else if (isCloudChoosed) {
    buttonType.value = DownloadPathType.Cloud
  } else {
    buttonType.value = DownloadPathType.Local
  }
}

/**
 * 处理右侧箭头图标点击事件
 * Handle right arrow icon click event
 */
async function handleRightIconClick(): Promise<void> {
  // 如果组件被禁用，则不执行任何操作
  if (props.disabled) {
    return
  }

  try {
    console.log('点击下载路径选择箭头，获取路径信息...')

    console.log('隐藏选择面板6')

    // 切换选择路径面板的显示状态
    selectPathVisible.value = !selectPathVisible.value
  } catch (error) {
    console.error('获取下载路径信息失败:', error)
  }
}

/**
 * 处理选择路径面板点击事件
 */
function handleSelectPathClick(event: Event): void {
  // 阻止事件冒泡，避免触发外部点击隐藏
  event.stopPropagation()
}

/**
 * 处理云盘路径选择点击事件
 */
async function handleSelectCloudPath(): Promise<void> {
  hideDrivelistPopup()
  // 如果组件被禁用，则不执行任何操作
  if (props.disabled) {
    return
  }

  try {
    console.log('[DownloadButton] 选择云盘路径')

    // 检查用户是否登录
    const isLogged = await userStore.getLoginStatus()
    console.log('[DownloadButton] 用户登录状态:', isLogged)

    let cloudPathInfoList: any[] = []
    let selectedCloudPath = ''
    let quotas: any = null

    if (isLogged) {
      // 用户已登录，获取正常的云盘信息
      // 1. 初始化云盘路径信息
      await downloadCloudPathStore.initializeCloudPathInfo()

      // 2. 获取默认路径
      const defaultPath = await downloadCloudPathStore.getRecentSaveDefaultFolder()
      selectedCloudPath = defaultPath?.id || ''
      console.log('[DownloadButton] 获取云盘默认路径:', defaultPath, '提取的ID:', selectedCloudPath)

      // 3. 刷新云盘路径信息，确保数据是最新的
      await downloadCloudPathStore.refreshCloudPathInfo()
      console.log('[DownloadButton] 云盘路径信息已刷新，准备打开云盘路径选择弹窗')

      // 4. 获取云盘配额信息
      quotas = await driveStore.getQuotas()
      console.log('[DownloadButton] 获取云盘配额信息:', quotas)

      // 5. 获取云盘路径列表
      cloudPathInfoList = downloadCloudPathStore.cloudPathInfoList
    } else {
      // 用户未登录，传递空数据
      console.log('[DownloadButton] 用户未登录，传递空数据')
      cloudPathInfoList = []
      selectedCloudPath = ''
      quotas = null
    }

    console.log('[DownloadButton] 选择其他云盘文件夹')

    const currentWindow = PopUpNS.getCurrentWindow()
    // 9. 打开云盘路径选择器,并传递当前的parentid
    const resp = await ThunderPanClientSDK.getInstance().openPathSelectorDialog({
      parentId: currentWindow.id,
    })

    console.log('[DownloadButton] 打开云盘路径选择器结果:', resp)

    if (resp?.success === true && resp.data?.selectedItem) {
      const selectedItem = resp.data.selectedItem
      const pathArray = resp?.data?.path || []
      const fullFolderpath = pathArray.map(item => item.name) || []
      fullFolderpath.push(selectedItem.name)
      const fullFolderpathStr = fullFolderpath.join('\\')
      const cloudPath = {
        id: selectedItem.id || '',
        name: selectedItem?.name || '云盘',
        fullFolderpathStr: fullFolderpathStr,
      }

      // 设置为当前路径
      downloadCloudPathStore.setCurrentCloudPath(cloudPath)

      // 用户主动选择云盘路径时，总是切换到云盘模式
      buttonType.value = DownloadPathType.Cloud
      // 仅在父组件未指定路径类型时才更新全局store
      if (props.selectedPathType === undefined) {
        downloadPathTypeStore.setPathTypeToCloud()
      }

      // 添加到历史记录
      await downloadCloudPathStore.addToRecentHistory(cloudPath)

      console.log('[DownloadButton] 选择其他云盘文件夹:', cloudPath)
    }

    console.log('[DownloadButton] handleSelectCloudPath: 云盘路径选择完成，隐藏路径选择面板')
    selectPathVisible.value = false
  } catch (error) {
    console.log('隐藏选择面板2')

    console.error('[DownloadButton] 选择云盘路径失败:', error)
    // 即使出错也隐藏选择面板
    console.log('[DownloadButton] handleSelectCloudPath error: 选择云盘路径出错，隐藏路径选择面板')
    selectPathVisible.value = false
  }
}

/**
 * 检查 download-button 元素距离屏幕右侧的距离是否大于指定宽度
 * @param width 要检查的宽度，默认为 DRIVELIST_POPUP_WIDTH
 * @returns Promise<boolean> 如果距离大于指定宽度返回true，否则返回false
 */
async function checkHasEnoughRightSpace(width: number = DRIVELIST_POPUP_WIDTH): Promise<boolean> {
  try {
    if (!downloadButtonRef.value) {
      console.warn('[DownloadButton] downloadButtonRef 未找到')
      return false
    }

    // 使用导入的通用函数
    return await isDistanceToRightGreaterThan(downloadButtonRef.value, width)
  } catch (error) {
    console.error('[DownloadButton] 检查距离屏幕右侧距离失败:', error)
    return false
  }
}

/**
 * 处理本地路径选择点击事件
 */
async function handleSelectDownloadPath(): Promise<void> {
  hideCloudPathPopup()
  // 如果组件被禁用，则不执行任何操作
  if (props.disabled) {
    return
  }

  console.log('[DownloadButton] 选择本地路径')

  try {
    // 1. 获取当前选中的路径（而不是默认路径）
    // const currentPath = downloadPathStore.getCurrentDownloadPath()
    // console.log('[DownloadButton] 获取当前选中路径:', currentPath)

    const defaultPath = await DownloadPathNS.getDefaultPath()
    console.log('[DownloadButton] 选择的路径:', defaultPath)

    // 打开文件选择器
    // 选择其他文件夹
    const selectedPath = await DownloadPathNS.choosePath(defaultPath)
    console.log('[DownloadButton] 选择的路径:', selectedPath)
    if (selectedPath && selectedPath.trim()) {
      // 设置当前下载路径，但不自动刷新（我们会在后面手动刷新）
      await downloadPathStore.setCurrentDownloadPath(selectedPath, false)

      // 用户主动选择本地路径时，总是切换到本地模式
      buttonType.value = DownloadPathType.Local
      // 仅在父组件未指定路径类型时才更新全局store
      if (props.selectedPathType === undefined) {
        downloadPathTypeStore.setPathTypeToLocal()
      }

      // 添加到历史记录
      await DownloadPathNS.addPath(selectedPath)

      // 手动刷新路径信息列表，确保包含最新的路径和磁盘空间信息
      await downloadPathStore.forceRefreshPathInfoList()

      console.log('[DownloadButton] 选择其他文件夹:', selectedPath)
    }
  } catch (error) {
    console.log('隐藏选择面板4')

    console.error('[DownloadButton] 选择本地路径失败:', error)
    // 即使出错也隐藏选择面板
    console.log(
      '[DownloadButton] handleSelectDownloadPath error: 选择本地路径出错，隐藏路径选择面板'
    )
    selectPathVisible.value = false
  }
}

// 隐藏drivelist
async function hideDrivelistPopup(): Promise<void> {
  console.log('[DownloadButton] 隐藏drivelist')
  const drivelistWnd = await PopUpNS.getSingletonWindow('drivelist')
  console.log('[DownloadButton] drivelistWnd', drivelistWnd)
  if (drivelistWnd) {
    console.log('[DownloadButton] 关闭drivelist')
    drivelistWnd.close()
  }
}

// 隐藏cloudpath
async function hideCloudPathPopup(): Promise<void> {
  console.log('[DownloadButton] 隐藏cloudpath')
  const cloudpathWnd = await PopUpNS.getSingletonWindow('download-cloud-path')
  const cloudpathWnd2 = await PopUpNS.getSingletonWindow('DownloadCloudPath')
  console.log('[DownloadButton] cloudpathWnd', cloudpathWnd, '-', cloudpathWnd2)
  if (cloudpathWnd) {
    console.log('[DownloadButton] 关闭cloudpath')
    cloudpathWnd.close()
  }
}

/**
 * 获取 download-button 元素的窗口坐标点
 * @returns Promise<Point> 窗口坐标点
 */
async function getDownloadButtonWindowPoint(): Promise<{ x: number; y: number } | null> {
  try {
    if (!downloadButtonRef.value) {
      console.warn('[DownloadButton] downloadButtonRef 未找到')
      return null
    }

    // 获取元素的客户端边界矩形
    const rect = downloadButtonRef.value.getBoundingClientRect()

    // 使用元素的左上角坐标作为起始点
    const elePoint = {
      x: rect.left,
      y: rect.top,
    }

    console.log('[DownloadButton] 元素相对坐标:', elePoint)

    // 调用 PopUpNS.toWindowPoint 转换为窗口坐标
    const windowPoint = await PopUpNS.toWindowPoint(elePoint)

    console.log('[DownloadButton] 窗口坐标:', windowPoint)

    return windowPoint
  } catch (error) {
    console.error('[DownloadButton] 获取窗口坐标失败:', error)
    return null
  }
}

/**
 * 计算弹窗位置坐标
 * @param popupWidth 弹窗宽度
 * @returns Promise<{x?: number, y?: number} | null> 计算出的弹窗位置坐标
 */
async function calculatePopupPosition(
  popupWidth: number
): Promise<{ x?: number; y?: number } | null> {
  try {
    // 1. 获取按钮元素的坐标点
    const buttonPoint = await getDownloadButtonWindowPoint()
    console.log('[DownloadButton] 获取到按钮坐标:', buttonPoint)

    if (!buttonPoint) {
      console.warn('[DownloadButton] 无法获取按钮坐标')
      return null
    }

    // 2. 判断按钮距离右侧的宽度是否足够显示弹窗
    const hasEnoughRightSpace = await checkHasEnoughRightSpace(popupWidth)
    console.log(
      `[DownloadButton] 按钮距离右侧是否有足够空间(${popupWidth}px):`,
      hasEnoughRightSpace
    )

    // 3. 计算右侧坐标位置（弹窗放在按钮右侧）
    const rightPos = {
      x: buttonPoint.x + 208, // 按钮坐标加上按钮宽度
      y: buttonPoint.y - 90,
    }

    // 4. 计算左侧坐标位置（弹窗放在按钮左侧）
    const leftPos = {
      x: buttonPoint.x - popupWidth, // 按钮坐标减去弹窗宽度
      y: buttonPoint.y - 90,
    }

    // 5. 根据空间情况选择位置
    const pos = hasEnoughRightSpace ? rightPos : leftPos

    console.log('[DownloadButton] 最终选择的弹窗位置:', pos)
    return pos
  } catch (error) {
    console.error('[DownloadButton] 计算弹窗位置失败:', error)
    return null
  }
}

// 组件挂载时初始化下载路径和添加全局点击监听
onMounted(() => {
  initializeDownloadPath()
  // 同时初始化云盘路径信息
  downloadCloudPathStore.initializeCloudPathInfo()
  getDownloadButtonWindowPoint()

  // 设置 Button 的类型
  initButtonType()
})

// 组件卸载前移除全局点击监听
onBeforeUnmount(() => {})

// 暴露给父组件的方法和属性
defineExpose({
  setButtonType,
  buttonType,
})
</script>

<style scoped lang="scss">
@import './download-button.scss';
</style>
