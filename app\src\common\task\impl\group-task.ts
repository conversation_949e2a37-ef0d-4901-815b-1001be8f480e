import * as BaseType from '../base'

export class GroupTask {
  private nativeTask: any;

  constructor(nativeTask: any) {
    this.nativeTask = nativeTask;
  }

  public async getSubTaskIdByIndex(index: number): Promise<number> {
    return await new Promise((v) => {
      this.nativeTask.getSubTaskByIndex(index, (id: number) => {
        v(id);
      });
    })
  }

  public async getSubTaskIds(): Promise<number[]> {
    return await new Promise((v) => {
      this.nativeTask.getSubTaskIds((ids: number[]) => {
        v(ids);
      });
    })
  }

  // 彻底删除子任务的下载
  public async deleteSubTask(ids: number[]): Promise<void> {
    return await new Promise((v) => {
      this.nativeTask.deleteSubTask(() => {
        v();
      }, ids);
    });
  }

  // 取消子任务下载
  public async cancelSubTask(ids: number[]): Promise<void> {
    return await new Promise((v) => {
      this.nativeTask.cancelSubTask(() => {
        v();
      }, ids);
    });
  }

  // 恢复取消下载的
  public async downloadSubTask(ids: number[]): Promise<void> {
    return await new Promise((v) => {
      this.nativeTask.downloadSubTask(() => {
        v();
      }, ids);
    });
  }

  // 更新需要下载的任务（任务和上次相比可能增加了部分、可能删除了部分、也可能同时有增有减），相当于包含了
  public async updateSubSelectTask(ids: number[]): Promise<void> {
    return await new Promise((v) => {
      this.nativeTask.updateSubSelectTask(() => {
        v();
      }, ids);
    });
  }

  // 彻底删除任务组里面的bt的子任务的下载
  public async deleteSubBtTask(info: {taskId: number, indexs: number[]}[]): Promise<void> {
    return await new Promise((v) => {
      this.nativeTask.deleteSubBtTask(() => {
        v();
      }, info);
    });
  }

  // 取消子任务组里面的bt的任务下载
  public async cancelSubBtTask(info: {taskId: number, indexs: number[]}[]): Promise<void> {
    return await new Promise((v) => {
      this.nativeTask.cancelSubBtTask(() => {
        v();
      }, info);
    });
  }

  // 恢复任务组里面的bt的取消下载的
  public async downloadSubBtTask(info: {taskId: number, indexs: number[]}[]): Promise<void> {
    return await new Promise((v) => {
      this.nativeTask.downloadSubBtTask(() => {
        v();
      }, info);
    });
  }

  // 更新任务组里面的bt的下载文件
  public async updateSubBtSelectTask(info: {taskId: number, indexs: number[]}[]): Promise<void> {
    return await new Promise((v) => {
      this.nativeTask.updateSubBtSelectTask(() => {
        v();
      }, info);
    });
  }

  // 获取总共下载的个数（如果包含bt，bt按照子文件计算）
  public async getDownloadCount(): Promise<number> {
    return await new Promise((v) => {
      this.nativeTask.getDownloadCount((n: number) => {
        v(n);
      });
    });
  }

  // 获取已完成下载的个数（如果包含bt，bt按照子文件计算）
  public async getCompleteCount(): Promise<number> {
    return await new Promise((v) => {
      this.nativeTask.getCompleteCount((n: number) => {
        v(n);
      });
    });
  }

  // 获取总的子任务的个数（如果包含bt，bt按照子文件计算）
  public async getTotalCount(): Promise<number> {
    return await new Promise((v) => {
      this.nativeTask.getTotalCount((n: number) => {
        v(n);
      });
    });
  }
}