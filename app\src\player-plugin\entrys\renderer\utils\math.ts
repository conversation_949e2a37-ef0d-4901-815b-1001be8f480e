// 解决小数计算精度问题

// 加法
export function accAdd(arg1, arg2): number {
  let r1, r2, m, n;
  try {
    r1 = arg1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  n = r1 >= r2 ? r1 : r2;
  return +((arg1 * m + arg2 * m) / m).toFixed(n);
}

// 减法
export function accSub(arg1, arg2): number {
  let r1, r2, m, n;
  try {
    r1 = arg1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  n = r1 >= r2 ? r1 : r2;
  return +((arg1 * m - arg2 * m) / m).toFixed(n);
}

// 乘法
export function accMul(arg1, arg2): number {
  let r1 = arg1.toString();
  let r2 = arg2.toString();
  let m = 0;
  try {
    m += arg1.toString().split('.')[1].length;
  } catch (e) {}
  try {
    m += arg2.toString().split('.')[1].length;
  } catch (e) {}
  return (
    (Number(r1.replace('.', '')) * Number(r2.replace('.', ''))) /
    Math.pow(10, m)
  );
}

//除法
export function accDiv(arg1, arg2): number {
  let r1,
    r2,
    t1 = 0,
    t2 = 0;
  arg1 = arg1.toString();
  arg2 = arg2.toString();
  try {
    t1 = arg1.split('.').length;
  } catch (e) {}
  try {
    t2 = arg2.split('.').length;
  } catch (e) {}
  r1 = Number(arg1.replace('.', ''));
  r2 = Number(arg2.replace('.', ''));
  let num = Math.pow(10, t2 - t1);
  let num1 = r1 / r2;
  return accMul(num1, num); //调用乘法
}
