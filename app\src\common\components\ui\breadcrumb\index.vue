<script setup lang="ts">
import { useResizeObserver, useThrottleFn } from '@vueuse/core';
import { nextTick, reactive, useTemplateRef, watch } from 'vue';

export interface IBreadcrumbItemData {
  id?: string
  title: string
}

const props = defineProps<{
  list: Readonly<IBreadcrumbItemData[]>
}>()

const emit = defineEmits<{
  (e: 'itemClick', item: IBreadcrumbItemData, index: number): void
}>()

const breadcrumbRef = useTemplateRef<HTMLDivElement>('breadcrumbRef')
let moreItems: IBreadcrumbItemData[] = reactive([])
let visibleItems: IBreadcrumbItemData[] = reactive(props.list.slice(1))

function calcVisibleItems () {
  const { offsetWidth, scrollWidth } = breadcrumbRef.value!

  if (scrollWidth <= offsetWidth && moreItems.length) {
    visibleItems.unshift(moreItems.pop()!)
    nextTick(() => {
      calcVisibleItems()
    })
  } else {
    calcMoreItems()
  }
}

function calcMoreItems () {
  const { offsetWidth, scrollWidth } = breadcrumbRef.value!
  if (scrollWidth > offsetWidth && visibleItems.length) {
    moreItems.push(visibleItems.shift()!)
    nextTick(() => {
      calcMoreItems()
    })
  }
}

function init () {
  const { offsetWidth, scrollWidth } = breadcrumbRef.value!
  if (!offsetWidth || !scrollWidth) return;

  moreItems.splice(0, moreItems.length)
  visibleItems.splice(0, visibleItems.length, ...props.list.slice(1))

  nextTick(() => {
    calcVisibleItems()
  })
}

function handleItemClick (itemData: IBreadcrumbItemData, index: number) {
  emit('itemClick', itemData, index)
}

useResizeObserver(breadcrumbRef, () => {
  debouncedInitFn()
})

const debouncedInitFn = useThrottleFn(() => {
  init();
}, 200, true, true)

watch(() => props.list.length, () => {
  debouncedInitFn()
})
</script>

<template>
  <div class="xmp-breadcrumb" ref="breadcrumbRef">
    <div class="xmp-breadcrumb-item">
      <div class="xmp-breadcrumb__text" @click="handleItemClick(list[0], 0)">{{ list[0].title }}</div>
      <div class="xmp-breadcrumb__separator">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
          <path
            d="M4.64645 2.14645C4.84171 1.95118 5.15822 1.95118 5.35348 2.14645L8.85348 5.64645C9.04874 5.84171 9.04874 6.15822 8.85348 6.35348L5.35348 9.85348C5.15822 10.0487 4.84171 10.0487 4.64645 9.85348C4.45118 9.65822 4.45118 9.34171 4.64645 9.14645L7.79293 5.99996L4.64645 2.85348C4.45118 2.65822 4.45118 2.34171 4.64645 2.14645Z"
            fill="currentColor" />
        </svg>
      </div>
    </div>

    <div v-if="moreItems.length" class="xmp-breadcrumb-item">
      <div class="xmp-breadcrumb__more">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path
            d="M5 8C5 8.55229 4.55228 9 4 9C3.44772 9 3 8.55229 3 8C3 7.44772 3.44772 7 4 7C4.55228 7 5 7.44772 5 8ZM9 8C9 8.55229 8.55229 9 8 9C7.44772 9 7 8.55229 7 8C7 7.44772 7.44772 7 8 7C8.55229 7 9 7.44772 9 8ZM12 9C12.5523 9 13 8.55229 13 8C13 7.44772 12.5523 7 12 7C11.4477 7 11 7.44772 11 8C11 8.55229 11.4477 9 12 9Z"
            fill="currentColor" />
        </svg>

        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
          <path
            d="M9.38119 4L9.45932 4.00586C9.84013 4.06326 10.0238 4.53327 9.75229 4.83496L6.59799 8.33887C6.28019 8.69197 5.72633 8.69197 5.40854 8.33887L2.25424 4.83496C1.96465 4.5132 2.19342 4 2.62631 4H9.38119Z"
            fill="currentColor" />
        </svg>

        <div class="xmp-breadcrumb__menus">
          <div v-for="(menuItem, index) in moreItems" :key="menuItem.id || index" :title="menuItem.title"
            @click="handleItemClick(menuItem, 1 + index)" class="xmp-breadcrumb__menu-item">
            {{ menuItem.title }}
          </div>
        </div>
      </div>
      <div class="xmp-breadcrumb__separator">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
          <path
            d="M4.64645 2.14645C4.84171 1.95118 5.15822 1.95118 5.35348 2.14645L8.85348 5.64645C9.04874 5.84171 9.04874 6.15822 8.85348 6.35348L5.35348 9.85348C5.15822 10.0487 4.84171 10.0487 4.64645 9.85348C4.45118 9.65822 4.45118 9.34171 4.64645 9.14645L7.79293 5.99996L4.64645 2.85348C4.45118 2.65822 4.45118 2.34171 4.64645 2.14645Z"
            fill="currentColor" />
        </svg>
      </div>
    </div>

    <div v-for="(item, index) in visibleItems" :key="item.id" class="xmp-breadcrumb-item" :class="{
      'is-limit-width': true
    }">
      <div class="xmp-breadcrumb__text" :title="item.title"
        @click="handleItemClick(item, 1 + moreItems.length + index)">
        {{ item.title }}
      </div>
      <div class="xmp-breadcrumb__separator">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
          <path
            d="M4.64645 2.14645C4.84171 1.95118 5.15822 1.95118 5.35348 2.14645L8.85348 5.64645C9.04874 5.84171 9.04874 6.15822 8.85348 6.35348L5.35348 9.85348C5.15822 10.0487 4.84171 10.0487 4.64645 9.85348C4.45118 9.65822 4.45118 9.34171 4.64645 9.14645L7.79293 5.99996L4.64645 2.85348C4.45118 2.65822 4.45118 2.34171 4.64645 2.14645Z"
            fill="currentColor" />
        </svg>
      </div>
    </div>
  </div>
</template>

<style scoped>
.xmp-breadcrumb {
  display: flex;
  align-items: center;
  height: 32px;
  flex-grow: 1;

  .xmp-breadcrumb-item {
    display: flex;
    flex-shrink: 0;
    align-items: center;

    .xmp-breadcrumb__text {
      color: var(--font-font-3);
      font-size: 13px;
      line-height: 22px;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      white-space: pre-wrap;

      &:hover {
        color: var(--font-font-1);
        cursor: pointer;
      }
    }

    .xmp-breadcrumb__more {
      height: 24px;
      border-radius: 6px;
      position: relative;
      color: var(--font-font-3);
      display: flex;
      align-items: center;

      &:hover {
        background-color: var(--font-white-5);
        cursor: pointer;

        .xmp-breadcrumb__menus {
          display: flex;
        }
      }

      .xmp-breadcrumb__menus {
        display: none;
        position: absolute;
        top: 24px;
        left: 0;
        z-index: 1000;
        width: 140px;
        flex-direction: column;
        padding: 6px;
        border-radius: var(--border-radius-S);
        background-color: var(--background-background-elevated);
        border: 1px solid var(--border-border-2);

        .xmp-breadcrumb__menu-item {
          padding: 11px 12px;
          color: var(--font-font-1);
          font-size: 13px;
          line-height: 22px;
          border-radius: var(--border-radius-S);
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;

          &:hover {
            background-color: var(--fill-fill-3);
            cursor: pointer;
          }
        }
      }
    }

    .xmp-breadcrumb__separator {
      width: 14px;
      height: 22px;
      color: var(--font-font-3);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 4px;
    }

    &:last-child {
      flex-grow: 1;

      .xmp-breadcrumb__text {
        color: var(--font-font-1);
        cursor: default;
      }

      .xmp-breadcrumb__separator {
        display: none;
      }
    }

    &.is-limit-width {
      max-width: 330px;
    }
  }
}
</style>
