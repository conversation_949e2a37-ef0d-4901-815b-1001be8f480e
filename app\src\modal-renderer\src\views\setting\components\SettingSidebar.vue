<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject, type Ref, nextTick, watch } from 'vue'
import type { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const menuList = [
  { id: 'basic-settings', label: '基本设置' },
  { id: 'cloud-settings', label: '云盘设置' },
  { id: 'connect-settings', label: '接管设置' },
  { id: 'download-settings', label: '下载设置' },
  { id: 'task-settings', label: '任务管理' },
  { id: 'remind-settings', label: '提醒' },
  { id: 'advanced-settings', label: '高级设置' },
]

const activeId = ref(menuList[0].id)

// 注入滚动容器
const scrollContainer = inject<Ref<InstanceType<typeof PerfectScrollbar> | undefined>>('scrollContainer')

// 添加标志位防止死循环
const isScrolling = ref(false)

function scrollTo(id: string) {
  const el = document.getElementById(id)
  if (el && scrollContainer?.value?.ps) {
    // 设置标志位，防止触发 onScroll
    isScrolling.value = true

    // 直接使用 PerfectScrollbar 的 ps 实例滚动
    const elementTop = el.offsetTop
    scrollContainer.value.ps.element.scrollTop = elementTop

    // 延迟重置标志位，等待滚动动画完成
    setTimeout(() => {
      isScrolling.value = false
    }, 500)
  }
  activeId.value = id
}

// 监听滚动事件
function onScroll() {
  // 如果是手动滚动触发的，则跳过
  if (isScrolling.value) return

  console.log('SettingSidebar onScroll')
  if (!scrollContainer?.value?.ps?.element) return

  const containerEl = scrollContainer.value.ps.element
  const scrollTop = containerEl.scrollTop

  let curId = menuList[0].id
  for (const item of menuList) {
    const el = document.getElementById(item.id)
    if (el) {
      const elementTop = el.offsetTop
      if (scrollTop >= elementTop - 50) { // 添加一些偏移量
        curId = item.id
      }
    }
  }
  activeId.value = curId
}

// 设置滚动监听
function setupScrollListener() {
  if (scrollContainer?.value?.ps?.element) {
    const containerEl = scrollContainer.value.ps.element
    containerEl.addEventListener('scroll', onScroll)
    console.log('SettingSidebar 设置滚动监听:', containerEl)
    return true
  }
  return false
}

// 移除滚动监听
function removeScrollListener() {
  if (scrollContainer?.value?.ps?.element) {
    const containerEl = scrollContainer.value.ps.element
    containerEl.removeEventListener('scroll', onScroll)
  }
}

onMounted(async () => {
  // 等待 PerfectScrollbar 初始化
  await nextTick()

  // 尝试设置滚动监听
  if (!setupScrollListener()) {
    // 如果还没有初始化完成，监听 scrollContainer 的变化
    if (scrollContainer) {
      watch(scrollContainer, (newVal) => {
        if (newVal?.ps?.element) {
          setupScrollListener()
        }
      }, { immediate: true })
    }
  }
})

onUnmounted(() => {
  removeScrollListener()
})
</script>

<template>
  <ul class="setting-sidebar-menu">
    <li v-for="item in menuList" :key="item.id" :class="{ 'setting-sidebar-menu-item-active': activeId === item.id }"
      @click="scrollTo(item.id)" class="setting-sidebar-menu-item">
      {{ item.label }}
    </li>
  </ul>
</template>

<style scoped lang="scss">
.setting-sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 32px 16px 0;
  background-color: var(--background-background-left-menu, #F6F7F9);
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  user-select: none;
}

.setting-sidebar-menu-item {
  padding: 12px 8px 12px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  width: 96px;
  height: 40px;
  border-radius: var(--border-radius-S, 6px);
  align-items: center;
  color: var(--font-font-3, #86909C);
  font-size: 13px;
  line-height: 22px;

  &-active {
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
    color: var(--font-font-1, #272E3B);
  }
}
</style>