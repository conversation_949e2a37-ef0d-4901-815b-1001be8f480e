<script setup lang="ts">
import { onMounted, ref, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import XMPMessage from '@root/common/components/ui/message/index'
import DownloadTrash from './components/DownloadTrash.vue'
import CloudTrash from './components/CloudTrash.vue'
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { AccountHelper } from '@root/common/account/client/accountHelper'
import { AccountHelperEventKey } from '@root/common/account/account-type'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import { useAlertDialog } from '@root/common/components/ui/Dialog/useAlertDialog'

defineOptions({
  name: 'trash'
})

const route = useRoute()
const alertDialog = useAlertDialog()
const downloadTrashRef = ref()
const cloudTrashRef = ref()

// 从URL查询参数获取默认tab，支持 'cloud' 和 'download'，默认为 'download'
const getDefaultTab = () => {
  const tabParam = route.query.tab as string
  if (tabParam === 'cloud' || tabParam === 'download') {
    return tabParam
  }
  return 'download'
}

const currentTab = ref(getDefaultTab())
const maxSaveDays = ref(0)
const showTips = ref(false)
const cloudTrashLength = ref(0)
const downloadTrashLength = ref(0)

const handleExtendTime = () => {
  XMPMessage({
    message: '延长时间功能待实现',
    type: 'warning'
  })
}

const getUserTrashPrivilege = async () => {
  try {
    const res = await ThunderPanClientSDK.getInstance().getCurrentUserTrashPrivilegeDuration()

    if (res.success) {
      console.log('getCurrentUserTrashPrivilegeDuration 响应', res.data)
      maxSaveDays.value = res.data || 0
      showTips.value = true
    } else {
      console.log('getCurrentUserTrashPrivilegeDuration 失败', res.error)
    }


  } catch (error) {
    console.log('getCurrentUserTrashPrivilegeDuration 失败', error)
  }
}

onMounted(() => {
  getUserTrashPrivilege()
  AccountHelper.getInstance().attachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, getUserTrashPrivilege)

})

onUnmounted(() => {
  AccountHelper.getInstance().detachEvent(AccountHelperEventKey.SIGN_IN_SUCCESS, getUserTrashPrivilege)
})

const handleClearAll = async () => {
  const ref = currentTab.value === 'download' ? downloadTrashRef.value : cloudTrashRef.value
  const result = await alertDialog.confirm({
    title: currentTab.value === 'download' ? '确定要清空回收站所有下载任务吗?' : '确定清空回收站云盘文件?',
    variant: 'error',
    content: '文件删除后将无法恢复',
    showTitleIcon: false,
    confirmText: '确认删除'
  })
  if (result !== false) {
    ref?.handleClearAll()
  }
}
</script>

<template>
  <div class="trash">
    <div class="trash-title">
      <span>回收站</span>
      <DropdownMenu :items="[{
        label: '清除所有回收站文件',
        key: 'clear-all',
        icon: 'xl-icon-general-clearout-l'
      }]" :align="'end'" @select="handleClearAll">
        <Button :is-icon="true" variant="ghost">
          <i class="xl-icon-general-more-l"></i>
        </Button>
      </DropdownMenu>
    </div>
    <div class="trash-tabs">
      <Button size="sm" :variant="currentTab === 'download' ? 'default' : 'secondary'" @click="currentTab = 'download'">
        {{ `下载 · ${downloadTrashLength}` }}
      </Button>
      <Button size="sm" :variant="currentTab === 'cloud' ? 'default' : 'secondary'" @click="currentTab = 'cloud'">
        {{ `云盘文件 · ${cloudTrashLength}` }}
      </Button>
    </div>
    <div class="trash-tips" v-if="currentTab === 'cloud' && showTips && cloudTrashLength > 0">
      <div class="trash-tips-content">
        <span>
          {{ `已删除的云盘文件最多可以保存${maxSaveDays}天` }}
          <a @click="handleExtendTime">延长时间</a>
        </span>
        <i class="xl-icon-close" @click="showTips = false" />
      </div>
    </div>
    <div class="trash-content">
      <div v-show="currentTab === 'download'" class="trash-content-body">
        <DownloadTrash ref="downloadTrashRef"
          @download-trash-update="(length: number) => downloadTrashLength = length" />
      </div>
      <div v-show="currentTab === 'cloud'" class="trash-content-body">
        <CloudTrash ref="cloudTrashRef" @cloud-trash-update="(length: number) => cloudTrashLength = length" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.trash {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--background-background-container, #fff);

  &-title {
    padding: 15px 40px;
    font-size: 26px;
    font-weight: 700;
    color: var(--font-font-1, #272E3B);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-tabs {
    padding: 8px 40px;
    display: flex;
    gap: 12px;
  }

  &-tips {
    padding: 0 40px 8px 40px;
    margin-top: 8px;

    &-content {
      display: flex;
      align-items: center;
      height: 40px;
      border-radius: var(--border-radius-M, 8px);
      background: var(--button-button-lead-default, rgba(34, 109, 245, 0.10));
      color: var(--font-font-1, #272E3B);
      font-size: 13px;
      padding: 0 12px;
      justify-content: space-between;

      a {
        color: var(--primary-primary-default, #226DF5);
        margin-left: 8px;
        cursor: pointer;
      }

      i {
        font-size: 12px;
        color: var(--font-font-3, #86909C);
        cursor: pointer;
      }
    }
  }


  &-content {
    flex: 1;
    position: relative;
    overflow: hidden;

    &-body {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
}
</style>