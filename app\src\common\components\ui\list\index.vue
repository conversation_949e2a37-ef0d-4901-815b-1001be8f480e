<template>
  <transition-group
    ref="tdList"
    :name="moving ? 'drag-list' : ''"
    tag="ul"
    class="td-draglist"
    tabindex="0"
    @click.native="handleCleanChosen"
    @keydown.native.prevent.ctrl.65="handleChooseAll">
    <li
      v-for="(item, index) in list"
      :ref="'item' + index"
      :data-index="index"
      class="td-draglist-item"
      :key="item.key"
      v-show="!status[item.key].hide || !moving"
      :data-key="item.key"
      :class="[{
        'is-chosen': status[item.key]?.chosen && !status[item.key]?.active,
        'is-active': status[item.key]?.active,
      }, sget(item.template, 'class')]"
      @click.stop="handleItemClick($event, item)"
      @mousedown.right="handleItemClickRight(item)"
    >
      <slot
        :item="item"
        :index="index"
        :chosen="status[item.key]?.chosen"
        :active="status[item.key]?.active"
      />
    </li>
  </transition-group>
</template>

<script lang="ts" setup>
import { computed, ref, watch, getCurrentInstance, onMounted } from 'vue'
import sget from '@xunlei/sget'


// 定义 props
const props = withDefaults(defineProps<{
  list: any[];
  chosenKeys: number[];
  multiple?: boolean;
  dragSelectable?: boolean;
  immediate?: boolean;
  drop?: boolean;
  loadMore?: () => void;
  loadDistance?: number;
  clickToFocus?: boolean;
  multiDragTextGenerator?: (maxIndexs: number[]) => string;
}>(), {
  multiple: false,
  dragSelectable: false,
  immediate: false,
  drop: false,
  loadDistance: 100,
  clickToFocus: true,
  multiDragTextGenerator: (maxIndexs: number[]) => maxIndexs.length.toString()
});

// 定义 emits
const emits = defineEmits<{
  (event: 'update:list', list: any[]): void;
  (event: 'update:chosenKeys', keys: number[]): void;
}>();


// 定义响应式数据
const tdList = ref<HTMLUListElement | null>(null)

const lastItem = ref<any>(null);
const chosenKeysCopy = ref<number[]>([]);
const extChosenKeys = ref<number[]>([]);
const listCopy = ref<any[]>([]);
const chosenIndexs = ref<any[]>([]);
const moveTargetKey = ref<string | null>(null);
const moving = ref(false);

const status = computed(() => {
  let status = {}

  listCopy.value.forEach((item, index) => {
    status[item.key] = {}
    if (chosenIndexs.value.indexOf(index) > -1) {
      status[item.key].moving = true
      if (moveTargetKey.value !== item.key) {
        status[item.key].hide = true
      }
    }
    status[item.key].chosen = isChosen(item)
    status[item.key].active = isActive(item)
  })
  console.log('>>>>>>>>>>>>>>>> status', status)
  return status
})

const isChosen = (item) => {
  if (props.multiple) {
    return chosenKeysCopy.value.includes(item.key)
  } else {
    return chosenKeysCopy.value === item.key
  }
}

const isActive = (item) => {
  if (props.multiple) {
    // return this.chosenKeysCopy.includes(item.key) && !(this.sortByHandler && this.dragging) && this.chosenKeysCopy.length === 1
    return chosenKeysCopy.value.includes(item.key) && chosenKeysCopy.value.length === 1
  } else {
    return chosenKeysCopy.value === item.key
  }
}

const handleCleanChosen = () => {
  updateChosenKeys([])
}

const updateChosenKeys = (keys: any[],  sorted?: boolean, needExt?: boolean) => {
  if (!sorted && keys.length >= 2) {
    const keyToIndex = keys.map(k => listCopy.value.findIndex(item => item.key === k))
    keyToIndex.sort((a, b) => a - b)
    keys = keyToIndex.map(index => listCopy.value[index].key)
  }
  if (needExt) {
    keys = keys.concat(extChosenKeys.value)
  }
  emits('update:chosenKeys', keys)
}

const handleChooseAll = () => {
  console.log('>>>>>>>>>>>>>>>>> 群选', listCopy.value.map(item => item.key))
  updateChosenKeys(listCopy.value.map(item => item.key), true)
}

const handleItemClick = (e, item) => {
  if (props.clickToFocus) {
    console.log('> handleItemClick', tdList.value, item)
    // tdList.value && tdList.value.focus();
  }
  console.log('>>>>>>>>>>>>>>>> e.ctrlKey', e.ctrlKey, e.shiftKey)
  if (!props.multiple) {
    updateChosenKeys([item.key])
    return
  }

  let chosenKeys = [] as any[]

  if (e.ctrlKey) {
    lastItem.value = item
    chosenKeys = [...chosenKeysCopy.value]

    if (chosenKeys.includes(item.key)) {
      chosenKeys.splice(chosenKeys.indexOf(item.key), 1)
    } else {
      chosenKeys.push(item.key)
    }
  } else if (e.shiftKey) {
    let lastIndex = listCopy.value.findIndex(item => item.key === lastItem.value.key)
    let index = listCopy.value.indexOf(item)

    if (index >= lastIndex) {
      for (let i = lastIndex; i <= index; i++) {
        chosenKeys.push(listCopy.value[i].key)
      }
    } else {
      for (let i = index; i <= lastIndex; i++) {
        chosenKeys.push(listCopy.value[i].key)
      }
    }
  } else {
    lastItem.value = item
    chosenKeys = [item.key]
  }

  chosenKeysCopy.value = chosenKeys

  updateChosenKeys(chosenKeys, false, !!e.ctrlKey)
}

const handleItemClickRight = (item) => {
  lastItem.value = item

  if (!props.chosenKeys.includes(item.key)) {
    updateChosenKeys([item.key])
  }
}

const doChosenKeysSort = () => {
  if (chosenKeysCopy.value.length <= 1) {
    return
  }
  let swaped = false
  for (let i = 1; i < chosenIndexs.value.length; i++) {
    if (chosenIndexs.value[i] - chosenIndexs.value[i - 1] > 1) {
      swaped = true
      break
    }
  }
  if (swaped) {
    const newList = []

    for (let i = 0; i < chosenIndexs.value.length; i++) {
      newList.push(listCopy.value[chosenIndexs.value[i]])
    }
    updateList(newList, true)
  }
}

const updateList = (list, fromKeysSort) => {
  const _list = [...list]
  const map = {}
  let i = 0
  let duplicate
  while (i < _list.length) {
    if (map[_list[i].key]) {
      duplicate = true
      _list.splice(i, 1)
    } else {
      map[_list[i].key] = true
      i++
    }
  }
  if (duplicate) {
    console.warn('oldList:', props.list.map(item => item.key), 'newList:', list.map(item => item.key), 'updateList:', JSON.parse(JSON.stringify(_list)), 'updateList.key:', _list.map(item => item.key))
  }
  emits('update:list', _list)
}

const updateChosenIndexs = () => {
  chosenIndexs.value = chosenKeysCopy.value.map(k => listCopy.value.findIndex(item => item.key === k))
}

// 监听 props 变化
watch(() => props.list, (v) => {
  listCopy.value = [...v];
  lastItem.value = v.find(item => item.key === props.chosenKeys[0]) || v[0];
  chosenKeysCopy.value = props.chosenKeys.filter(key => v.findIndex(item => item.key === key) !== -1);
  extChosenKeys.value = props.chosenKeys.filter(key => v.findIndex(item => item.key === key) === -1);
  if (moving.value) {
    doChosenKeysSort();
    moving.value = false;
  }
}, { immediate: true })

watch(() => props.chosenKeys, (v) => {
  chosenKeysCopy.value = v.filter(key => listCopy.value.findIndex(item => item.key === key) !== -1);
  extChosenKeys.value = v.filter(key => listCopy.value.findIndex(item => item.key === key) === -1);
  if (v.length) {
    lastItem.value = listCopy.value.find(item => item.key === v[0]) || listCopy.value[0];
  } else {
    lastItem.value = listCopy.value[0];
  }
}, { immediate: true });

watch([chosenKeysCopy, listCopy], () => {
  updateChosenIndexs();
}, { immediate: true });

// watch(() => props.list.length, () => {
//   if (moving.value && dragSortMixin.moveKeys) {
//     for (let k of dragSortMixin.moveKeys) {
//       if (props.list.findIndex(item => item.key === k) === -1) {
//         return dragSortMixin.onSortEnd();
//       }
//     }
//   }
//   const tdList = (defineExpose({ $el: getCurrentInstance()?.proxy?.$el }) as any).$el;
//   scrollHeight.value = tdList.scrollHeight;
//   updateRect();
// });

onMounted(() => {
  console.log('>>>>>>>>>>>>>>>>> list', props.list)
})
</script>


<style lang="scss" scoped>
.td-draglist {
  overflow-x: hidden;
  overflow-y: auto;
  outline: none;
}
.td-draglist-item {
  position: relative;
  &.is-chosen {
    background-color: #f5f5f5;
  }
  &.is-active {
    background-color: #e6f7ff;
  }
  &.is-transition {
    transition: all 0.3s ease;
  }
  &.is-hidden {
    display: none;
  }
}
</style>