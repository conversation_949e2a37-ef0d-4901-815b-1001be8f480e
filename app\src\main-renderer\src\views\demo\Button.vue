<script setup lang="ts">
</script>
<template>
  <h1>Button</h1>
  <i class="xl-icon-triangle-down"></i>
  <p>size</p>
  <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
    <Button size="sm">
      小按钮 small
    </Button>
    <Button>
      默认按钮 default
    </Button>
    <Button size="lg">
      大按钮 large
    </Button>
  </div>

  <p>variant</p>
  <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
    <Button>
      默认颜色 default
    </Button>
    <Button variant="primary">
      主色 primary
    </Button>
    <Button variant="secondary">
      次要色 secondary
    </Button>
    <Button variant="warning">
      警告色 warning
    </Button>
    <Button variant="weak-lead">
      弱引导色 weak-lead
    </Button>
    <Button variant="outline">
      描边色 outline
    </Button>
    <Button variant="ghost">
      幽灵色 ghost
    </Button>
  </div>

  <p>icon</p>
  <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
    <Button variant="outline" :is-icon="true">
      <i class="xl-icon-add"></i>
    </Button>
    <Button variant="ghost" :is-icon="true">
      <i class="xl-icon-add"></i>
    </Button>

    <Button variant="outline">
      <i class="xl-icon-add"></i>
      icon + 文本
    </Button>

    <Button variant="warning">
      <i class="xl-icon-add"></i>
      icon + 文本
    </Button>

    <Button variant="outline" :has-right-icon="true">
      <i class="xl-icon-add"></i>
      下拉菜单
    </Button>


    <Button variant="warning" :has-right-icon="true">
      <i class="xl-icon-add"></i>
      下拉菜单
    </Button>
  </div>

</template>

<style lang="scss" scoped></style>
