import path from 'path';
import * as BaseType from '@root/common/task/base';
import { TaskManager } from '@root/common/task/impl/task-manager';
import { ConsumeManagerNs } from '@root/common/consume/impl/consume';


export namespace DownloadAssistNs {
    export async function init() {
        let runOpenOnFinish = async (taskId: number) => {
            let task = await TaskManager.GetInstance().findTaskById(taskId);
            if (task && task.isOpenOnComplete()) {
                ConsumeManagerNs.consumeTask(taskId, -1);
            }
        };
        TaskManager.GetInstance().attachTaskStatusChangeEvent((taskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
            if (eNew === BaseType.TaskStatus.Succeeded) {
                runOpenOnFinish(taskId);
            }
        });
        TaskManager.GetInstance().attachGroupSubTaskStatusChangeEvent((taskId: number, groupTaskId: number, eOld: BaseType.TaskStatus, eNew: BaseType.TaskStatus) => {
            if (eNew === BaseType.TaskStatus.Succeeded) {
                runOpenOnFinish(taskId);
            }
        });
    }
}