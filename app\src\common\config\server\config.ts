/**
 * 配置管理模块
 */

import fs from 'fs';
import path from 'path';
import { Client } from '@xunlei/node-net-ipc/dist/Client';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { GetProfilesPath } from '@root/common/xxx-node-path';
import { server } from '@xunlei/node-net-ipc/dist/ipc-server';
import abTestServer from '@root/common/config/server/ab-test';
import IconManager from '@root/common/config/server/icon-manager';
import configModules from '@root/common/config/server/config-modules';
import configRemoteGlobal from '@root/common/config/server/config-remote-global';
import { quitPromisesServer } from '@root/common/quit-promises/server/quit-promises';
import { ConfigValueType, IConfigSectionMap, IConfigKeyMap, ConfigInitState } from '@root/common/config/types';

export class ConfigServer {
  protected initState_: ConfigInitState = ConfigInitState.None;
  protected configPath_: string = '';
  private configBakPath_: string = '';
  protected isChanged_: boolean = false;
  protected configData_: IConfigSectionMap = {};
  private saveTimerId: number | undefined = undefined;

  protected waitInitCallback_: (() => void)[] = [];

  constructor() {

  }

  get isInit() {
    return this.initState_ === ConfigInitState.Done;
  }

  public async init(): Promise<void> {
    do {
      if (this.initState_ === ConfigInitState.Done) {
        break;
      }

      if (this.initState_ === ConfigInitState.Ing) {
        return new Promise<void>(
          (resolve: () => void): void => {
            this.waitInitCallback_.push(() => {
              resolve();
            });
          }
        );
      }

      this.initState_ = ConfigInitState.Ing;
      // 注册IPC接口，本来希望弱化server对象，所有IPC均注册到client上
      // 但是有可能后续插件兼容问题，先保留接口注册到server的处理
      server.registerFunctions({
        GetConfigValue: async (client: Client, context: unknown, section: string, key: string, defValue: ConfigValueType): Promise<ConfigValueType> => {
          return this.getValue(section, key, defValue);
        },
        SetConfigValue: async (client: Client, context: unknown, section: string, key: string, value: ConfigValueType, isClick: boolean): Promise<void> => {
          await this.setValue(section, key, value, isClick);
        },
        HasConfigValue: async (client: Client, context: unknown, section: string, key: string): Promise<boolean> => {
          return this.has(section, key);
        },
        RemoveConfigValue: async (client: Client, context: unknown, section: string, key: string): Promise<void> => {
          await this.removeValue(section, key);
        },
        GetConfigModules: async (client: unknown, context: unknown, ...args: any[]): Promise<any> => {
          return configModules.getConfigInfo(args?.[0], args?.[1]);
        },
        GetAbTestConfigValue: async (client: unknown, context: unknown, section: string, key: string): Promise<any> => {
          return abTestServer.getAbTestValue(section, key);
        },
        GetRemoteGlobalConfigValue: async (client: unknown, context: unknown, section: string, key: string, defValue: any): Promise<any> => {
          return configRemoteGlobal.getConfigValue(section, key, defValue);
        },
        GetLinkFileIconConfigValue: async (client: unknown, context: unknown, fileType: string): Promise<any> => {
          return IconManager.getLinkFileIcon(fileType);
        },
      });


      this.configPath_ = path.join(GetProfilesPath(), 'config.json');
      this.configBakPath_ = this.configPath_ + '.bak';

      let configData: IConfigSectionMap | null = null;
      let exist: boolean = await FileSystemAWNS.existsAW(this.configPath_);
      if (exist) {
        const buf = await FileSystemAWNS.readFileAW(this.configPath_);
        if (buf?.length) {
          try {
            configData = JSON.parse(buf.toString());
          } catch (error) {
            //
          }
        }
      }

      if (!configData) {
        exist = await FileSystemAWNS.existsAW(this.configBakPath_);
        if (exist) {
          const buf: Buffer = await FileSystemAWNS.readFileAW(this.configBakPath_);
          if (buf?.length) {
            try {
              configData = JSON.parse(buf.toString());
            } catch (e) {
              //
            }
          }
        }
      }

      if (configData) {
        this.mergeConfigData(configData);
      }

      if (!this.saveTimerId) {
        this.saveTimerId = setInterval(async () => {
          // 5 秒自动保存配置
          if (this.isChanged_) {
            this.isChanged_ = false;
            await this.save();
          }
        }, 5000) as any;
      }

      quitPromisesServer.add(async () => {
        await this.save();
        return true;
      });

      // 初始化完成，并处理等待回调
      this.initState_ = ConfigInitState.Done;
      for (let func of this.waitInitCallback_) {
        func?.();
      }
      this.waitInitCallback_ = [];
    } while (0);
  }

  public async has(section: string, key: string): Promise<boolean> {
    await this.init();
    let exist: boolean = false;
    do {
      if (!section) {
        break;
      }

      if (!key) {
        break;
      }
      const sectionKey = this.configData_?.[section];
      if (!sectionKey) {
        break;
      }

      if (Object.prototype.hasOwnProperty.call(sectionKey, key)) {
        exist = true;
        break;
      }
    } while (0);
    return exist;
  }

  public async getValue(section: string, key: string, defValue: ConfigValueType): Promise<ConfigValueType> {
    await this.init();
    let value: ConfigValueType = defValue;
    do {
      if (!section || section === '') {
        break;
      }
      if (!key || key === '') {
        break;
      }

      const sectionKey = this.configData_?.[section];
      if (!sectionKey) {
        break;
      }

      if (key in sectionKey) {
        value = sectionKey[key];
      } else {
        //
      }
    } while (0);

    return value;
  }

  public async setValue(section: string, key: string, value: ConfigValueType, isClick: boolean = false): Promise<void> {
    await this.init();
    do {
      if (!section || section === '') {
        break;
      }
      if (!key || key === '') {
        break;
      }
      let preValue: ConfigValueType | undefined = undefined;
      const sectionKey = this.configData_?.[section];
      if (sectionKey) {
        // 获取当前的值
        preValue = sectionKey[key];
        // 设置值
        sectionKey[key] = value;
      } else {
        const sectionKey: IConfigKeyMap = {};
        sectionKey[key] = value;
        this.configData_[section] = sectionKey;
      }

      if (preValue !== value) {
        this.isChanged_ = true;
        this.fireValueChangedEvent(section, key, preValue, value, isClick);
      }
    } while (0);
  }

  public async removeValue(section: string, key: string): Promise<void> {
    await this.init();
    do {
      if (!section || section === '') {
        break;
      }
      if (!key || key === '') {
        break;
      }
      let preValue: ConfigValueType | undefined = undefined;
      const sectionKey: IConfigKeyMap = this.configData_?.[section];
      if (sectionKey) {
        // 获取当前的值
        preValue = sectionKey[key];
        // 设置值
        delete this.configData_[section][key];
        this.isChanged_ = true;
      }

      if (preValue !== undefined) {
        this.fireValueChangedEvent(section, key, preValue, undefined, false);
      }
    } while (0);
  }

  public async save(): Promise<void> {
    if (this.isInit) {
      const jsonStr: string = JSON.stringify(this.configData_, undefined, 2);
      await FileSystemAWNS.writeFileAW(this.configPath_, jsonStr);
      await FileSystemAWNS.writeFileAW(this.configBakPath_, jsonStr);
    }
  }

  public saveSync(): void {
    if (this.initState_) {
      const jsonStr: string = JSON.stringify(this.configData_, undefined, 2);
      fs.promises.writeFile(this.configPath_, jsonStr).catch();
      fs.promises.writeFile(this.configBakPath_, jsonStr).catch();
    }
  }

  protected mergeConfigData(configData: IConfigSectionMap): void {
    if (configData) {
      if (this.configData_ === null || Object.keys(this.configData_)?.length === 0) {
        this.configData_ = configData;
      } else {
        for (const section in configData) {
          const keyMap: IConfigKeyMap = configData[section];
          if (!keyMap) {
            break;
          }

          for (const key in keyMap) {
            const value: ConfigValueType = keyMap[key];
            const sectionKey: IConfigKeyMap = this.configData_[section];
            if (sectionKey) {
              sectionKey[key] = value;
            } else {
              const sectionKey: IConfigKeyMap = {};
              sectionKey[key] = value;
              this.configData_[section] = sectionKey;
            }
          }
        }
      }
    }
  }

  protected fireValueChangedEvent(
    section: string,
    key: string,
    preValue: ConfigValueType | undefined,
    newValue: ConfigValueType | undefined,
    isClick: boolean
  ): void {
    server.fireClientEvent('OnConfigValueChanged', section, key, preValue, newValue, isClick);
  }
}

export const configServer: ConfigServer = new ConfigServer();