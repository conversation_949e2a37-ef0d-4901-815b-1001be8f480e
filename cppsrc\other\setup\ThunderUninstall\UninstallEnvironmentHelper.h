#pragma once

class UninstallEnvironmentHelper
{
public:
	static UninstallEnvironmentHelper* GetInstance();
	xl::tstring CheckAllProcess(std::wstring isSlient);
	void KillRelateProcess(int killType);
	BOOL IsSpecialProcess(LPCTSTR lpszProcessName, LPCTSTR lpszFilePath);
private:
	BOOL IsThunderOrXMPWorking();
	BOOL IsMiniXmpWorking();
	BOOL IsFileLinkWorking();
	BOOL IsFileLinkConfigWorking();
	BOOL IsSoftwareCenterWorking();
	BOOL IsXBrowserWorking();
	void KillProcess(wchar_t** listProcessName, const DWORD len);
	bool NotifyXmpLiteXdasExit(bool bWait = true);
};

#define theUninstallEnvironmentHelper UninstallEnvironmentHelper::GetInstance()