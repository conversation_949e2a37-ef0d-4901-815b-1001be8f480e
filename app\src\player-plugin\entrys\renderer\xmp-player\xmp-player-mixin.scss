@mixin Controls-Popover-Fixed-Right {
  position: fixed;
  bottom: 60px;
  right: 10px;
}

@mixin Controls-Popover {
  width: 170px;
  background: var(--background-neutral-6, #131416E5);
  border: 1px solid var(--font-white-5);
  border-radius: var(--radius-12);
  padding: 6px;
}

@mixin Controls-Popover-Header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: 0 12px;
}

@mixin Controls-Popover-Title {
  height: 40px;
  display: flex;
  align-items: center;

  @include Heading-H5();

  color: var(--font-white-90);

  &.popover-title-clickable {
    cursor: pointer;

    &:hover {
      color: var(--font-white-100);
    }
  }
}

@mixin Controls-Popover-List {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

@mixin Controls-Popover-Item {
  flex-shrink: 0;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 12px;

  @include Body-M();

  color: var(--font-white-90);

  border-radius: var(--radius-6);
  cursor: pointer;

  &:hover {
    background: var(--font-white-5);
    color: var(--font-white-100);
  }

  &.popover-item-active {
    background: var(--font-white-5);
    color: var(--secondary-blue-def, #3C9BFF);
  }

  .popover-item-tag {
    margin-left: 10px;
  }

  .popover-item-tag_img {
    height: 14px;
  }
}


@mixin Subtitle-Timing-Btn {
  @include Body-M();

  cursor: pointer;
  border-radius: var(--radius-6);
  background: var(--font-white-5, rgba(255, 255, 255, 0.05));

  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--font-white-10, rgba(255, 255, 255, 0.1));
  }

  &:active {
    background: var(--font-white-5);
    color: var(--font-white-50);
  }

  color: var(--font-white-90);
}

@mixin Subtitle-Timing-Radio {
  @include Body-M();

  cursor: pointer;
  border-radius: var(--radius-6);
  background: var(--font-white-5, rgba(255, 255, 255, 0.05));
  color: var(--font-white-90);

  border: 1px solid transparent;

  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--font-white-10, rgba(255, 255, 255, 0.1));
    color: var(--font-white-100);
  }

  &.radio-active {
    border-color: var(--secondary-blue-def, rgba(60, 155, 255, 1));
    color: var(--secondary-blue-def, rgba(60, 155, 255, 1));
    background: inherit;

    cursor: default;
  }
}