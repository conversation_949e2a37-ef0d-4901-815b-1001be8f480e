
export type UserInfo = {
  username?: string;
  usernewno?: string;
  userid?: string;
  userId?: string;
  sessionid?: string;
  nickName?: string;
  account?: string;
  rank?: string;
  order?: string;
  isSubAccount?: string;
  country?: string;
  province?: string;
  city?: string;
  birthday?: string;
  sex?: string;
  mobile?: string;
  isSpecialNum?: number;
  role?: number;
  todayScore?: string;
  personalSign?: string;
  icon?: {
    general?: string
    small?: string
  }
  expireDate?: string
}

export enum VipType {
  normal = '0',
  normalVip= '2',
  platinum = '3',
  superv = '5'
}

export type VipInfo = {
  vasid?: string;
  isVip?: string;
  vipLevel?: string;
  expireDate?: string;
  vasType?: VipType;
  payId?: string;
  payName?: string;
  vipGrow?: string;
  vipDayGrow?: string;
  isAutoDeduct?: string;
  isRemind?: string;
  isYear?: string;
  register?: string;
}