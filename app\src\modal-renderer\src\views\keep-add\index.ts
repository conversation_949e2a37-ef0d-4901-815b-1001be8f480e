import { ref } from 'vue'
import { TaskType, TaskBase, BtFileDownloadInfo, BtSubFileStatus, TaskStatus } from '@root/common/task/base';
import { taskExtraClientFunc } from '@/common/impl-task'
import { IChangeTaskInfo } from './type'
import { parseFileList } from './util'

export const useKeepAdd = () => {
  const currentTaskInfo = ref<TaskBase | null>(null)
  const downloadTotal = ref(0)

  async function initTableData (taskId: number) {
    currentTaskInfo.value = await taskExtraClientFunc.getTaskBase(taskId)
    if (currentTaskInfo.value?.taskType === TaskType.Bt) {
      downloadTotal.value = await taskExtraClientFunc.getDownloadTotal(taskId)
    } else if (currentTaskInfo.value?.taskType === TaskType.Group) {
      downloadTotal.value = await taskExtraClientFunc.getGroupTotalCount(taskId)
    }
    // 判断文件是否为Bt
    if (currentTaskInfo.value?.taskType === TaskType.Bt) {
      const subFileInfos = await taskExtraClientFunc.getBtSubFileInfos(taskId)
      // tableData.value = subFileInfos
      console.log('>>>>>>>>>> subFileInfos', subFileInfos)
      const changeSubInfos = subFileInfos?.map((file) => {
        return changeBtData(file)
      })
      if (changeSubInfos) {
        console.log('>>>>>>>>>>>> changeSubInfos', changeSubInfos)
        return parseFileList(changeSubInfos, currentTaskInfo.value)
      }
    } else if (currentTaskInfo.value?.taskType === TaskType.Group) {
      const subTaskInfos = await transformGroupToBt(taskId)
      console.log('>>>>>>>>>> subTaskInfos', subTaskInfos)
      return parseFileList(subTaskInfos, currentTaskInfo.value)
    }
    return {branchMap: null, leafMap: null}
  }

  // 处理任务组转换成bt结构
  async function transformGroupToBt (taskId: number): Promise<IChangeTaskInfo[]> {
    const subTaskIds = (await taskExtraClientFunc.getGroupSubTasks(taskId)) || []
    let subTaskInfos: IChangeTaskInfo[] = []
    for (const subTaskId of subTaskIds) {
      const subTaskInfo = await taskExtraClientFunc.getTaskBase(subTaskId)
      console.log('>>>>>>>>>>> subTaskInfo', subTaskInfo)
      if (subTaskInfo) { 
        const taskToBt = await changeTaskToBtFormat(subTaskInfo)
        subTaskInfos.push(...taskToBt)
      }
    }
    // 转换成树结构
    return subTaskInfos
  }

  async function changeTaskToBtFormat (task: TaskBase): Promise<IChangeTaskInfo[]> {
    // 去除对应的rootPath
    console.log('>>>>>>> task', currentTaskInfo, task)
    const rootPath = (currentTaskInfo.value?.savePath ?? '') + '\\' +currentTaskInfo.value?.taskName
    const escapedRootPath = escapeRegExp(rootPath)
    const regexp = new RegExp(`^${escapedRootPath}`)
    const filePath = (task.savePath || '').replace(regexp, '')
    const changeTask: IChangeTaskInfo[] = []
    if (task.taskType === TaskType.Bt) {
      const groupSubFileInfos = (await taskExtraClientFunc.getBtSubFileInfos(task.taskId)) || []
      // 处理path
      const btRootPath = task.taskName + '\\'
      const changeBtSubFile = groupSubFileInfos.map((file) => {
        file.filePath = btRootPath + file.filePath
        let btData = changeBtData(file, task.taskId)
        if (!task.downloadSubTask) {
          // 是否下载完
          btData.download = btData.fileStatus === BtSubFileStatus.Complete
        }
        return btData
      })
      changeTask.push(...changeBtSubFile)
      console.log('>>>>>>>>>>>>>> groupSubFileInfos', changeBtSubFile)
    } else {
      changeTask.push({
        taskId: task.taskId,
        fileSize: task.fileSize || 0,
        downloadSize: task.downloadSize || 0,
        fileName: task.taskName || '',
        download: task.downloadSubTask,
        filePath,
        fileStatus: getFileStatus(task.taskStatus), // 完成
        errorCode: task.errorCode,
        taskType: task.taskType,
      })
    }

    return changeTask
  }

  function getFileStatus(status: number|undefined): BtSubFileStatus {
    // 转换成bt状态
    if (typeof status === 'number' && [TaskStatus.Seeding, TaskStatus.Succeeded].includes(status)) {
      return BtSubFileStatus.Complete;
    }
    if (status === TaskStatus.Failed) {
      return BtSubFileStatus.Failed;
    }
    return BtSubFileStatus.Downloading;
  }

  function changeBtData (file: BtFileDownloadInfo, rootTaskId = 0): IChangeTaskInfo {
    return {
      rootBtTaskId: rootTaskId,
      realIndex: file.realIndex,
      fileSize: file.fileSize,
      downloadSize: file.downloadSize,
      fileName: file.fileName,
      download: file.download,
      filePath: file.filePath,
      fileStatus: file.fileStatus,
      errorCode: file.errorCode,
    }
  }

  function escapeRegExp(str: string) {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // 注意：这里用了两次转义：replace 中的 \\ 表示字符串中的 \，$&表示整个被匹配的字符串
  }

  
  return {
    downloadTotal,
    currentTaskInfo,
    initTableData
  }
}