project (XLLiveUD VERSION ${THUNDER_VERSION})
THUNDER_LOG_INFO("${PROJECT_NAME} is Generating...")
# 生成version.h文件
configure_file(
    ${thunder_cmake_dir}/ThunderVersion.h.in
    ${CMAKE_CURRENT_SOURCE_DIR}/ThunderVersion.h
    @ONLY
)

file(GLOB_RECURSE PROJECT_SOURCES CONFIGURE_DEPENDS
	LIST_DIRECTORIES false
	RELATIVE "${CMAKE_CURRENT_SOURCE_DIR}"
    "*.cpp"
	"*.h"
    "*.rc"
    "*.ico"
)
DEV_CONFIG_BEGIN("${PROJECT_NAME}" ON ON)
add_executable(${PROJECT_NAME} WIN32
	${PROJECT_SOURCES}
)    
DEV_CONFIG_END()

foreach(SOURCE IN LISTS PROJECT_SOURCES)
    get_filename_component(SOURCE_PATH "${SOURCE}" PATH)
    string(REPLACE "/" "\\" SOURCE_PATH "${SOURCE_PATH}")
    source_group("${SOURCE_PATH}" FILES "${SOURCE}")
endforeach()

if (MSVC)
	target_precompile_headers(${PROJECT_NAME} PRIVATE stdafx.h)
    target_compile_options(${PROJECT_NAME} PRIVATE /MT)
endif()


USE_3RD_PARTY_LIBRARY(RAPIDJSON)
target_include_directories (${PROJECT_NAME} PRIVATE 
	${THUNDER_COMMON_PATH}/include
	${THUNDER_COMMON_PATH}/peer_id
	${THUNDER_COMMON_PATH}/xl_lib
	${THUNDER_COMMON_PATH}/xlstat/xlstat
	${THUNDER_COMMON_PATH}/Duilib/Include
	${THUNDER_COMMON_PATH}/7zUncompress
	${THUNDER_SETUP_PATH}/base/ThunderInstallInfo
)

target_link_libraries( ${PROJECT_NAME} 
	PRIVATE xl_lib
	PRIVATE Duilib
	PRIVATE 7zUncompress
	PRIVATE ThunderInstallInfo
	PRIVATE XLLiveUpdateAgent
	PRIVATE peer_id
	PUBLIC Wininet.lib
)
# compile
list(LENGTH ${PROJECT_NAME}_dependencies dependencies_length)
if(NOT dependencies_length EQUAL 0)
	add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
		COMMAND ${CMAKE_COMMAND} -E copy_if_different ${${PROJECT_NAME}_dependencies} $<TARGET_FILE_DIR:${PROJECT_NAME}>)
endif()
# 设置目标属性，将目标分组到 "Setup/ThunderUpdate" 文件夹中
set_target_properties(${PROJECT_NAME} PROPERTIES FOLDER "setup/ThunderUpdate")