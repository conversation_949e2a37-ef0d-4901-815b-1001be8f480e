import * as BaseType from '../base'

export class CategoryView {
  private nativeCategoryView: any;
  private viewId: BaseType.CategoryViewID = BaseType.CategoryViewID.UnKnown;
  constructor(nativeCategoryView: any) {
    this.nativeCategoryView = nativeCategoryView;
  }

  // viewid
  public getCategoryViewId(): BaseType.CategoryViewID {
    if (this.viewId === BaseType.CategoryViewID.UnKnown) {
      this.viewId = this.nativeCategoryView.getCategoryViewId();
    }
    return this.viewId;
  }

  // 当前view里面的所有任务
  public getTasks(): number[] {
    return this.nativeCategoryView.getTasks();
  }

  public getTaskCount(): number {
    return this.nativeCategoryView.getTaskCount();
  }

  // 某个任务是否在当前view里面
  public isTaskExist(taskId: number): boolean {
    return this.nativeCategoryView.isTaskExist(taskId) === 1;
  }

  // 开始当前view里面的所有任务
  public startAllTask(): void {
    this.nativeCategoryView.startAllTask();
  }

  // 暂停当前view里面的所有任务
  public stopAllTask(): void {
    this.nativeCategoryView.stopAllTask();
  }

  // 删除当前view里面的所有任务
  public deleteAllTask(deleteFile: boolean): void {
    this.nativeCategoryView.deleteAllTask(deleteFile);
  }
}