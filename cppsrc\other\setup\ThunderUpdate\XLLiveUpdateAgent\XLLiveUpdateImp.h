#pragma once

#define WM_EXECUTE_END  (WM_USER + 3000)

#include "XLLiveUpdate.h"
#include "../common/liveupdate.h"
#include <Windows.h>
#include "SockIPC/SockIPC_Interface.h"

class XLLiveUpdateImp : public CWindowImpl<XLLiveUpdateImp>
{
public:
    XLLiveUpdateImp(void);
    ~XLLiveUpdateImp(void);

    static XLLiveUpdateImp* XLLiveUpdateImp::GetInstance()
    {
        static XLLiveUpdateImp s_instance;

        return &s_instance;
    }

	BEGIN_MSG_MAP(XLLiveUpdateImp)
		MESSAGE_HANDLER(WM_EXECUTE_END, OnExecuteEnd)
	END_MSG_MAP()

    bool Init(const char* szProductKey, const char* szProductKeyName, const wchar_t* szProductName, const wchar_t* szProductVersion, const wchar_t* szDownloadInterfaceDllPath, XLLIVEUPDATE_SHUTDOWN_CALLBACK callbackShutdown, bool isCheckStartupUpdate = true, int useServerVersion = 2, int productID = 1);
    void StartAutoUpdate(XLLIVEUPDATE_STARTUP_PATCH_CALLBACK callbackStartupPatch = NULL, XLLIVEUPDATE_PACK_READY_CALLBACK callBackPackReady = NULL, bool bCheckUpdatePackage = true);
    void StartManualUpdate(XLLIVEUPDATE_PACK_READY_CALLBACK callBackPackReady = NULL);
    void ExecuteStartupPatch();
    void StartLiveUpdateWithXAR(const wchar_t* xarPath, const wchar_t* xarName, const wchar_t* param, bool bRunInThread);
    void UnInit();

    void SetLiveUDExePath(const wchar_t* szLiveUDExePath);
    void SetServer(const wchar_t* serverAddr, const int serverPort);
    void SetResetParam(const wchar_t* wsResetParam);
    void SetProductPath(const wchar_t* szProductPath);
    void PreCopyNeedFiles();
    void SetLocalInstallParam(const wchar_t* wsLocalInstallParam);

    void SetInService(bool isInService);
    void SetPackFinishCallBack(XLLIVEUPDATE_PACK_FINISH_CALLBACK callbackPackFinish);
	void SetExecuteCallBack(XLLIVEUPDATE_EXECUTE_CALLBACK callbackExecute, void* callbackParam);

	HRESULT OnExecuteEnd(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL & bHandled);

private:
    DWORD executeLiveUD(const wchar_t* startupType, const wchar_t* xarPath = NULL, const wchar_t* xarName = NULL, const wchar_t* param = NULL, bool bRunInThread = true);
    DWORD executeLiveUDProc(const wchar_t* startupType);
    void SetLiveUDExeRunPath(const wchar_t* szLiveUDExePath);
    static unsigned int __stdcall PreCopyNeedFileProc(void* param);
    static unsigned int __stdcall StartLiveUpdateWithXARProc(void* param);
    bool CopyNeedFile(const wchar_t* DestDir, const wchar_t* file_name);
    bool CopyNeedFiles(const wchar_t* DestDir);
    static unsigned int __stdcall ThreadProc(void* param);
    static unsigned int __stdcall IPCThread(void* param);
    static DWORD OnAccept(XAF_IPC_LISTENER_HANDLE pLis, XAF_IPC_CONNECTION_HANDLE sockConn, IPCAddress* pAddr, void* data);
    static DWORD OnRecv(XAF_IPC_CONNECTION_HANDLE sockIPC, void* data);
    static DWORD OnSend(XAF_IPC_CONNECTION_HANDLE sockIPC, void* data);
    std::wstring m_ProductName;
    std::wstring m_ProductVer;
    std::wstring m_tpPath;
    std::wstring m_startupType;

    bool m_bCheckUpdatePackage;

    std::wstring m_xarPath;
    std::wstring m_xarName;
    std::wstring m_param;

    std::wstring m_wsResetParam;

    std::wstring m_LiveUDPath;
    std::wstring m_LiveUDRunPath;
    std::wstring m_ProductPath;
    std::wstring m_ServerAddr;
    int m_serverPort;

    std::string m_szProductKey;
    std::string m_szProductKeyName;

    XLLIVEUPDATE_SHUTDOWN_CALLBACK m_callBackShutdown;
    XLLIVEUPDATE_STARTUP_PATCH_CALLBACK m_callbackStartupPatch;
    XLLIVEUPDATE_PACK_READY_CALLBACK m_AutoUpdatePackReady;
    XLLIVEUPDATE_PACK_READY_CALLBACK m_ManualUpdatePackReady;
    XLLIVEUPDATE_PACK_FINISH_CALLBACK m_callbackPackFinish;
	XLLIVEUPDATE_EXECUTE_CALLBACK m_callbackExecute;

	void* m_callbackExecuteParam;

    bool m_bReadyRecvMsgBody;
    unsigned int m_nBodySize;
    unsigned int m_nIPCThreadId;

    unsigned int m_nServerVersion;

    HANDLE m_hPreCopyFinishedEvent;
    bool m_bPreCopyNeededFiles;

    std::wstring m_wsLocalInstallParam;

    bool m_isInService;

	int m_productID;
};
