import { client } from "@xunlei/node-net-ipc/dist/ipc-client"
import { XLStatNS } from "../impl/xlstat";
import { XLStat } from "../impl/Stat";
import { UserInfo } from "@root/common/account/impl/accountHelper";

export class StatHelperServer {
  private static isinited_: boolean = false;
  static init() {
    if (this.isinited_) {
      return;
    }
    this.isinited_ = true;
    client.registerFunctions({
      // 旧上报使用，初始化参数，如appkey、appname
      StatHelperinitParam: (ctx: unknown, param: XLStatNS.IInitParam) => {
        XLStatNS.initParam(param);
      },

      StatHelperSetUserInfo: (ctx: unknown, userID: number = 0, userInfo: UserInfo | null) => {
        XLStatNS.setUserID(userID);
        XLStat.GetInstance().updateUser(userInfo);
      },

      StatHelperTrackEvent: (ctx: unknown, param: any) => {
        XLStatNS.trackEvent(param.key, param.attr1, param.attr2, param.cost1, param.cost2, param.cost3, param.cost4, param.extData, param.cookie);
        XLStat.GetInstance().trackEvent(param.eventName, param.dataObj);
        return true;
      },

      StatHelperTrackClick: (ctx: unknown, key: string, cookie: number = 0) => {
        XLStatNS.trackClick(key, cookie);
      },

      StatHelperAsyncUninit: (ctx: unknown, cookie: number = 0) => {
        XLStatNS.asyncUninit(cookie);
      },

      StatHelperUninit: (ctx: unknown) => {
        XLStatNS.uninit();
      },
    });
  }
}