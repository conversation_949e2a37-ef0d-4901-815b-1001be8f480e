<script setup lang="ts">
import { ref, type HTMLAttributes } from 'vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import { useDebounceFn } from '@vueuse/core'

const props = withDefaults(defineProps<{
  modelValue: string
  type?: string
  showPassword?: boolean
  placeholder?: string
  class?: HTMLAttributes['class']
  inputClass?: HTMLAttributes['class']
  disabled?: boolean
  countryCodes?: Array<{
    code: string
    name: string
  }>
  style?: HTMLAttributes['style']
  readonly?: boolean
  showPasswordIcon?: boolean
  needDebounce?: boolean
}>(), {
  type: 'text',
  showPassword: false,
  placeholder: '请输入',
  disabled: false,
  countryCodes: () => [{
    code: '+86',
    name: '中国大陆'
  }],
  readonly: false,
  showPasswordIcon: true,
  needDebounce: false
})
const curCountryCode = ref(props.countryCodes[0].code)

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'update:countryCode', value: string): void
  (e: 'keydown', value: KeyboardEvent): void
}>()

const _showPassword = ref(props.showPassword)

const handleInput = useDebounceFn((e: Event) => {
  emit('update:modelValue', (e.target as HTMLInputElement).value)
}, props.needDebounce ? 500 : 0)

const togglePassword = () => {
  _showPassword.value = !_showPassword.value
}

const handleSelectCountry = (value: string) => {
  curCountryCode.value = value
  emit('update:countryCode', value)
}

const handleKeyDown = (e: KeyboardEvent) => {
  emit('keydown', e)
}

</script>

<template>
  <div
    :class="['input-container', props.class, type === 'phone' && 'phone-input-container', disabled && 'input-container-disabled']"
    :style="props.style">
    <slot name="left" />
    <DropdownMenu v-if="type === 'phone'" :sideOffset="12" :items="countryCodes.map(item => ({
      key: item.code,
      label: item.name,
      leftText: item.code
    }))" side="bottom" :align="'start'" :contentClass="'input-dropdown-content'"
      :left-text-class="'input-dropdown-left-text'" :item-class="'input-dropdown-item'" @select="handleSelectCountry">
      <span class="input-dropdown-label">{{ curCountryCode }}</span>
      <i class="xl-icon-general-direction-caret-down-s"></i>
    </DropdownMenu>
    <input :class="inputClass" :disabled="disabled" :type="_showPassword ? 'text' : type" :placeholder="placeholder"
      :value="modelValue" @input="handleInput" @keydown="handleKeyDown" :readonly="readonly" />
    <slot name="right" />
    <i v-if="type === 'password' && showPasswordIcon"
      :class="!_showPassword ? 'xl-icon-general-eyeinvisible-m' : 'xl-icon-general-eye-m'" @click="togglePassword"></i>
  </div>
</template>

<style scoped lang="scss">
.input-container {
  display: flex;
  align-items: center;
  padding: 0 11px;
  height: 44px;
  width: 336px;
  border-radius: var(--border-radius-M, 8px);
  border: 1px solid var(--border-border-2, #E5E6EB);
  gap: 18px;
  flex-shrink: 0;

  &:focus-within {
    border: 1px solid var(--border-border-primary, #226DF5);
  }

  i {
    height: 16px;
    width: 16px;
    cursor: pointer;
  }

  input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    text-align: justify;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    width: 100%;
    color: var(--font-font-1, #272E3B);

    &::placeholder {
      color: var(--font-font-4, #C9CDD4);
    }
  }
}

.phone-input-container {
  padding-left: 0;
}

.input-container-disabled {
  input {
    color: var(--font-font-4, #C2C6CB) !important;
    cursor: not-allowed;
  }
}
</style>

<style>
.input-dropdown-content {
  width: 336px;
}

.input-dropdown-left-text {
  left: 12px
}

.input-dropdown-item {
  padding-left: 64px;
}

.input-dropdown-label {
  color: var(--font-font-1, #272E3B);
  font-size: 14px;
  line-height: 22px;
  margin-right: 6px;
  padding-left: 11px;
}
</style>