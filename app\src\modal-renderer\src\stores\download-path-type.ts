import { ref } from 'vue'

import { defineStore } from 'pinia'
import { DownloadPathType } from '@root/modal-renderer/types/new-task.type'

/**
 * Download Path Type Store
 * 专门管理下载路径类型状态
 */
export const useDownloadPathTypeStore = defineStore('downloadPathType', () => {
  // ===== 状态管理 =====

  /**
   * 当前下载路径类型
   */
  const currentPathType = ref<DownloadPathType>(DownloadPathType.Local)

  /**
   * 是否已经初始化
   */
  const isInitialized = ref<boolean>(false)

  // ===== Actions =====

  /**
   * 初始化路径类型
   * 设置为本地类型，且只能初始化一次
   */
  const initPathType = () => {
    if (isInitialized.value) {
      console.log('[DownloadPathTypeStore] 已经初始化过，跳过重复初始化')
      return
    }

    console.log('[DownloadPathTypeStore] 初始化路径类型为本地 后期需要接入接口')
    currentPathType.value = DownloadPathType.Local
    isInitialized.value = true
  }

  /**
   * 设置当前路径类型为本地
   */
  const setPathTypeToLocal = () => {
    console.log('[DownloadPathTypeStore] 设置路径类型为本地')
    currentPathType.value = DownloadPathType.Local
  }

  /**
   * 设置当前路径类型为云盘
   */
  const setPathTypeToCloud = () => {
    console.log('[DownloadPathTypeStore] 设置路径类型为云盘')
    currentPathType.value = DownloadPathType.Cloud
  }

  /**
   * 获取当前路径类型
   */
  const getCurrentPathType = (): DownloadPathType => {
    return currentPathType.value
  }

  /**
   * 检查当前是否为本地路径类型
   */
  const isLocalPath = (): boolean => {
    return currentPathType.value === DownloadPathType.Local
  }

  /**
   * 检查当前是否为云盘路径类型
   */
  const isCloudPath = (): boolean => {
    return currentPathType.value === DownloadPathType.Cloud
  }

  /**
   * 重置为默认类型（本地）
   */
  const resetToDefault = () => {
    console.log('[DownloadPathTypeStore] 重置路径类型为默认（本地）')
    currentPathType.value = DownloadPathType.Local
  }

  // ===== 返回值 =====
  return {
    // 状态
    currentPathType,
    isInitialized,

    // Actions
    initPathType,
    setPathTypeToLocal,
    setPathTypeToCloud,
    getCurrentPathType,
    isLocalPath,
    isCloudPath,
    resetToDefault,
  }
})
