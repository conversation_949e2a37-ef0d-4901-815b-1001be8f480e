#ifndef __XLIPC_STREAM_H__
#define __XLIPC_STREAM_H__
#include <Windows.h>
#include <vector>

#include "xlipc_ref.h"

class XLIPCStream
	: public XLIPCRef
{
public:
	XLIPCStream(void);
	virtual ~XLIPCStream(void);

public:
	long GetParamType(unsigned long* lpType);

	long WriteUtf8(const char* lpString);
	long WriteUnicode(const wchar_t* lpString);
	long WriteByte(BYTE bValue);
	long WriteWord(WORD wValue);
	long WriteDWord(DWORD dwValue);
	long WriteInt(int iValue);
	long WriteUint(unsigned int iValue);
	long WriteLong(long lValue);
	long WriteBoolean(bool bValue);
	long WriteInt64(long long value);
	long WriteUint64(unsigned long long value);
	long WriteBytes(const unsigned char* lpBuffer, size_t nLength);
	long WriteArray(XLIPCStream* pStream);
	long WriteStream(XLIPCStream* pStream);
	long WriteDouble(double value);
	long WriteCallback(__int64 func_ref);

	long ReadUtf8(char* lpString, size_t nLength, size_t* lpcbLength);
	long ReadUnicode(wchar_t* lpString, size_t nLength, size_t* lpcbLength);
	long ReadByte(BYTE* bValue);
	long ReadWord(WORD* wValue);
	long ReadDWord(DWORD* dwValue);
	long ReadInt(int* iValue);
	long ReadUint(unsigned int* iValue);
	long ReadLong(long* lValue);
	long ReadBoolean(bool* bValue);
	long Readint64(__int64* value);
	long ReadUint64(unsigned __int64* value);
	long ReadBytes(unsigned char* lpBuffer, size_t nLength, size_t* lpcbLength);
	long ReadStream(XLIPCStream** pStream);
	long ReadDouble(double* value);
	long ReadCallback(__int64* func_ref);

	long UpdateInt(int iValue);
	long UpdateSize(size_t iValue);

	long Next();
	bool IsEOF() const;
	size_t GetCurrentIndex() const;
	size_t GetSize() const;
	long Reset();
	long MoveTo(size_t index);
	void Clear();

	long Encode(char* lpBuffer, size_t nLength, size_t* lpcbLength);
	long Decode(const char* lpBuffer, size_t nLength);

	size_t GetEncodeBufferNeedLength() const;
	static size_t GetIntEncodeBufferNeedLength();

private:
	struct Data
	{
		size_t enum_type_;
		void* data_;
	};

	std::vector<Data> data_;
	size_t index_;
};

#endif