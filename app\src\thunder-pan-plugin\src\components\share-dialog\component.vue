<script lang="ts" setup>
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import Button from '@root/common/components/ui/button/index.vue'
import TDCheckbox from '@root/common/components/ui/checkbox/index.vue'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'

import { computed, onMounted, ref, watch } from 'vue';
import { IShareDialogOptions } from '.';
import { ThunderPanClientSDK } from '@root/common/thunder-pan-manager/client'
import { clipboard, nativeImage } from 'electron'
import { GlobalEventHelper } from '@/utils/global-event-helper';
import { Tippy } from 'vue-tippy'
import QRCode from 'qrcode'

const timesOptions = [
  { key: '0', label: '不限提取次数' },
  { key: '1', label: '1次' },
  { key: '3', label: '3次' },
  { key: '5', label: '5次' },
  { key: '10', label: '10次' },
  { key: '15', label: '15次' },
  { key: '20', label: '20次' },
]
const daysOptions = [
  { key: '0', label: '链接永久有效' },
  { key: '1', label: '1天' },
  { key: '3', label: '3天' },
  { key: '5', label: '5天' },
  { key: '10', label: '10天' },
  { key: '15', label: '15天' },
  { key: '20', label: '20天' },
]

defineOptions({
  name: 'ShareDialog',
})

const props = defineProps<IShareDialogOptions>()

const visible = ref(true)
const timesIndex = ref(0)
const daysIndex = ref(0)
const qrCodeImg = ref('')
const shareLinkText = ref('')
const shareText = ref('')
const shareUrl = ref('')
const passCode = ref('')
const isFastAccess = ref(true)

const fileIds = computed(() =>  props.files.map(file => file.id || ''))
const fileNames = computed(() => props.files.map(file => file.name || '').join('，'))
const currentTimes = computed(() => timesOptions.find(opt => Number(opt.key) === timesIndex.value))
const currentDays = computed(() => daysOptions.find(opt => Number(opt.key) === daysIndex.value))

function concatShareCodeToShareLink (shareLink: string, shareCode: string) {
  const query = new URLSearchParams({ pwd: shareCode }).toString()
  const split = shareLink.includes('?') ? '&' : '?'

  return `${shareLink}${split}${query}`
}

function fastShareQRCode (shareLink: string, shareCode: string): Promise<string>  {
  return new Promise((resolve) => {
    let url = shareLink + '?' + 'pwd=' + shareCode + '#'
    QRCode.toDataURL(url, { margin: 0 }, (error: any, imgCode: string) => {
      resolve(error ? '' : imgCode)
    })
  })
}

async function copyImageIntoClipboard () {
  const img = await nativeImage.createFromDataURL(qrCodeImg.value)
  clipboard.writeImage(img)
  window.__VueGlobalProperties__.$message({
    message: '复制成功',
    type: 'success',
  })
}

function handleCopyShareText () {
  clipboard.writeText(shareText.value)
  window.__VueGlobalProperties__.$message({
    message: '复制成功',
    type: 'success',
  })
}

function handleCopyShareLinkText () {
  clipboard.writeText(shareLinkText.value)
  window.__VueGlobalProperties__.$message({
    message: '复制成功',
    type: 'success',
  })
}

function handleTimesOptionsClick (key: string) {
  timesIndex.value = Number(key)
}

function handleDaysOptionsClick (key: string) {
  daysIndex.value = Number(key)
}

function handleClose () {
  if (props.onClose) {
    props.onClose()
  }
}

function handleDialogUpdateOpen (isOpen: boolean) {
  visible.value = isOpen
  if (!isOpen) {
    handleClose()
  }
}

async function updateShareSetting () {
  const res = await ThunderPanClientSDK.getInstance().createShare({
    params: {
      file_ids: fileIds.value,
      share_to: 'copy',
      title: '云盘资源分享',
      restore_limit: String(timesIndex.value === 0 ? -1 : timesIndex.value),
      expiration_days: String(daysIndex.value === 0 ? -1 : daysIndex.value),
      params: {
        subscribe_push: String(false)
      }
    }
  })

  if (res.success && res.data) {
    shareUrl.value = res.data.share_url!
    passCode.value = res.data.pass_code!
    shareText.value = res.data.share_text!

    generateQrCode(res.data.share_url!, res.data.pass_code!)
    GlobalEventHelper.getInstance().emit(GlobalEventHelper.EventKey.SHARE_LIST_REFRESH)
  }
}

async function generateQrCode (share_url: string, pass_code: string) {
  qrCodeImg.value = await fastShareQRCode(share_url, pass_code)
}

watch(timesIndex, () => {
  updateShareSetting()
})

watch(daysIndex, () => {
  updateShareSetting()
})

watch(isFastAccess, () => {
  shareLinkText.value = !isFastAccess.value ? (shareUrl.value + '#') : concatShareCodeToShareLink(shareUrl.value, passCode.value)
})

onMounted(() => {
  const { share_url, share_text, pass_code } = props.preShareResult

  shareUrl.value = share_url!
  passCode.value = pass_code!
  shareLinkText.value = concatShareCodeToShareLink(share_url!, pass_code!)
  shareText.value = share_text!
  generateQrCode(share_url!, pass_code!)
})

defineExpose({
  visible,
})
</script>

<template>
  <Dialog
    title="分享文件"
    contentStyle="width: 680px; height: 466px;"
    :open="visible"
    :modal="true"
    :draggable="false"
    :show-title-icon="false"
    :prevent-default-close="true"
    :disable-header-draggable="true"
    @update:open="handleDialogUpdateOpen"
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="file-details">
        <div class="file-icon">
          <img :src="files[0].icon_link" alt="">
        </div>

        <div class="file-info">
          <div class="file-name">{{ fileNames }}</div>
          <div class="file-count">共{{ files.length }}个文件</div>
        </div>
      </div>

      <div class="share-setting">
        <div>分享链接</div>

        <div class="setting-actions">
          <div class="fast-action-area">
            <TDCheckbox v-model="isFastAccess" class="action-checkbox">
              <span>无需提取码</span>
            </TDCheckbox>

            <Tippy content="开启后，链接访问者无需输入提取码，可直接访问分享文件">
              <i class="xl-icon-tips-question-circle-l"></i>
            </Tippy>
          </div>

          <DropdownMenu :items="timesOptions" @select="handleTimesOptionsClick">
            <div class="action-button">
              <span>{{ currentTimes!.label }}</span>
              <i class="xl-icon-triangle-down"></i>
            </div>
          </DropdownMenu>

          <DropdownMenu :items="daysOptions" @select="handleDaysOptionsClick">
            <div class="action-button">
              <span>{{ currentDays!.label }}</span>
              <i class="xl-icon-triangle-down"></i>
            </div>
          </DropdownMenu>
        </div>
      </div>

      <div class="share-link">
        <div class="link">{{ shareLinkText }}</div>
        <Button
          class="copy-btn"
          size="default"
          variant="ghost"
          @click="handleCopyShareLinkText"
        >
          <i class="xl-icon-general-copy-m"></i>
        </Button>
      </div>

      <div class="qr-code">
        <img :src="qrCodeImg" alt="">

        <div class="description">
          <div class="title">复制二维码并发送给好友</div>
          <div class="subtitle">已包含提取码，好友扫码后即可获取文件，无需再次输入</div>
          <Button
            size="default"
            variant="secondary"
            @click="copyImageIntoClipboard"
          >
            复制二维码
          </Button>
        </div>
      </div>
    </div>

    <template #actions>
      <div class="dialog-actions">
        <p class="tips">响应净网行动，迅雷严禁传播色情违法内容，如发现将封禁账号</p>

        <div class="right-actions">
          <Button
            size="default"
            variant="secondary"
            @click="handleClose"
          >
            取消
          </Button>

          <Button
            size="default"
            variant="default"
            @click="handleCopyShareText"
          >
            复制分享链接
          </Button>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  font-size: 13px;
  color: var(--font-font-1);

  .file-details {
    height: 72px;
    padding: 12px 18px;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid var(--border-border-2);
    border-radius: var(--border-radius-M);

    .file-icon {
      width: 40px;
      height: 40px;

      img {
        width: 100%;
      }
    }

    .file-info {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .file-name {
        flex-grow: 1;
        -webkit-line-clamp: 1;
        display: -webkit-box;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        white-space: pre-wrap;
      }

      .file-count {
        color: var(--font-font-3);
      }
    }
  }

  .share-setting {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;

    .setting-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .fast-action-area {
        display: flex;
        align-items: center;
      }

      .action-checkbox {
        padding: 5px 8px;

        i {
          margin-left: 4px;
          font-size: 16px;
        }
      }

      .action-button {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--font-font-2);
        padding: 5px 8px;
        position: relative;

        i {
          font-size: 12px;
        }

        &::before {
          position: absolute;
          left: -4px;
          content: '';
          height: 12px;
          width: 1px;
          background-color: var(--border-border-2);
        }
      }
    }
  }

  .share-link {
    margin-top: 4px;
    padding: 11px 12px;
    background-color: var(--button-button2-default);
    border-radius: var(--border-radius-S);
    display: flex;
    align-items: center;
    gap: 12px;

    .link {
      flex-grow: 1;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      white-space: pre-wrap;
    }

    .copy-btn {
      flex-shrink: 0;
      i {
        font-size: 16px;
      }
    }
  }

  .qr-code {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 0;

    img {
      width: 106px;
      height: 106px;
      padding: 8px;
      border-radius: var(--border-radius-M);
      border: 1px solid var(--border-border-3);
    }

    .description {
      display: flex;
      flex-direction: column;

      .title {
        margin-bottom: 8px;
      }

      .subtitle {
        color: var(--font-font-3);
        margin-bottom: 10px;
      }
    }
  }
}
.dialog-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .tips {
    color: var(--font-font-3);
    font-size: 13px;
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    button {
      min-width: 84px;
    }
  }

  .is-warning {
    color: var(--functional-error-default);
  }
}
</style>
