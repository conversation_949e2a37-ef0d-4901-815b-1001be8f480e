/**
 * 封装给所有进程调用退出回调的模块
 */

import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
// import { mainRendererContext } from '@xunlei/node-net-ipc/dist/ipc-base';

export async function addQuitPromise(callback: () => Promise<boolean>): Promise<void> {
  // if (global.__filename.indexOf(mainRendererContext + '\\') !== -1) {
  //   const { quitPromisesServer } = import('@root/common/quit-promises/server/quit-promises');
  //   quitPromisesServer.add(callback);
  // } else {
  //   await client.callServerFunction('AddQuitPromise', callback);
  // }
  await client.callServerFunction('AddQuitPromise', callback);
}

export async function quitApp(): Promise<void> {
  await client.callServerFunction('QuitApp');
}