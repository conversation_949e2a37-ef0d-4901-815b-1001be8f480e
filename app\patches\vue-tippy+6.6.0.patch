diff --git a/node_modules/vue-tippy/dist/vue-tippy.esm-browser.js b/node_modules/vue-tippy/dist/vue-tippy.esm-browser.js
index 875b3ee..00342c5 100644
--- a/node_modules/vue-tippy/dist/vue-tippy.esm-browser.js
+++ b/node_modules/vue-tippy/dist/vue-tippy.esm-browser.js
@@ -4355,11 +4355,11 @@ const TippyComponent = defineComponent({
         watch(tippy.state, () => {
             emit('state', unref(tippy.state));
         }, { immediate: true, deep: true });
-        watch(() => props, () => {
-            tippy.setProps(getOptions());
-            if (contentSlot)
-                tippy.setContent(() => contentElem.value);
-        }, { deep: true });
+        // watch(() => props, () => {
+        //     tippy.setProps(getOptions());
+        //     if (contentSlot)
+        //         tippy.setContent(() => contentElem.value);
+        // }, { deep: true });
         let exposed = reactive({
             elem,
             contentElem,
diff --git a/node_modules/vue-tippy/index.cjs b/node_modules/vue-tippy/index.cjs
index 9e63287..ab9444d 100644
--- a/node_modules/vue-tippy/index.cjs
+++ b/node_modules/vue-tippy/index.cjs
@@ -1,5 +1,6 @@
 'use strict'
 
+
 if (process.env.NODE_ENV === 'production') {
   module.exports = require('./dist/vue-tippy.prod.cjs')
 } else {
diff --git a/node_modules/vue-tippy/src/components/Tippy.ts b/node_modules/vue-tippy/src/components/Tippy.ts
index 9b37f5d..42ec082 100644
--- a/node_modules/vue-tippy/src/components/Tippy.ts
+++ b/node_modules/vue-tippy/src/components/Tippy.ts
@@ -142,12 +142,12 @@ const TippyComponent = defineComponent({
       emit('state', unref(tippy.state))
     }, { immediate: true, deep: true })
 
-    watch(() => props, () => {
-      tippy.setProps(getOptions())
+    // watch(() => props, () => {
+    //   tippy.setProps(getOptions())
 
-      if (contentSlot)
-        tippy.setContent(() => contentElem.value)
-    }, { deep: true })
+    //   if (contentSlot)
+    //     tippy.setContent(() => contentElem.value)
+    // }, { deep: true })
 
     let exposed = reactive({
       elem,
