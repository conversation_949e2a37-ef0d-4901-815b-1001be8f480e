import * as BaseType from '../base'
export class BtFile {
    private nativeFile: any;
    constructor(nativeFile: any) {
        this.nativeFile = nativeFile;
    }

    public isDownload(): boolean {
        return this.nativeFile.isDownload();
    }

    public getGcid(): string {
        return this.nativeFile.getGcid();
    }

    public getCid(): string {
        return this.nativeFile.getCid();
    }

    public getErrorCode(): number {
        return this.nativeFile.getErrorCode();
    }

    public getStatus(): BaseType.BtSubFileStatus {
        return this.nativeFile.getStatus();
    }

    public getFileSize(): number {
        return this.nativeFile.getFileSize();
    }

    public getReceiveSize(): number {
        return this.nativeFile.getReceiveSize();
    }

    public getFileIndex(): number {
        return this.nativeFile.getFileIndex();
    }

    public isSupportPlay(): boolean {
        return this.nativeFile.isSupportPlay();
    }

    public getFileName(): string {
        return this.nativeFile.getFileName();
    }

    public getFilePath(): string {
        return this.nativeFile.getFilePath();
    }
}