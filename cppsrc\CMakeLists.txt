# the minimum version of CMake.
cmake_minimum_required(VERSION 3.15.1)
project(thunder)

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)
if(${CMAKE_SYSTEM_NAME} STREQUAL Darwin) #macos 
    set(CMAKE_OSX_ARCHITECTURES "arm64;x86_64" CACHE STRING "Build architectures for Mac OS X" FORCE)
    set(CMAKE_CXX_STANDARD 20)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20")
    add_definitions(-DCOMMON_STORAGE -DCLOSE_LOG)
    add_compile_options("-fPIC")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20 -pthread")
    add_subdirectory(native_core/src/main/cpp/common)
    add_subdirectory(native_core/src/main/cpp/libuv1.44)
    add_subdirectory(native_core/src/main/cpp/download_kernel)
    add_subdirectory(native_core/src/main/cpp/player_control)
    add_subdirectory(thunder_helper)
elseif(${CMAKE_SYSTEM_NAME} STREQUAL Windows)
    add_compile_options(/wd4828)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build)
    set(CMAKE_JS_INC ${CMAKE_CURRENT_SOURCE_DIR}/node/win/x64/include/node)
    set(CMAKE_JS_LIB ${CMAKE_CURRENT_SOURCE_DIR}/node/win/x64/lib/node.lib)
    set(CMAKE_JS_SRC ${CMAKE_CURRENT_SOURCE_DIR}/node/win/src/win_delay_load_hook.cc)
    set(CMAKE_SHARED_LINKER_FLAGS "/DELAYLOAD:NODE.EXE")
    set(CMAKE_CXX_STANDARD 20)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20")
    #add_definitions(-DCOMMON_STORAGE -DCLOSE_LOG)
    add_definitions(-DCOMMON_STORAGE -DPLAYER_INDEPENDENT_PROCESS)
    add_subdirectory(native_core/src/main/cpp/common)
    add_subdirectory(native_core/src/main/cpp/libuv1.44)
    add_subdirectory(native_core/src/main/cpp/download_kernel)
    add_subdirectory(native_core/src/main/cpp/player_control)
    add_subdirectory(thunder)
    add_subdirectory(pc_addon)
    add_subdirectory(dk_addon)
    add_subdirectory(thunder_helper)
    add_subdirectory(win_common)
    add_subdirectory(player_containor)
else()
    message(FATAL_ERROR, "!!!!! not support platform: ${CMAKE_SYSTEM_NAME}")

endif()

