<script lang="ts" setup>
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
onMounted(() => {
  const redirect = route.query.redirect as string
  if (redirect) {
    // 跳回原页面, 100ms的动画效果展示
    setTimeout(() => {
      router.replace(redirect)
    }, 100)
    
  }
})
</script>

<template>
  <div class="xly-loading">
    <div class="xly-loading__img"></div>
  </div>
</template>

<style lang="scss" scoped></style>
