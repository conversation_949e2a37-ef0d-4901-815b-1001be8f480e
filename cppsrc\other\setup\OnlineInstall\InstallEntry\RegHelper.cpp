#include "StdAfx.h"
#include "./reghelper.h"
#include <Shlwapi.h>

HRESULT CRegHelper::ReadRegString(HKEY hKey, LPCTSTR pszSubKey, LPCTSTR pszKeyName, std::wstring& strValue)
{
    const int cdwBufferSize = 512;
    wchar_t szBuffer[cdwBufferSize];
    ZeroObject(szBuffer);
    DWORD dwBufferSize = cdwBufferSize;
    DWORD dwType = (DWORD)-1;

    DWORD dwRet = SHGetValue(hKey, pszSubKey, pszKeyName, &dwType, szBuffer, &dwBufferSize);
    if (dwRet != ERROR_SUCCESS)
    {
        strValue.clear();
        return E_FAIL;
    }
    strValue = szBuffer;
    return S_OK;
}


HRESULT CRegHelper::WriteRegString(HKEY hKey, LPCTSTR pszSub<PERSON>ey, LPCTSTR pszKeyName, const std::wstring& strValue)
{
    DWORD dwRet = SHSetValue(hKey, pszSubKey, pszKeyName, REG_SZ, strValue.c_str(), ((DWORD)strValue.size() + 1) * sizeof(wchar_t));
    return dwRet == ERROR_SUCCESS  ? S_OK : E_FAIL;
}


HRESULT CRegHelper::ReadRegDword(HKEY hKey, LPCTSTR pszSubKey, LPCTSTR pszKeyName, DWORD& dwValue, DWORD dwDefault)
{
    const int cdwBufferSize = 512;
    wchar_t szBuffer[cdwBufferSize];
    ZeroObject(szBuffer);
    DWORD dwBufferSize = cdwBufferSize;
    DWORD dwType = (DWORD)-1;

    DWORD dwRet = SHGetValue(hKey, pszSubKey, pszKeyName, &dwType, szBuffer, &dwBufferSize);
    if (dwRet != ERROR_SUCCESS)
    {
        dwValue = dwDefault;
        return E_FAIL;
    }

    dwValue = ((DWORD*)szBuffer)[0];
    return S_OK;
}


HRESULT CRegHelper::WriteRegDword(HKEY hKey, LPCTSTR pszSubKey, LPCTSTR pszKeyName, DWORD dwValue)
{
    DWORD dwRet = SHSetValue(hKey, pszSubKey, pszKeyName, REG_DWORD, &dwValue, sizeof(DWORD));
    return dwRet == ERROR_SUCCESS  ? S_OK : E_FAIL;
}


HRESULT CRegHelper::DeleteRegKey(HKEY hKey, LPCTSTR pszSubKey)
{
    DWORD dwRet = SHDeleteKey(hKey, pszSubKey);
    return dwRet == ERROR_SUCCESS  ? S_OK : E_FAIL;
}


HRESULT CRegHelper::DeleteRegValue(HKEY hKey, LPCTSTR pszSubKey, LPCTSTR pszKeyName)
{
    DWORD dwRet = SHDeleteValue(hKey, pszSubKey, pszKeyName);
    return dwRet == ERROR_SUCCESS  ? S_OK : E_FAIL;
}





