import { ref, computed } from 'vue'
import {  RouteRecordRaw } from 'vue-router'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { router } from '@root/main-renderer/src/router';
import { RouteHistoryManager_Channels } from '@root/common/constant';
// 路由配置，用于检测重定向
let routesConfig: RouteRecordRaw[] = []

// 路由历史记录最大限制数量
const MAX_HISTORY_SIZE = 10



// 设置路由配置
export function setRoutesConfig(routes: RouteRecordRaw[]) {
  routesConfig = routes
}

// 检查是否是重定向关系
function isRedirect(fromPath: string, toPath: string): boolean {
  // 查找源路径对应的路由配置
  const sourceRoute = findRouteByPath(fromPath, routesConfig)
  return typeof sourceRoute?.redirect === 'string' && sourceRoute.redirect === toPath
}

// 递归查找路由配置
function findRouteByPath(path: string, routes: RouteRecordRaw[]): RouteRecordRaw | null {
  for (const route of routes) {
    if (route.path === path) {
      return route
    }
    if (route.children) {
      const found = findRouteByPath(path, route.children)
      if (found) return found
    }
  }
  return null
}

// 获取路由的最终目标路径（处理重定向）
function getFinalPath(path: string): string {
  const route = findRouteByPath(path, routesConfig)
  return typeof route?.redirect === 'string' ? route.redirect : path
}

export class RouteHistoryManager {
  private routeHistory = ref<string[]>([])
  private currentHistoryIndex = ref(-1)
  private isNavigating = ref(false)  // 导航标志
  
  // 计算属性
  readonly canGoBack = computed(() => this.currentHistoryIndex.value > 0)
  readonly canGoForward = computed(() => this.currentHistoryIndex.value < this.routeHistory.value.length - 1)
  readonly history = computed(() => this.routeHistory.value)
  readonly currentIndex = computed(() => this.currentHistoryIndex.value)
  
  // 添加路由到历史记录
  addToHistory(path: string): void {
    // 获取最终目标路径
    const finalPath = getFinalPath(path)
    
    // 如果当前位置不是最后，则截断后面的历史记录
    if (this.currentHistoryIndex.value < this.routeHistory.value.length - 1) {
      this.routeHistory.value = this.routeHistory.value.slice(0, this.currentHistoryIndex.value + 1)
    }
    
    // 避免重复添加历史记录
    if (this.routeHistory.value.length > 0 && this.routeHistory.value[this.routeHistory.value.length - 1] === finalPath) {
      console.log('重定向到已存在的路径，不重复添加:', { from: path, to: finalPath })
      return
    }
    
    this.routeHistory.value.push(finalPath)
    this.currentHistoryIndex.value = this.routeHistory.value.length - 1
    
    // 限制历史记录长度
    if (this.routeHistory.value.length > MAX_HISTORY_SIZE) {
      this.routeHistory.value = this.routeHistory.value.slice(-MAX_HISTORY_SIZE)
      this.currentHistoryIndex.value = this.routeHistory.value.length - 1
    }
    
    console.log('路由历史更新:', {
      originalPath: path,
      finalPath: finalPath,
      history: this.routeHistory.value,
      currentIndex: this.currentHistoryIndex.value,
      canGoBack: this.canGoBack.value,
      canGoForward: this.canGoForward.value
    })
  }
  
  // 初始化路由历史
  initRouteHistory(path: string): void {
    const finalPath = getFinalPath(path)
    this.routeHistory.value = [finalPath]
    this.currentHistoryIndex.value = 0

    this._registerIPC()
    
    console.log('初始化路由历史:', {
      originalPath: path,
      finalPath: finalPath,
      history: this.routeHistory.value,
      currentIndex: this.currentHistoryIndex.value,
      canGoBack: this.canGoBack.value,
      canGoForward: this.canGoForward.value
    })
  }

  reset () {
    client.unRegisterFunctions([RouteHistoryManager_Channels.navigateToPath])
  }
  
  // 后退
  goBack(): string | null {
    if (this.canGoBack.value) {
      this.isNavigating.value = true
      this.currentHistoryIndex.value--
      const targetPath = this.routeHistory.value[this.currentHistoryIndex.value]
      console.log('执行后退:', {
        newIndex: this.currentHistoryIndex.value,
        targetPath
      })
      return targetPath
    } else {
      console.log('无法后退，历史记录为空或已在最前面')
      return null
    }
  }
  
  // 前进
  goForward(): string | null {
    if (this.canGoForward.value) {
      this.isNavigating.value = true
      this.currentHistoryIndex.value++
      const targetPath = this.routeHistory.value[this.currentHistoryIndex.value]
      console.log('执行前进:', {
        newIndex: this.currentHistoryIndex.value,
        targetPath
      })
      return targetPath
    } else {
      console.log('无法前进，已在历史记录最后')
      return null
    }
  }
  
  // 检查是否是重定向
  isRedirect(fromPath: string, toPath: string): boolean {
    return isRedirect(fromPath, toPath)
  }

  setIsNavigating(value: boolean) {
    this.isNavigating.value = value
  }
  
  // 检查是否正在导航（前进后退操作）
  isNavigatingHistory(): boolean {
    return this.isNavigating.value
  }
  
  // 重置导航标志
  resetNavigationFlag(): void {
    this.isNavigating.value = false
  }
  
  // 获取当前历史记录状态
  getStatus() {
    return {
      history: this.routeHistory.value,
      currentIndex: this.currentHistoryIndex.value,
      canGoBack: this.canGoBack.value,
      canGoForward: this.canGoForward.value
    }
  }

  navigateToPath (path: string, query: Record<string, string>) {
    console.log('navigateToPath:', path, query)
    return router.push({ path, query })
  }

  private _registerIPC () {
    client.registerFunctions({
      [RouteHistoryManager_Channels.navigateToPath]: (ctx: unknown, path: string, query: Record<string, string>) => {
        console.log('路由跳转:', path, query)
        return this.navigateToPath(path, query)
      },
    })
  }
}

// 单例实例
let instance: RouteHistoryManager | null = null

export function getRouteHistoryManager(): RouteHistoryManager {
  if (!instance) {
    instance = new RouteHistoryManager()
  }
  return instance
} 