#include <Windows.h>
#include <shlwapi.h>
#define NT_SUCCESS(Status) ((NTSTATUS)(Status) >= 0)
#define STATUS_INFO_LENGTH_MISMATCH ((NTSTATUS)0xC0000004L)
// typedef enum _SYSTEM_INFORMATION_CLASS {
// 	SystemBasicInformation,
// 	SystemProcessorInformation,
// 	SystemPerformanceInformation,
// 	SystemTimeOfDayInformation,
// 	SystemPathInformation,
// 	SystemProcessInformation,
// 	SystemCallCountInformation,
// 	SystemDeviceInformation,
// 	SystemProcessorPerformanceInformation,
// 	SystemFlagsInformation,
// 	SystemCallTimeInformation,
// 	SystemModuleInformation,
// 	SystemLocksInformation,
// 	SystemStackTraceInformation,
// 	SystemPagedPoolInformation,
// 	SystemNonPagedPoolInformation,
// 	SystemHandleInformation,
// 	SystemObjectInformation,
// 	SystemPageFileInformation,
// 	SystemVdmInstemulInformation,
// 	SystemVdmBopInformation,
// 	SystemFileCacheInformation,
// 	SystemPoolTagInformation,
// 	SystemInterruptInformation,
// 	SystemDpcBehaviorInformation,
// 	SystemFullMemoryInformation,
// 	SystemLoadGdiDriverInformation,
// 	SystemUnloadGdiDriverInformation,
// 	SystemTimeAdjustmentInformation,
// 	SystemSummaryMemoryInformation,
// 	SystemNextEventIdInformation,
// 	SystemEventIdsInformation,
// 	SystemCrashDumpInformation,
// 	SystemExceptionInformation,
// 	SystemCrashDumpStateInformation,
// 	SystemKernelDebuggerInformation,
// 	SystemContextSwitchInformation,
// 	SystemRegistryQuotaInformation,
// 	SystemExtendServiceTableInformation,
// 	SystemPrioritySeperation,
// 	SystemPlugPlayBusInformation,
// 	SystemDockInformation,
// 	SystemPowerInformation,
// 	SystemProcessorSpeedInformation,
// 	SystemCurrentTimeZoneInformation,
// 	SystemLookasideInformation
// } SYSTEM_INFORMATION_CLASS, *PSYSTEM_INFORMATION_CLASS;

typedef struct _IO_STATUS_BLOCK {
	LONG Status;
	LONG Information;
} IO_STATUS_BLOCK, *PIO_STATUS_BLOCK;

typedef struct _FILE_NAME_INFORMATION {
	ULONG FileNameLength;
	WCHAR FileName[MAX_PATH];
} FILE_NAME_INFORMATION;

// __declspec(dllimport) LONG __stdcall ZwQueryInformationFile (
// 	IN HANDLE FileHandle,
// 	OUT PIO_STATUS_BLOCK IoStatusBlock,
// 	OUT PVOID FileInformation,
// 	IN ULONG FileInformationLength,
// 	IN ULONG FileInformationClass
// 	);

typedef LONG (__stdcall * PFN_ZwQueryInformationFile) (
	IN HANDLE FileHandle,
	OUT PIO_STATUS_BLOCK IoStatusBlock,
	OUT PVOID FileInformation,
	IN ULONG FileInformationLength,
	IN ULONG FileInformationClass
	);

typedef NTSTATUS (WINAPI *PFN_NtQuerySystemInformation) (
	_In_       ULONG SystemInformationClass,
	_Inout_    PVOID SystemInformation,
	_In_       ULONG SystemInformationLength,
	_Out_opt_  PULONG ReturnLength
	);

typedef struct _NM_INFO
{
	HANDLE   hFile;
	FILE_NAME_INFORMATION Info;
	LONG	Status;
} NM_INFO, *PNM_INFO; 


/* The following structure is actually called SYSTEM_HANDLE_TABLE_ENTRY_INFO, but SYSTEM_HANDLE is shorter. */
typedef struct _SYSTEM_HANDLE
{
	ULONG ProcessId;
	BYTE ObjectTypeNumber;
	BYTE Flags;
	USHORT Handle;
	PVOID Object;
	ACCESS_MASK GrantedAccess;
} SYSTEM_HANDLE, *PSYSTEM_HANDLE;

typedef struct _SYSTEM_HANDLE_INFORMATION
{
	ULONG HandleCount; /* Or NumberOfHandles if you prefer. */
	SYSTEM_HANDLE Handles[1];
} SYSTEM_HANDLE_INFORMATION, *PSYSTEM_HANDLE_INFORMATION;

extern BOOL UnLockFile(TCHAR* fileName);