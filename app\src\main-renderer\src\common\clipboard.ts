import * as path from 'path'
import { Logger } from '@root/common/logger';
import { config } from '@root/common/config/config';
import * as DownloadKernel from '@root/common/task/base';
import { BHOConfigNS } from '@root/common/config/server/bho-config';
import { ParseUrlFileNameNS } from '@root/common/task/client/parse-helper';
import { thunderAgent } from '@root/main-renderer/src/common/thunder-agent';
import { ThunderHelper, ClipboardTextType } from "@root/common/thunder-helper";
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper';

const logger = new Logger({ tag: 'ClipBoardNS' })

function isMonitorExt(ext: string, extendNames: string): boolean {
  let ret: boolean = false;
  do {
    if (!ext) {
      break;
    }
    if (!extendNames) {
      break;
    }

    if (!extendNames.endsWith(';')) {
      extendNames = `${extendNames};`;
    }

    if (extendNames.indexOf(`${ext};`) > -1) {
      ret = true;
      break;
    }
  } while (0);
  return ret;
}

function isThunderXProtocol(dataStr: string): boolean {
  let bre: boolean = false;
  do {
    if (dataStr === undefined || dataStr === null || dataStr === '') {
      break;
    }
    dataStr = dataStr.trim();
    const strProtocol: string = 'thunderx://';
    if (dataStr === undefined || dataStr === null || dataStr === '') {
      break;
    }
    const nPos: number = dataStr.indexOf(strProtocol, 0);
    if (nPos === 0) {
      bre = true;
    }
  } while (false);
  return bre;
}

function getThunderxUrl(dataStr: string): string {
  let strUrl: string = '';
  dataStr = dataStr.trim();
  const strProtocol: string = 'thunderx://';
  const strTemp: string = dataStr.substring(strProtocol.length);
  const nPos: number = strTemp.lastIndexOf('}');
  if (nPos > 0) {
    strUrl = strTemp.substring(0, nPos + 1);
  }
  return strUrl;
}

async function attachClipBoardCallback(type: ClipboardTextType, textList: string[], clipboardProcessName: string): Promise<void> {
  do {
    if (clipboardProcessName.toLowerCase() === 'thunder.exe') {
      break;
    }
    if (!textList?.length) {
      break;
    }
    const isWatchClipBoard: boolean = await config.getValue('Monitor', 'MonitorClipBoard', true) as boolean;
    if (!isWatchClipBoard) {
      break;
    }
    clipboardProcessName = clipboardProcessName ?? '';
    const clipBoardText: string = textList.join('');

    if (isThunderXProtocol(clipBoardText)) {
      const lastUrl: string = getThunderxUrl(clipBoardText);
      thunderAgent.createThunderxTask(lastUrl).catch();
      break;
    }

    const watchTraditionLink: number = await BHOConfigNS.getValue('Monitor', 'WatchTraditionLink', 1) as number;
    const watchBtLink: number = await BHOConfigNS.getValue('Monitor', 'ConfigWatch_Bt', 1) as number;
    const watchEmuleLink: number = await BHOConfigNS.getValue('EMuleGenericSettings', 'EMuleWatchLink', 1) as number;
    const watchMagnetLink: number = await BHOConfigNS.getValue('MagnetGenericSettings', 'MagnetWatchLink', 1) as number;
    if (watchTraditionLink === 0
      && watchBtLink === 0
      && watchEmuleLink === 0
      && watchMagnetLink === 0
    ) {
      break;
    }

    const taskData: ThunderNewTaskHelperNS.INewTaskData[] = [];
    let validMagnetTaskList: ParseUrlFileNameNS.IMagnetPreCreateTaskItem[] = [];
    const parseResult = await ParseUrlFileNameNS.getContentParseResult(textList.join('\n'), 'clipboard')
    if (parseResult?.shareTaskList?.length) {
      parseResult.shareTaskList.forEach((shareTask: ParseUrlFileNameNS.IShareTaskItem) => {
        // todo 打开分享页面
      });
    }

    const defaultExts: string = await BHOConfigNS.getDefaultExtendNames();
    const extendNames: string = await BHOConfigNS.getValue('Monitor', 'ExtendNames', defaultExts) as string;
    if (parseResult?.p2pTaskList?.length) {
      for (let p2pTask of parseResult.p2pTaskList) {
        if (p2pTask.type === DownloadKernel.TaskType.P2sp) {
          const fileName: string = (await ParseUrlFileNameNS.getNameFromUrl(p2pTask.url))?.toLowerCase();
          const ext: string = path.extname(fileName);
          if (watchTraditionLink !== 0 && isMonitorExt(ext, extendNames)) {
            logger.log('valid watchTraditionLink');
            const data = await ThunderNewTaskHelperNS.getNewTaskDataByUrl(p2pTask.url, 'clipboard', p2pTask.type, p2pTask.birdKey);
            taskData.push(data);
          } else if (watchBtLink !== 0 && fileName === '.torrent') {
            logger.log('valid watchBtLink');
            const data = await ThunderNewTaskHelperNS.getNewTaskDataByUrl(p2pTask.url, 'clipboard', p2pTask.type, p2pTask.birdKey);
            taskData.push(data);
          } else {
            logger.log('invalid ext:', fileName);
            continue;
          }
        } else if (p2pTask.type === DownloadKernel.TaskType.Emule) {
          if (watchEmuleLink === 0) {
            logger.log('taskType: Emule, watchBtLink === 0');
            continue;
          }
          const data = await ThunderNewTaskHelperNS.getNewTaskDataByUrl(p2pTask.url, 'clipboard', p2pTask.type, p2pTask.birdKey);
            taskData.push(data);
        }
      }
    }


    console.log('clipboard valid taskData', taskData)

    if (watchMagnetLink !== 0) {
      validMagnetTaskList = parseResult?.magnetTaskList ?? [];
    }

    if (taskData.length) {
      ThunderNewTaskHelperNS.showPreCreateTaskWindow(taskData);
    }

    if (validMagnetTaskList && validMagnetTaskList.length > 0) {
      console.log('clipboard validMagnetTaskList', validMagnetTaskList)
      ThunderNewTaskHelperNS.showPreCreateTaskWindow(validMagnetTaskList);
    }
  } while (0);
}

export function initClipboard(): void {
  ThunderHelper.initClipboard();
  // 初始化命令行、联盟、Wfp、ThunderAgent等回调处理
  thunderAgent.init();

  setTimeout(() => {
    ThunderHelper.attachClipboard(attachClipBoardCallback);
  }, 1000);
}