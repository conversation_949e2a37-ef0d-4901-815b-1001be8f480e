import { CallApiProxyImplWithIpcClient } from '../call-api-impl';
import * as BaseType from '../base'
export class DkHelper {
    static apiProxy: CallApiProxyImplWithIpcClient | null = null;
    static init(apiProxy: CallApiProxyImplWithIpcClient) {
        DkHelper.apiProxy = apiProxy;
    }

    public static async isThunderPrivateUrl(url: string): Promise<boolean> {
        let info = await DkHelper.apiProxy!.CallApi('TaskManagerDkHelperIsThunderPrivateUrl', url);
        if (info.bSucc) {
            return info.result as boolean;
        }

        return false;
    }

    public static async parseThunderPrivateUrl(url: string): Promise<string> {
        let info = await DkHelper.apiProxy!.CallApi('TaskManagerDkHelperParseThunderPrivateUrl', url);
        if (info.bSucc) {
            return info.result as string;
        }

        return '';
    }

    public static async parserEd2kLink(url: string): Promise<BaseType.Ed2kLinkParseResult> {
        let info = await DkHelper.apiProxy!.CallApi('TaskManagerDkHelperParserEd2kLink', url);
        if (info.bSucc) {
            return info.result as BaseType.Ed2kLinkParseResult;
        }

        return undefined as any;
    }

    public static async parseMagnetUrl(url: string): Promise<BaseType.MagnetParseResult> {
        let info = await DkHelper.apiProxy!.CallApi('TaskManagerDkHelperParseMagnetUrl', url);
        if (info.bSucc) {
            return info.result as BaseType.MagnetParseResult;
        }

        return undefined as any;
    }

    public static async parseP2spUrl(url: string): Promise<BaseType.P2spUrlParseResult> {
        let info = await DkHelper.apiProxy!.CallApi('TaskManagerDkHelperParseP2spUrl', url);
        if (info.bSucc) {
            return info.result as BaseType.P2spUrlParseResult;
        }

        return undefined as any;
    }

    public static async parseFileNameFromP2spUrlPath(urlPath: string): Promise<string> {
        let info = await DkHelper.apiProxy!.CallApi('TaskManagerDkHelperParseFileNameFromP2spUrlPath', urlPath);
        if (info.bSucc) {
            return info.result as string;
        }

        return '';
    }

    public static async getTaskTypeFromUrl(url: string): Promise<BaseType.TaskType> {
        let info = await DkHelper.apiProxy!.CallApi('TaskManagerDkHelperGetTaskTypeFromUrl', url);
        if (info.bSucc) {
            return info.result as BaseType.TaskType;
        }

        return BaseType.TaskType.Unkown;
    }

    public static async parseBtTaskInfo(filePath: string): Promise<BaseType.BtTaskInfo> {
        let info = await DkHelper.apiProxy!.CallApi('TaskManagerDkHelperParseBtTaskInfo', filePath);
        if (info.bSucc) {
            return info.result as BaseType.BtTaskInfo;
        }

        return undefined as any;
    }

    public static async proxyVerify(host: string,
        port: number,
        userName: string,
        passWord: string,
        proxyType: BaseType.ProxyType): Promise<BaseType.ProxyVerifyResult> {
        let info = await DkHelper.apiProxy!.CallApi('TaskManagerDkHelperProxyVerify', host, port, userName, passWord, proxyType);
        if (info.bSucc) {
            return info.result as BaseType.ProxyVerifyResult;
        }

        return BaseType.ProxyVerifyResult.Fall;
    }
}