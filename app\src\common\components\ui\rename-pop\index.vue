<script setup lang="ts">
import Dialog from '@root/common/components/ui/Dialog/Dialog.vue'
import { ref, onMounted, nextTick, watch } from 'vue'
const props = defineProps<{
  open: boolean
  name: string
}>()

const text = ref('')
const textareaRef = ref<HTMLTextAreaElement | null>(null)
const errorInfo = ref('')

// 定义事件
const emit = defineEmits(['confirm', 'cancel', 'close'])

const handleConfirm = () => {
  if (errorInfo.value || !text.value) {
    return
  }
  emit('confirm', text.value)
}

const handleCancel = () => {
  emit('cancel')
}

const handleClose = () => {
  emit('close')
}

// 检查文件名是否包含特殊字符
const hasInvalidChars = (fileName: string) => {
  const invalidChars = /[\\/:*?"<>|]/
  return invalidChars.test(fileName)
}

watch(
  () => props.name,
  (newVal) => {
    console.log('>>>>>>>>>>>>>> newVal', newVal)
    text.value = newVal
  },
  { immediate: true }
);

// 监听文本变化
watch(text, (newValue) => {
  if (hasInvalidChars(newValue)) {
    errorInfo.value = '名称不能包含以下字符: \ / : * ? " <> |'
  } else if (newValue.length > 255) {
    errorInfo.value = '名称最多允许255个字符'
  } else {
    errorInfo.value = ''
  }
})

// 获取文件名（不包含扩展名）
const getFileNameWithoutExtension = (fileName: string) => {
  const lastDotIndex = fileName.lastIndexOf('.')
  return lastDotIndex === -1 ? fileName : fileName.slice(0, lastDotIndex)
}


onMounted(() => {
  nextTick(() => {
    if (textareaRef.value) {
      const fileName = getFileNameWithoutExtension(props.name)
      setTimeout(() => {
        console.log('>>>>>>>>>>>>> 执行', fileName)
        textareaRef.value?.focus()
        textareaRef.value?.setRangeText(fileName, 0, fileName.length, "select")
      }, 50)
    }
  })
})

</script>

<template>
  <Dialog :open="props.open" title="重命名" preventDefaultClose @confirm="handleConfirm" @cancel="handleCancel"
    @close="handleClose">
    <div class="rename-content">
      <textarea v-model="text" ref="textareaRef" tabindex="0"/>

      <div v-if="errorInfo" class="rename-content-error">
        {{ errorInfo }}
      </div>
    </div>

    <!-- Custom confirm button content -->
    <template #confirm-content>
      确定
    </template>
  </Dialog>
</template>

<style scoped lang="scss">
.rename-content {
  width: 100%;
  height: 82px;

  border-radius: var(--border-radius-M, 8px);
  border: 1px solid var(--border-border-primary, #226DF5);
  padding: 8px 0;


  textarea {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    resize: none;
    font-size: 13px;
    line-height: 22px;
    padding: 0 12px;
    color: var(--font-font-1, #272E3B);
    border-radius: var(--border-radius-M, 8px);

    &::selection {
      background-color: var(--primary-primary-default, #226DF5);
      color: #fff;
    }
  }

  &-error {
    font-size: 13px;
    color: var(----functional-warning-default, #FAAD14);
    line-height: 22px;
    margin-top: 10px;
  }
}
</style>
