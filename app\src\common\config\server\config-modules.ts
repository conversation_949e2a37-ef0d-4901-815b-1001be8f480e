// 获取https://static-xl.a.88cdn.com/json/modules.json 配置模块信息
import path from 'node:path';
import { EventEmitter } from 'events';
import { Logger } from '@root/common/logger';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { ConfigInitState } from '@root/common/config/types';
import { GetProfilesPath } from '@root/common/xxx-node-path';

const logger = new Logger({ tag: 'config-modules' })
// process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

class ConfigModules {
  private configModulesJsonData: any = null;
  private eventEmitter: EventEmitter = new EventEmitter();
  private initState_: ConfigInitState = ConfigInitState.None;
  private cachePath: string = path.join(GetProfilesPath(), 'modules.json');

  constructor() {
    //
  }

  private async cache(data: any): Promise<void> {
    await FileSystemAWNS.writeFileAW(this.cachePath, JSON.stringify(data));
  }

  private async loadLatest(): Promise<void> {
    do {
      const exist: boolean = await FileSystemAWNS.existsAW(this.cachePath);
      if (!exist) {
        break;
      }

      const dataStr: string = (await FileSystemAWNS.readFileAW(this.cachePath)).toString();
      try {
        this.configModulesJsonData = JSON.parse(dataStr);
      } catch (error) {
        //
      }
    } while (0);
  }

  private async update(): Promise<boolean> {
    let response: Response | null = null;
    let ret: boolean = false;
    try {
      response = await fetch('https://static-xl.a.88cdn.com/json/modules.json'); 
    } catch (error) {
      //
    }

    if (response?.ok && response.status === 200) {
      this.configModulesJsonData = await response.json();
      this.cache(this.configModulesJsonData).catch();
      ret = true;
    }
    return ret;
  }

  protected async init(): Promise<void> {
    do {
      if (this.initState_ === ConfigInitState.Done) {
        break;
      }

      if (this.initState_ === ConfigInitState.Ing) {
        return new Promise<void>(
          (resolve: (value: void) => void): void => {
            this.eventEmitter.once('OnConfigModulesLoaded', (loaded: boolean): void => {
              resolve();
            });
          }
        );
      }

      this.initState_ = ConfigInitState.Ing;
      const ret: boolean = await this.update();
      if (!ret) {
        logger.log('module_json_download_fail load cache');
        await this.loadLatest().catch();
      }
      // 一小时更新一次配置
      setInterval(async (): Promise<void> => {
        this.update().catch();
      }, 3600 * 1000);

      this.initState_ = ConfigInitState.Done;
      this.eventEmitter.emit('OnConfigModulesLoaded', ret);
    } while (0);
  }

  // groupName 为配置项中的每项key值，keyName为对应项中的某个子项
  public async getConfigInfo(groupName?: string, keyName?: string): Promise<any> {
    await this.init();
    let data: any = null;
    do {
      if (this.configModulesJsonData === null || this.configModulesJsonData === undefined) {
        break;
      }
      if (groupName === null || groupName === undefined || groupName === '') {
        // 直接返回全部数据
        data = this.configModulesJsonData;
      } else {
        if (keyName === null || keyName === undefined || keyName === '') {
          if (this.configModulesJsonData[groupName] !== null && this.configModulesJsonData[groupName] !== undefined) {
            data = this.configModulesJsonData[groupName];
          }
        } else {
          if (
            this.configModulesJsonData[groupName] !== null &&
            this.configModulesJsonData[groupName] !== undefined &&
            this.configModulesJsonData[groupName][keyName] !== null &&
            this.configModulesJsonData[groupName][keyName] !== undefined
          ) {
            data = this.configModulesJsonData[groupName][keyName];
          }
        }
      }
    } while (false);
    return data;
  }
}

const configModules: ConfigModules = new ConfigModules();
export default configModules;
