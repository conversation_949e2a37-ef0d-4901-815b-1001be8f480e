/* 7zMem.c -- MemStream
2013-06-27 : <PERSON> : Public domain */


#include "7zMem.h"


/*-------------MemStruct-------------*/

WRes Mem_Init(CSzMem *p, const void* pdata, UINT64 size)
{
	p->pstart = (BYTE*)(pdata);
	p->pcurrent = (BYTE*)(pdata);
	p->size = size;
	return SZ_OK;
}

WRes Mem_Read(CSzMem *p,void *data, size_t *size)
{
	memcpy(data,p->pcurrent,*size);
	p->pcurrent = p->pcurrent + *size;
	return SZ_OK;
}

WRes Mem_Seek(CSzMem *p, Int64 *pos, ESzSeek origin)
{
	switch(origin)
	{
	case SZ_SEEK_SET:
		p->pcurrent = p->pstart + *pos;
		break;
	case SZ_SEEK_CUR:
		p->pcurrent = p->pcurrent + *pos;
		break;
	case SZ_SEEK_END:
		p->pcurrent = p->pstart + p->size - *pos;
		break;
	}
	*pos = p->pcurrent - p->pstart;
	return  SZ_OK;
}

/*-------------MemInStream-------------*/
static SRes MemInStream_Read(void* pp, void* buf, size_t *size)
{
	CMemInStream* p = (CMemInStream*)pp;
	return Mem_Read(&p->m,buf,size);
}

static SRes MemInStream_Seek(void *pp, Int64 *pos, ESzSeek origin)
{
	CMemInStream* p = (CMemInStream*)pp;
	return Mem_Seek(&p->m,pos,origin);
}


void MemInStream_CreateVTable(CMemInStream *p)
{
	p->s.Read = MemInStream_Read;
	p->s.Seek = MemInStream_Seek;
}

SRes MemInStream_Init(CMemInStream *p, const void* pdata, UINT64 size)
{
	Mem_Init(&p->m,pdata,size);
	MemInStream_CreateVTable(p);
	return 0;
}

/*--------------ResInStream---------------*/
SRes ResInStream_Init(CResInStream *p, WORD resid, TCHAR* restype)
{
	HRSRC res;
	HGLOBAL data;
	void *pdata;
	UINT64 size = 0;

	p->lock = 0;

	res = FindResource( NULL, MAKEINTRESOURCE( resid ), restype );
	if( !res )
	{
		return 1;
	}

	data = LoadResource( NULL, res );
	if( !data )
	{
		return 1;
	}

	size = SizeofResource( NULL, res );

	pdata = LockResource( data );
	if( !pdata )
	{
		return 1;
	}

	p->lock = 1;
	p->data = data;
	p->res = res;

	Mem_Init(&(p->m.m), pdata, size);

	MemInStream_CreateVTable(&(p->m));

	return 0;
}

void ResInStream_Uninit(CResInStream *p)
{
	if (p->lock)
	{
		UnlockResource( p->data );
		FreeResource( p->res );
	}
}
