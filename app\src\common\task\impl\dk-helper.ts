import type * as BaseType from '../base'
import * as path from 'path'
import requireNodeFile from '@root/common/require-node-file';
import {GetXxxNodePath, GetDkAddonNodeName} from '@root/common/xxx-node-path'

const dk: any = requireNodeFile(path.join(GetXxxNodePath(), GetDkAddonNodeName()));
export class DkHelper {
  public static isThunderPrivateUrl(url: string): boolean {
    return dk.NativeDkHelper.isThunderPrivateUrl(url) === 1;
  }

  public static parseThunderPrivateUrl(url: string): string {
    return dk.NativeDkHelper.parseThunderPrivateUrl(url);
  }

  public static parserEd2kLink(url: string): BaseType.Ed2kLinkParseResult {
    return dk.NativeDkHelper.parserEd2kLink(url);
  }

  public static parseMagnetUrl(url: string): BaseType.MagnetParseResult {
    return dk.NativeDkHelper.parseMagnetUrl(url);
  }

  public static parseP2spUrl(url: string): BaseType.P2spUrlParseResult {
    return dk.NativeDkHelper.parseP2spUrl(url);
  }

  public static parseFileNameFromP2spUrlPath(urlPath: string): string {
    return dk.NativeDkHelper.parseFileNameFromP2spUrlPath(urlPath);
  }

  public static getTaskTypeFromUrl(url: string): BaseType.TaskType {
    return dk.NativeDkHelper.getTaskTypeFromUrl(url);
  }

  public static parseBtTaskInfo(filePath: string): BaseType.BtTaskInfo {
    return dk.NativeDkHelper.parseBtTaskInfo(filePath);
  }

  public static proxyVerify(host: string,
    port: number,
    userName: string,
    passWord: string,
    proxyType: BaseType.ProxyType): BaseType.ProxyVerifyResult {
    return dk.NativeDkHelper.proxyVerify(host, port, userName, passWord, proxyType);
  }
}