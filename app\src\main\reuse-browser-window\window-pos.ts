import { BrowserWindow, Point, Display, screen } from "electron";

export namespace WindowPosNS {

  /**
   * @description
   * @param selfWindow 
   * @param parentWindow 
   * 注意：设备无关像素（DIP： Device-Independent Pixel）坐标转换为屏幕物理像素: 在缩放为 200% 的屏幕上，100 DIP 对应 200 物理像素。
   * electron中getBounds、getSize、getPosition等接口一般返回设备无关相关(DIP)，可通过screen.dipToScreenPoint 转换为屏幕物理像素坐标,
   * electron中screen.getCursorScreenPoint() 函数名称中ScreenPoint已经表明：获取光标的物理像素坐标。
   * 而NAPI导出的Windows API中无特殊处理一般为屏幕物理像素坐标
   * 因此不建议NAPI和electron的接口混用，转来转去容易出问题
   */
  export function centerWnd(selfWindow: BrowserWindow, parent: BrowserWindow, size?: number[]): void {
    do {
      if (!selfWindow || !parent) {
        break;
      }

      if (!size?.[0] || !size?.[1]) {
        const selfSize: number[] = selfWindow.getSize();
        size = size ?? [];
        size[0] = size[0] ?? selfSize[0];
        size[1] = size[1] ?? selfSize[1];
      }   

      const leftTop = getRelateCenterPos(parent, size);
      const resizable = selfWindow.isResizable();
      if (!resizable) {
        selfWindow.setResizable(true);
      }
      selfWindow.setBounds({ x: leftTop.x, y: leftTop.y, width: Math.round(size[0]), height: Math.round(size[1]) });
      if (!resizable) {
        selfWindow.setResizable(false);
      }
    } while (0);
  }

  export function getRelateCenterPos(parent: BrowserWindow, selfSize: number[]): Point {
    let x: number = 0;
    let y: number = 0;
    do {
      if (!parent) {
        break;
      }

      const parentRect = parent.getBounds();
      const pos = parent.getPosition();

      // 二级面板显示在父窗口左上角所在的屏幕
      const p: Point = { x: pos[0], y: pos[1] };
      const display: Display = screen.getDisplayNearestPoint(p);

      // display返回的workArea、 workAreaSize 返回的是 物理像素 （比如 显示器分辨率3840*2160，缩放200%），workAreaSize返回 1920 * 1080
      // 此处无需*dpi，不理解...
      const screenWidth = display.workArea.width;
      const screenHeight = display.workArea.height;
      // 屏幕左上角
      const p1 = { x: display.workArea.x, y: display.workArea.y };

      // 父窗口左边超出了屏幕，那么只用右边部分进行居中
      if (parentRect.x < p1.x) {
        parentRect.width = parentRect.width - (p1.x - parentRect.x);
        parentRect.x = p1.x;
      }
      if (parentRect.y < p1.y) {
        parentRect.height = parentRect.height - (p1.y - parentRect.y);
        parentRect.y = p1.y;
      }

      // 计算父窗口在屏幕内的部分可显示的区域
      let visibleWidth: number = parentRect.width;
      let visibleHeight: number = parentRect.height;
      if (parentRect.x + visibleWidth > p1.x + screenWidth) {
        visibleWidth = p1.x + screenWidth - parentRect.x;
      }
      if (parentRect.y + visibleHeight > p1.y + screenHeight) {
        visibleHeight = p1.y + screenHeight - parentRect.y;
      }

      // 二级面板的预想位置
      x = parentRect.x + (visibleWidth - selfSize[0]) / 2;
      y = parentRect.y + (visibleHeight - selfSize[1]) / 2;

      // 矫正位置，二级面板不能超出屏幕,优先保证面板的左上角在屏幕内
      if (x < p1.x) {
        x = p1.x;
      } else if (x > p1.x + screenWidth - selfSize[0]) {
        x = p1.x + screenWidth - selfSize[0];
      }
      if (y < p1.y) {
        y = p1.y;
      } else if (y > p1.y + screenHeight - selfSize[1]) {
        y = p1.y + screenHeight - selfSize[1];
      }
    } while (0);
    // 注意返回浮点数，调用setBounds等会直接报错
    return { x: Math.round(x), y: Math.round(y) };
  }

  export function getRelateRightBottomPos(selfSize: number[]): Point {
    const display = screen.getPrimaryDisplay();
    const workAreaSize: Electron.Size = display.workAreaSize;
    // 此处workAreaSize 不需要*dpi，自测过
    const x: number = Math.round(workAreaSize.width - selfSize[0]);
    const y: number = Math.round(workAreaSize.height - selfSize[1]);
    // 注意返回浮点数，调用setBounds等会直接报错
    return { x: Math.round(x), y: Math.round(y) };
  }
}