#pragma once

#include "liveupdate.h"
#include <string>
#include <Windows.h>
#include <assert.h>
#include <process.h>
#include "xl_lib/xml/xml_dom_parser.h"
//#include "LiveUpdateLuaHost.h"

#include "SyncHttpsClient.h"

using namespace xl::xml;

class UpdateInfoChecker
{
public:
    UpdateInfoChecker(void);
    ~UpdateInfoChecker(void);

    bool CheckUpdateInfo(const char* ProductName, const char* ProductVer, int productID, const UPDATE_TYPE UpdateType, PACK_TYPE& PackType, bool& bNeedUAC, int nServerVersion);
	bool CheckLocalUpdateInfo(const char* ProductName, const char* ProductVer, int productID, PACK_TYPE& PackType, bool& bNeedUAC);

	void SetChannelID(const std::string& szChannelID);
	void SaveInstalledPatchInfo();

	void DeleteLocalUpdateInfoFile();

    void CleanUp();
private:
	bool CheckLocalUpdateInfoInternal(const std::wstring& wsLocalInfoFilePath, PACK_TYPE& PackType);

	bool ReadLocalUpdateInfoFile(const std::wstring& wsLocalInfoFilePath, std::string& strJson);
	bool WriteLocalUpdateInfoFileInternal(const std::wstring& wsLocalInfoFilePath, const std::string& strJson);

	bool GetLocalUpdateInfoFilePath(std::wstring& wsLocalInfoFilePath);

	std::string GetRequestUrl(bool bUseHttps);
	bool ParseReplyJson(const char* szJson, PACK_TYPE& PackType);
	void GetKeysWithArch(std::string& url_key, std::string& md5_key);

    std::string m_szProductName;
    std::string m_szProductVer;
    UPDATE_TYPE m_updateType;

	std::string m_szChannelID;
	int m_productID;

	CSyncHttpsClient m_syncHttpsClient;

	PACK_TYPE m_PackType;
	string m_strNewVersion;
	string m_strMD5;
};
