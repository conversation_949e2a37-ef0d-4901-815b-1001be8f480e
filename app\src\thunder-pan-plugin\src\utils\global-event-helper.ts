import { closeAllContextmenu } from "@root/common/components/ui/contextmenu";
import EventEmitter from "events"

export interface IGlobalRefreshEventOptions {
  silent?: boolean
}

export class GlobalEventHelper extends EventEmitter{
  private static _instance: GlobalEventHelper;

  static getInstance () {
    if (GlobalEventHelper._instance) return GlobalEventHelper._instance;
    GlobalEventHelper._instance = new GlobalEventHelper();
    return GlobalEventHelper._instance;
  }

  static EventKey = {
    // 框选结束事件
    DRAG_SELECT_END: 'drag_select_end',
    // 云盘全部列表
    DRIVE_LIST_REFRESH: 'drive_list_refresh',
    DRIVE_LIST_CLEAN_PICKED: 'drive_list_clean_picked',
    DRIVE_LIST_PICK_OR_HIGHLIGHT_FILE: 'drive_list_pick_or_highlight_file',
    // 云添加列表
    CLOUD_ADD_LIST_REFRESH: 'cloud_add_list_refresh',
    CLOUD_ADD_LIST_CLEAN_PICKED: 'cloud_add_list_clean_picked',
    CLOUD_ADD_TASK_ADD: 'cloud_add_task_add',
    CLOUD_ADD_TASK_FAIL: 'cloud_add_task_fail',
    CLOUD_ADD_TASK_SUCCESS: 'cloud_add_task_success',
    // 转存列表
    TRANSFER_LIST_REFRESH: 'transfer_list_refresh',
    TRANSFER_LIST_CLEAN_PICKED: 'transfer_list_clean_picked',
    // 我的分享列表
    SHARE_LIST_REFRESH: 'share_list_refresh',
    SHARE_LIST_CLEAN_PICKED: 'share_list_clean_picked',
    // mqtt 复制文件推送
    MESSAGE_CENTER_RECV_COPY: 'MessageCenterManager_recv_copy',
    // mqtt 移动文件推送
    MESSAGE_CENTER_RECV_MOVE: 'MessageCenterManager_recv_move',
    // mqtt 文件放入回收站推送
    MESSAGE_CENTER_RECV_TRASH: 'MessageCenterManager_recv_trash',
    // mqtt 回收站还原文件推送
    MESSAGE_CENTER_RECV_UNTRASH: 'MessageCenterManager_recv_untrash',
    // mqtt 压缩包文件解压完成推送
    MESSAGE_CENTER_RECV_DECOMPRESS: 'MessageCenterManager_recv_decompress',
    // mqtt 文件上传完成推送
    MESSAGE_CENTER_RECV_UPLOADED: 'MessageCenterManager_recv_uploaded',
    // 全局按键事件：回车
    KEYBOARD_ENTER: 'keyboard_enter',
    // 全局按键事件：方向键 上、下
    KEYBOARD_ARROW_BROWSER: 'keyboard_arrow_browser',
    // 全局按键事件：Alt + 方向键 左、右
    KEYBOARD_ARROW_NAVIGATE: 'keyboard_arrow_navigate',
    // 全局按键事件：Ctrl + F
    KEYBOARD_SEARCH: 'keyboard_search',
  }

  init () {
    // 全局监听按键事件
    document.addEventListener('keydown', (event) => {
      try {
        const condition = {
          search: event.key === 'f' && event.ctrlKey,
          browser: ['ArrowUp', 'ArrowDown'].includes(event.key),
          navigate: ['ArrowLeft', 'ArrowRight'].includes(event.key) && event.altKey,
          enter: event.key === 'Enter',
          esc: event.key === 'Escape'
        }
        if (condition.search) {
          this.emit(GlobalEventHelper.EventKey.KEYBOARD_SEARCH)
        }
        if (condition.browser) {
          this.emit(GlobalEventHelper.EventKey.KEYBOARD_ARROW_BROWSER, event.key)
        }
        if (condition.navigate) {
          this.emit(GlobalEventHelper.EventKey.KEYBOARD_ARROW_NAVIGATE, event.key)
        }
        if (condition.enter) {
          this.emit(GlobalEventHelper.EventKey.KEYBOARD_ENTER)
        }
        if (condition.esc) {
          this.emit(GlobalEventHelper.EventKey.DRIVE_LIST_CLEAN_PICKED)
          this.emit(GlobalEventHelper.EventKey.CLOUD_ADD_LIST_CLEAN_PICKED)
          this.emit(GlobalEventHelper.EventKey.TRANSFER_LIST_CLEAN_PICKED)
          this.emit(GlobalEventHelper.EventKey.SHARE_LIST_CLEAN_PICKED)
          closeAllContextmenu()
        }
      } catch (error) {
      }
    })
  }

  /**
   * 一键移除所有事件监听
   */
  removeAll () {
    // 需要忽略移除的事件列表
    const ignoreEventKey = [
      GlobalEventHelper.EventKey.SHARE_LIST_REFRESH,  // 【我的分享】页面不会被移除，这里不需要移除该事件监听
    ]

    Reflect.ownKeys(GlobalEventHelper.EventKey).forEach(key => {
      const eventKey = Reflect.get(GlobalEventHelper.EventKey, key)

      if (!ignoreEventKey.includes(eventKey)) {
        this.removeAllListeners(eventKey)
      }
    })
  }
}
