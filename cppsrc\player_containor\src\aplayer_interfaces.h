#ifndef __APLAYER_INTERFACES_H__
#define __APLAYER_INTERFACES_H__

#include <Windows.h>
#include <atlbase.h>
#include "./XLIPC/xlipc_define.h"
#include "./XLIPC/xlipc_connect_session.h"

static inline long ReadStreamStringW(XLIPCStream* stream, wchar_t** s, size_t* len)
{
	long ret = stream->ReadUnicode(NULL, 0, len);
	if (ret != XLIPC_RESULT_SUCCESS)
	{
		return ret;
	}
	*s = new wchar_t[*len + 1];
	if (*s == NULL)
	{
		return XLIPC_RESULT_OUT_OF_MEMORY;
	}
	return stream->ReadUnicode(*s, *len, len);
}

static inline long ReadStreamStringA(XLIPCStream* stream, char** s, size_t* len)
{
	long ret = stream->ReadUtf8(NULL, 0, len);
	if (ret != XLIPC_RESULT_SUCCESS)
	{
		return ret;
	}
	*s = new char[*len + 1];
	if (*s == NULL)
	{
		return XLIPC_RESULT_OUT_OF_MEMORY;
	}
	return stream->ReadUtf8(*s, *len, len);
}
struct AplayerParam {
	HWND hParent;
	HWND hFloat;
	bool bOpenLog;
	bool bIsAudioNormalize;
	std::string strLogDir;
	std::string strCachePath;
	std::string strSdkPath;
};

class APlayerInterface
{
public:
	APlayerInterface()
		: delegate_(NULL)
	{
	}
	~APlayerInterface()
	{
		delegate_ = NULL;
	}

	class Delegate
	{
	public:
		virtual bool CreateAPlayer(const AplayerParam& param, HWND* pWnd) = 0;
		virtual long Open(const char* szUrl) = 0;
		virtual long Close() = 0;
		virtual long Play() = 0;
		virtual long Pause() = 0;
		virtual long GetVersion(std::string& strVersion) = 0;
		virtual long SetCustomLogo(long logo) = 0;
		virtual long GetState(long* state) = 0;
		virtual long GetDuration(long* duration) = 0;
		virtual long GetPosition(long* position) = 0;
		virtual long SetPosition(long position, long* result) = 0;
		virtual long GetVideoWidth(long* video_width) = 0;
		virtual long GetVideoHeight(long* video_height) = 0;
		virtual long GetVolume(long* volume) = 0;
		virtual long SetVolume(long volume, long* result) = 0;
		virtual long IsSeeking(long* seeking) = 0;
		virtual long GetBufferProgress(long* buffer_progress) = 0;
		virtual long GetConfig(const char* szConfigId, std::string& strValue) = 0;
		virtual long SetConfig(const char* szConfigId, const char* szValue, long* result) = 0;
		virtual void SetMainWnd(HWND main_wnd) = 0;
	};

protected:
#define GET_APLAYER_DELEGATE(ud) \
	Delegate* delegate = (Delegate*)ud; \
	if (delegate == NULL) \
	{ \
		break; \
	}
	static long __stdcall AsyncCreateAPlayerProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		bool result = false;
		HWND aplayer_wnd = 0;
		do 
		{
			GET_APLAYER_DELEGATE(ud);

			AplayerParam param;
			int64_t nWnd = 0;
			param_stream->Readint64(&nWnd);
			param.hParent = (HWND)nWnd;
			param_stream->Readint64(&nWnd);
			param.hFloat = (HWND)nWnd;
			param_stream->ReadBoolean(&param.bOpenLog);
			param_stream->ReadBoolean(&param.bIsAudioNormalize);
			char* szValue = nullptr;
			size_t nLen = 0;
			ReadStreamStringA(param_stream, &szValue, &nLen);
			if (szValue) {
				param.strLogDir = szValue;
				delete[] szValue;
				szValue = nullptr;
			}
			nLen = 0;

			ReadStreamStringA(param_stream, &szValue, &nLen);
			if (szValue) {
				param.strCachePath = szValue;
				delete[] szValue;
				szValue = nullptr;
			}
			nLen = 0;

			ReadStreamStringA(param_stream, &szValue, &nLen);
			if (szValue) {
				param.strSdkPath = szValue;
				delete[] szValue;
				szValue = nullptr;
			}
			nLen = 0;

			result = delegate->CreateAPlayer(param, &aplayer_wnd);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteBoolean(result);
			(*ret_stream)->WriteInt64((int64_t)aplayer_wnd);
		}
		return 0;
	}
	static long __stdcall AsyncOpenProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		do 
		{
			GET_APLAYER_DELEGATE(ud);
			char* url = NULL;
			size_t len = 0;
			long ret = ReadStreamStringA(param_stream, &url, &len);
			if (ret == XLIPC_RESULT_SUCCESS)
			{
				result = delegate->Open(url);
			}
			if (url)
			{
				delete[] url;
			}
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
		}
		return 0;
	}
	static long __stdcall AsyncCloseProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->Close();
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
		}
		return 0;
	}
	static long __stdcall AsyncPlayProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->Play();
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
		}
		return 0;
	}
	static long __stdcall AsyncPauseProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->Pause();
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
		}
		return 0;
	}
	static long __stdcall AsyncGetVersionProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		std::string strVersion;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->GetVersion(strVersion);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteUtf8(strVersion.c_str());
		}
		return 0;
	}
	static long __stdcall AsyncSetCustomLogoProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			long logo = 0;
			long ret = param_stream->ReadLong(&logo);
			if (ret == XLIPC_RESULT_SUCCESS)
			{
				result = delegate->SetCustomLogo(logo);
			}
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
		}
		return 0;
	}
	static long __stdcall AsyncGetStateProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long state = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->GetState(&state);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(state);
		}
		return 0;
	}
	static long __stdcall AsyncGetDurationProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long duration = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->GetDuration(&duration);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(duration);
		}
		return 0;
	}
	static long __stdcall AsyncGetPositionProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long position = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->GetPosition(&position);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(position);
		}
		return 0;
	}
	static long __stdcall AsyncSetPositionProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long setposition = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			long position = 0;
			long ret = param_stream->ReadLong(&position);
			if (ret == XLIPC_RESULT_SUCCESS)
			{
				result = delegate->SetPosition(position, &setposition);
			}
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(setposition);
		}
		return 0;
	}
	static long __stdcall AsyncGetVideoWidthProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long video_width = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->GetVideoWidth(&video_width);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(video_width);
		}
		return 0;
	}
	static long __stdcall AsyncGetVideoHeightProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long video_height = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->GetVideoHeight(&video_height);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(video_height);
		}
		return 0;
	}
	static long __stdcall AsyncGetVolumeProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long volume = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->GetVolume(&volume);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(volume);
		}
		return 0;
	}
	static long __stdcall AsyncSetVolumeProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long setvolume = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			long volume = 0;
			long ret = param_stream->ReadLong(&volume);
			if (ret == XLIPC_RESULT_SUCCESS)
			{
				result = delegate->SetVolume(volume, &setvolume);
			}
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(setvolume);
		}
		return 0;
	}
	static long __stdcall AsyncIsSeekingProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long seeking = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->IsSeeking(&seeking);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(seeking);
		}
		return 0;
	}
	static long __stdcall AsyncGetBufferProgressProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long buffer_progress = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			result = delegate->GetBufferProgress(&buffer_progress);
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(buffer_progress);
		}
		return 0;
	}
	static long __stdcall AsyncGetConfigProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		std::string strConfig;
		long ret = XLIPC_RESULT_SUCCESS;
		do
		{
			GET_APLAYER_DELEGATE(ud);
			char* config_id = NULL;
			size_t len = 0;
			long ret = ReadStreamStringA(param_stream, &config_id, &len);
			if (ret == XLIPC_RESULT_SUCCESS)
			{
				result = delegate->GetConfig(config_id, strConfig);
			}
			if (config_id)
			{
				delete[] config_id;
			}
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			if (!strConfig.empty())
			{
				(*ret_stream)->WriteUtf8(strConfig.c_str());
			}
			
		}
		return 0;
	}
	static long __stdcall AsyncSetConfigProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		long result = -1;
		long setconfig = -1;
		do
		{
			GET_APLAYER_DELEGATE(ud);

			char* config_id = NULL;
			size_t szConfigIdlen = 0;

			char* config = NULL;
			size_t len = 0;
			if (ReadStreamStringA(param_stream, &config_id, &szConfigIdlen) == XLIPC_RESULT_SUCCESS &&
				ReadStreamStringA(param_stream, &config, &len) == XLIPC_RESULT_SUCCESS)
			{
				result = delegate->SetConfig(config_id, config, &setconfig);
			}
			if (config_id) {
				delete[] config_id;
			}
			if (config)
			{
				delete[] config;
			}
		} while (false);
		*ret_stream = new XLIPCStream();
		if (*ret_stream)
		{
			(*ret_stream)->WriteLong(result);
			(*ret_stream)->WriteLong(setconfig);
		}
		return 0;
	}
	static long __stdcall AsyncSetChangeViewSizeProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		return 0;
	}
	static long __stdcall AsyncAddWindowMessageFilterProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		return 0;
	}
	static long __stdcall AsyncRemoveWindowMessageFilterProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		return 0;
	}
	static long __stdcall AsyncClearWindowMessageFilterProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		return 0;
	}
	static long __stdcall AsyncSetWindowResizeRectProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		return 0;
	}
	static long __stdcall AsyncSetWindowCaptionMoveProc(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
	{
		return 0;
	}
  static long __stdcall AsyncSetMainWnd(void* ud, XLIPCStream* param_stream, XLIPCStream** ret_stream)
  {
    do
    {
      GET_APLAYER_DELEGATE(ud);
      long main_wnd = 0;
      long ret = param_stream->ReadLong(&main_wnd);
      if (ret != XLIPC_RESULT_SUCCESS)
      {
        break;
      }
      delegate->SetMainWnd((HWND)main_wnd);
    } while (false);
    return 0;
  }

public:
	void SetDelegate(Delegate* delegate)
	{
		delegate_ = delegate;
	}
	void Register(XLIPCConnectSession* connect_session)
	{
		connect_session->RegisterInterface("CreateAPlayer", AsyncCreateAPlayerProc, delegate_);

		connect_session->RegisterInterface("Open", AsyncOpenProc, delegate_);
		connect_session->RegisterInterface("Close", AsyncCloseProc, delegate_);
		connect_session->RegisterInterface("Play", AsyncPlayProc, delegate_);
		connect_session->RegisterInterface("Pause", AsyncPauseProc, delegate_);
		connect_session->RegisterInterface("GetVersion", AsyncGetVersionProc, delegate_);
		connect_session->RegisterInterface("SetCustomLogo", AsyncSetCustomLogoProc, delegate_);
		connect_session->RegisterInterface("GetState", AsyncGetStateProc, delegate_);
		connect_session->RegisterInterface("GetDuration", AsyncGetDurationProc, delegate_);
		connect_session->RegisterInterface("GetPosition", AsyncGetPositionProc, delegate_);
		connect_session->RegisterInterface("SetPosition", AsyncSetPositionProc, delegate_);
		connect_session->RegisterInterface("GetVideoWidth", AsyncGetVideoWidthProc, delegate_);
		connect_session->RegisterInterface("GetVideoHeight", AsyncGetVideoHeightProc, delegate_);
		connect_session->RegisterInterface("GetVolume", AsyncGetVolumeProc, delegate_);
		connect_session->RegisterInterface("SetVolume", AsyncSetVolumeProc, delegate_);
		connect_session->RegisterInterface("IsSeeking", AsyncIsSeekingProc, delegate_);
		connect_session->RegisterInterface("GetBufferProgress", AsyncGetBufferProgressProc, delegate_);
		connect_session->RegisterInterface("GetConfig", AsyncGetConfigProc, delegate_);
		connect_session->RegisterInterface("SetConfig", AsyncSetConfigProc, delegate_);

		connect_session->RegisterInterface("SetChangeViewSize", AsyncSetChangeViewSizeProc, delegate_);
		connect_session->RegisterInterface("AddWindowMessageFilter", AsyncAddWindowMessageFilterProc, delegate_);
		connect_session->RegisterInterface("RemoveWindowMessageFilter", AsyncRemoveWindowMessageFilterProc, delegate_);
		connect_session->RegisterInterface("ClearWindowMessageFilter", AsyncClearWindowMessageFilterProc, delegate_);
		connect_session->RegisterInterface("SetWindowResizeRect", AsyncSetWindowResizeRectProc, delegate_);
    connect_session->RegisterInterface("SetWindowCaptionMove", AsyncSetWindowCaptionMoveProc, delegate_);
    connect_session->RegisterInterface("SetMainWnd", AsyncSetMainWnd, delegate_);
	}
	
private:
	Delegate* delegate_;
};

#endif  //__APLAYER_INTERFACES_H__
