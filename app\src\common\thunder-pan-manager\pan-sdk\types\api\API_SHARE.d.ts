export namespace API_SHARE {
    /**
     * 应用信息(用户侧)
     */
    export interface DriveApp {
        /**
         * 应用ID，每个应用拥有一个唯一ID
         */
        id?: string;
        /**
         * 应用名称
         */
        name: string;
        /**
         * 可以使用此应用打开的文件类型，包括mime_type或者文件后缀, 满足任意一个条件即可
         */
        access?: string[];
        /**
         * 应用入口链接
         */
        link?: string;
        /**
         * 引导链接，用于引导开通会员，或者展示应用错误信息
         * 1. 当用户的会员等级无法使用该应用时，通过该链接跳转到会员开通页面
         * 2. 当应用不可用时（例如超过在线解压文件大小上限），通过该链接跳转错误提示页面
         */
        redirect_link?: string;
        /**
         * 在应用中打开当前文件所要求的会员类型(已废弃)
         */
        vip_types?: string[];
        /**
         * 用于判断是否需要更高的会员等级使用该应用
         * false表示允许用户打开该应用
         * true表示需要更高的会员身份才能打开该应用（所需会员等级在vip_types中列出）
         */
        need_more_quota?: boolean;
        /**
         * 应用图标
         */
        icon_link?: string;
        /**
         * 是否默认打开的应用，true表示进入应用列表时默认选择该应用
         */
        is_default?: boolean;
        /**
         * app自定义配置，app可通过接口查询自己的配置
         */
        params?: {
            [name: string]: string;
        };
        /**
         * app属于哪个品类
         */
        category_ids?: string[];
        /**
         * 广告位类型,0:不是广告位 1:是广告位 (其余枚举值用来预留后续的扩展需求,比如顶部广告位/底部广告位等)
         */
        ad_scene_type?: number; // int32
        /**
         * space 访问空间
         */
        space?: string;
        /**
         * 其它类型的链接
         * 遵循 mime_type => link
         * 例如:
         * "text/csv": "https://example.com/link?id=xxxx&format=csv",
         * "application/zip": "https://example.com/link?&format=zip",
         */
        links?: {
            [name: string]: DriveLink;
        };
    }
    /**
     * Audio 音频信息
     */
    export interface DriveAudio {
        /**
         * 专辑
         */
        album?: string;
        /**
         * 艺人
         */
        artist?: string;
        /**
         * 原专辑艺人
         */
        album_artist?: string;
        /**
         * 作曲者
         */
        composer?: string;
        /**
         * 归类
         */
        grouping?: string;
        /**
         * 类型，风格
         */
        genre?: string;
        /**
         * 年份
         */
        year?: string;
        /**
         * 时长
         */
        duration?: string;
        /**
         * 轨道
         */
        track?: string;
        /**
         * 采样率
         */
        sample_rate?: string;
        /**
         * 比特率
         */
        bitrate?: string;
        /**
         * 音频声道
         */
        channels?: string;
        /**
         * 歌词地址
         */
        lyric_address?: string;
        /**
         * 编解码器
         */
        codec?: string;
        /**
         * 扩展信息
         */
        ext?: string;
        /**
         * 更新时间
         */
        update_time?: string;
    }
    /**
     * 审核状态
     */
    export interface DriveAudit {
        /**
         * 审核状态
         */
        status?: DriveAuditStatus;
        /**
         * message 审核状态说明，用于客户端展现。
         */
        message?: string;
        /**
         * 审核状态简短说明
         */
        title?: string;
    }
    /**
     * 风控过滤结果
     * - STATUS_UNKNOW: 未知
     *  - STATUS_OK: 允许
     *  - STATUS_SENSITIVE_RESOURCE: 敏感资源
     *  - STATUS_SENSITIVE_WORD: 文件名包含敏感词
     *  - STATUS_INVALID_RESOURCE: 无效资源
     */
    export type DriveAuditStatus = "STATUS_UNKNOW" | "STATUS_OK" | "STATUS_SENSITIVE_RESOURCE" | "STATUS_SENSITIVE_WORD" | "STATUS_INVALID_RESOURCE";
    /**
     * BatchDeleteSharesRequest 用户批量停止分享请求
     */
    export interface DriveBatchDeleteSharesRequest {
        /**
         * 分享id列表，建议一次最多删除100个分享
         */
        ids?: string[];
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * BatchDeleteResponse 批量删除文件响应
     */
    export interface DriveBatchDeleteSharesResponse {
    }
    /**
     * BatchGetShareFileRequest 批量获取分享文件信息
     */
    export interface DriveBatchGetShareAdminRequest {
        /**
         * share_id 分享id
         */
        share_ids?: string[];
        /**
         * space 文件空间
         */
        space?: string;
        /**
         * {"share_to":{"in":"from1,from2,...,fromn"}}
         */
        filters?: string;
    }
    /**
     * BatchGetShareAdminResponse 获取分享基本数据请求
     */
    export interface DriveBatchGetShareAdminResponse {
        /**
         * shareid -> ShareMgrData
         */
        data?: {
            [name: string]: DriveShareMgrData;
        };
    }
    /**
     * BatchGetShareFileInfoAdminRequest 批量获取分享文件完整信息(包含下载链接)admin接口请求
     */
    export interface DriveBatchGetShareFileInfoAdminRequest {
        /**
         * share_id 分享id
         */
        share_id?: string;
        /**
         * file_id 文件id
         */
        file_ids?: string[];
        /**
         * space 空间
         */
        space?: string;
    }
    /**
     * BatchGetShareFileInfoAdminResponse 批量获取分享文件对应的播放url admin接口响应
     */
    export interface DriveBatchGetShareFileInfoAdminResponse {
        /**
         * share_status 状态
         */
        share_status?: DriveShareStatus;
        /**
         * file_info 文件信息
         */
        files?: DriveFile[];
        /**
         * share_status_text 状态文案
         */
        share_status_text?: string;
        /**
         * params 扩展参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * BatchGetShareFileInfoRequest 批量获取分享文件完整信息(包含下载链接)请求
     */
    export interface DriveBatchGetShareFileInfoRequest {
        /**
         * share_id 分享id
         */
        share_id?: string;
        /**
         * file_id 文件id
         */
        file_ids?: string[];
        /**
         * folder_id 最上层文件夹id
         */
        folder_id?: string;
        /**
         * pass_code_token 提取码token
         */
        pass_code_token?: string;
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 缩略图尺寸
         */
        thumbnail_size?: DriveImageSize;
        /**
         * 文件用途
         */
        usage?: DriveFileUsage;
        /**
         * 场景
         */
        scene?: DriveShareScene;
        /**
         * 密码
         */
        pass_code?: string;
    }
    /**
     * BatchGetShareFileInfoResponse 批量获取分享文件对应的播放url响应
     */
    export interface DriveBatchGetShareFileInfoResponse {
        /**
         * share_status 状态
         */
        share_status?: DriveShareStatus;
        /**
         * file_info 文件信息
         */
        files?: DriveFile[];
        /**
         * share_status_text 状态文案
         */
        share_status_text?: string;
    }
    /**
     * BatchStopShareAdminRequest  批量停止分享Admin请求
     */
    export interface DriveBatchStopShareAdminRequest {
        /**
         * share_ids 停止指定id分享
         */
        share_ids?: string[];
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 其他信息 如封停原因
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * BatchStopShareAdminResponse 批量停止分享Admin响应
     */
    export interface DriveBatchStopShareAdminResponse {
    }
    /**
     * ClearRestoredShareAdminRequest 清除已转存分享Admin请求
     */
    export interface DriveClearRestoredShareAdminRequest {
        /**
         * 用户id，必须传入
         */
        user_id?: string;
        /**
         * 用户空间
         */
        space?: string;
        /**
         * filters 过滤器
         * 结构是: 参数名->过滤操作
         * json 结构:
         * {"start_time":{"gt":"2006-01-02T15:04:05.999Z07:00"}}
         */
        filter?: string;
        /**
         * 任务id
         */
        task_id?: string;
        /**
         * params 扩展参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * ClearRestoredShareAdminResponse 清除已转存分享Admin响应
     */
    export interface DriveClearRestoredShareAdminResponse {
        /**
         * phase 状态
         */
        phase?: DrivePhaseType;
    }
    /**
     * ClearShareAdminRequest 清除分享Admin请求
     */
    export interface DriveClearShareAdminRequest {
        /**
         * 用户id，必须传入
         */
        user_id?: string;
        /**
         * 用户空间
         */
        space?: string;
        /**
         * filters 过滤器
         * 结构是: 参数名->过滤操作
         * json 结构:
         * {"start_time":{"gt":"2006-01-02T15:04:05.999Z07:00"}}
         */
        filter?: string;
        /**
         * 任务id
         */
        task_id?: string;
        /**
         * params 扩展参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * ClearShareAdminResponse 清除分享Admin响应
     */
    export interface DriveClearShareAdminResponse {
        /**
         * phase 状态
         */
        phase?: DrivePhaseType;
    }
    /**
     * 合辑信息
     */
    export interface DriveCollection {
        /**
         * 类型 video/audio/image
         */
        type?: string;
        /**
         * 文件数量
         */
        count?: number; // int32
        /**
         * 排列顺序(从大到小排列)
         */
        order?: string; // int64
    }
    /**
     * CreateShareAdminRequest 创建分享admin请求
     */
    export interface DriveCreateShareAdminRequest {
        /**
         * files 文件数组
         */
        file_ids?: string[];
        /**
         * restore_limit 最大转存次数
         */
        restore_limit?: string; // int64
        /**
         * expiration_days 过期时间（相对时间，N天之后）
         * 传入-1表示永不过期
         */
        expiration_days?: string; // int64
        /**
         * expiration_at 过期时间（绝对时间），RFC3339格式精确到毫秒: 2022-03-28T17:24:02.000+08:00，-1表示永不过期
         */
        expiration_at?: string;
        /**
         * share_to 分享途径
         */
        share_to?: string;
        /**
         * title 分享标题
         */
        title?: string;
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 提取码配置
         */
        pass_code_option?: DrivePassCodeOption;
        /**
         * 客户端指定提取码（仅在 pass_code_option=REQUIRED 时可用）
         */
        custom_pass_code?: string;
        /**
         * 扩展参数 Params
         * 支持的扩展参数列表:
         * - WithPassCodeInLink 是否在链接后追加密码 示例: `{"params":{"WithPassCodeInLink":"true"}}`
         */
        params?: {
            [name: string]: string;
        };
        /**
         * user_id 指定创建分享的用户
         */
        user_id?: string;
    }
    /**
     * CreateShareAdminResponse 创建分享admin响应
     */
    export interface DriveCreateShareAdminResponse {
        /**
         * share_id
         */
        share_id?: string;
        /**
         * share_url 分享页url
         */
        share_url?: string;
        /**
         * pass_code 提取码
         */
        pass_code?: string;
        /**
         * share_text 分享页文案
         */
        share_text?: string;
        /**
         * share_list 列表信息
         */
        share_list?: DriveShareMgrData[];
        /**
         * 审核异常的文件
         */
        share_error_files?: DriveFile[];
    }
    /**
     * CreateShareRequest 创建分享请求
     */
    export interface DriveCreateShareRequest {
        /**
         * files 文件数组
         */
        file_ids?: string[];
        /**
         * restore_limit 最大转存次数
         */
        restore_limit?: string; // int64
        /**
         * expiration_days 过期时间（相对时间，N天之后）
         * 传入-1表示永不过期
         */
        expiration_days?: string; // int64
        /**
         * expiration_at 过期时间（绝对时间），RFC3339格式精确到毫秒: 2022-03-28T17:24:02.000+08:00，-1表示永不过期
         */
        expiration_at?: string;
        /**
         * share_to 分享途径
         */
        share_to?: string;
        /**
         * title 分享标题
         */
        title?: string;
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 提取码配置
         */
        pass_code_option?: DrivePassCodeOption;
        /**
         * 客户端指定提取码（仅在 pass_code_option=REQUIRED 时可用）
         */
        custom_pass_code?: string;
        /**
         * 扩展参数 Params
         * 支持的扩展参数列表:
         * - WithPassCodeInLink 是否在链接后追加密码 示例: `{"params":{"WithPassCodeInLink":"true"}}`
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * CreateShareResponse 创建分享响应
     */
    export interface DriveCreateShareResponse {
        /**
         * share_id
         */
        share_id?: string;
        /**
         * share_url 分享页url
         */
        share_url?: string;
        /**
         * pass_code 提取码
         */
        pass_code?: string;
        /**
         * share_text 分享页文案
         */
        share_text?: string;
        /**
         * share_list 列表信息
         */
        share_list?: DriveShareMgrData[];
        /**
         * 审核异常的文件
         */
        share_error_files?: DriveFile[];
        /**
         * share_text_ext 分享页文案扩展
         */
        share_text_ext?: string;
    }
    /**
     * DeleteShareRequest 用户停止分享请求
     */
    export interface DriveDeleteShareRequest {
        /**
         * share_id 分享id
         */
        share_id?: string;
        /**
         * 文件空间
         */
        space?: string;
    }
    /**
     * DeleteShareResponse 用户停止分享响应
     */
    export interface DriveDeleteShareResponse {
    }
    /**
     * File 文件信息
     */
    export interface DriveFile {
        /**
         * the kind of the file, 可选值： drive#file和drive#folder
         */
        kind?: string;
        /**
         * The id of the file
         */
        id?: string;
        /**
         * The parent_id of the file
         */
        parent_id?: string;
        /**
         * The name of the file
         */
        name?: string;
        /**
         * The owner user_id of the file
         */
        user_id?: string;
        /**
         * The size of the file's content in bytes.
         */
        size?: string; // int64
        /**
         * The revision of the file
         */
        revision?: string; // int64
        /**
         * The final component of fullFileExtension. This is only available for files with binary content in Google Drive.
         */
        file_extension?: string;
        /**
         * The MIME type of the file. https://tool.oschina.net/commons
         */
        mime_type?: string;
        /**
         * Whether the user has starred the file.
         */
        starred?: boolean;
        /**
         * A link for downloading the content of the file in a browser.
         */
        web_content_link?: string;
        /**
         * 创建时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        created_time?: string;
        /**
         * 系统修改时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        modified_time?: string;
        /**
         * A static, unauthenticated link to the file's icon.
         */
        icon_link?: string;
        /**
         * A short-lived link to the file's thumbnail, if available. Typically lasts on the order of hours. Only populated when the requesting app can access the file's content.
         */
        thumbnail_link?: string;
        /**
         * file md5Checksum
         */
        md5_checksum?: string;
        /**
         * file hash gcid
         */
        hash?: string;
        /**
         * 其它类型的链接
         * 遵循 mime_type => link
         * 例如:
         * "text/csv": "https://example.com/link?id=xxxx&format=csv",
         * "application/zip": "https://example.com/link?&format=zip",
         */
        links?: {
            [name: string]: DriveLink;
        };
        /**
         * 状态阶段
         */
        phase?: DrivePhaseType;
        /**
         * 审核状态
         */
        audit?: DriveAudit;
        /**
         * medias 媒体信息
         */
        medias?: DriveMedia[];
        /**
         * 是否是回收站文件
         */
        trashed?: boolean;
        /**
         * 回收站文件预定删除时间
         */
        delete_time?: string;
        /**
         * 文件原始url
         */
        original_url?: string;
        /**
         * params 扩展参数
         */
        params?: {
            [name: string]: string;
        };
        /**
         * BT文件索引
         */
        original_file_index?: number; // int64
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 支持的app
         */
        apps?: DriveApp[];
        /**
         * 文件是否可写
         */
        writable?: boolean;
        /**
         * 文件夹类型
         */
        folder_type?: string;
        /**
         * 合辑信息
         */
        collection?: DriveCollection;
        /**
         * The sort_name of the file
         */
        sort_name?: string;
        /**
         * 用户修改时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        user_modified_time?: string;
        /**
         * 拼写文件名，不包含文件后缀
         */
        spell_name?: DriveWordSpell[];
        /**
         * 文件类型
         * video,text,image,audio,archive,font,subtitle,installer,other
         */
        file_category?: DriveFileCategory;
        /**
         * 文件的标签
         */
        tags?: DriveTag[];
        /**
         * 文件涉及到的事件
         */
        reference_events?: DriveReferenceEvent[];
        /**
         * 引用的资源 如:任务/子任务/...
         */
        reference_resource?: ProtobufAny;
    }
    /**
     * FileCategory 文件类型
     * - OTHER: 其他文件类型
     *  - VIDEO: Video视频文件类型
     *  - TEXT: Text文本文件类型
     *  - IMAGE: Image图像文件类型
     *  - AUDIO: Audio音频文件类型
     *  - ARCHIVE: Archive归档文件类型
     *  - FONT: Font字体文件类型
     *  - SUBTITLE: Subtitle字幕文件类型
     *  - INSTALLER: Installer安装包文件类型
     */
    export type DriveFileCategory = "OTHER" | "VIDEO" | "TEXT" | "IMAGE" | "AUDIO" | "ARCHIVE" | "FONT" | "SUBTITLE" | "INSTALLER";
    /**
     * FileOrder 文件排序类型
     * - DEFAULT_ORDER: 默认排序，由服务端决定
     *  - ID_DESC: 文件ID倒序
     *  - TYPE_DESC: 文件类型排序：默认文件夹 > 普通文件夹 > 文件 > id倒序排列
     *  - MODIFY_TIME_ASC: 文件修改时间正序
     *  - MODIFY_TIME_DESC: 文件修改时间倒序
     *  - SIZE_ASC: 文件大小正序
     *  - SIZE_DESC: 文件大小倒序
     *  - CREATED_TIME_ASC: 文件创建时间升序
     *  - CREATED_TIME_DESC: 文件创建时间降序
     *  - NAME_ASC: 文件名正序
     *  - NAME_DESC: 文件名倒序
     */
    export type DriveFileOrder = "DEFAULT_ORDER" | "ID_DESC" | "TYPE_DESC" | "MODIFY_TIME_ASC" | "MODIFY_TIME_DESC" | "SIZE_ASC" | "SIZE_DESC" | "CREATED_TIME_ASC" | "CREATED_TIME_DESC" | "NAME_ASC" | "NAME_DESC";
    /**
     * 文件用途
     * - ALL: 拉取文件全部信息
     *  - FETCH: 文件取回
     * 用于文件取回，不支持播放
     *  - PLAY: 文件播放
     * 用于媒体播放，不支持文件取回
     *  - CACHE: 文件预缓存
     * 客户端取medias列表里的default资源做预缓存，该资源链接与普通播放链接限速策略不同
     * medias列表为空表示禁止当前客户端预缓存
     *  - CACHE_ALL: 缓存文件全部信息，可用于取回和播放
     *  - PROJECTION: 投屏
     *  - CONSUME: 文件消费
     *  - DISPLAY: 仅用于文件展示, 类似文件列表接口, 不返回取回链接、播放链接和云app信息
     */
    export type DriveFileUsage = "ALL" | "FETCH" | "PLAY" | "CACHE" | "CACHE_ALL" | "PROJECTION" | "CONSUME" | "DISPLAY";
    /**
     * GetShareDetailAdminResponse 分享详情查询Admin响应
     */
    export interface DriveGetShareDetailAdminResponse {
        /**
         * files 文件数组
         */
        files?: DriveFile[];
        /**
         * next_page_token 偏移量，下一次拉取数据时的标记
         */
        next_page_token?: string;
        /**
         * params 扩展参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * GetShareDetailResponse 查看分享子文件响应
     */
    export interface DriveGetShareDetailResponse {
        /**
         * share_status 状态
         */
        share_status?: DriveShareStatus;
        /**
         * share_status_text 状态文案
         */
        share_status_text?: string;
        /**
         * files 文件数组
         */
        files?: DriveFile[];
        /**
         * next_page_token 偏移量，下一次拉取数据时的标记
         */
        next_page_token?: string;
        /**
         * parent 父文件
         */
        parent?: DriveFile;
    }
    /**
     * GetShareFileInfoResponse 获取分享文件对应的播放url响应
     */
    export interface DriveGetShareFileInfoResponse {
        /**
         * share_status 状态
         */
        share_status?: DriveShareStatus;
        /**
         * share_status_text 状态文案
         */
        share_status_text?: string;
        /**
         * file_info 文件信息
         */
        file_info?: DriveFile;
    }
    /**
     * GetShareMetaDataResponse 获取分享基本数据请求
     */
    export interface DriveGetShareMetaDataResponse {
        /**
         * share_status 状态
         */
        share_status?: DriveShareStatus;
        /**
         * share_status_text 状态文案
         */
        share_status_text?: string;
        /**
         * file_num 文件个数 // 勾选个数, 非总文件数
         */
        file_num?: string; // int64
        /**
         * expiration_left 剩余有效时长(天)
         * 精确的剩余有效时长为: expiration_left + expiration_left_seconds
         */
        expiration_left?: string; // int64
        /**
         * expiration_left_seconds 剩余有效时长(秒, 值可能为负数，绝对值一定小于24*60*60)
         * 精确的剩余有效时长为: expiration_left + expiration_left_seconds
         */
        expiration_left_seconds?: string; // int64
        /**
         * expiration_at 过期时间（绝对时间），RFC3339格式精确到毫秒: 2022-03-28T17:24:02.000+08:00，-1表示永不过期
         */
        expiration_at?: string;
        /**
         * restore_count_left 剩余转存次数
         */
        restore_count_left?: string; // int64
        /**
         * files 第一层文件列表
         */
        files?: DriveFile[];
        /**
         * user_info 用户信息
         */
        user_info?: DriveUserInfo;
        /**
         * next_page_token 偏移量，下一次拉取数据时的标记
         */
        next_page_token?: string;
        /**
         * pass_code_token 提取码token
         */
        pass_code_token?: string;
        /**
         * title 分享标题
         */
        title?: string;
        /**
         * icon_link 图标，如果分享包含多个文件，服务端根据一定策略选取一个图标
         */
        icon_link?: string;
        /**
         * thumbnail_link 缩略图，如果分享包含多个文件，服务端根据一定策略选取一张缩略图
         */
        thumbnail_link?: string;
        /**
         * contain_sensitive_resource_text 包含敏感资源显示文案
         */
        contain_sensitive_resource_text?: string;
        /**
         * params 扩展参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * GetShareStatusByFilesRequest 查询文件的分享状态请求
     */
    export interface DriveGetShareStatusByFilesRequest {
        /**
         * 文件id列表，一次最多100个
         */
        file_ids?: string[];
        /**
         * space 文件空间
         */
        space?: string;
    }
    /**
     * GetShareStatusByFilesResponse 查询文件的分享状态返回
     */
    export interface DriveGetShareStatusByFilesResponse {
        /**
         * file_statuses 每个文件对应的分享状态，未分享的文件id不会出现在列表中
         */
        file_statuses?: DriveGetShareStatusByFilesResponseStatus[];
    }
    export interface DriveGetShareStatusByFilesResponseStatus {
        /**
         * file_id 文件id
         */
        file_id?: string;
        /**
         * share_id 分享id
         */
        share_id?: string;
        /**
         * share_status 状态
         */
        share_status?: DriveShareStatus;
        /**
         * share_status_text 状态文案
         */
        share_status_text?: string;
    }
    /**
     * 图片尺寸
     * - SIZE_DEFAULT: 默认 具体行为由服务端定义
     *  - SIZE_SMALL: 小图
     *  - SIZE_MEDIUM: 中图
     *  - SIZE_LARGE: 大图
     *  - SIZE_BIG: 兼容TV端视频缩略图 width*height=720*406
     */
    export type DriveImageSize = "SIZE_DEFAULT" | "SIZE_SMALL" | "SIZE_MEDIUM" | "SIZE_LARGE" | "SIZE_BIG";
    /**
     * Link 下载/播放链接
     */
    export interface DriveLink {
        /**
         * url 下载/播放链接
         */
        url?: string;
        /**
         * token 加速token
         */
        token?: string;
        /**
         * expire 过期时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        expire?: string;
        /**
         * type 链接类型 wan=广域网 lan=局域网
         */
        type?: string;
    }
    /**
     * ListShareAdminResponse 查询分享列表Admin响应
     */
    export interface DriveListShareAdminResponse {
        /**
         * data 分享管理数据列表
         */
        data?: DriveShareMgrData[];
        /**
         * next_page_token 下一页page_token, 如没有下一页, next_page_token为空
         */
        next_page_token?: string;
    }
    /**
     * ListShareResponse 用户查询分享列表响应
     */
    export interface DriveListShareResponse {
        /**
         * data 分享管理数据列表
         */
        data?: DriveShareMgrData[];
        /**
         * next_page_token 下一页page_token, 如没有下一页, next_page_token为空
         */
        next_page_token?: string;
    }
    /**
     * Media 媒体信息
     * 分辨率判断
     * 清晰度|范围（pixel=高X宽）| 备注
     * 
     * 8K|pixel>=20736000|20736000=(8K+4K)/2
     * 
     * 4K|5990400<=pixel<20736000|5990400=(4K+2K)/2
     * 
     * 2K|2880000<=pixel<5990400|2880000=(2K+1080)/2
     * 
     * 1080P|1497600<=pixel<2880000|1497600=(1080+720)/2
     * 
     * 720P|664800<=pixel<1497600|664800=(720+480)/2
     * 
     * 480P|319680<=pixel<664800 | 319680=(480+360)/2
     * 
     * 360P|pixel<319680
     */
    export interface DriveMedia {
        /**
         * media_id 媒体ID。后续根据该ID来取播放链接
         */
        media_id?: string;
        /**
         * media_name 清晰度名称，用于清晰度列表展示：流畅 480P、高清 720P、超清1080P、无损
         */
        media_name?: string;
        /**
         * video 视频信息，包括分辨率、码率等信息
         */
        video?: DriveVideo;
        /**
         * link 播放链接信息
         */
        link?: DriveLink;
        /**
         * need_more_quota 用于判断用户是否有资格播放该清晰度的资源，值为true时表示需要更高的会员身份（所需会员等级在vip_types中列出），实际能否播放还需进一步判断is_visible
         */
        need_more_quota?: boolean;
        /**
         * vip_types 当need_more_quota=true时，标识该资源需要的会员身份
         */
        vip_types?: string[];
        /**
         * redirect_link 导购链接。目前，当need_more_quota=true时，则该链接跳转到购买vip链接
         */
        redirect_link?: string;
        /**
         * icon_link 清晰度图片
         */
        icon_link?: string;
        /**
         * is_default 是否默认播放，进入播放列表默认选择该清晰度
         */
        is_default?: boolean;
        /**
         * priority 播放优先级，级别越高越优先播放
         */
        priority?: number; // int32
        /**
         * is_origin 是否是原始画质视频
         */
        is_origin?: boolean;
        /**
         * resolution_name 分辨率名称。例如：4K、2K、1080P，用于展现原始画质的清晰度。
         */
        resolution_name?: string;
        /**
         * is_visible 该条目是否对用户可见。当is_visible=false时，清晰度列表需要隐藏该条目。
         */
        is_visible?: boolean;
        /**
         * category 视频清晰度类别，例如：原始画质、转码画质、HDR画质
         */
        category?: string;
        /**
         * 音频
         */
        audio?: DriveAudio;
    }
    /**
     * PassCodeOption 提取码配置
     * - DEFAULT: DEFAULT 由服务端决定是否需要设置提取码（提取码只能由服务端随机生成）
     *  - REQUIRED: REQUIRED 需要提取码访问。此模式下，客户端可传入custom_pass_code来指定提取码，若客户端不指定则由服务端随机生成
     *  - NOT_REQUIRED: NOT_REQUIRED 不需要提取码访问
     */
    export type DrivePassCodeOption = "DEFAULT" | "REQUIRED" | "NOT_REQUIRED";
    /**
     * PhaseType 文件阶段类型
     * - PHASE_TYPE_PENDING: 等待中
     *  - PHASE_TYPE_RUNNING: 进行中
     *  - PHASE_TYPE_ERROR: 失败
     *  - PHASE_TYPE_COMPLETE: 完成
     *  - PHASE_TYPE_PAUSED: 暂停
     */
    export type DrivePhaseType = "PHASE_TYPE_UNKNOW" | "PHASE_TYPE_PENDING" | "PHASE_TYPE_RUNNING" | "PHASE_TYPE_ERROR" | "PHASE_TYPE_COMPLETE" | "PHASE_TYPE_PAUSED";
    /**
     * RecoverShareAdminRequest 还原被停止的分享Admin请求
     */
    export interface DriveRecoverShareAdminRequest {
        /**
         * share_id 分享id
         */
        share_id?: string;
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 其他信息 如恢复原因
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * RecoverShareAdminResponse 还原被停止的分享Admin响应
     */
    export interface DriveRecoverShareAdminResponse {
    }
    /**
     * （文件列表的）引用事件信息
     */
    export interface DriveReferenceEvent {
        /**
         * kind 固定值：drive#event
         */
        kind?: string;
        /**
         * type 事件类型
         */
        type?: DriveType;
        /**
         * type_name 事件类型描述。前端直接使用该字段展现
         */
        type_name?: string;
        /**
         * source 事件源，比如是上传、离线添加
         */
        source?: string;
        /**
         * subject 事件主题
         */
        subject?: string;
        /**
         * id 事件id
         */
        id?: string;
        /**
         * device 发生该事件的设备
         */
        device?: string;
        /**
         * created_time 事件发生时间，日期时间格式使用RFC 3339格式精确到毫秒。
         * 例如：2006-01-02T15:04:05.999Z07:00。
         * go生成方式：time.Now().Format("2006-01-02T15:04:05.999Z07:00")；
         * java生成方式：new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX").format(new Date())
         */
        created_time?: string;
        /**
         * icon_url 封面地址
         */
        icon_url?: string;
        /**
         * params 事件内容，json格式
         * play_seconds 客户端上报的播放时间，单位：秒
         * play_duration 客户端上报的视频时长，单位：秒
         * audio_track 客户端上报的音轨索引，索引下标从0开始，0代表第一个音轨，形如："audio_track":"0"
         * subtitle 客户端上报的字幕，内嵌字幕上报索引，索引下标从0开始，0代表第一个内嵌字幕，形如："subtitle":"2"。在线字幕报gcid，形如"subtitle":"30467D13ACA17FFD76086108B2635C88F9D400C0"
         * media_id 客户端上报的文件gcid，值的是取medias里面的media_id字段，形如："media_id":"3D136857102A551559EA90781DBCFC88BE2BC54A"
         */
        params?: {
            [name: string]: string;
        };
        /**
         * updated_time 事件更新时间
         */
        updated_time?: string;
        /**
         * 事件标签
         */
        label?: string;
        /**
         * 进度
         */
        progress?: number; // int32
    }
    /**
     * RestoreDeleteRequest 删除转存记录请求
     */
    export interface DriveRestoreDeleteRequest {
        /**
         * restore_id 分享id
         */
        restore_id?: string;
        /**
         * space 空间
         */
        space?: string;
    }
    /**
     * RestoreDeleteResponse 删除转存记录响应
     */
    export interface DriveRestoreDeleteResponse {
    }
    /**
     * ListShareResponse 用户转存分享列表响应
     */
    export interface DriveRestoreListResponse {
        /**
         * data 分享管理数据列表
         */
        data?: DriveRestoreResp[];
        /**
         * next_page_token 下一页page_token, 如没有下一页, next_page_token为空
         */
        next_page_token?: string;
    }
    export interface DriveRestoreResp {
        id?: string;
        user_id?: string;
        review_time?: string;
        created_time?: string;
        updated_time?: string;
        data?: DriveShareMgrData;
    }
    /**
     * RestoreReviewRequest 更新分享查阅时间请求
     */
    export interface DriveRestoreReviewRequest {
        /**
         * restore_id 分享id
         */
        restore_id?: string;
        /**
         * space 空间
         */
        space?: string;
        /**
         * share_id 分享id
         */
        share_id?: string;
    }
    /**
     * RestoreReviewResponse 更新分享查阅时间响应
     */
    export interface DriveRestoreReviewResponse {
    }
    /**
     * RestoreShareRequest 分享转存请求
     */
    export interface DriveRestoreShareRequest {
        /**
         * 分享id
         */
        share_id?: string;
        /**
         * 提取码token
         */
        pass_code_token?: string;
        /**
         * 转存文件id列表
         */
        file_ids?: string[];
        /**
         * 转存文件的从根目录开始的祖先文件id列表
         * ancestor_ids、file_id使用说明
         * 假如某个分享的文件结构如下：
         * ```
         * ├── A
         * │   └── A_1
         * │       ├── A_1_1
         * │       └── A_1_2
         * ├── B
         * └── C
         * ```
         * 1. 转存全部文件，不需要传入file_ids，也不需要传入ancestor_ids
         * 2. 转存第一层的文件，例如A+B，file_ids传A、B，不需要传入ancestor_ids
         * 3. 转存第二层的文件，例如A_1, file_ids传A_1，ancestor_ids传A
         * 4. 转存第三层的文件，例如A_1_1,  file_ids传A_1_1，ancestor_ids传A、A_1
         */
        ancestor_ids?: string[];
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 转存至指定文件夹
         */
        parent_id?: string;
        /**
         * 是否转存至指定文件夹(由于parent_id为空字符串时含义为转存至跟路径, 增加此字段兼容原逻辑)
         */
        specify_parent_id?: boolean;
        /**
         * 转存到指定的系统文件夹（specify_parent_id=false时，默认转存到RESTORE系统文件夹）
         */
        folder_type?: string;
        /**
         * params 扩展参数
         * 支持的扩展参数列表:
         * - trace_file_ids 传入分享列表中的部分文件id，用于给客户端返回这些文件id被转存到自己的云盘后对应的新的文件id
         *   - 跟踪所有文件id:  传入`*`即可，示例：`{"params":{"trace_file_ids":"*"}}`
         *   - 跟踪指定几个文件id：多个id之间以逗号分隔，示例: `{"params":{"trace_file_ids":"id1,id2,id3"}}`
         * - group_id 分享对应的群组ID，示例：`{"params":{"group_id":"1234"}}`
         */
        params?: {
            [name: string]: string;
        };
        /**
         * 场景
         */
        scene?: DriveShareScene;
        /**
         * 目标空间
         */
        to_space?: string;
    }
    /**
     * RestoreShareResponse 分享转存响应
     */
    export interface DriveRestoreShareResponse {
        /**
         * 状态
         */
        share_status?: DriveShareStatus;
        /**
         * share_status_text 状态文案
         */
        share_status_text?: string;
        /**
         * 新建文件夹的id
         */
        file_id?: string;
        /**
         * 转存结果
         */
        restore_status?: DriveRestoreStatus;
        /**
         * 异步转存任务id
         */
        restore_task_id?: string;
        /**
         * 扩展参数
         * 支持的扩展参数列表:
         * - trace_file_ids 用于给客户端返回某些文件id被转存到自己的云盘后对应的新的文件id
         *   - 若请求中没有传入此字段，返回结果中也没有此字段
         *   - 若请求中传入了此字段
         *     1. 若是同步转存(restore_status=RESTORE_COMPLETE)，此结构中会返回此字段
         *     2. 若是异步转存(restore_status=RESTORE_START)，task的params中会返回此字段
         *   - 字段值为json字符串，示例: `{"params":{"trace_file_ids":"{\"id1\":\"new_id1\",\"id2\":\"new_id2\",\"id3\":\"new_id3\"}"}}`
         */
        params?: {
            [name: string]: string;
        };
    }
    export type DriveRestoreStatus = "RESTORE_UNKNOWN" | "RESTORE_COMPLETE" | "RESTORE_START";
    /**
     * ShareMgrData 分享管理数据
     */
    export interface DriveShareMgrData {
        /**
         * share_id 分享id
         */
        share_id?: string;
        /**
         * share_status 状态
         */
        share_status?: DriveShareStatus;
        /**
         * share_status_text 状态文案
         */
        share_status_text?: string;
        /**
         * title 标题
         */
        title?: string;
        /**
         * icon_link 图标
         */
        icon_link?: string;
        /**
         * thumbnail_link 缩略图
         */
        thumbnail_link?: string;
        /**
         * pass_code 提取码
         */
        pass_code?: string;
        /**
         * file_num 文件个数 // 勾选个数, 非总文件数
         */
        file_num?: string; // int64
        /**
         * restore_limit 用户设置最大转存次数
         */
        restore_limit?: string; // int64
        /**
         * expiration_days 用户设置过期时间(天)
         */
        expiration_days?: string; // int64
        /**
         * expiration_at 过期时间（绝对时间），RFC3339格式精确到毫秒: 2022-03-28T17:24:02.000+08:00，-1表示永不过期
         */
        expiration_at?: string;
        /**
         * restore_count 已转存次数
         */
        restore_count?: string; // int64
        /**
         * expiration_left 剩余有效时长(天)
         * 精确的剩余有效时长为: expiration_left + expiration_left_seconds
         */
        expiration_left?: string; // int64
        /**
         * expiration_left_seconds 剩余有效时长(秒, 值可能为负数，绝对值一定小于24*60*60)
         * 精确的剩余有效时长为: expiration_left + expiration_left_seconds
         */
        expiration_left_seconds?: string; // int64
        /**
         * 浏览量
         */
        view_count?: string; // int64
        /**
         * create_time 分享时间
         */
        create_time?: string;
        /**
         * user_info 用户信息
         */
        user_info?: DriveUserInfo;
        /**
         * share_url 分享链接
         */
        share_url?: string;
        /**
         * file_id 展示的文件的id
         */
        file_id?: string;
        /**
         * file_kind 展示的文件的类型
         */
        file_kind?: string;
        /**
         * file_size 展示的文件的大小
         */
        file_size?: string; // int64
        /**
         * share_to 分享渠道
         */
        share_to?: string;
        /**
         * params 扩展参数
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * 场景
     * - UNKNOWN: 其他
     *  - GROUP: GROUP 群组
     *  - BOOK: BOOK 小说
     *  - NORMAL: NORMAL 普通分享
     */
    export type DriveShareScene = "UNKNOWN" | "GROUP" | "BOOK" | "NORMAL";
    /**
     * ShareStatus 分享的状态
     * - OK: OK 有效
     *  - PASS_CODE_EMPTY: PASS_CODE_EMPTY 需要输入提取码
     *  - PASS_CODE_ERROR: PASS_CODE_ERROR 提取码错误
     *  - MAX_RESTORE_COUNT: MAX_RESTORE_COUNT 超过最大转存次数
     *  - EXPIRED: EXPIRED 已过期
     *  - DELETED: DELETED 已删除
     *  - SENSITIVE_RESOURCE: SENSITIVE 包含敏感资源
     *  - SENSITIVE_WORD: SENSITIVE_WORD 文件名包含敏感词
     *  - AUDITING: AUDITING 审核中
     *  - NOT_FOUND: NOT_FOUND 未找到
     *  - PROHIBITED: PROHIBITED 分享功能被禁止使用
     *  - USER_RESTORE_EXCEEDED_LIMIT: USER_RESTORE_EXCEEDED_LIMIT 用户转存分享超过给定次数
     */
    export type DriveShareStatus = "OK" | "PASS_CODE_EMPTY" | "PASS_CODE_ERROR" | "MAX_RESTORE_COUNT" | "EXPIRED" | "DELETED" | "SENSITIVE_RESOURCE" | "SENSITIVE_WORD" | "AUDITING" | "NOT_FOUND" | "PROHIBITED" | "USER_RESTORE_EXCEEDED_LIMIT";
    /**
     * StopShareAdminRequest 停止分享Admin请求
     */
    export interface DriveStopShareAdminRequest {
        /**
         * share_id 停止指定id分享
         */
        share_id?: string;
        /**
         * user_id 停止指定用户所有有效分享
         */
        user_id?: string;
        /**
         * 文件空间
         */
        space?: string;
        /**
         * 其他信息 如封停原因
         */
        params?: {
            [name: string]: string;
        };
    }
    /**
     * StopShareAdminResponse 停止分享Admin响应
     */
    export interface DriveStopShareAdminResponse {
    }
    /**
     * Tag 文件的标签
     */
    export interface DriveTag {
        /**
         * 标签的id
         */
        id?: string;
        /**
         * 标签名
         */
        name?: string;
        /**
         * 标签类型, 0:系统定义的 1:用户定义的
         */
        type?: number; // int32
        /**
         * 标签图标
         */
        icon_link?: string;
    }
    /**
     * Type 事件类型
     * - TYPE_PLAY: TYPE_PLAY 播放视频或音频
     *  - TYPE_VIEW: TYPE_VIEW 查看文件内容
     *  - TYPE_CREATE: TYPE_UPDATE 创建文件。
     * 可能是离线下载或者解压等动作创建的文件。
     *  - TYPE_UPDATE: TYPE_UPDATE 更新文件
     *  - TYPE_DOWNLOAD: TYPE_DOWNLOAD 下载文件
     *  - TYPE_DELETE: TYPE_DELETE 删除文件
     *  - TYPE_UPLOAD: TYPE_UPLOAD 上传文件
     *  - TYPE_RESTORE: TYPE_RESTORE 转存文件(夹)
     */
    export type DriveType = "TYPE_UNKNOWN" | "TYPE_PLAY" | "TYPE_VIEW" | "TYPE_CREATE" | "TYPE_UPDATE" | "TYPE_DOWNLOAD" | "TYPE_DELETE" | "TYPE_UPLOAD" | "TYPE_RESTORE";
    /**
     * UpdateShareRequest 更新分享请求
     */
    export interface DriveUpdateShareRequest {
        /**
         * share_id 要更新的分享id
         */
        share_id?: string;
        /**
         * space 要更新的分享id所在的空间
         */
        space?: string;
        /**
         * restore_limit 最大转存次数
         * =0:不更新
         * >0:更新为对应的值
         * <0:更新为不限制
         */
        restore_limit?: string; // int64
        /**
         * expiration_at 过期时间（绝对时间），RFC3339格式精确到毫秒: 2022-03-28T17:24:02.000+08:00，-1表示永不过期
         * 传空表示不修改
         * 非空则修改为对应值
         */
        expiration_at?: string;
        /**
         * pass_code_option 提取码配置
         * DEFAULT或不传: 不更新
         * NOT_REQUIRED: 删除提取码
         * REQUIRED: 提取码更新为custom_pass_code
         */
        pass_code_option?: DrivePassCodeOption;
        /**
         * 客户端指定提取码（仅在 pass_code_option=REQUIRED 时可用）
         */
        custom_pass_code?: string;
    }
    /**
     * UpdateShareResponse 更新分享返回
     */
    export interface DriveUpdateShareResponse {
        /**
         * data 更新后的数据
         */
        data?: DriveShareMgrData;
    }
    /**
     * UserInfo 用户信息
     */
    export interface DriveUserInfo {
        /**
         * user_id 用户id
         */
        user_id?: string;
        /**
         * portrait_url 用户头像
         */
        portrait_url?: string;
        /**
         * nickname 昵称
         */
        nickname?: string;
        /**
         * avatar 用户头像
         */
        avatar?: string;
    }
    /**
     * Video 视频信息
     */
    export interface DriveVideo {
        /**
         * height 高
         */
        height?: number; // int64
        /**
         * width 宽
         */
        width?: number; // int64
        /**
         * duration 视频时长
         */
        duration?: number; // int64
        /**
         * bit_rate 码率
         */
        bit_rate?: number; // int64
        /**
         * frame_rate 帧率
         */
        frame_rate?: number; // int64
        /**
         * video_codec 编码格式
         */
        video_codec?: string;
        /**
         * audio_codec 音频编码格式
         */
        audio_codec?: string;
        /**
         * video_type 视频类型
         */
        video_type?: string;
        /**
         * hdr_type 视频HDR类型
         */
        hdr_type?: string;
        /**
         * storage_type 存储类型  0:默认存储类型 1:边转边播存储类型
         */
        storage_type?: number; // int32
    }
    /**
     * WordSpell 单词拼写
     */
    export interface DriveWordSpell {
        /**
         * 单词，例如："长度123"
         * 此字段仅用于兼容TV端旧版逻辑，其他端请使用字段：split_word
         */
        word?: string;
        /**
         * 首字母，例如：“长度123” 返回的首字母是["C,Z", "D", "123"]
         */
        acronym?: string[];
        /**
         * 全拼，例如：“长度123”返回的全拼是["CHANG,ZHANG", "DU,DUO", "123"]
         */
        complete?: string[];
        /**
         * 切分后的单词，例如：例如：“长度123”返回的是["长", "度", "123"]
         */
        split_word?: string[];
    }
    export interface GooglerpcStatus {
        code?: number; // int32
        message?: string;
        details?: ProtobufAny[];
    }
    /**
     * `Any` contains an arbitrary serialized protocol buffer message along with a
     * URL that describes the type of the serialized message.
     * 
     * Protobuf library provides support to pack/unpack Any values in the form
     * of utility functions or additional generated methods of the Any type.
     * 
     * Example 1: Pack and unpack a message in C++.
     * 
     *     Foo foo = ...;
     *     Any any;
     *     any.PackFrom(foo);
     *     ...
     *     if (any.UnpackTo(&foo)) {
     *       ...
     *     }
     * 
     * Example 2: Pack and unpack a message in Java.
     * 
     *     Foo foo = ...;
     *     Any any = Any.pack(foo);
     *     ...
     *     if (any.is(Foo.class)) {
     *       foo = any.unpack(Foo.class);
     *     }
     * 
     * Example 3: Pack and unpack a message in Python.
     * 
     *     foo = Foo(...)
     *     any = Any()
     *     any.Pack(foo)
     *     ...
     *     if any.Is(Foo.DESCRIPTOR):
     *       any.Unpack(foo)
     *       ...
     * 
     * Example 4: Pack and unpack a message in Go
     * 
     *      foo := &pb.Foo{...}
     *      any, err := anypb.New(foo)
     *      if err != nil {
     *        ...
     *      }
     *      ...
     *      foo := &pb.Foo{}
     *      if err := any.UnmarshalTo(foo); err != nil {
     *        ...
     *      }
     * 
     * The pack methods provided by protobuf library will by default use
     * 'type.googleapis.com/full.type.name' as the type URL and the unpack
     * methods only use the fully qualified type name after the last '/'
     * in the type URL, for example "foo.bar.com/x/y.z" will yield type
     * name "y.z".
     * 
     * 
     * JSON
     * 
     * The JSON representation of an `Any` value uses the regular
     * representation of the deserialized, embedded message, with an
     * additional field `@type` which contains the type URL. Example:
     * 
     *     package google.profile;
     *     message Person {
     *       string first_name = 1;
     *       string last_name = 2;
     *     }
     * 
     *     {
     *       "@type": "type.googleapis.com/google.profile.Person",
     *       "firstName": <string>,
     *       "lastName": <string>
     *     }
     * 
     * If the embedded message type is well-known and has a custom JSON
     * representation, that representation will be embedded adding a field
     * `value` which holds the custom JSON in addition to the `@type`
     * field. Example (for message [google.protobuf.Duration][]):
     * 
     *     {
     *       "@type": "type.googleapis.com/google.protobuf.Duration",
     *       "value": "1.212s"
     *     }
     */
    export interface ProtobufAny {
        [name: string]: any;
        /**
         * A URL/resource name that uniquely identifies the type of the serialized
         * protocol buffer message. This string must contain at least
         * one "/" character. The last segment of the URL's path must represent
         * the fully qualified name of the type (as in
         * `path/google.protobuf.Duration`). The name should be in a canonical form
         * (e.g., leading "." is not accepted).
         * 
         * In practice, teams usually precompile into the binary all types that they
         * expect it to use in the context of Any. However, for URLs which use the
         * scheme `http`, `https`, or no scheme, one can optionally set up a type
         * server that maps type URLs to message definitions as follows:
         * 
         * * If no scheme is provided, `https` is assumed.
         * * An HTTP GET on the URL must yield a [google.protobuf.Type][]
         *   value in binary format, or produce an error.
         * * Applications are allowed to cache lookup results based on the
         *   URL, or have them precompiled into a binary to avoid any
         *   lookup. Therefore, binary compatibility needs to be preserved
         *   on changes to types. (Use versioned type names to manage
         *   breaking changes.)
         * 
         * Note: this functionality is not currently available in the official
         * protobuf release, and it is not used for type URLs beginning with
         * type.googleapis.com.
         * 
         * Schemes other than `http`, `https` (or the empty scheme) might be
         * used with implementation specific semantics.
         */
        "@type"?: string;
    }
    /**
     * - ORDER_DEFAULT: 默认排序（时间倒序）
     *  - ORDER_REVIEW_TIME_DESC: 按浏览时间倒序
     *  - ORDER_UPDATE_TIME_DESC: 按更新时间倒序 更新标签 -> 转存时间
     *  - ORDER_UPDATE_TIME_ASC: 按更新时间正序 更新标签 -> 转存时间
     */
    export type RestoreListRequestOrderBy = "ORDER_DEFAULT" | "ORDER_REVIEW_TIME_DESC" | "ORDER_UPDATE_TIME_DESC" | "ORDER_UPDATE_TIME_ASC";
}
