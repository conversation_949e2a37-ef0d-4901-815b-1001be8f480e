<script setup lang="ts">
import LoadingSpinner from '@root/common/components/ui/loading-spinner/index.vue'

import { useResizeObserver, useTimeoutFn } from '@vueuse/core'
import { computed, CSSProperties, onMounted, ref } from 'vue';
import { getLastOffset, getOffsetOrSpace, MessageType } from '.';

defineOptions({
  name: 'ThunderMessage',
})

const props = withDefaults(defineProps<{
  id: string
  message: string
  duration: number
  offset: number
  type: MessageType
  rightTextButton: string
  showTypeIcon: boolean
  onClose: () => void
  onRightTextButtonClick: () => void
}>(), {
  duration: 3000,
  offset: 80,
  type: 'info',
  showTypeIcon: true
})

const height = ref(0)
const visible = ref(false)
const innerType = ref<MessageType>('info')
const innerMessage = ref('')
const messageRef = ref<HTMLDivElement>()

const lastOffset = computed(() => getLastOffset(props.id))
const offset = computed(
  () => getOffsetOrSpace(props.id, props.offset) + lastOffset.value
)
const bottom = computed((): number => height.value + offset.value)
const customStyle = computed<CSSProperties>(() => ({
  top: `${offset.value}px`,
}))

let stopTimer: (() => void) | undefined = undefined

function close() {
  visible.value = false
}

function startTimer() {
  if (props.duration === 0) return

  const { stop } = useTimeoutFn(() => {
    close()
  }, props.duration);

  stopTimer = stop;
}

function clearTimer() {
  stopTimer?.()
}

function updateInnerType (type: MessageType) {
  innerType.value = type
  if (props.type !== 'loading') {
    startTimer()
  }
}

function updateInnerMessage (message: string) {
  innerMessage.value = message
}

function handleTextButtonClick () {
  if (props.onRightTextButtonClick) {
    props.onRightTextButtonClick()
    close()
  }
}

onMounted(() => {
  innerType.value = props.type
  innerMessage.value = props.message
  visible.value = true
  if (props.type !== 'loading') {
    startTimer()
  }
})

useResizeObserver(messageRef, () => {
  height.value = messageRef.value!.getBoundingClientRect().height
})

defineExpose({
  visible,
  bottom,
  close,
  updateInnerType,
  updateInnerMessage,
})
</script>

<template>
  <transition
    name="fadeInDown"
    @before-leave="onClose"
    @after-leave="$emit('destroy')"
  >
    <div
      v-show="visible"
      ref="messageRef"
      class="thunder-message"
      :id="id"
      :style="customStyle"
      @mouseenter="clearTimer"
      @mouseleave="startTimer"
    >
      <template v-if="showTypeIcon">
        <LoadingSpinner v-if="innerType === 'loading'" size="small" color="var(--font-font-1)" />

        <i  v-else class="thunder-message-icon" :class="{ [`thunder-message-icon__${innerType}`]: true }">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path v-if="innerType === 'error'" fill-rule="evenodd" clip-rule="evenodd" d="M10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19ZM12.793 5.79288L10.0001 8.58578L7.20718 5.79291L5.79297 7.20712L8.58585 10L5.79297 12.7929L7.20718 14.2071L10.0001 11.4142L12.793 14.2071L14.2072 12.7929L11.4143 10L14.2072 7.20709L12.793 5.79288Z" fill="currentColor"/>

            <path v-if="innerType === 'info'" fill-rule="evenodd" clip-rule="evenodd" d="M10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19ZM11.4309 6.43091C11.4309 7.22118 10.7903 7.86182 10 7.86182C9.20973 7.86182 8.56909 7.22118 8.56909 6.43091C8.56909 5.64064 9.20973 5 10 5C10.7903 5 11.4309 5.64064 11.4309 6.43091ZM11 8.63419V15.6342H9V8.63419H11Z" fill="currentColor"/>

            <path v-if="innerType === 'warning'" fill-rule="evenodd" clip-rule="evenodd" d="M11 1C6.02944 1 2 5.02944 2 10C2 14.9706 6.02944 19 11 19C15.9706 19 20 14.9706 20 10C20 5.02944 15.9706 1 11 1ZM9.56909 13.5691C9.56909 12.7788 10.2097 12.1382 11 12.1382C11.7903 12.1382 12.4309 12.7788 12.4309 13.5691C12.4309 14.3594 11.7903 15 11 15C10.2097 15 9.56909 14.3594 9.56909 13.5691ZM10 11.3658V4.36581H12V11.3658H10Z" fill="currentColor"/>

            <path v-if="innerType === 'success'" fill-rule="evenodd" clip-rule="evenodd" d="M10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19ZM9.11323 14.4142L15.2633 7.14595L13.7365 5.85406L8.8866 11.5858L6.1507 9.24075L4.84912 10.7593L9.11323 14.4142Z" fill="currentColor"/>
          </svg>
        </i>
      </template>

      <span class="thunder-message-text">{{ innerMessage }}</span>

      <span v-if="rightTextButton" class="thunder-message-button" @click="handleTextButtonClick">{{ rightTextButton }}</span>
    </div>
  </transition>
</template>

<style>
.fadeInDown-enter-active,
.fadeInDown-leave-active {
  transition: all 0.25s ease;
}

.fadeInDown-enter-from,
.fadeInDown-leave-to {
  opacity: 0;
  transform: translateY(-50px);
}

.thunder-message {
  position: fixed;
  z-index: var(--z-index-notice);
  left: 50%;
  translate: -50%;
  border: 1px solid var(--font-white-5);
  border-radius: var(--border-radius-L);
  background-color: var( --background-background-container);
  box-shadow: 0 6px 30px #272E3B29;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 10px;

  .thunder-message-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    &.thunder-message-icon__info {
      color: var(--primary-primary-default);
    }

    &.thunder-message-icon__error {
      color: var(--functional-error-default);
    }

    &.thunder-message-icon__warning {
      color: var(--functional-warning-default);
    }

    &.thunder-message-icon__success {
      color: var(--functional-success-default);
    }
  }

  .thunder-message-text {
    font-size: 13px;
    line-height: 22px;
    max-width: 400px;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    white-space: pre-wrap;
  }

  .thunder-message-button {
    font-size: 13px;
    line-height: 22px;
    cursor: pointer;
    color: var(--primary-primary-default);
  }
}
</style>
