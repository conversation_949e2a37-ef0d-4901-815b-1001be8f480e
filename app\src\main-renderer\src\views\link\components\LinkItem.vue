<script setup lang="ts">
import { clipboard } from 'electron'
import { computed, onMounted, ref } from 'vue'
import Tooltip from '@root/common/components/ui/tooltip/index.vue'
import ContextMenu from '@root/common/components/ui/context-menu/index.vue'
import { ThunderUtil } from '@root/common/utils'
import DropdownMenu from '@root/common/components/ui/dropdown-menu/index.vue'
import XMPMessage from '@root/common/components/ui/message/index'
import { ThunderNewTaskHelperNS } from '@root/common/task/client/new-task-helper';
import { usePromptDialog } from '@root/common/components/ui/Dialog/usePromptDialog'
import { useAlertDialog } from '@root/common/components/ui/Dialog/useAlertDialog'
import { LinkRecordHelper } from '@root/common/link-hub/client/link-record-helper'
import { config } from "@root/common/config/config"

import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { useThrottleFn } from '@vueuse/core'
dayjs.extend(relativeTime)

const promptDialog = usePromptDialog()
const alertDialog = useAlertDialog()

const LINK_ICON_MAP = {
  magnet: {
    icon: require('@root/common/assets/img/ic_magnet_link.svg'),
    tip: '磁力链'
  },
  http: {
    icon: require('@root/common/assets/img/ic_http_link.svg'),
    tip: '网页链接'
  },
  https: {
    icon: require('@root/common/assets/img/ic_http_link.svg'),
    tip: '网页链接'
  },
  ftp: {
    icon: require('@root/common/assets/img/ic_http_link.svg'),
    tip: '网页链接'
  },
  ed2k: {
    icon: require('@root/common/assets/img/ic_ed2k_link.svg'),
    tip: '电驴链接'
  },
  thunder: {
    icon: require('@root/common/assets/img/ic_thunder_link.svg'),
    tip: '迅雷云盘链接'
  }
}

const props = withDefaults(defineProps<{
  from?: 'linkList' | 'linkDetail'
  link: ThunderClientAPI.dataStruct.dataModals.LinkRecord
  highlighted?: boolean
}>(), {
  from: 'linkList',
  highlighted: false
})

const emit = defineEmits<{
  (e: 'selectLink'): void
  (e: 'deleteSuccess'): void
  (e: 'renameSuccess', newName: string): void
  (e: 'openLink')
  (e: 'playLink')
  (e: 'updateStatus', status: ThunderClientAPI.dataStruct.dataModals.LinkStatus)
}>()

const iconLink = ref('')

onMounted(async () => {
  const icon = await config.getLinkFileIconConfigValue(props?.link?.url_type)

  iconLink.value = icon
})

const contextMenuList = computed(() => [
  ...(props?.link?.can_video_play ? [{
    key: 'play',
    label: '播放',
    icon: 'xl-icon-general-play-m1',
  }] : props?.from === 'linkList' ? [{
    key: 'open',
    label: '打开',
    icon: 'xl-icon-general-openfile-m',
  }] : []),
  {
    key: 'download',
    label: '下载',
    icon: 'xl-icon-general-download-l"',
  },
  {
    key: 'cloud',
    label: '添加到云盘',
    icon: 'xl-icon-general-dump-m',
  },
  {
    key: 'rename',
    label: '重命名(R)',
    icon: 'xl-icon-general-edit-m'
  },
  {
    key: 'copy',
    label: '复制链接(C)',
    icon: 'xl-icon-general-copy-m'
  },
  {
    key: 'delete',
    label: '删除链接(D)',
    icon: 'xl-icon-delete',
    hasSeparator: true,
  },
])

const handleDownload = (type: ThunderNewTaskHelperNS.DownloadPathType) => {
  ThunderNewTaskHelperNS.showPreLinkCreateTaskWindow([{
    url: props?.link.url,
  }], {
    selectedPathType: type,
    autoExpandAll: true,
    title: '添加文件',
    extData: {
      [props?.link.url]: {
        fileName: props?.link?.name
      }
    }
  });
}

const handleItemAction = useThrottleFn((action: string) => {
  emit('updateStatus', 'STATUS_NORMAL')
  switch (action) {
    case 'openLink':
      emit('openLink')
      break
    case 'playLink':
      emit('playLink')
      break
    case 'selectLink':
      emit('selectLink')
      break
    case ThunderNewTaskHelperNS.DownloadPathType.Local:
    case ThunderNewTaskHelperNS.DownloadPathType.Cloud:
      handleDownload(action)
      break
    default:
      break
  }
}, 1000, false, true)

const handleRename = async () => {
  const result = await promptDialog.prompt({
    title: '重命名',
    defaultValue: props?.link?.name,
    validateOnInput: true,
    fixedHeight: true, // 启用固定高度模式
    inputType: 'textarea', // 使用文本区域
    selectAll: true,
    inputStyle: {
      height: '82px',
      minHeight: '82px',
      lineHeight: '22px',
      fontSize: '13px',
      resize: 'none',
      color: 'var(--font-font-1, #272E3B)',
      border: '1px solid var(--border-border-primary, #226DF5)',
      background: 'var(--background-background-elevated, #FFF)',
    },
    validator: (value) => {
      if (value.length > 255) {
        return { valid: false, message: `名称最多允许255个字符` };
      }

      if (/[\\/:*?"<>|]/.test(value)) {
        return { valid: false, message: `名称不能包含以下字符: \ / : * ? " <> |` };
      }
      return { valid: true, message: '' };
    },
    placeholder: '',
    hint: '',
    variant: '',
    inputClass: '',
    containerStyle: undefined,
    containerClass: '',
    inputProps: undefined,
    onChange: () => { },
    onConfirm: () => { }
  })

  if (result === false) {
    return
  }

  const res = await LinkRecordHelper.getInstance().updateLinkRecord({
    link: {
      ...props?.link,
      name: result as string
    }
  })

  console.log('重命名更新结果=========>', res)

  if (res?.error?.message) {
    XMPMessage({
      message: res?.error?.message,
      type: 'error'
    })
  } else {
    XMPMessage({
      message: '重命名成功',
      type: 'success'
    })
  }

  emit('renameSuccess', result as string)
}

const handleDelete = async () => {
  const result = await alertDialog.confirm({
    title: '确定删除链接吗？',
    content: '删除后，账号下所有设备的对应链接将同步删除',
    variant: 'error',
    showTitleIcon: false,
    confirmText: '确认删除'
  })

  if (result !== false) {
    const res = await LinkRecordHelper.getInstance().removeLinkRecords(
      { linkIds: [props?.link.url_hash] })

    console.log('删除结果', res)

    if (res?.error?.message) {
      XMPMessage({
        message: res.error.message,
        type: 'error'
      })
    } else {
      XMPMessage({
        message: '删除成功',
        type: 'success'
      })

      emit('deleteSuccess')
    }

  }
}

const handleMenuSelect = (key: string) => {
  console.log('LinkItem handleMenuSelect', key)
  switch (key) {
    case 'rename':
      handleRename()
      break
    case 'delete':
      handleDelete()
      break
    case 'copy':
      clipboard.writeText(props.link.url)
      XMPMessage({
        message: '复制链接成功',
        type: 'success'
      })
      break
    case 'download':
      handleItemAction(ThunderNewTaskHelperNS.DownloadPathType.Local)
      break
    case 'cloud':
      handleItemAction(ThunderNewTaskHelperNS.DownloadPathType.Cloud)
      break
    case 'play':
      handleItemAction('playLink')
      break
    case 'open':
      handleItemAction('openLink')
      break
    default:
      break
  }
}

const formatAddTime = (addTime: number) => {
  const now = dayjs();
  const date = dayjs(addTime);
  if (now.diff(date, 'second') < 60) {
    return '刚刚';
  }
  if (now.isSame(date, 'day')) {
    return `今天 ${date.format('HH:mm')}`;
  }
  return date.format('YYYY-MM-DD HH:mm');
}

const disabled = computed(() => {
  return !!props?.link?.audit?.message
})

const isUnread = computed(() => {
  return props?.link?.status === 'STATUS_DEFERRED' && props.from === 'linkList'
})

const itemInfo = computed(() => {
  return `${formatAddTime(props?.link?.add_time)} 添加 · ${ThunderUtil.bytesToSize(props?.link?.size, 2)}${props?.link?.is_dir ? ` · 共 ${props?.link?.selected_count}/${props?.link?.child_count} 个文件` : ''}`
})

</script>

<template>
  <ContextMenu :items="contextMenuList" @select="handleMenuSelect">
    <div :class="[
      'link-item',
      { 'link-item-disabled': disabled, 'link-item-detail': from === 'linkDetail' },
      { 'highlighted': highlighted }
    ]" @dblclick="handleItemAction('selectLink')">
      <!-- 未读状态 -->
      <div class="link-item-unread" v-if="isUnread">
      </div>
      <div class="link-item-left">
        <div class="link-item-left-url">
          <Tooltip>
            <template #trigger>
              <img v-if="iconLink" :src="iconLink" class="link-item-left-url-icon" />
              <span v-else class="link-item-left-url-icon">
                <inline-svg :src="LINK_ICON_MAP[link.url_type].icon" />
              </span>
            </template>
            <template #content>
              <span>{{ LINK_ICON_MAP[link.url_type].tip }}</span>
            </template>
          </Tooltip>
          <span class="link-item-left-url-text">{{ link.url }}</span>
          <Tooltip side="top" show-arrow contentClass="link-item-tip-content" arrowClass="link-item-tip-arrow">
            <template #trigger>
              <i v-if="disabled" class="xl-icon-general-details-m"></i>
            </template>
            <template #content>
              {{ link?.audit?.message }}
            </template>
          </Tooltip>
        </div>

        <Tooltip :side-offset="25" :max-width="398" :align="'start'" :trigger-by-pointer="true" :disabled="disabled"
          :delay-duration="1000" triggerClass="link-item-left-title">
          <template #trigger>
            <span class="link-item-left-title" @click="handleItemAction('selectLink')" @dblclick.stop>
              {{ link.name }}
            </span>
          </template>
          <template #content>
            <span>{{ link.name }}</span>
          </template>
        </Tooltip>

        <span class="link-item-left-info">{{ itemInfo }}</span>
      </div>
      <div class="link-item-right">
        <template v-if="from === 'linkList'">
          <Button v-if="link.can_video_play" variant="ghost" :is-icon="true" size="sm" :disabled="disabled"
            @click="handleItemAction('playLink')" v-tooltip="{
              content: '播放',
              placement: 'bottom',
            }">
            <i class="xl-icon-general-play-m1"></i>
          </Button>
          <Button v-else variant="ghost" :is-icon="true" size="sm" :disabled="disabled"
            @click="handleItemAction('openLink')" @dblclick.stop v-tooltip="{
              content: '打开',
              placement: 'bottom',
            }">
            <i class="xl-icon-general-openfile-m"></i>
          </Button>
          <Button variant="ghost" :is-icon="true" size="sm" :disabled="disabled"
            @click="handleItemAction(ThunderNewTaskHelperNS.DownloadPathType.Local)" @dblclick.stop v-tooltip="{
              content: '下载',
              placement: 'bottom',
            }">
            <i class="xl-icon-general-download-l"></i>
          </Button>
          <Button variant="ghost" :is-icon="true" size="sm" :disabled="disabled"
            @click="handleItemAction(ThunderNewTaskHelperNS.DownloadPathType.Cloud)" @dblclick.stop v-tooltip="{
              content: '添加到云盘',
              placement: 'bottom',
            }">
            <i class="xl-icon-general-dump-m"></i>
          </Button>
        </template>
        <template v-if="from === 'linkDetail'">
          <DropdownMenu :items="contextMenuList" @select="handleMenuSelect" :align="'end'">
            <Button variant="outline" :is-icon="true" size="sm">
              <i class="xl-icon-general-more-m"></i>
            </Button>
          </DropdownMenu>
        </template>
      </div>
    </div>
  </ContextMenu>
</template>

<style scoped lang="scss">
.link-item {
  display: flex;
  align-items: center;
  padding: 0px 14px;
  border-radius: var(--border-radius-L, 12px);
  gap: 32px;
  position: relative;
  height: 90px;

  &:hover {
    cursor: pointer;
    background: var(--fill-fill-3, rgba(12, 24, 49, 0.04));
  }

  &-disabled {
    pointer-events: none;

    .xl-icon-general-details-m {
      color: var(--functional-error-default, #C9CDD4);
      pointer-events: auto;
    }

    &:hover {
      background: inherit;
    }

    .link-item-left-title {
      color: var(--font-font-3, #86909C);
    }

    .link-item-left-url {
      span {
        color: var(--font-font-4, #C9CDD4);
      }
    }
  }

  &-detail {
    padding: 10px 0 20px 0;

    &:hover {
      cursor: default;
      background: inherit;
    }

    .link-item-left-title {
      font-size: 14px;
      line-height: 20px;
      font-weight: 700;
      pointer-events: none;
    }
  }

  &.highlighted {
    background: var(--button-button-lead-default, rgba(34, 109, 245, 0.10));
  }

  &-unread {
    width: 6px;
    height: 6px;
    border-radius: 100%;
    background: var(--primary-primary-hover, #488BF7);
    position: absolute;
    left: 6px;
    top: 17px;
  }

  &-left {
    display: flex;
    flex-direction: column;
    gap: 3px;
    flex: 1;
    min-width: 0;

    &-url {
      display: flex;
      align-items: center;
      height: 16px;

      &-icon {
        width: 16px;
        height: 16px;
      }

      &-text {
        color: var(--font-font-3, #86909C);
        font-size: 11px;
        margin: 0 4px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      i {
        font-size: 12px;
      }
    }

    &-info {
      font-size: 12px;
      line-height: 20px;
      color: var(--font-font-3, #86909C);
    }

  }

  &-right {
    display: flex;
    align-items: center;
    gap: 16px;

    i {
      font-size: 20px;
    }
  }
}
</style>

<style lang="scss">
.link-item-left-title {
  font-size: 13px;
  line-height: 22px;
  color: var(--font-font-1, #272E3B);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: fit-content;
  max-width: 100%;

  &:hover {
    color: var(--primary-primary-default, #226DF5);
  }
}

.link-item-tip-content {
  background: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
  height: 32px !important;
  padding: 0 12px !important;
  color: var(--white-white-900, #FFF) !important;
  line-height: 20px !important;
}

.link-item-tip-arrow {
  fill: var(--background-background-tooltip-black, rgba(0, 0, 0, 0.80)) !important;
}
</style>
