/**
 * 二级弹框
 */
import { Point, Rectangle } from 'electron';
import * as PopUpTypes from '@root/common/pop-up/types';
import { mainProcessContext } from '@xunlei/node-net-ipc/dist/ipc-base';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';
import { AsyncRemoteCall } from '@root/common/renderer-async-remote-call';
import { ReuseBrowserWindowProxy } from '@root/common/pop-up/reuse-window-proxy';
import { Action, ResolvePayload } from './types';
import * as BaseType from '@root/common/task/base'

export namespace PopUpNS {
  let currentWindow_: ReuseBrowserWindowProxy;
  
  export function constructorCurrentWindow(id: number) {
    do {
      if (currentWindow_) {
        break;
      }
      id = Number(id);
      if (isNaN(id)) {
        break;
      }
      currentWindow_ = new ReuseBrowserWindowProxy(id);
    } while (0);
  }

  /** 
   * @description 获取当前窗口代理对象
  */
  export function getCurrentWindow(): ReuseBrowserWindowProxy {
    return currentWindow_!;
  }

  export async function fromId(id: number): Promise<ReuseBrowserWindowProxy | null> {
    const exist: boolean = (await clientModule.callRemoteClientFunction(mainProcessContext, 'ReuseCheckExist', id))?.[0];
    return exist ? new ReuseBrowserWindowProxy(id) : null;
  }

  export async function getSingletonWindow(componentName: string, parentId?: number): Promise<ReuseBrowserWindowProxy | null> {
    if (parentId === undefined) {
      parentId = getCurrentWindow()?.id;
    }
    if (parentId !== undefined && parentId !== null && parentId <= 0) {
      parentId = 0;
    }
    const wndId = (await clientModule.callRemoteClientFunction(mainProcessContext, 'GetExistReuseWndId', componentName, parentId))?.[0];
    return wndId ? new ReuseBrowserWindowProxy(wndId) : null;
  }

  export async function confirm(options?: PopUpTypes.ConfirmOptions, browserWindowOptions?: Electron.BrowserWindowConstructorOptions): Promise<PopUpTypes.ResolvePayload> {
    browserWindowOptions = browserWindowOptions ?? {};
    browserWindowOptions.width = browserWindowOptions.width ?? 400;
    browserWindowOptions.height = browserWindowOptions.height ?? 174;
    browserWindowOptions.modal = true;

    return (await clientModule.callRemoteClientFunction(mainProcessContext, 'CustomReuseWindow', 'Confirm', options ?? {}, browserWindowOptions))[0];
  }

  export async function newWindow(extra?: { parentId?: number, relatePos?: PopUpTypes.RelatePosType }, dialogConf?: Electron.BrowserViewConstructorOptions): Promise<ReuseBrowserWindowProxy | null> {
    const newWindowId: number = (await clientModule.callRemoteClientFunction(mainProcessContext, 'CreateReuseWindow', dialogConf, extra))?.[0];
    return newWindowId ? new ReuseBrowserWindowProxy(newWindowId) : null;
  }

  export async function popup(
    componentName: string,
    options: { parentId?: number; singleton?: boolean; windowWidth?: number; windowHeight?: number; relatePos?: PopUpTypes.RelatePosType, replaceView?: boolean, [prop: string]: any }, 
    dialogConf: Electron.BrowserWindowConstructorOptions = {},
  ): Promise<PopUpTypes.ResolvePayload> {
    console.log('componentName', componentName, options, dialogConf, )
    if (process.type === 'renderer' && typeof options?.parentId !== 'number') {
      options = options ?? {};
      options.parentId = getCurrentWindow()?.id;
    }

    let retry: number = 0;
    do {
      if (retry >= 10) {
        break;
      }

      let exist = await clientModule.checkRemoteFunction(mainProcessContext, 'CustomReuseWindow');
      if (exist) {
        break;
      }
      await new Promise((v) => {
        setTimeout(() => {
          v(true);
        }, 200);
      })
      retry++;
    } while(true);

    return (await clientModule.callRemoteClientFunction(mainProcessContext, 'CustomReuseWindow', componentName, options, dialogConf))[0];
  }

  /**
   * 注册窗口的快捷键，ctrl+f12打开控制台
   */
  export function enableDevTools(options?: Electron.OpenDevToolsOptions): void {
    do {
      if (process.type !== 'renderer') {
        break;
      }
      window.addEventListener('keyup', async (e: KeyboardEvent) => {
        if (e.key === 'F12' && e.ctrlKey && !e.shiftKey) {
          const currentWindow = await AsyncRemoteCall.GetInstance().getCurrentWindow();
          const webContents = currentWindow.webContents;
          if (await webContents.isDevToolsOpened()) {
            await webContents.closeDevTools()
          } else {
            await webContents.openDevTools(options);
          }
        } else if ((e.key === 't' || e.key === 'T') && e.altKey) {
          // let div: HTMLElement | null = document.getElementById('DevProcessPid');
          // if (div) {
          //   document.body.removeChild(div);
          // } else {
          //   div = document.createElement('p');
          //   div.id = 'DevProcessPid';
          //   div.style.position = 'absolute';
          //   div.style.left = '0px';
          //   div.style.top = '0px';
          //   div.style.width = '100%';
          //   div.style.zIndex = '10000';
          //   div.style.color = 'rgb(255,0,0)';
          //   document.body.appendChild(div);
          //   let debugInfo: string = 'process.pid:' + process.pid;
          //   debugInfo += '\r\nlocation.href:' + location.href;
          //   debugInfo += '\r\nprocess.argv:' + process.argv;
          //   div.innerText = debugInfo;
          // }
        }
      });
    } while (0);
  }

  /**
   * @description: 窗口基于父窗口居中，如果不存在父窗口，基于主窗口居中
   * @param windowId 可选，不传递则默认当前窗口id
   * @param parentWindowId 可选，取值优先权：传递的窗口id > 子窗口的父窗口id > 主窗口的id
   */
  export async function fitWindowPosInParent(width?: number, height?: number, windowId?: number, parentWindowId?: number): Promise<boolean> {
    let result: boolean = false;
    do {
      if (!windowId) {
        windowId = getCurrentWindow()?.id;
      }
      if (!windowId) {
        break;
      }
      result = (await clientModule.callRemoteClientFunction(mainProcessContext, 'FitWindowPosInParent', windowId, parentWindowId, width, height))?.[0];
    } while (0);
    
    return result;
  }

  /**
   * @description： 设置窗口位置到屏幕右下角
   * @param width 必填，指定窗口宽度
   * @param height 必填，指定窗口高度
   * @param windowId 选填，默认当前窗口id
   */
  export async function fitWindowPosRightBottom(width: number, height: number, windowId?: number): Promise<boolean> {
    let result: boolean = false;
    do {
      if (!windowId) {
        windowId = getCurrentWindow()?.id;
      }
      if (!windowId) {
        break;
      }
      result = (await clientModule.callRemoteClientFunction(mainProcessContext, 'ResizeWindowPos', windowId, { width, height }, PopUpTypes.RelatePosType.RightBottom))?.[0];
    } while (0);
    return result;
  }

  /**
   * @description： 更改窗口大小，如果未传递x,y，则基于当前窗口左上向下拉伸窗口到指定大小
   * @param width 必填，指定窗口宽度
   * @param height 必填，指定窗口高度
   * @param windowId 选填，默认当前窗口id
   */
  export async function resizeWindow(rect: Partial<Rectangle>, windowId?: number): Promise<boolean> {
    let result: boolean = false;
    do {
      if (!windowId) {
        windowId = getCurrentWindow()?.id;
      }
      if (!windowId) {
        break;
      }
      result = (await clientModule.callRemoteClientFunction(mainProcessContext, 'ResizeWindowPos', windowId, rect))?.[0];
    } while (0);
    return result;
  }

  export async function toWindowPoint(elePoint: Point): Promise<Point> {
    const point: Point = { x: elePoint.x, y: elePoint.y };
    do {
      // const currentWindow = getCurrentWindow();
      // const BrowserWindow: any = await AsyncRemoteCall.GetInstance().getBrowserWindow(); // tslint:disable-line
      // const browserWnd: any = await BrowserWindow.fromId(currentWindow.id);
      // if (!browserWnd) {
      //   break;
      // }

      // const bounds: Rectangle = await browserWnd.getBounds();
      // point.x = Math.round(bounds.x + point.x);
      // point.y = Math.round(bounds.y + point.y);
      point.x = Math.round(window.screenX + point.x);
      point.y = Math.round(window.screenY + point.y);
    } while (0);
    return point;
  }

  export async function showLoginDlg(): Promise<PopUpTypes.ResolvePayload> {
    return popup('login', { singleton: true }, { resizable: false, title: '迅雷登录', alwaysOnTop: true });
  }

  export async function showSettingDlg(): Promise<PopUpTypes.ResolvePayload> {
    return popup('setting', { singleton: true }, { resizable: false, title: '迅雷设置' });
  }

  export async function showLimitSpeedSettingDlg(): Promise<ResolvePayload> {
    return popup('limit-speed-setting', { singleton: true }, { resizable: false, title: '限速设置'});
  }

  export async function showDelAllInvalidTaskDlg(): Promise<ResolvePayload> {
    // TODO: Implement the actual dialog logic
    console.log('showDelAllInvalidTaskDlg called');
    return Promise.resolve({ action: Action.Close });
  }

  export async function showAddProxyDlg(proxyInfo?: any): Promise<PopUpTypes.ResolvePayload> {
    return popup('add-proxy', { singleton: true, proxyInfo }, { resizable: true, title: '添加代理'});
  }

  export async function showBrowserConfigGuid(proxyInfo?: any): Promise<PopUpTypes.ResolvePayload> {
    return popup('browser-config-guide', { singleton: true, proxyInfo }, { resizable: true, title: '千万用户正在使用插件下载支持'});
  }

  export async function showDownloadTaskMessageDlg(data: {
    title: string
    taskName: string
    infoText: string
    infoType: 'success' | 'error'
    fileIconClass: string
    confirmText?: string
    cancelText?: string
    showCancel?: boolean
    task: BaseType.TaskBase
  }): Promise<PopUpTypes.ResolvePayload> {
    return popup(
      'download-task-message',
      { data: data, singleton: true, replaceView: true },
      { resizable: false, title: data.title }
    );
  }
}