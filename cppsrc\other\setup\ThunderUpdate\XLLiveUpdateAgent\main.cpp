#include "stdafx.h"

#ifdef ETW_LOGGER

// {7c6ced0c-7100-4513-b411-8ebd202d012e}
static const GUID providerGuid =
{ 0x7c6ced0c, 0x7100, 0x4513,{ 0xb4, 0x11, 0x8e, 0xbd, 0x20, 0x2d, 0x01, 0x2e } };

ETWLogger g_etwLogger(providerGuid);

#endif

BOOL APIENTRY DllMain(
    HMODULE hModule,
    DWORD  ul_reason_for_call,
    LPVOID lpReserved
)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        GLogConfigure::instance().InitLog();
        break;
    case DLL_THREAD_ATTACH:
        break;
    case DLL_THREAD_DETACH:
        break;
    case DLL_PROCESS_DETACH:
        GLogConfigure::instance().UninitLog();
        break;
    }
    return TRUE;
}