/// <reference path="../impl/thunder-client-api.d.ts" />
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { 
    getThunderClient,
    EventDispatcher<PERSON>vent<PERSON>ey
 } from '../impl/thunder-client'

 
export class LinkHubServer {
    private static isinited_: boolean = false;
    static init() {
        if (this.isinited_) {
            return;
        }
        this.isinited_ = true;
        client.registerFunctions({
            LinkHubHelperGetAssociateCloudFile: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.GetAssociateCloudFileParam) => {
                return getThunderClient().getBizProvider().getLinkHubPresenter().getAssociateCloudFile(JSON.stringify(param));
            },
            LinkHubHelperSaveLink: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordParam) => {
                return getThunderClient().getBizProvider().getLinkHubPresenter().saveLink(JSON.stringify(param));
            },
            LinkHubHelperSaveLinks: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.InsertLinkRecordsParam) => {
                return getThunderClient().getBizProvider().getLinkHubPresenter().saveLinks(JSON.stringify(param));
            },
            LinkHubHelperGetLinks: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.GetLinkRecordsParam) => {
                return getThunderClient().getBizProvider().getLinkHubPresenter().getLinks(JSON.stringify(param));
            },
            LinkHubHelperSearchLinks: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.SearchLinkRecordsParam) => {
                return getThunderClient().getBizProvider().getLinkHubPresenter().searchLinks(JSON.stringify(param));
            },
            LinkHubHelperSyncLinksToServer: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.SyncLinksToServerParam) => {
                return getThunderClient().getBizProvider().getLinkHubPresenter().syncLinksToServer(JSON.stringify(param));
            },
            LinkHubHelperSavePlaybackRecord: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.InsertPlaybackRecordParam) => {
                return getThunderClient().getBizProvider().getLinkHubPresenter().savePlaybackRecord(JSON.stringify(param));
            },
            LinkHubHelperGetPlaybackRecords: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.GetPlaybackRecordsParam) => {
                return getThunderClient().getBizProvider().getLinkHubPresenter().getPlaybackRecords(JSON.stringify(param));
            },
            LinkRecordHelperUpdateLinkRecord: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.UpdateLinkRecordParam) => {
                return getThunderClient().getBizProvider().getLinkRecordListPresenter().updateLinkRecord(JSON.stringify(param));
            },
            LinkRecordHelperLoadLinkRecords: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.LoadLinkRecordsParam) => {
                return getThunderClient().getBizProvider().getLinkRecordListPresenter().loadLinkRecords(JSON.stringify(param));
            },
            LinkRecordHelperRemoveLinkRecords: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.RemoveLinkRecordsParam) => {
                return getThunderClient().getBizProvider().getLinkRecordListPresenter().removeLinkRecords(JSON.stringify(param));
            },
            LinkRecordHelperGetLabels: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.GetLabelsParam) => {
                return getThunderClient().getBizProvider().getLinkRecordListPresenter().getLabels(JSON.stringify(param));
            },
            LinkRecordHelperFetchFirstPage: (ctx: unknown, param: ThunderClientAPI.dataStruct.common.FetchFirstPageParam) => {
                return getThunderClient().getBizProvider().getLinkRecordListPresenter().fetchFirstPage(JSON.stringify(param));
            },
            LinkRecordHelperFetchNextPage: (ctx: unknown, param: ThunderClientAPI.dataStruct.common.FetchNextPageParam) => {
                return getThunderClient().getBizProvider().getLinkRecordListPresenter().fetchNextPage(JSON.stringify(param));
            },
            PlaybackRecordGetPlaybackRecordList: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.LoadPlaybackRecordsParam) => {
                return getThunderClient().getBizProvider().getPlaybackRecordPresenter().loadPlaybackRecords(JSON.stringify(param));
            },
            PlaybackRecordRemovePlaybackRecords: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.RemovePlaybackRecordsParam) => {
                return getThunderClient().getBizProvider().getPlaybackRecordPresenter().removePlaybackRecords(JSON.stringify(param));
            },
            PlaybackRecordRemoveAllPlaybackRecords: (ctx: unknown, param: ThunderClientAPI.dataStruct.dataModals.RemoveAllPlaybackRecordsParam) => {
                return getThunderClient().getBizProvider().getPlaybackRecordPresenter().removeAllPlaybackRecords(JSON.stringify(param));
            },
            PlaybackRecordFetchFirstPage: (ctx: unknown, param: ThunderClientAPI.dataStruct.common.FetchFirstPageParam) => {
                return getThunderClient().getBizProvider().getPlaybackRecordPresenter().fetchFirstPage(JSON.stringify(param));
            },
            PlaybackRecordFetchNextPage: (ctx: unknown, param: ThunderClientAPI.dataStruct.common.FetchNextPageParam) => {
                return getThunderClient().getBizProvider().getPlaybackRecordPresenter().fetchNextPage(JSON.stringify(param));
            },
            PlaybackRecordGetDetail: async (ctx: unknown, param: any) => {
                return (getThunderClient().getBizProvider().getPlaybackRecordPresenter() as any).getPlaybackRecordDetail(JSON.stringify(param));
            },
            
        });
        console.log('getEventDispatcher init!!!!!!!!!!!!!!!!!!!!!!!!!!')
        getThunderClient().getEventDispatcher().attachEvent(EventDispatcherEventKey.SYNC_CLIENT_EVENT_MESSAGE_ARRIVED, (originMessage) => {
            console.log('getEventDispatcher recevice!!!!!!!!!!!!!!!!!!!!!!!!!!')
            if (originMessage?.type) {
                client.broadcastEvent(originMessage.type, originMessage.detail);
            }
        });
    }
}