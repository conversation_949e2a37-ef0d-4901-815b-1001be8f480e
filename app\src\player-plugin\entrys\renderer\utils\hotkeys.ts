import { platform } from "@root/common/env"

export type TFunctionHotkey =
  | 'altKey'
  | 'ctrlKey'
  | 'metaKey'
  | 'shiftKey'
const FunctionHotkey = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey']
/**
 * 
 * @param event 
 * @param trueFunctionKeysParams 有按下的key键，没有传的则表示没有按下；若不传这个参数，表示没有按key键 
 * @returns 
 */
export function genFunctionHotkeyBool(
  event: KeyboardEvent,
  ...trueFunctionKeysParams: Array<TFunctionHotkey>
) {
  const trueFunctionKeyMap = trueFunctionKeysParams.reduce(
    (acc, cur) => {
      acc[cur as unknown as string] = true
      return acc
    },
    {} as Record<string, boolean>,
  )
  const res = Object.values(FunctionHotkey).every((functionKey) => {
    return event[functionKey] === !!trueFunctionKeyMap[functionKey]
  })
  return res
}

export type THotkey = Pick<KeyboardEvent, 'code'> & Partial<Pick<KeyboardEvent, TFunctionHotkey>>

export function transformHotkey(event: KeyboardEvent): string {
  let valueStr = ''

  const key = event.key
  switch (key) {
    case 'ArrowLeft': {
      valueStr = '←'
      break
    }
    case 'ArrowRight': {
      valueStr = '→'
      break
    }
    case 'ArrowUp': {
      valueStr = '↑'
      break
    }
    case 'ArrowDown': {
      valueStr = '↑'
      break
    }
    case 'Escape': {
      valueStr = 'Esc'
      break
    }

    case 'Backspace':
    case 'Delete': {
      return ''
    }

    // FunctionKey忽略
    case 'Alt':
    case 'Control':
    case 'Meta':
    case 'Shift': {
      break
    }

    default: {
      // if (event.shiftKey) {
      //   // shift + 1 = !
      //   valueStr = key
      //   // shift + 1 = 1, 只适用与0-9，其他字符需要手动映射
      //   // valueStr = String.fromCharCode(event.keyCode || event.which)
      // } else {
      //   valueStr = key
      // }

      // shift + 1 = !
      if (event.code.startsWith('Key')) {
        valueStr = event.code.split('Key')[1]
      } else {
        valueStr = key
      }

      break
    }
  }

  let functionKeyList: string[] = []
  if (platform.isWindows) {
    functionKeyList = [event.ctrlKey && 'Ctrl', event.altKey && 'Alt', event.shiftKey && 'Shift'].filter(Boolean) as string[]
  } else if (platform.isMacOS) {
    functionKeyList = [event.metaKey && 'Command', event.ctrlKey && 'Ctrl', event.altKey && 'Alt', event.shiftKey && 'Shift'].filter(Boolean) as string[]
  }

  return functionKeyList.concat(valueStr).join('+')
}