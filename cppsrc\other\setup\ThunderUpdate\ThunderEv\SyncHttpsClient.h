#pragma once

#include "SyncHttps.h"

class CSyncHttpsClient
{
public:
	CSyncHttpsClient(int nBufSize);
	~CSyncHttpsClient(void);

public:
	static int Init();
	static void UnInit();
public:

	void SetRequestID(int nRequestID)
	{
		m_nRequestID = nRequestID;
	}

public:
	bool GetData(const string& strUrl, const string& strHeader);

	bool PostData(const string& strUrl, const string& strHeader, const string& strPostData);
	bool PostData(const string& strUrl, const string& strHeader, const byte* pPostData, int nPostDataSize);

	bool IsBufferOverFlow() const
	{
		return m_https.IsBufferOverFlow();
	}
	const char* GetRecvData() const
	{
		return m_https.GetRecvData();
	}
	int GetRecvDataSize() const
	{
		return m_https.GetRecvDataSize();
	}
	int GetErrorCode() const
	{
		return m_https.GetErrorCode();
	}
	int GetStatusCode() const
	{
		return m_https.GetStatusCode();
	}
	void SetCAInfoFilePath(const string& strCAInfoFilePath)
	{
		return m_https.SetCAInfoFilePath(strCAInfoFilePath);
	}

private:
	static string ms_strCAFilePath;

	CSyncHttps m_https;
	int m_nRequestID;
};
