#include "stdafx.h"
#include "define.h"
#include <list>
#include "SeqAnimation.h"

#define theResouce AniResource::GetInstance()
typedef std::list<IAniElipse*>	AniList;
typedef std::list<IAniElipse*>::iterator AniListItor;

CSeqAnimation::CSeqAnimation(CControlUI* pUI)
	:m_type(Ani_Min)
	,m_bloop(false)
	,m_pUI(pUI)
	,m_curIndex(0)
	,m_bReserve(false)
	,m_bRunReserve(false)
{
	
}


CSeqAnimation::~CSeqAnimation()
{
}

void CSeqAnimation::OnElipse()
{
	TCHAR szFileName[MAX_PATH];
	int nTotal = theResouce.GetTotalFrame(m_type);

	if (m_bReserve)
	{
		if (m_curIndex == nTotal)
		{
			m_bRunReserve = true;
		}
		else if (m_curIndex == 1)
		{
			m_bRunReserve = false;
		}
		if (m_bRunReserve)
		{
			m_curIndex--;
		}
		else
		{
			m_curIndex++;
		}
	}
	else
	{
		m_curIndex++;
		if (m_curIndex > nTotal)
		{
			if (theResouce.GetLoop(m_type))
			{
				m_curIndex = theResouce.GetStartFrame(m_type);
			}
			else
			{
				m_curIndex = nTotal;
			}
		}
	}

	theResouce.GetBkImage(m_type, szFileName, MAX_PATH,m_curIndex);

	if (m_pUI)
	{
		m_pUI->SetForeImage(szFileName);
	}	
}

AniType CSeqAnimation::GetType()
{
	return m_type;
}

void CSeqAnimation::SetType(AniType type)
{
	m_type = type;
}

CControlUI * CSeqAnimation::GetOwner()
{
	return m_pUI;
}

int CSeqAnimation::GetIndex()
{
	return m_curIndex;
}

void CSeqAnimation::SetIndex(int index)
{
	m_curIndex = index;
}

void CSeqAnimation::SetReserve()
{
	m_bReserve = true;
}

CSeqAniMgr::CSeqAniMgr()
	:m_hwnd(NULL)
	,m_timerAni(0)
{
}

CSeqAniMgr::~CSeqAniMgr()
{
}

void CSeqAniMgr::Init(HWND hwnd)
{
	m_hwnd = hwnd;
}

void CSeqAniMgr::OnTimer()
{
	AniListItor it = m_anilist.begin();

	for (; it != m_anilist.end(); it++)
	{
		(*it)->OnElipse();
	}
}

void CSeqAniMgr::ShowAni(CControlUI*pUI, AniType type, bool bShow)
{
	AniListItor it = m_anilist.begin();
	bool bexist = false;
	IAniElipse * pObjExist = NULL;
	for (; it != m_anilist.end(); it++)
	{
		if ((*it)->GetOwner() == pUI)
		{
			bexist = true;
			pObjExist = (*it);
			if (!bShow)
			{
				delete (*it);
				m_anilist.erase(it);
				return;
			}			
		}
	}
	if (!bexist)
	{
		IAniElipse * pObj = new CSeqAnimation(pUI);
		pObj->SetType(type);		
		m_anilist.push_back(pObj);
	}
	else
	{
		int nStart = theResouce.GetStartFrame(type);
		pObjExist->SetType(type);
		pObjExist->SetIndex(nStart);
	}
	EnableTimer(pUI,bShow);
}

void CSeqAniMgr::EnableTimer(CControlUI*pUI,bool bShow)
{
	if (bShow)
	{
		if (m_timerAni == 0)
		{
			m_timerAni = ::SetTimer(m_hwnd, IDC_TIMER_ANI_TICK, 50, NULL);
		}
	}
	else
	{
		AniListItor it = m_anilist.begin();
		for (; it != m_anilist.end(); it++)
		{
			if ((*it)->GetOwner() == pUI)
			{
				m_anilist.erase(it);
				break;
			}
		}
		if (m_timerAni != 0 && m_anilist.size() == 0)
		{
			m_timerAni = ::KillTimer(m_hwnd, IDC_TIMER_ANI_TICK);
		}
	}
}

AniResource::AniResource()
{
}

AniResource::~AniResource()
{
}

AniResource & AniResource::GetInstance()
{
	static AniResource aniRes;
	return aniRes;
}

void AniResource::GetBkImage(AniType type, OUT TCHAR fileName[], int bufCount,int index)
{
	int nTotalFrame = GetTotalFrame(type);
	PTSTR szBaseName = _T("");

	m_lastType = type;
	switch (type)
	{
	case Ani_Checking:
		szBaseName = _T("checking\\AniChecking");
		break;
	case Ani_NewVersion:
		szBaseName = _T("alreadynew\\AniAlreadyNew");
		break;
	case Ani_AlreadyNew:
		szBaseName = _T("newver\\AniNewVer");
		break;
	case Ani_Updating:
		szBaseName = _T("updating\\AniUpdating");
		break;
	case Ani_UpdateSucc:
		szBaseName = _T("updsucc\\AniUpdateSucc");
		break;
	case Ani_UpdateFail:
		szBaseName = _T("updfail\\AniUpdateFail");
		break;
	case Ani_UpdateCloud:
		szBaseName = _T("cloud\\AniCloud");
		break;
	default:
		break;
	}
	_stprintf_s(fileName, bufCount, _T("%s%d.png"), szBaseName, index);
	return;
}

int AniResource::GetTotalFrame(AniType type)
{
	int nTotalFrame = 0;
	static int nCheckAni = 20;
	static int nNewVerAni = 5;
	static int nUpdateAni = 24;
	static int nAlreadyNewAni = 5;
	static int nUpdateSuccAni = 5;
	static int nUpdateFailAni = 5;
	static int nUpdateCloud = 44;
	switch (type)
	{
	case Ani_Min:
		break;
	case Ani_Checking:
		nTotalFrame = nCheckAni;
		break;
	case Ani_NewVersion:
		nTotalFrame = nNewVerAni;
		break;
	case Ani_AlreadyNew:
		nTotalFrame = nAlreadyNewAni;
		break;
	case Ani_Updating:
		nTotalFrame = nUpdateAni;
		break;
	case Ani_UpdateSucc:
		nTotalFrame = nUpdateSuccAni;
		break;
	case Ani_UpdateFail:
		nTotalFrame = nUpdateFailAni;
		break;
	case Ani_UpdateCloud:
		nTotalFrame = nUpdateCloud;
		break;
	case Ani_Max:
		break;
	default:
		break;
	}
	return nTotalFrame;
}
bool AniResource::GetLoop(AniType type)
{
	bool bloop = true;
	switch (type)
	{
	case Ani_Min:
		break;
	case Ani_Checking:
		bloop = true;
		break;
	case Ani_NewVersion:
		bloop = false;
		break;
	case Ani_AlreadyNew:
		bloop = false;
		break;
	case Ani_Updating:
		bloop = true;
		break;
	case Ani_UpdateSucc:
		bloop = false;
		break;
	case Ani_UpdateFail:
		bloop = false;
		break;
	case Ani_UpdateCloud:
		bloop = true;
		break;
	case Ani_Max:
		break;
	default:
		break;
	}
	return bloop;
}

int AniResource::GetStartFrame(AniType type)
{
	int nStart = 1;
	switch (type)
	{
	case Ani_Min:
		break;
	case Ani_Checking:
		nStart = 5;
		break;
	case Ani_NewVersion:
		nStart = 1;
		break;
	case Ani_AlreadyNew:
		nStart = 1;
		break;
	case Ani_Updating:
		nStart = 1;
		break;
	case Ani_UpdateSucc:
		nStart = 1;
		break;
	case Ani_UpdateFail:
		nStart = 1;
		break;
	case Ani_UpdateCloud:
		nStart = 1;
		break;
	case Ani_Max:
		break;
	default:
		break;
	}
	return nStart;
}
