#include "StdAfx.h"
#include "UpdateDownloader.h"

#include "atlapp.h"

#include "xl_lib/filesystem/path_utility.h"
#include "xl_lib/filesystem/dir_utility.h"

UpdateDownloader::UpdateDownloader(void)
:m_pFileDownloader(NULL),
m_pCurDownloadInfo(NULL),
m_hThread(0),
m_asynRet(false),
isDownloading(false)
{
    m_mainThreadId = ::GetCurrentThreadId();
}

UpdateDownloader::~UpdateDownloader(void)
{
}

void UpdateDownloader::Init(const wchar_t* tpPath, FILEDOWN_CALLBACK callBack, void* param)
{
    if (!m_pFileDownloader)
    {
        m_pFileDownloader = new FileDownloader();
        m_pFileDownloader->Init(tpPath);
    }
    if (!m_pCurDownloadInfo)
    {
        m_pCurDownloadInfo = new DownloadInfo();
    }
    m_callBack = callBack;
    m_callBackParam = param;
}

void UpdateDownloader::UnInit()
{
     LOG_IF(INFO, NEED_LOG) << "Enter:" << __func__;

    if (m_pFileDownloader)
    {
        m_pFileDownloader->UnInit();
        delete m_pFileDownloader;
        m_pFileDownloader = NULL;
    }

    if (isDownloading && m_hThread != NULL)
    {
        WARNING_XLOG(L"downloading, cancel");
        ::TerminateThread(m_hThread, 1);

        ::PostThreadMessage(m_mainThreadId, WM_QUIT, 0, 0);

        m_hThread = NULL;
    }

    if (m_pCurDownloadInfo)
    {
        delete m_pCurDownloadInfo;
        m_pCurDownloadInfo = NULL;
    }

    while (m_DownloadQueue.size())
    {
        DownloadInfo* pInfo = m_DownloadQueue.front();
        m_DownloadQueue.pop();
        delete pInfo;
    }

     LOG_IF(INFO, NEED_LOG) << "Exit:" << __func__;
}

void UpdateDownloader::QueryCurDownloadInfo(std::wstring& filePath, unsigned long& ulFileSize, unsigned long& ulDownloadedSize)
{
    filePath = L"";
    ulFileSize = 0;
    ulDownloadedSize = 0;
    if (m_pCurDownloadInfo)
    {
        filePath = m_pCurDownloadInfo->szFilePath;
    }
    if (m_pFileDownloader)
    {
        m_pFileDownloader->GetDownloadProgress(ulDownloadedSize, ulFileSize);
    }
}

bool UpdateDownloader::AsynDownloadFile(const char* url, const char* md5, const char* name, const wchar_t* filePath, bool isInsertRecord /* = true */, bool useHttpDownload)
{
     LOG_IF(INFO, NEED_LOG) << "Enter:" << __func__;

    if (isDownloading)
    {
        WARNING_XLOG(L"is downloading");
        return false;
    }

    isDownloading = true;

    DownloadInfo* pInfo = new DownloadInfo();
    pInfo->szUrl = url;
    if (md5)
    {
        pInfo->szMd5 = md5;
    }
    pInfo->szName = name;
    pInfo->szFilePath = filePath;
    pInfo->bIsInsertRecord = isInsertRecord;
    pInfo->useHttpDownload = useHttpDownload;

    m_DownloadQueue.push(pInfo);

    unsigned int ThreadId;
    m_hThread = (HANDLE)::_beginthreadex(NULL, 0, DownlaodThreadFunc, this, 0, &ThreadId);

    CMessageLoop loop;
    int nRet = loop.Run();

    m_hThread = NULL;
    isDownloading = false;

     LOG_IF(INFO, NEED_LOG) << "Exit:" << __func__;

    return m_asynRet;
}

unsigned int UpdateDownloader::DownlaodThreadFunc(void* pParam)
{
     LOG_IF(INFO, NEED_LOG) << "Enter:" << __func__;

    UpdateDownloader* pThis = (UpdateDownloader*)pParam;
    while (pThis->m_DownloadQueue.size())
    {
        DownloadInfo* pInfo = pThis->m_DownloadQueue.front();
        pThis->m_DownloadQueue.pop();
        if (pThis->m_pCurDownloadInfo)
        {
            delete pThis->m_pCurDownloadInfo;
            pThis->m_pCurDownloadInfo = NULL;
        }
        pThis->m_pCurDownloadInfo = pInfo;
        pThis->m_asynRet = pThis->m_pFileDownloader->Download(pInfo->szUrl.c_str(), pInfo->szUrl.c_str(), pInfo->szMd5.c_str(), pInfo->szName.c_str(), pInfo->szFilePath, pInfo->bIsInsertRecord, pInfo->useHttpDownload);
        if (pThis->m_callBack)
        {
            pThis->m_callBack(pThis->m_asynRet, pInfo->szFilePath.c_str(), pThis->m_callBackParam);
        }
    }

    ::PostThreadMessage(pThis->m_mainThreadId, WM_QUIT, 0, 0);

     LOG_IF(INFO, NEED_LOG) << "Exit:" << __func__;

    return 0;
}

bool UpdateDownloader::DownloadFile(const char* url, const char* httpurl, const char* md5, const char* name, std::wstring& filePath, bool isInsertRecord /* = true */, bool useHttpDownload)
{
   return m_pFileDownloader->Download(url, httpurl, md5, name, filePath, isInsertRecord, useHttpDownload);
}
