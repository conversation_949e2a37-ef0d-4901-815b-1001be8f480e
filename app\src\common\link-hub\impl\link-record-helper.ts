/// <reference path="../impl/thunder-client-api.d.ts" />
import { getThunderClient } from '../impl/thunder-client'

export class LinkRecord<PERSON>elper implements ThunderClientAPI.biz.ILinkListPresenter {
    private static instance: LinkRecordHelper | null = null;
    private constructor() {}
    public static getInstance(): LinkRecordHelper {
        if (!LinkRecordHelper.instance) {
            if (global.LinkRecordHelperClientInstance) {
                LinkRecordHelper.instance = global.LinkRecordHelperClientInstance;
            } else {
                LinkRecordHelper.instance = new LinkRecordHelper();
                global.LinkRecordHelperClientInstance = LinkRecordHelper.instance;
            }
        }
        return LinkRecordHelper.instance!;
    }

    public async fetchFirstPage(param:ThunderClientAPI.dataStruct.common.FetchFirstPageParam)
    : Promise<ThunderClientAPI.dataStruct.common.FetchFirstPageResult> {
        return getThunderClient().getBizProvider().getLinkRecordListPresenter().fetchFirstPage(JSON.stringify(param));
    }
    public async fetchNextPage(param: ThunderClientAPI.dataStruct.common.FetchNextPageParam)
    : Promise<ThunderClientAPI.dataStruct.common.FetchNextPageResult> {
        return getThunderClient().getBizProvider().getLinkRecordListPresenter().fetchNextPage(JSON.stringify(param));
    }
    public async getLabels(param: ThunderClientAPI.dataStruct.dataModals.GetLabelsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.GetLabelsResult> {
        return getThunderClient().getBizProvider().getLinkRecordListPresenter().getLabels(JSON.stringify(param));
    }

    public async updateLinkRecord(param: ThunderClientAPI.dataStruct.dataModals.UpdateLinkRecordParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.UpdateLinkRecordResult> {
        return getThunderClient().getBizProvider().getLinkRecordListPresenter().updateLinkRecord(JSON.stringify(param));
    }

    public async loadLinkRecords(param: ThunderClientAPI.dataStruct.dataModals.LoadLinkRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.LoadLinkRecordsResult> {
        return getThunderClient().getBizProvider().getLinkRecordListPresenter().loadLinkRecords(JSON.stringify(param));
    }

    public async removeLinkRecords(param: ThunderClientAPI.dataStruct.dataModals.RemoveLinkRecordsParam)
        : Promise<ThunderClientAPI.dataStruct.dataModals.RemoveLinkRecordsResult> {
        return getThunderClient().getBizProvider().getLinkRecordListPresenter().removeLinkRecords(JSON.stringify(param));
    }
}