import { contextBridge } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { CONST_A } from '@root/common/constant'

// Custom APIs for renderer
const api = {}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
console.log('preload', CONST_A)
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('__ELECTRON__', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.__ELECTRON__ = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
