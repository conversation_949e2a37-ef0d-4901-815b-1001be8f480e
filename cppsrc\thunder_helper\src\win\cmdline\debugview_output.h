#pragma once 

#ifndef DEBUG_VIEW_FILTER_IDENTIFY
#define DEBUG_VIEW_FILTER_IDENTIFY (_T("debugview_output") )
#endif

#ifdef OUTPUT_DEBUGVIEW

#include <windows.h>
#include <assert.h>
#include <tchar.h>

#define MAX_LOGGER_BUFFER (2048 * 10)

#define WIDEN2(x) L ## x
#define WIDEN(x) WIDEN2(x)
#define __WFILE__ WIDEN(__FILE__)
#define __WFUNCTION__ WIDEN(__FUNCTION__)

#ifdef _UNICODE
#define __TFILE__ __WFILE__
#define __TFUNCTION__ __WFUNCTION__
#else
#define __TFILE__ __FILE__
#define __TFUNCTION__ __FUNCTION__
#endif

#define OutputDebugLastError()       \
{									 \
	DWORD errid = ::GetLastError();	 \
	TCHAR * buf = new TCHAR[MAX_LOGGER_BUFFER];			 \
	memset(buf, 0, MAX_LOGGER_BUFFER * sizeof(TCHAR) );  \
	_stprintf_s(buf, MAX_LOGGER_BUFFER, _T("%s:[%s - %s - %d] errorid=%u\n"), DEBUG_VIEW_FILTER_IDENTIFY, __TFILE__, __TFUNCTION__, __LINE__, errid); \
	OutputDebugString(buf); 																														  \
	delete []buf;																																	  \
}

inline TCHAR * VaAargs(const TCHAR * x, ...)			
{	
	TCHAR * pszMsg = NULL;

	assert(x != NULL);                  
	if(x != NULL)                       
	{                                           
		pszMsg = new TCHAR[MAX_LOGGER_BUFFER]; 
		memset(pszMsg, 0, MAX_LOGGER_BUFFER * sizeof(TCHAR) );

		va_list pArg;                           
		va_start(pArg, x);						
		_vstprintf_s(pszMsg, MAX_LOGGER_BUFFER, x, pArg);
		va_end(pArg); 
	}	

	return pszMsg;
}

#define OutputDebugMsg(x, ...) \
{							   \
	TCHAR * pszMsg = VaAargs(x, __VA_ARGS__);			 \
	TCHAR * buf = new TCHAR[MAX_LOGGER_BUFFER];			 \
	memset(buf, 0, MAX_LOGGER_BUFFER * sizeof(TCHAR) );  \
	_stprintf_s(buf, MAX_LOGGER_BUFFER, _T("%s:[%s - %s - %d] message=%s\n"), DEBUG_VIEW_FILTER_IDENTIFY, __TFILE__, __TFUNCTION__, __LINE__, pszMsg); \
	delete []pszMsg;																																   \
	OutputDebugString(buf);																															   \
	delete []buf;																																	   \
}




#else

#define OutputDebugLastError()
#define OutputDebugMsg(x, ...)

#endif