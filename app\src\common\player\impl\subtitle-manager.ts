import * as BaseType from '../base'

export class AplayerSubTitleManager {
    private nativeObj: any;
    constructor(obj: any) {
        this.nativeObj = obj;
    }
    public setPosition(pos: number): void {
        this.nativeObj.setPosition(pos);
    }
    public getPosition(): number {
        return this.nativeObj.getPosition();
    }
    public setTimming(num: number): void {
        this.nativeObj.setTimming(num);
    }
    public getTimming(): number {
        return this.nativeObj.getTimming();
    }
    public setVisible(visible: boolean): void {
        this.nativeObj.setVisible(visible);
    }
    public getVisible(): boolean {
        return this.nativeObj.getVisible();
    }
    public setFontSize(size: number): void {
        this.nativeObj.setFontSize(size);
    }
    public setFontColor(fontColor: string): void {
        this.nativeObj.setFontColor(fontColor);
    }
    public setFontFamily(fontFamily: string): void {
        this.nativeObj.setFontFamily(fontFamily);
    }
    public setFontStyle(fontStyle: BaseType.SubtitleFontStyle) {
        this.nativeObj.setFontStyle(fontStyle);
    }
    public getFontStyle(): BaseType.SubtitleFontStyle {
        return this.nativeObj.getFontStyle();
    }
    public getList(category: BaseType.SubtitleCategory): BaseType.SubtitleItemDisplayInfo[] {
        return this.nativeObj.getList(category);
    }
    public getSubtitleCategoryItemCount(): number {
        return this.nativeObj.getSubtitleCategoryItemCount();
    }
    public getSubtitleById(id: string): BaseType.SubtitleItemDisplayInfo {
        return this.nativeObj.getSubtitleById(id);
    }

    public attachPreparedEvent(cb: () => void): number {
        return this.nativeObj.attachPreparedEvent(cb);
    }
    public detachPreparedEvent(cookie: number): void {
        this.nativeObj.detachPreparedEvent(cookie);
    }
    public searchOnline(cb: (success: boolean) => void): void {
        this.nativeObj.searchOnline(cb);
    }
    public downloadOnlineSubtitle(id: string, cb: (success: boolean) => void): void {
        this.nativeObj.downloadOnlineSubtitle(id,cb);
    }
    public addLocal(filePath: string, cb: (success: boolean) => void): void {
        this.nativeObj.addLocal(filePath, cb);
    }
    public addPan(panId: string, fileName: string, cb: (success: boolean) => void): void {
        this.nativeObj.addPan(panId, fileName, cb);
    }
    public select(id: string): void {
        this.nativeObj.select(id);
    }
    public getSelect(): string {
        return this.nativeObj.getSelect();
    }
    public getSelectSubtitleCategory(): BaseType.SubtitleCategory {
        return this.nativeObj.getSelectSubtitleCategory();
    }
    public attachSelectChangeEvent(cb: (id: string, type: BaseType.SubtitleCategory, isAuto: boolean) => void): number {
        return this.nativeObj.attachSelectChangeEvent(cb);
    }
    public detachSelectChangeEvent(cookie: number): void {
        this.nativeObj.detachSelectChangeEvent(cookie);
    }
    public attachManualAdd(cb: (id: string) => void): number {
        return this.nativeObj.attachManualAdd(cb);
    }
    public detachManualAdd(cookie: number): void {
        this.nativeObj.detachManualAdd(cookie);
    }
}