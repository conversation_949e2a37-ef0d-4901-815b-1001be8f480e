#pragma once
#include "UninstallConfig.h"

class CUninstallBaseTool
{
public:
	static HRESULT RegisterDll(LPCTSTR lpszDllFilePath, BOOL bSilent, HANDLE* phPorcess);
	static HRESULT UnregisterDll(LPCTSTR lpszDllFilePath, BOOL bSilent, HANDLE* phPorcess);

	static HRESULT CreateShortcut(const ShortcutInfo* pShortcutInfo);
	static HRESULT DeleteShortcut(LPCTSTR lpszLinkFile);

	static HRESULT CleanFile(const CleanFileInfo* pCleanFileInfo);

	static HRESULT CopyFile(const CopyFileInfo* pCopyFileInfo);

	static HRESULT MoveFile(const MoveFileInfo* pMoveFileInfo);

	static HRESULT WriteRegValue(const RegInfo* pRegInfo);
	static HRESULT DeleteRegValue(const RegInfo* pRegInfo);

	static HRESULT WriteProfile(const IniInfo* pIniInfo);
};
