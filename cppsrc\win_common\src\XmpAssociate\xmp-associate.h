#ifndef XMP_PLAYER_XMP_ASSOCIATE_XMP_ASSOCIATE_H
#define XMP_PLAYER_XMP_ASSOCIATE_XMP_ASSOCIATE_H
#include <string>

class XmpAssociate
{
public:
	XmpAssociate()
	{

	}
	virtual ~XmpAssociate()
	{

	}

public:
	static bool Associate(const std::string &assoc_files, const std::string &not_assoc_files, bool set_ico = true, bool force_change = true, bool write_reg = true);
	static bool Associate(const std::string &strCmd, bool bForce);
	static void IsFileAssociatedWithXMP(const std::string &file_ext, bool &associated);
	static void IsFileAssociated(const std::string &file_ext, bool &associated);
	static bool GetFileAssociatedApp(const std::string& file_ext, std::string& app);
};

#endif // XMP_PLAYER_XMP_ASSOCIATE_XMP_ASSOCIATE_H
