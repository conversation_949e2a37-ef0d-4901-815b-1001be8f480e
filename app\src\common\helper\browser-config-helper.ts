/**
 * @description: 浏览器配置帮助类
 * @author: Assistant
 * @version: 1.0
 */

// 浏览器图标映射
const browserIcons: Record<string, string> = {
  'chrome': require('@root/common/assets/images/browser/logo-chrome.png'),
  '360se': require('@root/common/assets/images/browser/logo-360.png'),
  'edge': require('@root/common/assets/images/browser/logo-edge.png'),
}

export interface IBrowserInfo {
  browserExist: boolean; // 浏览器是否存在
  browserTitle: string; // 浏览器标题
  browserPath: string; // 浏览器路径
  browserAddonInstall: boolean; // 浏览器插件是否安装
  browserAddonOpen: boolean; // 浏览器插件是否开启
  browserAddonConfigPath: string; // 浏览器插件配置路径
  installCmdLine: string; // 安装命令
  openCmdLine: string; // 打开命令
  icon: string; // 浏览器图标
  logo: string; // 浏览器logo
}

export interface IMoreInfo {
  icon: string;
  name: string;
  default: boolean;
  install: boolean;
  open: boolean;
  exeName: string;
  logo: string;
  cooperate: boolean;
}

export interface ITipsInfo {
  show: boolean;
  title: string;
  browserInfo: IBrowserInfo;
}

export namespace BrowserHelperNs {
  
  class BrowserHelper {
    private static instance: BrowserHelper;

    public static getInstance(): BrowserHelper {
      if (!BrowserHelper.instance) {
        BrowserHelper.instance = new BrowserHelper();
      }
      return BrowserHelper.instance;
    }

    /**
     * 获取浏览器信息
     */
    public async getInfos(forceUpdate: boolean = false): Promise<Map<string, IBrowserInfo>> {
      const browserInfos = new Map<string, IBrowserInfo>();
      
      // 模拟浏览器信息，实际应该通过native调用获取
      const browsers = [
        {
          name: 'chrome',
          title: 'Chrome浏览器',
          exist: true,
          addonInstall: false,
          addonOpen: false,
          icon: 'xl-icon-browser-chrome',
          logo: browserIcons.chrome
        },
        {
          name: '360se',
          title: '360安全浏览器',
          exist: true,
          addonInstall: false,
          addonOpen: false,
          icon: 'xl-icon-browser-360',
          logo: browserIcons['360se']
        },
        {
          name: 'edge',
          title: 'Edge浏览器',
          exist: true,
          addonInstall: false,
          addonOpen: false,
          icon: 'xl-icon-browser-edge',
          logo: browserIcons.edge
        },
      ];

      browsers.forEach(browser => {
        browserInfos.set(browser.name, {
          browserExist: browser.exist,
          browserTitle: browser.title,
          browserPath: '',
          browserAddonInstall: browser.addonInstall,
          browserAddonOpen: browser.addonOpen,
          browserAddonConfigPath: '',
          installCmdLine: '',
          openCmdLine: '',
          icon: browser.icon,
          logo: browser.logo
        });
      });

      return browserInfos;
    }

    /**
     * 获取默认浏览器路径
     */
    public getDefaultBrowserPath(): string {
      // 实际应该通过native调用获取
      return 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe';
    }

    /**
     * 获取提示信息
     */
    public async getShowTipInfo(): Promise<ITipsInfo | null> {
      const browserInfos = await this.getInfos();
      
      // 查找需要显示提示的浏览器
      for (const [name, info] of browserInfos) {
        if (info.browserExist && (!info.browserAddonInstall || !info.browserAddonOpen)) {
          return {
            show: true,
            title: info.browserTitle,
            browserInfo: info
          };
        }
      }

      return null;
    }

    /**
     * 检查扩展更新
     */
    public checkExtensionCrxUpdate(): void {
      console.log('检查浏览器扩展更新');
      // 实际实现检查扩展更新逻辑
    }

    /**
     * 重置插件
     */
    public async resetAddons(): Promise<void> {
      console.log('重置浏览器插件');
      // 实际实现重置插件逻辑
    }
  }

  export function getInstance(): BrowserHelper {
    return BrowserHelper.getInstance();
  }

  export async function getInfos(forceUpdate: boolean = false): Promise<Map<string, IBrowserInfo>> {
    return getInstance().getInfos(forceUpdate);
  }

  export function getDefaultBrowserPath(): string {
    return getInstance().getDefaultBrowserPath();
  }

  export async function getShowTipInfo(): Promise<ITipsInfo | null> {
    return getInstance().getShowTipInfo();
  }
} 