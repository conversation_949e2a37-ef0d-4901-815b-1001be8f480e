<script lang="ts" setup>
import { ref, watch, onUnmounted, onMounted, reactive, nextTick } from 'vue'
import Checkbox from '@root/common/components/ui/checkbox/index.vue'
import Button from '@root/common/components/ui/button/index.vue'


export interface IFilterMap {
  val: any
  label: string
}

export interface IFiltersSource {
  title: string
  filters: IFilterMap[]
  checkAllTitle: string
}

export interface IFiltersCategoryData {
  type: string
  filters: string[]
  isCheckAll: boolean
}

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'confirm', result: IFiltersCategoryData[]): void
  (e: 'reset'): void
}>()

const props = withDefaults(defineProps<{
  filtersSource: IFiltersSource[]
  filtersCategoryData: IFiltersCategoryData[]
}>(), {
})

const filterCategoryResponse = reactive<IFiltersCategoryData[]>(JSON.parse(JSON.stringify(props.filtersCategoryData)))

const downloadFilterPop = ref<HTMLElement | null>(null)
const eventListeners = ref<{ type: string; handler: (e: Event) => void; }[]>([])

const isNode = (target: EventTarget | null): target is Node => {
  return target !== null && 'contains' in target;
};

const bindGlobalEvents = () => {
  // 监听文档的点击、滚动、触摸移动事件
  ['click', 'scroll', 'touchmove', 'contextmenu'].forEach(event => {
    const handler = (e: Event) => {
      // 若点击目标是菜单自身，不隐藏
      if (downloadFilterPop.value && isNode(e.target) && downloadFilterPop.value.contains(e.target)) return;
      handleClose();
    }
    // 存储事件监听器引用
    eventListeners.value.push({
      type: event,
      handler: handler
    });
    document.addEventListener(event, handler);
  });
}

// 解绑全局事件
const unbindGlobalEvents = () => {
  // 移除所有存储的事件监听器
  eventListeners.value.forEach(({ type, handler }) => {
    document.removeEventListener(type, handler);
  });

  // 清空事件监听器数组
  eventListeners.value = [];
}

function handleReset() {
  filterCategoryResponse.forEach(category => {
    category.filters = []
    category.isCheckAll = true
  })
}

function handleClose() {
  emit('close')
}

function handleConfirm() {
  emit('confirm', filterCategoryResponse)
}

async function handleCategoryCheckAll (sourceIndex, isCheckAll) {
  const categoryData = filterCategoryResponse[sourceIndex]

  if (isCheckAll) {
    categoryData.filters = []
    categoryData.isCheckAll = true
  } else if (!categoryData.filters.length) {
    // 需要模拟状态变化，才能让 checkbox 一直选中
    categoryData.isCheckAll = false
    await nextTick()
    categoryData.isCheckAll = true
  } else {
    categoryData.isCheckAll = isCheckAll
  }
}

function handleCategoryCheckItem (sourceIndex, sourceItem, isCheck) {
  const categoryData = filterCategoryResponse[sourceIndex]

  if (isCheck) {
    categoryData.filters.push(sourceItem.val)
  } else {
    categoryData.filters = categoryData.filters.filter(i => i !== sourceItem.val)
  }

  categoryData.isCheckAll = !categoryData.filters.length
}

onMounted(() => {
  setTimeout(() => {
    bindGlobalEvents()
  }, 500)
})

onUnmounted(() => {
  unbindGlobalEvents()
})
</script>

<template>
  <div class="filter-pop" ref="downloadFilterPop">
    <div class="filter-pop-header">
      <div class="filter-pop-title">
        <span>添加筛选条件</span>
      </div>
    </div>

    <div class="filter-btn-content">
      <div
        v-for="(sourceItem, sourceIndex) of filtersSource"
        class="filter-btn-item"
        :key="sourceItem.title"
      >
        <div class="filter-btn-item-title">{{ sourceItem.title }}</div>
        <div class="filter-btn-item-content">
          <div class="filter-btn-item-all">
            <Checkbox
              :model-value="filterCategoryResponse[sourceIndex].isCheckAll"
              @update:model-value="(isCheckAll) => handleCategoryCheckAll(sourceIndex, isCheckAll)"
            >
              {{ sourceItem.checkAllTitle }}
            </Checkbox>
          </div>

          <div class="filter-btn-item-content__list">
            <Checkbox
              v-for="item of sourceItem.filters"
              class="filter-btn-item__checkbox-wrapper"
              :model-value="filterCategoryResponse[sourceIndex].filters.includes(item.val)"
              :label="item.val"
              :key="item.val"
              @update:model-value="(isCheck) => handleCategoryCheckItem(sourceIndex, item, isCheck)"
            >
              {{ item.label }}
            </Checkbox>
          </div>
        </div>
      </div>
    </div>

    <div class="filter-pop-footer">
      <Button class="footer-btn" size="sm" variant="outline" @click="handleReset">
        重置
      </Button>

      <div class="right-btn-group">
        <Button class="footer-btn" size="sm" variant="secondary" @click="handleClose">
          取消
        </Button>

        <Button
          class="footer-btn"
          size="sm"
          variant="default"
          @click="handleConfirm"
        >
          确认
        </Button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.filter-pop {
  position: absolute;
  top: 40px;
  right: 0;
  width: 428px;
  border-radius: var(--border-radius-M);
  box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
  background: var(--background-background-container);
  padding: 0 18px 18px 18px;
  z-index: 1;

  .filter-pop-title {
    color: var(--font-font-1);
    font-weight: 700;
    font-size: 13px;
    height: 58px;
    line-height: 58px;
  }

  .filter-btn-item {
    margin-top: 24px;

    &:first-of-type {
      margin-top: 0;
    }

    .filter-btn-item-title {
      color: var(--font-font-3);
      font-size: 13px;
      line-height: 22px;
    }

    .filter-btn-item-content {
      margin-top: 18px;
    }
  }

  .filter-btn-item-all {
    margin-bottom: 18px;
  }

  .filter-btn-item-content__list {
    display: flex;
    flex-wrap: wrap;
    gap: 18px 24px;
  }

  .filter-btn-item__checkbox-wrapper {
    width: 80px;
    height: 22px;
  }

  .filter-pop-footer {
    margin-top: 18px;
    padding-top: 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .footer-btn {
      padding-left: 22px;
      padding-right: 22px;
    }

    .right-btn-group {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
}
</style>