/**
 * @description: 通用本地存储管理
 * @author: 迅雷
 */

import * as path from 'path';
import { FileSystemAWNS } from '@root/common/fs-utilities';
import { GetProfilesPath } from './xxx-node-path';

/**
 * 存储项接口
 */
export interface IStorageItem<T = any> {
  key: string;       // 唯一键
  value: T;          // 存储的值
  timestamp: number; // 时间戳
  type?: string;     // 可选类型标记
}

/**
 * 存储配置接口
 */
export interface IStorageConfig {
  name: string;         // 存储名称，用于文件名
  maxItems?: number;    // 最大存储项数量，默认无限制
  directory?: string;   // 自定义存储目录，默认为profiles
}

/**
 * 通用本地存储类
 */
export class LocalStorage<T = any> {
  private static instances: Map<string, LocalStorage<any>> = new Map();
  private configFile: string = '';
  private configPath: string = '';
  private items: IStorageItem<T>[] = [];
  private isLoaded: boolean = false;
  private maxItems: number = 0;
  private name: string = '';

  /**
   * 构造函数
   * @param config 存储配置
   */
  private constructor(config: IStorageConfig) {
    try {
      this.name = config.name;
      this.maxItems = config.maxItems || 0; // 0表示无限制
      
      // 确定存储目录
      const baseDir = config.directory || GetProfilesPath();
      this.configPath = baseDir;
      this.configFile = path.join(this.configPath, `${config.name}.json`);
      
      // 初始化加载
      this.loadItems();
    } catch (err) {
      console.error(`[${config.name}] 构造函数错误:`, err);
    }
  }

  /**
   * 获取单例实例
   * @param config 存储配置
   */
  public static getInstance<T>(config: IStorageConfig): LocalStorage<T> {
    if (!this.instances.has(config.name)) {
      this.instances.set(config.name, new LocalStorage<T>(config));
    }
    return this.instances.get(config.name) as LocalStorage<T>;
  }

  /**
   * 加载存储项
   */
  private async loadItems(): Promise<void> {
    try {
      // 确保目录存在
      if (!(await FileSystemAWNS.dirExistsAW(this.configPath))) {
        await FileSystemAWNS.mkdirsAW(this.configPath);
      }

      // 检查文件是否存在
      if (await FileSystemAWNS.existsAW(this.configFile)) {
        const data = await FileSystemAWNS.readFileAW(this.configFile, 'utf8');
        if (data) {
          try {
            this.items = JSON.parse(data.toString());
            console.log(`[${this.name}] 加载数据成功, 数量:`, this.items.length);
          } catch (err) {
            console.warn(`[${this.name}] 解析数据文件失败:`, err);
            this.items = [];
          }
        }
      } else {
        console.log(`[${this.name}] 数据文件不存在, 创建空列表`);
        this.items = [];
        await this.saveItems();
      }
      this.isLoaded = true;
    } catch (err) {
      console.error(`[${this.name}] 加载数据错误:`, err);
      this.items = [];
      this.isLoaded = true;
    }
  }

  /**
   * 保存存储项到文件
   */
  private async saveItems(): Promise<boolean> {
    try {
      // 确保目录存在
      if (!(await FileSystemAWNS.dirExistsAW(this.configPath))) {
        await FileSystemAWNS.mkdirsAW(this.configPath);
      }
      
      // 写入文件
      const result = await FileSystemAWNS.writeFileAW(
        this.configFile, 
        JSON.stringify(this.items, null, 2)
      );
      
      return result;
    } catch (err) {
      console.error(`[${this.name}] 保存数据错误:`, err);
      return false;
    }
  }

  /**
   * 等待数据加载完成
   */
  private async waitForLoad(): Promise<void> {
    if (this.isLoaded) {
      return;
    }

    return new Promise<void>((resolve) => {
      const checkLoaded = () => {
        if (this.isLoaded) {
          resolve();
        } else {
          setTimeout(checkLoaded, 100);
        }
      };
      checkLoaded();
    });
  }

  /**
   * 设置存储项
   * @param key 键
   * @param value 值
   * @param type 类型（可选）
   */
  public async setItem(key: string, value: T, type?: string): Promise<boolean> {
    await this.waitForLoad();

    if (!key || key.trim() === '') {
      return false;
    }

    try {
      // 查找是否已存在相同键
      const index = this.items.findIndex(item => item.key === key);
      
      // 如果已存在，更新值和时间并移到列表前面
      if (index !== -1) {
        const item = this.items[index];
        item.value = value;
        item.timestamp = Date.now();
        if (type !== undefined) {
          item.type = type;
        }
        this.items.splice(index, 1);
        this.items.unshift(item);
      } else {
        // 添加新记录
        const newItem: IStorageItem<T> = {
          key: key,
          value: value,
          timestamp: Date.now(),
          type: type
        };
        
        this.items.unshift(newItem);
        
        // 限制最大数量
        if (this.maxItems > 0 && this.items.length > this.maxItems) {
          this.items = this.items.slice(0, this.maxItems);
        }
      }
      
      // 保存到文件
      return await this.saveItems();
    } catch (err) {
      console.error(`[${this.name}] 设置数据错误:`, err);
      return false;
    }
  }

  /**
   * 获取存储项
   * @param key 键
   * @param defaultValue 默认值
   */
  public async getItem(key: string, defaultValue?: T): Promise<T | undefined> {
    await this.waitForLoad();
    
    try {
      const item = this.items.find(item => item.key === key);
      return item ? item.value : defaultValue;
    } catch (err) {
      console.error(`[${this.name}] 获取数据错误:`, err);
      return defaultValue;
    }
  }

  /**
   * 删除存储项
   * @param key 键
   */
  public async removeItem(key: string): Promise<boolean> {
    await this.waitForLoad();
    
    try {
      const index = this.items.findIndex(item => item.key === key);
      if (index !== -1) {
        this.items.splice(index, 1);
        return await this.saveItems();
      }
      return false;
    } catch (err) {
      console.error(`[${this.name}] 删除数据错误:`, err);
      return false;
    }
  }

  /**
   * 清空所有存储项
   */
  public async clear(): Promise<boolean> {
    await this.waitForLoad();
    
    try {
      this.items = [];
      return await this.saveItems();
    } catch (err) {
      console.error(`[${this.name}] 清空数据错误:`, err);
      return false;
    }
  }

  /**
   * 获取所有存储项
   */
  public async getAllItems(): Promise<IStorageItem<T>[]> {
    await this.waitForLoad();
    return [...this.items];
  }

  /**
   * 根据类型获取存储项
   * @param type 类型
   */
  public async getItemsByType(type: string): Promise<IStorageItem<T>[]> {
    await this.waitForLoad();
    return this.items.filter(item => item.type === type);
  }

  /**
   * 获取存储项数量
   */
  public async getItemCount(): Promise<number> {
    await this.waitForLoad();
    return this.items.length;
  }

  /**
   * 获取所有键
   */
  public async getAllKeys(): Promise<string[]> {
    await this.waitForLoad();
    return this.items.map(item => item.key);
  }

  /**
   * 检查键是否存在
   * @param key 键
   */
  public async hasKey(key: string): Promise<boolean> {
    await this.waitForLoad();
    return this.items.some(item => item.key === key);
  }
}


// 使用示例:
/*
// 创建一个用于存储用户设置的存储实例
const userSettings = LocalStorage.getInstance<Record<string, any>>({
  name: 'UserSettings',
  directory: path.join(process.cwd(), 'bin', 'profiles')
});

// 存储设置
await userSettings.setItem('theme', { mode: 'dark', accent: 'blue' });
await userSettings.setItem('language', 'zh-CN');

// 获取设置
const theme = await userSettings.getItem('theme');
const language = await userSettings.getItem('language', 'en-US'); // 提供默认值

// 检查设置是否存在
const hasNotifications = await userSettings.hasKey('notifications');

// 删除设置
await userSettings.removeItem('language');

// 清空所有设置
await userSettings.clear();
*/
