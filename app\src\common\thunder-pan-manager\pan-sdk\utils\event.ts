type Listener = (...args: any[]) => void;

class EventEmitter {
  private events: { [key: string]: Listener[] };

  constructor() {
    this.events = {};
  }

  /**
   * 监听事件，返回对应回调的在列表中的下标
   * @param event 事件名
   * @param listener 事件回调处理函数
   * @returns eventId
   */
  on(event: string, listener: Listener) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);

    return this.events[event].length - 1;
  }

  once(event: string, listener: Listener) {
    let eventId = -1;
    const onceListener: Listener = (...args: any[]) => {
      try {
        listener(...args);
      } catch { }
      this.off(event, eventId);
    };
    eventId = this.on(event, onceListener);
  }

  /**
   * 触发事件
   * @param event 事件名
   * @param args 回调传递的参数
   */
  emit(event: string, ...args: any[]) {
    const listeners = this.events[event];
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(...args);
        } catch { }
      }
    }
  }

  /**
   * 取消监听事件
   * @param event 事件名
   * @param eventId 事件 id
   */
  off(event: string, eventId: number) {
    const listeners = this.events[event];
    if (listeners) {
      this.events[event].splice(eventId, 1);
    }
  }

  /**
   * 取消该事件下所有的监听
   * @param event 事件名
   */
  offAll (event: string) {
    const listeners = this.events[event];
    if (listeners) {
      this.events[event] = [];
    }
  }
}

export default EventEmitter;