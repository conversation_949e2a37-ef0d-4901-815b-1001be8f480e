import { isPlainObject } from './checkTypes'

type VariableType = 'array' | 'object'
function ensureExist(val, type: VariableType, isFill?: boolean) {
  switch (type) {
    case 'array':
      return Array.isArray(val) ? val : isFill && val ? [val] : []
    case 'object':
      return isPlainObject(val) ? val : Object.create(null)
    default:
      return typeof val !== 'undefined' ? val : null
  }
}

export function ensureObject<T = any>(source: T) {
  return ensureExist(source, 'object') as T
}
export function ensureArray<T = any>(source: T[], isFill?: boolean) {
  return ensureExist(source, 'array', isFill) as T[]
}
export function ensureString(source): string {
  if (typeof source === 'string') {
    return source
  } else if (typeof source === 'number') {
    return source.toString()
  } else {
    return ''
  }
}

export function ensureNum(numParam: any) {
  const num = Number(numParam)
  if (Number.isNaN(num)) return 0
  return num
}

export function ensureNumPositive(numParam: number) {
  const num = Number(numParam)
  if (Number.isNaN(num)) return numParam
  return Math.abs(num)
}

export function ensureNumNegative(numParam: number) {
  const num = Number(numParam)
  if (Number.isNaN(num)) return numParam
  return -Math.abs(num)
}
