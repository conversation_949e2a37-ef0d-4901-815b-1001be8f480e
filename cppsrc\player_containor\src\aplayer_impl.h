#include <aplayer.h>
#include "aplayer_interfaces.h"
#include "./XLIPC/xlipc_connect_session.h"
#include <Windows.h>

class AplayerImpl : public APlayerInterface::Delegate {
public:
	AplayerImpl();
	void SetConnection(XLIPCConnectSession* pConn) { m_conn = pConn; }
	static LRESULT __stdcall PlayerWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam);
	void PostMessage(uint32_t nMsg, void* pData);
	void OnEventMessage(void* pData);
	void OnWndMessage(UINT message, WPARAM wParam, LPARAM lParam);

private:
	static int AplayerEventCallback(void* user, int event_id, int param1, int param2, const char* param_str);

public:
	virtual bool CreateAPlayer(const AplayerParam& param, HWND* pWnd);
	virtual long Open(const char* szUrl);
	virtual long Close();
	virtual long Play();
	virtual long Pause();
	virtual long GetVersion(std::string& strVersion);
	virtual long SetCustomLogo(long logo);
	virtual long GetState(long* state);
	virtual long GetDuration(long* duration);
	virtual long GetPosition(long* position);
	virtual long SetPosition(long position, long* result);
	virtual long GetVideoWidth(long* video_width);
	virtual long GetVideoHeight(long* video_height);
	virtual long GetVolume(long* volume);
	virtual long SetVolume(long volume, long* result);
	virtual long IsSeeking(long* seeking);
	virtual long GetBufferProgress(long* buffer_progress);
	virtual long GetConfig(const char* szConfigId, std::string& strValue);
	virtual long SetConfig(const char* szConfigId, const char* szValue, long* result);
	virtual void SetMainWnd(HWND main_wnd);

private:
	HWND CreatePlayerWnd(HWND hParent);

private:
	//HWND m_hParent{ NULL };
	HWND m_playerWnd{ NULL };
	void* m_pAplayer{ nullptr };
	XLIPCConnectSession* m_conn{ nullptr };
};