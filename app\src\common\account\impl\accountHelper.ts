import {
  Account,
  AccountEvent,
  IDeviceAuthorizeParams,
  IGetUserProfileParams,
  IGetVerificationParams,
  IGrantTokenParams,
  ISignInParams,
  ISignInWithOldAccountLoginInfoParams,
  IStartDeviceAuthorizationParams,
  IStartDeviceAuthorizationResponseData,
  IUpdateUserProfileParams,
  IUserDeviceAuthorizationUrlResult,
} from '@xbase/electron_account_kit'
import {
  Common,
  ErrorCodeType,
  EventEmitter,
  EventEmitterListener,
  ITraceInfoOptionLike,
  LogLevel,
  RecordLevel,
  ReportLevel,
  // RequestFunctionWrapper,
  ResponseError,
  // SimpleStorage,
  TraceInfoUtility,
} from '@xbase/electron_common_kit'
//import { ICreatePurchaseExtInfo, ICreatePurchaseParams, IAP, ICreatePurchaseResult }from '@xbase/electron_iap_kit';
import {
  IDeviceAuthorizeResponseData,
  IGetVerificationResponseData,
} from '@xbase/electron_auth_kit'
import {
  IGetCredentialsParams,
  RefreshTokenActionType,
} from '@xbase/electron_auth_types_kit'
import { DefaultConsole, MemoryStorage } from '@xbase/electron_default_plugins_kit'
//import { IProviderAccountAuthorizeResponseData, ProviderAccount, ProviderAccountHuawei }from '@xbase/electron_provider_account_kit';
import { Credentials } from '@xbase/electron_auth_types_kit'
import { CryptoUtility, Locale } from '@xbase/electron_base_kit'
import { DefaultRequest } from '@xbase/electron_default_plugins_kit'
import * as path from 'path'
import { DefaultBrowserWindow } from '../../browser-window/browser_window/DefaultBrowserWindow'
import { rendererProcessRemote } from '../../browser-window/process_remote/RendererProcessRemote'
import { FileSystemAWNS } from '../../fs-utilities'
import requireNodeFile from '../../require-node-file'
import { ApplicationManager } from '../../thunder-pan-manager/manager/application'
import { GetProfilesPath, GetXxxNodePath } from '../../xxx-node-path'
import { SyncClientEvent } from '@xbase/electron_sync_kit'
import { env } from '@root/common/env'
import {
  ISignUpParams,
  ISignWithVerificationParams,
  ISignInWithPasswordParams,
  AccountHelperEventKey,
  UserInfo,
  SessionInfo,
} from '@root/common/account/account-type'
import { LoginDataHelper } from '@root/common/account/impl/login-data-helper'
import { ILoginHistory, ILastLoginData, LoginMode } from '@root/common/account/impl/login-options-define'

const thunderHelper: any = requireNodeFile(path.join(GetXxxNodePath(), 'thunder_helper.node'));

let basePath = path.dirname(process.execPath)

/**
 * 登录、账号相关
 */
export class AccountHelper {
  private static _instance: AccountHelper
  static getInstance() {
    if (AccountHelper._instance) {
      return AccountHelper._instance
    } else {
      AccountHelper._instance = new AccountHelper()
      AccountHelper._instance.prepare()
      return AccountHelper._instance
    }
  }

  private _localDataHelper: LoginDataHelper;

  private static projectId = `2rvk4e3gkdnl7u1kl0k`
  static appOriginHosts: string[] = env === 'prod' ?
  // 外网
  [
    'https://xluser-ssl.xunlei.com',
    'https://xluser2-ssl.xunlei.com',
    'https://xluser3-ssl.xunlei.com',
  ] : [ 'https://dev-xluser-ssl.xunlei.com' ];   // 内网

  private common!: Common
  public account!: Account
  //private iap!: IAP;
  private readonly _eventEmitter: EventEmitter = new EventEmitter()
  // 是否已登录
  public isSignIn: boolean = false
  // 用户信息
  private userInfo: UserInfo = {} as any
  // sdk 是否初始化完毕
  public sdkInitReady: boolean = false

  private thirdLoginCallbackCount: number = 0
  // 支付sdk 是否初始化完毕
  public paySdkInitReady: boolean = false
  private defaultRequest: DefaultRequest = new DefaultRequest({
    usingProxy: true, // 线上关闭代理
  })
  deviceId: string = '';
  isLoginIng: boolean = false;
  waitLoginIngCbs: (() => void)[] = [];

  constructor() {
  }

  prepare() {
    rendererProcessRemote.prepare()
  }

  /**
   * 获取 sdk 内部已集成的请求函数（自动刷新与带上 captchaToken、Authorization 等）
   * @returns
   */
  get request() {
    return this.account.request.bind(this.account)
  }

  isVip(): boolean {
    return (
      this.isSignIn &&
      !!this.userInfo?.vip_info?.[0] &&
      this.userInfo.vip_info[0].is_vip === '1'
    )
  }

  get isPlatinumVip(): boolean {
    return (
      this.isVip() &&
      !!this.userInfo?.vip_info?.[0] &&
      (this.userInfo.vip_info[0].vas_type === '2' ||
        this.userInfo.vip_info[0].vas_type === '3')
    )
  }

  get isSuperVip(): boolean {
    return (
      this.isVip() &&
      !!this.userInfo?.vip_info?.[0] &&
      this.userInfo.vip_info[0].vas_type === '5'
    )
  }

  get isYear(): boolean {
    return (
      this.isVip() &&
      !!this.userInfo?.vip_info?.[0] &&
      this.userInfo.vip_info[0].is_year === '1'
    )
  }

  get vasType(): string {
    if (this.isVip() && !!this.userInfo?.vip_info?.[0]?.vas_type) {
      return this.userInfo.vip_info[0].vas_type
    }

    return ''
  }

  get vipLevel(): string {
    if (this.isVip() && !!this.userInfo?.vip_info?.[0]?.level) {
      return this.userInfo.vip_info[0].level
    }
    return '0'
  }

  /**
   * 与 request 功能一致，内部会自动转换 JSON，并且如果服务端错误以标准形式返回，这里面也会有相应的处理
   * @returns
   */
  get requestWithAutoJsonConvert() {
    return this.account.requestWithAutoJsonConvert.bind(this.account)
  }

  /**
   * 获取验证码接口
   * @returns
   */
  get getVerification() {
    return this.account.getVerification.bind(this.account)
  }

  /**
   * 校验验证码接口
   * @returns
   */
  get verifyVerification() {
    return this.account.verifyVerification.bind(this.account)
  }

  /**
   * mqtt 通道的实例
   * @returns
   */
  get syncClient () {
    return this.account.syncClient;
  }

  get accountDeviceSign() {
    return this.account.deviceSign
  }

  get commonAppName() {
    return this.common._commonShared.envManager.appName
  }

  async initSDK() {
    this.common = new Common({
      rootDir: path.join(GetProfilesPath(), 'user'),
      analysis: {
        apiOrigin: 'https://analysis-acc-ssl.xunlei.com',
        indexName: 'xluser-electron-kit-analatics',
      },
      locale: Locale.ZH_CN,
      console: new DefaultConsole(),
      logLevel: LogLevel.OFF, // 线上关掉
      recordLevel: RecordLevel.DEBUG,
      reportLevel: ReportLevel.DEBUG,
      projectId: AccountHelper.projectId,
      clientVersion: ApplicationManager.getCurrentDeviceClientVersion(),
      deviceId: await this.getDeviceID(),
      clientId: ApplicationManager.getCurrentDeviceClientId(),
      bundleName: ApplicationManager.getCurrentDeviceBundleName(),
      baseRequestFunction: (options) => {
        return this.defaultRequest.request(options)
      },
      createBrowserWindowBuilder: async () => {
        return new DefaultBrowserWindow(
          {
            width: 330,
            height: 345,
            icon: path.join(basePath, 'xmp.ico'),
            // resizable: false,
            // backgroundColor: 'rgba(38, 43, 50, 1)'
          },
          {
            // 注入css
            // executeJavaScriptCode: `
            //     // adding css
            //     const head = document.getElementsByTagName('head')[0];
            //     const style = document.createElement('style');
            //     style.textContent = \`
            //       body {
            //         color: rgba(255, 255, 255, 1) !important;
            //       }
            //       .pop-validate.pop-validate-auto {
            //         background:rgba(38, 43, 50, 1) !important;
            //       }
            //       .pop-validate .subtit {
            //         color: rgba(255, 255, 255, 0.5) !important;
            //       }
            //       .shumei_captcha_slide_wrapper {
            //         color: rgba(255, 255, 255, 0.5) !important;
            //         background:rgba(28, 32, 39, 1) !important;
            //         border: unset !important;
            //       }
            //       .shumei_captcha_slide_wrapper .shumei_captcha_slide_slot {
            //         background-color: rgba(28, 32, 39, 1) !important;
            //       }
            //     \`
            //     head.appendChild(style);
            //     0;
            //           `,
          },
        )
      },
    })

    // this.providerAccount = new ProviderAccount({
    //   common: this.common,
    //   console: new DefaultConsole(),
    //   logLevel: LogLevel.OFF,
    //   recordLevel: RecordLevel.DEBUG});

    // this.providerAccount.onHandleProvideAuthorizeResponseCallback = this.onHandleProvideAuthorizeResponseCallback.bind(this);

    this.account = new Account({
      recordLevel: RecordLevel.DEBUG,
      common: this.common,
      appId: ApplicationManager.getCurrentDeviceAppId(),
      appSignKey: ApplicationManager.getCurrentDeviceAppKey(),
      clientId: ApplicationManager.getCurrentDeviceClientId(),
      clientSecret: ApplicationManager.getCurrentDeviceClientSecret(),
      apiOrigins: AccountHelper.appOriginHosts,
      // NOTE: 正式包为 LogLevel.OFF，调试可以开启
      logLevel: LogLevel.OFF, // 线上关掉
      console: new DefaultConsole(),
      storage: new MemoryStorage(),
      captchaFlowRedirectUri:
        'https://i.xunlei.com/center/account/personal/loading/',
    })

    // sdk 初始化
    this.account.init().then(() => {
      this.sdkInitReady = true
      this._eventEmitter.emit(AccountHelperEventKey.SDK_INIT_READY)
    })

    // 登录历史记录初始化
    this._localDataHelper = new LoginDataHelper(path.join(basePath, '../profiles/xlaccount/'));
    await this._localDataHelper.init();
    
    // 转发登录事件
    this.account.on(AccountEvent.SIGNED_IN, async () => {
      this.isSignIn = true
      await this._updateUserInfo({
        traceInfo: TraceInfoUtility.generateTraceInfo({
          source: 'AccountHelper:' + AccountEvent.SIGNED_IN
        })
      }).catch()
      this._eventEmitter.emit(AccountHelperEventKey.SIGN_IN_SUCCESS)
      this.endLogin();
    })
    // 转发登出事件
    this.account.on(AccountEvent.SIGNED_OUT, () => {
      this.isSignIn = false
      this.userInfo = {} as any
      this._eventEmitter.emit(
        AccountHelperEventKey.USER_INFO_CHANGE,
        this.userInfo,
      )
      this._eventEmitter.emit(AccountHelperEventKey.SIGN_OUT);
      this.endLogin();
    })
    // 用户信息变更事件
    this.account.on(AccountEvent.USER_PROFILE_UPDATED, () => {
      this._updateUserInfo({
        traceInfo: TraceInfoUtility.generateTraceInfo({
          source: 'AccountHelper:' + AccountEvent.USER_PROFILE_UPDATED
        })
      }).catch()
    })
    // 转发刷新 Credentials 事件
    this.account.on(AccountEvent.CREDENTIALS_REFRESHED, () => {
      this._eventEmitter.emit(AccountHelperEventKey.REFRESH_CREDENTIALS)
    })
    // 转发刷新设备授权事件
    this.account.on(AccountEvent.DEVICE_AUTHORIZATION, (traceInfoOptionLike: ITraceInfoOptionLike, taskId: string, responseError?: ResponseError) => {
      const err = this.getSafeError(responseError);
      this._eventEmitter.emit(AccountHelperEventKey.DEVICE_AUTHORIZATION, traceInfoOptionLike, taskId, err)
    })
    // 转发 mqtt 通道连接事件
    this.account.syncClient.on(SyncClientEvent.CONNECTED, () => {
      this._eventEmitter.emit(AccountHelperEventKey.SYNC_CLIENT_EVENT_CONNECTED)
    })
    // 转发 mqtt 通道消息通知事件
    this.account.syncClient.on(SyncClientEvent.MESSAGE_ARRIVED, (msg) => {
      this._eventEmitter.emit(AccountHelperEventKey.SYNC_CLIENT_EVENT_MESSAGE_ARRIVED, msg)
    })

    // 支付
    // this.iap = new IAP({
    //   recordLevel: RecordLevel.DEBUG,
    //   common: this.common,
    //   agentPayCenterApiOrigin: 'https://agent-paycenter-ssl.xunlei.com',
    //   payGateWayApiOrigin: 'https://pay-gateway.xunlei.com',
    //   // agentPayCenterApiOrigin: 'https://dev-paycenter.xunlei.com',
    //   // payGateWayApiOrigin: 'https://test-pay-gateway.xunlei.com',
    //   logLevel: LogLevel.OFF,         // 线上关掉
    //   console: new DefaultConsole(),
    // });
    // // 支付sdk 初始化
    // this.iap.init().then(()=>{
    //   this.paySdkInitReady = true;
    //   this._eventEmitter.emit(AccountHelperEventKey.PAY_SDK_INIT_READY);
    // });
  }

  attachEvent(
    event: AccountHelperEventKey,
    listener: EventEmitterListener,
  ): void {
    this._eventEmitter.on(event, listener)
  }

  detachEvent(
    event: AccountHelperEventKey,
    listener: EventEmitterListener,
  ): void {
    this._eventEmitter.off(event, listener)
  }

  // attachThirdLoginResultEvent(func: Callback<ThirdLoginResultInfo>): string{
  //   if(!func){
  //     return '';
  //   }
  //   this.thirdLoginCallbackCount++;
  //   let funcId: string = `ThirdLoginResultFunc${this.thirdLoginCallbackCount.toFixed(0)}`
  //   this.thirdLoginResultCallbackMap.set(funcId,func);
  //   return funcId;
  // }

  // detachThirdLoginResultEvent(funcId: string){
  //   if(!funcId){
  //     return;
  //   }
  //   if(this.thirdLoginResultCallbackMap.hasKey(funcId)){
  //     this.thirdLoginResultCallbackMap.remove(funcId);
  //   }
  // }

  // 解析url是否是扫码授权
  async parseUserDeviceAuthorizationUrl(
    uri: string,
  ): Promise<IUserDeviceAuthorizationUrlResult> {
    return (
      this.account.parseUserDeviceAuthorizationUrl({ url: uri }).catch() || {
        userCode: '',
        clientId: '',
      }
    )
  }

  /**
   * 创建支付
   */
  // createPurchase(param: INativeCreatePurchaseParams): Promise<ICreatePurchaseResult>{
  //   return new Promise<ICreatePurchaseResult>(async (resolve, reject)=>{
  //     let res: ICreatePurchaseResult = {orderId: ''};
  //     if(this.iap && this.paySdkInitReady){
  //       let creatParam: ICreatePurchaseParams = this.getHmCreatePurchaseParams(param);
  //       try {
  //         res = await this.iap.createPurchase(creatParam);
  //       } catch (error) {
  //         const safeError = this.getSafeError(error);
  //         reject(safeError);
  //         return;
  //       }
  //     }
  //     resolve(res);
  //   });

  // }

  /**
   * 保持 sdk 的心跳
   */
  ping() {
    this.account?.pingIgnoreError()
  }
  /**
   * 注册账号，发送验证码到手机号
   * @param phoneNumber
   * @returns
   */
  async sendSignUpVerificationWithPhoneNumber(
    phoneNumber: string,
  ): Promise<IGetVerificationResponseData> {
    const params: IGetVerificationParams = {
      target: 'NOT_USER',
      usage: 'SIGN_IN',
      phoneNumber: phoneNumber,
      selectedChannel: 'VERIFICATION_PHONE',
    }

    return this.account.getVerification(params)
  }

  async signInWithOldAccountLoginInfo(
    loginInfo: ISignInWithOldAccountLoginInfoParams,
  ) {
    return this.account.signInWithOldAccountLoginInfo(loginInfo)
  }

  /**
   * 注册账号
   * @param params
   * @returns
   */
  async signUp(params: ISignUpParams): Promise<void> {
    return new Promise<void>(async (resolve, reject) => {
      // 校验验证码是否正确
      await this.asyncWaitLoginFinish();
      this.beginLogin();
      let verificationToken: string
      try {
        const res = await this.account.verifyVerification({
          verificationCode: params.verificationCode,
          verificationId: params.verification_id,
        })

        verificationToken = res.verification_token!
      } catch (error) {
        this.endLogin();
        const safeError = this.getSafeError(error);
        reject(safeError)
        return
      }

      try {
        await this.account.signUp({
          phoneNumber: params.phoneNumber,
          password: params.password,
          verificationCode: params.verificationCode,
          verificationToken: verificationToken,
        })
        resolve()
      } catch (error) {
        this.endLogin();
        const safeError = this.getSafeError(error);
        reject(safeError)
      }
    })
  }

  public async accountInitedAW(): Promise<void> {
    if (this.sdkInitReady) {
      return Promise.resolve();
    }
    return new Promise<void>(
      async (resolve: () => void): Promise<void> => {
        this.attachEvent(AccountHelperEventKey.SDK_INIT_READY, () => {
          console.log('SDK_INIT_READY')
          resolve();
        });
      }
    );
  }

  /** 等待帐号SDK初始化结束，并且自动登录逻辑完成（可能无需自动登录） */
  public async whenReady(): Promise<void> {
    await this.accountInitedAW();
    await this.asyncWaitLoginFinish();
  }

  /**
   * 自动登录
   * @returns
   */
  async autoSignIn(): Promise<void> {
    return new Promise<void>(async (resolve, reject) => {
      await this.accountInitedAW();
      await this.asyncWaitLoginFinish();
      this.beginLogin();
      try {
        await this.account.autoSignIn({
          refreshTokenActionType: RefreshTokenActionType.USER_AUTO_LOGIN,
        })
        resolve()
      } catch (error) {
        console.log('err', error, this.translateResponseError(error as ResponseError))
        this._eventEmitter.emit(AccountHelperEventKey.SIGN_IN_FAILURE)
        this.endLogin();
        const safeError = this.getSafeError(error);
        reject(safeError)
      }
    })
  }

  /**
   * 设备授权
   * @param params
   * @returns
   */
  async deviceAuthorize(
    param: IDeviceAuthorizeParams,
  ): Promise<IDeviceAuthorizeResponseData> {
    return this.account.deviceAuthorize(param)
  }

  /**
   * 账号密码登录
   * @param params
   * @returns
   */
  async signInWithPassword(params: ISignInWithPasswordParams): Promise<void> {
    return new Promise<void>(async (resolve, reject) => {
      console.log('login signInWithPassword', params)
      await this.asyncWaitLoginFinish();
      this.beginLogin();
      try {
        const signParams: ISignInParams = {
          username: params.username,
          password: params.password,
        }
        await this.account.signIn(signParams)
        console.log('login signInWithPassword success')
        this.updateLocalData({ userName: params.username, mode: LoginMode.Account });
        resolve()
      } catch (error) {
        const errorDesc = this.translateResponseError(error as ResponseError);
        console.log('login signInWithPassword failed', error, errorDesc)
        this._eventEmitter.emit(AccountHelperEventKey.SIGN_IN_FAILURE)
        this.endLogin();
        const safeError = this.getSafeError(error);
        reject(safeError)
      }
    })
  }

  /**
   * 账号+验证码登录
   * @param params
   * @returns
   */
  async signInWithVerification(
    params: ISignWithVerificationParams,
  ): Promise<boolean> {
    return new Promise<boolean>(async (resolve, reject) => {
      // 校验验证码是否正确
      let verificationToken: string
      try {
        await this.asyncWaitLoginFinish();
        this.beginLogin();
        const res = await this.account.verifyVerification({
          verificationCode: params.verificationCode,
          verificationId: params.verification_id,
        })

        verificationToken = res.verification_token!
      } catch (error) {
        this._eventEmitter.emit(AccountHelperEventKey.SIGN_IN_FAILURE)
        this.endLogin();
        const safeError = this.getSafeError(error);
        reject(safeError)
        return
      }

      try {
        if (params.isUser) {
          await this.account.signIn({
            username: params.username,
            verificationCode: params.verificationCode,
            verificationToken: verificationToken,
          })
        } else {
          await this.account.signUp({
            phoneNumber: params.username,
            verificationCode: params.verificationCode,
            verificationToken: verificationToken,
          })
        }
        this.updateLocalData({ userName: params.username, mode: LoginMode.Phone });
        resolve(true)
      } catch (error) {
        this._eventEmitter.emit(AccountHelperEventKey.SIGN_IN_FAILURE)
        this.endLogin();
        const safeError = this.getSafeError(error);
        reject(safeError)
      }
    })
  }

  /**
   * 手机号登录发送验证码
   * @param phoneNumber
   * @returns
   */
  async sendSignInVerificationWithPhoneNumber(
    phoneNumber: string,
  ): Promise<IGetVerificationResponseData> {
    const params: IGetVerificationParams = {
      target: 'ANY',
      usage: 'SIGN_IN',
      phoneNumber: phoneNumber,
      selectedChannel: 'VERIFICATION_PHONE',
    }

    try {
      const res: IGetVerificationResponseData = await this.account.getVerification(params)
      return res;
    } catch (error) {
      const safeError = this.getSafeError(error);
      return Promise.reject(safeError)
    }
  }

  /**
   * 获取扫码登录的二维码
   * @returns
   */
  async getSignInQrcode(): Promise<IStartDeviceAuthorizationResponseData> {
    let params: IStartDeviceAuthorizationParams = {
      scope: '',
    }

    try {
      const res: IStartDeviceAuthorizationResponseData = await this.account.startDeviceAuthorization(params)
      return res;
    } catch (error) {
      const safeError = this.getSafeError(error);
      return Promise.reject(safeError)
    }
  }

  async stopDeviceAuthorization() {
    this.account.stopDeviceAuthorization()
  }

  async grantToken(code: string, userId: string): Promise<void> {
    let request: IGrantTokenParams = {
      grantType: 'authorization_code',
      code
    };
    try {
      await this.asyncWaitLoginFinish();
      if (this.isSignIn && this.userInfo && (this.userInfo as any).id! === userId) {
        return;
      }
      if (this.isSignIn) {
        await this.signOut();
      }
      await this.asyncWaitLoginFinish();
      this.beginLogin();
      // await new Promise((v)=>{
      //   setTimeout(()=>{
      //     v(true);
      //   }, 10000);
      // })
      await this.account.grantToken(request);
    } catch (error) {
      //console.log('=================error', this.translateResponseError(error as any))
      this.endLogin();
      const safeError = this.getSafeError(error);
      return Promise.reject(safeError);
    }
  }
  /**
   * 唤起扫码界面
   * @returns
   */
  // async scanSignInQrcode (): Promise<IUserDeviceAuthorizationUrlResult> {
  //   return new Promise<IUserDeviceAuthorizationUrlResult>(async (resolve, reject)=>{
  //     let options: scanBarcode.ScanOptions = {
  //       scanTypes: [ scanCore.ScanType.ALL ],
  //       enableMultiMode: true,
  //       enableAlbum: true
  //     };

  //     let scanResult: scanBarcode.ScanResult;
  //     try {
  //       scanResult = await scanBarcode.startScanForResult(getContext(), options);
  //     } catch (error) {
  //       const safeError = this.getSafeError(error);
  //       reject(safeError);
  //       return;
  //     }

  //     let userDeviceAuthorizationUriResult: IUserDeviceAuthorizationUrlResult;
  //     try {
  //       userDeviceAuthorizationUriResult = await this.account.parseUserDeviceAuthorizationUrl({url: scanResult.originalValue});
  //     } catch (error){
  //       const safeError = this.getSafeError(error);
  //       reject(safeError);
  //       return;
  //     }

  //     const response = await this.account.deviceAuthorize({
  //       clientId: userDeviceAuthorizationUriResult.clientId,
  //       userCode: userDeviceAuthorizationUriResult.userCode,
  //       scope: userDeviceAuthorizationUriResult.scope,
  //       state: 'WAITING_CONSENT'
  //     });

  //     resolve(userDeviceAuthorizationUriResult);
  //   });
  // }

  /**
   * 同意扫码登录
   */
  async agreeQrcodeSignIn(
    userDeviceAuthorizationUriResult: IUserDeviceAuthorizationUrlResult,
  ): Promise<IDeviceAuthorizeResponseData> {
    return this.account.deviceAuthorize({
      clientId: userDeviceAuthorizationUriResult.clientId,
      userCode: userDeviceAuthorizationUriResult.userCode,
      scope: userDeviceAuthorizationUriResult.scope,
      state: 'ACCOMPLISHED',
    })
  }

  /**
   * 取消扫码登录
   */
  async denyQrcodeSignIn(
    userDeviceAuthorizationUriResult: IUserDeviceAuthorizationUrlResult,
  ): Promise<IDeviceAuthorizeResponseData> {
    return this.account.deviceAuthorize({
      clientId: userDeviceAuthorizationUriResult.clientId,
      userCode: userDeviceAuthorizationUriResult.userCode,
      scope: userDeviceAuthorizationUriResult.scope,
      state: 'ACCESS_DENIED',
    })
  }

  /**
   * 监听二维码扫码登录通知
   * @param callback
   */
  attachSignInQrcodeNotify(
    callback: (
      traceInfoOptionLike: ITraceInfoOptionLike,
      taskId: string,
      responseError?: ResponseError,
    ) => void,
  ) {
    this.account.on(AccountEvent.DEVICE_AUTHORIZATION, callback)
  }

  /**
   * 移除监听二维码扫码登录通知
   * @param callback
   */
  detachSignInQrcodeNotify(
    callback: (
      traceInfoOptionLike: ITraceInfoOptionLike,
      taskId: string,
      responseError?: ResponseError,
    ) => void,
  ) {
    this.account.off(AccountEvent.DEVICE_AUTHORIZATION, callback)
  }

  /**
   * 退出登录
   */
  async signOut(): Promise<void> {
    await this.asyncWaitLoginFinish();
    this.beginLogin();
    try {
      await this.account.signOut({})
    } catch (error) {
      this.endLogin();
      const safeError = this.getSafeError(error);
      return Promise.reject(safeError);
    }
  }

  /**
   * 登录凭证，实时有效（对应的每次请求前需要调用这个去刷新 Credentials）
   * @returns
   */
  async getCredentials(params: IGetCredentialsParams): Promise<Credentials> {
    try {
      const res = await this.account.getCredentials(params);
      return res;
    } catch (error) {
      const safeError = this.getSafeError(error);
      return Promise.reject(safeError);
    }
  }

  /**
   * 非校验的登录凭证，不一定实时有效（应用在离线场景展示部分用户信息）
   * @returns
   */
  async getCredentialsWithoutCheck(): Promise<Credentials | null> {
    try {
      const res = this.account.getCredentialsWithoutCheck();
      return res;
    } catch (error) {
      const safeError = this.getSafeError(error);
      return Promise.reject(safeError);
    }
  }

  /**
   * 获取拼接好的 Authorization （accessToken）
   * @returns
   */
  async getBearerAuthorization(): Promise<string> {
    return new Promise<string>(async (resolve, reject) => {
      try {
        const res = await this.getCredentials({})
        resolve((res?.tokenType || '') + ' ' + (res?.accessToken || ''))
      } catch (error) {
        const safeError = this.getSafeError(error);
        reject(safeError)
      }
    })
  }

  /**
   * 获取 CaptchaToken
   * @param action 规则例如：POST:/v1/auth/verification
   * @param meta 透传至服务端所需的参数，具体需要看所在业务的接口（服务端）定义
   * @param renew 是否强制刷新
   * @returns
   */
  // async getCaptchaToken (action: string, meta: IGetCaptchaTokenParams, renew?: boolean): Promise<string> {
  //   return await this.account.captcha.getCaptchaToken({action: action,meta: meta,renew: renew});
  // }

  /**
   * 获取用户信息
   * @returns
   */
  async getUserInfo(): Promise<UserInfo> {
    return new Promise<UserInfo>(async (resolve, reject) => {
      // 如果已登录，但用户信息为空的情况下，主动再去获取一次
      if (!this.userInfo) {
        this._updateUserInfo()
          .then(() => {
            resolve(this.userInfo!)
          })
          .catch((error?: ResponseError) => {
            const safeError = this.getSafeError(error);
            reject(safeError)
          })
      } else {
        resolve(this.userInfo)
      }
    })
  }

  /**
   * 更新用户信息
   * @param params
   * @returns
   */
  async updateUserProfile(params: IUpdateUserProfileParams) {
    try {
      await this.account.updateUserProfile(params);
    } catch (error) {
      const safeError = this.getSafeError(error);
      return Promise.reject(safeError)
    } 
  }

  /**
   * 获取历史登录账号列表
   * @returns
   */
  async getLoginHistoryList(): Promise<ILoginHistory[]> {
    await this._localDataHelper.init();
    return this._localDataHelper.loginHistoryList;
  }

  /**
   * 获取上次登录账号
   * @returns
   */
  async getLastLoginData(): Promise<ILastLoginData> {
    await this._localDataHelper.init();
    return this._localDataHelper.lastLoginData;
  }

  /**
   * 单次删除登录历史记录
   * @returns
   */
  async deleteLoginHistory(history: ILoginHistory, backTrack?: boolean): Promise<boolean> {
    let done: boolean = false;
    do {
      await this._localDataHelper.init();
      const item: ILoginHistory = history;
      const backtrack: boolean = backTrack ?? true;
      if (!this._localDataHelper.deleteLoginHistory(item, backtrack)) {
        break;
      }
      done = await this._localDataHelper.saveData();
    } while (0);
    return done;
  }

  /**
   * 删除所有登录历史记录
   * @returns
   */
  async clearLoginHistory(): Promise<boolean> {
    await this._localDataHelper.init();
    this._localDataHelper.deleteAllLoginHistory();
    return this._localDataHelper.saveData();
  }

  /**
   * 获取实时用户信息,非登录需要返回error
   * @returns
   */
  async getRealtimeUserInfo(): Promise<UserInfo> {
    return new Promise<UserInfo>(async (resolve, reject) => {
      try {
        let userInfo = await this.account.getUserProfile({ renew: false })
        userInfo = (userInfo || {}) as UserInfo
        resolve(userInfo)
      } catch (error) {
        const safeError = this.getSafeError(error);
        reject(safeError)
      }
    })
  }

  async getOldAccountSessionInfo(): Promise<SessionInfo> {
    return new Promise<SessionInfo>(async (resolve, reject) => {
      try {
        let oldSessionInfo = await this.account.getOldAccountSessionInfo()
        let sessionInfo: SessionInfo = {
          loginKey: '',
          sessionId: '',
          secureKey: '',
          userId: '',
        }
        sessionInfo.sessionId = oldSessionInfo?.sessionId || ''
        sessionInfo.loginKey = oldSessionInfo?.loginKey || ''
        sessionInfo.secureKey = oldSessionInfo?.secureKey || ''
        resolve(sessionInfo)
      } catch (error) {
        const safeError = this.getSafeError(error);
        reject(safeError)
      }
    })
  }

  /**
   * 获取登录状态
   * @returns
   */
  getSignInStatus(): boolean {
    return this.isSignIn
  }

  /**
   * 获取 sdk 初始化状态
   * @returns
   */
  getSDKInitStatus(): boolean {
    return this.sdkInitReady
  }

  getPaySDKiNITsTATUS(): boolean {
    return this.paySdkInitReady
  }

  /**
   * 更新用户信息数据
   * @param renew
   */
  private async _updateUserInfo(params?: IGetUserProfileParams): Promise<void> {
    // 重试逻辑
    const retryTime: number = 2;
    for (let i: number = 0; i < retryTime; i++) {
      try {
        const userInfo = await this.account.getUserProfile({ ...params })
        this.userInfo = (userInfo || {}) as UserInfo
        this._eventEmitter.emit(
          AccountHelperEventKey.USER_INFO_CHANGE,
          userInfo,
        )
      } catch (error) {
        if (i < retryTime - 1) {
          // 由于 sdk 内部处理的顺序问题，可能会失败，所以延迟 0s 再去获取
          await new Promise((v) => {
            setTimeout(() => {
              v(true)
            }, 0)
          })
          continue;
        }
        const safeError = this.getSafeError(error);
        return Promise.reject(safeError)
      }
    }
  }

  // public releaseProviderAccount(){
  //   if(this.providerAccountHuawei){
  //     this.providerAccountHuawei = undefined;
  //   }
  // }

  public translateResponseError(responseError: ResponseError | null) {
    if (!responseError) {
      return ''
    }
    return this.common?.translateResponseError(responseError) || ''
  }

  public async getDeviceID(): Promise<string> {
    let deviceIDMd5Str: string = '';
    do {
      if (this.deviceId !== null && this.deviceId !== undefined && this.deviceId !== '') {
        deviceIDMd5Str = this.deviceId;
        break;
      }
      let deviceIDStr: string = '';
      let peeridMd5Str: string = '';
      let appName: string = 'SYSTEM_CONFIG';
      let appKey: string = 'DEVICEID';
      let publicPath: string = thunderHelper.getPublicUserDataPath();
      let pathStr: string = path.join(publicPath, 'Thunder Network/Thunder');
      let pathFileStr: string = path.join(pathStr, '/xlconfig.ini');
      // 首先第一步是从保存文件里面去获取peerid的MD5值，没有的话才重新计算
      if (publicPath !== '') {
        let re: boolean = await FileSystemAWNS.mkdirsAW(pathStr);
        if (re) {
          // 读取相应文件里面的数据
          peeridMd5Str = thunderHelper.readINI(pathFileStr, appName, appKey);
        }
        console.log('得到文件保存 peeridMd5Str:', peeridMd5Str);
      }
      if (peeridMd5Str === '') {
        let peeridStr: string = thunderHelper.getPeerId();
        peeridMd5Str = CryptoUtility.createMDAlgorithms().MD5StringSync(peeridStr);
        console.log('重新计算 peeridStr:', peeridStr, ';peeridMd5Str:', peeridMd5Str);

        let re: boolean = await FileSystemAWNS.mkdirsAW(pathStr);
        if (re) {
          // 读取相应文件里面的数据
          thunderHelper.writeINI(pathFileStr, appName, appKey, peeridMd5Str);
        }
      }
      let dmideCodeStr: string = thunderHelper.getDmideCode();
      let dmideCodeMd5Str: string = CryptoUtility.createMDAlgorithms().MD5StringSync(dmideCodeStr);
      console.log('dmideCodeStr:', dmideCodeStr, ';dmideCodeMd5Str:', dmideCodeMd5Str);
      deviceIDStr = dmideCodeMd5Str + peeridMd5Str;
      // 综合之后再计算MD5值
      deviceIDMd5Str = CryptoUtility.createMDAlgorithms().MD5StringSync(deviceIDStr);
      this.deviceId = deviceIDMd5Str;
    } while (false);

    console.log('deviceIDMd5Str:', deviceIDMd5Str);
    return deviceIDMd5Str;
  }

  genFailReason(error: ResponseError, businessReason: string): string {
    switch (error.errorCode) {
      case ErrorCodeType.UNREACHABLE: {
        return 'network_error'
      }
      case ErrorCodeType.SERVER_ERROR: {
        return 'server_error'
      }
      default: {
        return businessReason
      }
    }
  }

  private endLogin() {
    this.isLoginIng = false;
    do {
      if ((this.waitLoginIngCbs.length === 0) || this.isLoginIng) {
        break;
      }
      let cb = this.waitLoginIngCbs[0];
      this.waitLoginIngCbs = this.waitLoginIngCbs.splice(1);
      cb();
    } while (true)
  }
  private beginLogin() {
    this.isLoginIng = true;
  }

  private async asyncWaitLoginFinish() {
    if (this.isLoginIng) {
      await new Promise((v) => {
        this.waitLoginIngCbs.push(() => {
          v(true);
        });
      });
    }
  }

  /** IPC内部仅支持泛型的传递，Error对象仅仅对传递error.message，此处转换一下 */
  private getSafeError(err: any) {
    let safeError: any = err;
    do {
      if (!err) {
        break;
      }
      if (err instanceof ResponseError) {
        try {
          safeError = err.toJsonObject();
        } catch (error) {
          safeError = err;
        }
      }
    } while (0);
    return safeError;
  }

  // 更新本地数据
  private async updateLocalData(loginHistory?: ILoginHistory): Promise<void> {
    if (loginHistory) {
      this._localDataHelper.lastLoginData = ({
        userName: loginHistory.userName,
        mode: loginHistory.mode,
        autoLogin: loginHistory.autoLogin
      });
      if (loginHistory.userName) {
        this._localDataHelper.deleteLoginHistory(loginHistory, false);
        this._localDataHelper.insertLoginHistory(loginHistory, 0);
      }
    }
    await this._localDataHelper.saveData();
  }
}
