project (ThunderInstallInfo)
THUNDER_LOG_INFO("${PROJECT_NAME} is Generating...")

set(thunder_common_dir ${CMAKE_SOURCE_DIR}/common)

file(GLOB_RECURSE prj_headerFiles "${CMAKE_CURRENT_SOURCE_DIR}/*.h")
file(GLOB_RECURSE prj_cppFiles "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")



add_library(${PROJECT_NAME} STATIC
	${prj_headerFiles} 
	${prj_cppFiles}
)

if (MSVC)
	target_precompile_headers(${PROJECT_NAME} PRIVATE stdafx.h)
	target_compile_options(${PROJECT_NAME} PRIVATE /MT)
endif()

target_include_directories (${PROJECT_NAME} PRIVATE 
	${thunder_common_dir}/include
	${THUNDER_COMMON_PATH}/xl_lib
	${CMAKE_CURRENT_SOURCE_DIR}
)

target_link_libraries( ${PROJECT_NAME} 
	PRIVATE xl_lib
	#PRIVATE ${XL_LIB_STATIC_LIBS}
)
# 设置目标属性，将目标分组到 "Setup/Base" 文件夹中
set_target_properties(${PROJECT_NAME} PROPERTIES FOLDER "setup/Base")