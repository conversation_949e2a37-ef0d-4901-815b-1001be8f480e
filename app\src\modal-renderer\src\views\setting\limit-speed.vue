<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { PopUpNS } from '@root/common/pop-up'
import Checkbox from '@root/common/components/ui/checkbox/index.vue'
import Slider from '@root/common/components/ui/slider/index.vue'
import { usePositionMixinComponent } from '@/common/mixins';
import * as PopUpTypes from '@root/common/pop-up/types'
import Select from '@root/common/components/ui/select/index.vue'
import { setSettingConfig, getSettingConfig, DOWNLOAD_SPEED_SETTING_NAME_MAP } from '@root/modal-renderer/src/views/setting'

// 使用基类的逻辑
const { overridePosition, resizeToFitContent } = usePositionMixinComponent();

// 重写控制位置基类非响应式数据
overridePosition({
  relatePos: PopUpTypes.RelatePosType.CenterParent,
  autoSize: true,
  show: true,
  selector: '.limit-speed-setting-container',
})

const handleClose = async () => {
  console.log('LimitSpeed: 关闭按钮被点击')
  const currentWindow = PopUpNS.getCurrentWindow()
  currentWindow.close()
}

const isLimitDownloadSpeed = ref(false)
const downloadSpeed = ref(1024)

const isLimitUploadSpeed = ref(false)
const uploadSpeed = ref(1024)

const isLimitDownloadSpeedTime = ref(false)
const limitDownloadSpeedStartHour = ref('0')
const limitDownloadSpeedStartMinute = ref('0')
const limitDownloadSpeedEndHour = ref('23')
const limitDownloadSpeedEndMinute = ref('59')

const limitDownloadSpeedHourOptions = ref(
  Array.from({ length: 24 }, (_, i) => ({
    name: i.toString(),
    value: i.toString(),
  }))
)

const limitDownloadSpeedMinuteOptions = ref(
  Array.from({ length: 60 }, (_, i) => ({
    name: i.toString(),
    value: i.toString(),
  }))
)

const initDefaultValue = async () => {
  isLimitDownloadSpeed.value = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.DownloadSpeedChk, false) as boolean
  isLimitUploadSpeed.value = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.UploadSpeedChk, false) as boolean

  downloadSpeed.value = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.MaxDownloadSpeed, 1024) as number
  uploadSpeed.value = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.MaxUploadSpeed, 1024) as number

  isLimitDownloadSpeedTime.value = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeSwitch, false) as boolean
  limitDownloadSpeedStartHour.value = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeBeginHour, '0') as string
  limitDownloadSpeedStartMinute.value = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeBeginMinute, '0') as string
  limitDownloadSpeedEndHour.value = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeEndHour, '23') as string
  limitDownloadSpeedEndMinute.value = await getSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeEndMinute, '59') as string
}

onMounted(() => {
  initDefaultValue()
})

watch(isLimitUploadSpeed, (newVal) => {
  if (newVal) {
    resizeToFitContent(680, 556);
  } else {
    resizeToFitContent(680, 490);
  }
})

const handleConfirm = () => {
  setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.Type, '0')

  if (isLimitDownloadSpeed.value) {
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.DownloadSpeedChk, true)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.MaxDownloadSpeed, downloadSpeed.value)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.MaxDownloadSpeedTmp, downloadSpeed.value)
  } else {
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.DownloadSpeedChk, false)
  }

  if (isLimitUploadSpeed.value) {
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.UploadSpeedChk, true)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.MaxUploadSpeed, uploadSpeed.value)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.MaxDownloadSpeedTmp, uploadSpeed.value)
  } else {
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.UploadSpeedChk, false)
  }

  if (isLimitDownloadSpeedTime.value) {
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeSwitch, true)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeBeginHour, +limitDownloadSpeedStartHour.value)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeBeginMinute, +limitDownloadSpeedStartMinute.value)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeEndHour, +limitDownloadSpeedEndHour.value)
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeEndMinute, +limitDownloadSpeedEndMinute.value)
  } else {
    setSettingConfig(DOWNLOAD_SPEED_SETTING_NAME_MAP.TimeSwitch, false)
  }

  handleClose()
}

</script>

<template>
  <div class="limit-speed-setting-container">
    <div class="limit-speed-setting-header draggable">
      <span>
        限速设置
      </span>
      <Button variant="ghost" is-icon @click="handleClose" class="none-draggable">
        <i class="xl-icon-general-close-m"></i>
      </Button>
    </div>

    <div class="limit-speed-setting-body none-draggable">
      <div class="limit-speed-setting-body-item">
        <div class="limit-speed-setting-body-item-title">
          <Checkbox v-model="isLimitDownloadSpeed" label-class="limit-speed-setting-label">最大下载速度</Checkbox>
        </div>
        <div class="limit-speed-setting-slider">
          <Slider v-model="downloadSpeed" :disabled="!isLimitDownloadSpeed" :step="1" :segments="[{
            id: '1',
            start: 60,
            end: 500,
          }, {
            id: '2',
            start: 500,
            end: 1024,
          }, {
            id: '3',
            start: 1024,
            end: 5120,
          }, {
            id: '4',
            start: 5120,
            end: 51200,
          }]" :labels="['60KB', '500KB', '1MB', '5MB', '50MB']" :show-input="true" unit="KB/s" />
        </div>
      </div>

      <div class="limit-speed-setting-body-item">
        <div class="limit-speed-setting-body-item-title">
          <Checkbox v-model="isLimitDownloadSpeedTime" label-class="limit-speed-setting-label">限速下载时间段</Checkbox>
        </div>
        <div class="limit-speed-setting-select-container">
          <span>开始限速时间</span>
          <Select v-model="limitDownloadSpeedStartHour" :options="limitDownloadSpeedHourOptions"
            anchor-class="limit-speed-setting-select" content-class="limit-speed-setting-select-content"
            :max-height="160" :divider="true" />
          <span>时</span>
          <Select v-model="limitDownloadSpeedStartMinute" :options="limitDownloadSpeedMinuteOptions"
            anchor-class="limit-speed-setting-select" content-class="limit-speed-setting-select-content"
            :max-height="160" :divider="true" />
          <span>分</span>
        </div>
        <div class="limit-speed-setting-select-container">
          <span>结束限速时间</span>
          <Select v-model="limitDownloadSpeedEndHour" :options="limitDownloadSpeedHourOptions"
            anchor-class="limit-speed-setting-select" content-class="limit-speed-setting-select-content"
            :max-height="160" :divider="true" />
          <span>时</span>
          <Select v-model="limitDownloadSpeedEndMinute" :options="limitDownloadSpeedMinuteOptions"
            anchor-class="limit-speed-setting-select" content-class="limit-speed-setting-select-content"
            :max-height="160" :divider="true" />
          <span>分</span>
        </div>
      </div>

      <div class="limit-speed-setting-body-item">
        <div class="limit-speed-setting-body-item-title">
          <Checkbox v-model="isLimitUploadSpeed" label-class="limit-speed-setting-label">最大上传速度</Checkbox>
        </div>
        <div class="limit-speed-setting-slider" v-if="isLimitUploadSpeed">
          <Slider v-model="uploadSpeed" :disabled="!isLimitUploadSpeed" :step="1" :segments="[{
            id: '1',
            start: 30,
            end: 100,
          }, {
            id: '2',
            start: 100,
            end: 200,
          }, {
            id: '3',
            start: 200,
            end: 1024,
          }, {
            id: '4',
            start: 1024,
            end: 2048,
          }]" :labels="['30KB', '100KB', '200KB', '1MB', '2MB']" :show-input="true" unit="KB/s" />
        </div>
      </div>
    </div>

    <div class="limit-speed-setting-footer">
      <Button variant="secondary" @click="handleClose">取消</Button>
      <Button :disabled="!isLimitDownloadSpeed && !isLimitUploadSpeed" @click="handleConfirm">确认</Button>
    </div>

  </div>
</template>

<style scoped lang="scss">
.limit-speed-setting {
  &-container {
    display: flex;
    width: 680px;

    flex-direction: column;
    align-items: flex-start;
    border-radius: var(--border-radius-L, 12px);
    background: var(--background-background-elevated, #FFF);
    box-shadow: 0px 6px 30px 0px rgba(39, 46, 59, 0.16);
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 64px;
    padding: 24px;

    span {
      color: var(--font-font-1, #272E3B);
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
    }

    i {
      color: var(--font-font-3);
    }
  }

  &-body {
    padding: 0 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;

    &-item {
      &-title {
        padding: 8px;
      }
    }
  }

  &-slider {
    width: 100%;
    padding: 12px 28px;
  }

  &-select-container {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 10px 52px;

    span {
      color: var(--font-font-3, #86909C);
      font-size: 14px;
      line-height: 22px;
    }
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 24px;
    width: 100%;

    button {
      padding: 0 48px;
    }
  }
}
</style>

<style lang="scss">
.limit-speed-setting-label {
  color: var(--font-font-2, #4E5769);
  font-size: 13px;
  line-height: 22px;
}

.limit-speed-setting-select {
  width: 84px !important;
  height: 32px !important;
  gap: 0px !important;
  padding: 0 !important;

  input {
    width: 100%;
    color: var(--font-font-2, #4E5769);
    font-size: 13px;
    width: 52px;
    text-align: center;
  }

  button {
    width: 32px;
    text-align: center;
  }

  i {
    color: var(--font-font-2, #4E5769);
  }
}

.limit-speed-setting-select-content {
  width: 84px !important;
}
</style>
