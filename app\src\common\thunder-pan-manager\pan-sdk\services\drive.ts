import { IRequestCommonConfig, IRequestClassImplement, IRequestCommonResponseT, IRequestHeader } from '..'
import { API_EVENT, API_FILE, API_TASK, Dictionary } from '../types'
import { convertEveryKeyToString } from '../utils/basic'

export interface IDriveApisCommonOptions<T> {
  params?: T
  headers?: IRequestHeader
}

export interface IGetAboutParams {
  space?: string
  with_quotas?: TQuotasString | string
}

export interface IGetEventsParams {
  limit?: number
  page_token?: string
  filters?: Dictionary
  space?: string
  thumbnail_size?: API_FILE.DriveImageSize
  with?: any
}

export type TUrlTasksOrder =
  | 'TASK_DEFAULT_ORDER'
  | 'TASK_ID_DESC'
  | 'TASK_TYPE_DESC'
  | 'TASK_MODIFY_TIME_ASC'
  | 'TASK_MODIFY_TIME_DESC'
  | 'TASK_SIZE_ASC'
  | 'TASK_SIZE_DESC'
  | 'TASK_CREATED_TIME_ASC'
  | 'TASK_CREATED_TIME_DESC'
  | 'TASK_NAME_ASC'
  | 'TASK_NAME_DESC'

export interface IGetUrlTasksParams {
  limit?: number
  page_token?: string
  type?: string
  thumbnail_size?: API_FILE.DriveImageSize
  with?: any
  filters?: any
  order?: TUrlTasksOrder
}

export interface IGetUrlTaskStatusParams {
  page_token?: string;
  limit?: number;
  filters?: any;
  space?: string,
  thumbnail_size?: API_FILE.DriveImageSize
}

export type TQuotasString = 'CREATE_UPLOAD_TASK_DAILY_SIZE_LIMIT' | 'CREATE_UPLOAD_TASK_MONTHLY_SIZE_LIMIT'

export type TPrivilegeString =
  | 'UPLOAD_FOLDER'
  | 'CREATE_OFFLINE_TASK_LIMIT'
  | 'ONLINE_PLAY_RESOLUTION'
  | 'DECOMPRESS_FILE_SIZE_LIMIT'    // 在线解压
  | 'UPLOAD_FLOW_SIZE_LIMIT'        // 上传流量
  | 'SPACE_SIZE_LIMIT'              // 云盘空间
  | 'TRASH_STORE_DURATION'          // 回收站保留时间
  | 'FILE_INVALID_DURATION'         // 流程播缓存时间
  | 'USER_SECURITY_TOKEN'           // 缩略图 token
  | 'OFFLINE_SUB_FILE_COUNT_LIMIT'  // 云添加子文件数量限制

export interface IFlowAboutResponse {
  usage: string
  limit: string
  state: number
  expire_seconds: number
  ms: string
  enabled: boolean
  report_strategy: {
    min_bytes: number
    min_interval: number
    max_interval: number
  },
  quota: {
    platinum: string
    'platinum.year': string
    student: string
    super: string
    'super.v1': string
    'super.v10': string
    'super.v2': string
    'super.v3': string
    'super.v4': string
    'super.v5': string
    'super.v6': string
    'super.v7': string
    'super.v8': string
    'super.v9': string
    'super.year': string
    user: string
  }
  usage_detail: {
    '迅雷Android': string
    '迅雷MAC':string
    '迅雷Windows': string
    '迅雷iPhone/iPad': string
  }
  params: any
  settle_day: number
  gms: string
  package_detail: {
    order_id:string
    batch_no: string
    size: string
    create_time: string
    end_time: string
  }[]
}

class DriveApis {
  private host: string
  private headers: IRequestHeader
  private config: IRequestCommonConfig
  private requestFn: IRequestClassImplement

  constructor(requestFn: IRequestClassImplement, config: IRequestCommonConfig) {
    this.requestFn = requestFn
    this.config = config
    this.initHost()
    this.initHeaders()
  }

  private initHost () {
    const DRIVE_API: Dictionary = {
      test: 'http://api-alpha-drive.office.k8s.xunlei.cn/drive/v1',
      prod: 'https://api-pan.xunlei.com/drive/v1'
    }
    this.host = DRIVE_API[this.config.env || 'prod']
  }

  private  initHeaders () {
    this.headers = this.config.headers || {}
  }

  /**
   * 获取云盘基础配置信息（如：云盘空间大小）
   * @param {IGetAboutParams} options
   * @returns API_FILE.DriveGetAboutResponse
   */
  async getAbout (options: IDriveApisCommonOptions<IGetAboutParams> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveGetAboutResponse>> {
    const url = `${this.host}/about`;
    const params = {
      space: "",
      with_quotas: "",
      ...options.params
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_FILE.DriveGetAboutResponse>
  }

  /**
   * 根据特权标识获取特权相关的数据
   * @param privilege 特权标识
   * @param headers 请求头信息
   * @returns API_FILE.DriveCheckPrivilegeResponse
   */
  async getPrivilege (privilege: TPrivilegeString, headers?: IRequestHeader): Promise<IRequestCommonResponseT<API_FILE.DriveCheckPrivilegeResponse>> {
    const url = `${this.host}/privilege/${privilege}`;
    const params = {}
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_FILE.DriveCheckPrivilegeResponse>
  }

  /**
   * 获取云盘取回（下载）高速流量使用相关的数据
   * @param headers 请求头信息
   * @returns IFlowAboutResponse
   */
  async getFlowAbout (headers?: IRequestHeader): Promise<IRequestCommonResponseT<IFlowAboutResponse>> {
    const url = `${this.host.replace('/drive/v1', '')}/flow/v1/about`;
    const params = {}
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<IFlowAboutResponse>
  }

  /**
   * 获取最近相关的数据
   * @param {IGetEventsParams} options
   * @returns API_EVENT.DriveListEventsResponse
   */
  async getEvents (options: IDriveApisCommonOptions<IGetEventsParams> = {}): Promise<IRequestCommonResponseT<API_EVENT.DriveListEventsResponse>> {
    const url = `${this.host}/events`;
    const params = convertEveryKeyToString({
      ...options.params
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_EVENT.DriveListEventsResponse>
  }

  /**
   * 上报事件
   * @param {API_EVENT.DriveReportEventRequest} options
   * @returns API_EVENT.DriveReportEventResponse
   */
  async reportEvents (options: IDriveApisCommonOptions<API_EVENT.DriveReportEventRequest> = {}): Promise<IRequestCommonResponseT<API_EVENT.DriveReportEventResponse>> {
    const url = `${this.host}/events`;
    const data: any = {
      space: "",
      event: {},
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_EVENT.DriveListEventsResponse>
  }

  /**
   * 批量删除最近相关的数据
   * @param {API_EVENT.DriveDeleteEventsRequest} options
   * @returns API_EVENT.DriveDeleteEventsResponse
   */
  async batchDeleteEvent (options: IDriveApisCommonOptions<API_EVENT.DriveDeleteEventsRequest> = {}): Promise<IRequestCommonResponseT<API_EVENT.DriveDeleteEventsResponse>>{
    const url = `${this.host}/events:delete`;
    const data: any = {
      space: "",
      ids: [],
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_EVENT.DriveDeleteEventsResponse>
  }

  /**
   * 获取云添加列表
   * @param {IGetUrlTasksParams} options
   * @returns API_TASK.DriveListTasksResponse
   */
  async getUrlTaskList (options: IDriveApisCommonOptions<IGetUrlTasksParams> = {}): Promise<IRequestCommonResponseT<API_TASK.DriveListTasksResponse>> {
    const url = `${this.host}/tasks`;
    const params = convertEveryKeyToString({
      limit: 999,
      page_token: '',
      type: 'offline',
      with: 'reference_resource',
      thumbnail_size: 'SIZE_MEDIUM',
      ...options.params
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_TASK.DriveListTasksResponse>
  }

  /**
   * 根据云添加任务 id 获取详情
   * @param taskId 任务 id
   * @param options 可选参数（headers）
   * @returns API_TASK.DriveTask
   */
  async getUrlTaskById (taskId: string, options: IDriveApisCommonOptions<null> = {}): Promise<IRequestCommonResponseT<API_TASK.DriveTask>> {
    const url = `${this.host}/tasks/${taskId}`;

    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header })

    return res as IRequestCommonResponseT<API_TASK.DriveTask>
  }

  /**
   * 根据云添加任务 id 获取 status 信息（获取该云添加任务内的文件列表）
   * @param taskId 任务 id
   * @param {IGetUrlTaskStatusParams} options
   * @returns API_TASK.DriveGetTaskStatusesResponse
   */
  async getUrlTaskStatusById (taskId: string, options: IDriveApisCommonOptions<IGetUrlTaskStatusParams> = {}): Promise<IRequestCommonResponseT<API_TASK.DriveGetTaskStatusesResponse>> {
    const url = `${this.host}/task/${taskId}/statuses`;

    const params = convertEveryKeyToString({
      ...options.params
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_TASK.DriveGetTaskStatusesResponse>
  }

  /**
   * 批量获取云添加任务 status
   * @param taskIds 任务 id 数组
   * @param options 可选参数（headers）
   * @returns API_TASK.DriveGetTaskStatusesResponse
   */
  async batchGetUrlTaskStatuses (taskIds: string[], options: IDriveApisCommonOptions<null> = {}): Promise<IRequestCommonResponseT<API_TASK.DriveGetTaskStatusesResponse>> {
    let query = taskIds.join('&status_ids=')
    const url = `${this.host}/statuses?status_ids=${query}`;

    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header })

    return res as IRequestCommonResponseT<API_TASK.DriveGetTaskStatusesResponse>
  }

  /**
   * 批量删除云添加任务（清除记录）
   * @param taskIds 任务 id 数组（每次请求限制 99）
   * @param options 可选参数（headers）
   * @returns
   */
  async batchDeleteUrlTasks (taskIds: string[], options: IDriveApisCommonOptions<null> = {}): Promise<IRequestCommonResponseT<API_TASK.DriveDeleteTasksResponse>> {
    let query = taskIds.join('&task_ids=')
    const url = `${this.host}/tasks?task_ids=${query}`;

    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.delete(url, { header })

    return res as IRequestCommonResponseT<API_TASK.DriveDeleteTasksResponse>
  }
}

export default DriveApis