import { CallApiProxy } from '@root/common/call-api';
import { EventEmitter } from 'events';
import * as BaseType from '../base';

export class AplayerPlayHistory {
  private apiProxy: CallApiProxy | null = null;
  private eventContainor: EventEmitter = new EventEmitter();
  private listCache: BaseType.PlayHistoryItem[] = [];

  public async initApiProxy(apiProxy: CallApiProxy): Promise<void> {
    this.apiProxy = apiProxy;
    this.apiProxy!.AttachServerEvent('AplayerPlayHistoryPrepared', () => {

      this.eventContainor.emit('AplayerPlayHistoryPrepared');
    });
    this.apiProxy!.AttachServerEvent('AplayerPlayHistoryItemUpdate', (item: BaseType.PlayHistoryItem, newAdd: boolean) => {
      this.eventContainor.emit('AplayerPlayHistoryItemUpdate', item, newAdd);
      if (newAdd) {
        this.listCache.unshift(item)
      } else {
        const replaceIndex = this.listCache.findIndex(i => Number(i.id) == item.id)
        if (replaceIndex > -1) {
          this.listCache.splice(replaceIndex, 1, item)
        }
      }
    });
    this.apiProxy!.AttachServerEvent('AplayerPlayHistoryItemDelete', (id: number) => {
      this.eventContainor.emit('AplayerPlayHistoryItemDelete', id);
      const delIndex = this.listCache.findIndex(i => Number(i.id) == id)
      if (delIndex > -1) {
        this.listCache.splice(delIndex, 1)
      }
    });
  }

  public async search(key: string): Promise<BaseType.PlayHistoryItem[]> {
    if (this.listCache.length === 0) {
      this.listCache = await this.getList();
    }
    let items: BaseType.PlayHistoryItem[] = [];
    this.listCache.forEach((item) => {
      if (item.name.toLowerCase().includes(key.toLowerCase())) {
        items.push(item);
      }
    });
    return items;
  }

  public async deleteItem(id: number): Promise<void> {
    await this.apiProxy!.CallApi('AplayerPlayHistoryDeleteItem', id);
  }

  public async deleteAllItem(): Promise<void> {
    await this.apiProxy!.CallApi('AplayerPlayHistoryDeleteAllItem');
  }

  public async getItemById(): Promise<BaseType.PlayHistoryItem> {
    let info = await this.apiProxy!.CallApi('AplayerPlayHistoryGetItemById');
    if (info.bSucc) {
      return info.result as BaseType.PlayHistoryItem;
    }
    return {} as any;
  }

  public async playItem(id: number): Promise<void> {
    await this.apiProxy!.CallApi('AplayerPlayHistoryPlayItem', id);
  }

  public async getList(): Promise<BaseType.PlayHistoryItem[]> {
    this.listCache = await this.getListImpl();
    return this.listCache;
  }

  public async getListImpl(): Promise<BaseType.PlayHistoryItem[]> {
    let info = await this.apiProxy!.CallApi('AplayerPlayHistoryGetList');
    if (info.bSucc) {
      return info.result as BaseType.PlayHistoryItem[];
    }
    return [];
  }

  public async getSelect(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerPlayHistoryGetSelect');
    if (info.bSucc) {
      return info.result as boolean;
    }
    return false;
  }

  public async isPrepared(): Promise<boolean> {
    let info = await this.apiProxy!.CallApi('AplayerPlayHistoryIsPrepared');
    if (info.bSucc) {
      return info.result as boolean;
    }
    return false;
  }

  public attachPreparedEvent(cb: () => void): void {
    this.eventContainor.on('AplayerPlayHistoryPrepared', cb);
  }

  public detachPreparedEvent(cb: () => void): void {
    this.eventContainor.off('AplayerPlayHistoryPrepared', cb);
  }

  public attachItemUpdate(cb: (item: BaseType.PlayHistoryItem, newAdd: boolean) => void): void {
    this.eventContainor.on('AplayerPlayHistoryItemUpdate', cb);
  }

  public detachItemUpdate(cb: (item: BaseType.PlayHistoryItem, newAdd: boolean) => void): void {
    this.eventContainor.off('AplayerPlayHistoryItemUpdate', cb);
  }

  public attachItemDeleteEvent(cb: (id: number) => void) {
    this.eventContainor.on('AplayerPlayHistoryItemDelete', cb);
  }

  public detachItemDeleteEvent(cb: (id: number) => void) {
    this.eventContainor.off('AplayerPlayHistoryItemDelete', cb);
  }
}