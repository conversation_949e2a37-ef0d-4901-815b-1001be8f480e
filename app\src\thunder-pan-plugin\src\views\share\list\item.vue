<script setup lang="ts">
import Button from '@root/common/components/ui/button/index.vue'
import TDCheckbox from '@root/common/components/ui/checkbox/index.vue'

import { computed } from 'vue';
import { API_SHARE } from '@root/common/thunder-pan-manager/pan-sdk/types';
import { formatDate } from '@root/common/thunder-pan-manager/pan-sdk/utils/file';
import { SharePageManager } from '@/manager/share-page-manager';

const props = defineProps<{
  file: API_SHARE.DriveShareMgrData
  pickedIds: string[]
}>()

const emit = defineEmits<{
  (e: 'copyLink', files: API_SHARE.DriveShareMgrData[]): void
  (e: 'cancelShare', files: API_SHARE.DriveShareMgrData[]): void
  (e: 'right-click', event: MouseEvent, file: API_SHARE.DriveShareMgrData): void
}>()
const isPicked = computed(() => props.pickedIds.includes(props.file.share_id!))

const title = computed(() => {
  if ((props.file.title ?? '') === '') return '已失效'
  if (Number(props.file.file_num ?? 0) > 1) return props.file.title + '等'
  return props.file.title
})

const disabledCopyLink = computed(() => {
  return (props.file.title ?? '') === ''
})

const icon = computed(() => {
  if ((props.file.icon_link ?? '') === '') return 'https://static-movie.a.88cdn.com/1b27b2347dc22c42b61953d0895e14db'
  if (Number(props.file.file_num ?? 0) > 1) {
    return 'https://backstage-img-ssl.a.88cdn.com/2638eeb5b2534a64ea8d22771418e067a1b8a0c4'
  }
  return props.file.icon_link
})

function handleItemClick () {
  SharePageManager.getInstance().togglePickedId(props.file.share_id!, true)
}

function handleCheckChange(isCheck: boolean) {
  SharePageManager.getInstance().togglePickedId(props.file.share_id!)
}

function handleCopyLink () {
  emit('copyLink', [props.file])
}

function handleCancelShare () {
  emit('cancelShare', [props.file])
}

function handleContextMenu (event: MouseEvent) {
  emit('right-click', event, props.file)
}
</script>

<template>
  <div class="file-item" :class="{ 'is-selected': isPicked }" @click="handleItemClick"  @click.right.stop="handleContextMenu($event)">
    <!-- 文件名复合区 -->
    <div class="file-name-container">
      <!-- 复选框 -->
      <div class="checkbox" @click.stop>
        <TDCheckbox label="" :model-value="isPicked" @update:model-value="handleCheckChange" />
      </div>

      <!-- 文件图标 -->
      <div class="icon">
        <img :src="icon" alt="">
      </div>

      <!-- 文件名 -->
      <div class="text">
        <span v-tooltip="title" class="file-name disable-drag-select">{{ title }}</span>
      </div>

      <!-- 文件状态 -->
      <div class="file-status">

      </div>

      <!-- 快捷操作区 -->
      <div class="operation-area">
        <Button
          v-if="!disabledCopyLink"
          v-tooltip="'复制链接'"
          class="disable-drag-select"
          variant="ghost"
          size="sm"
          :is-icon="true"
          @click.stop="handleCopyLink"
        >
          <i class="xl-icon-revert"></i>
        </Button>
        <Button
          v-tooltip="'取消分享'"
          class="disable-drag-select"
          variant="ghost"
          size="sm"
          :is-icon="true"
          @click.stop="handleCancelShare"
        >
          <i class="xl-icon-close"></i>
        </Button>
      </div>
    </div>

    <!-- 浏览数 -->
    <div class="file-view">
      {{ file.restore_count }}
    </div>

    <!-- 提取数 -->
    <div class="file-extract">
      {{ file.view_count }}
    </div>

    <!-- 可提取数 -->
    <div class="file-surplus">
      {{ file.restore_limit === '-1' ? '不限' : file.restore_limit }}
    </div>

    <!-- 剩余时长 -->
    <div class="file-duration">
      {{ file.expiration_left === '-1' ? '不限' : file.expiration_left }}
    </div>

    <!-- 分享时间 -->
    <div class="file-time">
      {{ formatDate(file.create_time!, 'YYYY-MM-DD HH:mm') }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.file-item {
  display: flex;
  align-items: center;
  gap: 24px;
  height: 56px;
  margin: 0 28px;
  padding: 0 12px;
  font-size: 12px;
  line-height: 22px;
  color: var(--font-font-1, #272E3B);
  border-radius: var(--border-radius-M2, 10px);
  overflow: hidden;

  .file-name-container {
    display: flex;
    align-items: center;
    flex-grow: 1;

    .checkbox {
      flex-shrink: 0;
    }

    .icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      margin: 0 12px;

      img {
        width: 100%;
      }
    }

    .text {
      flex-grow: 1;
      display: flex;
      align-items: center;

      .file-name {
        -webkit-line-clamp: 1;
        display: -webkit-box;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        white-space: pre-wrap;
        transition: color .2s;

        // &:hover {
        //   color: var(--primary-primary-default);
        //   cursor: pointer;
        // }
      }
    }

    .file-status {
      flex-shrink: 0;
    }

    .operation-area {
      display: none;
      flex-shrink: 0;
      margin-left: 24px;
    }
  }

  .file-view {
    flex-shrink: 0;
    width: 40px;
  }

  .file-extract {
    flex-shrink: 0;
    width: 40px;
  }

  .file-surplus {
    flex-shrink: 0;
    width: 48px;
  }

  .file-duration {
    flex-shrink: 0;
    width: 48px;
  }

  .file-time {
    flex-shrink: 0;
    width: 110px;
  }

  &.is-selected,
  &:hover {
    background-color: var(--fill-fill-3, #0C18310A);
  }

  &:hover:not(.is-selected) {
    .file-name-container {
      .operation-area {
        display: block;
      }
    }
  }
}
</style>
