console.log('00000000')
import { AsyncRemoteCall } from '@root/common/renderer-async-remote-call';

console.log('lllll')
//import { GetLogsPath, GetPlayerControlAddonNodeName, GetProfilesPath, GetSdkPath, GetXxxNodePath } from '@root/common/xxx-node-path';
console.log('222222222222')
const wndMinWidth: number = 960
const wndMinHeight: number = 640
const wndWidth: number = 1080
const wndHeight: number = 675
async function CreatePlayer() {
  const BrowserWindow: any = await AsyncRemoteCall.GetInstance().getBrowserWindow(); // tslint:disable-line
  // let playerParentWnd = await new BrowserWindow({
  //   width: wndWidth,
  //   height: wndHeight,
  //   minWidth: wndMinWidth,
  //   minHeight: wndMinHeight,
  //   show: true,
  //   frame: false,
  //   autoHideMenuBar: true,
  //   webPreferences: {
  //     nodeIntegration: true,
  //     devTools: true,
  //     webviewTag: true,
  //     contextIsolation: false,
  //     nodeIntegrationInSubFrames: true,
  //     backgroundThrottling: false,
  //   },
  // });
  let playerControlWnd = await new BrowserWindow({
    width: wndWidth,
    height: wndHeight,
    minWidth: wndMinWidth,
    minHeight: wndMinHeight,
    show: true,
    frame: false,
    closable: true,
    //parent: playerParentWnd ? playerParentWnd : undefined,
    transparent: true,
    autoHideMenuBar: true,
    webPreferences: {
      nodeIntegration: true,
      devTools: true,
      webviewTag: true,
      contextIsolation: false,
      nodeIntegrationInSubFrames: true,
      backgroundThrottling: false,
    },
  });

  //console.log(`file://${path.resolve(__dirname, 'renderer.html')}`)
  await playerControlWnd.loadURL('https://www.baidu.com')

  // let hwndBuf: Buffer = playerParentWnd.getNativeWindowHandle()
  // let handle = hwndBuf.readBigUInt64LE(0)

  // let playercontrol = requireNodeFile(
  //   path.join(GetXxxNodePath(), GetPlayerControlAddonNodeName()),
  // )
  // let playerInitParam: any = {
  //   xmp: false,
  //   openPlayerLog: false,
  //   logDir: GetLogsPath(),
  //   codecPath: '',
  //   subtitleCachePath: path.join(GetProfilesPath(), 'subtitle'),
  //   peerid: '1111111111',
  //   version: '',
  //   versionCode: 10,
  //   configPath: path.join(GetProfilesPath(), 'player_config.json'),
  //   dbDir: GetProfilesPath(),
  //   dbName: 'player_record.db',
  //   panPlayCache: path.join(GetProfilesPath(), 'yun_play'),
  //   downloadServerDir: GetSdkPath()
  // }
  // playercontrol.initAddon(playerInitParam)
  // setTimeout(() => {
  //   playercontrol.setWnd(handle)
  // }, 5000)
}

CreatePlayer();


// AsyncRemoteCall.GetInstance().test();