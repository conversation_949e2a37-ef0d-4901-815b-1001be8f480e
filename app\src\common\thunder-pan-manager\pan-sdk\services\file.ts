import { IRequestCommonConfig, IRequestClassImplement, IRequestCommonResponseT, IRequestHeader } from '..'
import { API_FILE, Dictionary } from '../types'
import { convertEveryKeyToString, flatten } from '../utils/basic'

export interface IFileApisGetFilesOptions<T> {
  params?: T
  headers?: IRequestHeader
}

export interface IGetFilesParams {
  space?: string
  parent_id?: string
  page_token?: string
  filters?: {
    kind?: {
      eq?: string
    }
    phase?: {
      eq?: string
    }
    trashed?: {
      eq?: boolean
    }
    mime_type?: {
      prefix?: string
    }
    writable?: {
      eq?: boolean
    }
    modified_time?: {
      gt?: string
      lt?: string
    }
    shared_from?: {
      eq?: string
    }
  }
  limit?: number
  with_audit?: boolean
  with?: string[]
  usage?: API_FILE.DriveFileUsage
  share_id?: string
  pass_code_token?: string
  thumbnail_size?: API_FILE.DriveImageSize
  order?: API_FILE.DriveFileOrder
  tag_scenes?: string
  operation_ids?: string[]
  folder_type?: string
  get_all?: boolean
  version?: string
}


export interface IGetFolderFilesParams {
  space?: string
  parent_id?: string
  page_token?: string
  limit?: number
}

export interface IGetFileInfoParams extends IGetFilesParams {
  thumbnail_size?: API_FILE.DriveImageSize
}

export interface ICreateFolderParams extends IGetFilesParams {
  name?: string
  kind?: string
  ignore_duplicated_name?: boolean
}

export interface IGetAllCategoryFileCountResponse {
  file_total: string      // 云盘文件总数
  offline_total: string   // 云添加文件总数
  restore_total: string   // 转存文件总数
}

export interface IGetFileBySpaceResponse {
  file_id: string
  space: string
  file_name: string
}

export interface IGetFileAncestorsRequest {
  space: string
  userid?: string
}

export interface ICleanTrashResponse {
  task_id: string
  space: string
}

export enum EFileSpaceFolderType {
  // 我的资源
  DOWNLOAD = 1,
  // 我的转存
  RESTORE = 2,
  // 在线解压
  DECOMPRESS = 3,
  // 保险箱
  SAFE = 4,
}

class FileApis {
  private host: string
  private headers: IRequestHeader
  private config: IRequestCommonConfig
  private requestFn: IRequestClassImplement

  constructor(requestFn: IRequestClassImplement, config: IRequestCommonConfig) {
    this.requestFn = requestFn
    this.config = config
    this.initHost()
    this.initHeaders()
  }

  private initHost() {
    const DRIVE_API: Dictionary = {
      test: 'http://api-alpha-drive.office.k8s.xunlei.cn/drive/v1',
      prod: 'https://api-pan.xunlei.com/drive/v1'
    }
    this.host = DRIVE_API[this.config.env || 'prod']
  }

  private initHeaders() {
    this.headers = this.config.headers || {}
  }

  /**
   * 获取文件列表
   * @param options
   * @returns
   */
  async getFiles(options: IFileApisGetFilesOptions<IGetFilesParams> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveListFilesResponse>> {
    const url = `${this.host}/files`;
    const params = convertEveryKeyToString({
      space: "",
      parent_id: "",
      page_token: "",
      filters: {
        trashed: {
          eq: false
        }
      },
      limit: 999,
      with_audit: 'true',
      with: ['public_share_tag'],
      ...options.params
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_FILE.DriveListFilesResponse>
  }

  /**
   * 获取文件夹下所有文件
   * @param options
   * @returns
   */
  async getFolderFiles(options: IFileApisGetFilesOptions<IGetFolderFilesParams> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveListFilesResponse>> {
    const url = `${this.host}/folder/files`;
    const params = convertEveryKeyToString({
      space: "",
      parent_id: "",
      page_token: "",
      limit: 999,
      ...options.params
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_FILE.DriveListFilesResponse>
  }

  /**
   * 获取指定目录（parent_id）下的所有文件列表
   * @param parent_id 目录 id
   * @param space 空间标识
   * @param page_token 分页页码（默认不传）
   * @returns
   */
  async getAllFilesByParentId(parent_id: string, space: string = '', page_token: string = '', options: IFileApisGetFilesOptions<IGetFilesParams> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveListFilesResponse>> {
    const res = await this.getFiles({
      params: {
        space,
        parent_id: parent_id,
        page_token: page_token,
        limit: 999,
        filters: { phase: { eq: 'PHASE_TYPE_COMPLETE' }, trashed: { eq: false } },
        ...options.params,
      },
      headers: options.headers,
    })

    if (res.success && res.data && res.data.next_page_token) {
      const nextRes = await this.getAllFilesByParentId(parent_id, space, res.data.next_page_token, options)

      return {
        success: nextRes.success,
        data: {
          ...res,
          next_page_token: '',
          files: flatten([res.data.files, nextRes.data?.files]) as API_FILE.DriveFile[]
        }
      }
    }
    return res as IRequestCommonResponseT<API_FILE.DriveListFilesResponse>
  }

  /**
   * 获取指定目录（parent_id）下的所有文件夹列表
   * @param parent_id 目录 id
   * @param space 空间标识
   * @param page_token 分页页码（默认不传）
   * @returns
   */
  async getAllFolderByParentId(parent_id: string, space: string = '', page_token: string = '', options: IFileApisGetFilesOptions<IGetFilesParams> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveListFilesResponse>> {
    const res = await this.getFiles({
      params: {
        space,
        parent_id: parent_id,
        page_token: page_token,
        limit: 999,
        filters: { kind: { eq: 'drive#folder' }, trashed: { eq: false } },
        ...options.params,
      },
      headers: options.headers,
    })

    if (res.success && res.data && res.data.next_page_token) {
      const nextRes = await this.getAllFolderByParentId(parent_id, space, res.data.next_page_token, options)

      return {
        success: nextRes.success,
        data: {
          ...res,
          next_page_token: '',
          files: flatten([res.data.files, nextRes.data?.files]) as API_FILE.DriveFile[]
        }
      }
    }
    return res as IRequestCommonResponseT<API_FILE.DriveListFilesResponse>
  }

  /**
   * 获取指定目录（parent_id）下的所有文件（平铺展示）
   * @param parent_id 目录 id
   * @param space 空间标识
   * @param page_token 分页页码（默认不传）
   * @returns
   */
  async getAllFlattenSubFilesByParentId(parent_id: string, space: string = '', page_token: string = '', options: IFileApisGetFilesOptions<IGetFolderFilesParams> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveListFilesResponse>> {
    const res = await this.getFolderFiles({
      params: {
        space,
        parent_id: parent_id,
        page_token: page_token,
        limit: 999,
        ...options.params,
      },
      headers: options.headers,
    })

    if (res.success && res.data && res.data.next_page_token) {
      const nextRes = await this.getAllFlattenSubFilesByParentId(parent_id, space, res.data.next_page_token, options)

      return {
        success: nextRes.success,
        data: {
          ...res,
          next_page_token: '',
          files: flatten([res.data.files, nextRes.data?.files]) as API_FILE.DriveFile[]
        }
      }
    }
    return res as IRequestCommonResponseT<API_FILE.DriveListFilesResponse>
  }

  /**
   * 获取全部的视频文件列表
   * @param page_token 分页页码（不需要传）
   * @returns
   */
  async getAllVideoFiles(page_token: string = ''): Promise<IRequestCommonResponseT<API_FILE.DriveListFilesResponse>> {
    const res = await this.getFiles({
      params: {
        parent_id: '*',
        page_token: page_token,
        limit: 999,
        filters: {
          mime_type: { prefix: "video" },
          phase: { eq: 'PHASE_TYPE_COMPLETE' },
          trashed: { eq: false }
        }
      }
    })

    if (res.success && res.data && res.data.next_page_token) {
      const nextRes = await this.getAllVideoFiles(res.data.next_page_token)

      return {
        success: nextRes.success,
        data: {
          ...res,
          next_page_token: '',
          files: flatten([res.data.files, nextRes.data?.files]) as API_FILE.DriveFile[]
        }
      }
    }
    return res as IRequestCommonResponseT<API_FILE.DriveListFilesResponse>
  }

  /**
   * 获取全部的图片文件列表
   * @param page_token 分页页码（不需要传）
   * @returns
   */
  async getAllImageFiles(page_token: string = ''): Promise<IRequestCommonResponseT<API_FILE.DriveListFilesResponse>> {
    const res = await this.getFiles({
      params: {
        parent_id: '*',
        page_token: page_token,
        limit: 999,
        filters: {
          mime_type: { prefix: "image" },
          phase: { eq: 'PHASE_TYPE_COMPLETE' },
          trashed: { eq: false }
        }
      }
    })

    if (res.success && res.data && res.data.next_page_token) {
      const nextRes = await this.getAllImageFiles(res.data.next_page_token)

      return {
        success: nextRes.success,
        data: {
          ...res,
          next_page_token: '',
          files: flatten([res.data.files, nextRes.data?.files]) as API_FILE.DriveFile[]
        }
      }
    }
    return res as IRequestCommonResponseT<API_FILE.DriveListFilesResponse>
  }

  /**
   * 获取全部的回收站文件列表
   * @param page_token 分页页码（不需要传）
   * @returns
   */
  async getAllTrashFiles(page_token: string = '', options: IFileApisGetFilesOptions<IGetFilesParams> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveListFilesResponse>> {
    const res = await this.getFiles({
      params: {
        parent_id: '*',
        page_token: page_token,
        limit: 999,
        filters: {
          trashed: {
            eq: true,
          },
        },
        ...options.params
      }
    })

    if (res.success && res.data && res.data.next_page_token) {
      const nextRes = await this.getAllTrashFiles(res.data.next_page_token)

      return {
        success: nextRes.success,
        data: {
          ...res,
          next_page_token: '',
          files: flatten([res.data.files, nextRes.data?.files]) as API_FILE.DriveFile[]
        }
      }
    }
    return res as IRequestCommonResponseT<API_FILE.DriveListFilesResponse>
  }

  /**
   * 获取文件详情
   * @param fileId 文件 id
   * @param options 其他参数
   * @returns
   */
  async getFileInfo(fileId: string, options: IFileApisGetFilesOptions<IGetFileInfoParams> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveFile>> {
    const url = `${this.host}/files/${fileId}`;
    const params = convertEveryKeyToString({
      space: "",
      thumbnail_size: "SIZE_MEDIUM",
      with: ['public_share_tag'],
      ...options.params,
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_FILE.DriveFile>
  }

  /**
   * 创建空文件夹
   * @param parentId
   * @param name
   * @param options
   * @returns
   */
  async createFolder(parentId: string, name: string, options: IFileApisGetFilesOptions<ICreateFolderParams> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveCreateFileResponse>> {
    const url = `${this.host}/files`;
    const data = {
      space: "",
      name: name,
      parent_id: parentId,
      kind: 'drive#folder',
      ignore_duplicated_name: false,
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_FILE.DriveCreateFileResponse>
  }

  /**
   * 创建文件（上传文件时使用）
   * @param {API_FILE.DriveCreateFileRequest} options
   * @returns API_FILE.DriveCreateFileResponse
   */
  async createFile(options: IFileApisGetFilesOptions<API_FILE.DriveCreateFileRequest> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveCreateFileResponse>> {
    const url = `${this.host}/files`;
    const data = {
      upload_type: '',
      kind: 'drive#file',
      parent_id: '',
      name: '',
      hash: '',
      size: '',
      id: '',
      space: '',
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_FILE.DriveCreateFileResponse>
  }

  /**
   * 批量删除文件
   * @param options
   * @returns
   */
  async batchDeleteFiles(options: IFileApisGetFilesOptions<API_FILE.DriveBatchDeleteFilesRequest> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveBatchDeleteFilesResponse>> {
    const url = `${this.host}/files:batchDelete`;
    const data: any = {
      space: "",
      ids: [],
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_FILE.DriveFile>
  }

  /**
   * 批量将文件放入回收站
   * @param options
   * @returns
   */
  async batchTrashFiles(options: IFileApisGetFilesOptions<API_FILE.DriveBatchTrashFilesRequest> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveBatchTrashFilesResponse>> {
    const url = `${this.host}/files:batchTrash`;
    const data: any = {
      space: "",
      ids: [],
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_FILE.DriveFile>
  }

  /**
   * 批量复制文件
   * @param options
   * @returns
   */
  async batchCopyFiles(options: IFileApisGetFilesOptions<API_FILE.DriveBatchCopyFilesRequest> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveBatchCopyFilesResponse>> {
    const url = `${this.host}/files:batchCopy`;
    const data: any = {
      space: "",
      ids: [],
      to: {
        parent_id: '',
        space: '',
      },
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_FILE.DriveFile>
  }

  /**
   * 批量移动文件
   * @param options
   * @returns
   */
  async batchMoveFiles(options: IFileApisGetFilesOptions<API_FILE.DriveBatchMoveFilesRequest> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveBatchMoveFilesResponse>> {
    const url = `${this.host}/files:batchMove`;
    const data: any = {
      space: "",
      ids: [],
      to: {
        parent_id: '',
        space: '',
      },
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_FILE.DriveFile>
  }

  /**
   * 批量还原回收站的文件
   * @param options
   * @returns
   */
  async batchUnTrash(options: IFileApisGetFilesOptions<API_FILE.DriveBatchUntrashFilesRequest> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveBatchUntrashFilesResponse>> {
    const url = `${this.host}/files:batchUntrash`;
    const data: any = {
      space: "",
      ids: [],
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_FILE.DriveBatchUntrashFilesResponse>
  }

  /**
   * 清空回收站
   * @param options
   * @returns
   */
  async cleanTrash (options: IFileApisGetFilesOptions<null> = {}) {
    const url = `${this.host}/files/trash:empty`;
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.patch(url, { header })

    return res as IRequestCommonResponseT<ICleanTrashResponse>
  }

  /**
   * 批量更新文件
   * @param options
   * @returns
   */
  async batchUpdate(options: IFileApisGetFilesOptions<API_FILE.DriveBatchUpdateFilesRequest> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveBatchUpdateFilesResponse>> {
    const url = `${this.host}/files:batchUpdate`;
    const data: any = {
      space: "",
      files: [],
      ...options.params,
    }
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers,
    }

    const res = await this.requestFn.post(url, { header, data })

    return res as IRequestCommonResponseT<API_FILE.DriveBatchUpdateFilesResponse>
  }


  /**
   * 根据 id 来判断当前文件是否处于【共享中】的文件夹中，否则会按照 parent_id 递归获取直到最上层
   * @param {boolean} isKol 是否是 kol
   * @param {string} id 文件 id
   * @param {API_FILE.DriveFile} files 文件信息（可选），可用于直接判断
   * @returns true | false
   */
  async checkFileIsInShareById(isKol: boolean, id: string, file?: API_FILE.DriveFile): Promise<boolean> {
    // 文件 id 不存、当前用户不在 kol 白名单内，直接返回 false
    if (!id || !isKol) return false
    // 传入了 file，则那其中的字段直接做判断
    if (file && file?.params?.public_share_tag) return true
    // 通过接口获取文件数据
    const res = await this.getFileInfo(id || file?.id!)
    return res.data?.params?.public_share_tag ? true : await this.checkFileIsInShareById(isKol, res.data?.parent_id!)
  }

  /**
   * 搜索文件
   * @param options
   * @returns
   */
  async searchFiles(options: IFileApisGetFilesOptions<API_FILE.DriveSearchFilesRequest> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveSearchFilesResponse>> {
    const url = `${this.host}/files:search`;
    const params = convertEveryKeyToString({
      space: "",
      parent_id: "",
      limit: 999,
      ...options.params
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_FILE.DriveListFilesResponse>
  }

  /**
   * 获取全类型文件总数（文件总数、云添加文件总数、转存文件总数）
   * @param options
   * @returns
   */
  async getAllCategoryFileCount(options: IFileApisGetFilesOptions<null> = {}): Promise<IRequestCommonResponseT<IGetAllCategoryFileCountResponse>> {
    const url = `${this.host}/file/count`;
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header })

    return res as IRequestCommonResponseT<IGetAllCategoryFileCountResponse>
  }

  /**
   * 获取文件祖先（面包屑）
   * @param options
   * @returns
   */
  async getFileAncestors(fileId: string, options: IFileApisGetFilesOptions<IGetFileAncestorsRequest> = {}): Promise<IRequestCommonResponseT<API_FILE.DriveGetFileAncestorsAdminResponse>> {
    const url = `${this.host}/file/ancestors`;
    const params = convertEveryKeyToString({
      fileid: fileId,
      space: '',
      ...options.params
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<API_FILE.DriveGetFileAncestorsAdminResponse>
  }

  /**
   * 获取指定空间目录id
   * @param options
   * @returns
   */
  async getFileBySpace(space: string, folder_type: EFileSpaceFolderType, options: IFileApisGetFilesOptions<null> = {}): Promise<IRequestCommonResponseT<IGetFileBySpaceResponse>> {
    const url = `${this.host}/space`;
    const params = convertEveryKeyToString({
      space: space,
      folder_type: folder_type,
    })
    const header = {
      "content-type": "application/json",
      ...this.headers,
      ...options.headers
    }

    const res = await this.requestFn.get(url, { header, params })

    return res as IRequestCommonResponseT<IGetFileBySpaceResponse>
  }
}

export default FileApis